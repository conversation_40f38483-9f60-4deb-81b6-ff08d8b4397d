"""滑点递增重试模块 - 智能滑点调整和重试机制

本模块提供以下核心功能：
1. 滑点递增计算 - SlippageCalculator
2. 重试决策引擎 - RetryDecisionEngine  
3. 重试间隔计算 - RetryDelayCalculator
4. 参数合并器 - ParameterMerger
5. 重试上下文 - RetryContext

主要特性：
- 支持买卖独立的滑点重试配置
- 智能的重试间隔计算（固定/线性/指数退避）
- 多层级配置优先级合并（运行时 > 策略 > 渠道 > 全局）
- 针对meme币市场优化的默认配置
- 完整的重试过程记录和可追溯性
"""

from .slippage_calculator import SlippageCalculator
from .retry_decision_engine import RetryDecisionEngine
from .retry_delay_calculator import RetryDelayCalculator
from .parameter_merger import ParameterMerger
from .retry_context import RetryContext

__all__ = [
    "SlippageCalculator",
    "RetryDecisionEngine", 
    "RetryDelayCalculator",
    "ParameterMerger",
    "RetryContext"
]

__version__ = "0.1.1"
__author__ = "memeMonitor Team" 