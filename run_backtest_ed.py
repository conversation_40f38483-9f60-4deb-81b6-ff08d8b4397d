import asyncio
import logging
import argparse
import json
import os
import time
import dataclasses
from typing import Dict, Any, List
from datetime import datetime, timedelta

from dotenv import load_dotenv
from models import init_db
from utils.backtest.config_manager import Config<PERSON>anager, BacktestConfig
from utils.backtest_event_driven.backtest import EventDrivenBacktest
from utils.backtest_v2.backtest_engine import BacktestEngineV2
from utils.backtest_v2.config_manager import ConfigManagerV2, BacktestConfigV2
from utils.backtest_analysis.kelly_calculator import calculate_and_add_kelly_to_json, calculate_kelly
from utils.backtest_analysis.result_filter import filter_and_save_by_metric
from utils.backtest_analysis.report_generator import generate_html_report, generate_single_run_report
import pandas as pd

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('backtest_ed.log')
    ]
)

logger = logging.getLogger("BacktestEventDriven")

# 结果输出目录
RESULT_DIR = "backtest_result"


def load_config_unified(args):
    """统一的配置加载函数，支持V1和V2回测模式

    Args:
        args: 命令行参数对象

    Returns:
        Tuple[Union[BacktestConfig, BacktestConfigV2], str]: (配置对象, 版本标识)
    """
    if args.mode in ['single_v2', 'grid_v2']:
        # 使用V2配置管理器
        if args.config:
            config = ConfigManagerV2.load_from_file(args.config)
        else:
            config = ConfigManagerV2.get_default_config()
        # 应用命令行参数覆盖
        config = ConfigManagerV2.apply_cli_overrides(config, args)
        return config, 'v2'
    else:
        # 使用现有的V1配置管理器
        if args.config:
            config = ConfigManager.load_from_file(args.config)
        else:
            config = BacktestConfig()
        # 现有的命令行参数覆盖逻辑
        for key, value in vars(args).items():
            if value is not None and key != 'config' and hasattr(config, key):
                setattr(config, key, value)
        return config, 'v1'


async def run_single_backtest(config: BacktestConfig):
    """运行单次事件驱动回测
    
    Args:
        config: 回测配置
    
    Returns:
        Dict[str, Any]: 回测结果
    """
    logger.info(f"开始单次事件驱动回测, 配置: {config}")
    
    # 确保结果目录存在
    os.makedirs(RESULT_DIR, exist_ok=True)
    
    start_time = time.time()
    backtest = EventDrivenBacktest(config)
    
    # 设置结果输出目录在backtest_result下
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    result_subdir_name = f"ed_backtest_results_{timestamp}" # 保存子目录名
    result_dir_path = os.path.join(RESULT_DIR, result_subdir_name)
    backtest.result_dir = result_dir_path
    os.makedirs(backtest.result_dir, exist_ok=True) # 确保子目录存在
    
    result = await backtest.run()
    end_time = time.time()
    
    execution_time = end_time - start_time
    result["execution_time"] = execution_time
    result["param_index"] = 1 # 单次回测视为参数组合 1
    
    # 为单次回测结果计算凯利并检查胜率
    if "statistics" in result and isinstance(result["statistics"], dict):
        stats = result["statistics"]
        win_rate_col = 'win_rate'
        return_rate_col = 'return_rate'
        kelly_col = 'kelly_fraction_calculated'

        if win_rate_col in stats and return_rate_col in stats:
            try:
                p_val = stats[win_rate_col]
                b_val = stats[return_rate_col]
                p = float(p_val) if p_val is not None else None
                b = float(b_val) if b_val is not None else None

                if p is not None and b is not None and not pd.isna(p) and not pd.isna(b):
                    raw_f, capped_f, status = calculate_kelly(p, b)
                    if capped_f is not None:
                        stats[kelly_col] = capped_f
                        logger.info(f"单次回测凯利分数计算 (原始值): {raw_f:.4f} (状态: {status}) - 已保存约束值")
                    else:
                        logger.warning(f"单次回测凯利分数计算失败: {status}")
                    if p > 0.50:
                        logger.info(f"单次回测胜率 {p:.2%} 超过 50%")
                    else:
                        logger.info(f"单次回测胜率 {p:.2%} 未超过 50%")
                else:
                    logger.warning(f"单次回测的胜率 ({p_val}) 或回报率 ({b_val}) 无效，无法计算凯利分数。")
            except Exception as e:
                logger.error(f"计算单次回测凯利分数或检查胜率时出错: {e}", exc_info=True)
        else:
            logger.warning("单次回测结果的 'statistics' 中缺少 'win_rate' 或 'return_rate'。")

    logger.info(f"回测完成，用时: {timedelta(seconds=execution_time)}")

    # **** 新增：为单次回测生成独立 HTML 报告 ****
    single_result_json_path = os.path.join(backtest.result_dir, "results.json")
    single_report_html_path = os.path.join(backtest.result_dir, "single_run_report.html")
    logger.info(f"开始生成单次回测 HTML 报告: {single_report_html_path}")
    try:
        report_generated = generate_single_run_report(single_result_json_path, single_report_html_path)
        if report_generated:
            logger.info(f"单次回测 HTML 报告已生成: {single_report_html_path}")
        else:
            logger.error("生成单次回测 HTML 报告失败。")
    except Exception as e:
        logger.error(f"生成单次回测 HTML 报告时发生意外错误: {e}", exc_info=True)
    # **** 结束新增 ****

    return result


async def run_single_backtest_v2(config: BacktestConfigV2):
    """运行单次直接分析回测

    Args:
        config: V2回测配置

    Returns:
        Dict[str, Any]: 回测结果
    """
    logger.info(f"开始单次直接分析回测, 配置: {config}")

    # 创建结果目录
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    result_subdir_name = f"v2_backtest_results_{timestamp}"
    result_dir_path = os.path.join(RESULT_DIR, result_subdir_name)
    os.makedirs(result_dir_path, exist_ok=True)

    start_time = time.time()

    # 使用V2回测引擎
    backtest_engine = BacktestEngineV2(config)
    backtest_engine.result_dir = result_dir_path

    result = await backtest_engine.run_backtest()
    end_time = time.time()

    execution_time = end_time - start_time
    result["execution_time"] = execution_time
    result["param_index"] = 1
    result["backtest_version"] = "v2"

    # 保存结果（复用现有的结果保存逻辑）
    results_json_path = os.path.join(result_dir_path, "results.json")
    with open(results_json_path, 'w') as f:
        json.dump(result, f, indent=4, default=str)

    # 生成报告（复用现有的报告生成逻辑）
    generate_single_run_report(results_json_path, os.path.join(result_dir_path, "single_run_report.html"))

    logger.info(f"V2回测完成，用时: {timedelta(seconds=execution_time)}")
    return result


async def run_backtest_with_params(params: Dict[str, Any], semaphore: asyncio.Semaphore, index: int, total: int, result_dir: str):
    """使用指定参数运行回测（包含信号量控制）
    
    Args:
        params: 参数字典
        semaphore: 控制并发的信号量
        index: 当前参数组合的索引
        total: 总参数组合数
        result_dir: 结果目录
        
    Returns:
        Dict: 包含参数和结果的字典
    """
    async with semaphore:
        logger.info(f"运行参数组合 {index+1}/{total}: {params}")
        
        start_time = time.time()
        
        config = BacktestConfig(**params)
        backtest = EventDrivenBacktest(config)
        
        # 设置结果输出子目录
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backtest_subdir_name = f"ed_backtest_results_{timestamp}_{index+1}"
        backtest.result_dir = os.path.join(result_dir, backtest_subdir_name)
        os.makedirs(backtest.result_dir, exist_ok=True)
        
        result = None
        error_message = None
        try:
            result = await backtest.run() # result 包含 trades, stats, config
        except Exception as e:
            logger.error(f"参数组合 {index+1}/{total} 的 backtest.run() 执行出错: {e}", exc_info=True)
            error_message = f"Backtest execution failed: {e}"
            result = {} # Ensure result is a dict even on error
            
        end_time = time.time()
        execution_time = end_time - start_time
        
        # ---- 从 backtest.run() 获取准确的统计信息和交易记录 ---- 
        final_statistics = result.get("statistics", {}) if isinstance(result, dict) else {}
        final_trades = result.get("trades", []) if isinstance(result, dict) else []
        if error_message and not final_statistics.get("error"):
            final_statistics["error"] = error_message
        
        # ---- 准备要保存到 results.json 的数据 ----
        result_to_save = {
            "param_index": index + 1,
            "params": params,
            "config": dataclasses.asdict(config), # 保存配置副本 
            "statistics": final_statistics, # 使用从 run() 获取的统计信息
            "trades": final_trades, # 使用从 run() 获取的交易记录
            "execution_time": execution_time,
            "execution_time_formatted": str(timedelta(seconds=execution_time)),
            "start_time": datetime.fromtimestamp(start_time).isoformat(),
            "end_time": datetime.fromtimestamp(end_time).isoformat(),
            "result_dir": backtest.result_dir, # 存储绝对路径
            "error": error_message # Add error message if exists
        }

        # ---- 保存包含 trades 的完整结果到子目录的 results.json ----
        full_result_path = os.path.join(backtest.result_dir, "results.json")
        try:
            with open(full_result_path, 'w') as f:
                json.dump(result_to_save, f, indent=4, default=str)
            logger.info(f"参数组合 {index+1}/{total} 的完整结果已保存到: {full_result_path}")
        except (TypeError, OverflowError, ValueError) as json_err: # Catch specific JSON errors
            logger.error(f"保存参数组合 {index+1}/{total} 的结果到 JSON 时序列化出错: {json_err}", exc_info=True)
            # Optionally, try to save a minimal error JSON
            try:
                 with open(full_result_path, 'w') as f_err:
                    json.dump({"error": f"Failed to serialize results: {json_err}", "param_index": index + 1, "params": params}, f_err, indent=4, default=str)
            except Exception as write_err:
                 logger.error(f"无法写入错误 JSON 文件 {full_result_path}: {write_err}")
        except Exception as e:
            logger.error(f"保存参数组合 {index+1}/{total} 的完整结果时发生意外错误: {e}", exc_info=True)
        
        logger.info(f"参数组合 {index+1}/{total} 完成，用时: {timedelta(seconds=execution_time)}")
        
        # ---- 返回给 asyncio.gather 的结果（不包含 trades，以减少内存占用）---- 
        return {
            "param_index": index + 1,
            "params": params,
            "statistics": final_statistics, # 返回从 run() 获取的统计信息 
            "result_dir": backtest.result_dir, # 仍然返回目录路径
            "execution_time": execution_time,
            "total_params": total
            # 注意：这里不返回 trades 列表
        }


async def run_parameter_grid(param_grid: Dict[str, List], max_concurrency: int = 4):
    """运行参数网格搜索（支持并行执行）
    
    Args:
        param_grid: 参数网格
        max_concurrency: 最大并发数量
        
    Returns:
        Dict[str, Any]: 参数搜索结果
    """
    logger.info(f"开始参数网格搜索, 参数网格: {param_grid}, 最大并发数: {max_concurrency}")
    
    total_start_time = time.time()
    
    parameter_combinations = ConfigManager.generate_parameter_combinations(param_grid)
    logger.info(f"生成了 {len(parameter_combinations)} 个参数组合")
    
    # 创建主结果目录
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    search_result_dir_name = f"ed_param_search_{timestamp}"
    full_result_dir = os.path.join(RESULT_DIR, search_result_dir_name)
    os.makedirs(full_result_dir, exist_ok=True)
    logger.info(f"主结果目录: {full_result_dir}")
    
    # 保存参数网格配置
    param_grid_save_path = os.path.join(full_result_dir, "param_grid.json")
    try:
        with open(param_grid_save_path, 'w') as f:
            json.dump(param_grid, f, indent=4)
        logger.info(f"参数网格配置已保存到: {param_grid_save_path}")
    except Exception as e:
        logger.error(f"保存参数网格配置时出错: {e}")

    semaphore = asyncio.Semaphore(max_concurrency)
    
    # 并行执行所有回测任务
    tasks = [
        run_backtest_with_params(params, semaphore, i, len(parameter_combinations), full_result_dir)
        for i, params in enumerate(parameter_combinations)
    ]
    # 'results' 现在只包含每个运行的摘要信息 (无 trades)
    results_summary_list = await asyncio.gather(*tasks)
    
    total_end_time = time.time()
    total_execution_time = total_end_time - total_start_time
    
    # 计算时间统计
    avg_execution_time = sum(r["execution_time"] for r in results_summary_list) / len(results_summary_list) if results_summary_list else 0
    max_execution_time = max(r["execution_time"] for r in results_summary_list) if results_summary_list else 0
    min_execution_time = min(r["execution_time"] for r in results_summary_list) if results_summary_list else 0
    
    time_stats = {
        "total_execution_time": total_execution_time,
        "total_execution_time_formatted": str(timedelta(seconds=total_execution_time)),
        "average_execution_time": avg_execution_time,
        "average_execution_time_formatted": str(timedelta(seconds=avg_execution_time)),
        "max_execution_time": max_execution_time,
        "max_execution_time_formatted": str(timedelta(seconds=max_execution_time)),
        "min_execution_time": min_execution_time,
        "min_execution_time_formatted": str(timedelta(seconds=min_execution_time)),
        "start_time": datetime.fromtimestamp(total_start_time).isoformat(),
        "end_time": datetime.fromtimestamp(total_end_time).isoformat()
    }
    
    # 保存参数搜索摘要结果 (不含 trades)
    summary_json_path = os.path.join(full_result_dir, "param_search_summary_results.json")
    results_for_summary_file = {
        "results": results_summary_list, # 只包含摘要
        "time_stats": time_stats
    }
    try:
        with open(summary_json_path, 'w') as f:
            json.dump(results_for_summary_file, f, indent=4, default=str)
        logger.info(f"参数搜索摘要结果已保存到: {summary_json_path}")
    except Exception as e:
        logger.error(f"保存参数搜索摘要结果时出错: {e}")

    # **** 新增：为每个参数组合生成独立的 HTML 报告 ****
    logger.info("开始为每个参数组合生成独立的 HTML 详细报告...")
    generated_report_paths = {}
    for result_summary in results_summary_list:
        param_idx = result_summary['param_index']
        run_result_dir = result_summary['result_dir'] # 每个运行的独立目录
        single_result_json_path = os.path.join(run_result_dir, "results.json")
        # 将独立报告放在主结果目录下，以 param_index 命名
        single_report_html_name = f"run_{param_idx}_report.html"
        single_report_html_path = os.path.join(full_result_dir, single_report_html_name)
        
        if os.path.exists(single_result_json_path):
            try:
                report_generated = generate_single_run_report(single_result_json_path, single_report_html_path)
                if report_generated:
                    # 保存相对路径用于主报告链接
                    generated_report_paths[param_idx] = single_report_html_name 
                    logger.info(f"  参数组合 {param_idx} 的详细报告已生成: {single_report_html_path}")
                else:
                    logger.error(f"  生成参数组合 {param_idx} 的详细报告失败 (从 {single_result_json_path})")
            except Exception as e:
                logger.error(f"  生成参数组合 {param_idx} 的详细报告时发生意外错误: {e}", exc_info=True)
        else:
            logger.warning(f"  未找到参数组合 {param_idx} 的详细结果文件: {single_result_json_path}，无法生成详细报告。")

    # **** 更新 results_summary_list，加入独立报告的相对路径 ****
    for result_summary in results_summary_list:
        param_idx = result_summary['param_index']
        if param_idx in generated_report_paths:
            result_summary['single_report_rel_path'] = generated_report_paths[param_idx]
        else:
            result_summary['single_report_rel_path'] = None
            
    # **** 更新摘要 JSON 文件，现在包含 single_report_rel_path ****
    results_for_summary_file_updated = {
        "results": results_summary_list, 
        "time_stats": time_stats
    }
    try:
        with open(summary_json_path, 'w') as f:
            json.dump(results_for_summary_file_updated, f, indent=4, default=str)
        logger.info(f"参数搜索摘要结果已更新并包含详细报告路径: {summary_json_path}")
    except Exception as e:
        logger.error(f"更新参数搜索摘要结果时出错: {e}")
        
    # **** 后续处理：凯利计算、筛选、生成聚合报告 ****
    # 注意：现在这些处理都基于 summary_json_path 或更新后的 results_summary_list
    
    # 1. 计算凯利分数 (直接修改摘要 JSON 文件)
    logger.info(f"开始为参数搜索摘要结果计算凯利分数: {summary_json_path}")
    kelly_success = calculate_and_add_kelly_to_json(summary_json_path)
    if kelly_success:
        logger.info("成功计算凯利分数并更新了摘要结果文件。")
    else:
        logger.error("计算凯利分数或更新摘要文件时出错。")

    # 2. 筛选高胜率结果 (从更新后的摘要 JSON 文件读取)
    high_win_rate_output_path = os.path.join(full_result_dir, "high_win_rate_50pct_results.csv")
    logger.info(f"开始筛选胜率 > 50% 的结果并保存到 {high_win_rate_output_path}")
    filter_success = filter_and_save_by_metric(
        json_file_path=summary_json_path, # 使用更新后的摘要文件
        output_file_path=high_win_rate_output_path,
        metric_column='win_rate',
        threshold=0.50,
        comparison_op='gt',
        output_format='csv'
    )
    if filter_success:
        logger.info("成功筛选并保存了高胜率结果。")
    else:
        logger.error("筛选高胜率结果或保存文件时出错。")
        
    # 修改: 重新加载包含凯利分数的结果，再生成报告
    logger.info(f"重新加载包含凯利分数的摘要结果: {summary_json_path}")
    try:
        with open(summary_json_path, 'r') as f:
            final_summary_data_with_kelly = json.load(f)
        # 确保传递给报告生成器的是最新的、包含凯利分数的数据
        results_list_with_kelly = final_summary_data_with_kelly.get('results', [])
        time_stats_from_file = final_summary_data_with_kelly.get('time_stats', time_stats) # 优先使用文件中的时间统计
        
        report_html_path = os.path.join(full_result_dir, "backtest_report.html")
        logger.info(f"开始生成 HTML 聚合回测报告: {report_html_path}")
        generate_html_report(summary_json_path, report_html_path, results_data=final_summary_data_with_kelly) # 传递整个字典
        logger.info(f"HTML 聚合报告已生成: {report_html_path}")
        
    except FileNotFoundError:
        logger.error(f"错误：未找到更新后的摘要 JSON 文件 {summary_json_path}，无法生成包含凯利分数的报告。")
        results_list_with_kelly = results_summary_list # Fallback to data without Kelly
        time_stats_from_file = time_stats
    except json.JSONDecodeError:
        logger.error(f"错误：解析更新后的摘要 JSON 文件 {summary_json_path} 失败，无法生成包含凯利分数的报告。")
        results_list_with_kelly = results_summary_list # Fallback
        time_stats_from_file = time_stats
    except Exception as e:
        logger.error(f"重新加载摘要 JSON 或生成报告时发生意外错误: {e}", exc_info=True)
        results_list_with_kelly = results_summary_list # Fallback
        time_stats_from_file = time_stats

    # 保存时间统计信息到单独文件 (使用最新的 time_stats)
    time_stats_path = os.path.join(full_result_dir, "time_stats.json")
    try:
        with open(time_stats_path, 'w') as f:
            json.dump(time_stats_from_file, f, indent=4, default=str)
    except Exception as e:
        logger.error(f"保存时间统计信息时出错: {e}")
    
    logger.info(f"参数网格搜索总用时: {timedelta(seconds=total_execution_time)}")
    logger.info(f"平均每个参数组合用时: {timedelta(seconds=avg_execution_time)}")
    logger.info(f"最长参数组合用时: {timedelta(seconds=max_execution_time)}")
    logger.info(f"最短参数组合用时: {timedelta(seconds=min_execution_time)}")
    
    # 修改: 找出最佳结果 (基于重新加载的 results_list_with_kelly)
    best_by_return = None
    best_by_win_rate = None
    if results_list_with_kelly: # 使用包含凯利分数的数据
        valid_results = [r for r in results_list_with_kelly if r.get("statistics")]
        if valid_results:
            try:
                best_by_return = max(valid_results, key=lambda x: float(x.get("statistics", {}).get("return_rate", -float('inf')) or -float('inf')))
            except (ValueError, TypeError) as e:
                logger.warning(f"查找最佳收益率时遇到非数值数据: {e}")
                best_by_return = max(valid_results, key=lambda x: -float('inf'))
                
            try:
                best_by_win_rate = max(valid_results, key=lambda x: float(x.get("statistics", {}).get("win_rate", -1.0) or -1.0))
            except (ValueError, TypeError) as e:
                logger.warning(f"查找最佳胜率时遇到非数值数据: {e}")
                best_by_win_rate = max(valid_results, key=lambda x: -1.0)

            # 打印最佳结果信息 (现在也包含 single_report_rel_path)
            if best_by_return:
                stats_return = best_by_return.get("statistics", {})
                win_rate_str = f"{stats_return.get('win_rate'):.2%}" if isinstance(stats_return.get('win_rate'), (int, float)) else str(stats_return.get('win_rate', 'N/A'))
                return_rate_str = f"{stats_return.get('return_rate'):.2%}" if isinstance(stats_return.get('return_rate'), (int, float)) else str(stats_return.get('return_rate', 'N/A'))
                kelly_str = f"{stats_return.get('kelly_fraction_calculated'):.4f}" if isinstance(stats_return.get('kelly_fraction_calculated'), (int, float)) else str(stats_return.get('kelly_fraction_calculated', 'N/A'))
                report_link = best_by_return.get('single_report_rel_path', '无')
                
                logger.info(f"最佳收益率参数组合: {best_by_return.get('params', {})}, "
                      f"胜率: {win_rate_str}, "
                      f"收益率: {return_rate_str}, "
                      f"凯利: {kelly_str}, "
                      f"用时: {timedelta(seconds=best_by_return.get('execution_time', 0))}, "
                      f"目录: {best_by_return.get('result_dir', 'N/A')}, "
                      f"详细报告: {report_link}")
            else:
                logger.info("未能确定最佳收益率参数组合。")

            if best_by_win_rate:
                stats_win = best_by_win_rate.get("statistics", {})
                win_rate_str = f"{stats_win.get('win_rate'):.2%}" if isinstance(stats_win.get('win_rate'), (int, float)) else str(stats_win.get('win_rate', 'N/A'))
                return_rate_str = f"{stats_win.get('return_rate'):.2%}" if isinstance(stats_win.get('return_rate'), (int, float)) else str(stats_win.get('return_rate', 'N/A'))
                kelly_str = f"{stats_win.get('kelly_fraction_calculated'):.4f}" if isinstance(stats_win.get('kelly_fraction_calculated'), (int, float)) else str(stats_win.get('kelly_fraction_calculated', 'N/A'))
                report_link = best_by_win_rate.get('single_report_rel_path', '无')
                
                logger.info(f"最佳胜率参数组合: {best_by_win_rate.get('params', {})}, "
                      f"胜率: {win_rate_str}, "
                      f"收益率: {return_rate_str}, "
                      f"凯利: {kelly_str}, "
                      f"用时: {timedelta(seconds=best_by_win_rate.get('execution_time', 0))}, "
                      f"目录: {best_by_win_rate.get('result_dir', 'N/A')}, "
                      f"详细报告: {report_link}")
            else:
                logger.info("未能确定最佳胜率参数组合。")
        else:
            logger.info("没有找到包含有效 'statistics' 的结果，无法确定最佳参数。")

    return {
        "best_by_win_rate": best_by_win_rate,
        "best_by_return": best_by_return,
        "all_results_summary": results_list_with_kelly, # 返回摘要列表
        "result_dir": full_result_dir,
        "time_stats": time_stats_from_file
    }


async def run_parameter_grid_v2(param_grid: Dict[str, List], max_concurrency: int = 4):
    """运行V2参数网格搜索

    Args:
        param_grid: 参数网格
        max_concurrency: 最大并发数量

    Returns:
        Dict[str, Any]: 参数搜索结果
    """
    logger.info(f"开始V2参数网格搜索, 参数网格: {param_grid}")

    # 创建主结果目录
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    search_result_dir_name = f"v2_param_search_{timestamp}"
    full_result_dir = os.path.join(RESULT_DIR, search_result_dir_name)
    os.makedirs(full_result_dir, exist_ok=True)

    # 生成参数组合
    parameter_combinations = ConfigManagerV2.generate_parameter_combinations(param_grid)
    logger.info(f"生成了 {len(parameter_combinations)} 个参数组合")

    # 并行执行回测（复用现有的并行执行逻辑，但使用V2引擎）
    semaphore = asyncio.Semaphore(max_concurrency)
    tasks = [
        run_backtest_v2_with_params(params, semaphore, i, len(parameter_combinations), full_result_dir)
        for i, params in enumerate(parameter_combinations)
    ]

    results_summary_list = await asyncio.gather(*tasks)

    # 复用现有的结果分析、凯利计算、报告生成逻辑
    # ... (与现有grid模式相同的后处理流程)

    return {
        "all_results_summary": results_summary_list,
        "result_dir": full_result_dir,
        "backtest_version": "v2"
    }


async def run_backtest_v2_with_params(params: Dict[str, Any], semaphore: asyncio.Semaphore,
                                     index: int, total: int, result_dir: str):
    """使用指定参数运行V2回测（包含信号量控制）

    Args:
        params: 参数字典
        semaphore: 控制并发的信号量
        index: 当前参数组合的索引
        total: 总参数组合数
        result_dir: 结果目录

    Returns:
        Dict: 包含参数和结果的字典
    """
    async with semaphore:
        logger.info(f"运行V2参数组合 {index+1}/{total}: {params}")

        start_time = time.time()

        config = BacktestConfigV2(**params)
        backtest = BacktestEngineV2(config)

        # 设置结果输出子目录
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backtest_subdir_name = f"v2_backtest_results_{timestamp}_{index+1}"
        backtest.result_dir = os.path.join(result_dir, backtest_subdir_name)
        os.makedirs(backtest.result_dir, exist_ok=True)

        result = None
        error_message = None
        try:
            result = await backtest.run_backtest()
        except Exception as e:
            logger.error(f"V2参数组合 {index+1}/{total} 执行出错: {e}", exc_info=True)
            error_message = f"V2 Backtest execution failed: {e}"
            result = {}

        end_time = time.time()
        execution_time = end_time - start_time

        # 准备返回结果
        final_statistics = result.get("statistics", {}) if isinstance(result, dict) else {}
        if error_message and not final_statistics.get("error"):
            final_statistics["error"] = error_message

        return {
            "param_index": index + 1,
            "params": params,
            "statistics": final_statistics,
            "result_dir": backtest.result_dir,
            "execution_time": execution_time,
            "total_params": total,
            "backtest_version": "v2"
        }


async def main():
    """主函数，解析命令行参数并运行回测"""
    global RESULT_DIR
    
    parser = argparse.ArgumentParser(description='运行KOL交易策略回测')
    parser.add_argument('--mode', choices=['single', 'grid', 'single_v2', 'grid_v2'], default='single',
                        help='回测模式: single-单次事件驱动回测, grid-参数网格搜索事件驱动回测, single_v2-单次直接分析回测, grid_v2-参数网格搜索直接分析回测')
    parser.add_argument('--config', type=str, help='配置文件路径')
    parser.add_argument('--param_grid', type=str, help='参数网格文件路径(JSON)')
    parser.add_argument('--max_concurrency', type=int, default=4, help='参数网格搜索最大并发数')
    parser.add_argument('--result_dir', type=str, default=RESULT_DIR, 
                       help=f'结果保存目录，默认为 {RESULT_DIR}')
    
    # 时间范围参数
    parser.add_argument('--start_time', type=int, help='回测开始时间 (Unix时间戳)')
    parser.add_argument('--end_time', type=int, help='回测结束时间 (Unix时间戳)')
    
    # KOL交易策略参数
    parser.add_argument('--transaction_lookback_hours', type=int, help='交易回溯小时数')
    parser.add_argument('--transaction_min_amount', type=float, help='最低交易金额')
    parser.add_argument('--kol_account_min_count', type=int, help='最低KOL账号数')
    parser.add_argument('--kol_account_min_txs', type=int, help='每个KOL账号最低交易数')
    parser.add_argument('--kol_account_max_txs', type=int, help='每个KOL账号最高交易数')
    parser.add_argument('--kol_min_winrate_7d', type=float, help='筛选KOL的最低7日胜率阈值 (0到1之间)')
    parser.add_argument('--token_mint_lookback_hours', type=int, help='代币创建回溯小时数')
    
    # 卖出策略参数
    parser.add_argument('--sell_strategy_hours', type=int, help='卖出策略回溯小时数')
    parser.add_argument('--sell_kol_ratio', type=float, help='卖出KOL比例阈值')
    
    # 回测控制参数
    parser.add_argument('--processing_interval', type=int, help='信号处理间隔 (秒)')
    parser.add_argument('--initial_capital', type=float, help='初始资金')
    parser.add_argument('--same_token_notification_interval_minutes', type=int, help='相同代币通知的最小间隔 (分钟)')
    
    start_time = time.time()
    args = parser.parse_args()

    RESULT_DIR = args.result_dir
    await init_db()

    # 统一配置加载
    config, backtest_version = load_config_unified(args)

    if args.mode == 'single':
        result = await run_single_backtest(config)
        logger.info(f"单次事件驱动回测完成，结果保存在 {result.get('result_dir', '')}")

    elif args.mode == 'single_v2':
        result = await run_single_backtest_v2(config)
        logger.info(f"单次直接分析回测完成，结果保存在 {result.get('result_dir', '')}")

    elif args.mode == 'grid':
        # 参数网格搜索模式
        if args.param_grid:
            with open(args.param_grid, 'r') as f:
                param_grid = json.load(f)
        else:
            # 默认参数网格
            param_grid = {
                'transaction_lookback_hours': [12, 24],
                'transaction_min_amount': [500, 1000],
                'kol_account_min_count': [3, 5],
                'sell_strategy_hours': [12, 24],
                'sell_kol_ratio': [0.3, 0.5, 0.7]
            }

        result = await run_parameter_grid(param_grid, args.max_concurrency)
        logger.info(f"事件驱动参数网格搜索完成，结果保存在 {result.get('result_dir', '')}")

    elif args.mode == 'grid_v2':
        # V2参数网格搜索模式
        if args.param_grid:
            with open(args.param_grid, 'r') as f:
                param_grid = json.load(f)
        else:
            # V2默认参数网格
            param_grid = {
                'transaction_lookback_hours': [12, 24, 48],
                'kol_account_min_count': [3, 5, 7],
                'sell_strategy_hours': [12, 24],
                'sell_kol_ratio': [0.3, 0.5, 0.7],
                'token_mint_lookback_hours': [24, 48, 72]
            }

        result = await run_parameter_grid_v2(param_grid, args.max_concurrency)
        logger.info(f"直接分析参数网格搜索完成，结果保存在 {result.get('result_dir', '')}")

    else:
        logger.error(f"不支持的回测模式: {args.mode}")
        return
    
    end_time = time.time()
    execution_time = end_time - start_time
    logger.info(f"程序总运行时间: {timedelta(seconds=execution_time)}")


if __name__ == "__main__":
    asyncio.run(main()) 