import logging
from typing import Dict, Any, List
import json
import asyncio
from utils.spiders.smart_money import BasicSpider

logger = logging.getLogger(__name__)

class GmgnTokenWindowSpider(BasicSpider):
    """GMGN代币多窗口信息爬虫
    
    用于获取代币的多窗口信息，包括：
    - 基本信息（地址、符号、名称等）
    - 池子信息
    - 开发者信息
    - 价格和交易量信息
    """
    
    BASE_URL = "https://gmgn.ai/api/v1/mutil_window_token_info"
    
    def __init__(self, max_retries: int = 3, retry_interval: float = 1.0):
        super().__init__(max_retries=max_retries, retry_interval=retry_interval)
        self.headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-TW;q=0.6',
            'cache-control': 'no-cache',
            'content-type': 'application/json',
            'origin': 'https://gmgn.ai',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://gmgn.ai/sol/token/dDO6ZdYE_6p6xgHyF7AeE6TZkSmFsko444wqoP15icUSqi2jfGiPN',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
    
    async def setup(self):
        """设置爬虫，初始化会话并更新头信息"""
        await self.init_sessions()
        # 更新会话头信息
        self.session.headers.update(self.headers)
        # 设置浏览器模拟
        self.session.impersonate = "chrome110"  # 模拟Chrome浏览器
    
    async def get_token_window_info(self, chain: str, addresses: List[str]) -> List[Dict[str, Any]]:
        """
        获取代币多窗口信息
        
        Args:
            chain: 链名称
            addresses: 代币地址列表
            
        Returns:
            List[Dict[str, Any]]: 代币多窗口信息列表
        """
        try:
                
            logger.info(f"正在获取代币多窗口信息: {addresses}")
            
            # 构建请求数据
            request_data = {
                "chain": chain,
                "addresses": addresses
            }
            
            # 使用带有重试机制的post方法
            response = await self.post(
                self.BASE_URL,
                json=request_data,
                params={
                    'device_id': '65990bc3-0a13-4d99-8c66-eeea94916b0f',
                    'client_id': 'gmgn_web_2025.0227.180046',
                    'from_app': 'gmgn',
                    'app_ver': '2025.0227.180046',
                    'tz_name': 'Asia/Shanghai',
                    'tz_offset': '28800',
                    'app_lang': 'zh-CN'
                }
            )
            
            # 记录响应状态和内容
            logger.debug(f"API响应状态码: {response.status_code}")
            logger.debug(f"API响应内容: {response.text[:500]}...")  # 只记录前500个字符
            
            # 检查响应状态
            if response.status_code != 200:
                logger.error(f"获取代币多窗口信息失败: HTTP {response.status_code}")
                logger.error(f"错误响应: {response.text}")
                return []
                
            # 解析响应数据
            try:
                data = response.json()
                logger.debug(f"解析后的API响应: {data}")
                
                # 检查API响应格式
                if not isinstance(data, dict):
                    logger.error(f"API响应格式错误，期望dict类型，实际为: {type(data)}")
                    return []
                    
                # 检查API响应状态
                if data.get('code') != 0:  # 0 表示成功
                    logger.error(f"API返回错误: {data.get('message', '未知错误')}")
                    return []
                    
                # 提取实际数据
                result = data.get('data', [])
                if not result:
                    logger.warning("API响应中没有data字段")
                    return []
                    
                # 格式化数据
                formatted_data = []
                for token_info in result:
                    formatted_token = {
                        # 基本信息
                        'chain': chain,
                        'address': token_info.get('address'),
                        'symbol': token_info.get('symbol'),
                        'name': token_info.get('name'),
                        'decimals': token_info.get('decimals'),
                        'logo': token_info.get('logo'),
                        'biggest_pool_address': token_info.get('biggest_pool_address'),
                        'open_timestamp': token_info.get('open_timestamp'),
                        'holder_count': token_info.get('holder_count'),
                        'circulating_supply': token_info.get('circulating_supply'),
                        'total_supply': token_info.get('total_supply'),
                        'max_supply': token_info.get('max_supply'),
                        'liquidity': float(token_info.get('liquidity', '0')),
                        'creation_timestamp': token_info.get('creation_timestamp'),
                        
                        # 池子信息
                        'pool': token_info.get('pool', {}),
                        
                        # 开发者信息
                        'dev': token_info.get('dev', {}),
                        
                        # 价格信息
                        'price': token_info.get('price', {})
                    }
                    formatted_data.append(formatted_token)
                
                logger.info(f"成功获取 {len(formatted_data)} 个代币的多窗口信息")
                return formatted_data
                
            except json.JSONDecodeError as e:
                logger.error(f"解析API响应JSON失败: {str(e)}")
                logger.error(f"原始响应: {response.text}")
                return []
                
        except Exception as e:
            logger.error(f"获取代币多窗口信息时发生错误: {str(e)}")
            return []

async def main():
    spider = GmgnTokenWindowSpider()
    data = await spider.get_token_window_info(
        "sol",
        ["6p6xgHyF7AeE6TZkSmFsko444wqoP15icUSqi2jfGiPN"]
    )
    print(json.dumps(data, indent=2))
    # 关闭爬虫会话连接
    await spider.close()

if __name__ == "__main__":
    # 使用asyncio运行异步主函数
    asyncio.run(main()) 