# 交易记录实际金额定期更新功能技术方案

## 1. 技术架构

### 1.1 整体架构
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Workflow      │    │   Handler        │    │   Updater       │
│   Scheduler     │───▶│   Entry Point    │───▶│   Core Logic    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │   TradeRecord    │    │   Solana        │
                       │   DAO            │    │   Monitor       │
                       └──────────────────┘    └─────────────────┘
```

### 1.2 组件说明

#### 1.2.1 Workflow配置
- 文件: `trade_record_actual_amount_updater_workflow.yaml`
- 功能: 定义执行间隔和参数
- 位置: `/workflows/trade_record_actual_amount_updater/`

#### 1.2.2 Handler处理器
- 文件: `handler.py`
- 功能: 工作流入口点，错误处理和日志记录
- 位置: `/workflows/trade_record_actual_amount_updater/`

#### 1.2.3 核心更新器
- 文件: `trade_record_verification_updater.py`
- 功能: 核心业务逻辑实现
- 位置: `/utils/trading/`

## 2. 详细设计

### 2.1 工作流配置设计

```yaml
# trade_record_actual_amount_updater_workflow.yaml
name: trade_record_actual_amount_updater
description: "定期更新交易记录实际金额"
concurrency: 1

nodes:
  - name: TradeRecordScannerNode
    node_type: input
    interval: 10  # 10秒扫描间隔
    process_item: "workflows.handlers.trade_record_verification_updater.scan_pending_records"
    
  - name: AmountVerificationNode
    node_type: process
    depend_ons: ["TradeRecordScannerNode"]
    concurrency: 2
    process_item: "workflows.handlers.trade_record_verification_updater.verify_amount"
    flow_control:
      max_pending_messages: 10
      message_timeout: 300
    
  - name: RecordUpdateNode
    node_type: storage
    depend_ons: ["AmountVerificationNode"]
    batch_size: 50
    store_data: "workflows.handlers.trade_record_verification_updater.update_records"
```

### 2.2 Handler设计

```python
# workflows/trade_record_actual_amount_updater/handler.py
import logging
from datetime import datetime
from utils.trading.trade_record_verification_updater import TradeRecordVerificationUpdater

logger = logging.getLogger(__name__)

async def perform_trade_record_actual_amount_update_task():
    """
    执行交易记录实际金额更新任务
    """
    start_time = datetime.now()
    logger.info(f"开始执行交易记录实际金额更新任务: {start_time}")
    
    try:
        updater = TradeRecordVerificationUpdater()
        result = await updater.update_trade_record_amounts()
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        logger.info(f"交易记录实际金额更新任务完成: 耗时{duration}秒, 结果: {result}")
        
        return {
            "status": "success",
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat(),
            "duration_seconds": duration,
            "result": result
        }
        
    except Exception as e:
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        logger.error(f"交易记录实际金额更新任务失败: {str(e)}", exc_info=True)
        
        return {
            "status": "error",
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat(),
            "duration_seconds": duration,
            "error": str(e)
        }
```

### 2.3 核心更新器设计

```python
# utils/trading/trade_record_verification_updater.py
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any
from dao.trade_record_dao import TradeRecordDao
from models.trade_record import TradeRecord
from utils.solana_monitor import SolanaMonitor
from utils.database import get_db_session

logger = logging.getLogger(__name__)

class TradeRecordVerificationUpdater:
    """
    交易记录验证更新器
    """
    
    def __init__(self, scan_hours: int = 24, batch_size: int = 50):
        self.scan_hours = scan_hours
        self.batch_size = batch_size
        self.trade_record_dao = TradeRecordDao()
        self.solana_monitor = SolanaMonitor()
        
    async def update_trade_record_amounts(self) -> Dict[str, Any]:
        """
        更新交易记录实际金额
        """
        logger.info(f"开始扫描最近{self.scan_hours}小时的交易记录")
        
        # 获取需要更新的交易记录
        records_to_update = await self._get_records_to_update()
        
        if not records_to_update:
            logger.info("没有需要更新的交易记录")
            return {
                "total_records": 0,
                "updated_records": 0,
                "failed_records": 0,
                "errors": []
            }
        
        logger.info(f"找到{len(records_to_update)}条需要更新的交易记录")
        
        # 批量处理
        updated_count = 0
        failed_count = 0
        errors = []
        
        for i in range(0, len(records_to_update), self.batch_size):
            batch = records_to_update[i:i + self.batch_size]
            batch_result = await self._process_batch(batch)
            
            updated_count += batch_result["updated"]
            failed_count += batch_result["failed"]
            errors.extend(batch_result["errors"])
            
            logger.info(f"处理批次 {i//self.batch_size + 1}: 更新{batch_result['updated']}条, 失败{batch_result['failed']}条")
        
        result = {
            "total_records": len(records_to_update),
            "updated_records": updated_count,
            "failed_records": failed_count,
            "errors": errors
        }
        
        logger.info(f"更新完成: {result}")
        return result
    
    async def _get_records_to_update(self) -> List[TradeRecord]:
        """
        获取需要更新的交易记录
        """
        cutoff_time = datetime.now() - timedelta(hours=self.scan_hours)
        
        with get_db_session() as session:
            # 优先获取token_out_actual_amount为空的记录
            records = self.trade_record_dao.get_records_for_amount_update(
                session=session,
                since_time=cutoff_time,
                limit=1000  # 限制单次处理数量
            )
            
        return records
    
    async def _process_batch(self, batch: List[TradeRecord]) -> Dict[str, Any]:
        """
        批量处理交易记录
        """
        updated = 0
        failed = 0
        errors = []
        
        with get_db_session() as session:
            for record in batch:
                try:
                    # 解析交易获取实际金额
                    actual_amount = await self._get_actual_amount_from_tx(record)
                    
                    if actual_amount is not None:
                        # 更新记录
                        record.token_out_verified_amount = actual_amount
                        record.token_out_verified_at = datetime.now()
                        record.amount_updated_at = datetime.now()
                        
                        self.trade_record_dao.update(session, record)
                        updated += 1
                        
                        logger.debug(f"更新交易记录 {record.id}: 验证金额 {actual_amount}")
                    else:
                        failed += 1
                        error_msg = f"无法获取交易 {record.transaction_signature} 的实际金额"
                        errors.append(error_msg)
                        logger.warning(error_msg)
                        
                except Exception as e:
                    failed += 1
                    error_msg = f"处理交易记录 {record.id} 时出错: {str(e)}"
                    errors.append(error_msg)
                    logger.error(error_msg, exc_info=True)
            
            # 提交批次更新
            try:
                session.commit()
                logger.debug(f"批次更新提交成功: 更新{updated}条记录")
            except Exception as e:
                session.rollback()
                error_msg = f"批次更新提交失败: {str(e)}"
                errors.append(error_msg)
                logger.error(error_msg, exc_info=True)
                # 将所有更新标记为失败
                failed += updated
                updated = 0
        
        return {
            "updated": updated,
            "failed": failed,
            "errors": errors
        }
    
    async def _get_actual_amount_from_tx(self, record: TradeRecord) -> float:
        """
        从交易中获取实际输出金额
        """
        try:
            if not record.transaction_signature:
                logger.warning(f"交易记录 {record.id} 缺少交易签名")
                return None
            
            # 使用现有的方法解析交易
            token_output = await self.solana_monitor.get_confirmed_token_output_from_tx(
                tx_signature=record.transaction_signature,
                wallet_address=record.wallet_address,
                token_mint=record.token_out_mint
            )
            
            if token_output and 'amount' in token_output:
                return float(token_output['amount'])
            else:
                logger.warning(f"交易 {record.transaction_signature} 未找到有效的输出金额")
                return None
                
        except Exception as e:
            logger.error(f"解析交易 {record.transaction_signature} 时出错: {str(e)}")
            return None
```

### 2.4 数据访问层扩展

```python
# dao/trade_record_dao.py 新增方法
def get_records_for_amount_update(self, session, since_time: datetime, limit: int = 1000) -> List[TradeRecord]:
    """
    获取需要更新实际金额的交易记录
    """
    query = session.query(TradeRecord).filter(
        TradeRecord.created_at >= since_time,
        TradeRecord.transaction_signature.isnot(None),
        TradeRecord.status == 'completed'
    )
    
    # 优先处理token_out_verified_amount为空的记录
    query = query.order_by(
        TradeRecord.token_out_verified_amount.is_(None).desc(),
        TradeRecord.created_at.desc()
    )
    
    return query.limit(limit).all()
```

### 2.5 模型扩展

```python
# models/trade_record.py 新增字段
class TradeRecord(Base):
    # ... 现有字段 ...
    
    # 新增字段
    amount_updated_at = Column(DateTime, nullable=True, comment="实际金额更新时间")
```

## 3. 实施步骤

### 3.1 第一阶段：基础框架
1. 创建工作流目录结构
2. 实现基础的Handler和配置文件
3. 创建核心更新器类框架

### 3.2 第二阶段：核心逻辑
1. 实现交易记录查询逻辑
2. 集成Solana交易解析功能
3. 实现批量更新逻辑

### 3.3 第三阶段：优化和监控
1. 添加详细的日志和监控
2. 实现错误处理和重试机制
3. 性能优化和测试

### 3.4 第四阶段：部署和验证
1. 单元测试和集成测试
2. 测试环境部署验证
3. 生产环境部署

## 4. 技术考虑

### 4.1 性能优化
- 使用批量查询和更新减少数据库访问
- 实现并发处理提高效率
- 添加缓存机制避免重复解析

### 4.2 错误处理
- 网络异常重试机制
- 数据库事务回滚
- 详细的错误日志记录

### 4.3 监控告警
- 处理成功率监控
- 执行时间监控
- 异常情况告警

### 4.4 数据一致性
- 使用数据库事务保证一致性
- 添加数据验证逻辑
- 支持手动回滚机制

## 5. 测试策略

### 5.1 单元测试

**测试文件**: `tests/test_trade_record_verification_updater.py`

```python
import pytest
from unittest.mock import Mock, patch
from datetime import datetime, timezone, timedelta

from workflows.handlers.trade_record_verification_updater import TradeRecordVerificationUpdater
from models.trade_record import TradeRecord, TradeStatus, VerificationStatus

class TestTradeRecordVerificationUpdater:
    
    @pytest.fixture
    def updater(self):
        return TradeRecordVerificationUpdater()
    
    @pytest.fixture
    def sample_trade_record(self):
        return TradeRecord(
            id="test_id",
            tx_hash="test_tx_hash",
            status=TradeStatus.SUCCESS,
            token_out_verified_amount=None,
            verification_status=VerificationStatus.PENDING,
            created_at=datetime.now(timezone.utc)
        )
    
    async def test_get_records_for_verification_success(self, updater):
        """测试获取需要验证的记录"""
        with patch.object(updater.dao, 'get_records_for_verification') as mock_get:
            mock_get.return_value = [Mock()]
            
            records = await updater._get_records_for_verification()
            
            assert len(records) > 0
            mock_get.assert_called_once()
    
    async def test_verify_transaction_success(self, updater, sample_trade_record):
        """测试验证交易成功"""
        with patch('workflows.handlers.trade_record_verification_updater.get_confirmed_token_output_from_tx') as mock_parse:
            mock_parse.return_value = 1000.0
            
            result = await updater._verify_transaction_amount(sample_trade_record)
            
            assert result == 1000.0
            mock_parse.assert_called_once_with(sample_trade_record.tx_hash)
    
    async def test_update_verification_batch_success(self, updater):
        """测试批量更新验证记录成功"""
        updates = [
            {"record_id": "test_id_1", "verified_amount": 1000.0, "verification_status": VerificationStatus.VERIFIED},
            {"record_id": "test_id_2", "verified_amount": 2000.0, "verification_status": VerificationStatus.VERIFIED}
        ]
        
        with patch.object(updater.dao, 'batch_update_verified_amounts') as mock_update:
            mock_update.return_value = 2
            
            count = await updater._update_verification_batch(updates)
            
            assert count == 2
            mock_update.assert_called_once_with(updates)
```

- 核心更新器逻辑测试
- 交易解析功能测试
- 数据访问层测试

### 5.2 集成测试

**测试文件**: `tests/integration/test_trade_record_verification_integration.py`

- 完整验证工作流测试
- 数据库操作测试
- 外部API调用测试
- 错误恢复测试
- 验证状态转换测试

### 5.3 性能测试

**测试文件**: `tests/performance/test_trade_record_verification_performance.py`

- 10秒间隔批量处理性能测试
- 50条记录批处理优化测试
- 内存使用测试
- 并发处理测试
- 大数据量验证处理测试

## 6. 部署配置

### 6.1 生产环境配置

**配置文件**: `config/workflows/trade_record_verification_updater.yaml`

```yaml
name: trade_record_verification_updater
concurrency: 1

nodes:
  - name: TradeRecordScannerNode
    node_type: input
    interval: 10  # 10秒扫描间隔
    process_item: "workflows.handlers.trade_record_verification_updater.scan_pending_records"
    
  - name: AmountVerificationNode
    node_type: process
    depend_ons: ["TradeRecordScannerNode"]
    concurrency: 2
    process_item: "workflows.handlers.trade_record_verification_updater.verify_amount"
    
  - name: RecordUpdateNode
    node_type: storage
    depend_ons: ["AmountVerificationNode"]
    batch_size: 50
    store_data: "workflows.handlers.trade_record_verification_updater.update_records"

logging:
  level: INFO
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

monitoring:
  metrics_enabled: true
  alert_on_failure: true
  performance_tracking: true
  verification_status_tracking: true
```

### 6.2 开发环境配置

**配置文件**: `config/workflows/dev/trade_record_verification_updater.yaml`

```yaml
name: trade_record_verification_updater_dev
concurrency: 1

nodes:
  - name: TradeRecordScannerNode
    node_type: input
    interval: 30  # 30秒扫描间隔
    process_item: "workflows.handlers.trade_record_verification_updater.scan_pending_records"
    
  - name: AmountVerificationNode
    node_type: process
    depend_ons: ["TradeRecordScannerNode"]
    concurrency: 1
    process_item: "workflows.handlers.trade_record_verification_updater.verify_amount"
    
  - name: RecordUpdateNode
    node_type: storage
    depend_ons: ["AmountVerificationNode"]
    batch_size: 10
    store_data: "workflows.handlers.trade_record_verification_updater.update_records"

logging:
  level: DEBUG
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

monitoring:
  metrics_enabled: false
  alert_on_failure: false
  performance_tracking: true
  verification_status_tracking: true
```

### 6.3 环境变量
```bash
# 工作流配置
TRADE_RECORD_UPDATER_INTERVAL=10
TRADE_RECORD_UPDATER_BATCH_SIZE=50
TRADE_RECORD_UPDATER_SCAN_HOURS=24

# 监控配置
TRADE_RECORD_UPDATER_ALERT_THRESHOLD=0.95
```

### 6.4 监控配置
- 添加Prometheus指标
- 配置Grafana仪表板
- 设置告警规则
- 验证状态跟踪