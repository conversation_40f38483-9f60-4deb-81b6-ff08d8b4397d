import unittest
from unittest.mock import Mock, AsyncMock, patch
from decimal import Decimal
from datetime import datetime
from beanie import PydanticObjectId

from utils.kol_scoring_service import KOLScoringService
from models.trade_record import TradeType, TradeStatus, TradeRecord
from models.config import KOLScoringConfig, KOLStrategyScoringParams
from models.signal import Signal
from models.trade_score_log import TradeScoreLog


class TestKOLScoringService(unittest.IsolatedAsyncioTestCase):
    """KOL评分服务单元测试"""
    
    def setUp(self):
        """测试前置设置"""
        self.service = KOLScoringService()
        
        # 创建Mock交易记录，模拟TradeRecord但不依赖Beanie初始化
        self.buy_trade = Mock()
        self.buy_trade.id = str(PydanticObjectId())
        self.buy_trade.signal_id = str(PydanticObjectId())
        self.buy_trade.strategy_name = "test_strategy"
        self.buy_trade.trade_provider = "gmgn"
        self.buy_trade.trade_type = TradeType.BUY
        self.buy_trade.status = TradeStatus.SUCCESS
        self.buy_trade.token_in_address = "So11111111111111111111111111111111111111112"  # SOL (计价货币)
        self.buy_trade.token_out_address = "test_token_address" # 目标代币
        self.buy_trade.wallet_address = "test_wallet_address"
        # FR-KOLSC-005: 买入成本是 token_in_actual_amount (花费的SOL)
        self.buy_trade.token_in_actual_amount = 100.0  # 花费100 SOL
        self.buy_trade.token_out_actual_amount = 1000.0  # 得到1000个目标代币 (这个用于验证，不直接用于PnL)
        self.buy_trade.created_at = datetime(2024, 1, 1, 10, 0, 0)
        
        self.sell_trade = Mock()
        self.sell_trade.id = str(PydanticObjectId())
        self.sell_trade.signal_id = str(PydanticObjectId())
        self.sell_trade.strategy_name = "test_strategy"
        self.sell_trade.trade_provider = "gmgn"
        self.sell_trade.trade_type = TradeType.SELL
        self.sell_trade.status = TradeStatus.SUCCESS
        self.sell_trade.token_in_address = "test_token_address"  # 卖出目标代币
        self.sell_trade.token_out_address = "So11111111111111111111111111111111111111112"  # 换回SOL (计价货币)
        self.sell_trade.wallet_address = "test_wallet_address"
        self.sell_trade.token_in_actual_amount = 1000.0  # 卖出1000个目标代币 (这个用于验证，不直接用于PnL)
        # FR-KOLSC-005: 卖出收入是 token_out_actual_amount (得到的SOL)
        self.sell_trade.token_out_actual_amount = 120.0  # 得到120 SOL
        self.sell_trade.created_at = datetime(2024, 1, 1, 12, 0, 0)  # 2小时后卖出
        
        # 测试用的评分配置
        self.scoring_config = KOLScoringConfig(
            default_positive_score=10.0,
            default_negative_score_multiplier=0.1,
            min_pnl_threshold=Decimal("0.01"), # 添加一个小的PnL阈值用于测试
            min_roi_threshold=Decimal("0.001") # 添加一个小的ROI阈值用于测试
        )

        # Mock 依赖的 DAO 实例 (它们在KOLScoringService的__init__中被实例化)
        # 我们需要patch这些DAO在KOLScoringService模块内的引用
        self.patcher_score_dao = patch('utils.kol_scoring_service.KOLStrategyScoreDAO', autospec=True)
        self.MockKOLStrategyScoreDAO_class = self.patcher_score_dao.start()
        self.mock_score_dao_instance = self.MockKOLStrategyScoreDAO_class.return_value
        self.mock_score_dao_instance.update_score = AsyncMock(return_value=True)
        self.addCleanup(self.patcher_score_dao.stop)

        self.patcher_log_dao = patch('utils.kol_scoring_service.TradeScoreLogDAO', autospec=True)
        self.MockTradeScoreLogDAO_class = self.patcher_log_dao.start()
        self.mock_log_dao_instance = self.MockTradeScoreLogDAO_class.return_value
        self.mock_log_dao_instance.has_log_entry_existed = AsyncMock(return_value=False) # Default: not existed
        self.mock_log_dao_instance.create_scoring_log = AsyncMock(return_value=Mock()) # Returns a mock log entry
        self.addCleanup(self.patcher_log_dao.stop)

        self.patcher_trade_dao = patch('utils.kol_scoring_service.TradeRecordDAO', autospec=True)
        self.MockTradeRecordDAO_class = self.patcher_trade_dao.start()
        # self.mock_trade_dao_instance = self.MockTradeRecordDAO_class.return_value
        # Not strictly needed for score_individual_kol_combination if it only receives TradeRecord objects
        self.addCleanup(self.patcher_trade_dao.stop)

        self.patcher_signal_dao = patch('utils.kol_scoring_service.SignalDAO', autospec=True)
        self.MockSignalDAO_class = self.patcher_signal_dao.start()
        # self.mock_signal_dao_instance = self.MockSignalDAO_class.return_value
        self.addCleanup(self.patcher_signal_dao.stop)

        self.patcher_config_dao = patch('utils.kol_scoring_service.ConfigDAO', autospec=True)
        self.MockConfigDAO_class = self.patcher_config_dao.start()
        self.mock_config_dao_instance = self.MockConfigDAO_class.return_value
        self.mock_config_dao_instance.get_kol_scoring_config = AsyncMock(return_value=self.scoring_config) # Default: return global config
        self.addCleanup(self.patcher_config_dao.stop)

        self.patcher_kol_wallet_dao = patch('utils.kol_scoring_service.KOLWalletDAO', autospec=True) # KOLWalletDAO is used in service
        self.MockKOLWalletDAO_class = self.patcher_kol_wallet_dao.start()
        # self.mock_kol_wallet_dao_instance = self.MockKOLWalletDAO_class.return_value 
        # Not directly used by score_individual_kol_combination but by other service methods potentially.
        self.addCleanup(self.patcher_kol_wallet_dao.stop)

        # Re-initialize service to use patched DAOs
        self.service = KOLScoringService()

    def test_calculate_pnl_profit(self):
        """测试计算盈利的PnL"""
        pnl_result = self.service.calculate_pnl(self.buy_trade, self.sell_trade)
        
        # 验证PnL计算: 120 - 100 = 20
        self.assertEqual(pnl_result, 20.0)
        
    def test_calculate_pnl_loss(self):
        """测试计算亏损的PnL"""
        # 创建亏损场景的Mock卖出交易
        loss_sell_trade = Mock()
        loss_sell_trade.id = str(PydanticObjectId())
        loss_sell_trade.signal_id = str(PydanticObjectId())
        loss_sell_trade.strategy_name = "test_strategy"
        loss_sell_trade.trade_provider = "gmgn"
        loss_sell_trade.trade_type = TradeType.SELL
        loss_sell_trade.status = TradeStatus.SUCCESS
        loss_sell_trade.token_in_address = "test_token_address"
        loss_sell_trade.token_out_address = "So11111111111111111111111111111111111111112"
        loss_sell_trade.wallet_address = "test_wallet_address"
        loss_sell_trade.token_in_actual_amount = 1000.0 # 卖出代币数量
        loss_sell_trade.token_out_actual_amount = 80.0  # 只得到80 SOL，亏损
        loss_sell_trade.created_at = datetime(2024, 1, 1, 12, 0, 0)
        
        pnl_result = self.service.calculate_pnl(self.buy_trade, loss_sell_trade)
        
        # 验证PnL计算: 80 - 100 = -20
        self.assertEqual(pnl_result, -20.0)
        
    def test_calculate_pnl_break_even(self):
        """测试计算盈亏平衡的PnL"""
        # 创建盈亏平衡场景的Mock卖出交易
        break_even_sell_trade = Mock()
        break_even_sell_trade.id = str(PydanticObjectId())
        break_even_sell_trade.signal_id = str(PydanticObjectId())
        break_even_sell_trade.strategy_name = "test_strategy"
        break_even_sell_trade.trade_provider = "gmgn"
        break_even_sell_trade.trade_type = TradeType.SELL
        break_even_sell_trade.status = TradeStatus.SUCCESS
        break_even_sell_trade.token_in_address = "test_token_address"
        break_even_sell_trade.token_out_address = "So11111111111111111111111111111111111111112"
        break_even_sell_trade.wallet_address = "test_wallet_address"
        break_even_sell_trade.token_in_actual_amount = 1000.0 # 卖出代币数量
        break_even_sell_trade.token_out_actual_amount = 100.0  # 恰好100 SOL，盈亏平衡
        break_even_sell_trade.created_at = datetime(2024, 1, 1, 12, 0, 0)
        
        pnl_result = self.service.calculate_pnl(self.buy_trade, break_even_sell_trade)
        
        # 验证PnL计算: 100 - 100 = 0
        self.assertEqual(pnl_result, 0.0)
        
    def test_calculate_score_changes_profit_default_config(self):
        """测试使用默认配置计算盈利的分数变化"""
        pnl = 20.0
        strategy_name = "test_strategy"
        
        positive_change, negative_change = self.service.calculate_score_changes(
            pnl=pnl,
            strategy_name=strategy_name,
            config=self.scoring_config
        )
        
        # 盈利时给加分：使用默认正分
        self.assertEqual(positive_change, 10.0)
        self.assertEqual(negative_change, 0.0)
        
    def test_calculate_score_changes_loss_default_config(self):
        """测试使用默认配置计算亏损的分数变化"""
        pnl = -20.0
        strategy_name = "test_strategy"
        
        positive_change, negative_change = self.service.calculate_score_changes(
            pnl=pnl,
            strategy_name=strategy_name,
            config=self.scoring_config
        )
        
        # 亏损时给扣分：使用固定分数 default_negative_score_multiplier * -1 = 0.1 * -1 = -0.1
        self.assertEqual(positive_change, 0.0)
        self.assertEqual(negative_change, -0.1)
        
    def test_calculate_score_changes_zero_pnl(self):
        """测试零盈亏的分数变化"""
        pnl = 0.0
        strategy_name = "test_strategy"
        
        positive_change, negative_change = self.service.calculate_score_changes(
            pnl=pnl,
            strategy_name=strategy_name,
            config=self.scoring_config
        )
        
        # 零盈亏不应该有分数变化
        self.assertEqual(positive_change, 0.0)
        self.assertEqual(negative_change, 0.0)
        
    def test_calculate_score_changes_profit_strategy_config(self):
        """测试使用策略特定配置计算盈利的分数变化"""
        # 创建带策略特定配置的配置对象
        strategy_config = KOLScoringConfig(
            default_positive_score=10.0,
            default_negative_score_multiplier=0.1,
            strategy_specific_params={
                "test_strategy": KOLStrategyScoringParams(
                    positive_score=15.0,  # 策略特定的正分
                    negative_score_multiplier=0.2
                )
            }
        )
        
        pnl = 25.0
        strategy_name = "test_strategy"
        
        positive_change, negative_change = self.service.calculate_score_changes(
            pnl=pnl,
            strategy_name=strategy_name,
            config=strategy_config
        )
        
        # 使用策略特定的正分
        self.assertEqual(positive_change, 15.0)
        self.assertEqual(negative_change, 0.0)
        
    def test_calculate_score_changes_loss_strategy_config(self):
        """测试使用策略特定配置计算亏损的分数变化"""
        strategy_config = KOLScoringConfig(
            default_positive_score=10.0,
            default_negative_score_multiplier=0.1,
            strategy_specific_params={
                "test_strategy": KOLStrategyScoringParams(
                    positive_score=15.0,
                    negative_score_multiplier=0.2  # 策略特定的固定负分
                )
            }
        )
        
        pnl = -30.0
        strategy_name = "test_strategy"
        
        positive_change, negative_change = self.service.calculate_score_changes(
            pnl=pnl,
            strategy_name=strategy_name,
            config=strategy_config
        )
        
        # 使用策略特定的固定负分：negative_score_multiplier * -1 = 0.2 * -1 = -0.2
        self.assertEqual(positive_change, 0.0)
        self.assertEqual(negative_change, -0.2)
        
    def test_validate_trade_pair_valid(self):
        """测试验证有效的交易对"""
        kol_wallet_address = self.buy_trade.wallet_address
        strategy_name = self.buy_trade.strategy_name
        passed, message = self.service._validate_trade_pair(self.buy_trade, self.sell_trade, kol_wallet_address, strategy_name)
        self.assertTrue(passed)
        self.assertEqual(message, "Validation successful")

    def test_validate_trade_pair_wrong_actions(self):
        """测试验证错误的交易动作 (例如，两个买入)"""
        another_buy_trade = Mock(spec=TradeRecord)
        another_buy_trade.trade_type = TradeType.BUY
        another_buy_trade.wallet_address = self.buy_trade.wallet_address
        another_buy_trade.token_out_address = self.buy_trade.token_out_address
        another_buy_trade.token_in_address = self.buy_trade.token_in_address
        another_buy_trade.strategy_name = self.buy_trade.strategy_name
        another_buy_trade.created_at = self.buy_trade.created_at

        kol_wallet_address = self.buy_trade.wallet_address
        strategy_name = self.buy_trade.strategy_name
        passed, message = self.service._validate_trade_pair(self.buy_trade, another_buy_trade, kol_wallet_address, strategy_name)
        self.assertFalse(passed)
        self.assertIn(f"卖出交易动作不正确: sell_action={another_buy_trade.trade_type}", message)

    def test_validate_trade_pair_different_tokens(self):
        """测试验证不同代币的交易对"""
        different_token_sell = Mock(spec=TradeRecord)
        different_token_sell.id = str(PydanticObjectId())
        different_token_sell.trade_type = TradeType.SELL
        different_token_sell.status = TradeStatus.SUCCESS
        different_token_sell.token_in_address = "another_test_token_address"
        different_token_sell.token_out_address = self.buy_trade.token_in_address
        different_token_sell.wallet_address = self.buy_trade.wallet_address
        different_token_sell.strategy_name = self.buy_trade.strategy_name
        different_token_sell.created_at = datetime(2024, 1, 1, 14, 0, 0)
        
        kol_wallet_address = self.buy_trade.wallet_address
        strategy_name = self.buy_trade.strategy_name
        passed, message = self.service._validate_trade_pair(self.buy_trade, different_token_sell, kol_wallet_address, strategy_name)
        self.assertFalse(passed)
        expected_msg = f"交易的标的代币地址不匹配: buy_trade.token_out_address={self.buy_trade.token_out_address}, sell_trade.token_in_address={different_token_sell.token_in_address}"
        self.assertEqual(message, expected_msg)

    def test_validate_trade_pair_different_strategies(self):
        """测试验证不同策略的交易对"""
        different_strategy_sell = Mock(spec=TradeRecord)
        different_strategy_sell.id = str(PydanticObjectId())
        different_strategy_sell.trade_type = TradeType.SELL
        different_strategy_sell.status = TradeStatus.SUCCESS
        different_strategy_sell.token_in_address = self.buy_trade.token_out_address
        different_strategy_sell.token_out_address = self.buy_trade.token_in_address
        different_strategy_sell.wallet_address = self.buy_trade.wallet_address
        different_strategy_sell.strategy_name = "DIFFERENT_STRATEGY"
        different_strategy_sell.created_at = datetime(2024, 1, 1, 14, 0, 0)

        kol_wallet_address = self.buy_trade.wallet_address
        reference_strategy_name = self.buy_trade.strategy_name
        passed, message = self.service._validate_trade_pair(self.buy_trade, different_strategy_sell, kol_wallet_address, reference_strategy_name)
        self.assertFalse(passed)
        expected_msg = f"卖出交易策略 '{different_strategy_sell.strategy_name}' 与预期策略 '{reference_strategy_name}' 不匹配。"
        self.assertEqual(message, expected_msg)

    def test_validate_trade_pair_buy_strategy_mismatch(self):
        """测试买入交易策略与参考策略不符的情况"""
        # Create a new Mock with the same spec, then copy relevant attributes
        buy_trade_wrong_strategy = Mock(spec=TradeRecord) 
        buy_trade_wrong_strategy.id = self.buy_trade.id
        buy_trade_wrong_strategy.trade_type = self.buy_trade.trade_type
        buy_trade_wrong_strategy.wallet_address = self.buy_trade.wallet_address
        buy_trade_wrong_strategy.token_out_address = self.buy_trade.token_out_address
        buy_trade_wrong_strategy.token_in_address = self.buy_trade.token_in_address
        buy_trade_wrong_strategy.created_at = self.buy_trade.created_at
        # It might be good to also copy other fields _validate_trade_pair might touch, like status
        buy_trade_wrong_strategy.status = self.buy_trade.status 
        buy_trade_wrong_strategy.strategy_name = "WRONG_BUY_STRATEGY" # Different strategy
        
        kol_wallet_address = self.buy_trade.wallet_address # or buy_trade_wrong_strategy.wallet_address
        # Use sell_trade's strategy as the reference context strategy for this test
        # Ensure self.sell_trade also has a strategy_name consistent with what _validate_trade_pair expects from it
        reference_strategy_name = self.sell_trade.strategy_name 

        passed, message = self.service._validate_trade_pair(
            buy_trade_wrong_strategy, 
            self.sell_trade, 
            kol_wallet_address, 
            reference_strategy_name 
        )
        self.assertFalse(passed)
        expected_msg = f"买入交易策略 '{buy_trade_wrong_strategy.strategy_name}' 与预期策略 '{reference_strategy_name}' 不匹配。"
        self.assertEqual(message, expected_msg)

    def test_validate_trade_pair_missing_strategy_arg(self):
        """测试当 reference strategy_name 为 None 或空时 _validate_trade_pair 的行为"""
        kol_wallet_address = self.buy_trade.wallet_address
        passed, message = self.service._validate_trade_pair(self.buy_trade, self.sell_trade, kol_wallet_address, None)
        self.assertFalse(passed)
        self.assertEqual(message, "策略名称缺失，无法进行验证。")

        passed, message = self.service._validate_trade_pair(self.buy_trade, self.sell_trade, kol_wallet_address, "")
        self.assertFalse(passed)
        self.assertEqual(message, "策略名称缺失，无法进行验证。")

    def test_validate_trade_pair_wrong_time_order(self):
        """测试验证错误的时间顺序 (卖出在买入之前)"""
        early_sell_trade = Mock(spec=TradeRecord)
        early_sell_trade.id = str(PydanticObjectId())
        early_sell_trade.trade_type = TradeType.SELL
        early_sell_trade.status = TradeStatus.SUCCESS
        early_sell_trade.token_in_address = self.buy_trade.token_out_address
        early_sell_trade.token_out_address = self.buy_trade.token_in_address
        early_sell_trade.wallet_address = self.buy_trade.wallet_address
        early_sell_trade.strategy_name = self.buy_trade.strategy_name
        early_sell_trade.created_at = datetime(2024, 1, 1, 8, 0, 0)

        kol_wallet_address = self.buy_trade.wallet_address
        strategy_name = self.buy_trade.strategy_name
        passed, message = self.service._validate_trade_pair(self.buy_trade, early_sell_trade, kol_wallet_address, strategy_name)
        self.assertFalse(passed)
        self.assertEqual(message, f"时间顺序错误: buy_time={self.buy_trade.created_at}, sell_time={early_sell_trade.created_at}")

    def test_calculate_pnl_missing_actual_amounts(self):
        """测试当实际金额缺失时PnL计算返回0.0"""
        # 场景1: 买入交易实际金额缺失
        buy_missing_actual = Mock(spec=TradeRecord)
        buy_missing_actual.id = str(PydanticObjectId())
        buy_missing_actual.trade_type = TradeType.BUY
        buy_missing_actual.token_in_actual_amount = None
        buy_missing_actual.token_out_actual_amount = 1000.0

        # 创建一个符合预期的 sell_trade mock，因为它也会被 calculate_pnl 使用
        valid_sell_for_missing_buy = Mock(spec=TradeRecord)
        valid_sell_for_missing_buy.id = str(PydanticObjectId())
        valid_sell_for_missing_buy.trade_type = TradeType.SELL
        valid_sell_for_missing_buy.token_out_actual_amount = 120.0
        valid_sell_for_missing_buy.token_in_actual_amount = 1000.0

        pnl_result_buy_missing = self.service.calculate_pnl(buy_missing_actual, valid_sell_for_missing_buy)
        self.assertEqual(pnl_result_buy_missing, 0.0)

        # 场景2: 卖出交易实际金额缺失
        sell_missing_actual = Mock(spec=TradeRecord)
        sell_missing_actual.id = str(PydanticObjectId())
        sell_missing_actual.trade_type = TradeType.SELL
        sell_missing_actual.token_in_actual_amount = 1000.0
        sell_missing_actual.token_out_actual_amount = None

        # 创建一个符合预期的 buy_trade mock
        valid_buy_for_missing_sell = Mock(spec=TradeRecord)
        valid_buy_for_missing_sell.id = str(PydanticObjectId())
        valid_buy_for_missing_sell.trade_type = TradeType.BUY
        valid_buy_for_missing_sell.token_in_actual_amount = 100.0
        valid_buy_for_missing_sell.token_out_actual_amount = 1000.0

        pnl_result_sell_missing = self.service.calculate_pnl(valid_buy_for_missing_sell, sell_missing_actual)
        self.assertEqual(pnl_result_sell_missing, 0.0)

        # 场景3: 两者都缺失
        pnl_result_both_missing = self.service.calculate_pnl(buy_missing_actual, sell_missing_actual)
        self.assertEqual(pnl_result_both_missing, 0.0)

    def test_high_precision_pnl_calculation(self):
        """测试高精度PnL计算"""
        # 创建高精度Mock交易记录
        high_precision_buy = Mock()
        high_precision_buy.id = str(PydanticObjectId())
        high_precision_buy.signal_id = str(PydanticObjectId())
        high_precision_buy.strategy_name = "test_strategy"
        high_precision_buy.trade_provider = "gmgn"
        high_precision_buy.trade_type = TradeType.BUY
        high_precision_buy.status = TradeStatus.SUCCESS
        high_precision_buy.token_in_address = "So11111111111111111111111111111111111111112"
        high_precision_buy.token_out_address = "test_token_address"
        high_precision_buy.wallet_address = "test_wallet_address"
        high_precision_buy.token_in_actual_amount = 123.456789
        high_precision_buy.cost = 123.456789
        high_precision_buy.created_at = datetime(2024, 1, 1, 10, 0, 0)
        
        high_precision_sell = Mock()
        high_precision_sell.id = str(PydanticObjectId())
        high_precision_sell.signal_id = str(PydanticObjectId())
        high_precision_sell.strategy_name = "test_strategy"
        high_precision_sell.trade_provider = "gmgn"
        high_precision_sell.trade_type = TradeType.SELL
        high_precision_sell.status = TradeStatus.SUCCESS
        high_precision_sell.token_in_address = "test_token_address"
        high_precision_sell.token_out_address = "So11111111111111111111111111111111111111112"
        high_precision_sell.wallet_address = "test_wallet_address"
        high_precision_sell.token_out_actual_amount = 135.987654  # 高精度收入
        high_precision_sell.cost = 135.987654  # 高精度收入
        high_precision_sell.created_at = datetime(2024, 1, 1, 12, 0, 0)
        
        pnl_result = self.service.calculate_pnl(high_precision_buy, high_precision_sell)
        
        # 验证高精度计算
        expected_pnl = 135.987654 - 123.456789
        
        self.assertAlmostEqual(pnl_result, expected_pnl, places=6)

    async def test_score_individual_kol_combination_success_first_time(self):
        """测试 score_individual_kol_combination 首次成功打分"""
        kol_wallet_address = "test_kol_address_123"
        strategy_name = self.buy_trade.strategy_name

        # 模拟 PnL 计算结果 (确保大于阈值)
        pnl_value = Decimal("20.0")
        self.service.calculate_pnl = Mock(return_value=pnl_value) # Patching instance method

        # 模拟 score_changes 计算结果
        score_positive_change = Decimal("10.0")
        score_negative_change = Decimal("0.0")
        self.service.calculate_score_changes = Mock(return_value=(score_positive_change, score_negative_change))

        # 执行打分
        result = await self.service.score_individual_kol_combination(
            buy_trade=self.buy_trade,
            sell_trade=self.sell_trade,
            kol_wallet_address=kol_wallet_address,
            strategy_name=strategy_name,
            pnl=pnl_value,
            config=self.scoring_config
        )

        # 断言结果
        self.assertTrue(result)

        # 断言依赖调用
        # self.mock_config_dao_instance.get_kol_scoring_config.assert_called_once_with(strategy_name) # 移除此断言，因为config是直接传入的
        # self.service.calculate_pnl.assert_called_once_with(self.buy_trade, self.sell_trade) # 移除此断言，因为pnl是直接传入的
        # self.mock_log_dao_instance.has_log_entry_existed.assert_called_once_with( # 移除此断言，因为 force_rescore=False 时不调用
        #     buy_trade_id=self.buy_trade.id,
        #     sell_trade_id=self.sell_trade.id,
        #     kol_wallet_address=kol_wallet_address,
        #     strategy_name=strategy_name
        # )
        self.service.calculate_score_changes.assert_called_once_with(
            pnl_value, # 使用位置参数
            strategy_name, # 使用位置参数
            self.scoring_config # 使用位置参数
        )
        self.mock_score_dao_instance.update_score.assert_called_once_with(
            kol_wallet_address,
            strategy_name,
            score_positive_change,
            score_negative_change
        )
        score_negative_change = Decimal("0.0")

        # 准备期望的 scoring_params_snapshot 内容，与服务层一致
        expected_scoring_params_snapshot = {
            "strategy_config": {},
            "default_positive_score": self.scoring_config.default_positive_score,
            "default_negative_score_multiplier": self.scoring_config.default_negative_score_multiplier,
            "pnl_used_for_scoring": pnl_value
        }

        self.mock_log_dao_instance.create_scoring_log.assert_called_once_with(
            buy_trade_record_id=self.buy_trade.id,
            sell_trade_record_id=self.sell_trade.id,
            kol_wallet_address=kol_wallet_address,
            strategy_name=strategy_name,
            pnl_at_scoring=pnl_value, 
            positive_score_applied=score_positive_change,
            negative_score_applied=score_negative_change,
            scoring_params_snapshot=expected_scoring_params_snapshot # 使用上面定义的期望快照
        )

    # --- 新增辅助测试方法 ---
    def _prepare_common_test_data(self):
        kol_wallet_address = "test_kol_address_123"
        strategy_name = "test_strategy"
        pnl_value = 20.0 # Using float directly as tests use it
        # These are more like expected changes for a default scenario, might be overridden in tests
        score_positive_change = 10.0 
        score_negative_change = 0.0
        return kol_wallet_address, strategy_name, pnl_value, score_positive_change, score_negative_change

    async def test_score_individual_kol_combination_force_rescore_log_exists(self):
        """测试 score_individual_kol_combination，当强制重新打分且日志已存在时"""
        kol_wallet_address, strategy_name, pnl_value, _, _ = self._prepare_common_test_data()

        # 模拟依赖方法的返回值
        self.mock_log_dao_instance.has_log_entry_existed = AsyncMock(return_value=True) # 日志已存在
        # 其他mock对象的方法不应该被调用
        self.mock_score_dao_instance.update_score = AsyncMock()
        self.mock_log_dao_instance.create_scoring_log = AsyncMock()
        # self.service.calculate_score_changes 仍然是 MagicMock, 如果被调用会记录

        # 执行
        result = await self.service.score_individual_kol_combination(
            buy_trade=self.buy_trade,
            sell_trade=self.sell_trade,
            kol_wallet_address=kol_wallet_address,
            strategy_name=strategy_name,
            pnl=pnl_value,
            config=self.scoring_config,
            force_rescore=True # 强制重新打分
        )

        # 断言结果
        self.assertFalse(result["success"])
        self.assertIn("Combination already scored (force_rescore)", result["error"])
        self.assertEqual(result["kol_wallet_address"], kol_wallet_address)
        self.assertEqual(result["strategy_name"], strategy_name)

        # 断言依赖调用
        self.mock_log_dao_instance.has_log_entry_existed.assert_called_once_with(
            str(self.buy_trade.id), 
            str(self.sell_trade.id),
            kol_wallet_address,    
            strategy_name          
        )
        self.mock_score_dao_instance.update_score.assert_not_called()
        self.mock_log_dao_instance.create_scoring_log.assert_not_called()

    async def test_score_individual_kol_combination_force_rescore_log_not_exists(self):
        """测试 score_individual_kol_combination，当强制重新打分且日志不存在时"""
        kol_wallet_address, strategy_name, pnl_value, score_positive_change, score_negative_change = self._prepare_common_test_data()
        
        # Using self.buy_trade, self.sell_trade, self.scoring_config from setUp
        buy_trade = self.buy_trade
        sell_trade = self.sell_trade
        config_to_use = self.scoring_config # Use the one from setUp

        expected_scoring_params_snapshot = {
            "strategy_config": {},
            "default_positive_score": self.scoring_config.default_positive_score,
            "default_negative_score_multiplier": self.scoring_config.default_negative_score_multiplier,
            "pnl_used_for_scoring": pnl_value
        }

        # 模拟依赖方法的返回值
        self.mock_log_dao_instance.has_log_entry_existed = AsyncMock(return_value=False) # 日志不存在
        
        # calculate_score_changes is synchronous. If we need to control its output for this specific test:
        # We can patch it directly or ensure the config leads to the desired score_positive_change, score_negative_change
        # Forcing specific return values for calculate_score_changes for this test:
        with patch.object(self.service, 'calculate_score_changes', return_value=(score_positive_change, score_negative_change)) as mock_calc_score_changes:
            self.mock_score_dao_instance.update_score = AsyncMock(return_value=True) 
            self.mock_log_dao_instance.create_scoring_log = AsyncMock(return_value=Mock(spec=TradeScoreLog))

            # 确保验证通过
            with patch.object(self.service, '_validate_trade_pair', return_value=(True, "")) as mock_validate:
                result = await self.service.score_individual_kol_combination(
                    buy_trade=buy_trade,
                    sell_trade=sell_trade,
                    kol_wallet_address=kol_wallet_address,
                    strategy_name=strategy_name,
                    pnl=pnl_value, # pnl_value from _prepare_common_test_data
                    config=config_to_use,
                    force_rescore=True 
                )

        # 断言结果 (应该类似首次打分成功)
        self.assertTrue(result["success"])
        # self.assertIn("scored successfully", result["message"]) # Message field might not exist in actual dict
        self.assertEqual(result["kol_wallet_address"], kol_wallet_address)
        self.assertEqual(result["strategy_name"], strategy_name)
        self.assertEqual(result["pnl_at_scoring"], pnl_value)
        self.assertEqual(result["positive_score_applied"], score_positive_change)
        self.assertEqual(result["negative_score_applied"], score_negative_change)

        # 断言依赖调用
        self.mock_log_dao_instance.has_log_entry_existed.assert_called_once_with(
            str(buy_trade.id), 
            str(sell_trade.id),
            kol_wallet_address,    
            strategy_name          
        )
        mock_calc_score_changes.assert_called_once_with( # Assert the patched method
            pnl_value,
            strategy_name,
            config_to_use
        )
        self.mock_score_dao_instance.update_score.assert_called_once_with(
            kol_wallet_address,
            strategy_name,
            score_positive_change,
            score_negative_change
        )
        self.mock_log_dao_instance.create_scoring_log.assert_called_once_with(
            buy_trade_record_id=buy_trade.id,
            sell_trade_record_id=sell_trade.id,
            kol_wallet_address=kol_wallet_address,
            strategy_name=strategy_name,
            pnl_at_scoring=pnl_value,
            positive_score_applied=score_positive_change,
            negative_score_applied=score_negative_change,
            scoring_params_snapshot=expected_scoring_params_snapshot
        )

    async def test_score_individual_kol_combination_validation_fails(self):
        """测试 score_individual_kol_combination，当 _validate_trade_pair 返回 False 时"""
        kol_wallet_address, strategy_name, pnl_value, _, _ = self._prepare_common_test_data()
        buy_trade = self.buy_trade
        sell_trade = self.sell_trade
        config_to_use = self.scoring_config

        # 模拟 _validate_trade_pair 返回 False 和错误消息
        validation_error_message = "Trade pair validation failed for test"
        with patch.object(self.service, '_validate_trade_pair', return_value=(False, validation_error_message)) as mock_validate:
            result = await self.service.score_individual_kol_combination(
                buy_trade=buy_trade,
                sell_trade=sell_trade,
                kol_wallet_address=kol_wallet_address,
                strategy_name=strategy_name,
                pnl=pnl_value,
                config=config_to_use,
                force_rescore=False 
            )

            self.assertFalse(result["success"])
            self.assertEqual(result["error"], validation_error_message) # Check for the exact message

            mock_validate.assert_called_once_with(buy_trade, sell_trade, kol_wallet_address, strategy_name)
            # If calculate_score_changes is a regular method not specifically mocked here,
            # it shouldn't be called due to early exit.
            # If it was a MagicMock from setup, then assert_not_called() is fine.
            if hasattr(self.service.calculate_score_changes, 'assert_not_called'):
                 self.service.calculate_score_changes.assert_not_called()

            self.mock_score_dao_instance.update_score.assert_not_called()
            self.mock_log_dao_instance.create_scoring_log.assert_not_called()

    async def test_score_individual_kol_combination_negative_pnl(self):
        """测试score_individual_kol_combination在PnL为负数时的行为"""
        kol_addr, strategy, _, _, _ = self._prepare_common_test_data()
        buy_trade = self.buy_trade
        sell_trade = self.sell_trade # sell_trade from setUp is profitable by default
        config_to_use = self.scoring_config

        # Adjust sell_trade for loss, or use a dedicated one if available
        # For this test, we directly pass a negative PnL value.
        pnl = -20.0  # 亏损
        
        # 预期分数变化: 使用固定分数 default_negative_score_multiplier * -1 = 0.1 * -1 = -0.1
        expected_negative_change = -0.1

        # 确保验证通过
        with patch.object(self.service, '_validate_trade_pair', return_value=(True, "")) as mock_validate:
            result = await self.service.score_individual_kol_combination(
                buy_trade, sell_trade, kol_addr, strategy, pnl, config_to_use, force_rescore=False
            )

        mock_validate.assert_called_once_with(buy_trade, sell_trade, kol_addr, strategy)
        self.assertTrue(result["success"])
        self.assertEqual(result["kol_wallet_address"], kol_addr)
        self.assertEqual(result["strategy_name"], strategy)
        self.assertEqual(result["pnl_at_scoring"], pnl)
        self.assertEqual(result["positive_score_applied"], 0.0)
        self.assertEqual(result["negative_score_applied"], expected_negative_change)
        self.assertTrue(result["log_created"])
        self.assertTrue(result["score_updated"])
        self.assertIsNone(result["error"])

        self.mock_score_dao_instance.update_score.assert_called_once_with(
            kol_addr, strategy, 0.0, expected_negative_change
        )
        self.mock_log_dao_instance.create_scoring_log.assert_called_once()
        call_kwargs = self.mock_log_dao_instance.create_scoring_log.call_args.kwargs
        self.assertEqual(call_kwargs['buy_trade_record_id'], str(buy_trade.id))
        self.assertEqual(call_kwargs['sell_trade_record_id'], str(sell_trade.id))
        self.assertEqual(call_kwargs['kol_wallet_address'], kol_addr)
        self.assertEqual(call_kwargs['strategy_name'], strategy)
        self.assertEqual(call_kwargs['pnl_at_scoring'], pnl)
        self.assertEqual(call_kwargs['positive_score_applied'], 0.0)
        self.assertEqual(call_kwargs['negative_score_applied'], expected_negative_change)
        self.assertIn('scoring_params_snapshot', call_kwargs)
        self.assertEqual(call_kwargs['scoring_params_snapshot']['pnl_used_for_scoring'], pnl)

    async def test_score_individual_kol_combination_zero_pnl(self):
        """测试score_individual_kol_combination在PnL为零时的行为"""
        kol_addr, strategy, _, _, _ = self._prepare_common_test_data()
        buy_trade = self.buy_trade
        sell_trade = self.sell_trade
        config_to_use = self.scoring_config
        pnl = 0.0  # 盈亏平衡
        
        expected_positive_change = 0.0
        expected_negative_change = 0.0

        with patch.object(self.service, '_validate_trade_pair', return_value=(True, "")) as mock_validate:
            result = await self.service.score_individual_kol_combination(
                buy_trade, sell_trade, kol_addr, strategy, pnl, config_to_use, force_rescore=False
            )

        mock_validate.assert_called_once_with(buy_trade, sell_trade, kol_addr, strategy)
        self.assertTrue(result["success"])
        self.assertEqual(result["pnl_at_scoring"], pnl)
        self.assertEqual(result["positive_score_applied"], expected_positive_change)
        self.assertEqual(result["negative_score_applied"], expected_negative_change)
        self.assertTrue(result["log_created"])
        self.assertTrue(result["score_updated"])

        self.mock_score_dao_instance.update_score.assert_called_once_with(
            kol_addr, strategy, expected_positive_change, expected_negative_change
        )
        self.mock_log_dao_instance.create_scoring_log.assert_called_once()
        call_kwargs = self.mock_log_dao_instance.create_scoring_log.call_args.kwargs
        self.assertEqual(call_kwargs['buy_trade_record_id'], str(buy_trade.id))
        self.assertEqual(call_kwargs['sell_trade_record_id'], str(sell_trade.id))
        self.assertEqual(call_kwargs['kol_wallet_address'], kol_addr)
        self.assertEqual(call_kwargs['strategy_name'], strategy)
        self.assertEqual(call_kwargs['pnl_at_scoring'], pnl)
        self.assertEqual(call_kwargs['positive_score_applied'], expected_positive_change)
        self.assertEqual(call_kwargs['negative_score_applied'], expected_negative_change)
        self.assertIn('scoring_params_snapshot', call_kwargs)

    async def test_score_individual_kol_combination_update_score_fails(self):
        """测试当KOLStrategyScoreDAO.update_score失败时的行为"""
        kol_addr, strategy, _, _, _ = self._prepare_common_test_data()
        buy_trade = self.buy_trade
        sell_trade = self.sell_trade
        config_to_use = self.scoring_config
        pnl = 10.0 # 确保会尝试更新分数

        self.mock_score_dao_instance.update_score.return_value = False # 模拟更新失败

        with patch.object(self.service, '_validate_trade_pair', return_value=(True, "")) as mock_validate:
            result = await self.service.score_individual_kol_combination(
                buy_trade, sell_trade, kol_addr, strategy, pnl, config_to_use, force_rescore=False
            )
        
        mock_validate.assert_called_once()
        self.assertFalse(result["success"])
        self.assertTrue(self.mock_score_dao_instance.update_score.called)
        self.assertFalse(result["score_updated"])
        self.assertFalse(result["log_created"]) # 不应创建日志
        self.mock_log_dao_instance.create_scoring_log.assert_not_called()
        self.assertIsNotNone(result["error"])
        self.assertIn("Failed to update KOL strategy score", result["error"])

    async def test_score_individual_kol_combination_create_log_fails(self):
        """测试当TradeScoreLogDAO.create_scoring_log失败时的行为"""
        kol_addr, strategy, _, _, _ = self._prepare_common_test_data()
        buy_trade = self.buy_trade
        sell_trade = self.sell_trade
        config_to_use = self.scoring_config
        pnl = 10.0

        self.mock_score_dao_instance.update_score.return_value = True # 确保分数更新成功
        self.mock_log_dao_instance.create_scoring_log.return_value = None # 模拟日志创建失败

        with patch.object(self.service, '_validate_trade_pair', return_value=(True, "")) as mock_validate:
            result = await self.service.score_individual_kol_combination(
                buy_trade, sell_trade, kol_addr, strategy, pnl, config_to_use, force_rescore=False
            )

        mock_validate.assert_called_once()
        self.assertFalse(result["success"])
        self.assertTrue(result["score_updated"]) # 分数应已更新
        self.assertTrue(self.mock_log_dao_instance.create_scoring_log.called)
        self.assertFalse(result["log_created"])
        self.assertIsNotNone(result["error"])
        self.assertIn("Failed to create trade score log", result["error"])

    async def test_score_individual_kol_combination_strategy_config_profit(self):
        """测试使用策略特定配置进行盈利打分"""
        kol_addr, strategy, _, _, _ = self._prepare_common_test_data()
        buy_trade = self.buy_trade
        sell_trade = self.sell_trade
        pnl = 30.0
        
        strategy_specific_config = KOLScoringConfig(
            default_positive_score=1.0, 
            default_negative_score_multiplier=0.05,
            strategy_specific_params={
                strategy: KOLStrategyScoringParams(
                    positive_score=15.5, 
                    negative_score_multiplier=0.25
                )
            }
        )
        expected_positive_change = 15.5

        with patch.object(self.service, '_validate_trade_pair', return_value=(True, "")) as mock_validate:
            result = await self.service.score_individual_kol_combination(
                buy_trade, sell_trade, kol_addr, strategy, pnl, strategy_specific_config, force_rescore=False
            )

        mock_validate.assert_called_once()
        self.assertTrue(result["success"])
        self.assertEqual(result["positive_score_applied"], expected_positive_change)
        self.assertEqual(result["negative_score_applied"], 0.0)
        
        self.mock_score_dao_instance.update_score.assert_called_once_with(
            kol_addr, strategy, expected_positive_change, 0.0
        )
        self.mock_log_dao_instance.create_scoring_log.assert_called_once()
        call_kwargs = self.mock_log_dao_instance.create_scoring_log.call_args.kwargs
        snapshot = call_kwargs["scoring_params_snapshot"]
        self.assertEqual(snapshot["strategy_config"]["positive_score"], 15.5)

    async def test_score_individual_kol_combination_strategy_config_loss(self):
        """测试使用策略特定配置进行亏损打分"""
        kol_addr, strategy, _, _, _ = self._prepare_common_test_data()
        buy_trade = self.buy_trade
        sell_trade = self.sell_trade
        pnl = -30.0

        strategy_specific_config = KOLScoringConfig(
            default_positive_score=1.0,
            default_negative_score_multiplier=0.05, # 全局配置，应被覆盖
            strategy_specific_params={
                strategy: KOLStrategyScoringParams(
                    positive_score=15.5,
                    negative_score_multiplier=0.25 
                )
            }
        )
        # 预期分数变化: 使用策略特定的固定负分 negative_score_multiplier * -1 = 0.25 * -1 = -0.25
        expected_negative_change = -0.25

        with patch.object(self.service, '_validate_trade_pair', return_value=(True, "")) as mock_validate:
            result = await self.service.score_individual_kol_combination(
                buy_trade, sell_trade, kol_addr, strategy, pnl, strategy_specific_config, force_rescore=False
            )
        
        mock_validate.assert_called_once()
        self.assertTrue(result["success"])
        self.assertEqual(result["positive_score_applied"], 0.0)
        self.assertEqual(result["negative_score_applied"], expected_negative_change)

        self.mock_score_dao_instance.update_score.assert_called_once_with(
            kol_addr, strategy, 0.0, expected_negative_change
        )
        self.mock_log_dao_instance.create_scoring_log.assert_called_once()
        call_kwargs = self.mock_log_dao_instance.create_scoring_log.call_args.kwargs
        snapshot = call_kwargs["scoring_params_snapshot"]
        self.assertEqual(snapshot["strategy_config"]["negative_score_multiplier"], 0.25)
        self.assertEqual(call_kwargs['negative_score_applied'], expected_negative_change) # check applied score in log too


if __name__ == '__main__':
    unittest.main() 