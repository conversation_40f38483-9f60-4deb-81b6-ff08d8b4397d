from typing import List, Dict, Any

from pymongo import UpdateOne
from dao.base_dao import BaseDAO
from models.gmgn_sniper_activity import GmgnSniperActivity

class GmgnSniperActivityDAO(BaseDAO[GmgnSniperActivity]):
    """GMGN代币狙击者活动DAO
    
    用于处理代币狙击者活动信息的数据库操作
    """
    
    def __init__(self):
        super().__init__(GmgnSniperActivity)
    
    async def insert_activity(self, activity_data: Dict[str, Any]) -> GmgnSniperActivity:
        """
        插入一条活动记录
        
        Args:
            trade_data: 活动数据字典
            
        Returns:
            GmgnKolActivity: 插入的活动记录
        """
        activity = await GmgnSniperActivity.from_api_data(data=activity_data)
        return activity
    
    async def insert_activities(self, activities_data: List[Dict[str, Any]]) -> List[GmgnSniperActivity]:
        """
        批量插入活动记录
        
        Args:
            trades_data: 活动数据字典列表
            
        Returns:
            List[GmgnKolActivity]: 插入的活动记录列表
        """
        activities = [await GmgnSniperActivity.from_api_data(data) for data in activities_data]
        await GmgnSniperActivity.insert_many(activities)
        return activities
    
    async def get_activities_by_token(self, token_address: str) -> List[GmgnSniperActivity]:
        """
        根据代币地址获取活动记录
        
        Args:
            token_address: 代币地址
            
        Returns:
            List[GmgnKolActivity]: 活动记录列表
        """
        activities = await GmgnSniperActivity.find(GmgnSniperActivity.token_address == token_address).to_list()
        return activities
    
    async def get_activity_by_tx_hash(self, tx_hash: str) -> GmgnSniperActivity:
        """
        根据交易哈希获取活动记录
        
        Args:
            tx_hash: 交易哈希
            
        Returns:
            GmgnKolActivity: 活动记录
        """
        activity = await GmgnSniperActivity.find_one(GmgnSniperActivity.tx_hash == tx_hash)
        return activity
    
    async def get_last_activity(self, token_address: str) -> GmgnSniperActivity:
        """
        获取最后一个活动记录
        
        Args:
            token_address: 代币地址
            
        Returns:
            GmgnKolActivity: 最后一个活动记录,根据timestamp排序
        """
        activity = await self.collection.find_one(
            GmgnSniperActivity.token_address == token_address,
            sort=[('timestamp', -1)]
        )
        return activity
    
    async def update_activity(self, tx_hash: str, update_data: Dict[str, Any]) -> GmgnSniperActivity:
        """
        更新活动记录
        
        Args:
            tx_hash: 交易哈希
            update_data: 更新的数据字典
            
        Returns:
            GmgnKolActivity: 更新后的活动记录
        """
        activity = await GmgnSniperActivity.find_one(GmgnSniperActivity.tx_hash == tx_hash)
        if activity:
            await activity.set(update_data)
        return activity
    
    async def upsert_activity_many(self, activities: List[Dict[str, Any]]) -> List[GmgnSniperActivity]:
        """
        批量插入或更新活动记录
        
        Args:
            activities: 活动数据字典列表
            
        Returns:
            List[GmgnKolActivity]: 插入或更新的活动记录列表
        """
        activities = [await GmgnSniperActivity.from_api_data(data) for data in activities]
        operations = [UpdateOne(
            {'id': activity.id},
            {'$set': activity.dict()},
            upsert=True
        ) for activity in activities]
        result = await self.collection.bulk_write(operations)
        return result.modified_count + result.upserted_count
    
    async def delete_trade(self, tx_hash: str) -> bool:
        """
        删除活动记录
        
        Args:
            tx_hash: 交易哈希
            
        Returns:
            bool: 是否成功删除
        """
        activity = await GmgnSniperActivity.find_one(GmgnSniperActivity.tx_hash == tx_hash)
        if activity:
            await activity.delete()
            return True
        return False 