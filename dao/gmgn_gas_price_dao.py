from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from models.gmgn_gas_price import GmgnGasPrice
from .base_dao import BaseDAO
from pymongo import UpdateOne

class GmgnGasPriceDAO(BaseDAO[GmgnGasPrice]):
    """GMGN Gas价格数据访问对象"""
    
    def __init__(self):
        super().__init__(GmgnGasPrice)
    
    async def find_by_chain(self, chain: str) -> Optional[GmgnGasPrice]:
        """获取指定链的最新gas价格信息
        
        Args:
            chain: 链名称
            
        Returns:
            Optional[GmgnGasPrice]: gas价格信息
        """
        return await self.find_one({
            "chain": chain
        }, sort=[("created_at", -1)])
    
    async def find_by_time_range(
        self,
        chain: str,
        start_time: datetime,
        end_time: datetime
    ) -> List[GmgnGasPrice]:
        """获取指定时间范围内的gas价格信息
        
        Args:
            chain: 链名称
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            List[GmgnGasPrice]: gas价格信息列表
        """
        return await self.find_many({
            "chain": chain,
            "created_at": {
                "$gte": start_time,
                "$lte": end_time
            }
        }, sort=[("created_at", 1)])
    
    async def find_by_price_range(
        self,
        chain: str,
        min_price: float,
        max_price: Optional[float] = None,
        price_type: str = "average"  # high/average/low
    ) -> List[GmgnGasPrice]:
        """获取指定价格范围内的gas价格信息
        
        Args:
            chain: 链名称
            min_price: 最小价格
            max_price: 最大价格
            price_type: 价格类型(high/average/low)
            
        Returns:
            List[GmgnGasPrice]: gas价格信息列表
        """
        query = {
            "chain": chain,
            price_type: {"$gte": str(min_price)}
        }
        if max_price is not None:
            query[price_type]["$lte"] = str(max_price)
            
        return await self.find_many(query, sort=[("created_at", -1)])
    
    async def upsert_gas_price(self, chain: str, data: Dict[str, Any]) -> bool:
        """更新或插入gas价格信息
        
        Args:
            chain: 链名称
            data: API返回的数据
            
        Returns:
            bool: 是否成功
        """
        try:
            # 创建新的gas价格实例
            gas_price = await GmgnGasPrice.from_api_data(chain, data)
            
            # 更新时间
            gas_price.updated_at = datetime.utcnow()
            
            # 使用原子更新操作
            await self.update_one(
                {
                    "chain": chain,
                    "created_at": gas_price.created_at
                },
                gas_price.dict(),
                upsert=True
            )
            return True
        except Exception as e:
            self.logger.error(f"更新gas价格数据失败: {str(e)}")
            return False
    
    async def cleanup_old_data(self, days: int = 7) -> int:
        """清理旧数据
        
        Args:
            days: 保留天数
            
        Returns:
            int: 删除的记录数
        """
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            result = await GmgnGasPrice.find({
                "created_at": {"$lt": cutoff_date}
            }).delete()
            return result  # 直接返回删除的记录数
        except Exception as e:
            self.logger.error(f"清理旧数据失败: {str(e)}")
            return 0 