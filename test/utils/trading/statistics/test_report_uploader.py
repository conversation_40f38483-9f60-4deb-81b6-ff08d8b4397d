"""报告上传器测试

测试报告上传器的各项功能，包括：
- 本地文件上传
- URL生成
- 目录创建
- 错误处理
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import patch, MagicMock
from datetime import datetime

from utils.trading.statistics.report_uploader import ReportUploader


class TestReportUploader:
    """报告上传器测试类"""
    
    @pytest.fixture
    def temp_upload_dir(self) -> Path:
        """创建临时上传目录"""
        temp_dir = Path(tempfile.mkdtemp())
        yield temp_dir
        # 清理临时目录
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def base_url(self) -> str:
        """基础URL"""
        return "https://reports.example.com"
    
    @pytest.fixture
    def uploader(self, temp_upload_dir: Path, base_url: str) -> ReportUploader:
        """创建上传器实例"""
        return ReportUploader(
            upload_path=str(temp_upload_dir),
            base_url=base_url,
            create_subdirs=True
        )
    
    @pytest.fixture
    def sample_html_content(self) -> str:
        """示例HTML内容"""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>交易统计报告</title>
        </head>
        <body>
            <h1>交易统计报告</h1>
            <p>这是一个测试报告</p>
        </body>
        </html>
        """
    
    def test_init_with_valid_params(self, temp_upload_dir: Path, base_url: str):
        """测试使用有效参数初始化"""
        uploader = ReportUploader(
            upload_path=str(temp_upload_dir),
            base_url=base_url,
            create_subdirs=False
        )
        
        assert uploader.upload_path == temp_upload_dir
        assert uploader.base_url == base_url
        assert uploader.create_subdirs is False
    
    def test_init_with_trailing_slash_in_base_url(self, temp_upload_dir: Path):
        """测试基础URL末尾有斜杠的处理"""
        base_url_with_slash = "https://reports.example.com/"
        uploader = ReportUploader(
            upload_path=str(temp_upload_dir),
            base_url=base_url_with_slash
        )
        
        # 验证末尾斜杠被移除
        assert uploader.base_url == "https://reports.example.com"
    
    def test_upload_html_report_success(
        self, 
        uploader: ReportUploader, 
        sample_html_content: str,
        temp_upload_dir: Path
    ):
        """测试成功上传HTML报告"""
        filename = "test_report.html"
        
        result = uploader.upload_html_report(sample_html_content, filename)
        
        # 验证返回结果
        assert result["success"] is True
        assert result["filename"] == filename
        assert result["file_path"] == str(temp_upload_dir / filename)
        assert result["url"] == f"https://reports.example.com/{filename}"
        
        # 验证文件确实被创建
        uploaded_file = temp_upload_dir / filename
        assert uploaded_file.exists()
        assert uploaded_file.read_text(encoding='utf-8') == sample_html_content
    
    def test_upload_html_report_with_subdirectory(
        self, 
        uploader: ReportUploader, 
        sample_html_content: str,
        temp_upload_dir: Path
    ):
        """测试上传到子目录"""
        filename = "reports/2024/01/test_report.html"
        
        result = uploader.upload_html_report(sample_html_content, filename)
        
        # 验证返回结果
        assert result["success"] is True
        assert result["filename"] == filename
        assert result["url"] == f"https://reports.example.com/{filename}"
        
        # 验证子目录被创建
        expected_path = temp_upload_dir / "reports" / "2024" / "01" / "test_report.html"
        assert expected_path.exists()
        assert expected_path.read_text(encoding='utf-8') == sample_html_content
    
    def test_upload_html_report_subdirs_disabled(
        self, 
        temp_upload_dir: Path, 
        base_url: str,
        sample_html_content: str
    ):
        """测试禁用子目录创建时的处理"""
        uploader = ReportUploader(
            upload_path=str(temp_upload_dir),
            base_url=base_url,
            create_subdirs=False
        )
        
        filename = "subdir/test_report.html"
        
        result = uploader.upload_html_report(sample_html_content, filename)
        
        # 验证上传失败
        assert result["success"] is False
        assert "目录不存在" in result["error"]
        
        # 验证文件未被创建
        expected_path = temp_upload_dir / "subdir" / "test_report.html"
        assert not expected_path.exists()
    
    def test_upload_html_report_permission_error(
        self, 
        uploader: ReportUploader, 
        sample_html_content: str
    ):
        """测试权限错误处理"""
        filename = "test_report.html"
        
        # Mock文件写入抛出权限错误
        with patch('pathlib.Path.write_text') as mock_write:
            mock_write.side_effect = PermissionError("Permission denied")
            
            result = uploader.upload_html_report(sample_html_content, filename)
            
            # 验证错误处理
            assert result["success"] is False
            assert "Permission denied" in result["error"]
    
    def test_upload_html_report_io_error(
        self, 
        uploader: ReportUploader, 
        sample_html_content: str
    ):
        """测试IO错误处理"""
        filename = "test_report.html"
        
        # Mock文件写入抛出IO错误
        with patch('pathlib.Path.write_text') as mock_write:
            mock_write.side_effect = IOError("Disk full")
            
            result = uploader.upload_html_report(sample_html_content, filename)
            
            # 验证错误处理
            assert result["success"] is False
            assert "Disk full" in result["error"]
    
    def test_generate_filename_with_timestamp(self, uploader: ReportUploader):
        """测试生成带时间戳的文件名"""
        prefix = "trading_report"
        
        with patch('utils.trading.statistics.report_uploader.datetime') as mock_datetime:
            mock_now = datetime(2024, 1, 15, 14, 30, 45)
            mock_datetime.now.return_value = mock_now
            
            filename = uploader.generate_filename(prefix)
            
            expected = "trading_report_20240115_143045.html"
            assert filename == expected
    
    def test_generate_filename_with_custom_extension(self, uploader: ReportUploader):
        """测试生成自定义扩展名的文件名"""
        prefix = "data_export"
        extension = "json"
        
        with patch('utils.trading.statistics.report_uploader.datetime') as mock_datetime:
            mock_now = datetime(2024, 1, 15, 14, 30, 45)
            mock_datetime.now.return_value = mock_now
            
            filename = uploader.generate_filename(prefix, extension)
            
            expected = "data_export_20240115_143045.json"
            assert filename == expected
    
    def test_generate_filename_with_subdirectory(self, uploader: ReportUploader):
        """测试生成带子目录的文件名"""
        prefix = "reports/daily/trading_summary"
        
        with patch('utils.trading.statistics.report_uploader.datetime') as mock_datetime:
            mock_now = datetime(2024, 1, 15, 14, 30, 45)
            mock_datetime.now.return_value = mock_now
            
            filename = uploader.generate_filename(prefix)
            
            expected = "reports/daily/trading_summary_20240115_143045.html"
            assert filename == expected
    
    def test_generate_url_simple_filename(self, uploader: ReportUploader):
        """测试生成简单文件名的URL"""
        filename = "report.html"
        url = uploader.generate_url(filename)
        
        assert url == "https://reports.example.com/report.html"
    
    def test_generate_url_with_subdirectory(self, uploader: ReportUploader):
        """测试生成带子目录的URL"""
        filename = "reports/2024/01/daily_report.html"
        url = uploader.generate_url(filename)
        
        assert url == "https://reports.example.com/reports/2024/01/daily_report.html"
    
    def test_generate_url_with_leading_slash(self, uploader: ReportUploader):
        """测试处理文件名开头的斜杠"""
        filename = "/reports/test.html"
        url = uploader.generate_url(filename)
        
        # 验证开头的斜杠被正确处理
        assert url == "https://reports.example.com/reports/test.html"
    
    def test_ensure_directory_exists_create_success(
        self, 
        uploader: ReportUploader,
        temp_upload_dir: Path
    ):
        """测试成功创建目录"""
        test_dir = temp_upload_dir / "new_directory" / "subdirectory"
        
        # 确保目录不存在
        assert not test_dir.exists()
        
        # 调用方法创建目录
        uploader._ensure_directory_exists(test_dir)
        
        # 验证目录被创建
        assert test_dir.exists()
        assert test_dir.is_dir()
    
    def test_ensure_directory_exists_already_exists(
        self, 
        uploader: ReportUploader,
        temp_upload_dir: Path
    ):
        """测试目录已存在的情况"""
        # temp_upload_dir已经存在
        uploader._ensure_directory_exists(temp_upload_dir)
        
        # 验证目录仍然存在且没有错误
        assert temp_upload_dir.exists()
        assert temp_upload_dir.is_dir()
    
    def test_ensure_directory_exists_permission_error(
        self, 
        uploader: ReportUploader,
        temp_upload_dir: Path
    ):
        """测试创建目录时的权限错误"""
        test_dir = temp_upload_dir / "restricted_directory"
        
        # Mock mkdir抛出权限错误
        with patch('pathlib.Path.mkdir') as mock_mkdir:
            mock_mkdir.side_effect = PermissionError("Permission denied")
            
            with pytest.raises(PermissionError):
                uploader._ensure_directory_exists(test_dir)
    
    def test_upload_with_auto_generated_filename(
        self, 
        uploader: ReportUploader, 
        sample_html_content: str,
        temp_upload_dir: Path
    ):
        """测试使用自动生成文件名上传"""
        with patch('utils.trading.statistics.report_uploader.datetime') as mock_datetime:
            mock_now = datetime(2024, 1, 15, 14, 30, 45)
            mock_datetime.now.return_value = mock_now
            
            # 不指定文件名，让系统自动生成
            result = uploader.upload_html_report(sample_html_content)
            
            # 验证使用了自动生成的文件名
            expected_filename = "report_20240115_143045.html"
            assert result["filename"] == expected_filename
            assert result["success"] is True
            
            # 验证文件被创建
            uploaded_file = temp_upload_dir / expected_filename
            assert uploaded_file.exists()
    
    def test_upload_empty_content(self, uploader: ReportUploader):
        """测试上传空内容"""
        result = uploader.upload_html_report("", "empty_report.html")
        
        # 验证可以上传空内容
        assert result["success"] is True
        assert result["filename"] == "empty_report.html"
    
    def test_upload_large_content(
        self, 
        uploader: ReportUploader,
        temp_upload_dir: Path
    ):
        """测试上传大文件内容"""
        # 创建较大的HTML内容（约1MB）
        large_content = "<html><body>" + "x" * (1024 * 1024) + "</body></html>"
        
        result = uploader.upload_html_report(large_content, "large_report.html")
        
        # 验证大文件上传成功
        assert result["success"] is True
        
        # 验证文件内容正确
        uploaded_file = temp_upload_dir / "large_report.html"
        assert uploaded_file.exists()
        assert len(uploaded_file.read_text(encoding='utf-8')) == len(large_content)
    
    def test_upload_with_special_characters_in_filename(
        self, 
        uploader: ReportUploader, 
        sample_html_content: str,
        temp_upload_dir: Path
    ):
        """测试文件名包含特殊字符的处理"""
        # 注意：这个测试假设文件系统支持这些字符
        filename = "报告_2024年1月15日.html"
        
        result = uploader.upload_html_report(sample_html_content, filename)
        
        # 验证上传成功
        assert result["success"] is True
        assert result["filename"] == filename
        
        # 验证文件被创建
        uploaded_file = temp_upload_dir / filename
        assert uploaded_file.exists()
    
    def test_multiple_uploads_same_filename(
        self, 
        uploader: ReportUploader, 
        sample_html_content: str,
        temp_upload_dir: Path
    ):
        """测试多次上传相同文件名的处理"""
        filename = "duplicate_report.html"
        content1 = sample_html_content
        content2 = sample_html_content.replace("测试报告", "更新的报告")
        
        # 第一次上传
        result1 = uploader.upload_html_report(content1, filename)
        assert result1["success"] is True
        
        # 第二次上传（覆盖）
        result2 = uploader.upload_html_report(content2, filename)
        assert result2["success"] is True
        
        # 验证文件被覆盖
        uploaded_file = temp_upload_dir / filename
        final_content = uploaded_file.read_text(encoding='utf-8')
        assert "更新的报告" in final_content
        assert final_content == content2 