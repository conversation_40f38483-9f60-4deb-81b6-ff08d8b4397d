from typing import Optional
from models.token_trade_fetch_status import TokenTradeFetchStatus
from beanie.odm.operators.update.general import Set
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class TokenTradeFetchStatusDAO:
    """
    用于 TokenTradeFetchStatus 模型的数据库访问对象
    """

    @staticmethod
    async def get_status(token_address: str, chain: str) -> Optional[TokenTradeFetchStatus]:
        """获取指定代币的抓取状态记录"""
        return await TokenTradeFetchStatus.find_one(
            TokenTradeFetchStatus.token_address == token_address,
            TokenTradeFetchStatus.chain == chain
        )

    @staticmethod
    async def get_or_create_status(token_address: str, chain: str) -> TokenTradeFetchStatus:
        """获取或创建指定代币的抓取状态记录 (委托给模型方法)"""
        return await TokenTradeFetchStatus.get_or_create(token_address, chain)

    @staticmethod
    async def update_status(
        token_address: str, 
        chain: str, 
        status: Optional[str] = None, 
        next_cursor: Optional[str] = None,
        error_message: Optional[str] = None,
        set_next_cursor_none: bool = False # 特殊标志，用于显式将 next_cursor 设为 None
    ):
        """
        更新指定代币的抓取状态、下一个游标或错误信息。

        Args:
            token_address: 代币地址。
            chain: 链名称。
            status: 新的状态 (e.g., 'in_progress', 'completed', 'failed')。
            next_cursor: 新的下一个游标。
            error_message: 错误信息。
            set_next_cursor_none: 如果为 True，则将 next_cursor 字段强制更新为 None。
        """
        update_fields = {"updated_at": datetime.utcnow()}
        if status:
            update_fields["initial_fetch_status"] = status
        if error_message:
            update_fields["last_error_message"] = error_message
            
        # 处理 next_cursor 的更新逻辑
        if set_next_cursor_none:
             update_fields["next_cursor"] = None
        elif next_cursor is not None: # 只有在提供了非 None 值时才更新
            update_fields["next_cursor"] = next_cursor
        # 如果 next_cursor 参数是 None 且 set_next_cursor_none 是 False，则不更新 next_cursor 字段

        if not update_fields and not set_next_cursor_none:
            logger.warning("调用 update_status 但没有提供任何要更新的字段。")
            return

        result = await TokenTradeFetchStatus.find_one(
            TokenTradeFetchStatus.token_address == token_address,
            TokenTradeFetchStatus.chain == chain
        ).update(
            Set(update_fields)
        )
        
        if result.modified_count == 0:
             logger.warning(f"尝试更新状态 for {chain}:{token_address} 但没有文档被修改 (可能文档不存在?)")
        else:
             logger.info(f"成功更新状态 for {chain}:{token_address} - 更新内容: {update_fields}") 