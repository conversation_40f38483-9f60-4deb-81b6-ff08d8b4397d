# 交易统计CLI日期参数增强功能需求规格

## 1. 功能概述

### 1.1 功能描述
为交易统计分析CLI工具添加便捷的时间参数，包括：
- `--date` 参数：指定单一日期进行一整天的数据统计分析
- `--yesterday` 参数：自动获取昨天的完整数据
- `--last-week` 参数：自动获取上周（周一到周日）的完整数据

解决当前使用相同的开始结束日期无法获取数据的问题，同时提供更便捷的日常使用方式。

### 1.2 业务背景
- 当前使用 `--start-date 2025-05-29 --end-date 2025-05-29` 无法获取到任何交易数据
- 用户经常需要查看昨天的交易表现，每天手动输入日期很繁琐
- 用户需要定期查看上周的交易统计，计算上周日期范围不便
- 需要简化日常数据查询的操作方式
- 确保时间查询覆盖完整的时间段

### 1.3 目标用户
- 需要查看昨天交易表现的日常用户
- 需要定期查看上周统计的周报用户
- 需要查看特定日期交易表现的用户
- 进行日常交易回顾的交易员
- 需要简化操作的系统管理员

## 2. 功能需求

### 2.1 核心需求

#### FR-001: 新增--date参数
- **需求**: 添加 `--date` 命令行参数，接受YYYY-MM-DD格式的日期
- **说明**: 该参数用于指定单一日期，自动扩展为该日期的完整24小时时间范围
- **示例**: `--date 2025-05-29` 等同于 `--start-date 2025-05-29 00:00:00 --end-date 2025-05-29 23:59:59`

#### FR-002: 新增--yesterday参数
- **需求**: 添加 `--yesterday` 命令行参数，自动获取昨天的完整数据
- **说明**: 该参数无需任何值，自动计算昨天的日期并扩展为完整24小时时间范围
- **示例**: `--yesterday` 等同于昨天日期的 `--date` 参数
- **计算逻辑**: `yesterday = datetime.now().date() - timedelta(days=1)`

#### FR-003: 新增--last-week参数
- **需求**: 添加 `--last-week` 命令行参数，自动获取上周的完整数据
- **说明**: 该参数无需任何值，自动计算上周的日期范围（周一到周日）
- **示例**: `--last-week` 等同于上周周一00:00:00到上周周日23:59:59
- **计算逻辑**: 
  - 找到当前周的周一
  - 上周周一 = 当前周周一 - 7天
  - 上周周日 = 上周周一 + 6天

#### FR-004: 参数互斥性
- **需求**: 所有时间参数互斥：`--date`、`--yesterday`、`--last-week`、`--start-date`/`--end-date`、`--days`
- **说明**: 不能同时使用多个时间范围参数
- **错误处理**: 同时使用时应显示清晰的错误信息

#### FR-005: 日期格式验证
- **需求**: 验证输入的日期格式为YYYY-MM-DD（仅适用于--date参数）
- **错误处理**: 无效格式时显示友好的错误信息
- **边界检查**: 验证日期的合理性

### 2.2 集成需求

#### FR-006: 飞书消息支持
- **需求**: 新参数应与飞书消息发送功能兼容
- **说明**: 
  - 使用 `--yesterday` 时，飞书消息显示"昨日报"
  - 使用 `--last-week` 时，飞书消息显示"上周报"
  - 使用 `--date` 时，飞书消息显示"1天报"

#### FR-007: 报告生成支持
- **需求**: 新参数应与HTML和JSON报告生成功能完全兼容
- **说明**: 生成的报告应正确显示对应的时间范围

#### FR-008: 统计计算支持
- **需求**: 确保自动计算的时间范围能正确查询到数据库中的交易记录
- **说明**: 解决当前相同开始结束日期查询为空的问题

## 3. 接口设计

### 3.1 命令行接口

#### 新增参数
```bash
--date DATE          指定单一日期进行统计 (YYYY-MM-DD格式)
--yesterday          获取昨天的交易统计数据
--last-week          获取上周的交易统计数据 (周一到周日)
```

#### 使用示例
```bash
# 查看昨天的交易统计
python -m utils.trading.statistics.cli --yesterday

# 查看上周的交易统计
python -m utils.trading.statistics.cli --last-week

# 查看2025年5月29日的交易统计
python -m utils.trading.statistics.cli --date 2025-05-29

# 生成昨天的HTML报告
python -m utils.trading.statistics.cli --yesterday --format html --output /www/report/

# 发送昨天的飞书日报
python -m utils.trading.statistics.cli --yesterday --send-feishu --feishu-webhook "xxx"

# 发送上周的飞书周报
python -m utils.trading.statistics.cli --last-week --send-feishu --feishu-webhook "xxx"

# 查看昨天特定策略的表现
python -m utils.trading.statistics.cli --yesterday --strategies "收益率高,胜率高"
```

#### 错误示例
```bash
# 错误：同时使用多个时间参数
python -m utils.trading.statistics.cli --yesterday --days 7
# 预期输出：错误信息"时间范围参数互斥，请只使用一个：--date、--yesterday、--last-week、--days、--start-date/--end-date"

# 错误：无效日期格式
python -m utils.trading.statistics.cli --date 2025/05/29
# 预期输出：错误信息"无效的日期格式: 2025/05/29，请使用 YYYY-MM-DD 格式"
```

### 3.2 配置模型扩展

无需修改 `StatisticsConfig` 模型，因为最终都会转换为 `start_date` 和 `end_date`。

## 4. 行为规范

### 4.1 参数优先级
1. 所有时间参数互斥，不允许同时指定多个
2. 如果同时指定了互斥参数，显示错误信息并退出

### 4.2 时间处理逻辑

#### --yesterday 逻辑
1. 获取当前日期：`today = datetime.now().date()`
2. 计算昨天：`yesterday = today - timedelta(days=1)`
3. 设置开始时间：`start_time = datetime.combine(yesterday, time(0, 0, 0))`
4. 设置结束时间：`end_time = datetime.combine(yesterday, time(23, 59, 59, 999999))`

#### --last-week 逻辑
1. 获取当前日期：`today = datetime.now().date()`
2. 找到本周周一：`current_monday = today - timedelta(days=today.weekday())`
3. 计算上周周一：`last_monday = current_monday - timedelta(days=7)`
4. 计算上周周日：`last_sunday = last_monday + timedelta(days=6)`
5. 设置开始时间：`start_time = datetime.combine(last_monday, time(0, 0, 0))`
6. 设置结束时间：`end_time = datetime.combine(last_sunday, time(23, 59, 59, 999999))`

#### --date 逻辑
1. 解析输入的日期字符串为 `datetime` 对象
2. 设置开始时间为该日期的 00:00:00.000
3. 设置结束时间为该日期的 23:59:59.999

### 4.3 错误处理
1. 参数冲突：列出冲突的参数并给出正确用法
2. 日期格式错误：显示格式要求和示例（仅--date参数）
3. 计算错误：如时区问题等，给出明确提示

## 5. 兼容性要求

### 5.1 向后兼容
- 现有的所有参数和功能保持不变
- 现有的使用方式继续有效
- 不影响现有的API和配置

### 5.2 功能兼容
- 与所有现有过滤参数兼容（`--strategies`、`--tokens`）
- 与所有输出参数兼容（`--format`、`--output`）
- 与飞书消息发送功能兼容
- 与数据验证功能兼容

## 6. 验收标准

### 6.1 基本功能
- [ ] 能够使用 `--yesterday` 参数获取昨天的数据
- [ ] 能够使用 `--last-week` 参数获取上周的数据
- [ ] 能够使用 `--date` 参数指定单一日期
- [ ] 自动时间范围计算正确
- [ ] 参数互斥性验证正确工作

### 6.2 集成功能
- [ ] 与HTML报告生成功能正常工作
- [ ] 与JSON报告生成功能正常工作
- [ ] 与飞书消息发送功能正常工作
- [ ] 与策略/Token过滤功能正常工作

### 6.3 错误处理
- [ ] 无效日期格式时显示清晰错误信息
- [ ] 参数冲突时显示清晰错误信息
- [ ] 时间计算错误时给出适当提示

### 6.4 用户体验
- [ ] 帮助信息中包含新参数说明
- [ ] 示例用法中包含新参数的使用方法
- [ ] 错误信息具有指导性，帮助用户纠正问题
- [ ] 昨天和上周的数据查询操作简单便捷 