#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
整体收益率指标功能单元测试

测试交易统计系统中新增的"整体收益率"指标功能，
验证计算逻辑、边界情况处理和数据模型正确性。
"""

import unittest
from datetime import datetime
from typing import List

from utils.trading.statistics.models import TradePair, OverallStats
from utils.trading.statistics.statistics_calculator import StatisticsCalculator


class TestOverallReturnRate(unittest.TestCase):
    """整体收益率指标测试类"""
    
    def setUp(self):
        """测试准备"""
        self.calculator = StatisticsCalculator()
    
    def _create_trade_pair(self, signal_id: str, buy_amount_sol: float, 
                          profit_amount: float, **kwargs) -> TradePair:
        """创建测试用的TradePair对象"""
        # 计算默认的卖出金额和盈利率
        sell_amount_sol = buy_amount_sol + profit_amount
        profit_rate = (profit_amount / buy_amount_sol * 100.0) if buy_amount_sol > 0 else 0.0
        is_profitable = profit_amount > 0
        
        # 合并默认值和传入的参数
        defaults = {
            'strategy_name': 'test_strategy',
            'token_address': f'token_{signal_id}',
            'buy_record_id': f'buy_{signal_id}',
            'sell_record_id': f'sell_{signal_id}',
            'sell_amount_sol': sell_amount_sol,
            'profit_rate': profit_rate,
            'is_profitable': is_profitable,
            'buy_time': datetime.now(),
            'sell_time': datetime.now(),
            'holding_duration': 1.0
        }
        defaults.update(kwargs)
        
        return TradePair(
            signal_id=signal_id,
            buy_amount_sol=buy_amount_sol,
            profit_amount=profit_amount,
            **defaults
        )
    
    def test_normal_mixed_case(self):
        """测试正常混合盈亏情况"""
        trade_pairs = [
            self._create_trade_pair("test1", buy_amount_sol=10.0, profit_amount=2.0),
            self._create_trade_pair("test2", buy_amount_sol=5.0, profit_amount=-1.0)
        ]
        
        result = self.calculator._calculate_overall_stats(trade_pairs)
        
        # 验证计算结果
        self.assertEqual(result.total_trades, 2)
        self.assertEqual(result.total_buy_amount, 15.0)
        self.assertEqual(result.total_profit_amount, 1.0)
        # 整体收益率 = (1.0 / 15.0) * 100 = 6.67%
        self.assertAlmostEqual(result.overall_return_rate, 6.67, places=2)
    
    def test_zero_investment(self):
        """测试零投入情况"""
        trade_pairs = []
        result = self.calculator._calculate_overall_stats(trade_pairs)
        
        self.assertEqual(result.total_trades, 0)
        self.assertEqual(result.overall_return_rate, 0.0)
    
    def test_all_profit_case(self):
        """测试全部盈利情况"""
        trade_pairs = [
            self._create_trade_pair("test1", buy_amount_sol=10.0, profit_amount=5.0)
        ]
        
        result = self.calculator._calculate_overall_stats(trade_pairs)
        
        self.assertEqual(result.total_buy_amount, 10.0)
        self.assertEqual(result.total_profit_amount, 5.0)
        # 整体收益率 = (5.0 / 10.0) * 100 = 50.0%
        self.assertEqual(result.overall_return_rate, 50.0)
    
    def test_all_loss_case(self):
        """测试全部亏损情况"""
        trade_pairs = [
            self._create_trade_pair("test1", buy_amount_sol=10.0, profit_amount=-2.0)
        ]
        
        result = self.calculator._calculate_overall_stats(trade_pairs)
        
        self.assertEqual(result.total_buy_amount, 10.0)
        self.assertEqual(result.total_profit_amount, -2.0)
        # 整体收益率 = (-2.0 / 10.0) * 100 = -20.0%
        self.assertEqual(result.overall_return_rate, -20.0)
    
    def test_single_trade_profit(self):
        """测试单笔盈利交易"""
        trade_pairs = [
            self._create_trade_pair("test1", buy_amount_sol=8.0, profit_amount=1.6)
        ]
        
        result = self.calculator._calculate_overall_stats(trade_pairs)
        
        self.assertEqual(result.total_buy_amount, 8.0)
        self.assertEqual(result.total_profit_amount, 1.6)
        # 整体收益率 = (1.6 / 8.0) * 100 = 20.0%
        self.assertEqual(result.overall_return_rate, 20.0)
    
    def test_single_trade_loss(self):
        """测试单笔亏损交易"""
        trade_pairs = [
            self._create_trade_pair("test1", buy_amount_sol=10.0, profit_amount=-1.0)
        ]
        
        result = self.calculator._calculate_overall_stats(trade_pairs)
        
        self.assertEqual(result.total_buy_amount, 10.0)
        self.assertEqual(result.total_profit_amount, -1.0)
        # 整体收益率 = (-1.0 / 10.0) * 100 = -10.0%
        self.assertEqual(result.overall_return_rate, -10.0)
    
    def test_precision_accuracy(self):
        """测试计算精度"""
        trade_pairs = [
            self._create_trade_pair("test1", buy_amount_sol=3.0, profit_amount=1.0)
        ]
        
        result = self.calculator._calculate_overall_stats(trade_pairs)
        
        self.assertEqual(result.total_buy_amount, 3.0)
        self.assertEqual(result.total_profit_amount, 1.0)
        # 整体收益率 = (1.0 / 3.0) * 100 = 33.33%
        self.assertAlmostEqual(result.overall_return_rate, 33.33, places=2)
    
    def test_large_numbers(self):
        """测试大数值计算"""
        trade_pairs = [
            self._create_trade_pair("test1", buy_amount_sol=1000.0, profit_amount=123.456)
        ]
        
        result = self.calculator._calculate_overall_stats(trade_pairs)
        
        self.assertEqual(result.total_buy_amount, 1000.0)
        self.assertEqual(result.total_profit_amount, 123.456)
        # 整体收益率 = (123.456 / 1000.0) * 100 = 12.3456%
        self.assertAlmostEqual(result.overall_return_rate, 12.3456, places=4)
    
    def test_small_numbers(self):
        """测试小数值计算"""
        trade_pairs = [
            self._create_trade_pair("test1", buy_amount_sol=0.001, profit_amount=0.0005)
        ]
        
        result = self.calculator._calculate_overall_stats(trade_pairs)
        
        self.assertEqual(result.total_buy_amount, 0.001)
        self.assertEqual(result.total_profit_amount, 0.0005)
        # 整体收益率 = (0.0005 / 0.001) * 100 = 50.0%
        self.assertEqual(result.overall_return_rate, 50.0)
    
    def test_zero_profit_zero_loss(self):
        """测试盈亏平衡情况"""
        trade_pairs = [
            self._create_trade_pair("test1", buy_amount_sol=10.0, profit_amount=0.0)
        ]
        
        result = self.calculator._calculate_overall_stats(trade_pairs)
        
        self.assertEqual(result.total_buy_amount, 10.0)
        self.assertEqual(result.total_profit_amount, 0.0)
        # 整体收益率 = (0.0 / 10.0) * 100 = 0.0%
        self.assertEqual(result.overall_return_rate, 0.0)
    
    def test_model_field_exists(self):
        """测试模型字段存在性"""
        # 创建OverallStats实例
        stats = OverallStats()
        
        # 验证字段存在
        self.assertTrue(hasattr(stats, 'overall_return_rate'))
        # 验证默认值
        self.assertEqual(stats.overall_return_rate, 0.0)
        # 验证字段类型
        self.assertIsInstance(stats.overall_return_rate, float)
    
    def test_model_field_validation(self):
        """测试模型字段验证"""
        # 测试设置有效的float值
        stats = OverallStats(overall_return_rate=25.5)
        self.assertEqual(stats.overall_return_rate, 25.5)
        
        # 测试设置负值
        stats = OverallStats(overall_return_rate=-15.3)
        self.assertEqual(stats.overall_return_rate, -15.3)
        
        # 测试设置零值
        stats = OverallStats(overall_return_rate=0.0)
        self.assertEqual(stats.overall_return_rate, 0.0)
    
    def test_multiple_trades_complex(self):
        """测试复杂多交易情况"""
        trade_pairs = [
            self._create_trade_pair("test1", buy_amount_sol=10.0, profit_amount=2.0),   # +20%
            self._create_trade_pair("test2", buy_amount_sol=5.0, profit_amount=-1.0),   # -20%
            self._create_trade_pair("test3", buy_amount_sol=20.0, profit_amount=3.0),   # +15%
            self._create_trade_pair("test4", buy_amount_sol=15.0, profit_amount=-0.5)   # -3.33%
        ]
        
        result = self.calculator._calculate_overall_stats(trade_pairs)
        
        # 验证基础数据
        self.assertEqual(result.total_trades, 4)
        self.assertEqual(result.total_buy_amount, 50.0)  # 10+5+20+15
        self.assertEqual(result.total_profit_amount, 3.5)  # 2-1+3-0.5
        
        # 验证整体收益率
        # 整体收益率 = (3.5 / 50.0) * 100 = 7.0%
        self.assertAlmostEqual(result.overall_return_rate, 7.0, places=10)
    
    def test_data_consistency(self):
        """测试数据一致性"""
        trade_pairs = [
            self._create_trade_pair("test1", buy_amount_sol=10.0, profit_amount=2.0),
            self._create_trade_pair("test2", buy_amount_sol=5.0, profit_amount=-1.0)
        ]
        
        result = self.calculator._calculate_overall_stats(trade_pairs)
        
        # 验证总买入金额等于各笔交易买入金额之和
        expected_buy_amount = sum(pair.buy_amount_sol for pair in trade_pairs)
        self.assertEqual(result.total_buy_amount, expected_buy_amount)
        
        # 验证总盈亏金额等于各笔交易盈亏金额之和
        expected_profit_amount = sum(pair.profit_amount for pair in trade_pairs)
        self.assertEqual(result.total_profit_amount, expected_profit_amount)
        
        # 验证整体收益率计算一致性
        expected_return_rate = (expected_profit_amount / expected_buy_amount * 100.0) if expected_buy_amount > 0 else 0.0
        self.assertAlmostEqual(result.overall_return_rate, expected_return_rate, places=10)


if __name__ == '__main__':
    unittest.main() 