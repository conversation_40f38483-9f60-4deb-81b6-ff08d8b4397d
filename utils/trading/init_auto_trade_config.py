"""
自动交易配置初始化脚本

支持：
1. 创建默认的AutoTradeManager配置
2. 从环境变量读取渠道参数
3. 配置验证功能
4. 配置更新和保存
"""

import logging
import os
from typing import Optional, Dict, Any, List
from datetime import datetime

from dao.config_dao import ConfigDAO
from models.config import (
    Config,
    WalletConfig,
    TradingParams,
    TradeChannelConfig,
    NotificationConfig,
    AutoTradeConfig,
    AutoTradeManagerConfig
)

logger = logging.getLogger(__name__)


class AutoTradeConfigInitializer:
    """自动交易配置初始化器"""
    
    def __init__(self):
        self.config_dao = ConfigDAO()
    
    async def initialize_default_config(
        self,
        force_overwrite: bool = False,
        custom_config: Optional[Dict[str, Any]] = None
    ) -> Config:
        """
        初始化默认的AutoTradeManager配置
        
        Args:
            force_overwrite: 是否强制覆盖现有配置
            custom_config: 自定义配置参数
            
        Returns:
            Config: 创建的配置对象
        """
        logger.info("开始初始化AutoTradeManager默认配置")
        
        # 检查是否已存在配置
        existing_config = await self.config_dao.get_config("auto_trade_manager")
        if existing_config and not force_overwrite:
            logger.info("AutoTradeManager配置已存在，跳过初始化")
            return existing_config
        
        # 创建默认配置
        config_data = self._create_default_config_data(custom_config)
        auto_trade_config = AutoTradeManagerConfig(**config_data)
        
        # 保存到数据库
        config = Config(
            config_type="auto_trade_manager",
            data=auto_trade_config,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        await self.config_dao.save_config(config)
        logger.info("AutoTradeManager默认配置已创建并保存")
        
        return config
    
    def _create_default_config_data(self, custom_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        创建默认配置数据
        
        Args:
            custom_config: 自定义配置参数
            
        Returns:
            Dict[str, Any]: 配置数据
        """
        custom = custom_config or {}
        
        # 从环境变量读取默认钱包配置
        wallet_config = WalletConfig(
            default_private_key_env_var=custom.get(
                "default_private_key_env_var", 
                "SOLANA_WALLET_PRIVATE_KEY"
            ),
            default_wallet_address=custom.get(
                "default_wallet_address",
                os.getenv("SOLANA_WALLET_ADDRESS", "")
            )
        )
        
        # 默认交易参数
        trading_params = TradingParams(
            default_buy_amount_sol=custom.get("default_buy_amount_sol", 0.01),
            default_buy_slippage_percentage=custom.get("default_buy_slippage_percentage", 10.0),
            default_buy_priority_fee_sol=custom.get("default_buy_priority_fee_sol", 0.0005),
            default_sell_slippage_percentage=custom.get("default_sell_slippage_percentage", 10.0),
            default_sell_priority_fee_sol=custom.get("default_sell_priority_fee_sol", 0.0005)
        )
        
        # 创建默认渠道配置
        channels = self._create_default_channels(custom.get("channels", {}))
        
        # 通知配置
        notification_config = NotificationConfig(
            include_trade_details=custom.get("include_trade_details", True),
            notify_on_success=custom.get("notify_on_success", True),
            notify_on_failure=custom.get("notify_on_failure", True)
        )
        
        # 主配置
        auto_trade_config_data = {
            "enabled": custom.get("enabled", True),
            "wallet_config": wallet_config,
            "trading_params": trading_params,
            "channels": channels,
            "notification_config": notification_config,
            "max_concurrent_trades": custom.get("max_concurrent_trades", 5),
            "trade_timeout_seconds": custom.get("trade_timeout_seconds", 120)
        }
        
        return auto_trade_config_data
    
    def _create_default_channels(self, custom_channels: Dict[str, Any]) -> List[TradeChannelConfig]:
        """
        创建默认渠道配置
        
        Args:
            custom_channels: 自定义渠道配置
            
        Returns:
            List[TradeChannelConfig]: 渠道配置列表
        """
        channels = []
        
        # GMGN渠道配置
        gmgn_config = custom_channels.get("gmgn", {})
        if gmgn_config.get("enabled", True):
            gmgn_api_host = (
                gmgn_config.get("gmgn_api_host") or 
                os.getenv("GMGN_API_HOST") or 
                "https://gmgn.ai"
            )
            
            gmgn_channel = TradeChannelConfig(
                channel_type="gmgn",
                enabled=gmgn_config.get("enabled", True),
                priority=gmgn_config.get("priority", 1),
                timeout_seconds=gmgn_config.get("timeout_seconds", 60),
                max_retries=gmgn_config.get("max_retries", 3),
                trading_params=TradingParams(
                    default_buy_amount_sol=gmgn_config.get("buy_amount_sol", 0.01),
                    default_buy_slippage_percentage=gmgn_config.get("buy_slippage_percentage", 10.0),
                    default_buy_priority_fee_sol=gmgn_config.get("buy_priority_fee_sol", 0.0005),
                    default_sell_slippage_percentage=gmgn_config.get("sell_slippage_percentage", 10.0),
                    default_sell_priority_fee_sol=gmgn_config.get("sell_priority_fee_sol", 0.0005)
                ),
                channel_params={
                    "gmgn_api_host": gmgn_api_host,
                    "min_amount": gmgn_config.get("min_amount", 0.001),
                    "max_amount": gmgn_config.get("max_amount", 10.0)
                }
            )
            channels.append(gmgn_channel)
        
        # Solana Direct渠道配置
        solana_config = custom_channels.get("jupiter", {})
        if solana_config.get("enabled", False):  # 默认不启用
            jupiter_api_host = (
                solana_config.get("jupiter_api_host") or
                os.getenv("JUPITER_API_HOST") or
                "https://quote-api.jup.ag"
            )
            
            solana_rpc_url = (
                solana_config.get("solana_rpc_url") or
                os.getenv("SOLANA_RPC_URL") or
                "https://api.mainnet-beta.solana.com"
            )
            
            solana_channel = TradeChannelConfig(
                channel_type="jupiter",
                enabled=solana_config.get("enabled", False),
                priority=solana_config.get("priority", 2),
                timeout_seconds=solana_config.get("timeout_seconds", 90),
                max_retries=solana_config.get("max_retries", 2),
                trading_params=TradingParams(
                    default_buy_amount_sol=solana_config.get("buy_amount_sol", 0.01),
                    default_buy_slippage_percentage=solana_config.get("buy_slippage_percentage", 15.0),
                    default_buy_priority_fee_sol=solana_config.get("buy_priority_fee_sol", 0.001),
                    default_sell_slippage_percentage=solana_config.get("sell_slippage_percentage", 15.0),
                    default_sell_priority_fee_sol=solana_config.get("sell_priority_fee_sol", 0.001)
                ),
                channel_params={
                    "jupiter_api_host": jupiter_api_host,
                    "solana_rpc_url": solana_rpc_url,
                    "min_amount": solana_config.get("min_amount", 0.001),
                    "max_amount": solana_config.get("max_amount", 5.0)
                }
            )
            channels.append(solana_channel)
        
        return channels
    
    async def validate_config(self, config_name: str = "auto_trade_manager") -> Dict[str, Any]:
        """
        验证配置的有效性
        
        Args:
            config_name: 配置名称
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        logger.info(f"开始验证配置: {config_name}")
        
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "config_exists": False,
            "channels_validated": []
        }
        
        try:
            # 检查配置是否存在
            config = await self.config_dao.get_config(config_name)
            if not config:
                validation_result["valid"] = False
                validation_result["errors"].append(f"配置 '{config_name}' 不存在")
                return validation_result
            
            validation_result["config_exists"] = True
            
            # 验证配置数据类型
            if not isinstance(config.data, AutoTradeManagerConfig):
                validation_result["valid"] = False
                validation_result["errors"].append("配置数据类型不正确")
                return validation_result
            
            auto_config = config.data
            
            # 验证钱包配置
            wallet_validation = self._validate_wallet_config(auto_config.wallet_config)
            if wallet_validation["errors"]:
                validation_result["errors"].extend(wallet_validation["errors"])
                validation_result["valid"] = False
            validation_result["warnings"].extend(wallet_validation["warnings"])
            
            # 验证渠道配置
            for channel in auto_config.channels:
                channel_validation = self._validate_channel_config(channel)
                validation_result["channels_validated"].append({
                    "channel_type": channel.channel_type,
                    "valid": len(channel_validation["errors"]) == 0,
                    "errors": channel_validation["errors"],
                    "warnings": channel_validation["warnings"]
                })
                
                if channel_validation["errors"]:
                    validation_result["errors"].extend(
                        [f"渠道 '{channel.channel_type}': {err}" for err in channel_validation["errors"]]
                    )
                    validation_result["valid"] = False
                
                validation_result["warnings"].extend(
                    [f"渠道 '{channel.channel_type}': {warn}" for warn in channel_validation["warnings"]]
                )
            
            # 检查是否有启用的渠道
            enabled_channels = [ch for ch in auto_config.channels if ch.enabled]
            if not enabled_channels:
                validation_result["warnings"].append("没有启用的交易渠道")
            
            logger.info(f"配置验证完成: valid={validation_result['valid']}, "
                       f"errors={len(validation_result['errors'])}, "
                       f"warnings={len(validation_result['warnings'])}")
            
        except Exception as e:
            logger.error(f"配置验证失败: {e}", exc_info=True)
            validation_result["valid"] = False
            validation_result["errors"].append(f"验证过程出错: {str(e)}")
        
        return validation_result
    
    def _validate_wallet_config(self, wallet_config: WalletConfig) -> Dict[str, List[str]]:
        """验证钱包配置"""
        errors = []
        warnings = []
        
        # 检查私钥环境变量
        if not wallet_config.default_private_key_env_var:
            errors.append("缺少默认私钥环境变量名")
        else:
            # 检查环境变量是否存在
            private_key = os.getenv(wallet_config.default_private_key_env_var)
            if not private_key:
                warnings.append(f"环境变量 '{wallet_config.default_private_key_env_var}' 未设置")
            elif len(private_key) < 32:  # 简单长度检查
                warnings.append("私钥长度可能不正确")
        
        # 检查钱包地址
        if not wallet_config.default_wallet_address:
            warnings.append("未设置默认钱包地址")
        elif len(wallet_config.default_wallet_address) < 32:  # 简单长度检查
            warnings.append("钱包地址格式可能不正确")
        
        return {"errors": errors, "warnings": warnings}
    
    def _validate_channel_config(self, channel_config: TradeChannelConfig) -> Dict[str, List[str]]:
        """验证渠道配置"""
        errors = []
        warnings = []
        
        # 基本字段检查
        if not channel_config.channel_type:
            errors.append("渠道类型不能为空")
        
        if channel_config.priority < 1:
            warnings.append("渠道优先级应该大于等于1")
        
        if channel_config.timeout_seconds < 10:
            warnings.append("超时时间可能过短")
        
        # 渠道特定验证
        if channel_config.channel_type == "gmgn":
            gmgn_api_host = channel_config.channel_params.get("gmgn_api_host")
            if not gmgn_api_host:
                errors.append("GMGN渠道缺少API主机地址")
            elif not gmgn_api_host.startswith(("http://", "https://")):
                warnings.append("GMGN API主机地址格式可能不正确")
        
        elif channel_config.channel_type == "jupiter":
            jupiter_api_host = channel_config.channel_params.get("jupiter_api_host")
            solana_rpc_url = channel_config.channel_params.get("solana_rpc_url")
            
            if not jupiter_api_host:
                errors.append("Solana Direct渠道缺少Jupiter API主机")
            if not solana_rpc_url:
                errors.append("Solana Direct渠道缺少RPC URL")
        
        # 交易参数验证
        trading_params = channel_config.trading_params
        if trading_params.default_buy_amount_sol <= 0:
            errors.append("默认买入金额必须大于0")
        
        if trading_params.default_buy_slippage_percentage < 0 or trading_params.default_buy_slippage_percentage > 100:
            warnings.append("买入滑点百分比应该在0-100之间")
        
        return {"errors": errors, "warnings": warnings}
    
    async def update_config(
        self,
        config_updates: Dict[str, Any],
        config_name: str = "auto_trade_manager"
    ) -> bool:
        """
        更新配置
        
        Args:
            config_updates: 要更新的配置项
            config_name: 配置名称
            
        Returns:
            bool: 是否成功
        """
        try:
            logger.info(f"开始更新配置: {config_name}")
            
            # 获取现有配置
            config = await self.config_dao.get_config(config_name)
            if not config:
                logger.error(f"配置 '{config_name}' 不存在")
                return False
            
            # 更新配置数据
            config_dict = config.data.model_dump()
            config_dict.update(config_updates)
            
            # 验证更新后的配置
            updated_config_data = AutoTradeManagerConfig(**config_dict)
            
            # 保存更新
            config.data = updated_config_data
            config.updated_at = datetime.now()
            
            await self.config_dao.save_config(config)
            logger.info(f"配置 '{config_name}' 更新成功")
            
            return True
            
        except Exception as e:
            logger.error(f"更新配置失败: {e}", exc_info=True)
            return False
    
    async def get_config_summary(self, config_name: str = "auto_trade_manager") -> Dict[str, Any]:
        """
        获取配置摘要信息
        
        Args:
            config_name: 配置名称
            
        Returns:
            Dict[str, Any]: 配置摘要
        """
        try:
            config = await self.config_dao.get_config(config_name)
            if not config:
                return {"exists": False, "error": "配置不存在"}
            
            auto_config = config.data
            summary = {
                "exists": True,
                "enabled": auto_config.enabled,
                "created_at": config.created_at.isoformat() if config.created_at else None,
                "updated_at": config.updated_at.isoformat() if config.updated_at else None,
                "wallet_configured": bool(auto_config.wallet_config.default_wallet_address),
                "channels": [],
                "total_channels": len(auto_config.channels),
                "enabled_channels": sum(1 for ch in auto_config.channels if ch.enabled)
            }
            
            for channel in auto_config.channels:
                summary["channels"].append({
                    "type": channel.channel_type,
                    "enabled": channel.enabled,
                    "priority": channel.priority,
                    "timeout": channel.timeout_seconds
                })
            
            return summary
            
        except Exception as e:
            logger.error(f"获取配置摘要失败: {e}")
            return {"exists": False, "error": str(e)}


# 便捷函数
async def init_default_auto_trade_config(
    force_overwrite: bool = False,
    custom_config: Optional[Dict[str, Any]] = None
) -> Config:
    """
    便捷函数：初始化默认AutoTradeManager配置
    
    Args:
        force_overwrite: 是否强制覆盖现有配置
        custom_config: 自定义配置参数
        
    Returns:
        Config: 创建的配置对象
    """
    initializer = AutoTradeConfigInitializer()
    return await initializer.initialize_default_config(force_overwrite, custom_config)


async def validate_auto_trade_config(config_name: str = "auto_trade_manager") -> Dict[str, Any]:
    """
    便捷函数：验证AutoTradeManager配置
    
    Args:
        config_name: 配置名称
        
    Returns:
        Dict[str, Any]: 验证结果
    """
    initializer = AutoTradeConfigInitializer()
    return await initializer.validate_config(config_name) 