from curl_cffi import requests
import asyncio
from typing import Dict, Any
from urllib.parse import quote
from utils.spiders.smart_money import BasicSpider


class SmartMoneySpider(BasicSpider):
    def __init__(self):
        super().__init__()
        self.base_url = "https://gmgn.ai"
        self.headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-TW;q=0.6',
            'cache-control': 'no-cache',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://gmgn.ai/trade/dDO6ZdYE?chain=sol&tab=pump_smart',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36'
        }
    
    async def setup(self):
        """设置爬虫，初始化会话并更新头信息"""
        await self.init_sessions()
        # 更新会话头信息
        self.session.headers.update(self.headers)
        # 设置浏览器模拟
        self.session.impersonate = "chrome110"  # 模拟Chrome浏览器
        
    async def get_smart_money_wallets(self, tag: str = "pump_smart", orderby: str = "pnl_7d") -> Dict[str, Any]:
        """
        使用curl-cffi获取智能钱包数据
        
        Returns:
            Dict[str, Any]: API返回的数据
        """
            
        url = f"{self.base_url}/defi/quotation/v1/rank/sol/wallets/7d"
        
        params = {
            'tag': tag,
            'device_id': '65990bc3-0a13-4d99-8c66-eeea94916b0f',
            'client_id': 'gmgn_web_2025.0224.164350',
            'from_app': 'gmgn',
            'app_ver': '2025.0224.164350',
            'tz_name': 'Asia/Shanghai',
            'tz_offset': '28800',
            'app_lang': 'zh-CN',
            'orderby': orderby,
            'direction': 'desc'
        }
        
        try:
            response = await self.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"请求失败: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"响应状态码: {e.response.status_code}")
                print(f"响应内容: {e.response.text}")
            return {}

async def main():
    spider = SmartMoneySpider()
    result = await spider.get_smart_money_wallets(tag="renowned")
    print(result)
    # 关闭爬虫会话连接
    await spider.close()

if __name__ == "__main__":
    # 使用asyncio运行异步主函数
    asyncio.run(main())