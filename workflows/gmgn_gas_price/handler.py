"""
GMGN Gas价格监控工作流处理函数模块

提供YAML配置中引用的处理函数
"""

import logging
import traceback
from typing import List, Dict, Any, Optional
from datetime import datetime

from utils.spiders.smart_money.gmgn_gas_price_spider import GmgnGasPriceSpider
from dao.gmgn_gas_price_dao import GmgnGasPriceDAO

# 创建日志记录器
logger = logging.getLogger("GmgnGasPriceHandler")

# 创建爬虫和DAO实例
spider = GmgnGasPriceSpider()
gas_price_dao = GmgnGasPriceDAO()

# 支持的链列表
SUPPORTED_CHAINS = ["sol"]  # 目前只支持Solana

async def generate_chain_list() -> Optional[List[Dict]]:
    """生成需要监控的链列表
    
    Returns:
        Optional[List[Dict]]: 链列表，如果获取失败则返回None
    """
    try:
        # 返回支持的链列表
        chain_list = []
        for chain in SUPPORTED_CHAINS:
            chain_list.append({
                "chain": chain,
                "timestamp": datetime.utcnow().isoformat()
            })
        
        logger.info(f"成功生成 {len(chain_list)} 个链的监控列表")
        return chain_list
    except Exception as e:
        logger.error(f"生成链列表时发生错误: {str(e)}\n{traceback.format_exc()}")
        return []

async def process_gas_price(chain_data: Dict) -> Optional[Dict]:
    """处理单个链的gas价格信息
    
    Args:
        chain_data: 链数据，包含链名称
        
    Returns:
        Optional[Dict]: 处理后的gas价格数据，如果处理失败则返回None
    """
    try:
        chain = chain_data.get("chain")
        if not chain:
            logger.warning("数据缺少chain字段")
            return None
        
        logger.info(f"开始获取{chain}链的gas价格信息...")
        
        # 调用爬虫获取数据
        result = await spider.get_gas_price(chain)
        
        if not result:
            logger.warning(f"获取{chain}链的gas价格信息失败")
            return None
        
        # 添加基本信息
        result.update({
            'chain': chain,
            'fetch_time': datetime.utcnow().isoformat()
        })
        
        logger.info(f"成功获取{chain}链的gas价格信息")
        return result
    except Exception as e:
        logger.error(f"处理{chain}链的gas价格数据时发生错误: {str(e)}\n{traceback.format_exc()}")
        return None

async def validate_gas_price_data(data: Any) -> bool:
    """验证gas价格数据
    
    Args:
        data: 待验证的数据
        
    Returns:
        bool: 是否验证通过
    """
    if not isinstance(data, dict):
        logger.warning(f"数据类型错误，期望dict，实际为{type(data)}")
        return False
    
    # 检查必要字段
    required_fields = ["chain", "last_block", "high", "average", "low"]
    for field in required_fields:
        if field not in data:
            logger.warning(f"数据缺少必要字段: {field}")
            return False
    
    return True

async def store_gas_price_data(data_list: List[Dict]) -> int:
    """存储gas价格数据到数据库
    
    Args:
        data_list: gas价格数据列表
        
    Returns:
        int: 成功存储的记录数
    """
    if not data_list:
        return 0
    
    success_count = 0
    
    for data in data_list:
        try:
            chain = data.get('chain')
            if not chain:
                logger.warning(f"数据缺少chain字段: {data}")
                continue
            
            # 保存到数据库
            success = await gas_price_dao.upsert_gas_price(chain, data)
            if success:
                logger.info(f"成功保存{chain}链的gas价格数据")
                success_count += 1
            else:
                logger.warning(f"保存{chain}链的gas价格数据失败")
        except Exception as e:
            logger.error(f"保存gas价格数据失败: {str(e)}\n{traceback.format_exc()}")
    
    return success_count 