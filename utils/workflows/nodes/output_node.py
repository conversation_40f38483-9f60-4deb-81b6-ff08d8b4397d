"""
输出节点模块

提供输出节点的抽象接口和基本实现
"""

import traceback
from abc import abstractmethod
from typing import Any, List

from utils.workflows.message_queue.dead_letter_queue import DeadLetterType

from .base_node import Node
from utils.workflows.message_queue.message_queue import Message, MessageQueue

class OutputNode(Node):
    """输出节点，只有输入队列"""
    
    def __init__(self, name: str = None, input_queue: MessageQueue = None):
        """初始化输出节点
        
        Args:
            name: 节点名称
            input_queue: 输入队列
        """
        super().__init__(name)
        self.input_queue = input_queue
        self.batch_size = 10  # 默认每次处理10条消息
    
    @abstractmethod
    async def handle_data(self, data: Any) -> bool:
        """处理数据的抽象方法，子类必须实现
        
        Args:
            data: 要处理的数据
            
        Returns:
            bool: 是否处理成功
        """
        pass
    
    async def process(self):
        """处理数据"""
        try:
            # 接收消息
            messages = await self.receive_messages()
            
            if not messages:
                return
            
            messages_data = [message.data for message in messages]
            msg = []
            for message_data in messages_data:
                if isinstance(message_data, list):
                    msg += message_data
                else:
                    msg.append(message_data)
            self.logger.info(f"存储 {len(msg)} 条数据")
            result = await self.handle_data(msg)
            
            if result:
                try:
                    for message in messages:
                        message_id = message.metadata.get("message_id")
                        await self.message_manager.solved_message_id(message_id)

                    self.logger.info(f"已确认消息: {message_id}，处理{'成功' if result else '失败'}")

                except Exception as e:
                    # 发送消息到死信队列
                    for message in messages:
                        await self.message_manager.send_to_dead_letter_queue(message, traceback.format_exc(), self.name, DeadLetterType.STORAGE_FAILURE)
                    self.logger.error(f"确认消息时发生错误: {traceback.format_exc()}")
            else:
                # 发送消息到死信队列
                for message in messages:
                    await self.message_manager.send_to_dead_letter_queue(message, traceback.format_exc(), self.name, DeadLetterType.STORAGE_FAILURE)
                self.logger.warning("没有有效数据需要存储")
        except Exception as e:
            # 发送消息到死信队列
            for message in messages:
                await self.message_manager.send_to_dead_letter_queue(message, traceback.format_exc(), self.name, DeadLetterType.STORAGE_FAILURE)
            self.logger.error(f"处理数据时发生错误: {str(e)}\n{traceback.format_exc()}")
            raise

class StorageNode(OutputNode):
    """存储节点，将数据存储到数据库
    
    基类提供基本的数据处理和错误处理功能
    子类需要实现validate_data和store_data方法
    """
    
    async def validate_data(self, data: Any) -> bool:
        """验证数据是否有效，由子类实现
        
        Args:
            data: 要验证的数据
            
        Returns:
            bool: 数据是否有效
        """
        # 默认实现，子类应该重写此方法
        return True
    
    async def store_data(self, data: Any) -> int:
        """存储数据到数据库，由子类实现
        
        Args:
            data: 要存储的数据
            
        Returns:
            int: 成功存储的记录数
        """
        # 默认实现，子类应该重写此方法
        self.logger.warning("store_data方法未实现，无法存储数据")
        return 0
    
    async def handle_data(self, data: List[Any]) -> bool:
        """处理数据并存储到数据库"""
        self.logger.info(f"收到数据，准备存储")
        
        # 检查数据
        if not data:
            self.logger.info("没有数据需要存储")
            return True
        
        # 确保data是列表
        if not isinstance(data, list):
            data = [data]
        
        self.logger.info(f"准备处理 {len(data)} 条数据")
        
        # 验证数据
        valid_items = []
        for item in data:
            try:
                if await self.validate_data(item):
                    valid_items.append(item)
                else:
                    self.logger.warning(f"数据验证失败: {item}")
            except Exception as e:
                self.logger.error(f"验证数据时发生错误: {str(e)}\n{traceback.format_exc()}")
                return False
        
        if not valid_items:
            self.logger.warning("没有有效数据需要存储")
            return True
        
        self.logger.info(f"验证通过 {len(valid_items)}/{len(data)} 条数据")
        
        # 存储数据
        try:
            update_count = await self.store_data(valid_items)
            if isinstance(update_count, bool):
                return update_count
            else:
                self.logger.info(f"成功存储 {update_count} 条数据")
                return True
        except Exception as e:
            error_msg = f"存储数据时发生错误: {str(e)}"
            self.logger.error(f"{error_msg}\n{traceback.format_exc()}")
            return False 