# GMGN KOL钱包统计数据爬取功能技术实现方案

**创建日期**: 2025-06-03  
**功能模块**: workflows/gmgn_kol_wallet_stats  
**版本**: 0.1.0

## 1. 技术架构概述

### 1.1 整体设计思路
基于现有项目的工作流引擎和爬虫框架，实现一个专门用于获取GMGN钱包统计数据的工作流。采用独立数据表存储策略，将统计数据与KOL基础数据分离，通过钱包地址关联。使用生产者-消费者模式，通过消息队列实现节点间的数据传递，确保高并发和容错性。

### 1.2 技术选型
- 爬虫基类: 继承`utils/spiders/smart_money/BasicSpider`
- 数据访问: 新建`GmgnWalletStatsDAO`类
- 工作流引擎: 使用现有的`utils/workflows/`框架
- 数据模型: 新建`models/gmgn_wallet_stats.py`

### 1.3 数据架构设计
采用分离存储策略：
- **kol_wallets表**: 存储KOL基本信息和社交媒体数据
- **gmgn_wallet_stats表**: 专门存储GMGN统计数据
- **关联方式**: 通过wallet_address字段关联

## 2. 核心组件设计

### 2.1 数据模型 - GmgnWalletStats

**文件位置**: `models/gmgn_wallet_stats.py`

**数据模型定义**:
```python
from datetime import datetime
from typing import Optional, List, Dict
from beanie import Document, Indexed
from pydantic import BaseModel, Field

class GmgnRiskMetrics(BaseModel):
    """GMGN风险指标"""
    token_active: str = Field(default="0", description="活跃代币数")
    token_honeypot: str = Field(default="0", description="蜜罐代币数")
    token_honeypot_ratio: float = Field(default=0.0, description="蜜罐代币比率")
    no_buy_hold: str = Field(default="0", description="未买入持有数")
    no_buy_hold_ratio: float = Field(default=0.0, description="未买入持有比率")
    sell_pass_buy: str = Field(default="0", description="卖出超过买入数")
    sell_pass_buy_ratio: float = Field(default=0.0, description="卖出超过买入比率")
    fast_tx: str = Field(default="0", description="快速交易数")
    fast_tx_ratio: float = Field(default=0.0, description="快速交易比率")

class GmgnWalletStats(Document):
    """GMGN钱包统计数据模型"""
    # 基本标识
    wallet_address: Indexed(str) = Field(description="钱包地址", unique=True)
    chain: str = Field(default="solana", description="区块链名称")
    
    # 交易统计
    buy: Optional[int] = Field(default=None, description="总买入次数")
    sell: Optional[int] = Field(default=None, description="总卖出次数")
    buy_1d: Optional[int] = Field(default=None, description="1天买入次数")
    sell_1d: Optional[int] = Field(default=None, description="1天卖出次数")
    buy_7d: Optional[int] = Field(default=None, description="7天买入次数")
    sell_7d: Optional[int] = Field(default=None, description="7天卖出次数")
    buy_30d: Optional[int] = Field(default=None, description="30天买入次数")
    sell_30d: Optional[int] = Field(default=None, description="30天卖出次数")
    txs: Optional[int] = Field(default=None, description="总交易次数")
    txs_30d: Optional[int] = Field(default=None, description="30天交易次数")
    
    # 收益指标
    pnl: Optional[float] = Field(default=None, description="总收益率")
    pnl_1d: Optional[float] = Field(default=None, description="1天收益率")
    pnl_7d: Optional[float] = Field(default=None, description="7天收益率")
    pnl_30d: Optional[float] = Field(default=None, description="30天收益率")
    realized_profit: Optional[float] = Field(default=None, description="总实现收益")
    realized_profit_1d: Optional[float] = Field(default=None, description="1天实现收益")
    realized_profit_7d: Optional[float] = Field(default=None, description="7天实现收益")
    realized_profit_30d: Optional[float] = Field(default=None, description="30天实现收益")
    
    # 余额信息
    balance: Optional[float] = Field(default=None, description="总余额")
    eth_balance: Optional[float] = Field(default=None, description="ETH余额")
    sol_balance: Optional[float] = Field(default=None, description="SOL余额")
    trx_balance: Optional[float] = Field(default=None, description="TRX余额")
    total_value: Optional[float] = Field(default=None, description="总价值")
    
    # 交易绩效
    winrate_7d: Optional[float] = Field(default=None, description="7天胜率")
    avg_cost_7d: Optional[float] = Field(default=None, description="7天平均买入成本")
    avg_holding_period_7d: Optional[float] = Field(default=None, description="7天平均持仓时间")
    token_num_7d: Optional[int] = Field(default=None, description="7天交易代币数")
    
    # 收益分布统计
    pnl_lt_minus_dot5_num_7d: Optional[int] = Field(default=None, description="7天内收益率<-0.5的次数")
    pnl_minus_dot5_0x_num_7d: Optional[int] = Field(default=None, description="7天内-0.5<=收益率<0的次数")
    pnl_lt_2x_num_7d: Optional[int] = Field(default=None, description="7天内0<=收益率<2的次数")
    pnl_2x_5x_num_7d: Optional[int] = Field(default=None, description="7天内2<=收益率<5的次数")
    pnl_gt_5x_num_7d: Optional[int] = Field(default=None, description="7天内收益率>=5的次数")
    
    # 收益分布比率
    pnl_lt_minus_dot5_num_7d_ratio: Optional[float] = Field(default=None, description="收益率<-0.5的比率")
    pnl_minus_dot5_0x_num_7d_ratio: Optional[float] = Field(default=None, description="-0.5<=收益率<0的比率")
    pnl_lt_2x_num_7d_ratio: Optional[float] = Field(default=None, description="0<=收益率<2的比率")
    pnl_2x_5x_num_7d_ratio: Optional[float] = Field(default=None, description="2<=收益率<5的比率")
    pnl_gt_5x_num_7d_ratio: Optional[float] = Field(default=None, description="收益率>=5的比率")
    
    # 风险指标
    risk: Optional[GmgnRiskMetrics] = Field(default=None, description="风险指标")
    
    # 时间信息
    last_active: Optional[datetime] = Field(default=None, description="最后活跃时间")
    data_timestamp: Optional[datetime] = Field(default=None, description="API数据时间戳")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")
    
    class Settings:
        name = "gmgn_wallet_stats"
        indexes = [
            [("wallet_address", 1)],
            [("updated_at", -1)],
            [("last_active", -1)],
        ]
```

### 2.2 爬虫组件 - GmgnWalletStatsSpider

**文件位置**: `utils/spiders/gmgn/gmgn_wallet_stats_spider.py`

**核心职责**:
- 继承BasicSpider，获得代理管理和重试机制
- 实现GMGN钱包统计API的调用逻辑
- 处理请求参数的动态生成和随机化
- 实现数据解析和格式转换

**关键方法设计**:
```python
class GmgnWalletStatsSpider(BasicSpider):
    
    async def setup(self):
        """设置爬虫会话和请求头"""
        - 生成随机的device_id和client_id
        - 设置GMGN特定的请求头
        - 配置cookie管理
        
    async def get_wallet_stats(self, wallet_address: str, period: str = "all"):
        """获取单个钱包的统计数据"""
        - 构建API URL，支持自定义period参数
        - 生成随机请求参数
        - 发送请求并处理响应
        - 解析数据并返回GmgnWalletStats对象
        
    def _generate_request_params(self):
        """生成随机化的请求参数"""
        - 随机生成device_id和client_id
        - 设置固定的必需参数
        - 返回完整参数字典
        
    def _parse_wallet_data(self, wallet_address: str, response_data: dict, period: str):
        """解析API响应数据"""
        - 验证响应格式和必需字段
        - 转换数据类型（字符串余额转浮点等）
        - 创建GmgnWalletStats实例并保存period信息
        - 处理异常数据和缺失字段
```

### 2.3 DAO组件 - GmgnWalletStatsDAO

**文件位置**: `dao/gmgn_wallet_stats_dao.py`

**核心职责**:
- 管理gmgn_wallet_stats集合的数据访问
- 实现基于钱包地址和时间窗口的查询和更新
- 支持批量操作和性能优化
- 提供与kol_wallets表的关联验证

**关键方法设计**:
```python
class GmgnWalletStatsDAO:
    
    async def find_wallets_need_stats_update(self, limit: int = 50, hours_threshold: int = 1, period: str = "all"):
        """获取需要更新统计数据的钱包地址列表"""
        - 查询kol_wallets表获取活跃钱包地址
        - 过滤掉最近已更新指定period数据的钱包
        - 按更新时间排序，返回最需要更新的地址列表
        
    async def upsert_wallet_stats(self, wallet_address: str, stats_data: dict, period: str = "all"):
        """新增或更新单个钱包的统计数据"""
        - 验证钱包地址是否在kol_wallets表中存在
        - 基于(wallet_address, chain, period)进行upsert操作
        - 更新时间戳字段
        - 返回操作结果
        
    async def batch_upsert_wallet_stats(self, stats_list: List[dict], period: str = "all"):
        """批量新增或更新钱包统计数据"""
        - 批量验证钱包地址的有效性
        - 使用bulk操作提高性能
        - 支持部分失败的错误处理
        - 返回成功和失败的统计信息
        
    async def get_wallet_stats_by_address(self, wallet_address: str, period: str = "all"):
        """根据钱包地址和时间窗口获取统计数据"""
        - 精确查询指定period的数据
        - 返回GmgnWalletStats对象或None
        
    async def get_wallet_stats_all_periods(self, wallet_address: str):
        """获取钱包所有时间窗口的统计数据"""
        - 返回同一钱包不同period的数据列表
        - 按period排序返回
```

### 2.4 工作流处理器 - Handler

**文件位置**: `workflows/gmgn_kol_wallet_stats/handler.py`

**核心函数设计**:
```python
async def generate_wallet_addresses() -> List[str]:
    """生成待更新的钱包地址列表"""
    - 调用GmgnWalletStatsDAO获取需要更新的钱包地址
    - 从kol_wallets表筛选活跃钱包
    - 返回钱包地址列表供后续节点处理
    
async def process_wallet_stats(wallet_address: str) -> Optional[Dict]:
    """处理单个钱包的统计数据获取"""
    - 初始化GmgnWalletStatsSpider实例
    - 调用GMGN API获取钱包统计数据
    - 错误处理和重试逻辑
    - 返回处理后的数据
    
async def store_wallet_stats(data: List[Dict]) -> int:
    """批量存储钱包统计数据"""
    - 数据验证和清洗
    - 调用GmgnWalletStatsDAO进行批量upsert
    - 记录成功和失败的统计信息
    
async def validate_wallet_data(data: Any) -> bool:
    """验证钱包数据的有效性"""
    - 检查必需字段的存在性
    - 验证数据类型和值的合理性
    - 确保钱包地址在kol_wallets表中存在
```

### 2.5 工作流配置 - YAML

**文件位置**: `workflows/gmgn_kol_wallet_stats/gmgn_kol_wallet_stats_workflow.yaml`

**节点设计**:
```yaml
name: "GMGN KOL钱包统计数据更新"
description: "定期获取和更新KOL钱包的统计数据到独立数据表"

nodes:
  - name: "WalletStatsSchedulerNode"
    node_type: "input"
    interval: 1800  # 30分钟
    generate_data: workflows.gmgn_kol_wallet_stats.handler.generate_wallet_addresses
    flow_control:
      max_pending_messages: 50
      check_interval: 5
      enable_flow_control: true

  - name: "WalletStatsProcessNode"
    node_type: "process"
    depend_ons: ["WalletStatsSchedulerNode"]
    concurrency: 5  # 5个并发
    interval: 2     # 2秒间隔
    process_item: workflows.gmgn_kol_wallet_stats.handler.process_wallet_stats

  - name: "WalletStatsStoreNode"
    node_type: "storage"
    depend_ons: ["WalletStatsProcessNode"]
    batch_size: 100  # 每批100条
    store_data: workflows.gmgn_kol_wallet_stats.handler.store_wallet_stats
    validate: workflows.gmgn_kol_wallet_stats.handler.validate_wallet_data
```

## 3. 数据处理逻辑

### 3.1 字段映射策略

**数据转换规则**:
- 数值字段: 直接复制或进行类型转换（string → float）
- 时间字段: 时间戳转换为datetime对象
- 嵌套对象: 映射到GmgnRiskMetrics模型
- 缺失字段: 使用默认值或保持None

**API字段到模型字段的映射**:
```python
FIELD_MAPPING = {
    'buy': 'buy',
    'sell': 'sell',
    'buy_1d': 'buy_1d',
    'sell_1d': 'sell_1d',
    'buy_7d': 'buy_7d',
    'sell_7d': 'sell_7d',
    'buy_30d': 'buy_30d',
    'sell_30d': 'sell_30d',
    'pnl': 'pnl',
    'pnl_1d': 'pnl_1d',
    'pnl_7d': 'pnl_7d',
    'pnl_30d': 'pnl_30d',
    'realized_profit': 'realized_profit',
    'realized_profit_1d': 'realized_profit_1d',
    'realized_profit_7d': 'realized_profit_7d',
    'realized_profit_30d': 'realized_profit_30d',
    'balance': 'balance',
    'sol_balance': 'sol_balance',
    'eth_balance': 'eth_balance',
    'trx_balance': 'trx_balance',
    'winrate': 'winrate_7d',
    'token_avg_cost': 'avg_cost_7d',
    'avg_holding_peroid': 'avg_holding_period_7d',
    'last_active_timestamp': 'last_active',
    'updated_at': 'data_timestamp',
    'risk': 'risk'
}
```

### 3.2 数据验证逻辑

**验证维度**:
- 必需字段检查: wallet_address不能为空
- 数据类型验证: 数值字段不能是非法值
- 业务规则验证: 胜率在0-1之间，交易次数非负等
- 关联验证: 钱包地址必须在kol_wallets表中存在

### 3.3 数据同步策略

**Upsert操作逻辑**:
- 如果钱包地址不存在，创建新记录
- 如果钱包地址已存在，更新所有统计字段
- 每次操作都更新updated_at字段
- 保持created_at字段不变（仅在创建时设置）

## 4. 关联查询支持

### 4.1 聚合查询设计

**查询模式支持**:
```python
# 获取KOL基本信息和统计数据
async def get_kol_with_stats(wallet_address: str):
    kol_info = await KOLWallet.find_one({"wallet_address": wallet_address})
    stats_info = await GmgnWalletStats.find_one({"wallet_address": wallet_address})
    return {"kol": kol_info, "stats": stats_info}

# 批量关联查询
async def get_top_performers(limit: int = 10):
    pipeline = [
        {"$lookup": {
            "from": "kol_wallets",
            "localField": "wallet_address", 
            "foreignField": "wallet_address",
            "as": "kol_info"
        }},
        {"$match": {"kol_info.tags": {"$in": ["kol"]}}},
        {"$sort": {"pnl_7d": -1}},
        {"$limit": limit}
    ]
    return await GmgnWalletStats.aggregate(pipeline).to_list()
```

### 4.2 索引优化

**关键索引设计**:
- wallet_address: 唯一索引，支持快速查找和关联
- updated_at: 倒序索引，支持按更新时间查询
- last_active: 倒序索引，支持按活跃度查询
- pnl_7d, pnl_30d: 支持收益排序查询

## 5. 错误处理和容错设计

### 5.1 分层错误处理

**爬虫层**:
- HTTP请求失败: 自动重试最多3次，切换代理
- 响应解析失败: 记录错误并跳过该钱包
- 限流检测: 增加随机延迟，降低请求频率

**工作流层**:
- 单个钱包处理失败不影响其他钱包
- 批次处理失败时进行部分回滚
- 重要错误信息记录到日志

**数据层**:
- 数据库连接失败时的重连机制
- 事务失败时的回滚处理
- 数据一致性检查

### 5.2 监控和告警机制

**性能指标监控**:
- 每小时处理的钱包数量
- API请求成功率统计
- 平均响应时间监控
- 数据更新成功率

**异常情况告警**:
- 连续失败次数超过阈值
- 数据质量异常（如大量null值）
- 系统资源使用异常

## 6. 性能优化策略

### 6.1 并发控制

**请求并发**:
- 最大5个并发HTTP请求
- 使用信号量控制并发数
- 请求间隔随机化（1-3秒）

**数据库操作**:
- 使用批量upsert操作减少数据库连接开销
- 索引优化确保查询性能
- 连接池复用减少建连成本

### 6.2 内存管理

**数据流控制**:
- 工作流消息队列大小限制
- 批次处理避免内存累积
- 及时释放不再使用的数据

## 7. 部署和配置

### 7.1 环境依赖

**新增依赖**:
- 复用现有的爬虫和工作流依赖
- 确保代理池配置正常
- MongoDB连接配置检查

### 7.2 配置参数

**可调配置项**:
- 执行间隔: 默认30分钟，可通过环境变量调整
- 批次大小: 默认50个钱包，可根据系统性能调整
- 并发数: 默认5个，可根据反爬策略调整
- 重试次数: 默认3次，可根据网络稳定性调整

## 8. 测试策略

### 8.1 单元测试覆盖

**测试组件**:
- 爬虫类的各个方法
- GmgnWalletStatsDAO的CRUD操作
- 数据转换和验证逻辑
- 错误处理机制

### 8.2 集成测试

**测试场景**:
- 完整工作流的端到端执行
- 并发处理的稳定性测试
- 数据一致性验证
- 关联查询性能测试

### 8.3 性能测试

**测试指标**:
- 处理100个钱包的总耗时
- 内存使用峰值和稳定性
- API请求成功率统计
- 数据库操作性能 