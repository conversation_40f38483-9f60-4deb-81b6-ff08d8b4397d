from typing import List, Optional, Dict, Any
from datetime import datetime
from models.smart_money_wallet import SmartMoneyWallet
from .base_dao import BaseDAO
from pymongo import UpdateOne

class SmartMoneyWalletDAO(BaseDAO[SmartMoneyWallet]):
    """智能钱包 DAO"""
    
    def __init__(self):
        super().__init__(SmartMoneyWallet)
    
    async def find_all_wallets(self) -> List[SmartMoneyWallet]:
        """获取所有智能钱包"""
        return await self.model.find_all().to_list()
    
    async def find_by_address(self, address: str) -> Optional[SmartMoneyWallet]:
        """根据地址查找钱包"""
        return await self.find_one({"address": address})
    
    async def add_wallet(
        self,
        address: str,
        name: str = None,
        description: str = None,
        tags: List[str] = None
    ) -> SmartMoneyWallet:
        """添加智能钱包"""
        wallet = SmartMoneyWallet(
            address=address,
            name=name,
            description=description,
            tags=tags or []
        )
        return await self.insert_one(wallet)
    
    async def update_wallet(
        self,
        address: str,
        name: str = None,
        description: str = None,
        tags: List[str] = None
    ) -> bool:
        """更新钱包信息"""
        update_data = {}
        if name is not None:
            update_data["name"] = name
        if description is not None:
            update_data["description"] = description
        if tags is not None:
            update_data["tags"] = tags
            
        return await self.update_one(
            {"address": address},
            update_data
        )
    
    async def delete_wallet(self, address: str) -> bool:
        """删除钱包"""
        return await self.delete_one({"address": address})
    
    async def find_by_wallet_address(self, wallet_address: str) -> Optional[SmartMoneyWallet]:
        """根据钱包地址查找钱包"""
        return await self.find_one({"wallet_address": wallet_address})
    
    async def find_by_wallet_addresses(self, wallet_addresses: List[str]) -> List[SmartMoneyWallet]:
        """根据钱包地址列表批量查找钱包"""
        return await self.find_many({"wallet_address": {"$in": wallet_addresses}})
    
    async def upsert_wallet(self, wallet_data: Dict[str, Any]) -> bool:
        """更新或插入钱包数据"""
        wallet_address = wallet_data["wallet_address"]
        existing_wallet = await self.find_by_wallet_address(wallet_address)
        
        if existing_wallet:
            # 如果存在，保留first_seen_at
            wallet_data["first_seen_at"] = existing_wallet.first_seen_at
        else:
            # 如果不存在，设置first_seen_at为当前时间
            wallet_data["first_seen_at"] = datetime.utcnow()
        
        # 更新last_updated_at
        wallet_data["last_updated_at"] = datetime.utcnow()
        
        # 使用原子更新操作
        return await self.update_one(
            {"wallet_address": wallet_address},
            wallet_data,
            upsert=True
        )
    
    async def upsert_wallets(self, wallet_data_list: List[Dict[str, Any]]) -> int:
        """批量更新或插入钱包数据"""
        if not wallet_data_list:
            return 0
            
        # 获取所有钱包地址
        wallet_addresses = [data["wallet_address"] for data in wallet_data_list]
        
        # 批量查询现有钱包
        existing_wallets = await self.find_by_wallet_addresses(wallet_addresses)
        existing_addresses = {w.wallet_address: w for w in existing_wallets}
        
        # 准备批量更新操作
        bulk_operations = []
        current_time = datetime.utcnow()
        
        for wallet_data in wallet_data_list:
            wallet_address = wallet_data["wallet_address"]
            
            # 设置时间字段
            if wallet_address in existing_addresses:
                wallet_data["first_seen_at"] = existing_addresses[wallet_address].first_seen_at
            else:
                wallet_data["first_seen_at"] = current_time
            
            wallet_data["last_updated_at"] = current_time
            
            # 创建更新操作
            bulk_operations.append(
                UpdateOne(
                    {"wallet_address": wallet_address},
                    {"$set": wallet_data},
                    upsert=True
                )
            )
        
        # 执行批量更新
        try:
            result = await self.collection.bulk_write(bulk_operations)
            self.logger.info(f"批量更新完成: 修改 {result.modified_count} 条, 插入 {result.upserted_count} 条")
            return result.modified_count + result.upserted_count
        except Exception as e:
            self.logger.error(f"批量更新钱包数据失败: {str(e)}")
            return 0
    
    async def find_active_wallets(
        self,
        days: int = 7,
        skip: int = 0,
        limit: int = 100
    ) -> List[SmartMoneyWallet]:
        """查找活跃钱包"""
        cutoff_time = datetime.utcnow() - datetime.timedelta(days=days)
        return await self.find_many(
            {"last_active": {"$gte": cutoff_time}},
            skip=skip,
            limit=limit,
            sort=[("realized_profit_7d", -1)]
        )
    
    async def find_top_wallets(
        self,
        metric: str = "realized_profit_7d",
        skip: int = 0,
        limit: int = 100
    ) -> List[SmartMoneyWallet]:
        """查找排名靠前的钱包"""
        return await self.find_many(
            {metric: {"$exists": True}},
            skip=skip,
            limit=limit,
            sort=[(metric, -1)]
        ) 