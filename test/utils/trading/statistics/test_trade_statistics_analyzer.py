#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易统计分析器测试用例

测试完整的分析流程和集成功能
"""

import unittest
from unittest.mock import AsyncMock, MagicMock, patch, Mock
from datetime import datetime, timezone, timedelta
from pathlib import Path
import tempfile
import os

from utils.trading.statistics.trade_statistics_analyzer import TradeStatisticsAnalyzer
from utils.trading.statistics.models import StatisticsConfig, ReportFormat
from models.trade_record import TradeRecord, TradeType, TradeStatus


class TestTradeStatisticsAnalyzer(unittest.IsolatedAsyncioTestCase):
    """交易统计分析器测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建模拟的依赖项
        self.mock_dao = AsyncMock()
        self.mock_verification_updater = AsyncMock()
        
        # 初始化分析器
        self.analyzer = TradeStatisticsAnalyzer(
            trade_record_dao=self.mock_dao,
            verification_updater=self.mock_verification_updater
        )
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """测试后清理"""
        # 清理临时文件
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_mock_trade_record(
        self,
        record_id: str,
        signal_id: str,
        trade_type: TradeType,
        token_in_amount: float,
        token_out_verified_amount: float,
        token_in_actual_amount: float = None,  # 新增：实际买入金额
        token_out_actual_amount: float = None,  # 新增：实际卖出金额
        created_at: datetime = None,
        strategy_name: str = "test_strategy",
        token_in_address: str = "token_in",
        token_out_address: str = "token_out"
    ) -> MagicMock:
        """创建模拟交易记录"""
        record = MagicMock(spec=TradeRecord)
        record.id = record_id
        record.signal_id = signal_id
        record.trade_type = trade_type
        record.status = TradeStatus.SUCCESS
        record.verification_status = "verified"
        record.token_in_amount = token_in_amount
        record.token_in_actual_amount = token_in_actual_amount if token_in_actual_amount is not None else token_in_amount
        record.token_out_verified_amount = token_out_verified_amount
        record.token_out_actual_amount = token_out_actual_amount if token_out_actual_amount is not None else token_out_verified_amount
        record.created_at = created_at or datetime.now(timezone.utc)
        record.strategy_name = strategy_name
        record.token_in_address = token_in_address
        record.token_out_address = token_out_address
        
        # 移除_mock_name属性以避免我们的Mock检测逻辑
        if hasattr(record, '_mock_name'):
            delattr(record, '_mock_name')
        
        return record
    
    async def test_analyze_trade_statistics_basic(self):
        """测试基本的交易统计分析"""
        # 创建测试数据
        signal_id = "507f1f77bcf86cd799439011"
        buy_record = self.create_mock_trade_record(
            record_id="507f1f77bcf86cd799439012",
            signal_id=signal_id,
            trade_type=TradeType.BUY,
            token_in_amount=100.0,
            token_in_actual_amount=100.0,  # 实际花费100.0
            token_out_verified_amount=1000.0,
            token_out_actual_amount=None,  # 买入不涉及卖出实际金额
            created_at=datetime(2024, 1, 1, 10, 0, 0, tzinfo=timezone.utc)
        )
        sell_record = self.create_mock_trade_record(
            record_id="507f1f77bcf86cd799439013",
            signal_id=signal_id,
            trade_type=TradeType.SELL,
            token_in_amount=1000.0,
            token_in_actual_amount=None,  # 卖出不涉及买入实际金额
            token_out_verified_amount=120.0,
            token_out_actual_amount=120.0,  # 实际收入120.0
            created_at=datetime(2024, 1, 1, 11, 0, 0, tzinfo=timezone.utc)
        )
        
        # 模拟DAO返回数据
        self.mock_dao.find_verified_trade_records.return_value = [buy_record, sell_record]
        
        # 创建配置
        config = StatisticsConfig()
        config.start_date = datetime(2024, 1, 1, tzinfo=timezone.utc)
        config.end_date = datetime(2024, 1, 2, tzinfo=timezone.utc)
        
        # 执行分析
        result = await self.analyzer.analyze(config)
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertIsNotNone(result.overall_stats)
        self.assertEqual(result.overall_stats.total_trades, 1)
        self.assertAlmostEqual(result.overall_stats.total_win_rate, 100.0)  # 100%胜率
        self.assertAlmostEqual(result.overall_stats.total_profit_rate, 20.0)  # (120-100)/100 * 100 = 20%
        
        # 验证DAO调用
        self.mock_dao.find_verified_trade_records.assert_called_once()
    
    async def test_analyze_trade_statistics_empty_data(self):
        """测试空数据的分析"""
        # 模拟DAO返回空数据
        self.mock_dao.find_verified_trade_records.return_value = []
        
        # 创建配置
        config = StatisticsConfig()
        
        # 执行分析
        result = await self.analyzer.analyze(config)
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertIsNotNone(result.overall_stats)
        self.assertEqual(result.overall_stats.total_trades, 0)
        self.assertEqual(result.overall_stats.total_win_rate, 0.0)
        self.assertEqual(result.overall_stats.total_profit_rate, 0.0)
        self.assertEqual(len(result.token_stats), 0)
        self.assertEqual(len(result.strategy_stats), 0)
    
    async def test_analyze_trade_statistics_with_filters(self):
        """测试带过滤条件的分析"""
        # 创建测试数据
        buy_record = self.create_mock_trade_record(
            record_id="507f1f77bcf86cd799439012",
            signal_id="507f1f77bcf86cd799439011",
            trade_type=TradeType.BUY,
            token_in_amount=100.0,
            token_in_actual_amount=100.0,  # 实际花费
            token_out_verified_amount=1000.0,
            token_out_actual_amount=None,  # 买入不涉及卖出实际金额
            strategy_name="target_strategy",
            token_in_address="target_token"
        )
        sell_record = self.create_mock_trade_record(
            record_id="507f1f77bcf86cd799439013",
            signal_id="507f1f77bcf86cd799439011",
            trade_type=TradeType.SELL,
            token_in_amount=1000.0,
            token_in_actual_amount=None,  # 卖出不涉及买入实际金额
            token_out_verified_amount=120.0,
            token_out_actual_amount=120.0,  # 实际收入
            strategy_name="target_strategy",
            token_out_address="target_token"
        )
        
        # 模拟DAO返回数据
        self.mock_dao.find_verified_trade_records.return_value = [buy_record, sell_record]
        
        # 创建带过滤条件的配置
        config = StatisticsConfig()
        config.strategy_filter = ["target_strategy"]
        config.token_filter = ["target_token"]
        
        # 执行分析
        result = await self.analyzer.analyze(config)
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertEqual(result.overall_stats.total_trades, 1)
        
        # 验证DAO调用时传递了过滤条件
        call_args = self.mock_dao.find_verified_trade_records.call_args
        self.assertEqual(call_args.kwargs['strategies'], ["target_strategy"])
        self.assertEqual(call_args.kwargs['tokens'], ["target_token"])
    
    @patch('utils.trading.statistics.trade_statistics_analyzer.HTMLReportGenerator')
    async def test_generate_statistics_report_html(self, mock_html_generator_class):
        """测试HTML报告生成"""
        # 模拟DAO返回空数据
        self.mock_dao.find_verified_trade_records.return_value = []
        
        # 创建模拟HTML生成器
        mock_html_generator = AsyncMock()
        mock_html_generator_class.return_value = mock_html_generator
        mock_html_generator.generate_report.return_value = "<html>Test Report</html>"
        
        # 创建配置
        config = StatisticsConfig()
        
        # 执行报告生成
        result = await self.analyzer.generate_report(
            format=ReportFormat.HTML,
            config=config
        )
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertIn("html", result.lower())
    
    async def test_generate_statistics_report_json(self):
        """测试JSON报告生成"""
        # 模拟DAO返回空数据
        self.mock_dao.find_verified_trade_records.return_value = []
        
        # 创建模拟JSON生成器并替换分析器的实例
        mock_json_generator = Mock()
        mock_json_generator.generate_report.return_value = '{"test": "data"}'
        self.analyzer.json_generator = mock_json_generator
        
        # 创建配置
        config = StatisticsConfig()
        
        # 执行报告生成
        result = await self.analyzer.generate_report(
            format=ReportFormat.JSON,
            config=config
        )
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertIn("test", result)
    
    async def test_validate_data_integrity(self):
        """测试数据完整性验证"""
        # 创建测试数据
        buy_record = self.create_mock_trade_record(
            record_id="507f1f77bcf86cd799439012",
            signal_id="507f1f77bcf86cd799439011",
            trade_type=TradeType.BUY,
            token_in_amount=100.0,
            token_out_verified_amount=1000.0
        )
        
        # 模拟DAO返回数据
        self.mock_dao.find_verified_trade_records.return_value = [buy_record]
        
        # 创建配置
        config = StatisticsConfig()
        
        # 执行验证
        result = await self.analyzer.validate_data_integrity(config)
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertIn("total_records", result)
        self.assertIn("verified_records", result)
        self.assertIn("integrity_percentage", result)
    
    async def test_generate_statistics_summary(self):
        """测试统计摘要生成"""
        # 创建测试数据
        signal_id = "507f1f77bcf86cd799439011"
        buy_record = self.create_mock_trade_record(
            record_id="507f1f77bcf86cd799439012",
            signal_id=signal_id,
            trade_type=TradeType.BUY,
            token_in_amount=100.0,
            token_out_verified_amount=1000.0
        )
        sell_record = self.create_mock_trade_record(
            record_id="507f1f77bcf86cd799439013",
            signal_id=signal_id,
            trade_type=TradeType.SELL,
            token_in_amount=1000.0,
            token_out_verified_amount=120.0
        )
        
        # 模拟DAO返回数据
        self.mock_dao.find_verified_trade_records.return_value = [buy_record, sell_record]
        
        # 创建配置
        config = StatisticsConfig()
        
        # 执行摘要生成
        result = await self.analyzer.get_statistics_summary(config)
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertIn("total_trades", result)
        self.assertIn("total_win_rate", result)
        self.assertIn("total_profit_rate", result)
    
    async def test_config_validation(self):
        """测试配置验证"""
        # 测试无效的日期范围
        config = StatisticsConfig()
        config.start_date = datetime(2024, 1, 2, tzinfo=timezone.utc)
        config.end_date = datetime(2024, 1, 1, tzinfo=timezone.utc)  # 结束日期早于开始日期
        
        # 模拟DAO返回空数据
        self.mock_dao.find_verified_trade_records.return_value = []
        
        # 执行分析（应该正常处理，因为配置验证在DAO层）
        result = await self.analyzer.analyze(config)
        self.assertIsNotNone(result)
    
    async def test_error_handling(self):
        """测试错误处理"""
        # 模拟DAO抛出异常
        self.mock_dao.find_verified_trade_records.side_effect = Exception("Database error")
        
        # 创建配置
        config = StatisticsConfig()
        
        # 应该抛出异常
        with self.assertRaises(Exception):
            await self.analyzer.analyze(config)
    
    async def test_performance_with_large_dataset(self):
        """测试大数据集性能"""
        # 创建大量测试数据
        records = []
        for i in range(1000):
            signal_id = f"signal_{i // 2}"  # 每两条记录一个信号
            trade_type = TradeType.BUY if i % 2 == 0 else TradeType.SELL
            
            record = self.create_mock_trade_record(
                record_id=f"record_{i}",
                signal_id=signal_id,
                trade_type=trade_type,
                token_in_amount=100.0,
                token_out_verified_amount=110.0 if trade_type == TradeType.SELL else 1000.0
            )
            records.append(record)
        
        # 模拟DAO返回大量数据
        self.mock_dao.find_verified_trade_records.return_value = records
        
        # 创建配置
        config = StatisticsConfig()
        
        # 记录开始时间
        start_time = datetime.now()
        
        # 执行分析
        result = await self.analyzer.analyze(config)
        
        # 记录结束时间
        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertEqual(result.overall_stats.total_trades, 500)  # 500个交易对
        
        # 验证性能（应该在合理时间内完成）
        self.assertLess(execution_time, 10.0)  # 应该在10秒内完成


if __name__ == '__main__':
    unittest.main() 