import asyncio
import logging
import os
import time
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json

from utils.backtest_event_driven.events import TransactionEvent, SignalType, FillEvent, EventType
from utils.backtest_event_driven.event_queue import EventQueue
from utils.backtest_event_driven.data_handler import DataHandler
from utils.backtest_event_driven.strategy_adapter import BuyStrategyAdapter, SellStrategyAdapter
from utils.backtest_event_driven.portfolio import PortfolioManager
from utils.backtest_event_driven.execution_handler import ExecutionHandler
from utils.backtest_event_driven.result_analyzer import ResultAnalyzer
from utils.backtest.config_manager import BacktestConfig

logger = logging.getLogger("EventDrivenBacktest")


class EventDrivenBacktest:
    """事件驱动型回测系统"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """初始化回测系统
        
        Args:
            config: 配置参数
        """
        # 如果是BacktestConfig对象，转换为字典
        if hasattr(config, '__dict__'):
            self.config = config.__dict__.copy()
        else:
            self.config = config or {}
            
        self.event_queue = EventQueue()
        self.data_handler = None
        self.buy_strategy = None
        self.sell_strategy = None
        self.portfolio_manager = None
        self.execution_handler = None
        self.result_analyzer = None
        self.result_dir = f"ed_backtest_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
    async def setup(self):
        """初始化回测环境"""
        logger.info("创建结果目录...")
        os.makedirs(self.result_dir, exist_ok=True)
        
        logger.info("初始化组件...")
        # 初始化数据处理器
        skip_token_api_query = self.config.get("skip_token_api_query", False)
        self.data_handler = DataHandler(skip_token_api_query=skip_token_api_query)
        if skip_token_api_query:
            logger.info("已配置跳过代币API查询，将使用本地数据或基本信息")
        
        # 初始化事件队列
        self.event_queue = EventQueue()
        
        # 初始化策略适配器
        self.buy_strategy = BuyStrategyAdapter(self.config, self.event_queue)
        self.sell_strategy = SellStrategyAdapter(self.config, self.event_queue)
        
        # 初始化投资组合管理器
        self.portfolio_manager = PortfolioManager(self.event_queue, self.config)
        
        # 初始化执行处理器
        self.execution_handler = ExecutionHandler(self.event_queue, self.data_handler, self.config)
        
        # 初始化结果分析器
        self.result_analyzer = ResultAnalyzer(self.config)
        
        # 添加同步处理代码 - 将Portfolio的持仓变化同步到SellStrategyAdapter中
        async def sync_holdings(event):
            """同步Portfolio和SellStrategyAdapter的持仓信息"""
            if not isinstance(event, FillEvent):
                return
                
            token_address = event.token_address
            
            if event.signal_type == SignalType.BUY:
                # 买入时，更新SellStrategyAdapter的持仓信息
                token_info = event.token_info or {}
                
                # 确保token_info中包含buy_time字段，这是卖出策略所需的
                if isinstance(token_info, dict):
                    token_info['buy_time'] = event.timestamp
                    
                    # 如果token_info中没有kol_wallets字段，尝试从portfolio中获取
                    if 'kol_wallets' not in token_info:
                        # 查找portfolio中的同一代币持仓
                        if token_address in self.portfolio_manager.portfolio.positions:
                            position = self.portfolio_manager.portfolio.positions[token_address]
                            if position.token_info and 'kol_wallets' in position.token_info:
                                token_info['kol_wallets'] = position.token_info['kol_wallets']
                                logger.debug(f"从Portfolio获取kol_wallets信息: {token_address}")
                            else:
                                # 如果portfolio中也没有，设置为空列表
                                token_info['kol_wallets'] = []
                        else:
                            token_info['kol_wallets'] = []
                
                self.sell_strategy.holdings[token_address] = {
                    'bought_at': event.timestamp,
                    'price': event.price,
                    'amount': event.amount,
                    'token_info': token_info,
                    'kol_wallets': token_info.get('kol_wallets', [])  # 额外保存一份kol_wallets，方便访问
                }
                logger.debug(f"同步持仓: 买入 {token_address} 到卖出策略，kol_wallets数量: {len(token_info.get('kol_wallets', []))}")
            elif event.signal_type == SignalType.SELL:
                # 卖出时，从SellStrategyAdapter中移除持仓信息
                if token_address in self.sell_strategy.holdings:
                    logger.debug(f"同步持仓: 从卖出策略中移除 {token_address}")
                    del self.sell_strategy.holdings[token_address]
                    
        # 注册同步处理器
        self.event_queue.register_handler(EventType.FILL, sync_holdings)
        
    async def prepare_time_range(self):
        """准备回测时间范围"""
        self.start_time = self.config.get("backtest_start_time")
        self.end_time = self.config.get("backtest_end_time")
        
        if not self.start_time or not self.end_time:
            # 如果未指定时间范围，使用最近30天
            current_time = int(time.time())
            if not self.end_time:
                self.end_time = current_time
            if not self.start_time:
                self.start_time = self.end_time - 30 * 24 * 3600  # 30天前
        
        logger.info(f"回测时间范围: {datetime.fromtimestamp(self.start_time)} 到 {datetime.fromtimestamp(self.end_time)}")
    
    async def load_wallets(self):
        """加载KOL钱包数据"""
        logger.info("加载KOL钱包数据...")
        wallets = await self.data_handler.load_wallets()
        
        # 设置钱包数据到策略适配器
        self.buy_strategy.set_wallets(wallets)
        self.sell_strategy.set_wallets(wallets)
        
        logger.info(f"加载了 {len(wallets)} 个KOL钱包")
        
    async def process_historical_data(self):
        """处理历史数据，生成交易事件"""
        logger.info("开始处理历史数据...")
        
        # 设置处理间隔，用于控制日志输出和垃圾回收频率
        batch_count = 0
        processed_count = 0
        
        # 从数据库流式加载历史活动记录
        async for events_batch in self.data_handler.stream_activities(
            self.start_time, self.end_time, batch_size=100
        ):
            batch_count += 1
            
            # 将事件批次推入队列
            self.event_queue.push_all(events_batch)
            processed_count += len(events_batch)
            
            # 处理队列中的所有事件
            logger.info(f"处理第 {batch_count} 批事件，共 {len(events_batch)} 个事件")
            events_processed = await self.event_queue.process_all()
            
            # 每处理5批数据输出一次队列状态
            if batch_count % 5 == 0:
                event_counts = self.event_queue.event_counts()
                logger.info(f"已处理 {processed_count} 个交易事件, "
                           f"当前队列状态: {event_counts}")
                
                # 每处理20批数据执行一次垃圾回收
                if batch_count % 20 == 0:
                    import gc
                    gc.collect()
                    logger.info("执行垃圾回收，释放内存")
        
        logger.info(f"历史数据处理完成，共处理 {processed_count} 个交易事件")
        
    async def analyze_results(self):
        """分析回测结果"""
        logger.info("分析回测结果...")
        
        # 计算统计数据
        statistics = self.result_analyzer.calculate_statistics(self.portfolio_manager.portfolio)
        
        # 导出结果
        self.result_analyzer.export_to_json(
            self.portfolio_manager.portfolio, 
            statistics, 
            f"{self.result_dir}/results.json"
        )
        
        self.result_analyzer.export_to_csv(
            self.portfolio_manager.portfolio,
            f"{self.result_dir}/trades.csv"
        )
        
        # 绘制图表
        self.result_analyzer.plot_equity_curve(
            self.portfolio_manager.portfolio,
            f"{self.result_dir}/equity_curve.png"
        )
        
        self.result_analyzer.plot_returns_distribution(
            self.portfolio_manager.portfolio,
            f"{self.result_dir}/returns_distribution.png"
        )
        
        # 输出统计数据
        logger.info(f"回测完成，统计数据：")
        logger.info(f"总交易数: {statistics['total_trades']}")
        logger.info(f"盈利交易数: {statistics['winning_trades']}")
        logger.info(f"亏损交易数: {statistics['losing_trades']}")
        logger.info(f"胜率: {statistics['win_rate']:.2%}")
        logger.info(f"收益率: {statistics['return_rate']:.2%}")
        logger.info(f"单笔最大回撤: {statistics['max_drawdown']:.2%}")
        if 'max_profit_per_trade' in statistics:
            logger.info(f"单笔最大收益: {statistics['max_profit_per_trade']:.2%}")
        
        # Return the full results dictionary including trades and stats
        result_data = {
            "config": self.config,
            "statistics": statistics,
            "result_dir": self.result_dir,
            "trades": self.portfolio_manager.portfolio.trade_history
        }
        
        logger.info(f"回测结果已导出到: {self.result_dir}/results.json")
        
        return result_data
    
    async def run(self):
        """运行回测流程"""
        logger.info(f"开始事件驱动回测，配置：{self.config}")
        
        # 初始化环境
        await self.setup()
        
        # 准备时间范围
        await self.prepare_time_range()
        
        # 加载KOL钱包数据
        await self.load_wallets()
        
        # 从配置中获取筛选参数
        kol_account_min_txs = self.config.get('kol_account_min_txs', 1)
        kol_account_max_txs = self.config.get('kol_account_max_txs', 10)
        min_amount = self.config.get('transaction_min_amount', 0)
        kol_min_winrate_7d = self.config.get('kol_min_winrate_7d', None)
        
        logger.info("开始处理历史数据...")
        
        # 设置处理间隔，用于控制日志输出和垃圾回收频率
        batch_count = 0
        processed_count = 0
        
        # 添加一个集合来跟踪已处理的交易活动，避免重复处理多次
        # 使用tuple(tx_hash, timestamp, token_address, event_type)作为唯一键
        processed_transactions = set()
        
        # 使用优化的stream_activities方法加载符合条件的KOL活动记录
        async for events_batch in self.data_handler.stream_activities(
            self.start_time, 
            self.end_time, 
            batch_size=100,
            kol_account_min_txs=kol_account_min_txs,
            kol_account_max_txs=kol_account_max_txs,
            min_amount=min_amount,
            kol_min_winrate_7d=kol_min_winrate_7d
        ):
            batch_count += 1
            
            # 过滤掉已经处理过的交易活动
            filtered_events = []
            for event in events_batch:
                # 获取交易数据
                tx_data = event.transaction_data
                # 创建唯一标识符
                tx_key = (
                    tx_data.get('tx_hash', ''), 
                    tx_data.get('timestamp', 0),
                    tx_data.get('token', {}).get('address', ''),
                    tx_data.get('event_type', '')
                )
                
                # 如果这个交易还没有处理过，加入到要处理的列表
                if tx_key not in processed_transactions:
                    processed_transactions.add(tx_key)
                    filtered_events.append(event)
            
            # 只处理未处理过的事件
            if filtered_events:
                # 将事件批次推入队列
                self.event_queue.push_all(filtered_events)
                processed_count += len(filtered_events)
                
                # 处理队列中的所有事件
                logger.info(f"处理第 {batch_count} 批事件，共 {len(filtered_events)} 个事件 (过滤前 {len(events_batch)} 个)")
                events_processed = await self.event_queue.process_all()
                
                # 每处理5批数据输出一次队列状态
                if batch_count % 5 == 0:
                    event_counts = self.event_queue.event_counts()
                    logger.info(f"已处理 {processed_count} 个交易事件, "
                                f"当前队列状态: {event_counts}")
                    
                    # 每处理20批数据执行一次垃圾回收
                    if batch_count % 20 == 0:
                        import gc
                        gc.collect()
                        logger.info("执行垃圾回收，释放内存")
            else:
                logger.info(f"第 {batch_count} 批次中的所有事件 ({len(events_batch)}个) 已被处理过，跳过")
        
        logger.info(f"历史数据处理完成，共处理 {processed_count} 个交易事件")
        
        # 分析结果
        result_data = await self.analyze_results()
        
        # 保存跳过的交易记录
        if hasattr(self.execution_handler, 'skipped_trades') and self.execution_handler.skipped_trades:
            logger.info(f"记录因价格不可用而跳过的交易: {len(self.execution_handler.skipped_trades)}笔")
            skipped_trades_file = os.path.join(self.result_dir, "skipped_trades.json")
            with open(skipped_trades_file, 'w') as f:
                json.dump(self.execution_handler.skipped_trades, f, indent=4, default=str)
        
        return result_data


async def run_event_driven_backtest(config: BacktestConfig):
    """运行事件驱动回测
    
    Args:
        config: 回测配置
    
    Returns:
        Dict[str, Any]: 回测结果
    """
    backtest = EventDrivenBacktest(config)
    return await backtest.run()


if __name__ == "__main__":
    # 简单测试代码
    from utils.backtest.config_manager import ConfigManager
    
    async def test():
        config = ConfigManager.load_from_file("config/backtest_config.yaml")
        result = await run_event_driven_backtest(config)
        print(json.dumps(result, indent=2, default=str))
        
    asyncio.run(test()) 