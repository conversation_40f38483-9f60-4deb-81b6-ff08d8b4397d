"""
GMGN代币买家监控工作流处理函数模块

提供YAML配置中引用的处理函数
"""

import logging
import traceback
from typing import List, Dict, Any, Optional
from datetime import datetime

from models.token import Token
from utils.spiders.smart_money.gmgn_top_buyers import GmgnTopBuyersSpider
from dao.gmgn_token_buyers_dao import GmgnTokenBuyersDAO

# 创建日志记录器
logger = logging.getLogger("GmgnTokenBuyersHandler")

# 创建爬虫和DAO实例
spider = GmgnTopBuyersSpider()
token_buyers_dao = GmgnTokenBuyersDAO()

async def generate_token_list() -> Optional[List[Dict]]:
    """生成需要监控的代币列表
    
    从数据库获取所有代币，并生成监控列表
    
    Returns:
        Optional[List[Dict]]: 代币列表，如果获取失败则返回None
    """
    try:
        # 从数据库获取所有代币
        tokens = await Token.find_all().to_list()
        
        if not tokens:
            logger.info("没有需要监控的代币")
            return []
        
        # 转换为字典列表
        token_dicts = []
        for token in tokens:
            token_dict = {
                "address": token.address,
                "symbol": token.symbol,
                "name": token.name,
                "chain": "sol",  # 目前只支持Solana
                "timestamp": datetime.utcnow().isoformat()
            }
            token_dicts.append(token_dict)
        
        logger.info(f"成功获取 {len(tokens)} 个监控代币")
        return token_dicts
    except Exception as e:
        logger.error(f"获取监控代币列表时发生错误: {str(e)}\n{traceback.format_exc()}")
        return []

async def process_token_buyers(token_data: Dict) -> Optional[Dict]:
    """处理单个代币的买家信息
    
    Args:
        token_data: 代币数据，包含地址和链信息
        
    Returns:
        Optional[Dict]: 处理后的买家数据，如果处理失败则返回None
    """
    try:
        chain = token_data.get("chain", "sol")
        address = token_data.get("address")
        symbol = token_data.get("symbol")
        
        if not address:
            logger.warning("数据缺少address字段")
            return None
        
        logger.info(f"开始获取代币 {address} 的买家信息...")
        
        # 调用爬虫获取数据
        result = await spider.get_top_buyers(chain=chain, address=address)
        
        if not result:
            logger.warning(f"获取代币 {address} 的买家信息失败")
            return None
        
        # 格式化数据
        formatted_data = spider.format_top_buyers(result)
        
        if not formatted_data:
            logger.warning(f"格式化代币 {address} 的买家信息失败")
            return None
        
        # 添加基本信息
        formatted_data.update({
            'chain': chain,
            'address': address,
            'symbol': symbol,
            'fetch_time': datetime.utcnow().isoformat()
        })
        
        # 检查holders数据
        if not formatted_data.get('holders'):
            logger.warning(f"代币 {address} 的holders数据为空")
            # 从原始数据中获取holders
            holders_data = result.get('holderInfo', [])
            if holders_data:
                formatted_data['holders'] = holders_data
                logger.info(f"从原始数据中恢复了 {len(holders_data)} 条holders数据")
        
        logger.info(f"成功获取代币 {address} 的买家信息 (holders数量: {len(formatted_data.get('holders', []))})")
        return formatted_data
    except Exception as e:
        logger.error(f"处理代币买家数据时发生错误: {str(e)}\n{traceback.format_exc()}")
        return None

async def validate_token_buyers_data(data: Any) -> bool:
    """验证代币买家数据
    
    Args:
        data: 待验证的数据
        
    Returns:
        bool: 是否验证通过
    """
    if not isinstance(data, dict):
        logger.warning(f"数据类型错误，期望dict，实际为{type(data)}")
        return False
    
    # 检查必要字段
    required_fields = ["chain", "address", "holders"]
    for field in required_fields:
        if field not in data:
            logger.warning(f"数据缺少必要字段: {field}")
            return False
    
    # 检查holders是否为列表
    if not isinstance(data.get("holders", []), list):
        logger.warning("holders字段不是列表类型")
        return False
    
    return True

async def store_token_buyers_data(data_list: List[Dict]) -> int:
    """存储代币买家数据到数据库
    
    Args:
        data_list: 代币买家数据列表
        
    Returns:
        int: 成功存储的记录数
    """
    if not data_list:
        return 0
    
    success_count = 0
    
    # 按链分组的数据
    data_by_chain = {}
    
    # 处理每条数据并按链分组
    for data in data_list:
        try:
            chain = data.get('chain', 'sol')
            address = data.get('address')
            
            if not address:
                logger.warning(f"数据缺少address字段: {data}")
                continue
                
            if chain not in data_by_chain:
                data_by_chain[chain] = []
                
            data_by_chain[chain].append(data)
            
        except Exception as e:
            logger.error(f"处理数据时发生错误: {str(e)}\n{traceback.format_exc()}")
    
    # 按链批量更新数据
    for chain, chain_data_list in data_by_chain.items():
        try:
            if chain_data_list:
                logger.info(f"准备更新链 {chain} 的 {len(chain_data_list)} 条数据")
                update_count = await token_buyers_dao.upsert_token_buyers_many(chain, chain_data_list)
                logger.info(f"链 {chain} 批量更新完成: 更新了 {update_count} 条记录")
                success_count += update_count
        except Exception as e:
            logger.error(f"批量更新链 {chain} 的数据时发生错误: {str(e)}\n{traceback.format_exc()}")
            continue
    
    return success_count 