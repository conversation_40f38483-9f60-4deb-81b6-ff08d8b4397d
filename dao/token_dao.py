from datetime import datetime
from typing import AsyncGenerator, List, Optional, Dict
import logging

from pymongo import UpdateOne

from models.token import Token
from dao.base_dao import BaseDAO


class TokenDAO(BaseDAO[Token]):
    """代币数据访问对象"""
    
    def __init__(self):
        super().__init__(Token)
        self.logger = logging.getLogger(__name__) # 添加日志记录器
    
    async def find_by_address(self, address: str) -> Optional[Token]:
        """根据代币地址查找代币信息
        
        Args:
            address: 代币地址
            
        Returns:
            Token对象，如果不存在则返回None
        """
        return await self.collection.find_one({"address": address})
    
    async def find_by_addresses(self, addresses: List[str]) -> List[dict]:
        """根据代币地址列表批量查找代币信息
        
        Args:
            addresses: 代币地址列表
            
        Returns:
            Token对象列表，不存在的地址将不会包含在结果中
        """
        return await self.collection.find({"address": {"$in": addresses}}).to_list()
    
    async def save(self, token: Token) -> Token:
        """保存代币信息
        
        Args:
            token: 要保存的Token对象
            
        Returns:
            保存后的Token对象
        """
        return await token.save()
    
    async def save_tokens(self, tokens: List[Token | dict]) -> bool:
        """批量保存代币信息
        
        Args:
            tokens: 要保存的Token对象列表或字典列表
            
        Returns:
            保存后的Token对象列表
        """
        try:
            if not tokens:
                return False
                
            # 准备批量操作
            operations = []
            addresses = []
            
            # 准备所有更新操作
            for token_data in tokens:
                if isinstance(token_data, dict):
                    address = token_data.get("address")
                    if not address:
                        continue
                    update_data = token_data
                else:
                    address = token_data.address
                    update_data = token_data.dict(exclude_unset=True)
                    
                addresses.append(address)
                operations.append(
                    UpdateOne(
                        {"address": address},
                        {"$set": update_data},
                        upsert=True
                    )
                )
            
            # 执行批量写入
            if operations:
                await self.collection.bulk_write(operations)
                
            return True
        except Exception as e:
            self.logger.error(f"批量保存代币信息失败: {e}")
            return False
            
    async def update_token(self, address: str, update_data: dict) -> Optional[Token]:
        """更新代币信息
        
        Args:
            address: 代币地址
            update_data: 要更新的字段和值
            
        Returns:
            更新后的Token对象，如果不存在则返回None
        """
        result = await self.collection.find_one_and_update(
            {"address": address},
            {"$set": {**update_data, "updated_at": datetime.utcnow()}},
            upsert=True,
            return_document=True
        )
        return Token(**result) if result else None
    
    async def get_or_create(self, address: str) -> Token:
        """获取或创建代币记录
        
        Args:
            address: 代币地址
            
        Returns:
            Token对象
        """
        await self.collection.update_one(
            {"address": address},
            {"$set": {"address": address}},
            upsert=True
        )
        return await self.find_by_address(address)
    
    async def filter_token_no_first_mint_info(self) -> AsyncGenerator[List[dict], None]:
        """过滤没有first_mint_info的代币
        
        Returns:
            Token对象列表
        """
        limit = 100
        offset = 0
        while True:
            tokens = await self.collection.find({"first_mint_time": None}).skip(offset).limit(limit).to_list(None)
            if not tokens:
                break
            yield tokens
            offset += limit
    
    async def get_token_no_links(self) -> AsyncGenerator[List[dict], None]:
        """获取在gmgn_token_links表中没有记录的代币
        
        Returns:
            异步生成器，每次产生一批没有链接信息的Token字典列表
        """
        limit = 100
        offset = 0
        while True:
            pipeline = [
                {
                    "$lookup": {
                        "from": "gmgn_token_links",  # 要连接的集合
                        "localField": "address",  # 当前集合（tokens）的连接字段
                        "foreignField": "address",  # 目标集合（gmgn_token_links）的连接字段
                        "as": "links_info"  # 输出数组字段名
                    }
                },
                {
                    "$match": {
                        "links_info": {"$eq": []}  # 筛选出 links_info 为空数组的文档，表示没有找到匹配项
                    }
                },
                {
                    "$skip": offset
                },
                {
                    "$limit": limit
                },
                {
                    "$project": {
                        "address": 1,
                        "name": 1,
                        "symbol": 1,
                        "decimals": 1,
                        "icon": 1,
                        "_id": 0
                    }
                }
            ]
            # 执行聚合查询，注意 to_list 需要指定 length 参数以配合 limit
            tokens = await self.collection.aggregate(pipeline).to_list(length=limit)
            if not tokens:
                break  # 如果没有更多结果，则退出循环
            yield tokens  # 产生当前批次的结果
            offset += limit # 更新偏移量以便获取下一批
            
    async def get_tokens_address(self) -> AsyncGenerator[str, None]:
        """获取所有唯一的代币地址

        Yields:
            str: 单个代币地址
        """
        try:
            # 使用 distinct 命令获取所有唯一的 address 值
            all_addresses = await self.collection.distinct("address")
            
            # 逐个产生地址
            for address in all_addresses:
                yield address

        except Exception as e:
            self.logger.error(f"获取 tokens 地址时出错: {e}", exc_info=True)
            # 可以在这里决定是否要抛出异常或返回空
