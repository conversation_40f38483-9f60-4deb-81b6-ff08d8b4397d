"""报告上传器

负责将生成的HTML报告文件上传到指定位置，并生成公网访问链接。
支持多种上传方式：
- 本地文件系统 (用于Web服务器静态文件目录)
- 云存储服务 (预留接口)
"""

import os
import shutil
import logging
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
from urllib.parse import urljoin

logger = logging.getLogger(__name__)


class ReportUploader:
    """报告上传器
    
    负责上传HTML报告文件并生成访问链接
    """
    
    def __init__(self, 
                 upload_path: str,
                 base_url: str,
                 create_subdirs: bool = True):
        """初始化上传器
        
        Args:
            upload_path: 上传目录路径
            base_url: 基础URL，用于生成访问链接
            create_subdirs: 是否自动创建子目录
        """
        self.upload_path = Path(upload_path)
        self.base_url = base_url.rstrip('/')
        self.create_subdirs = create_subdirs
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 确保上传目录存在
        if create_subdirs:
            self.upload_path.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"报告上传器初始化完成，上传路径: {self.upload_path}, 基础URL: {self.base_url}")
    
    def upload_html_report(self, 
                          html_content: str, 
                          filename: Optional[str] = None) -> Dict[str, Any]:
        """上传HTML报告内容
        
        Args:
            html_content: HTML内容字符串
            filename: 目标文件名，如果为None则自动生成
            
        Returns:
            Dict: 上传结果，包含文件路径和访问URL
        """
        try:
            # 生成目标文件名
            if filename is None:
                filename = self.generate_filename("report")
            
            # 构建目标路径
            target_path = self.upload_path / filename
            
            # 如果文件名包含子目录，需要创建目录
            if '/' in filename:
                target_dir = target_path.parent
                if self.create_subdirs:
                    self._ensure_directory_exists(target_dir)
                elif not target_dir.exists():
                    return {
                        "success": False,
                        "error": f"目录不存在且禁用了自动创建: {target_dir}",
                        "filename": filename
                    }
            
            # 写入HTML内容
            target_path.write_text(html_content, encoding='utf-8')
            
            # 生成访问URL
            access_url = self.generate_url(filename)
            
            result = {
                "success": True,
                "filename": filename,
                "file_path": str(target_path),
                "url": access_url,
                "file_size": target_path.stat().st_size,
                "upload_time": datetime.now().isoformat()
            }
            
            self.logger.info(f"HTML报告上传成功: {access_url}")
            return result
            
        except Exception as e:
            self.logger.error(f"HTML报告上传失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "filename": filename or "未知"
            }
    
    def generate_filename(self, 
                         prefix: str = "report",
                         extension: str = "html") -> str:
        """生成带时间戳的文件名
        
        Args:
            prefix: 文件名前缀
            extension: 文件扩展名
            
        Returns:
            str: 生成的文件名
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 处理包含路径的前缀
        if '/' in prefix:
            path_parts = prefix.split('/')
            filename_prefix = path_parts[-1]
            dir_path = '/'.join(path_parts[:-1])
            return f"{dir_path}/{filename_prefix}_{timestamp}.{extension}"
        else:
            return f"{prefix}_{timestamp}.{extension}"
    
    def generate_url(self, filename: str) -> str:
        """生成文件的访问URL
        
        Args:
            filename: 文件名（可能包含子目录）
            
        Returns:
            str: 完整的访问URL
        """
        # 移除开头的斜杠
        clean_filename = filename.lstrip('/')
        return f"{self.base_url}/{clean_filename}"
    
    def _ensure_directory_exists(self, directory: Path) -> None:
        """确保目录存在
        
        Args:
            directory: 目录路径
            
        Raises:
            PermissionError: 权限不足
            OSError: 其他文件系统错误
        """
        try:
            directory.mkdir(parents=True, exist_ok=True)
            self.logger.debug(f"目录已创建或已存在: {directory}")
        except Exception as e:
            self.logger.error(f"创建目录失败: {directory}, 错误: {str(e)}")
            raise
    
    def get_upload_stats(self) -> Dict[str, Any]:
        """获取上传统计信息
        
        Returns:
            Dict: 统计信息
        """
        try:
            if not self.upload_path.exists():
                return {
                    "total_files": 0,
                    "total_size": 0,
                    "upload_path": str(self.upload_path),
                    "path_exists": False
                }
            
            total_files = 0
            total_size = 0
            
            for file_path in self.upload_path.rglob("*.html"):
                if file_path.is_file():
                    total_files += 1
                    total_size += file_path.stat().st_size
            
            return {
                "total_files": total_files,
                "total_size": total_size,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "upload_path": str(self.upload_path),
                "path_exists": True
            }
            
        except Exception as e:
            self.logger.error(f"获取上传统计失败: {str(e)}")
            return {
                "error": str(e),
                "upload_path": str(self.upload_path)
            }
    
    def cleanup_old_reports(self, days_to_keep: int = 30) -> Dict[str, Any]:
        """清理旧的报告文件
        
        Args:
            days_to_keep: 保留天数
            
        Returns:
            Dict: 清理结果
        """
        try:
            if not self.upload_path.exists():
                return {
                    "success": False,
                    "error": "上传目录不存在",
                    "deleted_files": 0,
                    "freed_space": 0
                }
            
            cutoff_time = datetime.now().timestamp() - (days_to_keep * 24 * 3600)
            deleted_files = 0
            freed_space = 0
            
            for file_path in self.upload_path.rglob("*.html"):
                if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                    file_size = file_path.stat().st_size
                    file_path.unlink()
                    deleted_files += 1
                    freed_space += file_size
                    self.logger.debug(f"删除旧报告文件: {file_path}")
            
            # 清理空目录
            for dir_path in self.upload_path.rglob("*"):
                if dir_path.is_dir() and not any(dir_path.iterdir()):
                    try:
                        dir_path.rmdir()
                        self.logger.debug(f"删除空目录: {dir_path}")
                    except OSError:
                        pass  # 目录可能不为空或无权限删除
            
            result = {
                "success": True,
                "deleted_files": deleted_files,
                "freed_space": freed_space,
                "freed_space_mb": round(freed_space / (1024 * 1024), 2),
                "days_to_keep": days_to_keep
            }
            
            self.logger.info(f"清理完成，删除 {deleted_files} 个文件，释放 {result['freed_space_mb']} MB 空间")
            return result
            
        except Exception as e:
            self.logger.error(f"清理旧报告失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "deleted_files": 0,
                "freed_space": 0
            }


class URLGenerator:
    """URL生成器工具类"""
    
    @staticmethod
    def generate_report_url(base_url: str, 
                           relative_path: str,
                           query_params: Optional[Dict[str, str]] = None) -> str:
        """生成报告访问URL
        
        Args:
            base_url: 基础URL
            relative_path: 相对路径
            query_params: 查询参数
            
        Returns:
            str: 完整URL
        """
        url = urljoin(base_url.rstrip('/') + '/', relative_path.lstrip('/'))
        
        if query_params:
            query_string = '&'.join([f"{k}={v}" for k, v in query_params.items()])
            url += f"?{query_string}"
        
        return url
    
    @staticmethod
    def generate_download_url(base_url: str, 
                             relative_path: str,
                             filename: Optional[str] = None) -> str:
        """生成下载URL
        
        Args:
            base_url: 基础URL
            relative_path: 相对路径
            filename: 下载文件名
            
        Returns:
            str: 下载URL
        """
        query_params = {"download": "1"}
        if filename:
            query_params["filename"] = filename
        
        return URLGenerator.generate_report_url(base_url, relative_path, query_params)
    
    @staticmethod
    def generate_preview_url(base_url: str, 
                            relative_path: str) -> str:
        """生成预览URL
        
        Args:
            base_url: 基础URL
            relative_path: 相对路径
            
        Returns:
            str: 预览URL
        """
        return URLGenerator.generate_report_url(base_url, relative_path, {"preview": "1"}) 