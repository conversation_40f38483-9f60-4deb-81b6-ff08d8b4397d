# 过滤测试策略信号需求规格

**创建日期**: 2025-05-27  
**需求提出者**: 用户  
**需求分析者**: AI助手  
**功能模块**: APIs - 信号记录列表API  
**版本**: 0.1.0  

## 1. 需求概述

在现有的获取信号记录列表API (`/api/v1/signals`) 中，需要添加过滤功能，自动排除`trigger_conditions.strategy_name`字段值为"测试"的信号记录。

## 2. 详细需求规格

### 2.1 功能需求

**FR-001**: 自动过滤测试策略信号
- **描述**: API应自动过滤掉所有`trigger_conditions.strategy_name`等于"测试"的信号记录
- **优先级**: 高
- **业务价值**: 避免测试数据污染生产环境的信号列表，提高数据质量

### 2.2 技术需求

**TR-001**: MongoDB查询过滤条件
- **描述**: 在MongoDB查询中添加过滤条件：`{"trigger_conditions.strategy_name": {"$ne": "测试"}}`
- **实现位置**: `api/v1/signal_api.py` 的 `list_signals` 函数中的 `query_filter` 构建逻辑

**TR-002**: 向后兼容性
- **描述**: 确保现有的所有查询参数（skip, limit, signal_type, period, tz）继续正常工作
- **验证**: 所有现有的API调用应返回相同结果（除了被过滤的测试策略信号）

### 2.3 数据要求

**DR-001**: 字段存在性处理
- **描述**: 正确处理以下情况：
  - `trigger_conditions`字段不存在的记录
  - `trigger_conditions.strategy_name`字段不存在的记录
  - `trigger_conditions.strategy_name`字段值为null的记录
- **期望行为**: 这些记录应该被保留（不被过滤掉）

**DR-002**: 大小写敏感性
- **描述**: 过滤条件应严格匹配"测试"（区分大小写）
- **示例**: "测试"会被过滤，但"Test"、"测试1"、"测试策略"等不会被过滤

## 3. 验收标准

### 3.1 功能验收标准

**AC-001**: 过滤功能正确性
- **Given**: 数据库中存在多条信号记录，其中部分记录的`trigger_conditions.strategy_name`为"测试"
- **When**: 调用`/api/v1/signals`API
- **Then**: 返回的结果中不包含任何`trigger_conditions.strategy_name`为"测试"的记录

**AC-002**: 现有功能保持不变
- **Given**: 使用现有的查询参数（signal_type, period等）
- **When**: 调用API
- **Then**: 除了测试策略信号被过滤外，其他行为与之前完全一致

**AC-003**: 边界情况处理
- **Given**: 数据库中存在`trigger_conditions`或`strategy_name`字段缺失的记录
- **When**: 调用API
- **Then**: 这些记录正常返回，不被过滤

### 3.2 性能验收标准

**AC-004**: 查询性能
- **Given**: 大量信号数据
- **When**: 添加过滤条件后调用API
- **Then**: 响应时间不应显著增加（增幅<10%）

## 4. 非功能性需求

### 4.1 性能要求
- API响应时间不应因添加过滤条件而显著增加
- 支持现有的分页机制（skip/limit）

### 4.2 可维护性要求
- 过滤逻辑应清晰易懂
- 代码注释应说明过滤的目的和逻辑

### 4.3 可扩展性要求
- 过滤逻辑应易于扩展，以便将来可能需要过滤其他策略名称

## 5. 约束条件

### 5.1 技术约束
- 必须使用MongoDB的原生查询语法
- 不能破坏现有的API接口定义
- 必须保持现有的错误处理机制

### 5.2 业务约束
- 过滤应该是静默的，不需要在API响应中说明过滤了多少条记录
- 不需要提供开关来控制是否启用过滤

## 6. 测试场景

### 6.1 正常场景
1. 查询包含测试策略和非测试策略信号的数据集
2. 查询只包含测试策略信号的数据集
3. 查询不包含任何测试策略信号的数据集

### 6.2 边界场景
1. `trigger_conditions`字段为空对象的记录
2. `trigger_conditions`字段不存在的记录
3. `strategy_name`字段值为null的记录
4. `strategy_name`字段值为空字符串的记录

### 6.3 组合场景
1. 结合signal_type过滤的查询
2. 结合时间范围过滤的查询
3. 结合分页参数的查询 