from typing import Optional
from datetime import datetime
from beanie import Link
from models.kol_strategy_score import KOLStrategyScore
from models.kol_wallet import KOLWallet
from dao.kol_wallet_dao import KOLWalletDAO
import logging

logger = logging.getLogger(__name__)

class KOLStrategyScoreDAO:
    """KOL策略分数数据访问对象"""
    
    def __init__(self):
        self.kol_wallet_dao = KOLWalletDAO()
    
    async def get_by_kol_and_strategy(self, kol_wallet_address: str, strategy_name: str) -> Optional[KOLStrategyScore]:
        """
        根据KOL钱包地址和策略名称获取策略分数记录
        
        Args:
            kol_wallet_address: KOL钱包地址
            strategy_name: 策略名称
            
        Returns:
            KOLStrategyScore对象或None
        """
        try:
            # 获取KOL钱包对象
            kol_wallet = await self.kol_wallet_dao.find_by_wallet_address(kol_wallet_address)
            if not kol_wallet:
                logger.warning(f"未找到KOL钱包: {kol_wallet_address}")
                return None
            
            # 使用KOL钱包Link查询
            score_record = await KOLStrategyScore.find_one(
                KOLStrategyScore.kol_wallet._id == kol_wallet.id,
                KOLStrategyScore.strategy_name == strategy_name
            )
            
            return score_record
            
        except Exception as e:
            logger.error(f"查询KOL策略分数记录失败: kol={kol_wallet_address}, strategy={strategy_name}, error={str(e)}")
            return None
    
    async def get_or_create_score(self, kol_wallet_address: str, strategy_name: str) -> Optional[KOLStrategyScore]:
        """
        获取或创建KOL策略分数记录
        
        Args:
            kol_wallet_address: KOL钱包地址
            strategy_name: 策略名称
            
        Returns:
            KOLStrategyScore对象或None（如果KOL钱包不存在）
        """
        try:
            # 首先尝试获取现有记录
            existing_record = await self.get_by_kol_and_strategy(kol_wallet_address, strategy_name)
            if existing_record:
                return existing_record
            
            # 如果不存在，创建新记录
            kol_wallet = await self.kol_wallet_dao.find_by_wallet_address(kol_wallet_address)
            if not kol_wallet:
                logger.error(f"无法创建KOL策略分数记录：KOL钱包不存在: {kol_wallet_address}")
                return None
            
            new_record = KOLStrategyScore(
                kol_wallet=Link(kol_wallet, KOLWallet),
                strategy_name=strategy_name,
                total_positive_score=0.0,
                total_negative_score=0.0,
                last_scored_at=None
            )
            
            await new_record.save()
            logger.info(f"创建新的KOL策略分数记录: kol={kol_wallet_address}, strategy={strategy_name}")
            return new_record
            
        except Exception as e:
            logger.error(f"获取或创建KOL策略分数记录失败: kol={kol_wallet_address}, strategy={strategy_name}, error={str(e)}")
            return None
    
    async def update_score(
        self, 
        kol_wallet_address: str, 
        strategy_name: str, 
        positive_score_change: float = 0.0, 
        negative_score_change: float = 0.0
    ) -> bool:
        """
        原子性地更新KOL策略分数
        
        Args:
            kol_wallet_address: KOL钱包地址
            strategy_name: 策略名称
            positive_score_change: 加分变化量
            negative_score_change: 扣分变化量
            
        Returns:
            更新是否成功
        """
        try:
            # 确保记录存在
            score_record = await self.get_or_create_score(kol_wallet_address, strategy_name)
            if not score_record:
                logger.error(f"无法更新分数：记录创建失败: kol={kol_wallet_address}, strategy={strategy_name}")
                return False
            
            # 原子性更新，使用$inc操作符
            current_time = datetime.utcnow()
            update_query = {
                "$inc": {
                    "total_positive_score": positive_score_change,
                    "total_negative_score": negative_score_change
                },
                "$set": {
                    "last_scored_at": current_time,
                    "updated_at": current_time
                }
            }
            
            # 使用find_one_and_update保证原子性
            result = await KOLStrategyScore.find_one(
                KOLStrategyScore.id == score_record.id
            ).update(update_query)
            
            if result.acknowledged:
                logger.info(f"成功更新KOL策略分数: kol={kol_wallet_address}, strategy={strategy_name}, "
                           f"positive_change={positive_score_change}, negative_change={negative_score_change}")
                return True
            else:
                logger.error(f"更新KOL策略分数失败: kol={kol_wallet_address}, strategy={strategy_name}")
                return False
                
        except Exception as e:
            logger.error(f"更新KOL策略分数时发生异常: kol={kol_wallet_address}, strategy={strategy_name}, error={str(e)}")
            return False
    
    async def get_top_kols_by_strategy(self, strategy_name: str, limit: int = 10) -> list[KOLStrategyScore]:
        """
        获取指定策略下得分最高的KOL列表
        
        Args:
            strategy_name: 策略名称
            limit: 返回记录数量限制
            
        Returns:
            按净分数排序的KOLStrategyScore列表
        """
        try:
            # 使用aggregation计算净分数并排序
            pipeline = [
                {"$match": {"strategy_name": strategy_name}},
                {"$addFields": {
                    "net_score": {"$add": ["$total_positive_score", "$total_negative_score"]}
                }},
                {"$sort": {"net_score": -1}},
                {"$limit": limit}
            ]
            
            results = await KOLStrategyScore.aggregate(pipeline).to_list()
            
            # 将聚合结果转换回KOLStrategyScore对象
            score_records = []
            for result in results:
                score_record = await KOLStrategyScore.find_one(KOLStrategyScore.id == result["_id"])
                if score_record:
                    score_records.append(score_record)
            
            return score_records
            
        except Exception as e:
            logger.error(f"获取策略Top KOL失败: strategy={strategy_name}, error={str(e)}")
            return []
    
    async def get_kol_all_strategies(self, kol_wallet_address: str) -> list[KOLStrategyScore]:
        """
        获取指定KOL在所有策略下的分数记录
        
        Args:
            kol_wallet_address: KOL钱包地址
            
        Returns:
            该KOL的所有策略分数记录列表
        """
        try:
            kol_wallet = await self.kol_wallet_dao.find_by_wallet_address(kol_wallet_address)
            if not kol_wallet:
                logger.warning(f"未找到KOL钱包: {kol_wallet_address}")
                return []
            
            records = await KOLStrategyScore.find(
                KOLStrategyScore.kol_wallet._id == kol_wallet.id
            ).sort("-last_scored_at").to_list()
            
            return records
            
        except Exception as e:
            logger.error(f"获取KOL所有策略分数失败: kol={kol_wallet_address}, error={str(e)}")
            return [] 