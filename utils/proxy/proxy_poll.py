from abc import ABC, abstractmethod
import os
import aiohttp
import json

from dataclasses import dataclass


@dataclass
class ProxyInfo:
    """
    代理信息
    """
    proxy_url: str
    proxy_user: str = ""
    proxy_pass: str = ""


class ProxyPollBasic(ABC):
    
    @abstractmethod
    async def get_proxy(self) -> ProxyInfo:
        pass
    

class FreeProxy(ProxyPollBasic):
    """
    免费代理
    """
    @abstractmethod
    async def get_proxy(self) -> ProxyInfo:
        pass
    
    
class PaidProxy(ProxyPollBasic):
    """
    付费代理
    """
    @abstractmethod
    async def get_proxy(self) -> ProxyInfo:
        pass


class ProxyPoll(FreeProxy):
    """
    开源IP代理池

    https://github.com/jhao104/proxy_pool
    """

    def __init__(self, server: str):
        """
        :param server: 代理服务器地址, 格式为 host:port
        """
        self.server = f"http://{server}"

    async def get_proxy(self) -> ProxyInfo:
        """
        从代理服务器获取代理IP
        
        Returns:
            str: 代理IP，格式为 host:port
        """
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(f"{self.server}/get/?type=https&anonymous=high_anonymous") as response:
                    if response.status == 200:
                        data = await response.json()
                        if not data.get("proxy", ""):
                            return None
                        return ProxyInfo(
                            proxy_url=data.get("proxy", ""),
                        )
                    else:
                        return None
            except aiohttp.ClientError:
                return None
            

class QgNetProxyPoll(PaidProxy):
    """
    QgNet代理池 青果网络 www.qg.net
    """
    
    async def get_proxy(self) -> str:
        """
        从代理服务器获取代理IP

        Returns:
            str: 代理IP，格式为 host:port
        """
        qg_proxy_url = os.getenv("QGNET_PROXY_URL")
        qg_proxy_user = os.getenv("QGNET_PROXY_USER")
        qg_proxy_pass = os.getenv("QGNET_PROXY_PASS")
        return ProxyInfo(
            proxy_url=qg_proxy_url,
            proxy_user=qg_proxy_user,
            proxy_pass=qg_proxy_pass,
        )


if __name__ == '__main__':
    import asyncio
    async def test():
        poll = ProxyPoll("localhost:5010")
        proxy = await poll.get_proxy()
        print(proxy)
    asyncio.run(test())
