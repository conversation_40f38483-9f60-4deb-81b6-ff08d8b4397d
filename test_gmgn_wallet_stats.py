#!/usr/bin/env python3
"""
测试GMGN钱包统计爬虫修复

测试curl命令可以工作但代码不行的问题修复情况
"""

import asyncio
import logging
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.spiders.smart_money.gmgn_wallet_stats_spider import GmgnWalletStatsSpider


async def test_wallet_stats():
    """测试钱包统计数据获取"""
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
        ]
    )
    
    # 测试钱包地址（与curl命令中的一致）
    wallet_address = "AHnQjWa4jxYNxzfJantHDpcUKAE9pzVSGGPLhthPxCSW"
    
    spider = GmgnWalletStatsSpider()
    
    try:
        # 设置爬虫
        await spider.setup()
        
        print(f"开始测试钱包地址: {wallet_address}")
        
        # 测试获取钱包统计数据
        result = await spider.get_wallet_stats(wallet_address, period="all")
        
        if result:
            print("✅ 成功获取钱包统计数据！")
            print(f"钱包地址: {result.wallet_address}")
            print(f"时间窗口: {result.period}")
            print(f"胜率: {result.winrate}")
            print(f"PnL: {result.pnl}")
            print(f"买入次数: {result.buy}")
            print(f"卖出次数: {result.sell}")
            print(f"做多胜率: {result.winrate_long}")
            print(f"做空胜率: {result.winrate_short}")
        else:
            print("❌ 获取钱包统计数据失败")
            
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 关闭会话
        await spider.close()


if __name__ == "__main__":
    asyncio.run(test_wallet_stats()) 