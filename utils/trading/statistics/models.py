"""
交易统计分析数据模型

定义用于统计分析的业务数据模型，包括交易对、统计结果、配置等。
这些模型用于内存中的数据处理和传递，不保存到数据库。
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum


class ReportFormat(str, Enum):
    """报告格式枚举"""
    HTML = "html"
    JSON = "json"


class TradePair(BaseModel):
    """交易对数据模型"""
    signal_id: str = Field(..., description="信号ID")
    strategy_name: str = Field(..., description="策略名称")
    token_address: str = Field(..., description="Token地址")
    buy_record_id: str = Field(..., description="买入记录ID")
    sell_record_id: str = Field(..., description="卖出记录ID")
    buy_amount_sol: float = Field(..., description="买入金额（SOL）")
    sell_amount_sol: float = Field(..., description="卖出金额（SOL）")
    profit_rate: float = Field(..., description="盈利率(%)")
    profit_amount: float = Field(..., description="盈亏金额（SOL）")
    is_profitable: bool = Field(..., description="是否盈利")
    buy_time: datetime = Field(..., description="买入时间")
    sell_time: datetime = Field(..., description="卖出时间")
    holding_duration: float = Field(..., description="持仓时长（小时）")


class OverallStats(BaseModel):
    """总体统计模型"""
    total_trades: int = Field(default=0, description="总交易数")
    total_profit_rate: float = Field(default=0.0, description="总盈利率(%)")
    total_win_rate: float = Field(default=0.0, description="总胜率(%)")
    avg_profit_rate: float = Field(default=0.0, description="平均盈利率(%)")
    overall_return_rate: float = Field(default=0.0, description="整体收益率(%)")
    total_profit_amount: float = Field(default=0.0, description="总盈亏金额（SOL）")
    profitable_trades: int = Field(default=0, description="盈利交易数")
    loss_trades: int = Field(default=0, description="亏损交易数")
    avg_holding_duration: float = Field(default=0.0, description="平均持仓时长（小时）")
    total_buy_amount: float = Field(default=0.0, description="总买入金额（SOL）")
    total_sell_amount: float = Field(default=0.0, description="总卖出金额（SOL）")
    max_single_profit: float = Field(default=0.0, description="最大单笔盈利（SOL）")
    max_single_loss: float = Field(default=0.0, description="最大单笔亏损（SOL）")


class TokenStats(BaseModel):
    """Token统计模型（简化版，不包含最大盈利亏损）"""
    token_address: str = Field(..., description="Token地址")
    token_symbol: Optional[str] = Field(default=None, description="Token符号")
    trade_count: int = Field(default=0, description="交易次数")
    win_rate: float = Field(default=0.0, description="胜率(%)")
    avg_profit_rate: float = Field(default=0.0, description="平均盈利率(%)")
    total_profit_amount: float = Field(default=0.0, description="总盈亏金额（SOL）")
    profitable_trades: int = Field(default=0, description="盈利交易数")
    loss_trades: int = Field(default=0, description="亏损交易数")
    total_buy_amount: float = Field(default=0.0, description="总买入金额（SOL）")
    total_sell_amount: float = Field(default=0.0, description="总卖出金额（SOL）")


class StrategyStats(BaseModel):
    """策略统计模型（包含最大盈利亏损）"""
    strategy_name: str = Field(..., description="策略名称")
    trade_count: int = Field(default=0, description="交易次数")
    win_rate: float = Field(default=0.0, description="胜率(%)")
    avg_profit_rate: float = Field(default=0.0, description="平均盈利率(%)")
    total_profit_amount: float = Field(default=0.0, description="总盈亏金额（SOL）")
    profitable_trades: int = Field(default=0, description="盈利交易数")
    loss_trades: int = Field(default=0, description="亏损交易数")
    total_buy_amount: float = Field(default=0.0, description="总买入金额（SOL）")
    total_sell_amount: float = Field(default=0.0, description="总卖出金额（SOL）")
    max_single_profit: float = Field(default=0.0, description="最大单笔盈利（SOL）")
    max_single_loss: float = Field(default=0.0, description="最大单笔亏损（SOL）")


class ProfitRanking(BaseModel):
    """盈利排行"""
    signal_id: str = Field(..., description="信号ID")
    strategy_name: str = Field(..., description="策略名称")
    token_address: str = Field(..., description="Token地址")
    profit_amount: float = Field(..., description="盈利金额（SOL）")
    profit_rate: float = Field(..., description="盈利率(%)")
    buy_time: datetime = Field(..., description="买入时间")
    sell_time: datetime = Field(..., description="卖出时间")


class LossRanking(BaseModel):
    """亏损排行"""
    signal_id: str = Field(..., description="信号ID")
    strategy_name: str = Field(..., description="策略名称")
    token_address: str = Field(..., description="Token地址")
    loss_amount: float = Field(..., description="亏损金额（SOL，正数显示）")
    loss_rate: float = Field(..., description="亏损率(%，正数显示)")
    buy_time: datetime = Field(..., description="买入时间")
    sell_time: datetime = Field(..., description="卖出时间")


class StatisticsResult(BaseModel):
    """完整统计结果模型"""
    overall_stats: OverallStats = Field(..., description="总体统计")
    token_stats: List[TokenStats] = Field(default_factory=list, description="Token统计列表")
    strategy_stats: List[StrategyStats] = Field(default_factory=list, description="策略统计列表")
    trade_pairs: List[TradePair] = Field(default_factory=list, description="交易对列表")
    profit_rankings: List[ProfitRanking] = Field(default_factory=list, description="盈利排行")
    loss_rankings: List[LossRanking] = Field(default_factory=list, description="亏损排行")
    generation_time: datetime = Field(..., description="生成时间")
    data_range: Dict[str, Any] = Field(default_factory=dict, description="数据范围信息")


class StatisticsConfig(BaseModel):
    """统计配置模型"""
    start_date: Optional[datetime] = Field(default=None, description="开始日期")
    end_date: Optional[datetime] = Field(default=None, description="结束日期")
    strategy_filter: Optional[List[str]] = Field(default=None, description="策略过滤列表")
    token_filter: Optional[List[str]] = Field(default=None, description="Token过滤列表")
    min_trade_amount: Optional[float] = Field(default=None, description="最小交易金额过滤")
    output_path: Optional[str] = Field(default=None, description="输出文件路径")
    report_format: ReportFormat = Field(default=ReportFormat.HTML, description="报告格式")
    include_charts: bool = Field(default=True, description="是否包含图表")
    include_tables: bool = Field(default=True, description="是否包含表格")
    chart_style: str = Field(default="plotly", description="图表样式")
    profit_ranking_limit: int = Field(default=50, description="盈利排行榜数量限制")
    loss_ranking_limit: int = Field(default=50, description="亏损排行榜数量限制")
    token_stats_limit: int = Field(default=20, description="Token统计显示数量限制")
    
    # Token链接配置
    token_link_base_url: str = Field(
        default="https://gmgn.ai/sol/token/", 
        description="Token链接基础URL，Token地址将附加到此URL后"
    )
    enable_token_links: bool = Field(
        default=True, 
        description="是否启用Token地址链接功能"
    ) 