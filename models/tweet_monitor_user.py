


from typing import Optional
from beanie import Document, Indexed
from pydantic import Field


class TweetMonitorUser(Document):
    """推文监控用户模型"""
    
    username: Indexed(str) = Field(description="用户名,如 elonmusk", unique=True)
    description: Optional[str] = Field(default=None, description="用户简介")
    
    class Settings:
        name = "tweet_monitor_users"
        
    class Config:
        json_schema_extra = {
            "example": {
                "username": "elonmusk",
                "description": "<PERSON><PERSON> is the CEO of SpaceX and Tesla."
            }
        }
        