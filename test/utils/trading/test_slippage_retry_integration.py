# -*- coding: utf-8 -*-
"""
滑点递增重试功能集成测试
测试端到端的滑点重试流程，验证各组件协作正确性
"""

import unittest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime
from typing import Optional, Dict, Any

from models.config import TradingParams, SingleKolStrategyConfig
from models.trade_record import TradeStatus as RecordTradeStatus
from models.trade_execution import TradeExecutionResult, ChannelAttemptResult
from utils.trading.slippage_retry import (
    SlippageCalculator, RetryDecisionEngine, ParameterMerger, 
    RetryContext, RetryDelayCalculator
)
from models.slippage_retry import SlippageAdjustmentRecord, SlippageAdjustmentReason, SlippageRetryConfig
from models.config import RetryDelayStrategy
from utils.trading.solana.trade_interface import TradeInterface, TradeResult, TradeStatus, TradeType


class MockTradeInterface(TradeInterface):
    """模拟交易接口，用于集成测试"""
    
    def __init__(self, interface_name: str = "mock"):
        self.interface_name = interface_name
        self.call_count = 0
        self.should_fail_until = 0  # 前N次调用失败
        self.error_type = "slippage"  # "slippage" 或 "other"
        self.success_tx_hash = "mock_tx_hash_12345"
        
    async def execute_trade(
        self,
        trade_type: TradeType,
        input_token_address: str,
        output_token_address: str,
        amount_input_token: float,
        wallet_private_key_b58: str,
        wallet_address: str,
        strategy_snapshot: dict,
        signal_id: Optional[str] = None,
        trade_record_id: Optional[str] = None
    ) -> TradeResult:
        """模拟交易执行"""
        self.call_count += 1
        
        if self.call_count <= self.should_fail_until:
            # 模拟失败
            if self.error_type == "slippage":
                error_msg = "slippage tolerance exceeded"
            else:
                error_msg = "insufficient funds"
                
            return TradeResult(
                status=TradeStatus.FAILED,
                error_message=error_msg
            )
        else:
            # 模拟成功
            return TradeResult(
                status=TradeStatus.SUCCESS,
                tx_hash=self.success_tx_hash
            )
    
    def is_slippage_related_error(self, error_message: str, error_code: Optional[str] = None) -> bool:
        """模拟滑点错误识别"""
        slippage_keywords = ["slippage", "price impact", "insufficient output"]
        return any(keyword in error_message.lower() for keyword in slippage_keywords)
    
    def get_interface_name(self) -> str:
        return self.interface_name


class TestSlippageRetryIntegration(unittest.TestCase):
    """滑点递增重试集成测试类"""
    
    def setUp(self):
        """测试前设置"""
        self.slippage_calculator = SlippageCalculator()
        self.retry_decision_engine = RetryDecisionEngine()
        self.parameter_merger = ParameterMerger()
        
        # 标准滑点重试配置
        self.standard_trading_params = TradingParams(
            default_buy_amount_sol=0.01,
            default_buy_slippage_percentage=1.0,
            default_sell_slippage_percentage=1.0,
            enable_slippage_retry=True,
            slippage_increment_percentage=0.5,
            max_slippage_percentage=5.0,
            retry_delay_seconds=0.1,  # 测试用短间隔
            retry_delay_strategy=RetryDelayStrategy.FIXED,
            max_retry_delay_seconds=1.0
        )
        
        # 禁用滑点重试的配置
        self.disabled_trading_params = TradingParams(
            default_buy_amount_sol=0.01,
            default_buy_slippage_percentage=1.0,
            default_sell_slippage_percentage=1.0,
            enable_slippage_retry=False,
            slippage_increment_percentage=0.5,
            max_slippage_percentage=5.0
        )
    
    @patch('utils.trading.slippage_retry.retry_context.SlippageAdjustmentRecord')
    def test_successful_slippage_retry_flow(self, mock_slippage_adjustment):
        """测试成功的滑点递增重试流程"""
        # 设置mock返回值
        mock_adjustment_instance = Mock()
        mock_adjustment_instance.adjustment_summary = "BUY 滑点从 1.0% 调整至 1.5% (第1次重试)"
        mock_slippage_adjustment.return_value = mock_adjustment_instance
        # 设置mock接口：第一次失败（滑点错误），第二次成功
        mock_interface = MockTradeInterface("test_interface")
        mock_interface.should_fail_until = 1
        mock_interface.error_type = "slippage"
        
        # 初始化重试上下文
        config = SlippageRetryConfig(
            enabled=True,
            increment_percentage=0.5,
            max_slippage_percentage=5.0,
            retry_delay_seconds=0.1
        )
        retry_context = RetryContext(
            trade_type="buy",
            initial_slippage=1.0,
            config=config,
            max_retries=3
        )
        
        max_retries = 3
        trade_type = "buy"
        
        # 模拟第一次尝试失败
        result1 = asyncio.run(mock_interface.execute_trade(
            trade_type=TradeType.BUY,
            input_token_address="token_in",
            output_token_address="token_out",
            amount_input_token=0.01,
            wallet_private_key_b58="test_key",
            wallet_address="test_address",
            strategy_snapshot={"buy_slippage_percentage": retry_context.current_slippage}
        ))
        
        self.assertEqual(result1.status, TradeStatus.FAILED)
        self.assertTrue(mock_interface.is_slippage_related_error(result1.error_message))
        
        # 检查是否应该继续重试
        should_continue = self.retry_decision_engine.should_continue_retry(
            retry_count=1,
            max_retries=max_retries,
            error_message=result1.error_message
        )
        self.assertTrue(should_continue)
        
        # 检查是否应该调整滑点
        should_adjust = self.retry_decision_engine.should_increase_slippage(
            error_message=result1.error_message,
            config=retry_context.config,
            current_slippage=retry_context.current_slippage
        )
        
        self.assertTrue(should_adjust)
        
        # 计算新的滑点值
        new_slippage, exceeded = self.slippage_calculator.calculate_next_slippage(
            current_slippage=retry_context.current_slippage,
            config=retry_context.config
        )
        self.assertEqual(new_slippage, 1.5)
        
        # 记录滑点调整
        adjustment = retry_context.record_slippage_adjustment(
            new_slippage=new_slippage,
            reason=SlippageAdjustmentReason.SLIPPAGE_ERROR,
            error_message=result1.error_message
        )
        
        # 模拟第二次尝试成功
        result2 = asyncio.run(mock_interface.execute_trade(
            trade_type=TradeType.BUY,
            input_token_address="token_in",
            output_token_address="token_out",
            amount_input_token=0.01,
            wallet_private_key_b58="test_key",
            wallet_address="test_address",
            strategy_snapshot={"buy_slippage_percentage": retry_context.current_slippage}
        ))
        
        self.assertEqual(result2.status, TradeStatus.SUCCESS)
        self.assertEqual(result2.tx_hash, "mock_tx_hash_12345")
        
        # 验证重试上下文状态
        self.assertEqual(retry_context.trade_type, "buy")
        self.assertEqual(retry_context.current_slippage, 1.5)
        self.assertEqual(len(retry_context.adjustment_history), 1)
        
        # 验证调整摘要
        summary = retry_context.get_summary()
        self.assertEqual(summary['total_adjustments'], 1)
        self.assertEqual(summary['total_slippage_increase'], 0.5)
    
    @patch('utils.trading.slippage_retry.retry_context.SlippageAdjustmentRecord')
    def test_slippage_limit_reached_continue_retry(self, mock_slippage_adjustment):
        """测试滑点达到上限后继续重试"""
        # 设置mock返回值
        mock_adjustment_instance = Mock()
        mock_adjustment_instance.adjustment_summary = "BUY 滑点从 4.8% 调整至 5.0% (第1次重试)"
        mock_slippage_adjustment.return_value = mock_adjustment_instance
        # 设置接近上限的初始滑点
        trading_params = TradingParams(
            enable_slippage_retry=True,
            slippage_increment_percentage=0.5,
            max_slippage_percentage=5.0
        )
        
        # 初始滑点已接近上限
        config = SlippageRetryConfig(
            enabled=True,
            increment_percentage=0.5,
            max_slippage_percentage=5.0,
            retry_delay_seconds=0.1
        )
        retry_context = RetryContext(
            trade_type="buy",
            initial_slippage=4.8,
            config=config,
            max_retries=3
        )
        
        # 第一次调整：滑点应该到达上限
        should_adjust1 = self.retry_decision_engine.should_increase_slippage(
            error_message="slippage tolerance exceeded",
            config=retry_context.config,
            current_slippage=retry_context.current_slippage
        )
        
        self.assertTrue(should_adjust1)
        
        # 计算新滑点值
        new_slippage1, exceeded1 = self.slippage_calculator.calculate_next_slippage(
            current_slippage=retry_context.current_slippage,
            config=retry_context.config
        )
        self.assertEqual(new_slippage1, 5.0)  # 到达上限
        
        # 更新上下文
        adjustment1 = retry_context.record_slippage_adjustment(
            new_slippage=new_slippage1,
            reason=SlippageAdjustmentReason.SLIPPAGE_ERROR,
            error_message="slippage tolerance exceeded"
        )
        
        # 第二次调整：滑点已达上限，不应再调整
        should_adjust2 = self.retry_decision_engine.should_increase_slippage(
            error_message="slippage tolerance exceeded",
            config=retry_context.config,
            current_slippage=retry_context.current_slippage
        )
        
        self.assertFalse(should_adjust2)  # 已达上限，不应再调整
        
        # 但仍应该继续重试（使用当前滑点）
        should_continue = self.retry_decision_engine.should_continue_retry(
            retry_count=2,
            max_retries=3,
            error_message="slippage tolerance exceeded"
        )
        self.assertTrue(should_continue)
    
    def test_non_slippage_error_no_slippage_adjustment(self):
        """测试非滑点错误不触发滑点调整"""
        mock_interface = MockTradeInterface("test_interface")
        mock_interface.should_fail_until = 2
        mock_interface.error_type = "other"  # 非滑点错误
        
        # 模拟失败
        result = asyncio.run(mock_interface.execute_trade(
            trade_type=TradeType.BUY,
            input_token_address="token_in",
            output_token_address="token_out",
            amount_input_token=0.01,
            wallet_private_key_b58="test_key",
            wallet_address="test_address",
            strategy_snapshot={"buy_slippage_percentage": 1.0}
        ))
        
        self.assertEqual(result.status, TradeStatus.FAILED)
        self.assertFalse(mock_interface.is_slippage_related_error(result.error_message))
        
        # 检查滑点调整决策
        config = SlippageRetryConfig(
            enabled=True,
            increment_percentage=0.5,
            max_slippage_percentage=5.0,
            retry_delay_seconds=0.1
        )
        should_adjust = self.retry_decision_engine.should_increase_slippage(
            error_message=result.error_message,  # 非滑点错误
            config=config,
            current_slippage=1.0
        )
        
        self.assertFalse(should_adjust)  # 非滑点错误不应调整
    
    def test_disabled_slippage_retry_no_adjustment(self):
        """测试禁用滑点重试时不进行滑点调整"""
        # 使用禁用滑点重试的配置
        disabled_config = SlippageRetryConfig(
            enabled=False,  # 禁用
            increment_percentage=0.5,
            max_slippage_percentage=5.0,
            retry_delay_seconds=0.1
        )
        should_adjust = self.retry_decision_engine.should_increase_slippage(
            error_message="slippage tolerance exceeded",  # 即使是滑点错误
            config=disabled_config,
            current_slippage=1.0
        )
        
        self.assertFalse(should_adjust)  # 功能禁用，不应调整
    
    def test_parameter_merger_strategy_override(self):
        """测试参数合并器的策略覆盖功能"""
        # 全局配置
        global_params = TradingParams(
            enable_slippage_retry=False,
            slippage_increment_percentage=0.5,
            max_slippage_percentage=10.0
        )
        
        # 运行时覆盖（直接使用TradingParams字段名）
        strategy_overrides = {
            'enable_slippage_retry': True,
            'slippage_increment_percentage': 1.0,
            'max_slippage_percentage': 8.0
        }
        
        # 合并配置
        merged_params = self.parameter_merger.merge_trading_params_with_slippage_retry(
            global_params=global_params,
            channel_params=None,
            strategy_config=None,
            runtime_overrides=strategy_overrides
        )
        
        # 验证策略覆盖生效
        self.assertTrue(merged_params.enable_slippage_retry)  # 策略覆盖
        self.assertEqual(merged_params.slippage_increment_percentage, 1.0)  # 策略覆盖
        self.assertEqual(merged_params.max_slippage_percentage, 8.0)  # 策略覆盖
    
    def test_sell_trade_slippage_adjustment(self):
        """测试卖出交易的滑点调整"""
        # 配置卖出滑点重试
        sell_config = SlippageRetryConfig(
            enabled=True,
            increment_percentage=0.8,
            max_slippage_percentage=12.0,
            retry_delay_seconds=0.1
        )
        
        # 测试卖出滑点调整
        should_adjust = self.retry_decision_engine.should_increase_slippage(
            error_message="slippage tolerance exceeded",
            config=sell_config,
            current_slippage=2.0
        )
        
        self.assertTrue(should_adjust)
        
        # 计算新滑点
        new_slippage, exceeded = self.slippage_calculator.calculate_next_slippage(
            current_slippage=2.0,
            config=sell_config
        )
        self.assertEqual(new_slippage, 2.8)  # 2.0 + 0.8
    
    def test_retry_delay_calculation_integration(self):
        """测试重试间隔计算集成"""
        delay_calculator = RetryDelayCalculator()
        
        # 测试固定间隔策略
        trading_params = TradingParams(
            retry_delay_seconds=0.5,
            retry_delay_strategy=RetryDelayStrategy.FIXED,
            max_retry_delay_seconds=5.0
        )
        delay = delay_calculator.calculate_delay(
            retry_count=1,
            trading_params=trading_params,
            is_slippage_error=True,
            trade_type="buy"
        )
        self.assertEqual(delay, 0.5)
        
        # 测试线性递增策略
        trading_params_linear = TradingParams(
            retry_delay_seconds=0.5,
            retry_delay_strategy=RetryDelayStrategy.LINEAR,
            max_retry_delay_seconds=5.0
        )
        delay = delay_calculator.calculate_delay(
            retry_count=3,
            trading_params=trading_params_linear,
            is_slippage_error=False,
            trade_type="sell"
        )
        self.assertEqual(delay, 1.5)  # 0.5 * 3
        
        # 测试指数退避策略
        trading_params_exp = TradingParams(
            retry_delay_seconds=0.5,
            retry_delay_strategy=RetryDelayStrategy.EXPONENTIAL,
            max_retry_delay_seconds=5.0
        )
        delay = delay_calculator.calculate_delay(
            retry_count=3,
            trading_params=trading_params_exp,
            is_slippage_error=True,
            trade_type="buy"
        )
        self.assertEqual(delay, 2.0)  # 0.5 * (2^(3-1)) = 0.5 * 4
        
        # 测试最大间隔限制
        trading_params_max = TradingParams(
            retry_delay_seconds=2.0,
            retry_delay_strategy=RetryDelayStrategy.EXPONENTIAL,
            max_retry_delay_seconds=5.0
        )
        delay = delay_calculator.calculate_delay(
            retry_count=5,
            trading_params=trading_params_max,
            is_slippage_error=False,
            trade_type="buy"
        )
        self.assertEqual(delay, 5.0)  # 受限于max_delay
    
    @patch('utils.trading.slippage_retry.retry_context.SlippageAdjustmentRecord')
    def test_multiple_adjustments_tracking(self, mock_slippage_adjustment):
        """测试多次滑点调整的跟踪"""
        # 设置mock返回值
        mock_adjustment_instance1 = Mock()
        mock_adjustment_instance1.adjustment_summary = "BUY 滑点从 1.0% 调整至 1.5% (第1次重试)"
        mock_adjustment_instance2 = Mock()
        mock_adjustment_instance2.adjustment_summary = "BUY 滑点从 1.5% 调整至 2.0% (第2次重试)"
        mock_slippage_adjustment.side_effect = [mock_adjustment_instance1, mock_adjustment_instance2]
        config = SlippageRetryConfig(
            enabled=True,
            increment_percentage=0.5,
            max_slippage_percentage=5.0,
            retry_delay_seconds=0.1
        )
        retry_context = RetryContext(
            trade_type="buy",
            initial_slippage=1.0,
            config=config,
            max_retries=5
        )
        
        # 第一次调整（买入）
        adjustment1 = retry_context.record_slippage_adjustment(
            new_slippage=1.5,
            reason=SlippageAdjustmentReason.SLIPPAGE_ERROR,
            error_message="第一次滑点调整"
        )
        
        # 第二次调整（买入继续）
        adjustment2 = retry_context.record_slippage_adjustment(
            new_slippage=2.0,
            reason=SlippageAdjustmentReason.SLIPPAGE_ERROR,
            error_message="第二次滑点调整"
        )
        
        # 验证状态
        self.assertEqual(retry_context.trade_type, "buy")
        self.assertEqual(retry_context.current_slippage, 2.0)
        self.assertEqual(len(retry_context.adjustment_history), 2)
        
        # 验证摘要
        summary = retry_context.get_summary()
        self.assertEqual(summary['total_adjustments'], 2)
        self.assertEqual(summary['total_slippage_increase'], 1.0)  # 1.0 -> 2.0
    
    def test_config_validation_integration(self):
        """测试配置验证集成"""
        delay_calculator = RetryDelayCalculator()
        
        # 测试有效配置
        valid_config = TradingParams(
            enable_slippage_retry=True,
            slippage_increment_percentage=0.5,
            max_slippage_percentage=10.0,
            retry_delay_seconds=0.5,
            max_retry_delay_seconds=5.0
        )
        
        # 应该能正常创建
        self.assertTrue(valid_config.enable_slippage_retry)
        
        # 测试配置验证
        is_valid = delay_calculator.validate_delay_config(
            trading_params=valid_config,
            meme_coin_market=True
        )
        self.assertTrue(is_valid)
        
        # 测试无效配置
        invalid_config = TradingParams(
            retry_delay_seconds=10.0,
            max_retry_delay_seconds=5.0  # 基础延迟大于最大延迟
        )
        is_valid = delay_calculator.validate_delay_config(
            trading_params=invalid_config,
            meme_coin_market=True
        )
        self.assertFalse(is_valid)


if __name__ == '__main__':
    unittest.main() 