# Jupiter Swap API详细错误处理Bug修复方案

## Bug标识
**Bug ID**: JupiterSwap_DetailedErrorHandling  
**发现日期**: 2025-05-27T11:05:21+08:00  
**报告者**: 用户  
**分析者**: AI Assistant  

## Bug描述
Jupiter swap API返回400 Bad Request错误时，当前的错误处理不够详细，无法获取Jupiter API返回的具体错误原因，导致难以诊断和解决交易失败问题。

**错误现象**：
```
标准重试耗尽: Jupiter API error: Client error '400 Bad Request' for url 'https://quote-api.jup.ag/v6/swap'
```

**期望行为**：
应该能够获取并显示Jupiter API返回的具体错误信息，如参数验证失败、余额不足、流动性不足等详细原因。

## 根源分析
通过代码分析发现：

1. **不一致的错误处理**：
   - `_get_jupiter_quote`方法有详细的HTTPStatusError处理
   - `_get_jupiter_swap_transaction`方法只有简单的`response.raise_for_status()`

2. **缺失的错误信息**：
   - 当前只能看到HTTP状态码（400）
   - 无法获取Jupiter API返回的具体错误详情
   - 难以诊断交易失败的真实原因

3. **影响范围**：
   - 所有通过Jupiter进行的swap交易
   - 调试和问题排查困难

## 修复方案

### 核心思路
在`_get_jupiter_swap_transaction`方法中添加与`_get_jupiter_quote`方法相同的详细错误处理逻辑。

### 具体修改

**文件**: `utils/trading/solana/jupiter_trade_service.py`  
**方法**: `_get_jupiter_swap_transaction`  

**修改前**:
```python
response = await self.http_client.post(swap_url, json=swap_data)
response.raise_for_status()

swap_response = response.json()
```

**修改后**:
```python
try:
    response = await self.http_client.post(swap_url, json=swap_data)
    response.raise_for_status()
    
    swap_response = response.json()
    
except httpx.HTTPStatusError as e:
    # 尝试解析错误响应的详细信息
    error_detail = "Unknown error"
    try:
        if e.response.text:
            error_response = e.response.json()
            error_detail = error_response.get('error', error_response.get('message', str(error_response)))
            logger.error(f"Jupiter swap API error details: {error_detail}")
    except:
        error_detail = e.response.text or "No error details available"
        
    raise ValueError(f"Jupiter swap API error ({e.response.status_code}): {error_detail}")
```

### 测试用例设计

1. **模拟400错误响应测试**：
   - 模拟Jupiter API返回400错误
   - 验证能够正确解析和记录错误详情

2. **错误信息格式测试**：
   - 测试不同格式的错误响应（JSON、纯文本）
   - 验证错误处理的健壮性

3. **向后兼容性测试**：
   - 确保正常的swap请求不受影响
   - 验证成功场景的行为不变

## 预期效果

修复后，当Jupiter swap API返回400错误时，我们将能够看到类似以下的详细错误信息：

```
Jupiter swap API error (400): Insufficient balance for swap
```

或

```
Jupiter swap API error (400): Invalid slippage parameter: must be between 1 and 5000 bps
```

这将大大提高问题诊断和解决的效率。

## 风险评估
- **低风险**：只是改进错误处理，不影响正常功能
- **向后兼容**：完全向后兼容，不破坏现有行为
- **性能影响**：无性能影响，只在错误情况下执行额外逻辑

## 方案提出者
AI Assistant

## 方案提出日期
2025-05-27T11:05:21+08:00 