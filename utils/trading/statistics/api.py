#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易统计分析API接口

提供简化的API接口，用于Web应用或其他系统集成
"""

import logging
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any

from utils.trading.statistics.trade_statistics_analyzer import TradeStatisticsAnalyzer
from utils.trading.statistics.models import StatisticsConfig, StatisticsResult, ReportFormat
from dao.trade_record_dao import TradeRecordDAO
from utils.trading.trade_record_verification_updater import TradeRecordVerificationUpdater

logger = logging.getLogger(__name__)


class TradeStatisticsAPI:
    """
    交易统计分析API类
    
    提供简化的接口用于集成到其他系统中
    """
    
    def __init__(self, 
                 trade_record_dao: Optional[TradeRecordDAO] = None,
                 verification_updater: Optional[TradeRecordVerificationUpdater] = None):
        """
        初始化API
        
        Args:
            trade_record_dao: 交易记录DAO，如果为None则延迟初始化
            verification_updater: 验证更新器，如果为None则延迟初始化
        """
        self._trade_record_dao = trade_record_dao
        self._verification_updater = verification_updater
        self._analyzer = None
    
    async def _ensure_initialized(self):
        """
        确保数据库已初始化
        """
        try:
            from models import init_db
            await init_db()
        except Exception as e:
            logger.error(f"数据库初始化失败: {str(e)}")
            raise
    
    async def _get_analyzer(self) -> TradeStatisticsAnalyzer:
        """
        获取分析器实例，使用延迟初始化
        
        Returns:
            TradeStatisticsAnalyzer: 分析器实例
        """
        if self._analyzer is None:
            # 确保数据库已初始化
            await self._ensure_initialized()
            
            # 如果没有提供依赖项，则创建默认实例
            if self._trade_record_dao is None:
                self._trade_record_dao = TradeRecordDAO()
            
            if self._verification_updater is None:
                self._verification_updater = TradeRecordVerificationUpdater()
            
            self._analyzer = TradeStatisticsAnalyzer(
                trade_record_dao=self._trade_record_dao,
                verification_updater=self._verification_updater
            )
        
        return self._analyzer
    
    async def get_statistics_summary(
        self,
        days: Optional[int] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        strategies: Optional[List[str]] = None,
        tokens: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        获取统计摘要
        
        Args:
            days: 最近天数
            start_date: 开始日期
            end_date: 结束日期
            strategies: 策略过滤
            tokens: Token过滤
            
        Returns:
            统计摘要字典
        """
        try:
            # 创建配置
            config = self._create_config(days, start_date, end_date, strategies, tokens)
            
            # 获取分析器并执行分析
            analyzer = await self._get_analyzer()
            result = await analyzer.analyze(config)
            
            # 生成摘要
            summary = {
                "success": True,
                "overall_stats": {
                    "total_trades": result.overall_stats.total_trades if result.overall_stats else 0,
                    "win_rate": round(result.overall_stats.total_win_rate, 2) if result.overall_stats else 0,
                    "total_profit_rate": round(result.overall_stats.total_profit_rate, 2) if result.overall_stats else 0,
                    "avg_profit_rate": round(result.overall_stats.avg_profit_rate, 2) if result.overall_stats else 0,
                    "total_profit_amount": round(result.overall_stats.total_profit_amount, 4) if result.overall_stats else 0,
                    "profitable_trades": result.overall_stats.profitable_trades if result.overall_stats else 0,
                    "loss_trades": result.overall_stats.loss_trades if result.overall_stats else 0
                },
                "strategy_count": len(result.strategy_stats),
                "token_count": len(result.token_stats),
                "generation_time": result.generation_time.isoformat() if result.generation_time else datetime.now().isoformat(),
                "data_range": result.data_range
            }
            
            # 添加最佳表现信息
            if result.profit_rankings:
                summary["best_profit"] = {
                    "amount": round(result.profit_rankings[0].profit_amount, 4),
                    "rate": round(result.profit_rankings[0].profit_rate, 2),
                    "signal_id": str(result.profit_rankings[0].signal_id)
                }
            
            if result.loss_rankings:
                summary["worst_loss"] = {
                    "amount": round(result.loss_rankings[0].loss_amount, 4),
                    "rate": round(result.loss_rankings[0].loss_rate, 2),
                    "signal_id": str(result.loss_rankings[0].signal_id)
                }
            
            return summary
            
        except Exception as e:
            error_message = e.args[0] if e.args else f"An unexpected error occurred in get_statistics_summary: {e.__class__.__name__}"
            logger.error(f"获取统计摘要失败: {error_message}")
            return {
                "success": False,
                "error": error_message,
                "overall_stats": {},
                "strategy_count": 0,
                "token_count": 0
            }
    
    async def get_detailed_statistics(
        self,
        days: Optional[int] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        strategies: Optional[List[str]] = None,
        tokens: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        获取详细统计数据
        
        Args:
            days: 最近天数
            start_date: 开始日期
            end_date: 结束日期
            strategies: 策略过滤
            tokens: Token过滤
            
        Returns:
            详细统计数据字典
        """
        try:
            # 创建配置
            config = self._create_config(days, start_date, end_date, strategies, tokens)
            
            # 获取分析器并执行分析
            analyzer = await self._get_analyzer()
            result = await analyzer.analyze(config)
            
            # 转换为字典格式
            return {
                "success": True,
                "overall_stats": result.overall_stats.model_dump() if result.overall_stats else {},
                "token_stats": [stat.model_dump() for stat in result.token_stats],
                "strategy_stats": [stat.model_dump() for stat in result.strategy_stats],
                "profit_rankings": [ranking.model_dump() for ranking in result.profit_rankings],
                "loss_rankings": [ranking.model_dump() for ranking in result.loss_rankings],
                "generation_time": result.generation_time.isoformat() if result.generation_time else datetime.now().isoformat(),
                "data_range": result.data_range,
                "trade_pair_count": len(result.trade_pairs)
            }
            
        except Exception as e:
            error_message = e.args[0] if e.args else f"An unexpected error occurred in get_detailed_statistics: {e.__class__.__name__}"
            logger.error(f"获取详细统计失败: {error_message}")
            return {
                "success": False,
                "error": error_message
            }
    
    async def generate_report_file(
        self,
        file_path: str,
        format: ReportFormat = ReportFormat.HTML,
        days: Optional[int] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        strategies: Optional[List[str]] = None,
        tokens: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        生成报告文件
        
        Args:
            file_path: 文件保存路径
            format: 报告格式
            days: 最近天数
            start_date: 开始日期
            end_date: 结束日期
            strategies: 策略过滤
            tokens: Token过滤
            
        Returns:
            操作结果字典
        """
        try:
            # 创建配置
            config = self._create_config(days, start_date, end_date, strategies, tokens)
            
            # 获取分析器并生成报告
            analyzer = await self._get_analyzer()
            await analyzer.save_report(file_path, format, config)
            
            # 获取统计信息
            statistics = await analyzer.analyze(config)
            
            return {
                "success": True,
                "file_path": file_path,
                "format": format.value,
                "total_trades": statistics.overall_stats.total_trades if statistics.overall_stats else 0,
                "generation_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            error_message = e.args[0] if e.args else f"An unexpected error occurred in generate_report_file: {e.__class__.__name__}"
            logger.error(f"生成报告文件失败: {error_message}")
            return {
                "success": False,
                "error": error_message,
                "file_path": file_path
            }
    
    async def validate_data_integrity(
        self,
        days: Optional[int] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        strategies: Optional[List[str]] = None,
        tokens: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        验证数据完整性
        
        Args:
            days: 最近天数
            start_date: 开始日期
            end_date: 结束日期
            strategies: 策略过滤
            tokens: Token过滤
            
        Returns:
            验证结果字典
        """
        try:
            # 创建配置
            config = self._create_config(days, start_date, end_date, strategies, tokens)
            
            # 获取分析器并执行验证
            analyzer = await self._get_analyzer()
            validation_result = await analyzer.validate_data_integrity(config)
            
            return {
                "success": True,
                "data_valid": True, # Placeholder, actual validation logic needed
                "checked_trades": 0, # Placeholder
                "issues_found": [] # Placeholder
            }
            
        except Exception as e:
            error_message = e.args[0] if e.args else f"An unexpected error occurred in validate_data_integrity: {e.__class__.__name__}"
            logger.error(f"数据完整性验证失败: {error_message}")
            return {"success": False, "error": error_message}

    def _create_config(self, 
                      days: Optional[int] = None,
                      start_date: Optional[datetime] = None,
                      end_date: Optional[datetime] = None,
                      strategies: Optional[List[str]] = None,
                      tokens: Optional[List[str]] = None) -> StatisticsConfig:
        """
        创建统计配置对象
        
        Args:
            days: 最近天数
            start_date: 开始日期
            end_date: 结束日期
            strategies: 策略过滤
            tokens: Token过滤
            
        Returns:
            StatisticsConfig: 配置对象
        """
        config = StatisticsConfig()
        
        # 设置时间范围
        if days is not None:
            config.end_date = datetime.now()
            config.start_date = config.end_date - timedelta(days=days)
        else:
            if start_date:
                config.start_date = start_date
            if end_date:
                config.end_date = end_date
        
        # 设置过滤条件
        config.strategy_filter = strategies
        config.token_filter = tokens
        
        return config


# 全局API实例（延迟初始化）
_trade_statistics_api = None


def get_api_instance() -> TradeStatisticsAPI:
    """
    获取API实例（单例模式）
    
    Returns:
        TradeStatisticsAPI: API实例
    """
    global _trade_statistics_api
    if _trade_statistics_api is None:
        _trade_statistics_api = TradeStatisticsAPI()
    return _trade_statistics_api


# 便捷函数
async def get_quick_summary(days: int = 7) -> Dict[str, Any]:
    """
    快速获取最近N天的交易统计摘要
    
    Args:
        days: 天数
        
    Returns:
        Dict[str, Any]: 统计摘要
    """
    api = get_api_instance()
    return await api.get_statistics_summary(days=days)


async def get_strategy_performance(strategy_name: str, days: int = 30) -> Dict[str, Any]:
    """
    获取特定策略的表现
    
    Args:
        strategy_name: 策略名称
        days: 天数
        
    Returns:
        Dict[str, Any]: 策略表现数据
    """
    api = get_api_instance()
    return await api.get_detailed_statistics(
        days=days,
        strategies=[strategy_name]
    )


async def get_token_performance(token_address: str, days: int = 30) -> Dict[str, Any]:
    """
    获取特定Token的表现
    
    Args:
        token_address: Token地址
        days: 天数
        
    Returns:
        Dict[str, Any]: Token表现数据
    """
    api = get_api_instance()
    return await api.get_detailed_statistics(
        days=days,
        tokens=[token_address]
    ) 