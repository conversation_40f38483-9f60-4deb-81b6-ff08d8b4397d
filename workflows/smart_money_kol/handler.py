"""
智能钱包监控工作流处理函数模块

提供YAML配置中引用的处理函数
"""

import logging
import traceback
from typing import List, Dict, Any, Optional, Set
from datetime import datetime

from utils.spiders.smart_money.smart_money import SmartMoneySpider
from dao.kol_wallet_dao import KOLWalletDAO
from dao.solana_monitor_address_dao import SolanaMonitorAddressDAO
from models.smart_money_wallet import DailyProfit, RiskMetrics

# 创建日志记录器
logger = logging.getLogger("SmartMoneyHandler")

# 创建爬虫和DAO实例
spider = SmartMoneySpider()
wallet_dao = KOLWalletDAO()
monitor_address_dao = SolanaMonitorAddressDAO()

def safe_float(value, default=0.0):
    """安全地转换值为浮点数"""
    try:
        if value is None:
            return default
        return float(value)
    except (ValueError, TypeError):
        return default

def safe_int(value, default=0):
    """安全地转换值为整数"""
    try:
        if value is None:
            return default
        return int(value)
    except (ValueError, TypeError):
        return default

def safe_timestamp_to_datetime(timestamp, default=None):
    """安全地转换时间戳为datetime"""
    try:
        if timestamp is None:
            return default or datetime.utcnow()
        return datetime.fromtimestamp(timestamp)
    except (ValueError, TypeError):
        return default or datetime.utcnow()

async def fetch_smart_money_wallets():
    """
    获取智能钱包数据
    
    Yield:
        List[Dict]: 智能钱包数据列表
    """
    try:
        logger.info("开始获取智能钱包数据")
        
        # 调用爬虫获取数据
        result = await spider.get_smart_money_wallets(tag="renowned", orderby="last_active")
        
        if not result or result.get("code") != 0:
            logger.warning(f"获取数据失败或数据无效: {result}")
            yield
        
        # 处理返回的数据
        wallets = result.get("data", {}).get("rank", [])
        wallets = list(map(lambda x: {**x, "chain": "solana"}, wallets))
        
        if not wallets:
            logger.warning("没有找到钱包数据")
            yield
        
        logger.info(f"已获取 {len(wallets)} 个智能钱包数据")
        for wallet in wallets:
            yield wallet
        
    except Exception as e:
        logger.error(f"获取智能钱包数据时发生错误: {str(e)}\n{traceback.format_exc()}")
        yield

async def validate_smart_money_data(data: Any) -> bool:
    """
    验证智能钱包数据的有效性
    
    Args:
        data: 要验证的数据
        
    Returns:
        bool: 数据是否有效
    """
    try:
        if not data:
            logger.warning("数据为空")
            return False
            
        # 检查是否为列表
        if isinstance(data, list):
            if not data:
                logger.warning("数据列表为空")
                return False
                
            # 检查每个项目是否包含必要字段
            for item in data:
                if not all(field in item for field in ["wallet_address", "address"]):
                    logger.error(f"数据缺少必要字段: {item}")
                    return False
        # 检查单个项目
        elif isinstance(data, dict):
            if not all(field in data for field in ["wallet_address", "address"]):
                logger.error(f"数据缺少必要字段: {data}")
                return False
        else:
            logger.error(f"数据类型错误: {type(data)}")
            return False
            
        return True
        
    except Exception as e:
        logger.error(f"验证智能钱包数据时发生错误: {str(e)}\n{traceback.format_exc()}")
        return False

async def store_smart_money_data(data_list: List[Dict]) -> int:
    """
    存储智能钱包数据到数据库
    
    Args:
        data_list: 智能钱包数据列表
        
    Returns:
        int: 成功存储的数据数量
    """
    try:
        if not data_list:
            logger.warning("没有数据需要存储")
            return 0
            
        # 使用 DAO 批量更新钱包数据
        update_count = await wallet_dao.upsert_wallets(data_list)
        logger.info(f"成功更新 {update_count} 个钱包数据")
        return update_count
        
    except Exception as e:
        logger.error(f"存储智能钱包数据时发生错误: {str(e)}\n{traceback.format_exc()}")
        return False

async def sync_smart_money_addresses(data_list: List[Dict]) -> int:
    """
    同步智能钱包地址到监控地址表
    
    Args:
        data_list: 智能钱包数据列表
        
    Returns:
        int: 成功同步的地址数量
    """
    try:
        if not data_list:
            logger.warning("没有数据需要同步")
            return 0
            
        # 提取地址集合
        addresses = {data.get("address") for data in data_list if data.get("address")}
        logger.info(f"找到 {len(addresses)} 个智能钱包地址")
        
        # 查找已存在的监控地址
        existing = await monitor_address_dao.find_addresses_in_list(addresses)
        existing_addresses = {addr.address for addr in existing}
        
        # 找出需要新增的地址
        new_addresses = addresses - existing_addresses
        if not new_addresses:
            logger.info("没有需要新增的监控地址")
            return 0
            
        logger.info(f"需要新增 {len(new_addresses)} 个监控地址")
        
        # 批量添加新地址
        try:
            added = await monitor_address_dao.batch_add_monitor_addresses(
                list(new_addresses),
                name_prefix="smart_money",
                description="智能钱包地址"
            )
            logger.info(f"成功添加 {len(added)} 个监控地址")
            return len(added)
            
        except Exception as e:
            logger.error(f"添加监控地址失败: {str(e)}\n{traceback.format_exc()}")
            return 0
        
    except Exception as e:
        logger.error(f"同步智能钱包地址时发生错误: {str(e)}\n{traceback.format_exc()}")
        return 0 