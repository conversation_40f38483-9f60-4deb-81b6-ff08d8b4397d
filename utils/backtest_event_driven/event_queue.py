import heapq
from typing import List, Optional, Callable, Dict, Any, Awaitable, Union
import logging

from utils.backtest_event_driven.events import Event, EventType

logger = logging.getLogger("EventQueue")


class EventQueue:
    """事件队列类，管理和分发事件"""
    
    def __init__(self):
        """初始化事件队列"""
        self._queue = []  # 使用列表作为最小堆的基础
        self._handlers = {
            EventType.TRANSACTION: [],
            EventType.SIGNAL: [],
            EventType.ORDER: [],
            EventType.FILL: [],
            EventType.PRICE_UNAVAILABLE: []  # 添加对价格不可用事件的处理
        }
        self._event_count = {event_type: 0 for event_type in EventType}
        
    def push(self, event: Event) -> None:
        """将事件推入队列
        
        Args:
            event: 要推入的事件
        """
        heapq.heappush(self._queue, event)
        self._event_count[event.event_type] += 1
        
    def push_all(self, events: List[Event]) -> None:
        """将多个事件推入队列
        
        Args:
            events: 要推入的事件列表
        """
        for event in events:
            self.push(event)
            
    def pop(self) -> Optional[Event]:
        """从队列中弹出时间最早的事件
        
        Returns:
            Optional[Event]: 最早的事件，如果队列为空则返回None
        """
        if not self._queue:
            return None
        
        event = heapq.heappop(self._queue)
        self._event_count[event.event_type] -= 1
        return event
    
    def peek(self) -> Optional[Event]:
        """查看队列中时间最早的事件，但不弹出
        
        Returns:
            Optional[Event]: 最早的事件，如果队列为空则返回None
        """
        if not self._queue:
            return None
        
        return self._queue[0]  # 最小堆的根节点是时间最早的事件
    
    def is_empty(self) -> bool:
        """检查队列是否为空
        
        Returns:
            bool: 队列是否为空
        """
        return len(self._queue) == 0
    
    def size(self) -> int:
        """获取队列中事件的数量
        
        Returns:
            int: 队列中的事件数量
        """
        return len(self._queue)
    
    def event_counts(self) -> Dict[EventType, int]:
        """获取各类型事件的数量
        
        Returns:
            Dict[EventType, int]: 事件类型及其数量的映射
        """
        return self._event_count.copy()
    
    def register_handler(self, event_type: EventType, handler: Union[Callable[[Event], None], Callable[[Event], Awaitable[None]]]) -> None:
        """注册事件处理器
        
        Args:
            event_type: 事件类型
            handler: 事件处理函数，接收一个事件参数，可以是同步或异步函数
        """
        self._handlers[event_type].append(handler)
        
    async def dispatch(self, event: Event) -> None:
        """分发事件到对应的处理器
        
        Args:
            event: 要分发的事件
        """
        if not self._handlers[event.event_type]:
            logger.warning(f"没有处理器注册用于处理事件类型: {event.event_type}")
            return
            
        # 调用所有注册的对应类型的处理器
        for handler in self._handlers[event.event_type]:
            try:
                await handler(event)
            except Exception as e:
                logger.error(f"处理事件 {event} 时发生错误: {e}", exc_info=True)
    
    async def process_next(self) -> bool:
        """处理队列中的下一个事件
        
        Returns:
            bool: 如果处理了事件返回True，如果队列为空返回False
        """
        event = self.pop()
        if not event:
            return False
            
        await self.dispatch(event)
        return True
    
    async def process_all(self) -> int:
        """处理队列中的所有事件
        
        Returns:
            int: 处理的事件数量
        """
        count = 0
        while await self.process_next():
            count += 1
            
        return count 