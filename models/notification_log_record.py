from datetime import datetime
from typing import Optional
from beanie import Document, Indexed, Link # Link可能需要引入
from pydantic import Field
# from models.alert_event_record import AlertEventRecord # 如果使用Link

class NotificationLogRecord(Document):
    """记录每一次发送通知的尝试"""
    alert_event_raw_id: Optional[str] = Field(default=None, description="关联的告警事件记录的ObjectId字符串")
    # 或者 alert_event_id: Optional[Link[AlertEventRecord]] = Field(default=None, description="关联的告警事件记录ID")
    
    recipient_chat_id: Indexed(str) = Field(description="接收方的Telegram Chat ID")
    send_time: Indexed(datetime) = Field(default_factory=datetime.utcnow, description="通知发送/尝试时间 (UTC)")
    status: Indexed(str) = Field(description="发送状态 (e.g., success, failure)")
    message_content_preview: Optional[str] = Field(default=None, description="发送消息内容的预览或摘要", max_length=500)
    error_message: Optional[str] = Field(default=None, description="如果发送失败，记录错误信息")

    class Settings:
        name = "notification_log_records"
        indexes = [
            "alert_event_raw_id",
            "recipient_chat_id",
            "send_time",
            "status",
        ] 