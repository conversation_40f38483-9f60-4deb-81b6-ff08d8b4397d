from typing import List, Optional
from datetime import datetime

from models.telegram_users import TelegramUser
from dao.base_dao import BaseDAO


class TelegramUserDAO(BaseDAO[TelegramUser]):
    """Telegram用户数据访问对象"""
    
    def __init__(self):
        """初始化，获取数据库连接"""
        super().__init__(TelegramUser)
    
    async def create_user(self, chat_id: str) -> TelegramUser:
        """创建新用户
        
        Args:
            chat_id: Telegram聊天ID
            
        Returns:
            TelegramUser: 创建的用户对象
        """
        user = self.collection.find_one({"chat_id": chat_id})
        if not user:
            user = TelegramUser(
                chat_id=chat_id,
                created_at=datetime.utcnow()
            )
            await user.insert()
        return user
    
    async def get_user_by_chat_id(self, chat_id: str) -> Optional[TelegramUser]:
        """通过聊天ID获取用户
        
        Args:
            chat_id: Telegram聊天ID
            
        Returns:
            Optional[TelegramUser]: 找到的用户对象，如果不存在则返回None
        """
        return await self.collection.find_one({"chat_id": chat_id})
    
    async def get_all_users(self) -> List[TelegramUser]:
        """获取所有用户
        
        Returns:
            List[TelegramUser]: 所有用户列表
        """
        return await self.collection.find().to_list()
    
    async def delete_user(self, chat_id: str) -> bool:
        """删除用户
        
        Args:
            chat_id: Telegram聊天ID
            
        Returns:
            bool: 删除成功返回True，否则返回False
        """
        user = await self.get_user_by_chat_id(chat_id)
        if user:
            await user.delete()
            return True
        return False
    
    async def user_exists(self, chat_id: str) -> bool:
        """检查用户是否存在
        
        Args:
            chat_id: Telegram聊天ID
            
        Returns:
            bool: 用户存在返回True，否则返回False
        """
        user = await self.get_user_by_chat_id(chat_id)
        return user is not None
    
    async def get_or_create_user(self, chat_id: str) -> TelegramUser:
        """获取用户，如果不存在则创建
        
        Args:
            chat_id: Telegram聊天ID
            
        Returns:
            TelegramUser: 获取或创建的用户对象
        """
        user = await self.get_user_by_chat_id(chat_id)
        if not user:
            user = await self.create_user(chat_id)
        return user
