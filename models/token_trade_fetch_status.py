from beanie import Document, Indexed, PydanticObjectId
from pydantic import Field
from typing import Optional
from datetime import datetime
import pymongo # 导入 pymongo
from pymongo import IndexModel # 导入 IndexModel
import logging # Add logging

logger = logging.getLogger(__name__)

class TokenTradeFetchStatus(Document):
    """
    跟踪 Gmgn 代币交易历史记录首次抓取的状态
    """
    token_address: Indexed(str)
    chain: Indexed(str)
    
    initial_fetch_status: str = Field(
        default="pending", 
        description="Initial fetch status: pending, in_progress, completed, failed"
    )
    next_cursor: Optional[str] = Field(
        default=None,
        description="Next cursor for resuming interrupted initial fetch"
    )
    last_error_message: Optional[str] = Field(
        default=None,
        description="Last error message during fetch, if any"
    )
    
    # 使用 Pydantic V2 和 Beanie 推荐的方式定义时间戳
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    class Settings:
        name = "token_trade_fetch_status"
        # 创建索引
        indexes = [
            # 复合唯一索引，确保 token_address 和 chain 组合的唯一性
            IndexModel([
                ("token_address", pymongo.ASCENDING),
                ("chain", pymongo.ASCENDING)
            ], name="token_chain_unique", unique=True),
            # 为 initial_fetch_status 创建索引以加速状态查询
            IndexModel([("initial_fetch_status", pymongo.ASCENDING)], name="initial_fetch_status_index"),
            # 为 token_address 创建索引 (已通过 Indexed 隐式创建，但显式定义更清晰)
            # IndexModel([("token_address", pymongo.ASCENDING)], name="token_address_index"),
            # 为 chain 创建索引 (已通过 Indexed 隐式创建，但显式定义更清晰)
            # IndexModel([("chain", pymongo.ASCENDING)], name="chain_index"),
        ]

    @classmethod
    async def get_or_create(cls, token_address: str, chain: str) -> "TokenTradeFetchStatus":
        """获取或创建指定代币的抓取状态记录"""
        status_doc = await cls.find_one(
            cls.token_address == token_address,
            cls.chain == chain
        )
        if not status_doc:
            status_doc = cls(token_address=token_address, chain=chain)
            # 使用 insert 确保唯一索引生效，如果并发创建可能失败，需要调用者处理
            try:
                await status_doc.insert()
                logger.info(f"为 {chain}:{token_address} 创建了新的抓取状态记录。")
            except Exception as e: # Catch potential duplicate key error on concurrent creation
                 logger.warning(f"尝试创建状态记录时可能遇到并发冲突 for {chain}:{token_address}，重新查询: {e}")
                 # 即使插入失败（例如因为唯一键冲突），也要尝试重新获取文档
                 status_doc = await cls.find_one(
                     cls.token_address == token_address,
                     cls.chain == chain
                 )
                 if not status_doc: # 如果重查还是没有，说明有其他问题
                      logger.error(f"创建或查询状态记录失败 for {chain}:{token_address}: {e}")
                      raise # Re-raise the exception
        return status_doc 