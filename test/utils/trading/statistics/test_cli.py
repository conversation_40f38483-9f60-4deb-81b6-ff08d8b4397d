#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CLI工具测试用例
"""

import unittest
import tempfile
import argparse
from pathlib import Path
from unittest.mock import patch, MagicMock
from datetime import datetime

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../../'))

from utils.trading.statistics.cli import validate_args, generate_default_output_path, parse_date, parse_list


class TestCLI(unittest.TestCase):
    """CLI工具测试"""
    
    def _create_mock_args(self, **kwargs):
        """创建包含所有必要属性的mock参数对象"""
        args = argparse.Namespace()
        
        # 基本参数
        args.days = kwargs.get('days', None)
        args.start_date = kwargs.get('start_date', None)
        args.end_date = kwargs.get('end_date', None)
        args.output = kwargs.get('output', None)
        args.format = kwargs.get('format', 'html')
        
        # 新增的时间参数
        args.date = kwargs.get('date', None)
        args.yesterday = kwargs.get('yesterday', False)
        args.last_week = kwargs.get('last_week', False)
        
        # 飞书相关参数
        args.send_feishu = kwargs.get('send_feishu', False)
        args.feishu_webhook = kwargs.get('feishu_webhook', None)
        args.report_base_url = kwargs.get('report_base_url', None)
        args.upload_path = kwargs.get('upload_path', None)
        args.feishu_message_type = kwargs.get('feishu_message_type', 'card')
        args.no_upload_report = kwargs.get('no_upload_report', False)
        
        # Token链接配置参数
        args.token_link_base_url = kwargs.get('token_link_base_url', 'https://gmgn.ai/sol/token/')
        args.disable_token_links = kwargs.get('disable_token_links', False)
        
        # 其他参数
        args.strategies = kwargs.get('strategies', None)
        args.tokens = kwargs.get('tokens', None)
        args.period = kwargs.get('period', 'custom')
        args.verbose = kwargs.get('verbose', False)
        args.validate_data = kwargs.get('validate_data', False)
        args.no_charts = kwargs.get('no_charts', False)
        
        return args
    
    def test_parse_date(self):
        """测试日期解析"""
        # 正确格式
        date = parse_date("2024-01-01")
        self.assertEqual(date.year, 2024)
        self.assertEqual(date.month, 1)
        self.assertEqual(date.day, 1)
        
        # 错误格式
        with self.assertRaises(argparse.ArgumentTypeError):
            parse_date("2024/01/01")
    
    def test_parse_list(self):
        """测试列表解析"""
        # 正常列表
        result = parse_list("a,b,c")
        self.assertEqual(result, ["a", "b", "c"])
        
        # 带空格的列表
        result = parse_list("a, b , c")
        self.assertEqual(result, ["a", "b", "c"])
        
        # 空字符串
        result = parse_list("")
        self.assertEqual(result, [])
        
        # None
        result = parse_list(None)
        self.assertEqual(result, [])
    
    def test_directory_output_validation(self):
        """测试目录输出验证"""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # 测试1: 目录路径（以/结尾）
            args = self._create_mock_args(
                output=str(temp_path) + "/",
                format="html"
            )
            
            original_output = args.output
            validate_args(args)
            
            # 验证输出路径已被修改为文件路径
            self.assertNotEqual(args.output, original_output)
            self.assertTrue(args.output.endswith('.html'))
            self.assertTrue(args.output.startswith(str(temp_path)))
            
            # 测试2: 已存在的目录
            test_dir = temp_path / "test_reports"
            test_dir.mkdir()
            
            args2 = self._create_mock_args(
                output=str(test_dir),
                format="json"
            )
            
            validate_args(args2)
            
            # 验证输出路径已被修改为文件路径
            self.assertTrue(args2.output.endswith('.json'))
            self.assertTrue(args2.output.startswith(str(test_dir)))
            
            # 测试3: 文件路径保持不变
            args3 = self._create_mock_args(
                output=str(temp_path / "custom_report.html"),
                format="html"
            )
            
            original_output3 = args3.output
            validate_args(args3)
            
            # 验证文件路径保持不变
            self.assertEqual(args3.output, original_output3)
    
    def test_file_extension_validation(self):
        """测试文件扩展名验证"""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # 正确的扩展名
            args = self._create_mock_args(
                output=str(temp_path / "report.html"),
                format="html"
            )
            
            # 应该不会抛出异常
            validate_args(args)
            
            # 错误的扩展名（会有警告但不会抛出异常）
            args2 = self._create_mock_args(
                output=str(temp_path / "report.txt"),
                format="html"
            )
            
            # 应该不会抛出异常
            validate_args(args2)
    
    def test_date_range_validation(self):
        """测试日期范围验证"""
        # 正确的日期范围
        args = self._create_mock_args(
            start_date=datetime(2024, 1, 1),
            end_date=datetime(2024, 12, 31)
        )
        
        # 应该不会抛出异常
        validate_args(args)
        
        # 错误的日期范围
        args2 = self._create_mock_args(
            start_date=datetime(2024, 12, 31),
            end_date=datetime(2024, 1, 1)
        )
        
        with self.assertRaises(ValueError):
            validate_args(args2)
        
        # days与日期范围冲突
        args3 = self._create_mock_args(
            start_date=datetime(2024, 1, 1),
            days=7
        )
        
        with self.assertRaises(ValueError):
            validate_args(args3)
    
    def test_feishu_validation_success(self):
        """测试飞书参数验证成功情况"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 测试完整的飞书配置
            args = self._create_mock_args(
                send_feishu=True,
                feishu_webhook="https://open.feishu.cn/open-apis/bot/v2/hook/test",
                report_base_url="https://reports.example.com",
                upload_path=temp_dir
            )
            
            # 应该不会抛出异常
            validate_args(args)
            
            # 验证路径被设置
            self.assertEqual(args.feishu_webhook, "https://open.feishu.cn/open-apis/bot/v2/hook/test")
            self.assertEqual(args.report_base_url, "https://reports.example.com")
            self.assertEqual(args.upload_path, temp_dir)
    
    def test_feishu_validation_missing_webhook(self):
        """测试缺少飞书Webhook的情况"""
        args = self._create_mock_args(send_feishu=True)
        
        with self.assertRaises(ValueError) as context:
            validate_args(args)
        
        self.assertIn("FEISHU_WEBHOOK_URL", str(context.exception))
    
    @patch.dict(os.environ, {'FEISHU_WEBHOOK_URL': 'https://test.webhook.url'})
    def test_feishu_validation_env_webhook(self):
        """测试从环境变量获取飞书Webhook"""
        args = self._create_mock_args(send_feishu=True)
        
        # 应该不会抛出异常，因为环境变量中有webhook
        validate_args(args)
        
        # 验证从环境变量获取的值
        self.assertEqual(args.feishu_webhook, 'https://test.webhook.url')
    
    def test_feishu_validation_no_upload_fallback(self):
        """测试飞书配置缺失时的降级处理"""
        args = self._create_mock_args(
            send_feishu=True,
            feishu_webhook="https://test.webhook.url"
            # 缺少 report_base_url 和 upload_path
        )
        
        # 应该不会抛出异常，但会设置 no_upload_report
        validate_args(args)
        
        # 验证降级处理
        self.assertTrue(args.no_upload_report)
    
    def test_generate_default_output_path(self):
        """测试默认输出路径生成"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 测试指定目录
            output_path = generate_default_output_path("html", temp_dir)
            
            self.assertTrue(output_path.startswith(temp_dir))
            self.assertTrue(output_path.endswith('.html'))
            self.assertIn('trade_statistics_', output_path)
            
            # 验证目录存在
            self.assertTrue(Path(output_path).parent.exists())
            
            # 测试默认目录
            default_path = generate_default_output_path("json")
            
            self.assertTrue(default_path.endswith('.json'))
            self.assertIn('trade_statistics_', default_path)
            self.assertIn('reports/trading_statistics', default_path)


if __name__ == '__main__':
    unittest.main() 