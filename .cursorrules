# memeMonitor 项目规则

## 项目概述
这是一个基于节点和工作流概念的数据处理系统，用于爬取和处理与加密货币相关的数据。系统使用代理IP进行爬虫操作，以避免被目标网站封锁。

## 项目目录结构

```
memeMonitor/
├── dao/                    # 数据访问对象层，封装数据库操作
├── deploy/                 # 部署相关配置和脚本
├── models/                 # 数据模型定义
├── test/                   # 测试代码
├── utils/                  # 工具类和通用功能
│   ├── connectors/         # 数据库和外部服务连接器
│   ├── spiders/            # 爬虫实现
│   │   ├── smart_money/    # Gmgn智能资金爬虫
│   │   ├── solana/         # Solana链相关爬虫
│   │   └── x/              # Twitter(X)爬虫
│   └── workflows/          # 工作流系统实现
│       ├── nodes/          # 工作流节点实现
│       └── *.py            # 工作流核心类
├── workflows/              # 工作流配置和定义
├── .env                    # 环境变量配置
├── main.py                 # 主程序入口
├── run_jobs.py             # 运行定时任务
├── run_workflow.py         # 运行工作流
└── setup.py                # 项目安装配置
```

## 关键文件说明

### 核心文件
- `run_workflow.py`: 工作流运行入口，负责加载YAML配置并启动工作流
- `run_jobs.py`: 定时任务运行入口，负责调度和执行定时任务
- `.env`: 环境变量配置文件，存储数据库连接信息和其他配置

### 工具类文件
- `utils/session.py`: 代理会话实现，用于爬虫请求
- `utils/spiders/smart_money/__init__.py`: 智能资金爬虫基类，实现代理切换和重试逻辑
- `utils/workflows/workflow.py`: 工作流核心实现，定义工作流和节点的基本行为
- `utils/workflows/node.py`: 工作流节点实现，定义各种类型的节点
- `utils/workflows/workflow_config.py`: 工作流配置解析，支持从YAML文件创建工作流
- `utils/workflows/workflow_manager.py`: 工作流管理器，负责管理多个工作流的运行
- `utils/workflows/message_queue.py`: 消息队列实现，用于节点间通信

### 数据库相关
- `utils/connectors/mongodb.py`: MongoDB连接器，负责建立数据库连接
- `models/__init__.py`: 数据模型初始化，注册Beanie文档模型

### 初始化数据库文档（创建数据库表）

- `models/__init__.py` 在这个文件添加初始化数据库文档

### `__init__.py` 文件的作用

在Python项目中，`__init__.py` 文件有以下几个重要作用：

1. **包标识**：将目录标记为Python包，使其可以被导入
2. **包初始化**：在导入包时执行初始化代码
3. **导出控制**：定义 `__all__` 变量控制 `from package import *` 导入的内容
4. **简化导入路径**：通过在 `__init__.py` 中导入子模块，简化外部导入路径

项目中的主要 `__init__.py` 文件：

- `models/__init__.py`: 负责初始化数据库连接和注册所有数据模型，是数据模型的核心入口点
- `utils/__init__.py`: 导出通用工具函数，方便其他模块导入
- `utils/spiders/__init__.py`: 定义爬虫包的基本组件
- `utils/spiders/smart_money/__init__.py`: 定义智能资金爬虫的基类和核心功能
- `utils/workflows/__init__.py`: 导出工作流核心组件，简化导入路径

特别是 `models/__init__.py` 文件，它实现了以下功能：
- 导入所有数据模型类
- 从环境变量获取数据库连接信息
- 提供 `init_db()` 函数初始化数据库连接
- 注册所有数据模型到Beanie ODM

## 代码规范

### 命名规范
- 类名：使用 PascalCase（如 `BasicSpider`）
- 方法和变量：使用 snake_case（如 `request_with_retry`）
- 常量：使用全大写 SNAKE_CASE（如 `PROXYs`）

### 文档规范
- 所有类和公共方法必须有文档字符串
- 文档字符串使用中文，格式遵循 Google 风格
- 复杂逻辑需要添加注释说明

### 错误处理
- 使用 try-except 捕获可预见的异常
- 记录异常信息，包括堆栈跟踪
- 实现重试机制处理临时性错误

## 爬虫规则
- 所有爬虫必须继承 `BasicSpider` 类
- 直接使用代理IP进行请求，避免使用原始IP
- 请求失败时自动切换代理并重试
- 遵循目标网站的robots.txt规则
- 添加适当的请求间隔，避免过度请求

## 工作流规则
- 工作流由节点组成，每个节点负责特定的数据处理任务
- 节点之间通过 `WorkflowData` 传递数据
- 工作流配置使用YAML文件定义
- 错误处理应在节点级别实现

## 数据库操作
- 使用异步MongoDB客户端
- 数据库操作应封装在DAO层
- 使用Beanie ODM进行对象映射

## 安全规则
- 敏感信息（如代理凭据）不应硬编码在源代码中
- 使用环境变量或配置文件存储敏感信息
- API密钥和凭据应妥善保管

## 性能优化
- 使用异步编程提高并发性能
- 批量处理数据减少数据库操作次数
- 实现缓存机制减少重复请求

## 测试规则
- 编写单元测试验证核心功能
- 模拟外部依赖进行测试
- 测试覆盖率应达到合理水平 

## Python包管理

使用poetry管理项目依赖，使用poetry安装依赖

## 数据分析和可视化

    You are an expert in data analysis, visualization, and Jupyter Notebook development, with a focus on Python libraries such as pandas, matplotlib, seaborn, and numpy.
  
    Key Principles:
    - Write concise, technical responses with accurate Python examples.
    - Prioritize readability and reproducibility in data analysis workflows.
    - Use functional programming where appropriate; avoid unnecessary classes.
    - Prefer vectorized operations over explicit loops for better performance.
    - Use descriptive variable names that reflect the data they contain.
    - Follow PEP 8 style guidelines for Python code.

    Data Analysis and Manipulation:
    - Use pandas for data manipulation and analysis.
    - Prefer method chaining for data transformations when possible.
    - Use loc and iloc for explicit data selection.
    - Utilize groupby operations for efficient data aggregation.

    Visualization:
    - Use matplotlib for low-level plotting control and customization.
    - Use seaborn for statistical visualizations and aesthetically pleasing defaults.
    - Create informative and visually appealing plots with proper labels, titles, and legends.
    - Use appropriate color schemes and consider color-blindness accessibility.

    Jupyter Notebook Best Practices:
    - Structure notebooks with clear sections using markdown cells.
    - Use meaningful cell execution order to ensure reproducibility.
    - Include explanatory text in markdown cells to document analysis steps.
    - Keep code cells focused and modular for easier understanding and debugging.
    - Use magic commands like %matplotlib inline for inline plotting.

    Error Handling and Data Validation:
    - Implement data quality checks at the beginning of analysis.
    - Handle missing data appropriately (imputation, removal, or flagging).
    - Use try-except blocks for error-prone operations, especially when reading external data.
    - Validate data types and ranges to ensure data integrity.

    Performance Optimization:
    - Use vectorized operations in pandas and numpy for improved performance.
    - Utilize efficient data structures (e.g., categorical data types for low-cardinality string columns).
    - Consider using dask for larger-than-memory datasets.
    - Profile code to identify and optimize bottlenecks.

    Dependencies:
    - pandas
    - numpy
    - matplotlib
    - seaborn
    - jupyter
    - scikit-learn (for machine learning tasks)

    Key Conventions:
    1. Begin analysis with data exploration and summary statistics.
    2. Create reusable plotting functions for consistent visualizations.
    3. Document data sources, assumptions, and methodologies clearly.
    4. Use version control (e.g., git) for tracking changes in notebooks and scripts.

    Refer to the official documentation of pandas, matplotlib, and Jupyter for best practices and up-to-date APIs.
      