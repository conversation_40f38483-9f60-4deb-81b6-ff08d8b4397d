"""结果分析组件 - 回测模块V2

保持与现有回测模块的输出兼容性，提供丰富的统计指标和可视化，
支持结果导出和报告生成。
"""

import os
import json
import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
from datetime import datetime

from utils.backtest_v2.config_manager import BacktestConfigV2

logger = logging.getLogger("ResultAnalyzerV2")


class ResultAnalyzer:
    """结果分析组件"""
    
    def __init__(self, config: BacktestConfigV2):
        """初始化结果分析器
        
        Args:
            config: 回测配置对象
        """
        self.config = config
        logger.info("结果分析器V2初始化完成")
    
    def analyze(self, trades: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析交易结果并生成统计指标
        
        Args:
            trades: 交易列表
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        try:
            logger.info(f"开始分析 {len(trades)} 笔交易的结果")
            
            if not trades:
                return self._generate_empty_result()
            
            # 计算性能指标
            statistics = self.calculate_performance_metrics(trades)
            
            # 生成资金曲线数据
            equity_curve = self.generate_equity_curve(trades)
            
            # 构建完整结果
            result = {
                'statistics': statistics,
                'trades': trades,
                'equity_curve': equity_curve,
                'config': self.config.__dict__,
                'analysis_timestamp': datetime.now().isoformat()
            }
            
            logger.info("结果分析完成")
            return result
            
        except Exception as e:
            logger.error(f"结果分析失败: {e}")
            raise
    
    def calculate_performance_metrics(self, trades: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算回测统计指标
        
        Args:
            trades: 交易列表
            
        Returns:
            Dict[str, Any]: 统计指标
        """
        if not trades:
            return {}
        
        df = pd.DataFrame(trades)
        
        # 基础统计
        total_trades = len(trades)
        winning_trades = len(df[df['return_rate'] > 0])
        losing_trades = len(df[df['return_rate'] < 0])
        
        # 胜率
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # 收益统计
        returns = df['return_rate'].values
        total_return = np.prod(1 + returns) - 1  # 复合收益率
        avg_return = np.mean(returns)
        std_return = np.std(returns)
        
        # 盈亏统计
        winning_returns = df[df['return_rate'] > 0]['return_rate']
        losing_returns = df[df['return_rate'] < 0]['return_rate']
        
        avg_winning_return = np.mean(winning_returns) if len(winning_returns) > 0 else 0
        avg_losing_return = np.mean(losing_returns) if len(losing_returns) > 0 else 0
        
        # 最大回撤
        cumulative_returns = (1 + df['return_rate']).cumprod()
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max
        max_drawdown = drawdown.min()
        
        # 夏普比率（假设无风险利率为0）
        sharpe_ratio = avg_return / std_return if std_return > 0 else 0
        
        # 盈亏比
        profit_loss_ratio = abs(avg_winning_return / avg_losing_return) if avg_losing_return != 0 else 0
        
        # 持仓时间统计
        holding_hours = df['holding_hours'].values
        avg_holding_hours = np.mean(holding_hours)
        max_holding_hours = np.max(holding_hours)
        min_holding_hours = np.min(holding_hours)
        
        # 按卖出原因分组统计
        sell_reason_stats = df.groupby('sell_reason').agg({
            'return_rate': ['count', 'mean'],
            'holding_hours': 'mean'
        }).round(4)
        
        # KOL数量统计
        kol_count_stats = df.groupby('kol_count').agg({
            'return_rate': ['count', 'mean']
        }).round(4)
        
        statistics = {
            # 基础统计
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': round(win_rate, 4),
            
            # 收益统计
            'total_return': round(total_return, 4),
            'avg_return': round(avg_return, 4),
            'return_rate': round(avg_return, 4),  # 兼容现有格式
            'std_return': round(std_return, 4),
            'avg_winning_return': round(avg_winning_return, 4),
            'avg_losing_return': round(avg_losing_return, 4),
            
            # 风险指标
            'max_drawdown': round(max_drawdown, 4),
            'sharpe_ratio': round(sharpe_ratio, 4),
            'profit_loss_ratio': round(profit_loss_ratio, 4),
            
            # 持仓时间
            'avg_holding_hours': round(avg_holding_hours, 2),
            'max_holding_hours': round(max_holding_hours, 2),
            'min_holding_hours': round(min_holding_hours, 2),
            
            # 分组统计
            'sell_reason_stats': sell_reason_stats.to_dict() if not sell_reason_stats.empty else {},
            'kol_count_stats': kol_count_stats.to_dict() if not kol_count_stats.empty else {},
            
            # 时间范围
            'backtest_start_time': self.config.backtest_start_time,
            'backtest_end_time': self.config.backtest_end_time,
            'backtest_days': (self.config.backtest_end_time - self.config.backtest_start_time) / 86400
        }
        
        logger.info(f"统计指标计算完成: 总交易={total_trades}, 胜率={win_rate:.2%}, "
                   f"总收益={total_return:.2%}, 最大回撤={max_drawdown:.2%}")
        
        return statistics
    
    def generate_equity_curve(self, trades: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """生成资金曲线数据
        
        Args:
            trades: 交易列表
            
        Returns:
            List[Dict[str, Any]]: 资金曲线数据点
        """
        if not trades:
            return []
        
        df = pd.DataFrame(trades)
        df = df.sort_values('buy_timestamp')
        
        # 计算累积收益
        df['cumulative_return'] = (1 + df['return_rate']).cumprod() - 1
        df['equity'] = self.config.initial_capital * (1 + df['cumulative_return'])
        
        # 生成资金曲线数据点
        equity_curve = []
        
        # 起始点
        equity_curve.append({
            'timestamp': self.config.backtest_start_time,
            'equity': self.config.initial_capital,
            'cumulative_return': 0.0,
            'trade_count': 0
        })
        
        # 每笔交易后的资金状态
        for i, row in df.iterrows():
            equity_curve.append({
                'timestamp': int(row['sell_timestamp']),
                'equity': float(row['equity']),
                'cumulative_return': float(row['cumulative_return']),
                'trade_count': len(equity_curve)  # 当前交易数量
            })
        
        return equity_curve
    
    def _generate_empty_result(self) -> Dict[str, Any]:
        """生成空结果（无交易时）
        
        Returns:
            Dict[str, Any]: 空结果
        """
        return {
            'statistics': {
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'win_rate': 0.0,
                'total_return': 0.0,
                'avg_return': 0.0,
                'return_rate': 0.0,
                'max_drawdown': 0.0,
                'sharpe_ratio': 0.0,
                'backtest_start_time': self.config.backtest_start_time,
                'backtest_end_time': self.config.backtest_end_time,
                'backtest_days': (self.config.backtest_end_time - self.config.backtest_start_time) / 86400
            },
            'trades': [],
            'equity_curve': [
                {
                    'timestamp': self.config.backtest_start_time,
                    'equity': self.config.initial_capital,
                    'cumulative_return': 0.0,
                    'trade_count': 0
                }
            ],
            'config': self.config.__dict__,
            'analysis_timestamp': datetime.now().isoformat()
        }
    
    def export_results(self, results: Dict[str, Any], output_dir: str):
        """导出结果到文件
        
        Args:
            results: 分析结果
            output_dir: 输出目录
        """
        try:
            os.makedirs(output_dir, exist_ok=True)
            
            # 导出JSON结果
            results_path = os.path.join(output_dir, "results.json")
            with open(results_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=4, default=str, ensure_ascii=False)
            
            # 导出交易记录CSV
            if results.get('trades'):
                trades_df = pd.DataFrame(results['trades'])
                trades_path = os.path.join(output_dir, "trades.csv")
                trades_df.to_csv(trades_path, index=False, encoding='utf-8')
            
            # 导出资金曲线CSV
            if results.get('equity_curve'):
                equity_df = pd.DataFrame(results['equity_curve'])
                equity_path = os.path.join(output_dir, "equity_curve.csv")
                equity_df.to_csv(equity_path, index=False, encoding='utf-8')
            
            logger.info(f"结果已导出到目录: {output_dir}")
            
        except Exception as e:
            logger.error(f"导出结果失败: {e}")
            raise
