{"name": "gmgn-trade-script", "version": "1.0.0", "description": "GMGN Trading Script with Anti-Cloudflare Protection", "main": "utils/trading/solana/gmgn_test.mjs", "type": "module", "scripts": {"test": "node utils/trading/solana/gmgn_test.mjs --txhash test"}, "dependencies": {"@coral-xyz/anchor": "^0.29.0", "@playwright/test": "^1.52.0", "@solana/web3.js": "^1.87.6", "bs58": "^5.0.0", "cloudscraper": "^4.6.0", "dotenv": "^16.4.5", "https-proxy-agent": "^7.0.4", "minimist": "^1.2.8", "node-fetch": "^3.3.2", "playwright": "^1.52.0", "puppeteer": "^24.9.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2"}, "engines": {"node": ">=18.0.0"}, "keywords": ["solana", "trading", "gmgn", "defi", "cloudflare", "proxy", "curl-cffi", "cloudscraper"], "author": "memeMonitor", "license": "MIT"}