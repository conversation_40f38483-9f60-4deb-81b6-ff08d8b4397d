import logging
from datetime import datetime
from typing import Optional, List, Dict, Any
from beanie import PydanticObjectId

from dao.trade_record_dao import TradeRecordDAO
from models.trade_record import TradeRecord, TradeStatus as ModelTradeStatus, TradeType as ModelTradeType
from models.channel_attempt import ChannelAttemptRecord
from models.trade_execution import TradeExecutionResult, ChannelAttemptResult, TradeStatus

logger = logging.getLogger(__name__)


class TradeRecordManager:
    """交易记录管理器 - 负责交易记录的创建、更新和查询"""
    
    def __init__(self):
        self.trade_record_dao = TradeRecordDAO()
    
    async def create_trade_record(
        self,
        signal_id: PydanticObjectId,
        strategy_name: str,
        trade_type: str,
        token_in_address: str,
        token_out_address: str,
        amount: float,
        wallet_address: str,
        trade_provider: str = "auto_trade_manager"
    ) -> TradeRecord:
        """
        创建新的交易记录
        
        Args:
            signal_id: 信号ID
            strategy_name: 策略名称
            trade_type: 交易类型（buy/sell）
            token_in_address: 输入代币地址
            token_out_address: 输出代币地址
            amount: 交易金额
            wallet_address: 钱包地址
            trade_provider: 交易提供商
            
        Returns:
            TradeRecord: 创建的交易记录
        """
        logger.info(f"创建交易记录：{trade_type} {amount} {token_in_address} -> {token_out_address}")
        
        trade_record = TradeRecord(
            signal_id=signal_id,
            strategy_name=strategy_name,
            trade_provider=trade_provider,
            trade_type=ModelTradeType(trade_type),
            status=ModelTradeStatus.PENDING,
            token_in_address=token_in_address,
            token_in_amount=amount,
            token_out_address=token_out_address,
            wallet_address=wallet_address,
            auto_trade_manager_version="1.0"
        )
        
        await self.trade_record_dao.save(trade_record)
        logger.info(f"交易记录已创建：ID = {trade_record.id}")
        
        return trade_record
    
    async def update_trade_record_from_execution_result(
        self,
        trade_record: TradeRecord,
        execution_result: TradeExecutionResult
    ) -> TradeRecord:
        """
        根据执行结果更新交易记录
        
        Args:
            trade_record: 要更新的交易记录
            execution_result: 交易执行结果
            
        Returns:
            TradeRecord: 更新后的交易记录
        """
        logger.info(f"更新交易记录 {trade_record.id}：状态 = {execution_result.final_status.value}")
        
        # 更新基本状态
        trade_record.status = ModelTradeStatus(execution_result.final_status.value)
        trade_record.total_execution_time = execution_result.total_execution_time
        trade_record.final_successful_channel = execution_result.successful_channel
        trade_record.total_channels_attempted = len(execution_result.channel_attempts)
        
        # 如果成功，更新成功相关字段
        if execution_result.final_status == TradeStatus.SUCCESS:
            # 找到成功的渠道尝试
            successful_attempt = next(
                (attempt for attempt in execution_result.channel_attempts 
                 if attempt.status == TradeStatus.SUCCESS), 
                None
            )
            
            if successful_attempt:
                trade_record.tx_hash = successful_attempt.tx_hash
                trade_record.executed_at = successful_attempt.completed_at
                trade_record.trade_provider = successful_attempt.channel_type  # 向后兼容
                
                # 更新实际交易数量
                if successful_attempt.actual_amount_in is not None:
                    trade_record.token_in_actual_amount = successful_attempt.actual_amount_in
                if successful_attempt.actual_amount_out is not None:
                    trade_record.token_out_actual_amount = successful_attempt.actual_amount_out
                
                logger.info(f"交易记录 {trade_record.id} 标记为成功，tx_hash: {successful_attempt.tx_hash}, "
                           f"actual_in: {successful_attempt.actual_amount_in}, "
                           f"actual_out: {successful_attempt.actual_amount_out}")
            else:
                logger.info(f"交易记录 {trade_record.id} 标记为成功，但未找到成功的渠道尝试详情")
        
        # 如果失败，更新错误信息
        elif execution_result.final_status == TradeStatus.FAILED:
            trade_record.error_message = execution_result.error_summary
            logger.info(f"交易记录 {trade_record.id} 标记为失败：{execution_result.error_summary}")
        
        await self.trade_record_dao.save(trade_record)
        logger.debug(f"交易记录 {trade_record.id} 已更新")
        
        return trade_record
    
    async def save_channel_attempt_records(
        self,
        trade_record: TradeRecord,
        channel_attempts: List[ChannelAttemptResult],
        trade_type: str,
        token_in_address: str,
        token_out_address: str,
        amount: float,
        signal_id: Optional[PydanticObjectId] = None
    ) -> List[ChannelAttemptRecord]:
        """
        保存渠道尝试记录
        
        Args:
            trade_record: 关联的交易记录
            channel_attempts: 渠道尝试结果列表
            trade_type: 交易类型
            token_in_address: 输入代币地址
            token_out_address: 输出代币地址
            amount: 交易金额
            signal_id: 可选的信号ID
            
        Returns:
            List[ChannelAttemptRecord]: 保存的渠道尝试记录列表
        """
        logger.info(f"保存 {len(channel_attempts)} 个渠道尝试记录，关联交易记录 {trade_record.id}")
        
        saved_records = []
        
        for attempt in channel_attempts:
            channel_record = ChannelAttemptRecord(
                trade_record_id=trade_record.id,
                signal_id=signal_id,
                channel_type=attempt.channel_type,
                attempt_number=attempt.attempt_number,
                status=attempt.status,
                trade_type=trade_type,
                token_in_address=token_in_address,
                token_out_address=token_out_address,
                amount_in=amount,
                tx_hash=attempt.tx_hash,
                error_message=attempt.error_message,
                started_at=attempt.started_at,
                completed_at=attempt.completed_at,
                execution_time=attempt.execution_time,
                actual_amount_in=attempt.actual_amount_in,
                actual_amount_out=attempt.actual_amount_out
            )
            
            await channel_record.save()
            saved_records.append(channel_record)
            
            # 关联到交易记录
            trade_record.add_channel_attempt_record(channel_record.id)
            
            logger.debug(f"渠道尝试记录已保存：{attempt.channel_type} (ID: {channel_record.id})")
        
        # 更新交易记录以保存关联的渠道尝试ID
        await self.trade_record_dao.save(trade_record)
        
        logger.info(f"所有渠道尝试记录已保存并关联到交易记录 {trade_record.id}")
        return saved_records
    
    async def get_trade_record_by_id(self, trade_record_id: PydanticObjectId) -> Optional[TradeRecord]:
        """
        根据ID获取交易记录
        
        Args:
            trade_record_id: 交易记录ID
            
        Returns:
            Optional[TradeRecord]: 交易记录，如果不存在则返回None
        """
        try:
            return await self.trade_record_dao.get_by_id(trade_record_id)
        except Exception as e:
            logger.error(f"获取交易记录 {trade_record_id} 失败: {e}")
            return None
    
    async def get_channel_attempt_records_by_trade_id(
        self, 
        trade_record_id: PydanticObjectId
    ) -> List[ChannelAttemptRecord]:
        """
        根据交易记录ID获取所有渠道尝试记录
        
        Args:
            trade_record_id: 交易记录ID
            
        Returns:
            List[ChannelAttemptRecord]: 渠道尝试记录列表
        """
        try:
            records = await ChannelAttemptRecord.find(
                ChannelAttemptRecord.trade_record_id == trade_record_id
            ).sort("+attempt_number").to_list()
            
            logger.debug(f"找到 {len(records)} 个渠道尝试记录，关联交易记录 {trade_record_id}")
            return records
            
        except Exception as e:
            logger.error(f"获取交易记录 {trade_record_id} 的渠道尝试记录失败: {e}")
            return []
    
    async def get_recent_trade_records(
        self, 
        limit: int = 100,
        status_filter: Optional[ModelTradeStatus] = None,
        strategy_name_filter: Optional[str] = None
    ) -> List[TradeRecord]:
        """
        获取最近的交易记录
        
        Args:
            limit: 限制返回数量
            status_filter: 可选的状态过滤
            strategy_name_filter: 可选的策略名称过滤
            
        Returns:
            List[TradeRecord]: 交易记录列表
        """
        try:
            query = {}
            
            if status_filter:
                query["status"] = status_filter
            
            if strategy_name_filter:
                query["strategy_name"] = strategy_name_filter
            
            records = await self.trade_record_dao.find_by_query(
                query=query,
                sort=[("created_at", -1)],
                limit=limit
            )
            
            logger.debug(f"获取到 {len(records)} 个最近的交易记录")
            return records
            
        except Exception as e:
            logger.error(f"获取最近交易记录失败: {e}")
            return []
    
    async def get_trade_stats(
        self, 
        strategy_name: Optional[str] = None,
        days: int = 7
    ) -> Dict[str, Any]:
        """
        获取交易统计信息
        
        Args:
            strategy_name: 可选的策略名称过滤
            days: 统计天数
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 计算开始时间
            start_time = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            start_time = start_time.replace(day=start_time.day - days + 1)
            
            query = {"created_at": {"$gte": start_time}}
            if strategy_name:
                query["strategy_name"] = strategy_name
            
            # 获取所有符合条件的记录
            records = await self.trade_record_dao.find_by_query(query=query)
            
            # 统计分析
            total_trades = len(records)
            successful_trades = len([r for r in records if r.status == ModelTradeStatus.SUCCESS])
            failed_trades = len([r for r in records if r.status == ModelTradeStatus.FAILED])
            pending_trades = len([r for r in records if r.status == ModelTradeStatus.PENDING])
            skipped_trades = len([r for r in records if r.status == ModelTradeStatus.SKIPPED])
            
            # 多渠道交易统计
            multi_channel_trades = len([r for r in records if r.is_multi_channel_trade()])
            
            # 成功率计算
            success_rate = successful_trades / total_trades if total_trades > 0 else 0.0
            
            # 渠道使用统计
            channel_usage = {}
            for record in records:
                if record.final_successful_channel:
                    channel = record.final_successful_channel
                    channel_usage[channel] = channel_usage.get(channel, 0) + 1
            
            stats = {
                "period_days": days,
                "total_trades": total_trades,
                "successful_trades": successful_trades,
                "failed_trades": failed_trades,
                "pending_trades": pending_trades,
                "skipped_trades": skipped_trades,
                "success_rate": success_rate,
                "multi_channel_trades": multi_channel_trades,
                "multi_channel_rate": multi_channel_trades / total_trades if total_trades > 0 else 0.0,
                "channel_usage": channel_usage,
                "strategy_filter": strategy_name
            }
            
            logger.info(f"交易统计完成：{days}天内共 {total_trades} 笔交易，成功率 {success_rate:.2%}")
            return stats
            
        except Exception as e:
            logger.error(f"获取交易统计失败: {e}")
            return {}
    
    async def cleanup_old_records(self, days_to_keep: int = 30) -> int:
        """
        清理旧的交易记录（可选功能）
        
        Args:
            days_to_keep: 保留天数
            
        Returns:
            int: 删除的记录数量
        """
        try:
            cutoff_time = datetime.now().replace(day=datetime.now().day - days_to_keep)
            
            # 先删除渠道尝试记录
            channel_records = await ChannelAttemptRecord.find(
                ChannelAttemptRecord.created_at < cutoff_time
            ).to_list()
            
            deleted_channel_records = 0
            for record in channel_records:
                await record.delete()
                deleted_channel_records += 1
            
            # 再删除交易记录
            trade_records = await self.trade_record_dao.find_by_query({
                "created_at": {"$lt": cutoff_time}
            })
            
            deleted_trade_records = 0
            for record in trade_records:
                await self.trade_record_dao.delete(record)
                deleted_trade_records += 1
            
            total_deleted = deleted_channel_records + deleted_trade_records
            logger.info(f"清理完成：删除了 {deleted_trade_records} 个交易记录和 {deleted_channel_records} 个渠道尝试记录")
            
            return total_deleted
            
        except Exception as e:
            logger.error(f"清理旧记录失败: {e}")
            return 0 