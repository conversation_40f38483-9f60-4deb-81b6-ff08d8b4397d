from datetime import datetime
from typing import Optional, List
from beanie import Document, Link
from pydantic import Field

class XUser(Document):
    """X 用户模型"""
    
    # 用户基本信息
    username: str = Field(unique=True, index=True)  # 用户名
    display_name: Optional[str] = None  # 显示名称
    description: Optional[str] = None  # 用户简介
    
    # 统计信息
    followers_count: int = Field(default=0)  # 粉丝数
    following_count: int = Field(default=0)  # 关注数
    tweets_count: int = Field(default=0)  # 推文数
    
    # 其他信息
    profile_image_url: Optional[str] = None  # 头像 URL
    verified: bool = Field(default=False)  # 是否认证
    location: Optional[str] = None  # 位置
    
    # 元数据
    created_at: Optional[datetime] = None  # 账号创建时间
    first_seen_at: datetime = Field(default_factory=datetime.utcnow)  # 首次发现时间
    last_updated_at: datetime = Field(default_factory=datetime.utcnow)  # 最后更新时间
    rest_id: str = Field(index=True)  # 用户ID

    class Settings:
        name = "x_users"
        indexes = [
            "username",
            "created_at",
            "first_seen_at",
            "last_updated_at"
        ] 