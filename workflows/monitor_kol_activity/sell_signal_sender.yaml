name: "监控买入信号并生成/处理卖出信号"
description: "定期检查开放的买入 Signal 记录，根据KOL卖出活动或超时条件生成卖出信号数据，然后创建卖出 Signal、更新买入 Signal 状态并发送通知"

nodes:
  - name: "GenerateSellSignalDataNode2" # Renamed for clarity
    node_type: "input"
    interval: 1 # 每秒运行一次 (根据需要调整)
    generate_data: workflows.monitor_kol_activity.sell_signal_handler.generate_sell_signals
    flow_control:
      max_pending_messages: 10 # 根据实际情况调整
      check_interval: 1
      enable_flow_control: true

  - name: "ProcessSellSignalNode2"
    node_type: "storage" # Keeps as storage type for DB interactions
    depend_ons: ["GenerateSellSignalDataNode2"]
    store_data: workflows.monitor_kol_activity.sell_signal_handler.process_sell_signal
    validate: workflows.monitor_kol_activity.sell_signal_handler.validate
