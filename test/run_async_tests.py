#!/usr/bin/env python3
"""
异步测试运行器

用于运行AutoTradeManager的异步测试用例
"""

import asyncio
import unittest
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


class AsyncTestRunner:
    """异步测试运行器"""
    
    @staticmethod
    async def run_async_test(test_instance, test_method_name):
        """运行单个异步测试方法"""
        try:
            test_method = getattr(test_instance, test_method_name)
            if asyncio.iscoroutinefunction(test_method):
                await test_method()
            else:
                test_method()
            return True, None
        except Exception as e:
            return False, e
    
    @staticmethod
    async def run_test_class(test_class):
        """运行整个测试类的所有异步方法"""
        test_instance = test_class()
        
        # 运行setUp
        if hasattr(test_instance, 'setUp'):
            test_instance.setUp()
        
        # 收集所有测试方法
        test_methods = [method for method in dir(test_instance) 
                       if method.startswith('test_')]
        
        results = {}
        
        for method_name in test_methods:
            print(f"Running {test_class.__name__}.{method_name}...")
            success, error = await AsyncTestRunner.run_async_test(test_instance, method_name)
            results[method_name] = (success, error)
            
            if success:
                print(f"  ✅ {method_name} PASSED")
            else:
                print(f"  ❌ {method_name} FAILED: {error}")
        
        # 运行tearDown
        if hasattr(test_instance, 'tearDown'):
            test_instance.tearDown()
        
        return results


async def run_auto_trade_manager_tests():
    """运行AutoTradeManager测试"""
    from test.utils.trading.test_auto_trade_manager import TestAutoTradeManager
    
    print("🚀 Running AutoTradeManager async tests...")
    print("=" * 60)
    
    results = await AsyncTestRunner.run_test_class(TestAutoTradeManager)
    
    # 统计结果
    total_tests = len(results)
    passed_tests = sum(1 for success, _ in results.values() if success)
    failed_tests = total_tests - passed_tests
    
    print("=" * 60)
    print(f"📊 Test Results Summary:")
    print(f"   Total: {total_tests}")
    print(f"   Passed: {passed_tests}")
    print(f"   Failed: {failed_tests}")
    
    if failed_tests > 0:
        print("\n❌ Failed tests:")
        for method_name, (success, error) in results.items():
            if not success:
                print(f"   - {method_name}: {error}")
    
    return failed_tests == 0


async def run_integration_tests():
    """运行集成测试"""
    from test.utils.trading.test_auto_trade_integration import TestAutoTradeIntegration
    
    print("\n🚀 Running AutoTradeManager integration tests...")
    print("=" * 60)
    
    results = await AsyncTestRunner.run_test_class(TestAutoTradeIntegration)
    
    # 统计结果
    total_tests = len(results)
    passed_tests = sum(1 for success, _ in results.values() if success)
    failed_tests = total_tests - passed_tests
    
    print("=" * 60)
    print(f"📊 Integration Test Results Summary:")
    print(f"   Total: {total_tests}")
    print(f"   Passed: {passed_tests}")
    print(f"   Failed: {failed_tests}")
    
    if failed_tests > 0:
        print("\n❌ Failed integration tests:")
        for method_name, (success, error) in results.items():
            if not success:
                print(f"   - {method_name}: {error}")
    
    return failed_tests == 0


async def main():
    """主函数"""
    print("🧪 AutoTradeManager Test Suite")
    print("=" * 60)
    
    # 运行单元测试
    unit_tests_passed = await run_auto_trade_manager_tests()
    
    # 运行集成测试
    integration_tests_passed = await run_integration_tests()
    
    # 总结
    print("\n" + "=" * 60)
    print("🎯 Final Results:")
    print(f"   Unit Tests: {'✅ PASSED' if unit_tests_passed else '❌ FAILED'}")
    print(f"   Integration Tests: {'✅ PASSED' if integration_tests_passed else '❌ FAILED'}")
    
    if unit_tests_passed and integration_tests_passed:
        print("\n🎉 All tests passed! The AutoTradeManager implementation is ready.")
        return 0
    else:
        print("\n💥 Some tests failed. Please review and fix the issues.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code) 