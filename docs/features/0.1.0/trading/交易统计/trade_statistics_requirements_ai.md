# 交易统计分析功能需求规格

## 1. 功能概述

### 1.1 功能描述
实现一个针对trade_records表的交易统计分析功能，提供全面的交易数据分析和可视化报告，帮助用户了解交易表现、策略效果和token表现。

### 1.2 业务背景
- 当前系统已积累大量交易记录数据
- 需要对交易表现进行量化分析
- 用户需要了解不同策略和token的盈利情况
- 需要直观的图表展示交易统计结果
- 需要基于链上验证的真实交易金额进行准确计算

### 1.3 目标用户
- 交易策略分析师
- 系统管理员
- 投资决策者
- 数据分析人员

## 2. 功能需求

### 2.1 核心统计指标

#### 2.1.1 总体统计
- **FR-001**: 系统应统计总交易数（只统计status为success且verification_status为verified的买入卖出配对）
- **FR-002**: 系统应计算总盈利率（基于链上验证的真实交易金额）
- **FR-003**: 系统应计算总胜率（盈利交易数/总交易数）
- **FR-004**: 系统应计算平均盈利率（所有交易对盈利率的平均值）

#### 2.1.2 分Token统计
- **FR-005**: 系统应按token_out_address分组统计各token的交易次数
- **FR-006**: 系统应计算每个token的胜率
- **FR-007**: 系统应计算每个token的平均盈利率
- **FR-008**: 系统应计算每个token的总盈亏金额

#### 2.1.3 分策略统计
- **FR-009**: 系统应按strategy_name分组统计各策略的交易次数
- **FR-010**: 系统应计算每个策略的胜率
- **FR-011**: 系统应计算每个策略的平均盈利率
- **FR-012**: 系统应计算每个策略的总盈亏金额
- **FR-013**: 系统应计算每个策略的最大单笔盈利
- **FR-014**: 系统应计算每个策略的最大单笔亏损

#### 4.1.4 数据验证和处理
- **FR-024**: 只统计`status`为`SUCCESS`的交易记录
- **FR-025**: 只统计`verification_status`为`verified`的交易记录
- **FR-026**: 如果`token_out_verified_amount`为空，系统应：
  1. 调用`TradeRecordVerificationUpdater.verify_single_record()`方法重新获取验证金额
  2. 如果重新获取成功，使用新获取的金额进行统计计算
  3. 如果重新获取失败，跳过该交易对并记录警告日志
  4. 理论上`verification_status`为`verified`的记录应该有`token_out_verified_amount`数据，如果为空则表示数据异常
- **FR-027**: 买入金额使用`token_in_amount`字段
- **FR-028**: 卖出金额使用`token_out_verified_amount`字段（链上验证的真实金额）

### 2.2 数据处理要求

#### 2.2.1 数据筛选
- **FR-016**: 只统计status为"success"的交易记录
- **FR-017**: 只统计verification_status为"verified"的交易记录
- **FR-018**: 通过signal_id匹配买入和卖出交易对
- **FR-019**: 排除无法配对的单独买入或卖出记录
- **FR-020**: 支持按时间范围筛选交易记录

#### 2.2.2 盈利计算（重新设计）
- **FR-021**: 买入金额使用token_in_amount（实际投入的SOL金额）
- **FR-022**: 卖出金额使用token_out_verified_amount（链上验证的实际收到的SOL金额）
- **FR-023**: 盈利率 = (token_out_verified_amount - token_in_amount) / token_in_amount * 100%
- **FR-024**: 盈利金额 = token_out_verified_amount - token_in_amount
- **FR-025**: 胜率 = 盈利交易数 / 总交易数 * 100%
- **FR-026**: 如果token_out_verified_amount为空，则跳过该交易对

### 2.3 可视化要求

#### 2.3.1 表格展示
- **FR-027**: 提供总体统计表格
- **FR-028**: 提供分Token统计表格（包含token地址、交易次数、胜率、盈利率）
- **FR-029**: 提供分策略统计表格（包含策略名、交易次数、胜率、盈利率、最大盈利、最大亏损）
- **FR-030**: 提供盈利排行表（按盈利金额从大到小排序）
- **FR-031**: 提供亏损排行表（按亏损金额从接近0到最亏排序）
- **FR-032**: 表格支持按盈利率、胜率、交易次数排序

#### 2.3.2 图表展示
- **FR-033**: 提供总体盈利率饼图
- **FR-034**: 提供策略胜率对比柱状图
- **FR-035**: 提供Token盈利率分布图
- **FR-036**: 提供交易时间分布图
- **FR-037**: 提供累计盈亏趋势图

#### 2.3.3 多格式报告
- **FR-038**: 支持生成HTML格式报告（包含所有表格和图表）
- **FR-039**: 支持生成JSON格式数据（用于前端展示）
- **FR-040**: HTML报告具有良好的样式和布局
- **FR-041**: 支持指定报告输出位置
- **FR-042**: JSON格式直接返回数据，不写入文件

## 3. 非功能需求

### 3.1 性能要求
- **NFR-001**: 处理10000条交易记录的统计时间不超过30秒
- **NFR-002**: 内存使用不超过500MB
- **NFR-003**: 支持增量统计以提高性能

### 3.2 可用性要求
- **NFR-004**: 提供命令行接口调用统计功能
- **NFR-005**: 支持配置统计参数（时间范围、输出路径、输出格式等）
- **NFR-006**: 提供详细的错误信息和日志

### 3.3 扩展性要求
- **NFR-007**: 统计模块应易于扩展新的指标
- **NFR-008**: 图表类型应易于增加
- **NFR-009**: 支持多种输出格式扩展

## 4. 数据模型

### 4.1 统计结果数据结构

#### 4.1.1 总体统计
```python
class OverallStats(BaseModel):
    total_trades: int                    # 总交易数
    total_profit_rate: float            # 总盈利率(%)
    total_win_rate: float               # 总胜率(%)
    avg_profit_rate: float              # 平均盈利率(%)
    total_profit_amount: float          # 总盈亏金额
    profitable_trades: int              # 盈利交易数
    loss_trades: int                    # 亏损交易数
    max_single_profit: float            # 最大单笔盈利
    max_single_loss: float              # 最大单笔亏损
```

#### 4.1.2 Token统计
```python
class TokenStats(BaseModel):
    token_address: str                   # Token地址
    token_symbol: Optional[str]          # Token符号
    trade_count: int                     # 交易次数
    win_rate: float                      # 胜率(%)
    avg_profit_rate: float              # 平均盈利率(%)
    total_profit_amount: float          # 总盈亏金额
    profitable_trades: int              # 盈利交易数
    loss_trades: int                    # 亏损交易数
```

#### 4.1.3 策略统计
```python
class StrategyStats(BaseModel):
    strategy_name: str                   # 策略名称
    trade_count: int                     # 交易次数
    win_rate: float                      # 胜率(%)
    avg_profit_rate: float              # 平均盈利率(%)
    total_profit_amount: float          # 总盈亏金额
    profitable_trades: int              # 盈利交易数
    loss_trades: int                    # 亏损交易数
    max_single_profit: float            # 最大单笔盈利
    max_single_loss: float              # 最大单笔亏损
```

### 4.2 交易对数据结构
```python
class TradePair(BaseModel):
    signal_id: str                       # 信号ID
    strategy_name: str                   # 策略名称
    token_address: str                   # Token地址
    buy_record: TradeRecord             # 买入记录
    sell_record: TradeRecord            # 卖出记录
    profit_rate: float                  # 盈利率(%)
    profit_amount: float                # 盈亏金额（SOL）
    is_profitable: bool                 # 是否盈利
    buy_amount_sol: float               # 买入金额（SOL）
    sell_amount_sol: float              # 卖出金额（SOL）
```

### 4.3 排行榜数据结构
```python
class ProfitRanking(BaseModel):
    """盈利排行"""
    signal_id: str
    strategy_name: str
    token_address: str
    profit_amount: float
    profit_rate: float
    buy_time: datetime
    sell_time: datetime

class LossRanking(BaseModel):
    """亏损排行"""
    signal_id: str
    strategy_name: str
    token_address: str
    loss_amount: float
    loss_rate: float
    buy_time: datetime
    sell_time: datetime
```

## 5. 技术约束

### 5.1 系统约束
- 基于现有的MongoDB数据库
- 使用Python进行数据分析
- 使用matplotlib/plotly进行图表生成
- 使用Jinja2进行HTML模板渲染
- 依赖链上验证的交易金额数据

### 5.2 数据约束
- 只处理已验证的交易记录
- 确保买入卖出记录的正确配对
- 处理缺失验证金额的情况
- 保证计算结果的准确性

## 6. 接口规范

### 6.1 命令行接口
```bash
# 生成HTML报告
python -m utils.trading.trade_statistics --format html --output reports/trade_stats.html

# 生成JSON数据
python -m utils.trading.trade_statistics --format json

# 指定时间范围
python -m utils.trading.trade_statistics --start-date 2024-01-01 --end-date 2024-12-31

# 指定策略过滤
python -m utils.trading.trade_statistics --strategy "gmgn_smart_money"
```

### 6.2 编程接口
```python
from utils.trading.trade_statistics import TradeStatisticsAnalyzer

analyzer = TradeStatisticsAnalyzer()

# 生成统计数据
stats = await analyzer.generate_statistics(
    start_date=datetime(2024, 1, 1),
    end_date=datetime(2024, 12, 31),
    strategy_filter=None,
    token_filter=None
)

# 生成HTML报告
await analyzer.generate_html_report(stats, "reports/trade_stats.html")

# 生成JSON数据
json_data = await analyzer.generate_json_report(stats)
```

## 7. 验收标准

### 7.1 功能验收
- [ ] 能够正确统计已验证的交易指标
- [ ] 能够按Token和策略分组统计
- [ ] 能够生成完整的HTML报告和JSON数据
- [ ] 图表显示正确且美观
- [ ] 盈利亏损排行表准确

### 7.2 数据准确性验收
- [ ] 基于链上验证金额的盈利率计算准确
- [ ] 胜率计算准确
- [ ] 交易配对正确
- [ ] 统计数据一致性
- [ ] 最大盈利亏损计算准确

### 7.3 性能验收
- [ ] 处理大量数据时性能满足要求
- [ ] 内存使用合理
- [ ] 报告生成速度满足要求

## 8. 风险评估

### 8.1 数据风险
- **风险**: 链上验证金额数据不完整
- **缓解**: 增加数据验证和跳过机制

### 8.2 性能风险
- **风险**: 大量数据处理导致性能问题
- **缓解**: 实施分批处理和缓存机制

### 8.3 计算风险
- **风险**: 基于新字段的盈利率计算错误
- **缓解**: 增加单元测试和数据验证

## 9. 实施优先级

### 9.1 高优先级
1. 基于验证金额的统计功能实现
2. 交易配对逻辑更新
3. 新的盈利率和胜率计算
4. 基础HTML和JSON报告生成

### 9.2 中优先级
1. 图表可视化
2. 分Token和策略统计（含最大盈利亏损）
3. 盈利亏损排行表
4. 命令行接口

### 9.3 低优先级
1. 高级图表类型
2. 性能优化
3. 扩展功能 