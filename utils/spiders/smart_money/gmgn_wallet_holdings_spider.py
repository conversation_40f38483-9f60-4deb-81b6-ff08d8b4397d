import asyncio
import logging
from typing import Dict, Any, List
import json
from utils.spiders.smart_money import BasicSpider

logger = logging.getLogger(__name__)

class GmgnWalletHoldingsSpider(BasicSpider):
    """GMGN钱包持仓信息爬虫
    
    用于获取钱包的持仓信息，包括：
    - 代币基本信息
    - 持仓数量和价值
    - 收益信息
    - 交易统计
    """
    
    BASE_URL = "https://gmgn.ai/api/v1/wallet_holdings"
    
    def __init__(self, max_retries: int = 3, retry_interval: float = 1.0):
        super().__init__(max_retries=max_retries, retry_interval=retry_interval)
        self.headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-TW;q=0.6',
            'cache-control': 'no-cache',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://gmgn.ai/sol/address/dDO6ZdYE_DgAhccuXs7mjbvY3EXX6XLkBB5WStESQyLGQ9vLL1Z5x',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
    
    async def setup(self):
        """设置爬虫，初始化会话并更新头信息"""
        await self.init_sessions()
        # 更新会话头信息
        self.session.headers.update(self.headers)
        # 设置浏览器模拟
        self.session.impersonate = "chrome110"  # 模拟Chrome浏览器
    
    async def get_wallet_holdings(
        self,
        chain: str,
        wallet_address: str,
        limit: int = 50,
        orderby: str = "last_active_timestamp",
        direction: str = "desc",
        showsmall: bool = False,
        sellout: bool = False,
        hide_abnormal: bool = False,
        cursor: str = None  # 分页游标
    ) -> List[Dict[str, Any]]:
        """
        获取钱包持仓信息
        
        Args:
            chain: 链名称
            wallet_address: 钱包地址
            limit: 返回数量限制
            orderby: 排序字段
            direction: 排序方向(asc/desc)
            showsmall: 是否显示小额持仓
            sellout: 是否显示已卖出
            hide_abnormal: 是否隐藏异常数据
            cursor: 分页游标
            
        Returns:
            List[Dict[str, Any]]: 钱包持仓信息列表
        """
        try:
                
            url = f"{self.BASE_URL}/{chain}/{wallet_address}"
            logger.info(f"正在获取钱包 {wallet_address} 的持仓信息...")
            
            params = {
                'device_id': '65990bc3-0a13-4d99-8c66-eeea94916b0f',
                'client_id': 'gmgn_web_2025.0307.143957',
                'from_app': 'gmgn',
                'app_ver': '2025.0307.143957',
                'tz_name': 'Asia/Shanghai',
                'tz_offset': '28800',
                'app_lang': 'zh-CN',
                'limit': limit,
                'orderby': orderby,
                'direction': direction,
                'showsmall': str(showsmall).lower(),
                'sellout': str(sellout).lower(),
                'hide_abnormal': str(hide_abnormal).lower(),
            }
            if cursor:
                params['cursor'] = cursor
            
            # 使用带有重试机制的get方法
            response = await self.get(url, params=params)
            
            # 记录响应状态和内容
            logger.debug(f"API响应状态码: {response.status_code}")
            logger.debug(f"API响应内容: {response.text[:500]}...")  # 只记录前500个字符
            
            # 检查响应状态
            if response.status_code != 200:
                logger.error(f"获取钱包持仓信息失败: HTTP {response.status_code}")
                logger.error(f"错误响应: {response.text}")
                return []
                
            # 解析响应数据
            try:
                data = response.json()
                logger.debug(f"解析后的API响应: {data}")
                
                # 检查API响应格式
                if not isinstance(data, dict):
                    logger.error(f"API响应格式错误，期望dict类型，实际为: {type(data)}")
                    return []
                    
                # 检查API响应状态
                if data.get('code') != 0:  # 0 表示成功
                    logger.error(f"API返回错误: {data.get('message', '未知错误')}")
                    return []
                    
                # 提取实际数据
                result = data.get('data', {})
                if not result:
                    logger.warning("API响应中没有data字段")
                    return []
                
                holdings = result.get('holdings', [])
                
                # 检查是否还有下一页
                if result.get('next'):
                    next_cursor = result.get('next')
                    await asyncio.sleep(0.5)
                    if next_cursor:
                        next_page_holdings = await self.get_wallet_holdings(
                            chain, wallet_address, limit, orderby, direction, 
                            showsmall, sellout, hide_abnormal, next_cursor
                        )
                        holdings.extend(next_page_holdings)
                    
                logger.info(f"成功获取钱包 {wallet_address} 的持仓信息，共 {len(holdings)} 条记录")
                return holdings
                
            except json.JSONDecodeError as e:
                logger.error(f"解析API响应JSON失败: {str(e)}")
                logger.error(f"原始响应: {response.text}")
                return []
                
        except Exception as e:
            logger.error(f"获取钱包持仓信息时发生错误: {str(e)}")
            return []

async def main():
    spider = GmgnWalletHoldingsSpider()
    data = await spider.get_wallet_holdings(
        "sol",
        "HXkaN7TAQYXsybY1UHLrn18cVa9jgM3mbReasbAoSjTv"
    )
    print(json.dumps(data, indent=2))
    # 关闭爬虫会话连接
    await spider.close()

if __name__ == "__main__":
    # 使用asyncio运行异步主函数
    asyncio.run(main()) 