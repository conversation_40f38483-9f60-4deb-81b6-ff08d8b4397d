"""重试上下文单元测试"""

import unittest
from unittest.mock import patch, MagicMock, ANY, PropertyMock
from datetime import datetime, timedelta
# 导入真实的 SlippageAdjustmentRecord 以用作 spec
from models.slippage_retry import SlippageRetryConfig, SlippageAdjustmentReason, RetryDecision, SlippageAdjustmentRecord
from utils.trading.slippage_retry.retry_context import RetryContext


# 辅助函数来创建带有属性的 mock SlippageAdjustmentRecord
def create_mock_adjustment_record(**kwargs):
    mock = MagicMock() 
    mock.reason = kwargs.get('reason')
    mock.trade_type = kwargs.get('trade_type')
    mock.previous_slippage = kwargs.get('previous_slippage')
    mock.new_slippage = kwargs.get('new_slippage')
    mock.increment_applied = kwargs.get('increment_applied')
    mock.original_error_message = kwargs.get('original_error_message')
    mock.retry_attempt = kwargs.get('retry_attempt')
    mock.max_slippage_limit = kwargs.get('max_slippage_limit')
    mock.increment_step = kwargs.get('increment_step')
    mock.adjustment_time = kwargs.get('adjustment_time', datetime.now())
    tt = kwargs.get('trade_type')
    ps = kwargs.get('previous_slippage')
    ns = kwargs.get('new_slippage')
    ra = kwargs.get('retry_attempt')
    if tt and ps is not None and ns is not None and ra is not None:
        summary_val = f"{tt.upper()} 滑点从 {ps}% 调整至 {ns}% (第{ra}次重试)"
    else:
        summary_val = "Mocked Adjustment Summary (default)"
    mock.adjustment_summary = summary_val
    return mock

class TestRetryContext(unittest.TestCase):
    """重试上下文测试类"""
    
    def setUp(self) -> None:
        """测试前准备"""
        self.config = SlippageRetryConfig(
            enabled=True,
            increment_percentage=0.5,
            max_slippage_percentage=5.0,
            retry_delay_seconds=0.1,
            config_source="test_source"
        )
        
        self.context = RetryContext(
            trade_type="buy",
            initial_slippage=1.0,
            config=self.config,
            max_retries=3
        )
    
    def test_context_initialization(self) -> None:
        """测试上下文初始化"""
        self.assertEqual(self.context.trade_type, "buy")
        self.assertEqual(self.context.initial_slippage, 1.0)
        self.assertEqual(self.context.current_slippage, 1.0)
        self.assertEqual(self.context.retry_count, 0)
        self.assertEqual(self.context.max_retries, 3)
        self.assertIsNotNone(self.context.start_time)
        self.assertIsNone(self.context.last_attempt_time)
        self.assertEqual(len(self.context.adjustment_history), 0)
        self.assertEqual(len(self.context.decision_history), 0)
    
    @patch('utils.trading.slippage_retry.retry_context.SlippageAdjustmentRecord')
    def test_record_slippage_adjustment(self, MockSlippageAdjustmentRecord: MagicMock) -> None:
        """测试记录滑点调整"""
        MockSlippageAdjustmentRecord.side_effect = create_mock_adjustment_record
        
        current_slippage_before_adjustment = self.context.current_slippage
        new_slippage_value = 1.5
        
        # 第一次调用 record_slippage_adjustment 时，retry_count 还是0，所以记录中的 retry_attempt 是 1
        expected_retry_attempt = self.context.retry_count + 1

        adjustment = self.context.record_slippage_adjustment(
            new_slippage=new_slippage_value,
            reason=SlippageAdjustmentReason.SLIPPAGE_ERROR,
            error_message="Test error"
        )
        
        self.assertEqual(self.context.current_slippage, new_slippage_value)
        self.assertEqual(self.context.total_adjustments, 1)
        self.assertEqual(len(self.context.adjustment_history), 1)
        
        # 验证 MockSlippageAdjustmentRecord 是否以正确的参数被调用
        args, kwargs = MockSlippageAdjustmentRecord.call_args
        self.assertEqual(kwargs.get('reason'), SlippageAdjustmentReason.SLIPPAGE_ERROR)
        self.assertEqual(kwargs.get('new_slippage'), new_slippage_value)
        self.assertEqual(kwargs.get('previous_slippage'), current_slippage_before_adjustment)
        self.assertEqual(kwargs.get('retry_attempt'), expected_retry_attempt)
        self.assertEqual(kwargs.get('trade_type'), self.context.trade_type)
        self.assertEqual(kwargs.get('max_slippage_limit'), self.config.max_slippage_percentage)
        self.assertEqual(kwargs.get('increment_step'), self.config.increment_percentage)
        self.assertIsInstance(kwargs.get('adjustment_time'), datetime)
        self.assertEqual(kwargs.get('original_error_message'), "Test error")
    
    def test_increment_retry_count(self) -> None:
        """测试增加重试计数"""
        initial_count = self.context.retry_count
        
        result = self.context.increment_retry_count()
        self.assertEqual(self.context.retry_count, initial_count + 1)
        self.assertEqual(result, initial_count + 1)
        self.assertIsNotNone(self.context.last_attempt_time)
        
        result2 = self.context.increment_retry_count()
        self.assertEqual(self.context.retry_count, initial_count + 2)
        self.assertEqual(result2, initial_count + 2)
    
    @patch('utils.trading.slippage_retry.retry_context.SlippageAdjustmentRecord')
    def test_get_total_slippage_increase(self, MockSlippageAdjustmentRecord: MagicMock) -> None:
        """测试获取总滑点增加量"""
        MockSlippageAdjustmentRecord.side_effect = create_mock_adjustment_record

        self.context.record_slippage_adjustment(1.5, SlippageAdjustmentReason.SLIPPAGE_ERROR)
        self.assertAlmostEqual(self.context.get_total_slippage_increase(), 0.5)
        
        self.context.record_slippage_adjustment(2.5, SlippageAdjustmentReason.PRICE_IMPACT_ERROR)
        self.assertAlmostEqual(self.context.get_total_slippage_increase(), 1.5)
    
    def test_can_adjust_slippage(self) -> None:
        """测试是否能调整滑点"""
        # 正常情况下可以调整
        self.assertTrue(self.context.can_adjust_slippage())
        
        # 达到上限后不能调整
        self.context.current_slippage = self.config.max_slippage_percentage
        self.assertFalse(self.context.can_adjust_slippage())
        
        # 配置禁用时不能调整
        self.config.enabled = False
        self.context.current_slippage = 1.0
        self.assertFalse(self.context.can_adjust_slippage())
    
    def test_is_retry_exhausted(self) -> None:
        """测试是否耗尽重试次数"""
        # 未达到上限
        self.context.retry_count = 2
        self.assertFalse(self.context.is_retry_exhausted())
        
        # 达到上限
        self.context.retry_count = 3
        self.assertTrue(self.context.is_retry_exhausted())
        
        # 超过上限
        self.context.retry_count = 4
        self.assertTrue(self.context.is_retry_exhausted())
    
    def test_get_retry_duration(self) -> None:
        """测试获取重试持续时间"""
        # 设置开始时间为1秒前
        self.context.start_time = datetime.utcnow() - timedelta(seconds=1)
        
        duration = self.context.get_retry_duration()
        
        # 应该大约是1秒，允许小的误差
        self.assertGreaterEqual(duration, 0.9)
        self.assertLessEqual(duration, 1.5)
    
    def test_get_next_slippage_preview(self) -> None:
        """测试预览下次滑点调整"""
        # 正常情况下可以预览
        next_slippage = self.context.get_next_slippage_preview()
        self.assertEqual(next_slippage, 1.5)  # 1.0 + 0.5
        
        # 接近上限时
        self.context.current_slippage = 4.8
        next_slippage = self.context.get_next_slippage_preview()
        self.assertEqual(next_slippage, 5.0)  # 受max_slippage_percentage限制
        
        # 达到上限时
        self.context.current_slippage = 5.0
        next_slippage = self.context.get_next_slippage_preview()
        self.assertIsNone(next_slippage)
    
    @patch('utils.trading.slippage_retry.retry_context.SlippageAdjustmentRecord')
    def test_get_summary(self, MockSlippageAdjustmentRecord: MagicMock) -> None:
        """测试获取摘要"""
        MockSlippageAdjustmentRecord.side_effect = create_mock_adjustment_record

        self.context.record_slippage_adjustment(1.2, SlippageAdjustmentReason.SLIPPAGE_ERROR)
        self.context.increment_retry_count() # retry_count becomes 1, total_adjustments = 1
        
        self.context.record_slippage_adjustment(1.8, SlippageAdjustmentReason.PRICE_IMPACT_ERROR) 
        self.context.increment_retry_count() # retry_count becomes 2, total_adjustments = 2
        
        self.context.record_slippage_adjustment(2.0, SlippageAdjustmentReason.INSUFFICIENT_OUTPUT)
        self.context.increment_retry_count() # retry_count becomes 3, total_adjustments = 3

        summary = self.context.get_summary()
        
        self.assertEqual(summary["trade_type"], "buy")
        self.assertEqual(summary["retry_count"], 3)
        self.assertEqual(summary["max_retries"], 3)
        self.assertEqual(summary["initial_slippage"], 1.0)
        self.assertEqual(summary["current_slippage"], 2.0) 
        self.assertAlmostEqual(summary["total_slippage_increase"], 1.0) 
        self.assertEqual(summary["total_adjustments"], 3)
        self.assertFalse(summary["slippage_at_limit"]) 
        self.assertTrue(summary["config_enabled"])
        self.assertEqual(summary["max_slippage_limit"], self.config.max_slippage_percentage) # 使用配置中的值进行比较
        self.assertTrue(summary["can_adjust_more"]) # 2.0 < 5.0，所以可以调整
        self.assertTrue(summary["retry_exhausted"]) # retry_count (3) >= max_retries (3)
    
    @patch('utils.trading.slippage_retry.retry_context.SlippageAdjustmentRecord')
    def test_detailed_report(self, MockSlippageAdjustmentRecord: MagicMock) -> None:
        """测试详细报告"""
        MockSlippageAdjustmentRecord.side_effect = create_mock_adjustment_record
        
        # 第1次调整 (retry_attempt 将是 1)
        self.context.record_slippage_adjustment(1.5, SlippageAdjustmentReason.SLIPPAGE_ERROR, error_message="error1")
        self.context.increment_retry_count() # retry_count 变为 1
        
        mock_decision_1 = MagicMock(spec=RetryDecision)
        type(mock_decision_1).decision_summary = PropertyMock(return_value="继续重试并调整滑点: 滑点不足")
        self.context.record_retry_decision(mock_decision_1)

        # 第2次调整 (retry_attempt 将是 2, 因为 retry_count 是 1)
        self.context.record_slippage_adjustment(2.0, SlippageAdjustmentReason.PRICE_IMPACT_ERROR, error_message="error2")
        self.context.increment_retry_count() # retry_count 变为 2

        report = self.context.get_detailed_report()

        self.assertIn("=== BUY 重试上下文报告 ===", report)
        self.assertIn("滑点从 1.0% 调整至 1.5% (第1次重试)", report) # 检查 mock summary 是否在报告中
        self.assertIn("滑点从 1.5% 调整至 2.0% (第2次重试)", report)
        self.assertIn("继续重试并调整滑点: 滑点不足", report)
    
    @patch('utils.trading.slippage_retry.retry_context.SlippageAdjustmentRecord')
    def test_slippage_limit_detection(self, MockSlippageAdjustmentRecord: MagicMock) -> None:
        """测试滑点上限检测"""
        mock_instance = MagicMock()
        mock_instance.adjustment_summary = "Mocked Summary"
        MockSlippageAdjustmentRecord.return_value = mock_instance
        
        self.context.record_slippage_adjustment(self.config.max_slippage_percentage, SlippageAdjustmentReason.MANUAL_ADJUSTMENT)
        self.assertTrue(self.context.slippage_at_limit)
        self.assertFalse(self.context.can_adjust_slippage())
    
    @patch('utils.trading.slippage_retry.retry_context.SlippageAdjustmentRecord')
    def test_context_edge_cases(self, MockSlippageAdjustmentRecord: MagicMock) -> None:
        """测试边界情况"""
        MockSlippageAdjustmentRecord.side_effect = create_mock_adjustment_record
        MockSlippageAdjustmentRecord.reset_mock() # 确保 mock 调用计数器干净

        # 禁用配置下的调整
        disabled_config = SlippageRetryConfig(enabled=False, increment_percentage=0.1, max_slippage_percentage=1.0, retry_delay_seconds=0)
        disabled_context = RetryContext("sell", 0.5, disabled_config)
        self.assertFalse(disabled_context.can_adjust_slippage())
        
        call_count_before_disabled = MockSlippageAdjustmentRecord.call_count
        disabled_context.record_slippage_adjustment(0.6, SlippageAdjustmentReason.MANUAL_ADJUSTMENT)
        self.assertEqual(MockSlippageAdjustmentRecord.call_count, call_count_before_disabled + 1)
        args_disabled, kwargs_disabled = MockSlippageAdjustmentRecord.call_args
        self.assertEqual(kwargs_disabled.get('new_slippage'), 0.6)
        self.assertEqual(kwargs_disabled.get('trade_type'), "sell")
        self.assertEqual(disabled_context.current_slippage, 0.6) 
        
        # 最大重试次数为0
        # 重置 mock 以隔离此部分测试的调用计数
        MockSlippageAdjustmentRecord.reset_mock() 
        zero_retry_context = RetryContext("buy", 1.0, self.config, max_retries=0)
        self.assertTrue(zero_retry_context.is_retry_exhausted())
        
        zero_retry_context.record_slippage_adjustment(1.5, SlippageAdjustmentReason.SLIPPAGE_ERROR)
        MockSlippageAdjustmentRecord.assert_called_once() # 确保只调用了一次
        
        args_zero, kwargs_zero = MockSlippageAdjustmentRecord.call_args 
        self.assertEqual(kwargs_zero.get('new_slippage'), 1.5)
        self.assertEqual(kwargs_zero.get('trade_type'), "buy")
        self.assertEqual(kwargs_zero.get('previous_slippage'), 1.0)
        self.assertEqual(kwargs_zero.get('retry_attempt'), 1) # context.retry_count is 0, so attempt is 1

    @patch('utils.trading.slippage_retry.retry_context.SlippageAdjustmentRecord')
    def test_adjustment_reason_handling(self, MockSlippageAdjustmentRecord: MagicMock) -> None:
        """测试调整原因处理"""
        MockSlippageAdjustmentRecord.side_effect = create_mock_adjustment_record
        
        reasons = [
            SlippageAdjustmentReason.SLIPPAGE_ERROR,
            SlippageAdjustmentReason.PRICE_IMPACT_ERROR,
            SlippageAdjustmentReason.INSUFFICIENT_OUTPUT
        ]
        # 为这个测试方法创建一个独立的 context 实例，或者确保彻底重置 self.context 的状态
        # 以避免不同 subtest 之间的干扰，特别是对于 adjustment_history 和 retry_count
        # 这里我们选择在每次 subtest 迭代前重置 mock 的调用记录
        
        # 保存并恢复 context 的部分状态，或者使用全新的 context
        original_current_slippage = self.context.current_slippage
        original_retry_count = self.context.retry_count
        original_adjustment_history = list(self.context.adjustment_history) # 浅拷贝

        for reason_val in reasons:
            with self.subTest(reason=reason_val):
                # 为了隔离 subtest，重置/设置 context 状态和 mock 状态
                self.context.current_slippage = original_current_slippage # 或一个已知值
                self.context.retry_count = original_retry_count # 或一个已知值
                self.context.adjustment_history = [] # 清空历史，以便 call_args 对应当前 subtest
                MockSlippageAdjustmentRecord.reset_mock()

                current_slippage_before_call = self.context.current_slippage
                new_slip = current_slippage_before_call + 0.1 
                expected_retry_attempt = self.context.retry_count + 1
                
                self.context.record_slippage_adjustment(new_slip, reason_val)
                
                MockSlippageAdjustmentRecord.assert_called_once()
                called_args, called_kwargs = MockSlippageAdjustmentRecord.call_args 
                self.assertEqual(called_kwargs.get('reason'), reason_val) 
                self.assertEqual(called_kwargs.get('new_slippage'), new_slip)
                self.assertEqual(called_kwargs.get('retry_attempt'), expected_retry_attempt)
                self.assertEqual(called_kwargs.get('previous_slippage'), current_slippage_before_call)
        
        # 恢复 context 状态
        self.context.current_slippage = original_current_slippage
        self.context.retry_count = original_retry_count
        self.context.adjustment_history = original_adjustment_history

    @patch('utils.trading.slippage_retry.retry_context.SlippageAdjustmentRecord')
    def test_slippage_adjustment_calculation(self, MockSlippageAdjustmentRecord: MagicMock) -> None:
        """测试滑点调整计算"""
        MockSlippageAdjustmentRecord.side_effect = create_mock_adjustment_record
        # 创建一个全新的 context 实例，以确保此测试的完全隔离性
        # 或者在测试开始前和结束后仔细重置 self.context 和 MockSlippageAdjustmentRecord 的状态
        # 这里为了简单，我们直接重置 mock
        MockSlippageAdjustmentRecord.reset_mock()
        
        # 重置或重新初始化 self.context 以确保初始状态
        self.context = RetryContext(
            trade_type="buy",
            initial_slippage=1.0,
            config=self.config,
            max_retries=3
        )

        initial_slippage_val = self.context.current_slippage 
        new_slippage_val1 = 1.8
        expected_retry_attempt1 = self.context.retry_count + 1 

        self.context.record_slippage_adjustment(
            new_slippage=new_slippage_val1,
            reason=SlippageAdjustmentReason.MANUAL_ADJUSTMENT
        )
        MockSlippageAdjustmentRecord.assert_called_once()
        args1, kwargs1 = MockSlippageAdjustmentRecord.call_args
        self.assertEqual(kwargs1.get('previous_slippage'), initial_slippage_val)
        self.assertEqual(kwargs1.get('new_slippage'), new_slippage_val1)
        self.assertAlmostEqual(kwargs1.get('increment_applied'), new_slippage_val1 - initial_slippage_val)
        self.assertEqual(kwargs1.get('retry_attempt'), expected_retry_attempt1)

        # 为下一次调用重置 mock
        MockSlippageAdjustmentRecord.reset_mock()
        self.context.increment_retry_count() 
        
        current_slippage_val2 = self.context.current_slippage 
        new_slippage_val2 = 2.5
        expected_retry_attempt2 = self.context.retry_count + 1 

        self.context.record_slippage_adjustment(
            new_slippage=new_slippage_val2,
            reason=SlippageAdjustmentReason.MARKET_CONDITION
        )
        MockSlippageAdjustmentRecord.assert_called_once()
        args2, kwargs2 = MockSlippageAdjustmentRecord.call_args
        self.assertEqual(kwargs2.get('previous_slippage'), current_slippage_val2)
        self.assertEqual(kwargs2.get('new_slippage'), new_slippage_val2)
        self.assertAlmostEqual(kwargs2.get('increment_applied'), new_slippage_val2 - current_slippage_val2)
        self.assertEqual(kwargs2.get('retry_attempt'), expected_retry_attempt2)


if __name__ == '__main__':
    unittest.main() 