# 交易统计飞书消息发送器技术实现方案

## 1. 架构设计

### 1.1 整体架构
```
飞书消息发送系统
├── 消息发送层 (Message Sending Layer)
│   ├── FeishuNotifier - 飞书消息发送器
│   └── MessageSender - 消息发送器基类扩展
├── 摘要生成层 (Summary Generation Layer)
│   ├── TradingSummaryGenerator - 交易摘要生成器
│   └── SummaryFormatter - 摘要格式化器
├── 文件管理层 (File Management Layer)
│   ├── ReportUploader - 报告上传器
│   └── URLGenerator - URL生成器
└── 配置管理层 (Configuration Layer)
    ├── FeishuConfig - 飞书配置管理
    └── EnvironmentConfig - 环境变量配置
```

### 1.2 模块依赖关系
- CLI → FeishuNotifier → TradingSummaryGenerator
- FeishuNotifier → MessageSender, ReportUploader
- TradingSummaryGenerator → TradeStatisticsAnalyzer
- ReportUploader → URLGenerator

## 2. 核心模块设计

### 2.1 飞书消息发送器

#### 2.1.1 扩展现有MessageSender架构
```python
# utils/message_sender/message_sender.py (扩展)

class MessageChannel(Enum):
    """消息频道"""
    TELEGRAM = "telegram"
    FEISHU = "feishu"  # 新增

class FeishuMessageSender(MessageSender):
    """飞书消息发送器"""
    
    def __init__(self, webhook_url: str):
        self.webhook_url = webhook_url
        super().__init__()
    
    async def send_message_to_webhook(
        self, 
        message: Dict[str, Any], 
        webhook_url: str = None
    ) -> bool:
        """发送消息到飞书Webhook"""
        pass
    
    async def send_card_message(
        self,
        card: Dict[str, Any],
        webhook_url: str = None
    ) -> bool:
        """发送卡片消息"""
        pass
    
    async def send_message_to_user(self, message: str, user_id: str, parse_mode: str = "text") -> bool:
        """发送消息给用户（Webhook模式不支持）"""
        raise NotImplementedError("飞书Webhook模式不支持直接发送给用户")
    
    async def send_message_to_channel(self, message: str, channel_id: str, parse_mode: str = "text") -> bool:
        """发送消息给频道（使用Webhook）"""
        return await self.send_message_to_webhook({"msg_type": "text", "content": {"text": message}})
```

#### 2.1.2 飞书专用通知器
```python
# utils/trading/statistics/feishu_notifier.py

from typing import Dict, Any, Optional, List
import aiohttp
import asyncio
import logging
from datetime import datetime, timedelta
from pydantic import BaseModel

class FeishuCard(BaseModel):
    """飞书卡片模型"""
    config: Dict[str, Any]
    header: Dict[str, Any]
    elements: List[Dict[str, Any]]

class TradingSummary(BaseModel):
    """交易摘要模型"""
    period: str                    # daily/weekly
    start_date: datetime
    end_date: datetime
    total_trades: int
    win_rate: float
    profit_rate: float
    profit_amount: float
    top_strategies: List[Dict[str, Any]]
    best_trade: Optional[Dict[str, Any]]
    worst_trade: Optional[Dict[str, Any]]
    report_url: Optional[str]
    generation_time: datetime

class FeishuNotifierConfig(BaseModel):
    """飞书通知器配置"""
    webhook_url: str
    report_base_url: Optional[str] = None
    upload_path: Optional[str] = None
    retry_times: int = 3
    timeout: int = 30
    enable_card_message: bool = True

class FeishuNotifier:
    """飞书消息通知器"""
    
    def __init__(self, config: FeishuNotifierConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    async def send_trading_summary(
        self, 
        summary: TradingSummary
    ) -> bool:
        """发送交易统计摘要"""
        try:
            if self.config.enable_card_message:
                message = self._generate_card_message(summary)
            else:
                message = self._generate_text_message(summary)
            
            return await self._send_message_with_retry(message)
        
        except Exception as e:
            self.logger.error(f"发送飞书消息失败: {e}")
            return False
    
    def _generate_card_message(self, summary: TradingSummary) -> Dict[str, Any]:
        """生成卡片消息"""
        period_text = "日报" if summary.period == "daily" else "周报"
        date_text = summary.start_date.strftime("%Y-%m-%d")
        if summary.period == "weekly":
            date_text = f"{summary.start_date.strftime('%Y-%m-%d')} 至 {summary.end_date.strftime('%Y-%m-%d')}"
        
        # 构建卡片头部
        header = {
            "template": "blue",
            "title": {
                "content": f"📊 交易{period_text} - {date_text}",
                "tag": "plain_text"
            }
        }
        
        # 构建卡片元素
        elements = []
        
        # 总体统计
        overall_stats = {
            "tag": "div",
            "text": {
                "content": f"📈 **总体表现**\n"
                          f"• 总交易数：{summary.total_trades}\n"
                          f"• 胜率：{summary.win_rate:.2f}%\n"
                          f"• 盈利率：{summary.profit_rate:.2f}%\n"
                          f"• 盈利金额：{summary.profit_amount:.4f} SOL",
                "tag": "lark_md"
            }
        }
        elements.append(overall_stats)
        
        # 分隔线
        elements.append({"tag": "hr"})
        
        # 策略表现
        if summary.top_strategies:
            strategy_text = "🎯 **策略表现 (Top 5)**\n"
            for i, strategy in enumerate(summary.top_strategies[:5], 1):
                strategy_text += f"{i}. {strategy['name']} - 胜率: {strategy['win_rate']:.1f}%, 盈利率: {strategy['profit_rate']:.1f}%\n"
            
            strategy_stats = {
                "tag": "div",
                "text": {
                    "content": strategy_text.strip(),
                    "tag": "lark_md"
                }
            }
            elements.append(strategy_stats)
            elements.append({"tag": "hr"})
        
        # 极值统计
        if summary.best_trade or summary.worst_trade:
            extreme_text = "💰 **极值统计**\n"
            if summary.best_trade:
                extreme_text += f"• 最大盈利：{summary.best_trade['profit_amount']:.4f} SOL\n"
            if summary.worst_trade:
                extreme_text += f"• 最大亏损：{abs(summary.worst_trade['profit_amount']):.4f} SOL\n"
            
            extreme_stats = {
                "tag": "div",
                "text": {
                    "content": extreme_text.strip(),
                    "tag": "lark_md"
                }
            }
            elements.append(extreme_stats)
        
        # 报告链接按钮
        if summary.report_url:
            elements.append({"tag": "hr"})
            report_button = {
                "tag": "action",
                "actions": [
                    {
                        "tag": "button",
                        "text": {
                            "content": "📋 查看详细报告",
                            "tag": "plain_text"
                        },
                        "type": "primary",
                        "url": summary.report_url
                    }
                ]
            }
            elements.append(report_button)
        
        # 时间戳
        timestamp = {
            "tag": "note",
            "elements": [
                {
                    "tag": "plain_text",
                    "content": f"生成时间：{summary.generation_time.strftime('%Y-%m-%d %H:%M:%S')}"
                }
            ]
        }
        elements.append(timestamp)
        
        return {
            "msg_type": "interactive",
            "card": {
                "config": {
                    "wide_screen_mode": True
                },
                "header": header,
                "elements": elements
            }
        }
    
    def _generate_text_message(self, summary: TradingSummary) -> Dict[str, Any]:
        """生成文本消息（降级方案）"""
        period_text = "日报" if summary.period == "daily" else "周报"
        date_text = summary.start_date.strftime("%Y-%m-%d")
        if summary.period == "weekly":
            date_text = f"{summary.start_date.strftime('%Y-%m-%d')} 至 {summary.end_date.strftime('%Y-%m-%d')}"
        
        text = f"📊 交易{period_text} - {date_text}\n\n"
        text += f"📈 总体表现\n"
        text += f"• 总交易数：{summary.total_trades}\n"
        text += f"• 胜率：{summary.win_rate:.2f}%\n"
        text += f"• 盈利率：{summary.profit_rate:.2f}%\n"
        text += f"• 盈利金额：{summary.profit_amount:.4f} SOL\n\n"
        
        if summary.top_strategies:
            text += f"🎯 策略表现 (Top 5)\n"
            for i, strategy in enumerate(summary.top_strategies[:5], 1):
                text += f"{i}. {strategy['name']} - 胜率: {strategy['win_rate']:.1f}%, 盈利率: {strategy['profit_rate']:.1f}%\n"
            text += "\n"
        
        if summary.best_trade or summary.worst_trade:
            text += f"💰 极值统计\n"
            if summary.best_trade:
                text += f"• 最大盈利：{summary.best_trade['profit_amount']:.4f} SOL\n"
            if summary.worst_trade:
                text += f"• 最大亏损：{abs(summary.worst_trade['profit_amount']):.4f} SOL\n"
            text += "\n"
        
        if summary.report_url:
            text += f"📋 详细报告：{summary.report_url}\n\n"
        
        text += f"生成时间：{summary.generation_time.strftime('%Y-%m-%d %H:%M:%S')}"
        
        return {
            "msg_type": "text",
            "content": {
                "text": text
            }
        }
    
    async def _send_message_with_retry(self, message: Dict[str, Any]) -> bool:
        """带重试的消息发送"""
        for attempt in range(self.config.retry_times):
            try:
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.config.timeout)) as session:
                    async with session.post(self.config.webhook_url, json=message) as response:
                        if response.status == 200:
                            result = await response.json()
                            if result.get("code") == 0:
                                self.logger.info("飞书消息发送成功")
                                return True
                            else:
                                self.logger.error(f"飞书API返回错误: {result}")
                        else:
                            self.logger.error(f"HTTP请求失败: {response.status}")
            
            except asyncio.TimeoutError:
                self.logger.warning(f"发送超时，第{attempt + 1}次重试")
            except Exception as e:
                self.logger.error(f"发送异常: {e}，第{attempt + 1}次重试")
            
            if attempt < self.config.retry_times - 1:
                await asyncio.sleep(2 ** attempt)  # 指数退避
        
        self.logger.error("飞书消息发送失败，已达到最大重试次数")
        return False
```

### 2.2 交易摘要生成器

```python
# utils/trading/statistics/trading_summary_generator.py

from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from .trade_statistics_analyzer import TradeStatisticsAnalyzer
from .models import StatisticsConfig, ReportFormat
from .feishu_notifier import TradingSummary

class TradingSummaryGenerator:
    """交易摘要生成器"""
    
    def __init__(self, analyzer: TradeStatisticsAnalyzer):
        self.analyzer = analyzer
        self.logger = logging.getLogger(__name__)
    
    async def generate_daily_summary(
        self, 
        target_date: datetime = None,
        report_url: str = None
    ) -> TradingSummary:
        """生成日交易摘要"""
        if target_date is None:
            target_date = datetime.now()
        
        start_date = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = start_date + timedelta(days=1)
        
        return await self._generate_summary("daily", start_date, end_date, report_url)
    
    async def generate_weekly_summary(
        self, 
        target_date: datetime = None,
        report_url: str = None
    ) -> TradingSummary:
        """生成周交易摘要"""
        if target_date is None:
            target_date = datetime.now()
        
        # 计算本周开始时间（周一）
        days_since_monday = target_date.weekday()
        start_date = (target_date - timedelta(days=days_since_monday)).replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = start_date + timedelta(days=7)
        
        return await self._generate_summary("weekly", start_date, end_date, report_url)
    
    async def _generate_summary(
        self,
        period: str,
        start_date: datetime,
        end_date: datetime,
        report_url: str = None
    ) -> TradingSummary:
        """生成摘要的通用方法"""
        
        # 配置统计参数
        config = StatisticsConfig(
            start_date=start_date,
            end_date=end_date,
            report_format=ReportFormat.JSON
        )
        
        # 生成统计数据
        stats_result = await self.analyzer.generate_statistics(config)
        
        # 提取前5策略
        top_strategies = []
        for strategy in stats_result.strategy_stats[:5]:
            top_strategies.append({
                "name": strategy.strategy_name,
                "trade_count": strategy.trade_count,
                "win_rate": strategy.win_rate,
                "profit_rate": strategy.avg_profit_rate,
                "profit_amount": strategy.total_profit_amount
            })
        
        # 提取最佳和最差交易
        best_trade = None
        worst_trade = None
        
        if stats_result.profit_rankings:
            best_ranking = stats_result.profit_rankings[0]
            best_trade = {
                "signal_id": best_ranking.signal_id,
                "strategy_name": best_ranking.strategy_name,
                "profit_amount": best_ranking.profit_amount,
                "profit_rate": best_ranking.profit_rate
            }
        
        if stats_result.loss_rankings:
            worst_ranking = stats_result.loss_rankings[0]
            worst_trade = {
                "signal_id": worst_ranking.signal_id,
                "strategy_name": worst_ranking.strategy_name,
                "profit_amount": -worst_ranking.loss_amount,  # 转为负数
                "profit_rate": -worst_ranking.loss_rate       # 转为负数
            }
        
        return TradingSummary(
            period=period,
            start_date=start_date,
            end_date=end_date,
            total_trades=stats_result.overall_stats.total_trades,
            win_rate=stats_result.overall_stats.total_win_rate,
            profit_rate=stats_result.overall_stats.total_profit_rate,
            profit_amount=stats_result.overall_stats.total_profit_amount,
            top_strategies=top_strategies,
            best_trade=best_trade,
            worst_trade=worst_trade,
            report_url=report_url,
            generation_time=datetime.now()
        )
```

### 2.3 报告上传器

```python
# utils/trading/statistics/report_uploader.py

import os
import shutil
from typing import Optional
from datetime import datetime
from pathlib import Path
import logging

class ReportUploader:
    """报告上传器"""
    
    def __init__(self, base_url: str = None, upload_path: str = None):
        self.base_url = base_url
        self.upload_path = upload_path
        self.logger = logging.getLogger(__name__)
    
    async def upload_html_report(
        self,
        local_report_path: str,
        filename: str = None
    ) -> Optional[str]:
        """上传HTML报告并返回访问URL"""
        
        if not self.upload_path or not self.base_url:
            self.logger.warning("未配置上传路径或基础URL，跳过文件上传")
            return None
        
        try:
            # 生成文件名
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"trade_report_{timestamp}.html"
            
            # 确保上传目录存在
            upload_dir = Path(self.upload_path)
            upload_dir.mkdir(parents=True, exist_ok=True)
            
            # 复制文件到上传目录
            target_path = upload_dir / filename
            shutil.copy2(local_report_path, target_path)
            
            # 生成访问URL
            url = f"{self.base_url.rstrip('/')}/{filename}"
            
            self.logger.info(f"报告上传成功: {target_path} -> {url}")
            return url
            
        except Exception as e:
            self.logger.error(f"报告上传失败: {e}")
            return None
    
    def generate_filename(self, period: str, date: datetime) -> str:
        """生成报告文件名"""
        date_str = date.strftime("%Y%m%d")
        timestamp = datetime.now().strftime("%H%M%S")
        return f"trade_{period}_report_{date_str}_{timestamp}.html"
```

### 2.4 CLI参数扩展

```python
# utils/trading/statistics/cli.py (扩展)

def add_feishu_arguments(parser):
    """添加飞书相关参数"""
    feishu_group = parser.add_argument_group('飞书通知选项')
    
    feishu_group.add_argument(
        '--send-feishu',
        action='store_true',
        help='启用飞书消息发送'
    )
    
    feishu_group.add_argument(
        '--feishu-webhook',
        type=str,
        help='飞书Webhook URL'
    )
    
    feishu_group.add_argument(
        '--report-base-url',
        type=str,
        help='报告访问基础URL'
    )
    
    feishu_group.add_argument(
        '--upload-path',
        type=str,
        help='报告上传路径'
    )
    
    feishu_group.add_argument(
        '--period',
        choices=['daily', 'weekly'],
        default='daily',
        help='统计周期 (默认: daily)'
    )
    
    feishu_group.add_argument(
        '--feishu-retry',
        type=int,
        default=3,
        help='飞书发送重试次数 (默认: 3)'
    )

async def handle_feishu_notification(args, stats_result):
    """处理飞书通知"""
    if not args.send_feishu:
        return
    
    # 获取Webhook URL
    webhook_url = args.feishu_webhook or os.getenv('FEISHU_WEBHOOK_URL')
    if not webhook_url:
        print("错误: 未指定飞书Webhook URL")
        return
    
    # 上传报告（如果配置了上传参数）
    report_url = None
    if args.output and args.report_base_url and args.upload_path:
        uploader = ReportUploader(args.report_base_url, args.upload_path)
        filename = uploader.generate_filename(args.period, datetime.now())
        report_url = await uploader.upload_html_report(args.output, filename)
    
    # 生成摘要
    summary_generator = TradingSummaryGenerator(analyzer)
    
    if args.period == 'daily':
        summary = await summary_generator.generate_daily_summary(report_url=report_url)
    else:
        summary = await summary_generator.generate_weekly_summary(report_url=report_url)
    
    # 发送飞书消息
    config = FeishuNotifierConfig(
        webhook_url=webhook_url,
        report_base_url=args.report_base_url,
        upload_path=args.upload_path,
        retry_times=args.feishu_retry
    )
    
    notifier = FeishuNotifier(config)
    success = await notifier.send_trading_summary(summary)
    
    if success:
        print("✅ 飞书消息发送成功")
    else:
        print("❌ 飞书消息发送失败")

# 在main函数中集成
async def main():
    parser = create_argument_parser()
    add_feishu_arguments(parser)  # 添加飞书参数
    
    args = parser.parse_args()
    
    # ... 现有逻辑 ...
    
    # 处理飞书通知
    await handle_feishu_notification(args, stats_result)
```

## 3. 环境变量配置

```bash
# .env 文件扩展
FEISHU_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/xxx
REPORT_BASE_URL=https://reports.example.com
REPORT_UPLOAD_PATH=/var/www/reports
```

## 4. 使用示例

### 4.1 基础使用
```bash
# 生成日报并发送到飞书
python -m utils.trading.statistics.cli \
    --days 1 \
    --format html \
    --output /tmp/daily_report.html \
    --send-feishu \
    --feishu-webhook "https://open.feishu.cn/open-apis/bot/v2/hook/xxx"

# 生成周报并发送到飞书（包含报告链接）
python -m utils.trading.statistics.cli \
    --days 7 \
    --period weekly \
    --format html \
    --output /tmp/weekly_report.html \
    --send-feishu \
    --feishu-webhook "https://open.feishu.cn/open-apis/bot/v2/hook/xxx" \
    --report-base-url "https://reports.example.com" \
    --upload-path "/var/www/reports"
```

### 4.2 使用环境变量
```bash
# 设置环境变量
export FEISHU_WEBHOOK_URL="https://open.feishu.cn/open-apis/bot/v2/hook/xxx"
export REPORT_BASE_URL="https://reports.example.com"
export REPORT_UPLOAD_PATH="/var/www/reports"

# 简化命令
python -m utils.trading.statistics.cli --days 1 --send-feishu
```

## 5. 文件结构

```
utils/trading/statistics/
├── feishu_notifier.py          # 飞书通知器
├── trading_summary_generator.py # 交易摘要生成器
├── report_uploader.py          # 报告上传器
└── cli.py                      # CLI扩展

utils/message_sender/
└── message_sender.py           # 消息发送器扩展

test/utils/trading/statistics/
├── test_feishu_notifier.py     # 飞书通知器测试
├── test_trading_summary_generator.py # 摘要生成器测试
└── test_report_uploader.py     # 报告上传器测试
```

## 6. 测试策略

### 6.1 单元测试
- 飞书消息格式生成测试
- 摘要数据生成测试
- 文件上传功能测试
- 重试机制测试

### 6.2 集成测试
- 端到端飞书消息发送测试
- CLI参数解析测试
- 环境变量配置测试

### 6.3 手动测试
- 实际飞书群组消息发送测试
- 报告链接访问测试
- 错误处理测试

## 7. 部署注意事项

### 7.1 权限配置
- 确保上传目录有写权限
- 配置Web服务器访问上传目录
- 设置适当的文件权限

### 7.2 安全考虑
- 使用环境变量存储敏感信息
- 定期清理过期报告文件
- 限制上传目录的访问权限

### 7.3 监控和日志
- 监控飞书消息发送成功率
- 记录详细的错误日志
- 设置告警机制

这个技术实现方案提供了完整的飞书消息发送功能，包括卡片消息格式、文件上传、CLI集成等所有必要组件。 