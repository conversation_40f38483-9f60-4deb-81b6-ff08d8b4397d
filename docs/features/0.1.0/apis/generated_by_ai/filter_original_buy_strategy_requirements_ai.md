# 过滤原始买入策略信号功能详细需求规格

**创建日期**: 2025-05-27  
**功能模块**: APIs - 信号记录列表API  
**版本**: 0.1.0  
**需求类型**: 功能扩展

## 需求概述

在现有的过滤测试策略信号功能基础上，增加对`original_buy_strategy_name`字段的过滤，确保API返回的信号列表中不包含任何与"测试"策略相关的记录。

## 详细需求

### 1. 功能需求

#### 1.1 核心功能
- **FR-1**: 在获取信号列表API中，过滤掉`trigger_conditions.original_buy_strategy_name`为"测试"的记录
- **FR-2**: 保持现有的`trigger_conditions.strategy_name`过滤功能不变
- **FR-3**: 两个过滤条件需要同时生效（AND逻辑）

#### 1.2 过滤逻辑
- **FR-4**: 排除`trigger_conditions.original_buy_strategy_name`等于"测试"的记录
- **FR-5**: 保留`trigger_conditions.original_buy_strategy_name`字段不存在的记录
- **FR-6**: 保留`trigger_conditions`字段不存在的记录
- **FR-7**: 过滤条件区分大小写，只过滤"测试"，不过滤"Test"、"测试1"等

#### 1.3 兼容性需求
- **FR-8**: 保持所有现有查询参数的功能不变
- **FR-9**: 保持API响应格式不变
- **FR-10**: 保持错误处理机制不变

### 2. 非功能需求

#### 2.1 性能需求
- **NFR-1**: API响应时间增幅不超过5%
- **NFR-2**: 支持现有的分页功能
- **NFR-3**: 查询优化，避免全表扫描

#### 2.2 可靠性需求
- **NFR-4**: 过滤逻辑必须准确，不能误过滤有效数据
- **NFR-5**: 处理字段不存在的情况，不能导致查询错误

#### 2.3 可维护性需求
- **NFR-6**: 代码注释清晰，说明过滤逻辑
- **NFR-7**: 易于理解和修改的查询结构

## 数据结构分析

### 信号数据结构
```json
{
  "_id": "ObjectId",
  "signal_type": "kol_sell",
  "trigger_conditions": {
    "strategy_name": "某策略名称",
    "original_buy_strategy_name": "测试",  // 需要过滤的字段
    "reason": "卖出原因"
  }
}
```

### 过滤场景
1. **需要过滤的记录**：
   - `trigger_conditions.original_buy_strategy_name` = "测试"

2. **需要保留的记录**：
   - `trigger_conditions.original_buy_strategy_name` ≠ "测试"
   - `trigger_conditions.original_buy_strategy_name` 字段不存在
   - `trigger_conditions` 字段不存在

## 验收标准

### 功能验收
1. **AC-1**: API正确过滤掉`trigger_conditions.original_buy_strategy_name`为"测试"的记录
2. **AC-2**: 保留`original_buy_strategy_name`字段不存在的记录
3. **AC-3**: 保留`trigger_conditions`字段不存在的记录
4. **AC-4**: 现有的`strategy_name`过滤功能继续正常工作
5. **AC-5**: 所有现有查询参数（signal_type, period, skip, limit, tz）继续正常工作
6. **AC-6**: 过滤条件区分大小写

### 性能验收
1. **AC-7**: API响应时间无显著增加
2. **AC-8**: 分页功能正常工作
3. **AC-9**: 大数据量下过滤功能正确

### 兼容性验收
1. **AC-10**: 不破坏现有API接口定义
2. **AC-11**: 响应数据结构保持不变
3. **AC-12**: 错误处理机制保持不变

## 边界条件

### 数据边界
- 空字符串：`original_buy_strategy_name`为空字符串时应保留
- null值：`original_buy_strategy_name`为null时应保留
- 大小写：严格区分大小写，只过滤"测试"

### 查询边界
- 无数据：当所有记录都被过滤时，返回空数组
- 大量数据：确保过滤逻辑在大数据量下正常工作

## 风险评估

### 技术风险
- **低风险**: MongoDB查询语法复杂度增加
- **低风险**: 字段不存在情况的处理

### 业务风险
- **低风险**: 过度过滤有效数据
- **低风险**: 过滤逻辑错误导致数据泄露

## 依赖关系

### 技术依赖
- MongoDB查询引擎
- 现有的SignalDAO实现
- FastAPI查询参数处理

### 数据依赖
- Signal集合中的trigger_conditions字段结构
- 现有的过滤逻辑实现

## 测试策略

### 单元测试
- 基本过滤功能测试
- 字段不存在情况测试
- 组合查询测试
- 边界条件测试

### 集成测试
- API端到端测试
- 性能测试
- 兼容性测试

## 实施优先级

**优先级**: 中等
**紧急程度**: 非紧急
**业务价值**: 提高数据质量，避免测试数据污染生产环境

## 相关文档

- 现有过滤功能文档：`filter_test_strategy_requirements_ai.md`
- API文档：`docs/features/0.1.0/apis/获取信号记录列表（分页）.md`
- Signal模型文档：`models/signal.py` 