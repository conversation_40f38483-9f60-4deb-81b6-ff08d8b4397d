"""
输入节点模块

提供输入节点的抽象接口和基本实现
"""

import asyncio
import logging
import traceback
from abc import abstractmethod
from typing import Any, Optional
import time

from .base_node import Node
from utils.workflows.message_queue.message_queue import KafkaOffsetFlowController, KafkaQueue, MessageQueue, FlowController

class InputNode(Node):
    """输入节点，只有输出队列"""
    
    def __init__(self, name: str = None, 
                 max_pending_messages: int = 100, check_interval: float = 60.0, 
                 enable_flow_control: bool = True):
        """初始化输入节点
        
        Args:
            name: 节点名称
            # output_queue: 输出队列 (已从基类继承 output_queues)
            max_pending_messages: 输出队列中最大待处理消息数
            check_interval: 流量控制检查间隔（秒）
            enable_flow_control: 是否启用流量控制
        """
        super().__init__(name)
        # self.output_queue = output_queue # 已移除, 使用 self.output_queues
        
        self.interval = 60  # 每分钟运行一次
        
        # 流量控制参数
        self.max_pending_messages = max_pending_messages
        self.check_interval = check_interval
        self.enable_flow_control = enable_flow_control
        
        # 创建流量控制器
        self.setup_flow_controller()

    def setup_flow_controller(self):
        """设置流量控制器"""
        if self.output_queues: # 修改: 检查 self.output_queues
            # 暂时只基于第一个输出队列创建流量控制器
            # TODO: 后续可以优化为支持基于多个队列的复杂流量控制策略
            primary_output_queue = self.output_queues[0]
            if isinstance(primary_output_queue, KafkaQueue):
                self.flow_controller = KafkaOffsetFlowController(
                    queue=primary_output_queue, # 修改: 使用 primary_output_queue
                    max_lag=self.max_pending_messages,
                    check_interval=self.check_interval,
                    enable_flow_control=self.enable_flow_control
                )
            else:
                self.flow_controller = FlowController(
                    queue=primary_output_queue, # 修改: 使用 primary_output_queue
                    max_pending_messages=self.max_pending_messages,
                    check_interval=self.check_interval,
                    enable_flow_control=self.enable_flow_control
            )
        elif self.enable_flow_control: # 如果启用了流控但没有输出队列，则警告
             self.logger.warning(f"节点 {self.name} 启用了流量控制，但没有配置输出队列。")
    
    @abstractmethod
    async def generate_data(self):
        """生成数据的抽象方法，子类必须实现
        
        变更为异步生成器方法：
        
        Returns:
            AsyncGenerator: 异步生成数据项
        """
        pass
    
    async def check_flow_control(self) -> bool:
        """检查流量控制
        
        Returns:
            bool: 是否需要限流
        """
        if hasattr(self, 'flow_controller') and self.flow_controller and not await self.flow_controller.check_immediate(): # 修改: 确保 flow_controller 存在
            self.logger.info("流量控制，暂停处理")
            return True
        return False
    
    async def process(self):
        """处理数据"""
        # 检查是否需要限流
        if await self.check_flow_control():
            return
        
        # 使用异步生成器方式获取数据
        try:
            item_count = 0
            async for data in self.generate_data():
                # 发送数据到输出队列
                if data:
                    await self.send_data_to_output_queue(data)
                    item_count += 1
            
            if item_count > 0:
                self.logger.info(f"处理完成，共生成 {item_count} 条数据")
        except Exception as e:
            self.logger.error(f"处理数据时发生异常: {str(e)}\n{traceback.format_exc()}")