"""交易摘要生成器

为飞书机器人消息生成交易统计摘要数据，包括：
- 总体统计摘要
- 策略排行榜
- 极值统计
- 时间范围信息
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

from .api import TradeStatisticsAPI
from .models import StatisticsResult

logger = logging.getLogger(__name__)


class TradingSummaryGenerator:
    """交易摘要生成器
    
    生成适用于飞书机器人消息的交易统计摘要数据
    """
    
    def __init__(self, api: Optional[TradeStatisticsAPI] = None):
        """初始化摘要生成器
        
        Args:
            api: 交易统计API实例，如果为None则创建新实例
        """
        self.api = api or TradeStatisticsAPI()
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def generate_daily_summary(
        self,
        target_date: Optional[datetime] = None,
        strategies: Optional[List[str]] = None,
        tokens: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """生成日报摘要
        
        Args:
            target_date: 目标日期，默认为昨天
            strategies: 策略过滤列表
            tokens: Token过滤列表
            
        Returns:
            Dict: 日报摘要数据
        """
        if target_date is None:
            target_date = datetime.now() - timedelta(days=1)
        
        # 计算24小时时间范围
        end_date = target_date.replace(hour=23, minute=59, second=59, microsecond=999999)
        start_date = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
        
        return await self._generate_summary(
            period_type="日",
            start_date=start_date,
            end_date=end_date,
            strategies=strategies,
            tokens=tokens
        )
    
    async def generate_weekly_summary(
        self,
        target_date: Optional[datetime] = None,
        strategies: Optional[List[str]] = None,
        tokens: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """生成周报摘要
        
        Args:
            target_date: 目标日期，默认为今天
            strategies: 策略过滤列表
            tokens: Token过滤列表
            
        Returns:
            Dict: 周报摘要数据
        """
        if target_date is None:
            target_date = datetime.now()
        
        # 计算7天时间范围
        end_date = target_date.replace(hour=23, minute=59, second=59, microsecond=999999)
        start_date = (target_date - timedelta(days=6)).replace(hour=0, minute=0, second=0, microsecond=0)
        
        return await self._generate_summary(
            period_type="周",
            start_date=start_date,
            end_date=end_date,
            strategies=strategies,
            tokens=tokens
        )
    
    async def generate_custom_summary(
        self,
        days: Optional[int] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        strategies: Optional[List[str]] = None,
        tokens: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """生成自定义时间范围摘要
        
        Args:
            days: 最近天数
            start_date: 开始日期
            end_date: 结束日期
            strategies: 策略过滤列表
            tokens: Token过滤列表
            
        Returns:
            Dict: 摘要数据
        """
        # 确定时间范围
        if days is not None:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            period_type = f"{days}天"
        elif start_date and end_date:
            # 修复周期计算逻辑
            # 检查是否为同一天
            if start_date.date() == end_date.date():
                period_type = "1天"
            else:
                # 计算天数差
                delta = end_date.date() - start_date.date()
                days_count = delta.days + 1
                
                # 检查是否为一周的数据（7天）
                if days_count == 7:
                    # 检查是否从周一开始到周日结束
                    if start_date.weekday() == 0 and end_date.weekday() == 6:
                        period_type = "上周"
                    else:
                        period_type = f"{days_count}天"
                else:
                    period_type = f"{days_count}天"
        
        return await self._generate_summary(
            period_type=period_type,
            start_date=start_date,
            end_date=end_date,
            strategies=strategies,
            tokens=tokens
        )
    
    async def _generate_summary(
        self,
        period_type: str,
        start_date: datetime,
        end_date: datetime,
        strategies: Optional[List[str]] = None,
        tokens: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """生成摘要数据的核心方法
        
        Args:
            period_type: 时间周期类型
            start_date: 开始日期
            end_date: 结束日期
            strategies: 策略过滤列表
            tokens: Token过滤列表
            
        Returns:
            Dict: 摘要数据
        """
        try:
            # 获取详细统计数据
            detailed_stats = await self.api.get_detailed_statistics(
                start_date=start_date,
                end_date=end_date,
                strategies=strategies,
                tokens=tokens
            )
            
            if not detailed_stats.get("success", False):
                error_msg = detailed_stats.get('error', '未知错误')
                self.logger.error(f"获取统计数据失败: {error_msg}")
                return self._create_empty_summary(period_type, start_date, end_date, error=error_msg)
            
            # 构建摘要数据
            summary = {
                "period_type": period_type,
                "start_date": start_date.strftime("%Y-%m-%d"),
                "end_date": end_date.strftime("%Y-%m-%d"),
                "generation_time": datetime.now().isoformat(),
                "overall_stats": self._extract_overall_stats(detailed_stats.get("overall_stats", {})),
                "top_strategies": self._extract_top_strategies(detailed_stats.get("strategy_stats", []), limit=5),
                "top_tokens": self._extract_top_tokens(detailed_stats.get("token_stats", []), limit=5),
                "extremes": self._extract_extremes(
                    detailed_stats.get("profit_rankings", []),
                    detailed_stats.get("loss_rankings", [])
                ),
                "data_range": detailed_stats.get("data_range", {}),
                "trade_pair_count": detailed_stats.get("trade_pair_count", 0)
            }
            
            self.logger.info(f"成功生成{period_type}报摘要，总交易次数: {summary['overall_stats']['total_trades']}")
            return summary
            
        except Exception as e:
            self.logger.error(f"生成摘要失败: {str(e)}", exc_info=True)
            return self._create_empty_summary(period_type, start_date, end_date, error=str(e))
    
    def _extract_overall_stats(self, overall_stats: Dict[str, Any]) -> Dict[str, Any]:
        """提取总体统计数据
        
        Args:
            overall_stats: 原始总体统计数据
            
        Returns:
            Dict: 格式化的总体统计数据
        """
        return {
            "total_trades": overall_stats.get("total_trades", 0),
            "win_rate": round(overall_stats.get("total_win_rate", 0), 2),
            "total_pnl_rate": round(overall_stats.get("total_profit_rate", 0), 2),
            "avg_pnl_rate": round(overall_stats.get("avg_profit_rate", 0), 2),
            "overall_return_rate": round(overall_stats.get("overall_return_rate", 0), 2),
            "total_pnl_amount": round(overall_stats.get("total_profit_amount", 0), 4),
            "profitable_trades": overall_stats.get("profitable_trades", 0),
            "loss_trades": overall_stats.get("loss_trades", 0)
        }
    
    def _extract_top_strategies(self, strategy_stats: List[Dict[str, Any]], limit: int = 5) -> List[Dict[str, Any]]:
        """提取策略排行榜
        
        Args:
            strategy_stats: 策略统计数据列表
            limit: 返回数量限制
            
        Returns:
            List: 排序后的策略列表
        """
        # 按总盈利率排序
        sorted_strategies = sorted(
            strategy_stats,
            key=lambda x: x.get("total_profit_amount", 0),
            reverse=True
        )
        
        result = []
        for strategy in sorted_strategies[:limit]:
            result.append({
                "strategy": strategy.get("strategy_name", "未知策略"),
                "total_trades": strategy.get("trade_count", 0),
                "win_rate": round(strategy.get("win_rate", 0), 2),
                "total_pnl_rate": round(strategy.get("avg_profit_rate", 0), 2),
                "avg_pnl_rate": round(strategy.get("avg_profit_rate", 0), 2),
                "profitable_trades": strategy.get("profitable_trades", 0),
                "loss_trades": strategy.get("loss_trades", 0)
            })
        
        return result
    
    def _extract_top_tokens(self, token_stats: List[Dict[str, Any]], limit: int = 5) -> List[Dict[str, Any]]:
        """提取Token排行榜
        
        Args:
            token_stats: Token统计数据列表
            limit: 返回数量限制
            
        Returns:
            List: 排序后的Token列表
        """
        # 按总盈利率排序
        sorted_tokens = sorted(
            token_stats,
            key=lambda x: x.get("total_profit_rate", 0),
            reverse=True
        )
        
        result = []
        for token in sorted_tokens[:limit]:
            result.append({
                "token": token.get("token", "未知Token"),
                "token_address": token.get("token_address", ""),
                "total_trades": token.get("total_trades", 0),
                "win_rate": round(token.get("total_win_rate", 0), 2),
                "total_pnl_rate": round(token.get("total_profit_rate", 0), 2),
                "avg_pnl_rate": round(token.get("avg_profit_rate", 0), 2),
                "profitable_trades": token.get("profitable_trades", 0),
                "loss_trades": token.get("loss_trades", 0)
            })
        
        return result
    
    def _extract_extremes(
        self,
        profit_rankings: List[Dict[str, Any]],
        loss_rankings: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """提取极值统计
        
        Args:
            profit_rankings: 盈利排行榜
            loss_rankings: 亏损排行榜
            
        Returns:
            Dict: 极值统计数据
        """
        extremes = {}
        
        # 最大盈利
        if profit_rankings:
            max_profit = profit_rankings[0]
            extremes["max_profit"] = {
                "pnl_rate": round(max_profit.get("profit_rate", 0), 2),
                "pnl_amount": round(max_profit.get("profit_amount", 0), 4),
                "token": max_profit.get("token", "未知"),
                "signal_id": str(max_profit.get("signal_id", ""))
            }
        
        # 最大亏损
        if loss_rankings:
            max_loss = loss_rankings[0]
            extremes["max_loss"] = {
                "pnl_rate": round(max_loss.get("loss_rate", 0), 2),
                "pnl_amount": round(max_loss.get("loss_amount", 0), 4),
                "token": max_loss.get("token", "未知"),
                "signal_id": str(max_loss.get("signal_id", ""))
            }
        
        return extremes
    
    def _create_empty_summary(
        self,
        period_type: str,
        start_date: datetime,
        end_date: datetime,
        error: Optional[str] = None
    ) -> Dict[str, Any]:
        """创建空摘要数据
        
        Args:
            period_type: 时间周期类型
            start_date: 开始日期
            end_date: 结束日期
            error: 错误信息
            
        Returns:
            Dict: 空摘要数据
        """
        summary = {
            "period_type": period_type,
            "start_date": start_date.strftime("%Y-%m-%d"),
            "end_date": end_date.strftime("%Y-%m-%d"),
            "generation_time": datetime.now().isoformat(),
            "overall_stats": {
                "total_trades": 0,
                "win_rate": 0,
                "total_pnl_rate": 0,
                "avg_pnl_rate": 0,
                "total_pnl_amount": 0,
                "profitable_trades": 0,
                "loss_trades": 0
            },
            "top_strategies": [],
            "top_tokens": [],
            "extremes": {},
            "data_range": {},
            "trade_pair_count": 0
        }
        
        if error:
            summary["error"] = error
        
        return summary


class SummaryFormatter:
    """摘要格式化器
    
    将摘要数据格式化为不同的输出格式
    """
    
    @staticmethod
    def format_as_text(summary_data: Dict[str, Any]) -> str:
        """格式化为纯文本
        
        Args:
            summary_data: 摘要数据
            
        Returns:
            str: 格式化的文本
        """
        period_type = summary_data.get("period_type", "")
        start_date = summary_data.get("start_date", "")
        end_date = summary_data.get("end_date", "")
        overall = summary_data.get("overall_stats", {})
        strategies = summary_data.get("top_strategies", [])
        extremes = summary_data.get("extremes", {})
        
        lines = [
            f"📊 交易{period_type}报",
            f"📅 时间范围: {start_date} ~ {end_date}",
            "",
            "📈 总体统计:",
            f"  • 总交易次数: {overall.get('total_trades', 0)}",
            f"  • 总胜率: {overall.get('win_rate', 0):.2f}%",
            f"  • 总盈利率: {overall.get('total_pnl_rate', 0):.2f}%",
            f"  • 平均盈利率: {overall.get('avg_pnl_rate', 0):.2f}%",
            f"  • 整体收益率: {overall.get('overall_return_rate', 0):.2f}%",
            ""
        ]
        
        # 策略排行榜
        if strategies:
            lines.append("🏆 策略排行榜 (前5名):")
            for i, strategy in enumerate(strategies[:5], 1):
                lines.append(
                    f"  {i}. {strategy.get('strategy', '未知策略')} - "
                    f"胜率: {strategy.get('win_rate', 0):.2f}% | "
                    f"盈利率: {strategy.get('total_pnl_rate', 0):.2f}% | "
                    f"交易: {strategy.get('total_trades', 0)}次"
                )
            lines.append("")
        
        # 极值统计
        if extremes:
            lines.append("📊 极值统计:")
            max_profit = extremes.get("max_profit", {})
            max_loss = extremes.get("max_loss", {})
            
            if max_profit:
                lines.append(f"  • 最大盈利: {max_profit.get('pnl_rate', 0):.2f}% ({max_profit.get('token', 'N/A')})")
            
            if max_loss:
                lines.append(f"  • 最大亏损: {max_loss.get('pnl_rate', 0):.2f}% ({max_loss.get('token', 'N/A')})")
        
        return "\n".join(lines)
    
    @staticmethod
    def format_as_markdown(summary_data: Dict[str, Any]) -> str:
        """格式化为Markdown
        
        Args:
            summary_data: 摘要数据
            
        Returns:
            str: 格式化的Markdown文本
        """
        period_type = summary_data.get("period_type", "")
        start_date = summary_data.get("start_date", "")
        end_date = summary_data.get("end_date", "")
        overall = summary_data.get("overall_stats", {})
        strategies = summary_data.get("top_strategies", [])
        extremes = summary_data.get("extremes", {})
        
        lines = [
            f"# 📊 交易{period_type}报",
            f"**📅 时间范围**: {start_date} ~ {end_date}",
            "",
            "## 📈 总体统计",
            f"- **总交易次数**: {overall.get('total_trades', 0)}",
            f"- **总胜率**: {overall.get('win_rate', 0):.2f}%",
            f"- **总盈利率**: {overall.get('total_pnl_rate', 0):.2f}%",
            f"- **平均盈利率**: {overall.get('avg_pnl_rate', 0):.2f}%",
            f"- **整体收益率**: {overall.get('overall_return_rate', 0):.2f}%",
            ""
        ]
        
        # 策略排行榜
        if strategies:
            lines.append("## 🏆 策略排行榜 (前5名)")
            for i, strategy in enumerate(strategies[:5], 1):
                lines.append(
                    f"{i}. **{strategy.get('strategy', '未知策略')}** - "
                    f"胜率: {strategy.get('win_rate', 0):.2f}% | "
                    f"盈利率: {strategy.get('total_pnl_rate', 0):.2f}% | "
                    f"交易: {strategy.get('total_trades', 0)}次"
                )
            lines.append("")
        
        # 极值统计
        if extremes:
            lines.append("## 📊 极值统计")
            max_profit = extremes.get("max_profit", {})
            max_loss = extremes.get("max_loss", {})
            
            if max_profit:
                lines.append(f"- **最大盈利**: {max_profit.get('pnl_rate', 0):.2f}% ({max_profit.get('token', 'N/A')})")
            
            if max_loss:
                lines.append(f"- **最大亏损**: {max_loss.get('pnl_rate', 0):.2f}% ({max_loss.get('token', 'N/A')})")
        
        return "\n".join(lines) 