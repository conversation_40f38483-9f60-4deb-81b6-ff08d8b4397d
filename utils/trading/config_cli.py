#!/usr/bin/env python3
"""
AutoTradeManager配置管理命令行工具

支持：
1. 初始化默认配置
2. 验证现有配置
3. 迁移配置
4. 查看配置状态
5. 更新配置参数
"""

import asyncio
import argparse
import json
import logging
import sys
from typing import Dict, Any

from models import init_db
from utils.trading.init_auto_trade_config import (
    AutoTradeConfigInitializer, 
    init_default_auto_trade_config,
    validate_auto_trade_config
)
from utils.trading.migrate_trade_config import (
    TradingConfigMigrator,
    migrate_trading_config
)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ConfigCLI:
    """AutoTradeManager配置命令行工具"""
    
    def __init__(self):
        self.initializer = AutoTradeConfigInitializer()
        self.migrator = TradingConfigMigrator()
    
    async def init_config(self, force: bool = False, custom_config_file: str = None) -> None:
        """初始化默认配置"""
        logger.info("开始初始化AutoTradeManager配置...")
        
        custom_config = None
        if custom_config_file:
            try:
                with open(custom_config_file, 'r', encoding='utf-8') as f:
                    custom_config = json.load(f)
                logger.info(f"从文件 {custom_config_file} 加载自定义配置")
            except Exception as e:
                logger.error(f"加载自定义配置文件失败: {e}")
                return
        
        try:
            config = await init_default_auto_trade_config(
                force_overwrite=force,
                custom_config=custom_config
            )
            print(f"✅ 配置初始化成功！配置ID: {config.id}")
            
            # 显示配置摘要
            summary = await self.initializer.get_config_summary()
            self._print_config_summary(summary)
            
        except Exception as e:
            logger.error(f"配置初始化失败: {e}")
            print(f"❌ 配置初始化失败: {e}")
    
    async def validate_config(self, config_name: str = "auto_trade_manager") -> None:
        """验证配置"""
        logger.info(f"开始验证配置: {config_name}")
        
        try:
            result = await validate_auto_trade_config(config_name)
            
            if result["valid"]:
                print(f"✅ 配置 '{config_name}' 验证通过")
            else:
                print(f"❌ 配置 '{config_name}' 验证失败")
            
            # 显示验证详情
            self._print_validation_result(result)
            
        except Exception as e:
            logger.error(f"配置验证失败: {e}")
            print(f"❌ 配置验证失败: {e}")
    
    async def migrate_config(
        self, 
        source: str = "kol_activity",
        target: str = "auto_trade_manager",
        preserve_source: bool = True,
        backup: bool = True
    ) -> None:
        """迁移配置"""
        logger.info(f"开始迁移配置: {source} -> {target}")
        
        try:
            result = await self.migrator.migrate_from_kol_activity_config(
                source_config_name=source,
                target_config_name=target,
                preserve_source=preserve_source,
                create_backup=backup
            )
            
            if result["success"]:
                print(f"✅ 配置迁移成功！")
            else:
                print(f"❌ 配置迁移失败")
            
            # 显示迁移详情
            self._print_migration_result(result)
            
        except Exception as e:
            logger.error(f"配置迁移失败: {e}")
            print(f"❌ 配置迁移失败: {e}")
    
    async def show_status(self, config_name: str = "auto_trade_manager") -> None:
        """显示配置状态"""
        logger.info(f"获取配置状态: {config_name}")
        
        try:
            summary = await self.initializer.get_config_summary(config_name)
            
            if summary.get("exists"):
                print(f"📊 配置 '{config_name}' 状态:")
                self._print_config_summary(summary)
            else:
                print(f"❌ 配置 '{config_name}' 不存在")
                if summary.get("error"):
                    print(f"错误: {summary['error']}")
            
        except Exception as e:
            logger.error(f"获取配置状态失败: {e}")
            print(f"❌ 获取配置状态失败: {e}")
    
    async def update_config(
        self, 
        config_name: str = "auto_trade_manager",
        updates_file: str = None,
        **kwargs
    ) -> None:
        """更新配置"""
        logger.info(f"开始更新配置: {config_name}")
        
        updates = {}
        
        # 从文件加载更新
        if updates_file:
            try:
                with open(updates_file, 'r', encoding='utf-8') as f:
                    updates = json.load(f)
                logger.info(f"从文件 {updates_file} 加载更新参数")
            except Exception as e:
                logger.error(f"加载更新文件失败: {e}")
                return
        
        # 从命令行参数加载更新
        if kwargs:
            updates.update(kwargs)
        
        if not updates:
            print("❌ 没有提供更新参数")
            return
        
        try:
            success = await self.initializer.update_config(updates, config_name)
            
            if success:
                print(f"✅ 配置 '{config_name}' 更新成功")
                
                # 显示更新后的状态
                await self.show_status(config_name)
            else:
                print(f"❌ 配置 '{config_name}' 更新失败")
            
        except Exception as e:
            logger.error(f"配置更新失败: {e}")
            print(f"❌ 配置更新失败: {e}")
    
    def _print_config_summary(self, summary: Dict[str, Any]) -> None:
        """打印配置摘要"""
        if not summary.get("exists"):
            return
        
        print(f"  状态: {'启用' if summary.get('enabled') else '禁用'}")
        print(f"  创建时间: {summary.get('created_at', 'N/A')}")
        print(f"  更新时间: {summary.get('updated_at', 'N/A')}")
        print(f"  钱包已配置: {'是' if summary.get('wallet_configured') else '否'}")
        print(f"  总渠道数: {summary.get('total_channels', 0)}")
        print(f"  启用渠道数: {summary.get('enabled_channels', 0)}")
        
        if summary.get("channels"):
            print("  渠道列表:")
            for channel in summary["channels"]:
                status = "启用" if channel["enabled"] else "禁用"
                print(f"    - {channel['type']}: {status} (优先级: {channel['priority']}, 超时: {channel['timeout']}s)")
    
    def _print_validation_result(self, result: Dict[str, Any]) -> None:
        """打印验证结果"""
        if result.get("errors"):
            print("  错误:")
            for error in result["errors"]:
                print(f"    ❌ {error}")
        
        if result.get("warnings"):
            print("  警告:")
            for warning in result["warnings"]:
                print(f"    ⚠️ {warning}")
        
        if result.get("channels_validated"):
            print("  渠道验证:")
            for channel in result["channels_validated"]:
                status = "✅" if channel["valid"] else "❌"
                print(f"    {status} {channel['channel_type']}")
                
                if channel.get("errors"):
                    for error in channel["errors"]:
                        print(f"        ❌ {error}")
                
                if channel.get("warnings"):
                    for warning in channel["warnings"]:
                        print(f"        ⚠️ {warning}")
    
    def _print_migration_result(self, result: Dict[str, Any]) -> None:
        """打印迁移结果"""
        print(f"  迁移类型: {result.get('migration_type', 'unknown')}")
        print(f"  策略迁移数: {result.get('strategies_migrated', 0)}")
        print(f"  渠道创建数: {result.get('channels_created', 0)}")
        print(f"  备份已创建: {'是' if result.get('backup_created') else '否'}")
        
        if result.get("target_config_id"):
            print(f"  新配置ID: {result['target_config_id']}")
        
        if result.get("warnings"):
            print("  警告:")
            for warning in result["warnings"]:
                print(f"    ⚠️ {warning}")
        
        if result.get("errors"):
            print("  错误:")
            for error in result["errors"]:
                print(f"    ❌ {error}")


async def main():
    """主入口函数"""
    parser = argparse.ArgumentParser(description="AutoTradeManager配置管理工具")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 初始化配置命令
    init_parser = subparsers.add_parser("init", help="初始化默认配置")
    init_parser.add_argument("--force", action="store_true", help="强制覆盖现有配置")
    init_parser.add_argument("--config-file", help="自定义配置文件路径")
    
    # 验证配置命令
    validate_parser = subparsers.add_parser("validate", help="验证配置")
    validate_parser.add_argument("--config", default="auto_trade_manager", help="配置名称")
    
    # 迁移配置命令
    migrate_parser = subparsers.add_parser("migrate", help="迁移配置")
    migrate_parser.add_argument("--source", default="kol_activity", help="源配置名称")
    migrate_parser.add_argument("--target", default="auto_trade_manager", help="目标配置名称")
    migrate_parser.add_argument("--no-preserve", action="store_true", help="不保留源配置")
    migrate_parser.add_argument("--no-backup", action="store_true", help="不创建备份")
    
    # 显示状态命令
    status_parser = subparsers.add_parser("status", help="显示配置状态")
    status_parser.add_argument("--config", default="auto_trade_manager", help="配置名称")
    
    # 更新配置命令
    update_parser = subparsers.add_parser("update", help="更新配置")
    update_parser.add_argument("--config", default="auto_trade_manager", help="配置名称")
    update_parser.add_argument("--updates-file", help="更新参数文件路径")
    update_parser.add_argument("--enabled", type=bool, help="启用/禁用自动交易")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 初始化数据库
    try:
        await init_db()
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        print(f"❌ 数据库初始化失败: {e}")
        return
    
    # 执行命令
    cli = ConfigCLI()
    
    try:
        if args.command == "init":
            await cli.init_config(args.force, args.config_file)
        
        elif args.command == "validate":
            await cli.validate_config(args.config)
        
        elif args.command == "migrate":
            await cli.migrate_config(
                source=args.source,
                target=args.target,
                preserve_source=not args.no_preserve,
                backup=not args.no_backup
            )
        
        elif args.command == "status":
            await cli.show_status(args.config)
        
        elif args.command == "update":
            kwargs = {}
            if args.enabled is not None:
                kwargs["enabled"] = args.enabled
            
            await cli.update_config(
                config_name=args.config,
                updates_file=args.updates_file,
                **kwargs
            )
        
        else:
            parser.print_help()
    
    except KeyboardInterrupt:
        print("\n⏹️ 操作已取消")
    except Exception as e:
        logger.error(f"执行命令时出错: {e}", exc_info=True)
        print(f"❌ 执行失败: {e}")


if __name__ == "__main__":
    asyncio.run(main()) 