"""
卖出策略组件单元测试

测试SellStrategy类的各项功能
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from utils.backtest_v2.sell_strategy import SellStrategy
from utils.backtest_v2.config_manager import BacktestConfigV2


class TestSellStrategy:
    """卖出策略组件测试"""
    
    @pytest.fixture
    def sample_config(self):
        """创建测试配置"""
        return BacktestConfigV2(
            backtest_start_time=**********,  # 2022-01-01
            backtest_end_time=**********,    # 2022-01-02
            transaction_min_amount=100,
            kol_account_min_txs=5,
            kol_account_max_txs=1000,
            kol_account_min_count=3,
            token_mint_lookback_hours=24,
            transaction_lookback_hours=1,
            sell_strategy_hours=12,
            sell_kol_ratio=0.3,
            initial_capital=1000.0
        )
    
    @pytest.fixture
    def sell_strategy(self, sample_config):
        """创建卖出策略实例"""
        return SellStrategy(sample_config)
    
    def test_initialization(self, sell_strategy, sample_config):
        """测试初始化"""
        assert sell_strategy.config == sample_config
        assert sell_strategy.sell_strategy_hours == sample_config.sell_strategy_hours
        assert sell_strategy.sell_kol_ratio == sample_config.sell_kol_ratio
        assert sell_strategy.activity_dao is not None
        assert sell_strategy.price_spider is None  # 延迟初始化
    
    def test_configuration_parameters(self, sell_strategy):
        """测试配置参数"""
        assert sell_strategy.sell_strategy_hours == 12
        assert sell_strategy.sell_kol_ratio == 0.3
        assert hasattr(sell_strategy, 'sell_data_cache')
    
    @pytest.mark.asyncio
    async def test_preload_sell_data_mock(self, sell_strategy):
        """测试卖出数据预加载（使用mock）"""
        # Mock聚合查询结果
        mock_results = [
            {
                "_id": "addr1",
                "records": [
                    {"timestamp": 1640995500, "wallet": "kol1", "cost_usd": 300, "event_type": "sell"},
                    {"timestamp": 1640995600, "wallet": "kol2", "cost_usd": 400, "event_type": "sell"}
                ]
            }
        ]
        
        mock_cursor = AsyncMock()
        mock_cursor.to_list = AsyncMock(return_value=mock_results)
        
        # Mock DAO的aggregate方法
        with patch.object(sell_strategy.activity_dao.collection, 'aggregate', return_value=mock_cursor):
            # 执行预加载
            await sell_strategy.preload_sell_data(**********, **********)
        
        # 验证结果
        assert hasattr(sell_strategy, 'sell_data_cache')
        assert "addr1" in sell_strategy.sell_data_cache
        assert len(sell_strategy.sell_data_cache["addr1"]) == 2
    
    def test_check_kol_sell_ratio(self, sell_strategy):
        """测试KOL卖出比例检查"""
        # 准备测试数据
        token_address = "test_token"
        signal_timestamp = 1640995400
        kol_wallets = ["kol1", "kol2", "kol3", "kol4", "kol5"]  # 5个KOL
        
        # Mock卖出数据
        sell_strategy.sell_data_cache = {
            token_address: [
                {"timestamp": 1640995500, "wallet": "kol1", "cost_usd": 300},  # 1个KOL卖出
                {"timestamp": 1640995600, "wallet": "kol3", "cost_usd": 400}   # 另1个KOL卖出
            ]
        }
        
        # 检查卖出比例 - 2/5 = 0.4 > 0.3，应该触发卖出
        should_sell, sell_timestamp = sell_strategy._check_kol_sell_ratio(
            token_address, signal_timestamp, kol_wallets
        )
        
        assert should_sell == True
        assert sell_timestamp == 1640995600  # 最后一个卖出的时间戳
    
    def test_check_kol_sell_ratio_insufficient(self, sell_strategy):
        """测试KOL卖出比例不足的情况"""
        # 准备测试数据
        token_address = "test_token"
        signal_timestamp = 1640995400
        kol_wallets = ["kol1", "kol2", "kol3", "kol4", "kol5"]  # 5个KOL
        
        # Mock卖出数据 - 只有1个KOL卖出
        sell_strategy.sell_data_cache = {
            token_address: [
                {"timestamp": 1640995500, "wallet": "kol1", "cost_usd": 300}
            ]
        }
        
        # 检查卖出比例 - 1/5 = 0.2 < 0.3，不应该触发卖出
        should_sell, sell_timestamp = sell_strategy._check_kol_sell_ratio(
            token_address, signal_timestamp, kol_wallets
        )
        
        assert should_sell == False
        assert sell_timestamp is None
    
    def test_check_timeout_sell(self, sell_strategy):
        """测试超时卖出检查"""
        signal_timestamp = 1640995400
        current_time = signal_timestamp + 13 * 3600  # 13小时后，超过12小时限制
        
        # 应该触发超时卖出
        should_sell, sell_timestamp = sell_strategy._check_timeout_sell(signal_timestamp, current_time)
        
        assert should_sell == True
        assert sell_timestamp == current_time
        
        # 测试未超时的情况
        current_time = signal_timestamp + 10 * 3600  # 10小时后，未超时
        should_sell, sell_timestamp = sell_strategy._check_timeout_sell(signal_timestamp, current_time)
        
        assert should_sell == False
        assert sell_timestamp is None
    
    @pytest.mark.asyncio
    async def test_get_sell_price_mock(self, sell_strategy):
        """测试获取卖出价格（使用mock）"""
        token_address = "test_token"
        sell_timestamp = 1640995500
        
        # Mock GMGN价格接口
        mock_price_data = {"close": 0.0015}
        
        with patch.object(sell_strategy, '_ensure_price_spider') as mock_ensure:
            mock_ensure.return_value = None
            
            with patch.object(sell_strategy, 'price_spider') as mock_spider:
                mock_spider.get_price_point_in_time = AsyncMock(return_value=mock_price_data)
                
                # 获取价格
                price = await sell_strategy._get_sell_price(token_address, sell_timestamp)
        
        # 验证结果
        assert price == 0.0015
    
    @pytest.mark.asyncio
    async def test_get_sell_price_fallback(self, sell_strategy):
        """测试获取卖出价格失败时的回退"""
        token_address = "test_token"
        sell_timestamp = 1640995500
        
        # Mock GMGN价格接口返回空数据
        with patch.object(sell_strategy, '_ensure_price_spider') as mock_ensure:
            mock_ensure.return_value = None
            
            with patch.object(sell_strategy, 'price_spider') as mock_spider:
                mock_spider.get_price_point_in_time = AsyncMock(return_value={})
                
                # 获取价格
                price = await sell_strategy._get_sell_price(token_address, sell_timestamp)
        
        # 验证结果 - 应该返回0
        assert price == 0.0
    
    def test_get_sell_data_for_token(self, sell_strategy):
        """测试获取特定token的卖出数据"""
        # 准备测试数据
        sell_strategy.sell_data_cache = {
            "addr1": [
                {"timestamp": 1640995500, "wallet": "kol1", "cost_usd": 300},
                {"timestamp": 1640995600, "wallet": "kol2", "cost_usd": 400}
            ],
            "addr2": [
                {"timestamp": 1640995700, "wallet": "kol3", "cost_usd": 500}
            ]
        }
        
        # 获取addr1的卖出数据
        sell_data = sell_strategy.get_sell_data_for_token("addr1")
        assert len(sell_data) == 2
        assert sell_data[0]["wallet"] == "kol1"
        
        # 获取不存在token的卖出数据
        sell_data = sell_strategy.get_sell_data_for_token("addr_not_exist")
        assert len(sell_data) == 0
    
    def test_sell_data_cache_structure(self, sell_strategy):
        """测试卖出数据缓存结构"""
        # 验证初始状态
        assert hasattr(sell_strategy, 'sell_data_cache')
        
        # 设置测试数据
        sell_strategy.sell_data_cache = {
            "test_token": [
                {"timestamp": 1640995500, "wallet": "kol1", "cost_usd": 300, "event_type": "sell"}
            ]
        }
        
        # 验证数据结构
        assert "test_token" in sell_strategy.sell_data_cache
        sell_record = sell_strategy.sell_data_cache["test_token"][0]
        assert "timestamp" in sell_record
        assert "wallet" in sell_record
        assert "cost_usd" in sell_record
        assert "event_type" in sell_record


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
