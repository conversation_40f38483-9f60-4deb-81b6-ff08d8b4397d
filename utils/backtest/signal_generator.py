import logging
from typing import Dict, Any, List, Optional
from models.token import Token
from utils.strategies.base_strategy import BaseStrategy

logger = logging.getLogger("SignalGenerator")

class SignalGenerator:
    """信号生成器，负责协调策略生成交易信号"""
    
    def __init__(self, buy_strategy: BaseStrategy, sell_strategy: BaseStrategy):
        """初始化信号生成器
        
        Args:
            buy_strategy: 买入策略
            sell_strategy: 卖出策略
        """
        self.buy_strategy = buy_strategy
        self.sell_strategy = sell_strategy
        self.buy_signals = []
        
    async def generate_buy_signals(self,
        start_time: int,
        end_time: int,
        min_amount: float,
        kol_account_min_txs: int,
        kol_account_max_txs: int,
        min_kol_count: int,
        token_mint_lookback_hours: int
        ) -> List[Token]:
        """生成买入信号
        
        Args:
            time_point: 当前时间点
            activities: KOL活动数据 (对于KOLBuyStrategy不需要，但保留参数以兼容其他策略)
            tokens: 代币数据 (对于KOLBuyStrategy不需要，但保留参数以兼容其他策略)
            wallets: KOL钱包数据
            
        Returns:
            List[Dict[str, Any]]: 买入信号列表
        """
        signals = await self.buy_strategy.generate_signals(
            start_time, end_time, min_amount, kol_account_min_txs, kol_account_max_txs, min_kol_count, token_mint_lookback_hours
        )
        
        # 保存买入信号，用于后续生成卖出信号
        self.buy_signals.extend(signals)
        
        return signals
    
    async def generate_sell_signals(self, 
                                  time_point: int, 
                                  activities: List[Dict[str, Any]], 
                                  tokens: List[Dict[str, Any]], 
                                  wallets: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """生成卖出信号
        
        Args:
            time_point: 当前时间点
            activities: KOL活动数据
            tokens: 代币数据
            wallets: KOL钱包数据
            
        Returns:
            List[Dict[str, Any]]: 卖出信号列表
        """
        signals = await self.sell_strategy.generate_signals(
            time_point=time_point,
            activities=activities,
            tokens=tokens,
            wallets=wallets,
            buy_signals=self.buy_signals
        )
        
        return signals 