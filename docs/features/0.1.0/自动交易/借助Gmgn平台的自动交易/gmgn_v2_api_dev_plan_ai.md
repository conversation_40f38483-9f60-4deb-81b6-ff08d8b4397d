# GMGN v2 API 交易接口技术实现方案

## 1. 架构设计

### 1.1 整体架构
```
GmgnTradeServiceV2 (Python)
├── HTTP Client (httpx)
├── Solana Transaction Handler (solders)
├── Error Handler
├── Status Monitor
└── Configuration Manager
```

### 1.2 与现有系统的关系
- 实现`TradeInterface`抽象类
- 作为独立的交易接口实现，与现有的GMGN v1服务并行存在
- 在交易渠道注册时使用不同的标识符进行区分（如"gmgn_v2"）
- 遵循相同的`TradeInterface`规范，但内部实现完全独立

## 2. 核心组件设计

### 2.1 主要类结构

#### 2.1.1 GmgnTradeServiceV2
```python
class GmgnTradeServiceV2(TradeInterface):
    def __init__(self, gmgn_api_host: str, http_client: Optional[httpx.AsyncClient] = None):
        self.gmgn_api_host = gmgn_api_host.rstrip('/')
        self.http_client = http_client or httpx.AsyncClient(timeout=30.0)
        
    async def execute_trade(self, ...) -> TradeResult:
        # 主要交易执行逻辑（实现TradeInterface抽象方法）
        
    def is_slippage_related_error(self, ...) -> bool:
        # 滑点错误识别（实现TradeInterface抽象方法）
        
    def is_non_retryable_error(self, ...) -> bool:
        # 不可重试错误识别（可选覆盖TradeInterface默认实现）
        
    async def _get_swap_route(self, ...) -> Dict[str, Any]:
        # 获取交易路由
        
    async def _submit_transaction(self, ...) -> Dict[str, Any]:
        # 提交签名交易
        
    async def _monitor_transaction_status(self, ...) -> Dict[str, Any]:
        # 监控交易状态
        
    def _sign_transaction(self, ...) -> str:
        # 签名交易
        
    async def close(self):
        # 资源清理
```

#### 2.1.2 API端点常量
```python
GMGN_API_ENDPOINTS = {
    "GET_SWAP_ROUTE": "/defi/router/v1/sol/tx/get_swap_route",
    "SUBMIT_TRANSACTION": "/txproxy/v1/send_transaction", 
    "GET_TRANSACTION_STATUS": "/defi/router/v1/sol/tx/get_transaction_status"
}
```

### 2.2 交易流程实现

#### 2.2.1 路由查询阶段
```python
async def _get_swap_route(
    self,
    token_in_address: str,
    token_out_address: str, 
    in_amount: str,
    from_address: str,
    slippage: float,
    fee: float,
    is_anti_mev: bool = False,
    partner: Optional[str] = None
) -> Dict[str, Any]:
    """
    调用GMGN API获取交易路由
    
    Returns:
        包含quote和raw_tx的响应数据
    """
    params = {
        "token_in_address": token_in_address,
        "token_out_address": token_out_address,
        "in_amount": in_amount,
        "from_address": from_address,
        "slippage": slippage,
        "fee": fee
    }
    
    if is_anti_mev:
        params["is_anti_mev"] = "true"
    if partner:
        params["partner"] = partner
        
    url = f"{self.gmgn_api_host}{GMGN_API_ENDPOINTS['GET_SWAP_ROUTE']}"
    response = await self.http_client.get(url, params=params)
    
    # 错误处理和响应解析
    return response.json()
```

#### 2.2.2 交易签名阶段
```python
def _sign_transaction(
    self, 
    swap_transaction_b64: str, 
    private_key_b58: str
) -> str:
    """
    使用私钥签名交易
    
    Args:
        swap_transaction_b64: base64编码的待签名交易
        private_key_b58: base58编码的私钥
        
    Returns:
        base64编码的已签名交易
    """
    from solders.keypair import Keypair
    from solders.transaction import VersionedTransaction
    import base64
    import bs58
    
    # 解码交易
    transaction_bytes = base64.b64decode(swap_transaction_b64)
    transaction = VersionedTransaction.from_bytes(transaction_bytes)
    
    # 创建密钥对
    private_key_bytes = bs58.decode(private_key_b58)
    keypair = Keypair.from_bytes(private_key_bytes)
    
    # 签名交易
    transaction.sign([keypair])
    
    # 返回base64编码的签名交易
    return base64.b64encode(bytes(transaction)).decode()
```

#### 2.2.3 交易提交阶段
```python
async def _submit_transaction(
    self,
    signed_tx: str,
    is_anti_mev: bool = False
) -> Dict[str, Any]:
    """
    提交已签名的交易
    
    Returns:
        包含交易哈希的响应数据
    """
    payload = {
        "chain": "sol",
        "signedTx": signed_tx
    }
    
    if is_anti_mev:
        payload["isAntiMev"] = True
        
    url = f"{self.gmgn_api_host}{GMGN_API_ENDPOINTS['SUBMIT_TRANSACTION']}"
    response = await self.http_client.post(url, json=payload)
    
    return response.json()
```

#### 2.2.4 状态监控阶段
```python
async def _monitor_transaction_status(
    self,
    tx_hash: str,
    last_valid_height: int,
    max_attempts: int = 12,
    poll_interval: int = 5
) -> Dict[str, Any]:
    """
    监控交易状态直到确认或超时
    
    Returns:
        最终的交易状态
    """
    for attempt in range(max_attempts):
        params = {
            "hash": tx_hash,
            "last_valid_height": last_valid_height
        }
        
        url = f"{self.gmgn_api_host}{GMGN_API_ENDPOINTS['GET_TRANSACTION_STATUS']}"
        response = await self.http_client.get(url, params=params)
        status_data = response.json()
        
        if status_data.get("data", {}).get("success") or \
           status_data.get("data", {}).get("expired") or \
           status_data.get("data", {}).get("failed"):
            return status_data
            
        await asyncio.sleep(poll_interval)
    
    # 超时情况
    return {"data": {"expired": True, "success": False}}
```

### 2.3 错误处理设计

#### 2.3.1 滑点错误识别（实现TradeInterface抽象方法）
```python
def is_slippage_related_error(
    self, 
    error_message: Optional[str], 
    provider_response: Optional[Dict[str, Any]] = None
) -> bool:
    """
    实现TradeInterface的抽象方法 - GMGN API特定的滑点错误识别
    
    Args:
        error_message: 错误信息文本
        provider_response: 原始API响应（可选，用于更精确的判断）
        
    Returns:
        bool: 是否为滑点相关错误
    """
    if not error_message:
        return False
        
    # GMGN API特定的滑点相关关键词
    gmgn_slippage_keywords = [
        "route failed", "swap failed", "price change", 
        "market impact", "liquidity insufficient",
        "output amount too low", "insufficient output amount",
        "price moved too much", "slippage", "price impact",
        "slippage tolerance exceeded", "exceeds maximum slippage"
    ]
    
    error_lower = error_message.lower()
    for keyword in gmgn_slippage_keywords:
        if keyword in error_lower:
            return True
            
    # 检查provider_response中的错误码（GMGN API特定）
    if provider_response:
        # 检查路由响应中的错误
        if isinstance(provider_response, dict):
            route_response = provider_response.get('route_response', {})
            if route_response.get('code') != 0:
                route_msg = route_response.get('msg', '').lower()
                if any(keyword in route_msg for keyword in gmgn_slippage_keywords):
                    return True
            
            # 检查提交响应中的错误
            submit_response = provider_response.get('submit_response', {})
            if submit_response.get('code') != 0:
                submit_msg = submit_response.get('msg', '').lower()
                if any(keyword in submit_msg for keyword in gmgn_slippage_keywords):
                    return True
            
    return False
```

#### 2.3.2 不可重试错误识别（继承TradeInterface默认实现）
```python
def is_non_retryable_error(
    self, 
    error_message: Optional[str], 
    provider_response: Optional[Dict[str, Any]] = None
) -> bool:
    """
    扩展TradeInterface的默认实现 - 添加GMGN API特定的不可重试错误
    
    Args:
        error_message: 错误信息文本
        provider_response: 原始API响应（可选）
        
    Returns:
        bool: 是否为不可重试错误
    """
    # 调用父类通用实现（TradeInterface提供的默认实现）
    if super().is_non_retryable_error(error_message, provider_response):
        return True
        
    if not error_message:
        return False
        
    # GMGN API特定的不可重试错误关键词
    gmgn_non_retryable_keywords = [
        "invalid wallet", "private key invalid", "token not supported",
        "invalid token address", "api key invalid", "unauthorized access",
        "invalid parameters", "amount too small", "amount too large",
        "wallet not found", "transaction malformed"
    ]
    
    error_lower = error_message.lower()
    for keyword in gmgn_non_retryable_keywords:
        if keyword in error_lower:
            return True
    
    # 检查GMGN API响应中的特定错误码
    if provider_response and isinstance(provider_response, dict):
        route_response = provider_response.get('route_response', {})
        if route_response.get('code') in [400, 401, 403, 404]:  # HTTP客户端错误
            return True
            
    return False
```

## 3. 数据流设计

### 3.1 输入数据转换
```python
def _prepare_route_params(
    self,
    input_token_address: str,
    output_token_address: str,
    amount_input_token: float,
    wallet_address: str,
    strategy_snapshot: Dict[str, Any],
    trade_type: TradeType
) -> Dict[str, Any]:
    """
    将交易参数转换为GMGN v2 API格式
    """
    # 计算lamports数量
    if input_token_address == SOL_MINT_ADDRESS:
        decimals = 9
    else:
        decimals = strategy_snapshot.get('input_token_decimals', 9)
    
    in_amount = str(int(amount_input_token * (10 ** decimals)))
    
    # 提取GMGN v2特有的策略参数
    if trade_type == TradeType.BUY:
        slippage = strategy_snapshot.get('gmgn_v2_buy_slippage_percentage', 0.5)
        priority_fee = strategy_snapshot.get('gmgn_v2_buy_priority_fee', 0.00005)
    else:  # SELL
        slippage = strategy_snapshot.get('gmgn_v2_sell_slippage_percentage', 1.0)
        priority_fee = strategy_snapshot.get('gmgn_v2_sell_priority_fee', 0.0001)
    
    is_anti_mev = strategy_snapshot.get('gmgn_v2_anti_mev', False)
    partner = strategy_snapshot.get('gmgn_v2_partner')
    
    params = {
        "token_in_address": input_token_address,
        "token_out_address": output_token_address,
        "in_amount": in_amount,
        "from_address": wallet_address,
        "slippage": slippage,
        "fee": priority_fee,
        "is_anti_mev": is_anti_mev
    }
    
    if partner:
        params["partner"] = partner
        
    return params
```

### 3.2 输出数据转换
```python
def _parse_trade_result(
    self,
    route_response: Dict[str, Any],
    submit_response: Dict[str, Any],
    status_response: Dict[str, Any],
    trade_record_id: PydanticObjectId
) -> TradeResult:
    """
    将GMGN API响应转换为TradeResult格式
    """
    # 提取交易哈希
    tx_hash = submit_response.get("data", {}).get("hash")
    
    # 确定交易状态
    status_data = status_response.get("data", {})
    if status_data.get("success"):
        status = TradeStatus.SUCCESS
        error_message = None
    elif status_data.get("failed"):
        status = TradeStatus.FAILED
        error_message = "Transaction failed on chain"
    elif status_data.get("expired"):
        status = TradeStatus.FAILED
        error_message = "Transaction expired"
    else:
        status = TradeStatus.PENDING
        error_message = "Transaction status unknown"
    
    # 提取实际交易数量
    quote_data = route_response.get("data", {}).get("quote", {})
    actual_amount_in = quote_data.get("inAmount")
    actual_amount_out = quote_data.get("outAmount")
    
    return TradeResult(
        status=status,
        tx_hash=tx_hash,
        error_message=error_message,
        provider_response_raw={
            "route_response": route_response,
            "submit_response": submit_response,
            "status_response": status_response
        },
        actual_amount_in=float(actual_amount_in) if actual_amount_in else None,
        actual_amount_out=float(actual_amount_out) if actual_amount_out else None,
        executed_at=datetime.now(timezone.utc),
        trade_record_id=trade_record_id
    )
```

## 4. 配置和部署

### 4.1 依赖管理
在`pyproject.toml`中添加新依赖：
```toml
[tool.poetry.dependencies]
httpx = "^0.24.0"
solders = "^0.18.0"  # 或 solana-py
```

### 4.2 配置选项
```python
# GMGN v2特有的配置项
GMGN_V2_CONFIG_KEYS = {
    "gmgn_v2_api_host": "https://gmgn.ai",  # v2专用API主机
    "gmgn_v2_anti_mev": False,  # 是否启用防夹交易
    "gmgn_v2_partner": None,  # 合作伙伴标识
    "gmgn_v2_timeout": 30.0,  # HTTP超时时间
    "gmgn_v2_max_poll_attempts": 12,  # 最大轮询次数
    "gmgn_v2_poll_interval": 5,  # 轮询间隔（秒）
    "gmgn_v2_buy_slippage_percentage": 0.5,  # 买入滑点
    "gmgn_v2_sell_slippage_percentage": 1.0,  # 卖出滑点
    "gmgn_v2_buy_priority_fee": 0.00005,  # 买入优先费
    "gmgn_v2_sell_priority_fee": 0.0001  # 卖出优先费
}
```

### 4.3 渠道注册
```python
# 在交易渠道注册时使用独立的标识符
def register_gmgn_v2_channel():
    """注册GMGN v2交易渠道"""
    channel_registry.register_channel(
        channel_type="gmgn_v2",  # 独立的渠道标识符
        interface_class=GmgnTradeServiceV2,
        config=TradeChannelConfig(
            enabled=True,
            priority=1,
            timeout_seconds=30,
            max_retries=3
        )
    )
```

## 5. 测试策略

### 5.1 单元测试
- 测试每个API调用方法
- 测试错误处理逻辑
- 测试数据转换函数
- Mock HTTP响应进行测试

### 5.2 集成测试
- 端到端交易流程测试
- 与现有系统的兼容性测试
- 性能对比测试（vs Node.js版本）

### 5.3 测试用例设计
```python
class TestGmgnTradeServiceV2:
    async def test_successful_buy_trade(self):
        # 测试成功的买入交易
        
    async def test_successful_sell_trade(self):
        # 测试成功的卖出交易
        
    async def test_slippage_error_handling(self):
        # 测试滑点错误处理
        
    async def test_transaction_timeout(self):
        # 测试交易超时处理
        
    async def test_api_error_handling(self):
        # 测试API错误处理
```

## 6. 性能优化

### 6.1 HTTP连接复用
- 使用单一的`httpx.AsyncClient`实例
- 配置连接池和超时参数
- 支持HTTP/2和连接保持

### 6.2 并发处理
- 支持多个交易并发执行
- 使用异步编程模式
- 避免阻塞操作

### 6.3 错误重试
- 网络错误自动重试
- 指数退避策略
- 最大重试次数限制

## 7. 监控和日志

### 7.1 日志记录
```python
logger.info(f"[TradeRec:{trade_record_id}] Starting GMGN v2 trade: {amount_input_token} {input_token_address} -> {output_token_address}")
logger.debug(f"[TradeRec:{trade_record_id}] Route params: {route_params}")
logger.info(f"[TradeRec:{trade_record_id}] Transaction submitted: {tx_hash}")
logger.info(f"[TradeRec:{trade_record_id}] Trade completed: {status}")
```

### 7.2 性能指标
- API调用延迟
- 交易成功率
- 错误分类统计
- 资源使用情况

## 8. 部署计划

### 8.1 阶段1：基础实现
- 实现核心交易功能
- 基本错误处理
- 单元测试

### 8.2 阶段2：集成测试
- 与现有系统集成
- 端到端测试
- 性能测试

### 8.3 阶段3：生产部署
- 配置管理
- 监控告警
- 文档更新

## 9. 风险缓解

### 9.1 独立部署策略
- 作为独立渠道与现有GMGN v1并行运行
- 可以独立启用/禁用v2渠道
- 不影响现有v1渠道的运行

### 9.2 监控告警
- 交易失败率监控
- API错误率监控
- 性能指标监控

### 9.3 测试验证
- 全面的测试覆盖
- 生产环境小规模测试
- 逐步扩大使用范围 