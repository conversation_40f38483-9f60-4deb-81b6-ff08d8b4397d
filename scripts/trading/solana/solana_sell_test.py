#!/usr/bin/env python3
"""
Solana直接交易服务 - 卖出测试脚本
测试将代币（如Fartcoin）卖出换取SOL
"""

import asyncio
import logging
import os
from datetime import datetime
from utils.trading.solana.jupiter_trade_service import JupiterTradeService, create_jupiter_trade_service, SOL_MINT_ADDRESS
from utils.trading.solana.trade_interface import TradeType, TradeStatus
from beanie import PydanticObjectId

async def main():
    """测试卖出功能的主函数"""
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("🚀 Solana直接交易服务 - 卖出测试")
    print("=" * 50)
    print(f"⏰ 测试时间: {datetime.now()}")
    print()
    
    # 从环境变量获取钱包信息
    wallet_private_key = os.getenv('WALLET_PRIVATE_KEY')
    wallet_address = os.getenv('WALLET_ADDRESS')
    
    if not wallet_private_key or not wallet_address:
        print("❌ 错误：请设置环境变量")
        print("   WALLET_PRIVATE_KEY=你的钱包私钥（Base58格式）")
        print("   WALLET_ADDRESS=你的钱包地址")
        print()
        print("示例:")
        print("   export WALLET_PRIVATE_KEY='your_base58_private_key_here'")
        print("   export WALLET_ADDRESS='your_wallet_address_here'")
        return
    
    # 卖出配置 - 设置为卖出代币换取SOL
    input_token_address = "9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump"  # Fartcoin (我们之前买入的)
    output_token_address = SOL_MINT_ADDRESS  # SOL
    
    # 卖出数量 - 您可以根据实际持有量调整
    # 注意：这里需要设置您想要卖出的Fartcoin数量（UI单位）
    sell_amount = float(os.getenv('SELL_AMOUNT', '0.1'))  # 默认卖出0.1个代币，您可以通过环境变量调整
    
    # 卖出交易配置
    sell_config = {
        'jupiter_api_host': 'https://quote-api.jup.ag',
        'solana_rpc_url': 'https://api.mainnet-beta.solana.com',
        'http_timeout': 30.0,
        'sell_max_slippage_bps': 150,     # 卖出滑点设置为1.5%（通常比买入稍高）
        'sell_priority_fee_lamports': 75000,  # 卖出优先费设置稍高
        'input_token_decimals': 6,        # Fartcoin的精度（大多数meme币是6位）
        'output_token_decimals': 9,       # SOL的精度
        'enable_smart_routing': False     # 🔧 禁用智能路由，便于统计
    }
    
    print(f"📋 卖出交易配置:")
    print(f"   输入代币: Fartcoin")
    print(f"   输入代币地址: {input_token_address}")
    print(f"   输出代币: SOL")
    print(f"   卖出数量: {sell_amount} Fartcoin")
    print(f"   滑点: {sell_config['sell_max_slippage_bps']/100}%")
    print(f"   优先费: {sell_config['sell_priority_fee_lamports']} lamports")
    print(f"   钱包地址: {wallet_address}")
    print()
    
    # 安全确认
    print("⚠️  这将执行真实的卖出交易，是否继续？(y/N): ", end="")
    confirmation = input().strip().lower()
    if confirmation != 'y':
        print("❌ 取消执行")
        return
    
    print()
    
    # 创建交易服务
    print("🔧 创建交易服务...")
    service = create_jupiter_trade_service(sell_config)
    
    try:
        # 生成测试用的ID
        test_signal_id = PydanticObjectId()
        test_trade_record_id = PydanticObjectId()
        
        print(f"🎯 开始执行卖出交易...")
        print(f"   Signal ID: {test_signal_id}")
        print(f"   Trade Record ID: {test_trade_record_id}")
        print()
        
        # 执行卖出交易
        result = await service.execute_trade(
            trade_type=TradeType.SELL,  # 卖出交易
            input_token_address=input_token_address,  # Fartcoin
            output_token_address=output_token_address,  # SOL
            amount_input_token=sell_amount,
            wallet_private_key_b58=wallet_private_key,
            wallet_address=wallet_address,
            strategy_snapshot=sell_config,
            signal_id=test_signal_id,
            trade_record_id=test_trade_record_id
        )
        
        # 输出结果
        print("📊 卖出交易结果:")
        print(f"   状态: {result.status.value}")
        
        if result.status == TradeStatus.SUCCESS:
            print(f"✅ 卖出成功!")
            print(f"   交易哈希: {result.tx_hash}")
            print(f"   执行时间: {result.executed_at}")
            print(f"   卖出数量: {result.actual_amount_in} (最小单位)")
            print(f"   获得SOL: {result.actual_amount_out} lamports")
            
            # 计算实际获得的SOL数量（UI单位）
            if result.actual_amount_out:
                sol_received = result.actual_amount_out / 1_000_000_000  # 转换为SOL
                print(f"   获得SOL: ~{sol_received:.6f} SOL")
            
            if result.tx_hash:
                print(f"   🔗 Solscan链接: https://solscan.io/tx/{result.tx_hash}")
                print(f"   🔗 Explorer链接: https://explorer.solana.com/tx/{result.tx_hash}")
                
        elif result.status == TradeStatus.PENDING:
            print(f"⏳ 交易待确认:")
            print(f"   交易哈希: {result.tx_hash}")
            print(f"   错误信息: {result.error_message}")
            
            if result.tx_hash:
                print(f"   🔗 Solscan链接: https://solscan.io/tx/{result.tx_hash}")
                print(f"   🔗 Explorer链接: https://explorer.solana.com/tx/{result.tx_hash}")
                
        else:  # FAILED
            print(f"❌ 卖出失败:")
            print(f"   错误信息: {result.error_message}")
            
            # 分析可能的失败原因
            if "insufficient" in str(result.error_message).lower():
                print(f"   💡 可能原因: 代币余额不足")
                print(f"   💡 建议: 检查钱包中的Fartcoin余额，或者减少卖出数量")
            elif "no route" in str(result.error_message).lower():
                print(f"   💡 可能原因: Jupiter找不到交易路径")
                print(f"   💡 建议: 等待一段时间后重试，或者检查代币是否仍然活跃")
            
    except Exception as e:
        print(f"💥 执行过程中发生异常: {e}")
        logging.error("Sell test error", exc_info=True)
        
    finally:
        # 清理资源
        print("\n🧹 清理资源...")
        await service.close()
        print("✨ 卖出测试完成")


if __name__ == "__main__":
    """
    使用方法:
    
    1. 设置环境变量:
       export WALLET_PRIVATE_KEY='your_base58_private_key_here'
       export WALLET_ADDRESS='your_wallet_address_here' 
       export SELL_AMOUNT='0.1'  # 可选，设置要卖出的代币数量
    
    2. 运行脚本:
       python test_jupiter_trade_sell.py
    
    注意事项:
    - 确保钱包中有足够的Fartcoin余额
    - 确保钱包中有足够的SOL支付交易费用
    - 卖出金额请根据实际持有量设置
    """
    print("🚀 启动卖出测试...")
    asyncio.run(main()) 