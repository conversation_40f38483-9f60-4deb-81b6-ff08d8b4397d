# KOL 活动延迟监控 - 技术实现方案 (AI生成)

## 1. 概述

本方案描述了KOL活动时间字段差异监控功能的实现细节。该功能将实现为一个**工作流**，每10秒自动触发。它会检查两类KOL（根据`imported_from_following`状态区分）的最新活动记录（按`created_at`排序），并比较该记录内部的 `created_at` (UTC datetime) 字段与 `timestamp` (东八区 Unix timestamp) 字段。当同一类别的KOL连续3次检测到时间差异超过60秒时，才触发通过Telegram通知管理员。同时，支持配置在指定时间内抑制重复告警。所有告警事件和通知发送尝试都将被持久化到数据库中。

## 2. 模块与文件结构

1.  **新工作流目录**: `workflows/kol_activity_timestamp_discrepancy_monitor/`
2.  **新处理器文件**: `workflows/kol_activity_latency_monitor/handler.py`
    *   包含核心监控逻辑，将被工作流节点调用。
3.  **新工作流定义文件**: `workflows/kol_activity_latency_monitor/kol_activity_latency_monitor_workflow.yaml`
    *   定义工作流的节点、触发间隔等。
4.  **新数据模型**:
    *   `models/monitor_alert_state.py`: 定义用于持久化存储告警状态的模型。
    *   `models/alert_event_record.py`: 定义用于记录每次触发的告警事件的模型。
    *   `models/notification_log_record.py`: 定义用于记录每次发送通知尝试的模型。

## 3. 数据模型定义

### 3.1. `models/monitor_alert_state.py`

```python
from datetime import datetime
from typing import Optional
from beanie import Document, Indexed
from pydantic import Field

class MonitorAlertState(Document):
    """监控告警状态模型，用于持久化存储监控告警的状态信息"""
    
    monitor_type: Indexed(str) = Field(description="监控类型标识符")
    category: Indexed(str) = Field(description="监控的类别或子分类")
    consecutive_alerts: int = Field(default=0, description="连续超过阈值的次数")
    last_notification_time: Optional[datetime] = Field(default=None, description="上次发送通知的时间")
    
    class Settings:
        name = "monitor_alert_states"
        indexes = [
            [("monitor_type", 1), ("category", 1)],  # 复合索引
        ]
```

### 3.2. `models/alert_event_record.py`

```python
from datetime import datetime
from typing import Optional, Dict, Any
from beanie import Document, Indexed
from pydantic import Field

class AlertEventRecord(Document):
    """记录每一次触发的告警事件"""
    monitor_type: Indexed(str) = Field(description="监控类型标识符, e.g., kol_activity_timestamp_discrepancy")
    category: Indexed(str) = Field(description="监控的类别, e.g., imported, non_imported")
    trigger_time: Indexed(datetime) = Field(default_factory=datetime.utcnow, description="告警触发时间 (UTC)")
    
    wallet_address: Optional[str] = Field(default=None, description="相关的KOL钱包地址")
    activity_tx_hash: Optional[str] = Field(default=None, description="相关活动的交易哈希")
    
    discrepancy_seconds: Optional[float] = Field(default=None, description="计算出的时间差异秒数")
    message: str = Field(description="告警的简要描述或发送给用户的核心信息")
    details: Optional[Dict[str, Any]] = Field(default=None, description="其他与告警相关的详细信息，如updated_at, timestamp等")

    class Settings:
        name = "alert_event_records"
        indexes = [
            "monitor_type",
            "category",
            "trigger_time",
            "wallet_address",
        ]
```

### 3.3. `models/notification_log_record.py`

```python
from datetime import datetime
from typing import Optional
from beanie import Document, Indexed, Link # Link可能需要引入
from pydantic import Field
# from models.alert_event_record import AlertEventRecord # 如果使用Link

class NotificationLogRecord(Document):
    """记录每一次发送通知的尝试"""
    alert_event_raw_id: Optional[str] = Field(default=None, description="关联的告警事件记录的ObjectId字符串")
    # 或者 alert_event_id: Optional[Link[AlertEventRecord]] = Field(default=None, description="关联的告警事件记录ID")
    
    recipient_chat_id: Indexed(str) = Field(description="接收方的Telegram Chat ID")
    send_time: Indexed(datetime) = Field(default_factory=datetime.utcnow, description="通知发送/尝试时间 (UTC)")
    status: Indexed(str) = Field(description="发送状态 (e.g., success, failure)")
    message_content_preview: Optional[str] = Field(default=None, description="发送消息内容的预览或摘要", max_length=500)
    error_message: Optional[str] = Field(default=None, description="如果发送失败，记录错误信息")

    class Settings:
        name = "notification_log_records"
        indexes = [
            "alert_event_raw_id",
            "recipient_chat_id",
            "send_time",
            "status",
        ]
```

## 4. 核心逻辑实现 (`workflows/kol_activity_latency_monitor/handler.py`)

**`KolActivityTimestampDiscrepancyChecker` 类:**

*   **`__init__(self)`**: 
    *   初始化 `admin_chat_ids` 列表为空。
    *   实例化 `TelegramMessageSender`。
    *   初始化告警状态跟踪器 `_alert_state`，作为内存中的缓存：
      ```python
      self._alert_state = {
          True: {  # imported_from_following=True 的状态
              "consecutive_alerts": 0,  # 连续超过阈值的次数
              "last_notification_time": None,  # 上次通知时间
          },
          False: {  # imported_from_following=False 的状态
              "consecutive_alerts": 0,  # 连续超过阈值的次数
              "last_notification_time": None,  # 上次通知时间
          }
      }
      ```
    *   初始化默认配置值：
      ```python
      self._config = {
          "alert_threshold_seconds": 60,  # 默认告警阈值（秒）
          "consecutive_alerts_required": 3,  # 触发告警需要的连续次数
          "alert_suppression_minutes": 30,  # 告警抑制时间（分钟）
      }
      ```

*   **`async _load_config(self)`** (伪代码/文字描述):
    1.  异步查询 `Config` 集合，查找 `type == "application_config"` 的文档。
    2.  如果找到配置且配置数据有效：
        *   从中提取 `admin_telegram_chat_ids` 并存入 `self.admin_chat_ids`。
        *   尝试从配置中读取告警相关配置：
          ```python
          if "kol_activity_monitor" in config.data:
              monitor_config = config.data["kol_activity_monitor"]
              if "alert_threshold_seconds" in monitor_config:
                  self._config["alert_threshold_seconds"] = monitor_config["alert_threshold_seconds"]
              if "consecutive_alerts_required" in monitor_config:
                  self._config["consecutive_alerts_required"] = monitor_config["consecutive_alerts_required"]
              if "alert_suppression_minutes" in monitor_config:
                  self._config["alert_suppression_minutes"] = monitor_config["alert_suppression_minutes"]
          ```
        *   记录加载成功的日志。
    3.  如果未找到或 `admin_telegram_chat_ids` 为空：
        *   将 `self.admin_chat_ids` 设为空列表。
        *   记录警告日志。

*   **`async _load_alert_state(self)`** (伪代码/文字描述):
    1.  初始化默认的告警状态：
        ```python
        self._alert_state = {
            True: {"consecutive_alerts": 0, "last_notification_time": None},
            False: {"consecutive_alerts": 0, "last_notification_time": None}
        }
        ```
    2.  从数据库加载导入KOL类别（`imported_from_following=True`）的状态：
        ```python
        imported_state = await MonitorAlertState.find_one({
            "monitor_type": "kol_activity_timestamp_discrepancy",
            "category": "imported"
        })
        if imported_state:
            self._alert_state[True] = {
                "consecutive_alerts": imported_state.consecutive_alerts,
                "last_notification_time": imported_state.last_notification_time
            }
            logger.info(f"加载导入KOL的告警状态: 连续计数={imported_state.consecutive_alerts}, 上次通知时间={imported_state.last_notification_time}")
        ```
    3.  从数据库加载非导入KOL类别（`imported_from_following=False`）的状态：
        ```python
        non_imported_state = await MonitorAlertState.find_one({
            "monitor_type": "kol_activity_timestamp_discrepancy",
            "category": "non_imported"
        })
        if non_imported_state:
            self._alert_state[False] = {
                "consecutive_alerts": non_imported_state.consecutive_alerts,
                "last_notification_time": non_imported_state.last_notification_time
            }
            logger.info(f"加载非导入KOL的告警状态: 连续计数={non_imported_state.consecutive_alerts}, 上次通知时间={non_imported_state.last_notification_time}")
        ```

*   **`async _save_alert_state(self, imported_from_following: bool)`** (伪代码/文字描述):
    1.  确定类别标识：
        ```python
        category = "imported" if imported_from_following else "non_imported"
        ```
    2.  获取当前内存中的状态：
        ```python
        state = self._alert_state[imported_from_following]
        ```
    3.  更新或创建数据库中的状态记录：
        ```python
        await MonitorAlertState.update_one(
            {
                "monitor_type": "kol_activity_timestamp_discrepancy",
                "category": category
            },
            {
                "$set": {
                    "consecutive_alerts": state["consecutive_alerts"],
                    "last_notification_time": state["last_notification_time"]
                }
            },
            upsert=True  # 如果记录不存在则创建
        )
        logger.debug(f"已保存{category}类别的告警状态: 连续计数={state['consecutive_alerts']}, 上次通知时间={state['last_notification_time']}")
        ```

*   **`async _get_latest_kol_wallet(self, imported_from_following: bool)`** (伪代码/文字描述):
    1.  构建查询条件：`last_active != None`。
    2.  根据 `imported_from_following` 参数，添加条件 `imported_from_following == True` 或 `Or(imported_from_following == False, imported_from_following == None)`。
    3.  异步查询 `KOLWallet` 集合，应用上述条件，按 `last_active` 降序排序，取第一个结果。
    4.  记录找到的钱包地址或未找到钱包的调试日志。
    5.  返回找到的 `KOLWallet` 对象或 `None`。

*   **`async _get_latest_activity(self, wallet_address: str)`** (伪代码/文字描述):
    1.  异步查询 `KOLWalletActivity` 集合，条件为 `wallet == wallet_address`。
    2.  按 `created_at` 降序排序，取第一个结果。
    3.  记录找到的活动信息 (tx_hash, created_at, timestamp 字段) 或未找到活动的调试日志。
    4.  返回找到的 `KOLWalletActivity` 对象或 `None`。

*   **`async _record_alert_event(self, imported_from_following: bool, wallet_address: str, activity_tx_hash: str, discrepancy_seconds: float, message: str, details: Dict[str, Any]) -> str`** (新增方法，伪代码/文字描述):
    1.  创建 `AlertEventRecord` 实例，填充数据：
        *   `monitor_type`: "kol_activity_timestamp_discrepancy"
        *   `category`: "imported" 或 "non_imported"
        *   `wallet_address`, `activity_tx_hash`, `discrepancy_seconds`, `message`, `details`
    2.  保存 `AlertEventRecord` 到数据库。
    3.  记录成功保存告警事件的日志。
    4.  返回已保存的 `AlertEventRecord` 的ID (字符串格式)。

*   **`async _record_notification_log(self, alert_event_id: str, recipient_chat_id: str, status: str, message_preview: Optional[str] = None, error_message: Optional[str] = None)`** (新增方法，伪代码/文字描述):
    1.  创建 `NotificationLogRecord` 实例，填充数据。
    2.  保存 `NotificationLogRecord` 到数据库。
    3.  记录成功保存通知日志的日志。

*   **`async _should_send_notification(self, imported_from_following: bool, discrepancy_seconds: float, wallet_address: str, activity: Any)`** (参数中增加 wallet_address 和 activity 以便记录事件，伪代码/文字描述):
    1.  获取当前时间 `now = datetime.now(timezone.utc)`。
    2.  获取该类别的告警状态 `state = self._alert_state[imported_from_following]`。
    3.  如果差异超过阈值：
        ```python
        if discrepancy_seconds > self._config["alert_threshold_seconds"]:
            state["consecutive_alerts"] += 1
            logger.info(f"Category {'imported' if imported_from_following else 'non-imported'}: Consecutive alerts: {state['consecutive_alerts']}/{self._config['consecutive_alerts_required']}")
            await self._save_alert_state(imported_from_following)
            
            if state["consecutive_alerts"] >= self._config["consecutive_alerts_required"]:
                if state["last_notification_time"] is None or (now - state["last_notification_time"]).total_seconds() > self._config["alert_suppression_minutes"] * 60:
                    state["last_notification_time"] = now
                    state["consecutive_alerts"] = 0
                    await self._save_alert_state(imported_from_following)
                    
                    # >> 新增：记录告警事件 <<
                    alert_message_core = f"KOL活动时间戳差异告警 ({'导入' if imported_from_following else '非导入'}): 地址 {wallet_address}, 哈希 {activity.tx_hash}, 差异 {discrepancy_seconds:.2f}s"
                    alert_details = {
                        "created_at_utc": activity.created_at.isoformat(),
                        "timestamp_raw": activity.timestamp,
                        # ... (其他相关字段)
                    }
                    alert_event_id = await self._record_alert_event(
                        imported_from_following=imported_from_following,
                        wallet_address=wallet_address,
                        activity_tx_hash=activity.tx_hash,
                        discrepancy_seconds=discrepancy_seconds,
                        message=alert_message_core,
                        details=alert_details
                    )
                    return True, alert_event_id # 返回True及告警事件ID
                else:
                    # ... (抑制日志)
                    return False, None
        else:
            # ... (重置计数逻辑，包含保存状态)
            return False, None
        ```
    4.  返回 `(False, None)` 如果不发送通知。

*   **`async check_and_notify_for_category(self, imported_from_following: bool)`** (伪代码/文字描述):
    1.  如果 `self.admin_chat_ids` 为空，记录警告并返回。
    2.  调用 `_get_latest_kol_wallet` 获取KOL钱包。
    3.  如果未找到KOL钱包，记录日志并返回。
    4.  调用 `_get_latest_activity` 获取该KOL的最新活动记录。
    5.  如果未找到活动记录，记录日志并返回。
    6.  从活动记录中获取 `created_at` (UTC `datetime` 对象)。确保它是时区感知的UTC时间 (如果原始数据可能是naive，则附加UTC时区；如果是其他时区，则转换为UTC)。
    7.  从活动记录中获取 `timestamp` (整数，代表东八区时间的Unix时间戳)。
    8.  将整数 `timestamp` 转换为UTC `datetime` 对象：`timestamp_as_utc_dt = datetime.fromtimestamp(activity.timestamp, timezone.utc)`。
    9.  计算 `created_at_utc` 和 `timestamp_as_utc_dt` 之间的绝对差异秒数: `discrepancy_seconds = abs((created_at_utc - timestamp_as_utc_dt).total_seconds())`。
    10. 记录检查日志，包含钱包地址、分类、交易哈希、`created_at` (UTC ISO格式)、`timestamp` 原始值、`timestamp` (转换后的UTC ISO格式) 和计算出的差异秒数。
    11. 调用 `should_send, alert_event_id = await self._should_send_notification(imported_from_following, discrepancy_seconds, kol_wallet.wallet_address, latest_activity)`。
    12. 如果 `should_send` 为 `True`:
        *   准备通知消息 (与之前类似，但可能基于 `alert_event_id` 生成或关联)。
        *   记录高差异警告日志。
        *   遍历 `self.admin_chat_ids`:
            *   `try`:
                *   调用 `self.message_sender.send_message_to_user` 发送通知。
                *   记录通知发送成功的日志。
                *   `await self._record_notification_log(alert_event_id, chat_id, "success", message_preview=notification_message[:200])`
            *   `except Exception as e`:
                *   记录通知发送失败的日志，包含错误信息。
                *   `await self._record_notification_log(alert_event_id, chat_id, "failure", message_preview=notification_message[:200], error_message=str(e))`

*   **`async run_check_cycle(self)`** (伪代码/文字描述):
    1.  调用 `_load_config` 加载最新配置。
    2.  调用 `_load_alert_state` 加载持久化的告警状态。
    3.  如果 `admin_chat_ids` 为空，记录警告。
    4.  记录开始检查 `imported_from_following=True` 类别的日志。
    5.  调用 `check_and_notify_for_category(imported_from_following=True)`。
    6.  记录开始检查 `imported_from_following=False/None` 类别的日志。
    7.  调用 `check_and_notify_for_category(imported_from_following=False)`。

**工作流入口函数 `perform_kol_activity_timestamp_discrepancy_check_task` (或类似名称)** (伪代码/文字描述):

1.  记录工作流任务被触发的日志。
2.  实例化 `KolActivityTimestampDiscrepancyChecker`。
3.  使用 `try-except` 块：
    *   **`try`**: 
        *   调用 `checker.run_check_cycle()`。
        *   记录任务成功完成的日志。
        *   返回包含 `{"status": "success", "timestamp": current_utc_time_iso}` 的字典。
    *   **`except Exception as e`**:
        *   记录任务执行错误的日志，包含异常信息和堆栈跟踪。
        *   返回包含 `{"status": "error", "error_message": str(e), "timestamp": current_utc_time_iso}` 的字典。

## 5. 工作流定义 (`kol_activity_latency_monitor_workflow.yaml` 或新名称)

```yaml
name: "KOL Activity Timestamp Discrepancy Monitor"
description: "Monitors KOL wallet activity for significant discrepancies between 'updated_at' (UTC) and 'timestamp' (East Eight Unix ts) fields within the same record. Alerts after 3 consecutive checks with discrepancy > 60s."

nodes:
  - name: "CheckKOLTimestampDiscrepancyNode"
    node_type: "input" 
    interval: 10 # 每10秒执行一次
    # generate_data 应指向上述 handler.py 中定义的入口函数，例如：
    generate_data: workflows.kol_activity_latency_monitor.handler.perform_kol_activity_timestamp_discrepancy_check_task 
    flow_control:
      max_pending_messages: 1 
      check_interval: 0.5   
      enable_flow_control: true
```

## 6. 初始化与数据库注册

1. 在 `models/__init__.py` 中注册 `MonitorAlertState`, `AlertEventRecord`, `NotificationLogRecord` 模型:
```python
# 现有导入...
from models.monitor_alert_state import MonitorAlertState
from models.alert_event_record import AlertEventRecord
from models.notification_log_record import NotificationLogRecord

# 在初始化函数中添加到要注册的文档列表中
async def init_db():
    # 现有代码...
    await init_beanie(
        database=db,
        document_models=[
            # 现有模型...
            MonitorAlertState,
            AlertEventRecord,
            NotificationLogRecord,
        ]
    )
```

## 7. 配置示例

在 `Config` 集合中的 `application_config` 文档中，添加以下配置示例：

```json
{
  "type": "application_config",
  "data": {
    "admin_telegram_chat_ids": ["id1", "id2"],
    "kol_activity_monitor": {
      "alert_threshold_seconds": 60,
      "consecutive_alerts_required": 3,
      "alert_suppression_minutes": 30
    }
  }
}
```

## 8. 运行工作流

通过 `run_workflow.py` 脚本启动，例如：
`python run_workflow.py workflows/kol_activity_latency_monitor/kol_activity_latency_monitor_workflow.yaml`
(如果重命名了目录/文件，路径需相应调整)

## 9. 数据库查询优化

*   确保 `KOLWallet.imported_from_following`, `KOLWallet.last_active`, `KOLWalletActivity.wallet`, `KOLWalletActivity.created_at` 字段已建立索引。
*   为 `MonitorAlertState` 集合建立复合索引 `[("monitor_type", 1), ("category", 1)]`，以加速查询特定监控类型和类别的状态。
*   为 `AlertEventRecord` 和 `NotificationLogRecord` 添加相关字段的索引，如方案中所示。

## 10. 日志

在 `handler.py` 中的关键逻辑点（如选择KOL、获取活动、计算差异、连续阈值检测、告警抑制、发送通知、错误处理等）添加详细日志，遵循项目日志规范。

增加记录告警事件保存和通知日志保存的日志。

## 11. 错误处理与健壮性

*   入口函数 `perform_kol_activity_timestamp_discrepancy_check_task` 使用 `try-except` 捕获并记录执行期间的总体错误。
*   `TelegramMessageSender` 内部应有其API调用失败的处理逻辑和日志记录。
*   数据库操作（如在 `_load_config`, `_load_alert_state`, `_save_alert_state`, `_get_latest_kol_wallet`, `_get_latest_activity`中）如果失败，应被优雅处理（例如，记录错误并允许当前检查周期对该分支失败，但不中断整个工作流的后续执行）。
*   确保在 `_save_alert_state`, `_record_alert_event`, `_record_notification_log` 方法中使用 `try-except` 处理可能的数据库操作异常，以避免状态保存失败影响主要功能。

## 12. 环境变量
*   `TELEGRAM_BOT_TOKEN`: 由 `TelegramMessageSender` 使用。