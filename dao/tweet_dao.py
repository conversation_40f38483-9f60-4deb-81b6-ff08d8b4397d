from typing import List, Optional, Dict, Any
from datetime import datetime
from models.tweet import Tweet
from .base_dao import BaseDAO
from pymongo import UpdateOne, InsertOne
import traceback


class TweetDAO(BaseDAO[Tweet]):
    """推文数据访问对象"""
    
    def __init__(self):
        super().__init__(Tweet)
    
    async def find_by_tweet_id(self, tweet_id: str) -> Optional[Tweet]:
        """根据推文ID查找推文"""
        return await self.find_one({"tweet_id": tweet_id})
    
    async def find_by_author_id(
        self,
        author_id: str,
        skip: int = 0,
        limit: int = 100,
        sort_by_created: bool = True
    ) -> List[Tweet]:
        """查找作者的推文"""
        sort = [("created_at", -1)] if sort_by_created else None
        return await self.find_many(
            {"author_id": author_id},
            skip=skip,
            limit=limit,
            sort=sort
        )
    
    async def find_recent_tweets(
        self,
        hours: int = 24,
        limit: int = 100
    ) -> List[Tweet]:
        """查找最近的推文"""
        since = datetime.utcnow() - datetime.timedelta(hours=hours)
        return await self.find_many(
            {"created_at": {"$gte": since}},
            limit=limit,
            sort=[("created_at", -1)]
        )
    
    async def update_tweet_stats(
        self,
        tweet_id: str,
        stats: Dict[str, int]
    ) -> bool:
        """更新推文统计数据"""
        valid_stats = {
            k: v for k, v in stats.items()
            if k in ["like_count", "retweet_count", "reply_count", "quote_count"]
        }
        if not valid_stats:
            return False
        
        return await self.update_one(
            {"tweet_id": tweet_id},
            valid_stats
        )
    
    async def save_tweet(self, tweet_data: Dict[str, Any]) -> Optional[Tweet]:
        """保存或更新推文"""
        tweet_id = tweet_data.get("tweet_id")
        if not tweet_id:
            return None
            
        existing_tweet = await self.find_by_tweet_id(tweet_id)
        if existing_tweet:
            # 更新现有推文
            update_data = {
                "like_count": tweet_data.get("like_count", 0),
                "retweet_count": tweet_data.get("retweet_count", 0),
                "reply_count": tweet_data.get("reply_count", 0),
                "quote_count": tweet_data.get("quote_count", 0),
                "last_updated": datetime.utcnow()
            }
            await self.update_one({"tweet_id": tweet_id}, update_data)
            return existing_tweet
        
        # 创建新推文
        tweet = Tweet(**tweet_data)
        return await self.insert_one(tweet)
    
    async def save_many_tweets(self, tweets_data: List[Dict[str, Any]]) -> List[Tweet]:
        """批量保存推文"""
        if not tweets_data:
            return []
            
        try:
            # 准备批量操作列表
            operations = []
            tweet_ids = []
            current_time = datetime.utcnow()
            
            # 处理每条推文数据
            for tweet_data in tweets_data:
                tweet_id = tweet_data.get("tweet_id")
                if not tweet_id:
                    continue
                
                tweet_ids.append(tweet_id)
                
                # 处理日期字段
                created_at = tweet_data.get("created_at")
                if isinstance(created_at, str):
                    try:
                        created_at = datetime.strptime(created_at, "%Y-%m-%dT%H:%M:%S")
                    except ValueError:
                        self.logger.warning(f"无法解析created_at日期: {created_at}, tweet_id: {tweet_id}")
                        created_at = current_time
                
                # 准备完整的推文数据
                new_tweet_data = {
                    "tweet_id": tweet_id,
                    "text": tweet_data.get("text", ""),
                    "created_at": created_at,
                    "language": tweet_data.get("language"),
                    "like_count": tweet_data.get("like_count", 0),
                    "retweet_count": tweet_data.get("retweet_count", 0),
                    "reply_count": tweet_data.get("reply_count", 0),
                    "quote_count": tweet_data.get("quote_count", 0),
                    "is_retweet": tweet_data.get("is_retweet", False),
                    "is_reply": tweet_data.get("is_reply", False),
                    "is_quote": tweet_data.get("is_quote", False),
                    "replied_to_tweet_id": tweet_data.get("replied_to_tweet_id"),
                    "quoted_tweet_id": tweet_data.get("quoted_tweet_id"),
                    "retweeted_tweet_id": tweet_data.get("retweeted_tweet_id"),
                    "media": tweet_data.get("media", []),
                    "urls": tweet_data.get("urls", []),
                    "hashtags": tweet_data.get("hashtags", []),
                    "mentions": tweet_data.get("mentions", []),
                    "is_top": tweet_data.get("is_top", False),
                    "author_id": tweet_data.get("author_id"),
                    "sender_id": tweet_data.get("sender_id")
                }
                
                # 处理 fetched_at 字段
                fetched_at = tweet_data.get("fetched_at")
                if isinstance(fetched_at, str):
                    try:
                        fetched_at = datetime.strptime(fetched_at, "%Y-%m-%dT%H:%M:%S.%f")
                    except ValueError:
                        try:
                            fetched_at = datetime.strptime(fetched_at, "%Y-%m-%dT%H:%M:%S")
                        except ValueError:
                            self.logger.warning(f"无法解析fetched_at日期: {fetched_at}, tweet_id: {tweet_id}")
                            fetched_at = current_time
                else:
                    fetched_at = current_time
                
                update_data = {
                    "$set": {
                        **new_tweet_data,
                        "fetched_at": fetched_at,
                        "last_updated": current_time
                    }
                }
                
                operations.append(
                    UpdateOne(
                        {"tweet_id": tweet_id},
                        update_data,
                        upsert=True
                    )
                )
            
            # 执行批量操作
            if operations:
                result = await self.collection.bulk_write(operations, ordered=False)
                self.logger.info(f"批量操作结果 - 插入: {result.upserted_count}, 更新: {result.modified_count}, 匹配: {result.matched_count}")
                
            # 返回更新后的推文列表
            saved_tweets = await self.find_many({"tweet_id": {"$in": tweet_ids}})
            self.logger.info(f"成功保存/更新了 {len(saved_tweets)} 条推文")
            return saved_tweets
            
        except Exception as e:
            self.logger.error(f"批量保存推文时发生错误: {str(e)}\n{traceback.format_exc()}")
            return [] 