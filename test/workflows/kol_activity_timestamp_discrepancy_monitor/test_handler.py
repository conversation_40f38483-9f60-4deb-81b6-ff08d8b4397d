import asyncio
import unittest
from unittest.mock import AsyncMock, patch, MagicMock, call
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Any, Optional
import logging

# Remove Beanie and Motor imports as we will be mocking DAOs completely
# from beanie import init_beanie
# from motor.motor_asyncio import AsyncIOMotorClient

from models.config import Config, ApplicationConfig, KolActivityMonitorConfig # Keep model imports for type hinting and data construction
from models.kol_wallet import KOLWallet
from models.kol_wallet_activity import KOLWalletActivity
from models.monitor_alert_state import MonitorAlertState
from models.alert_event_record import AlertEventRecord
from models.notification_log_record import NotificationLogRecord
# from models.kol_activity_filter import KolActivityFilter # Not used in current tests

from workflows.kol_activity_timestamp_discrepancy_monitor.handler import (
    KolActivityTimestampDiscrepancyChecker,
    perform_kol_activity_timestamp_discrepancy_check_task
)

# DAO imports are still needed for patching targets
from dao.kol_wallet_dao import KOLWalletDAO
from dao.kol_wallet_activity_dao import KOLWalletActivityDAO
from dao.config_dao import ConfigDAO
from dao.monitor_alert_state_dao import MonitorAlertStateDAO
from dao.alert_event_record_dao import AlertEventRecordDAO
from dao.notification_log_record_dao import NotificationLogRecordDAO

# Configure logging for tests to see output
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Helper for datetime comparison with tolerance - remains useful
def assert_datetime_close(dt1: Optional[datetime], dt2: Optional[datetime], tolerance_seconds: int = 2) -> None:
    if dt1 is None and dt2 is None:
        return
    assert dt1 is not None, "First datetime is None, second is not"
    assert dt2 is not None, "Second datetime is None, first is not"
    # Ensure both are offset-aware and in UTC before comparison
    dt1_utc = dt1.astimezone(timezone.utc) if dt1.tzinfo else dt1.replace(tzinfo=timezone.utc)
    dt2_utc = dt2.astimezone(timezone.utc) if dt2.tzinfo else dt2.replace(tzinfo=timezone.utc)
    assert abs((dt1_utc - dt2_utc).total_seconds()) <= tolerance_seconds, \
           f"{dt1} and {dt2} are not within {tolerance_seconds}s tolerance"

class TestKolActivityTimestampDiscrepancyChecker(unittest.TestCase):

    def _create_mock_document(self, data_dict: Dict, spec_class: Any) -> MagicMock:
        mock_doc = MagicMock(spec=spec_class)
        for key, value in data_dict.items():
            setattr(mock_doc, key, value)
        
        # Ensure 'id' is present if the spec_class has it, to mimic Beanie behavior
        if not hasattr(mock_doc, 'id') and hasattr(spec_class, 'id'):
            # Simulate ObjectId-like behavior for 'id' if it's typically a complex type
            if 'id' in data_dict: # If id is explicitly provided
                 setattr(mock_doc, 'id', data_dict['id'])
            else: # Otherwise, mock it if spec has id
                # Simplification: use a string or a new MagicMock for id
                setattr(mock_doc, 'id', MagicMock(spec=str)) 

        return mock_doc

    def setUp(self):
        logger.info("Setting up TestKolActivityTimestampDiscrepancyChecker test case...")
        
        # Mock configurations
        self.mock_config_data_dict = {
            "admin_telegram_chat_ids": ["12345", "67890"],
            "kol_activity_monitor": { # This should be a dict for ApplicationConfig
                "alert_threshold_seconds": 60,
                "consecutive_alerts_required": 3,
                "alert_suppression_minutes": 30
            }
        }
        # Construct ApplicationConfig correctly
        kol_monitor_conf = KolActivityMonitorConfig(**self.mock_config_data_dict["kol_activity_monitor"])
        self.mock_app_config_obj = ApplicationConfig(
            admin_telegram_chat_ids=self.mock_config_data_dict["admin_telegram_chat_ids"],
            kol_activity_monitor=kol_monitor_conf
        )

        # Mock Alert State Data
        self.mock_alert_state_data_imported = {
            "monitor_type": "kol_activity_timestamp_discrepancy", "category": "imported",
            "consecutive_alerts": 0, "last_notification_time": None, "id": "state_imported_id"
        }
        self.mock_alert_state_data_non_imported = {
            "monitor_type": "kol_activity_timestamp_discrepancy", "category": "non_imported",
            "consecutive_alerts": 0, "last_notification_time": None, "id": "state_non_imported_id"
        }

        # Mock KOL Wallet Data
        self.mock_kol_wallet_data_imported = {
            "wallet_address": "wallet_imported_123", "name": "Imported KOL",
            "imported_from_following": True,
            "last_active": datetime(2023, 1, 1, 12, 0, 0, tzinfo=timezone.utc),
            "id": "kol_imported_id"
        }
        self.mock_kol_wallet_data_non_imported = {
            "wallet_address": "wallet_non_imported_456", "name": "Non-Imported KOL",
            "imported_from_following": False,
            "last_active": datetime(2023, 1, 1, 10, 0, 0, tzinfo=timezone.utc),
            "id": "kol_non_imported_id"
        }
        
        # Mock Wallet Activity Data - now focusing on created_at
        self.east_eight_tz = timezone(timedelta(hours=8))
        self.created_at_utc_for_activity = datetime(2023, 1, 1, 12, 1, 30, tzinfo=timezone.utc) # UTC
        self.timestamp_for_activity_e8 = datetime(2023, 1, 1, 20, 0, 0, tzinfo=self.east_eight_tz) # UTC+8, corresponds to 12:00:00 UTC
        
        self.mock_wallet_activity_data = {
            "id": "activity_id_1",
            "tx_hash": "0x123abc_created_at_test",
            "wallet": self.mock_kol_wallet_data_imported["wallet_address"],
            "timestamp": int(self.timestamp_for_activity_e8.timestamp()), # E8 Unix timestamp
            "created_at": self.created_at_utc_for_activity, # UTC datetime
            "updated_at": datetime(2023, 1, 1, 12, 5, 0, tzinfo=timezone.utc) # Keep updated_at for other potential uses
        }

        # Mock Alert Event Data
        self.mock_alert_event_data = {
            "id": "mock_event_id_str", "monitor_type": "kol_activity_timestamp_discrepancy",
            "category": "imported", "wallet_address": self.mock_kol_wallet_data_imported["wallet_address"],
            "activity_tx_hash": self.mock_wallet_activity_data["tx_hash"],
            "discrepancy_seconds": 90.0, "message": "Test alert for created_at",
            "details": {"created_at_utc": self.created_at_utc_for_activity.isoformat(), "timestamp_raw": self.mock_wallet_activity_data["timestamp"]},
            "trigger_time": datetime.now(timezone.utc)
        }

        # Patch DAOs
        self.mock_config_dao = MagicMock(spec=ConfigDAO)
        self.mock_monitor_alert_state_dao = MagicMock(spec=MonitorAlertStateDAO)
        self.mock_kol_wallet_dao = MagicMock(spec=KOLWalletDAO)
        self.mock_kol_wallet_activity_dao = MagicMock(spec=KOLWalletActivityDAO)
        self.mock_alert_event_record_dao = MagicMock(spec=AlertEventRecordDAO)
        self.mock_notification_log_record_dao = MagicMock(spec=NotificationLogRecordDAO)

        self.patchers = [
            patch("workflows.kol_activity_timestamp_discrepancy_monitor.handler.config_dao", self.mock_config_dao),
            patch("workflows.kol_activity_timestamp_discrepancy_monitor.handler.monitor_alert_state_dao", self.mock_monitor_alert_state_dao),
            patch("workflows.kol_activity_timestamp_discrepancy_monitor.handler.kol_wallet_dao", self.mock_kol_wallet_dao),
            patch("workflows.kol_activity_timestamp_discrepancy_monitor.handler.kol_wallet_activity_dao", self.mock_kol_wallet_activity_dao),
            patch("workflows.kol_activity_timestamp_discrepancy_monitor.handler.alert_event_record_dao", self.mock_alert_event_record_dao),
            patch("workflows.kol_activity_timestamp_discrepancy_monitor.handler.notification_log_record_dao", self.mock_notification_log_record_dao)
        ]
        for p in self.patchers:
            p.start()

        # Setup checker with mocked Telegram sender
        with patch("workflows.kol_activity_timestamp_discrepancy_monitor.handler.TelegramMessageSender") as MockTelegramSender:
            self.mock_telegram_sender_instance = MockTelegramSender.return_value
            self.mock_telegram_sender_instance.send_message_to_user = AsyncMock(return_value=True)
            self.checker = KolActivityTimestampDiscrepancyChecker()
        
        # Setup default return values for DAO mocks
        self.mock_config_dao.get_application_config = AsyncMock(return_value=self.mock_app_config_obj)
        self.mock_monitor_alert_state_dao.get_alert_state = AsyncMock(side_effect=self._get_alert_state_side_effect)
        self.mock_monitor_alert_state_dao.upsert_alert_state = AsyncMock(return_value=True)
        
        mock_created_alert_event_doc = self._create_mock_document(self.mock_alert_event_data, AlertEventRecord)
        setattr(mock_created_alert_event_doc, 'id', self.mock_alert_event_data['id']) # Ensure id is correctly set
        self.mock_alert_event_record_dao.create_alert_event = AsyncMock(return_value=mock_created_alert_event_doc)
        self.mock_notification_log_record_dao.create_notification_log = AsyncMock(return_value=True)

        # Specific mocks for KOLWallet and KOLWalletActivity
        self.mock_kol_wallet_dao._get_kol_wallet_with_latest_activity = AsyncMock()
        self.mock_kol_wallet_activity_dao.find_many = AsyncMock()
        
        logger.info("Finished setting up test case.")

    def _get_alert_state_side_effect(self, monitor_type: str, category: str) -> Optional[MagicMock]:
        if monitor_type == "kol_activity_timestamp_discrepancy":
            if category == "imported":
                return self._create_mock_document(self.mock_alert_state_data_imported.copy(), MonitorAlertState)
            elif category == "non_imported":
                return self._create_mock_document(self.mock_alert_state_data_non_imported.copy(), MonitorAlertState)
        return None

    def tearDown(self):
        for p in self.patchers:
            p.stop()
        logger.info("Finished tearing down test case.")

    async def test_load_config_success(self):
        """Test _load_config successfully loads configuration."""
        await self.checker._load_config()
        self.assertEqual(self.checker.admin_chat_ids, self.mock_config_data_dict["admin_telegram_chat_ids"])
        self.assertEqual(self.checker._config["alert_threshold_seconds"], self.mock_config_data_dict["kol_activity_monitor"]["alert_threshold_seconds"])
        self.mock_config_dao.get_application_config.assert_called_once()

    async def test_load_config_no_app_config(self):
        """Test _load_config when no application_config document is found."""
        self.mock_config_dao.get_application_config.return_value = None
        await self.checker._load_config()
        self.assertEqual(self.checker.admin_chat_ids, [])
        self.assertEqual(self.checker._config["alert_threshold_seconds"], 60) # Default value
        self.mock_config_dao.get_application_config.assert_called_once()

    async def test_load_alert_state_success(self):
        """Test _load_alert_state successfully loads states."""
        await self.checker._load_alert_state()
        self.assertEqual(self.checker._alert_state[True]["consecutive_alerts"], self.mock_alert_state_data_imported["consecutive_alerts"])
        self.assertEqual(self.checker._alert_state[False]["consecutive_alerts"], self.mock_alert_state_data_non_imported["consecutive_alerts"])
        self.mock_monitor_alert_state_dao.get_alert_state.assert_any_call(monitor_type="kol_activity_timestamp_discrepancy", category="imported")
        self.mock_monitor_alert_state_dao.get_alert_state.assert_any_call(monitor_type="kol_activity_timestamp_discrepancy", category="non_imported")

    async def test_save_alert_state_success(self):
        """Test _save_alert_state successfully saves state."""
        self.checker._alert_state[True] = {"consecutive_alerts": 2, "last_notification_time": datetime.now(timezone.utc)}
        await self.checker._save_alert_state(imported_from_following=True)
        self.mock_monitor_alert_state_dao.upsert_alert_state.assert_called_once_with(
            monitor_type="kol_activity_timestamp_discrepancy",
            category="imported",
            consecutive_alerts=2,
            last_notification_time=self.checker._alert_state[True]["last_notification_time"]
        )

    async def test_get_latest_kol_wallet_found(self):
        """Test _get_latest_kol_wallet when a wallet is found."""
        mock_wallet = self._create_mock_document(self.mock_kol_wallet_data_imported, KOLWallet)
        self.mock_kol_wallet_dao._get_kol_wallet_with_latest_activity.return_value = mock_wallet
        
        result = await self.checker._get_latest_kol_wallet(imported_from_following=True)
        
        self.assertEqual(result.wallet_address, mock_wallet.wallet_address)
        self.mock_kol_wallet_dao._get_kol_wallet_with_latest_activity.assert_called_once_with(
            imported_from_following=True
        )

    async def test_get_latest_kol_wallet_not_found(self):
        """Test _get_latest_kol_wallet when no wallet is found."""
        self.mock_kol_wallet_dao._get_kol_wallet_with_latest_activity.return_value = None
        result = await self.checker._get_latest_kol_wallet(imported_from_following=False)
        self.assertIsNone(result)
        self.mock_kol_wallet_dao._get_kol_wallet_with_latest_activity.assert_called_once_with(
            imported_from_following=False
        )
        
    async def test_get_latest_activity_found(self):
        """Test _get_latest_activity when an activity is found, sorted by created_at."""
        mock_activity = self._create_mock_document(self.mock_wallet_activity_data, KOLWalletActivity)
        # find_many returns a list
        self.mock_kol_wallet_activity_dao.find_many.return_value = [mock_activity]
        
        result = await self.checker._get_latest_activity(wallet_address="some_wallet_address")
        
        self.assertEqual(result.tx_hash, mock_activity.tx_hash)
        self.mock_kol_wallet_activity_dao.find_many.assert_called_once_with(
            filter_dict={"wallet": "some_wallet_address"},
            sort=[("created_at", -1)], # Verify sorting by created_at
            limit=1
        )

    async def test_get_latest_activity_not_found(self):
        """Test _get_latest_activity when no activity is found."""
        self.mock_kol_wallet_activity_dao.find_many.return_value = [] # find_many returns an empty list
        result = await self.checker._get_latest_activity(wallet_address="some_wallet_address")
        self.assertIsNone(result)
        self.mock_kol_wallet_activity_dao.find_many.assert_called_once_with(
            filter_dict={"wallet": "some_wallet_address"},
            sort=[("created_at", -1)],
            limit=1
        )

    async def test_record_alert_event_success(self):
        """Test _record_alert_event successfully records an event."""
        event_id = await self.checker._record_alert_event(
            imported_from_following=True,
            wallet_address="wallet_test",
            activity_tx_hash="tx_test",
            discrepancy_seconds=100.0,
            message="Test alert message",
            details={"key": "value"}
        )
        self.assertEqual(event_id, self.mock_alert_event_data["id"])
        self.mock_alert_event_record_dao.create_alert_event.assert_called_once()
        args, _ = self.mock_alert_event_record_dao.create_alert_event.call_args
        self.assertEqual(args[0]["wallet_address"], "wallet_test")

    async def test_record_notification_log_success(self):
        """Test _record_notification_log successfully records a log."""
        await self.checker._record_notification_log(
            alert_event_id="event_id_test",
            recipient_chat_id="chat_id_test",
            status="success",
            message_preview="Test preview"
        )
        self.mock_notification_log_record_dao.create_notification_log.assert_called_once()
        args, _ = self.mock_notification_log_record_dao.create_notification_log.call_args
        self.assertEqual(args[0]["recipient_chat_id"], "chat_id_test")

    async def test_should_send_notification_over_threshold_triggers_alert(self):
        """Test _should_send_notification when discrepancy triggers an alert (3rd consecutive)."""
        self.checker._alert_state[True]["consecutive_alerts"] = 2 # Setup for 3rd alert
        self.checker._config["consecutive_alerts_required"] = 3
        self.checker._config["alert_threshold_seconds"] = 50
        
        mock_activity = self._create_mock_document(self.mock_wallet_activity_data, KOLWalletActivity)

        should_send, event_id = await self.checker._should_send_notification(
            imported_from_following=True,
            discrepancy_seconds=60.0, # Over threshold
            wallet_address=mock_activity.wallet,
            activity=mock_activity
        )

        self.assertTrue(should_send)
        self.assertIsNotNone(event_id)
        self.assertEqual(event_id, self.mock_alert_event_data["id"]) # Check returned event_id
        self.assertEqual(self.checker._alert_state[True]["consecutive_alerts"], 0) # Reset after alert
        self.assertIsNotNone(self.checker._alert_state[True]["last_notification_time"])
        self.mock_alert_event_record_dao.create_alert_event.assert_called_once()
        # upsert_alert_state is called twice: once for increment, once for reset.
        self.assertEqual(self.mock_monitor_alert_state_dao.upsert_alert_state.call_count, 2)


    async def test_should_send_notification_increment_consecutive(self):
        """Test _should_send_notification increments count but doesn't trigger yet (1st alert)."""
        self.checker._alert_state[True]["consecutive_alerts"] = 0
        self.checker._config["consecutive_alerts_required"] = 3
        self.checker._config["alert_threshold_seconds"] = 50

        mock_activity = self._create_mock_document(self.mock_wallet_activity_data, KOLWalletActivity)

        should_send, event_id = await self.checker._should_send_notification(
            imported_from_following=True,
            discrepancy_seconds=60.0, # Over threshold
            wallet_address=mock_activity.wallet,
            activity=mock_activity
        )

        self.assertFalse(should_send)
        self.assertIsNone(event_id)
        self.assertEqual(self.checker._alert_state[True]["consecutive_alerts"], 1)
        self.mock_monitor_alert_state_dao.upsert_alert_state.assert_called_once() # Only for increment

    async def test_should_send_notification_reset_consecutive_on_below_threshold(self):
        """Test _should_send_notification resets count when discrepancy is below threshold."""
        self.checker._alert_state[True]["consecutive_alerts"] = 2 # Had previous alerts
        self.checker._config["alert_threshold_seconds"] = 50

        mock_activity = self._create_mock_document(self.mock_wallet_activity_data, KOLWalletActivity)

        should_send, event_id = await self.checker._should_send_notification(
            imported_from_following=True,
            discrepancy_seconds=40.0, # Below threshold
            wallet_address=mock_activity.wallet,
            activity=mock_activity
        )
        self.assertFalse(should_send)
        self.assertIsNone(event_id)
        self.assertEqual(self.checker._alert_state[True]["consecutive_alerts"], 0) # Reset
        self.mock_monitor_alert_state_dao.upsert_alert_state.assert_called_once() # For reset

    async def test_should_send_notification_suppressed(self):
        """Test _should_send_notification when alert is suppressed due to recent notification."""
        self.checker._alert_state[True]["consecutive_alerts"] = 3 # Ready to trigger
        self.checker._config["consecutive_alerts_required"] = 3
        self.checker._config["alert_threshold_seconds"] = 50
        self.checker._config["alert_suppression_minutes"] = 30
        # Simulate last notification was 10 minutes ago
        self.checker._alert_state[True]["last_notification_time"] = datetime.now(timezone.utc) - timedelta(minutes=10)

        mock_activity = self._create_mock_document(self.mock_wallet_activity_data, KOLWalletActivity)
        
        should_send, event_id = await self.checker._should_send_notification(
            imported_from_following=True,
            discrepancy_seconds=60.0, # Over threshold
            wallet_address=mock_activity.wallet,
            activity=mock_activity
        )

        self.assertFalse(should_send) # Suppressed
        self.assertIsNone(event_id)
        # consecutive_alerts remains high because the condition was met, but alert was suppressed
        self.assertEqual(self.checker._alert_state[True]["consecutive_alerts"], 4) # It still increments
        # last_notification_time should NOT be updated during suppression
        self.assertTrue( (datetime.now(timezone.utc) - self.checker._alert_state[True]["last_notification_time"]).total_seconds() > 9 * 60 ) 
        self.mock_monitor_alert_state_dao.upsert_alert_state.assert_called_once() # Only for incrementing

    async def test_check_and_notify_for_category_sends_notification(self):
        """Test check_and_notify_for_category successfully sends a notification."""
        # Prepare mocks for _get_latest_kol_wallet and _get_latest_activity
        mock_kol_wallet = self._create_mock_document(self.mock_kol_wallet_data_imported, KOLWallet)
        
        # Activity data with significant discrepancy (created_at vs timestamp)
        created_at_utc = datetime(2023, 1, 1, 10, 0, 0, tzinfo=timezone.utc) # 10:00 UTC
        # Timestamp for 10:00 E8 (UTC+8) is 02:00 UTC. So, 2023-01-01 02:00:00 UTC
        timestamp_e8_val = int(datetime(2023, 1, 1, 2, 0, 0, tzinfo=timezone.utc).timestamp())

        mock_activity_data_discrepancy = {
            "id": "activity_discrepancy", "tx_hash": "0xDiscrepancy", "wallet": mock_kol_wallet.wallet_address,
            "created_at": created_at_utc, # 10:00 UTC
            "timestamp": timestamp_e8_val, # Corresponds to 02:00 UTC
            "updated_at": created_at_utc # Keep it same as created_at for simplicity here
        }
        mock_activity_with_discrepancy = self._create_mock_document(mock_activity_data_discrepancy, KOLWalletActivity)

        self.mock_kol_wallet_dao._get_kol_wallet_with_latest_activity.return_value = mock_kol_wallet
        # find_many returns a list
        self.mock_kol_wallet_activity_dao.find_many.return_value = [mock_activity_with_discrepancy]

        # Setup checker state to trigger alert on this run
        self.checker._alert_state[True]["consecutive_alerts"] = self.checker._config["consecutive_alerts_required"] - 1
        self.checker._alert_state[True]["last_notification_time"] = None # Ensure no suppression

        await self.checker.check_and_notify_for_category(imported_from_following=True)

        self.mock_telegram_sender_instance.send_message_to_user.assert_called()
        # Check if it was called for each admin_chat_id
        expected_calls = [call(chat_id, unittest.mock.ANY) for chat_id in self.checker.admin_chat_ids]
        self.mock_telegram_sender_instance.send_message_to_user.assert_has_calls(expected_calls, any_order=True)
        
        # Check that alert event and notification logs were recorded
        self.mock_alert_event_record_dao.create_alert_event.assert_called_once()
        self.assertEqual(self.mock_notification_log_record_dao.create_notification_log.call_count, len(self.checker.admin_chat_ids))


    async def test_check_and_notify_no_kol_wallet(self):
        """Test check_and_notify_for_category when no KOL wallet is found."""
        self.mock_kol_wallet_dao._get_kol_wallet_with_latest_activity.return_value = None # No wallet found

        with patch.object(self.checker, '_should_send_notification', new_callable=AsyncMock) as mock_should_send:
            await self.checker.check_and_notify_for_category(imported_from_following=True)
            mock_should_send.assert_not_called() # Should not proceed to check discrepancy
        self.mock_telegram_sender_instance.send_message_to_user.assert_not_called()


    async def test_check_and_notify_no_activity(self):
        """Test check_and_notify_for_category when no activity is found for the KOL wallet."""
        mock_kol_wallet = self._create_mock_document(self.mock_kol_wallet_data_imported, KOLWallet)
        self.mock_kol_wallet_dao._get_kol_wallet_with_latest_activity.return_value = mock_kol_wallet
        self.mock_kol_wallet_activity_dao.find_many.return_value = [] # No activity, find_many returns empty list

        with patch.object(self.checker, '_should_send_notification', new_callable=AsyncMock) as mock_should_send:
            await self.checker.check_and_notify_for_category(imported_from_following=True)
            mock_should_send.assert_not_called()
        self.mock_telegram_sender_instance.send_message_to_user.assert_not_called()

    async def test_check_and_notify_invalid_created_at_type(self):
        """Test handling of invalid created_at type in activity."""
        mock_kol_wallet = self._create_mock_document(self.mock_kol_wallet_data_imported, KOLWallet)
        invalid_activity_data = self.mock_wallet_activity_data.copy()
        invalid_activity_data["created_at"] = "not-a-datetime" # Invalid type
        mock_invalid_activity = self._create_mock_document(invalid_activity_data, KOLWalletActivity)

        self.mock_kol_wallet_dao._get_kol_wallet_with_latest_activity.return_value = mock_kol_wallet
        self.mock_kol_wallet_activity_dao.find_many.return_value = [mock_invalid_activity] # find_many returns a list
        
        await self.checker.check_and_notify_for_category(imported_from_following=True)
        self.mock_telegram_sender_instance.send_message_to_user.assert_not_called() # Should skip

    async def test_check_and_notify_invalid_timestamp_type(self):
        """Test handling of invalid timestamp type in activity."""
        mock_kol_wallet = self._create_mock_document(self.mock_kol_wallet_data_imported, KOLWallet)
        invalid_activity_data = self.mock_wallet_activity_data.copy()
        invalid_activity_data["timestamp"] = "not-an-int" # Invalid type
        mock_invalid_activity = self._create_mock_document(invalid_activity_data, KOLWalletActivity)

        self.mock_kol_wallet_dao._get_kol_wallet_with_latest_activity.return_value = mock_kol_wallet
        self.mock_kol_wallet_activity_dao.find_many.return_value = [mock_invalid_activity] # find_many returns a list
        
        await self.checker.check_and_notify_for_category(imported_from_following=True)
        self.mock_telegram_sender_instance.send_message_to_user.assert_not_called() # Should skip


    @patch('workflows.kol_activity_timestamp_discrepancy_monitor.handler.KolActivityTimestampDiscrepancyChecker.check_and_notify_for_category', new_callable=AsyncMock)
    async def test_run_check_cycle_calls_check_for_both_categories(self, mock_check_and_notify):
        """Test run_check_cycle calls check_and_notify_for_category for both True and False."""
        await self.checker.run_check_cycle()
        expected_calls = [call(imported_from_following=True), call(imported_from_following=False)]
        mock_check_and_notify.assert_has_calls(expected_calls, any_order=True)
        self.assertEqual(mock_check_and_notify.call_count, 2)
        self.mock_config_dao.get_application_config.assert_called_once() # From _load_config
        self.mock_monitor_alert_state_dao.get_alert_state.assert_called() # From _load_alert_state

    async def test_perform_kol_activity_timestamp_discrepancy_check_task_success(self):
        """Test the main task entry point on successful execution."""
        with patch('workflows.kol_activity_timestamp_discrepancy_monitor.handler.KolActivityTimestampDiscrepancyChecker.run_check_cycle', new_callable=AsyncMock) as mock_cycle:
            result = await perform_kol_activity_timestamp_discrepancy_check_task()
            mock_cycle.assert_called_once()
            self.assertEqual(result["status"], "success")
            self.assertIn("timestamp", result)

    async def test_perform_kol_activity_timestamp_discrepancy_check_task_failure(self):
        """Test the main task entry point when run_check_cycle raises an exception."""
        test_exception = Exception("Test cycle failure")
        with patch('workflows.kol_activity_timestamp_discrepancy_monitor.handler.KolActivityTimestampDiscrepancyChecker.run_check_cycle', new_callable=AsyncMock, side_effect=test_exception) as mock_cycle:
            result = await perform_kol_activity_timestamp_discrepancy_check_task()
            mock_cycle.assert_called_once()
            self.assertEqual(result["status"], "error")
            self.assertEqual(result["error_message"], str(test_exception))
            self.assertIn("timestamp", result)

if __name__ == '__main__':
    unittest.main()