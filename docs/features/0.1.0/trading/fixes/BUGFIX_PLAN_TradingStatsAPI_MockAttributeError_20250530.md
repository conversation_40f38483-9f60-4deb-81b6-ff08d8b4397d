# Bug修复方案：TradeStatisticsAPI 测试中 AsyncMock AttributeError

## 1. Bug 标识
- **Bug描述**: 在 `test/utils/trading/statistics/test_integration.py` 的 `test_api_error_handling` 测试方法中，尝试对 `@patch('utils.trading.statistics.api.TradeRecordDAO', new_callable=AsyncMock)` 的 mock 对象设置 `side_effect` 时，发生 `AttributeError: 'AsyncMock' object has no attribute 'get_trade_pairs_for_analysis'`。
- **影响范围**: 导致 `test_api_error_handling` 测试用例失败。

## 2. 报告日期/发现日期
- 2025-05-30

## 3. 根源分析概要
当使用 `@patch('module.ClassName', new_callable=AsyncMock)` 时，被修饰的测试方法接收到的是 `AsyncMock` *类* 本身，而不是一个实例。直接在该 mock 类上访问其实例方法（如 `get_trade_pairs_for_analysis`）并尝试设置 `side_effect` 会导致 `AttributeError`。
正确的做法是，首先通过 `mock_class.return_value` 获取由该 mock 类在实际代码中被调用（实例化）时将返回的那个 `AsyncMock` *实例*，然后在这个实例上配置其方法的行为（如 `side_effect`）。

## 4. 详细的、已获批准的修复方案
1.  **修改 `test/utils/trading/statistics/test_integration.py` 文件中的 `test_api_error_handling` 方法。**
2.  将 `@patch('utils.trading.statistics.api.TradeRecordDAO', new_callable=AsyncMock)` 的参数从 `mock_dao` (或类似名称) 重命名为更清晰的 `mock_dao_class`，以表明它是一个类。
3.  在测试方法内部，通过 `mock_instance = mock_dao_class.return_value` 获取将由 `TradeRecordDAO()` 调用返回的 mock 实例。
4.  在该 `mock_instance` 上设置 `side_effect`：`mock_instance.get_trade_pairs_for_analysis.side_effect = Exception("DAO Error {placeholder}")`。
5.  调整断言以适应更通用的异常处理返回结构，例如检查错误消息中是否包含 "DAO Error" 子串，并确认对于通用异常不应返回 `status_code`。

## 5. 测试用例设计
此修复直接针对测试用例的 mock 设置。
- **核心验证**: 修复后，`test_api_error_handling` 测试用例不再抛出 `AttributeError`，并且其原有的逻辑（验证API在DAO层抛出异常时的错误处理行为）应能正确执行并通过。
- **附带验证**: 修复此 mock 问题后，运行完整的单元测试套件，观察之前失败的 `test_get_statistics_summary_no_data` 和 `test_scenario_complex_filters` 是否也得到解决（事实证明是的，因为正确的 mock 行为影响了它们依赖的数据流）。

## 6. 方案提出者/执行者
- AI Assistant (Gemini 2.5 Pro)

## 7. 方案审阅者/批准者
- User (通过后续操作隐式批准)

## 8. 方案批准日期
- 2025-05-30

## 9. 预期的验证方法
- 重新运行所有单元测试 (`poetry run python -m unittest`)。
- 确认 `test_api_error_handling` 测试通过。
- 确认整体测试套件全部通过。 