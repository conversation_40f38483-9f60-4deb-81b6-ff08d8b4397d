import unittest
from unittest.mock import patch, AsyncMock, MagicMock, call
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta, timezone

from beanie import PydanticObjectId
# Use the original TradeRecord for spec and type hinting where appropriate
from models.trade_record import TradeRecord as OriginalTradeRecord, TradeStatus, TradeType
from models.signal import Signal as OriginalSignal # For spec if needed
from models.trade_score_log import TradeScoreLog as OriginalTradeScoreLog # For spec if needed
# dao.trade_record_dao an TradeRecord itself will be patched.
import dao.trade_record_dao # MOVED TO TOP LEVEL

# No class-level decorators for patching anymore
class TestTradeRecordDAO(unittest.IsolatedAsyncioTestCase):
    """单元测试 TradeRecordDAO"""

    def setUp(self): 
        """在每个测试方法运行前设置"""
        self.dao = dao.trade_record_dao.TradeRecordDAO()

        # 1. Patch the TradeRecord class itself (in dao.trade_record_dao module) to control its constructor
        #    and its class methods.
        self.patcher_trade_record_class = patch('dao.trade_record_dao.TradeRecord', autospec=True)
        self.MockTradeRecordClass = self.patcher_trade_record_class.start() # This is the mock for the class
        self.addCleanup(self.patcher_trade_record_class.stop)

        # Configure the mock instance returned by TradeRecord() (constructor call)
        self.mock_tr_instance = MagicMock(spec=OriginalTradeRecord) # Renamed for clarity
        self.mock_tr_instance.save = AsyncMock(name='tr_instance_save_method')
        self.mock_tr_instance.insert = AsyncMock(name='tr_instance_insert_method')
        self.MockTradeRecordClass.return_value = self.mock_tr_instance # When TradeRecord() is called

        # Configure class methods directly on self.MockTradeRecordClass
        self.mock_find_method = MagicMock(name='find_method_on_MockedClass') 
        self.MockTradeRecordClass.find = self.mock_find_method
        
        self.query_builder_mock = MagicMock(name='QueryBuilderMock')
        self.query_builder_mock.to_list = AsyncMock(name='query_builder_to_list')
        self.query_builder_mock.sort = MagicMock(return_value=self.query_builder_mock, name='query_builder_sort')
        self.query_builder_mock.first_or_none = AsyncMock(name='query_builder_first_or_none')
        self.query_builder_mock.count = AsyncMock(name='query_builder_count') # Added for count_by_query
        self.mock_find_method.return_value = self.query_builder_mock

        self.mock_get_method = AsyncMock(name='get_method_on_MockedClass')
        self.MockTradeRecordClass.get = self.mock_get_method

        self.mock_insert_many_method = AsyncMock(name='insert_many_method_on_MockedClass')
        self.MockTradeRecordClass.insert_many = self.mock_insert_many_method
        
        self.mock_find_one_method = AsyncMock(name='find_one_method_on_MockedClass')
        self.MockTradeRecordClass.find_one = self.mock_find_one_method

        self.mock_aggregate_method = AsyncMock(name='aggregate_method_on_MockedClass') # Added for aggregate_trade_statistics
        self.MockTradeRecordClass.aggregate = self.mock_aggregate_method # Assuming aggregate is a class method

        self.mock_motor_collection = MagicMock(name='MotorCollectionMock')
        self.mock_get_motor_collection_method = MagicMock(name='get_motor_collection_method_on_MockedClass', return_value=self.mock_motor_collection)
        self.MockTradeRecordClass.get_motor_collection = self.mock_get_motor_collection_method
        
        self.MockTradeRecordClass.signal_id = MagicMock(name='signal_id_field_on_class')
        self.MockTradeRecordClass.trade_type = MagicMock(name='trade_type_field_on_class')
        self.MockTradeRecordClass.status = MagicMock(name='status_field_on_class')
        self.MockTradeRecordClass.created_at = MagicMock(name='created_at_field_on_class')
        self.MockTradeRecordClass.tx_hash = MagicMock(name='tx_hash_field_on_class') # For verification tests
        self.MockTradeRecordClass.verification_status = MagicMock(name='verification_status_field_on_class') # For verification tests
        self.MockTradeRecordClass.token_in_address = MagicMock(name='token_in_address_field_on_class')
        self.MockTradeRecordClass.token_out_address = MagicMock(name='token_out_address_field_on_class')

        # Patch Signal class used within TradeRecordDAO for the new aggregate method
        self.patcher_signal_class = patch('dao.trade_record_dao.Signal', autospec=True)
        self.MockSignalClass = self.patcher_signal_class.start()
        self.addCleanup(self.patcher_signal_class.stop)

        self.mock_signal_motor_collection = MagicMock(name='SignalMotorCollectionMock')
        self.mock_signal_aggregate_cursor = MagicMock(name='SignalAggregateCursorMock')
        self.mock_signal_aggregate_cursor.to_list = AsyncMock(name='signal_aggregate_to_list')
        
        self.mock_signal_motor_collection.aggregate = MagicMock(return_value=self.mock_signal_aggregate_cursor, name='signal_motor_collection_aggregate')
        self.MockSignalClass.get_motor_collection = MagicMock(return_value=self.mock_signal_motor_collection, name='Signal_get_motor_collection')
        
        # Settings mock for collection names
        self.MockTradeRecordClass.Settings = MagicMock()
        self.MockTradeRecordClass.Settings.name = "trade_records"
        self.MockSignalClass.Settings = MagicMock()
        self.MockSignalClass.Settings.name = "signals"
        # Patch TradeScoreLog as well if its Settings.name is used in the pipeline (it is)
        self.patcher_trade_score_log_class = patch('dao.trade_record_dao.TradeScoreLog', autospec=True)
        self.MockTradeScoreLogClass = self.patcher_trade_score_log_class.start()
        self.addCleanup(self.patcher_trade_score_log_class.stop)
        self.MockTradeScoreLogClass.Settings = MagicMock()
        self.MockTradeScoreLogClass.Settings.name = "trade_score_logs"

    async def test_save_trade_record(self):
        """测试 save 方法"""
        self.mock_tr_instance.save.return_value = self.mock_tr_instance
        returned_record = await self.dao.save(self.mock_tr_instance)
        self.mock_tr_instance.save.assert_called_once()
        self.assertEqual(returned_record, self.mock_tr_instance)

    async def test_insert_one_from_dict(self):
        """测试 insert_one_from_dict 方法"""
        record_data = {
            "signal_id": PydanticObjectId(), "strategy_name": "s1", "trade_provider": "p1",
            "trade_type": TradeType.BUY, "status": TradeStatus.PENDING,
            "token_in_address": "IN", "token_in_amount": 1.0, "token_out_address": "OUT",
            "wallet_address": "W1"
        }
        self.mock_tr_instance.insert.return_value = self.mock_tr_instance
        result_record = await self.dao.insert_one_from_dict(record_data)
        self.MockTradeRecordClass.assert_called_once_with(**record_data)
        self.mock_tr_instance.insert.assert_called_once()
        self.assertEqual(result_record, self.mock_tr_instance)

    async def test_insert_many_from_dicts(self):
        """测试 insert_many_from_dicts 方法"""
        records_data = [
            {"signal_id": PydanticObjectId(), "strategy_name": "s1", "trade_type": TradeType.BUY, "status": TradeStatus.PENDING, "token_in_address": "IN1", "token_out_address": "OUT1", "wallet_address": "W1", "trade_provider": "p1", "token_in_amount": 1.0},
            {"signal_id": PydanticObjectId(), "strategy_name": "s2", "trade_type": TradeType.SELL, "status": TradeStatus.SUCCESS, "token_in_address": "IN2", "token_out_address": "OUT2", "wallet_address": "W2", "trade_provider": "p2", "token_in_amount": 2.0},
        ]
        mock_instances_created = [MagicMock(spec=OriginalTradeRecord) for _ in records_data]
        self.MockTradeRecordClass.side_effect = mock_instances_created
        mock_insert_many_result = MagicMock(name="InsertManyResultMock")
        mock_insert_many_result.inserted_ids = [PydanticObjectId() for _ in records_data]
        self.mock_insert_many_method.return_value = mock_insert_many_result
        result_records = await self.dao.insert_many_from_dicts(records_data)
        self.assertEqual(self.MockTradeRecordClass.call_count, len(records_data))
        for i, data_dict in enumerate(records_data):
            self.MockTradeRecordClass.assert_any_call(**data_dict)
        self.mock_insert_many_method.assert_called_once_with(mock_instances_created)
        self.assertEqual(result_records, mock_instances_created)
        self.MockTradeRecordClass.side_effect = None 
        self.MockTradeRecordClass.return_value = self.mock_tr_instance

    async def test_insert_many_from_dicts_empty_list(self):
        """测试 insert_many_from_dicts 方法，当输入为空列表时"""
        result = await self.dao.insert_many_from_dicts([])
        self.assertEqual(result, [])
        self.mock_insert_many_method.assert_not_called()

    async def test_get_by_id(self):
        """测试 get_by_id 方法"""
        record_id = PydanticObjectId()
        mock_found_record = MagicMock(spec=OriginalTradeRecord)
        self.mock_get_method.return_value = mock_found_record 
        result = await self.dao.get_by_id(record_id)
        self.mock_get_method.assert_called_once_with(record_id)
        self.assertEqual(result, mock_found_record)

    async def test_get_by_id_not_found(self):
        """测试 get_by_id 方法，当记录未找到时"""
        record_id = PydanticObjectId()
        self.mock_get_method.return_value = None
        result = await self.dao.get_by_id(record_id)
        self.mock_get_method.assert_called_once_with(record_id)
        self.assertIsNone(result)

    async def test_find_by_signal_id(self):
        """测试 find_by_signal_id 方法"""
        signal_id = PydanticObjectId()
        mock_records_list = [MagicMock(spec=OriginalTradeRecord), MagicMock(spec=OriginalTradeRecord)]
        self.query_builder_mock.to_list.return_value = mock_records_list
        result = await self.dao.find_by_signal_id(signal_id)
        self.mock_find_method.assert_called_once_with(self.MockTradeRecordClass.signal_id == signal_id)
        self.query_builder_mock.to_list.assert_called_once_with()
        self.assertEqual(result, mock_records_list)

    async def test_find_successful_trade_for_signal(self):
        """测试 find_successful_trade_for_signal 方法"""
        signal_id = PydanticObjectId()
        trade_type_arg = TradeType.BUY
        mock_record = MagicMock(spec=OriginalTradeRecord)
        self.query_builder_mock.first_or_none.return_value = mock_record
        result = await self.dao.find_successful_trade_for_signal(signal_id, trade_type_arg)
        self.mock_find_method.assert_called_once_with(
            self.MockTradeRecordClass.signal_id == signal_id,
            self.MockTradeRecordClass.trade_type == trade_type_arg,
            self.MockTradeRecordClass.status == TradeStatus.SUCCESS
        )
        self.query_builder_mock.sort.assert_called_once_with("-created_at")
        self.query_builder_mock.first_or_none.assert_called_once_with()
        self.assertEqual(result, mock_record)

    async def test_update_record_by_id(self):
        """测试 update_record_by_id 方法，当记录存在时"""
        record_id = PydanticObjectId()
        updates = {"status": TradeStatus.SUCCESS.value, "tx_hash": "new_hash"} 
        mock_record_found = MagicMock(spec=OriginalTradeRecord)
        mock_record_found.save = AsyncMock(name='found_record_save_method') 
        mock_record_found.status = TradeStatus.PENDING
        mock_record_found.tx_hash = None
        self.mock_get_method.return_value = mock_record_found
        result = await self.dao.update_record_by_id(record_id, updates)
        self.mock_get_method.assert_called_once_with(record_id)
        self.assertEqual(mock_record_found.status, TradeStatus.SUCCESS.value)
        self.assertEqual(mock_record_found.tx_hash, "new_hash")
        mock_record_found.save.assert_called_once()
        self.assertEqual(result, mock_record_found)

    async def test_update_record_by_id_not_found(self):
        """测试 update_record_by_id 方法，当记录未找到时"""
        record_id = PydanticObjectId()
        updates = {"status": TradeStatus.SUCCESS.value}
        self.mock_get_method.return_value = None 
        result = await self.dao.update_record_by_id(record_id, updates)
        self.mock_get_method.assert_called_once_with(record_id)
        self.assertIsNone(result)

    async def test_update_record_by_id_no_valid_updates(self):
        """测试 update_record_by_id 方法，当 updates 字典中没有 TradeRecord 的有效字段时"""
        record_id = PydanticObjectId()
        updates = {"non_existent_field": "some_value"}
        mock_record_found = MagicMock(spec=OriginalTradeRecord) 
        mock_record_found.save = AsyncMock(name='found_record_save_no_update')
        # Make non_existent_field not present in spec to test hasattr
        del mock_record_found.non_existent_field 
        self.mock_get_method.return_value = mock_record_found
        result = await self.dao.update_record_by_id(record_id, updates)
        self.mock_get_method.assert_called_once_with(record_id)
        mock_record_found.save.assert_not_called() # Save should not be called
        self.assertEqual(result, mock_record_found)
    
    async def test_get_pending_kol_trade_combinations_for_scoring_success_scenario(self):
        """测试 get_pending_kol_trade_combinations_for_scoring 成功获取待打分组合"""
        limit_param = 10
        max_lookback_days_param = 7
        # lookback_cutoff_time = datetime.now(timezone.utc) - timedelta(days=max_lookback_days_param) # Not directly used in assertion but good for context

        buy_signal_id_obj = PydanticObjectId()
        sell_signal_id_obj = PydanticObjectId()
        buy_trade_id_obj = PydanticObjectId()
        sell_trade_id_obj = PydanticObjectId()
        kol_addr1 = "kol_address_1"
        strategy_name1 = "StrategyAlpha"
        token_a_addr = "TOKEN_A_ADDR"
        usdc_addr = "USDC_ADDR"

        # 1. Mock an aggregation result from Signal.get_motor_collection().aggregate(...).to_list(...)
        # These should be DICTIONARIES, as they would come from the database aggregation
        raw_buy_trade_data = {
            "_id": buy_trade_id_obj, "signal_id": buy_signal_id_obj,
            "status": TradeStatus.SUCCESS.value, "trade_type": TradeType.BUY.value,
            "strategy_name": strategy_name1,
            "token_out_address": token_a_addr, # e.g. buy TOKEN_A with USDC
            "token_in_address": usdc_addr,
            "created_at": datetime.now(timezone.utc) - timedelta(days=2),
            "wallet_address": kol_addr1, # Assuming wallet_address is part of TradeRecord
            "trade_provider": "provider_X",
            "token_in_amount": 100.0,
            "token_out_amount": 1.0
        }
        raw_sell_trade_data = {
            "_id": sell_trade_id_obj, "signal_id": sell_signal_id_obj,
            "status": TradeStatus.SUCCESS.value, "trade_type": TradeType.SELL.value,
            "strategy_name": strategy_name1,
            "token_in_address": token_a_addr, # e.g. sell TOKEN_A for USDC
            "token_out_address": usdc_addr,
            "created_at": datetime.now(timezone.utc) - timedelta(days=1),
            "wallet_address": kol_addr1,
            "trade_provider": "provider_Y",
            "token_in_amount": 1.0,
            "token_out_amount": 105.0
        }

        mock_raw_agg_result = [
            {
                "buy_trade": raw_buy_trade_data, # Now a dictionary
                "sell_trade": raw_sell_trade_data, # Now a dictionary
                "kol_wallet_address": kol_addr1,
                "strategy_name": strategy_name1
            }
        ]
        self.mock_signal_aggregate_cursor.to_list.return_value = mock_raw_agg_result

        # 2. Mock the TradeRecord constructor (self.MockTradeRecordClass)
        #    These mock instances will be returned by TradeRecord(**data)
        #    The **raw_..._data already includes _id and strategy_name, so no need to pass them separately.
        mock_buy_tr_instance = MagicMock(spec=OriginalTradeRecord, **raw_buy_trade_data)
        mock_sell_tr_instance = MagicMock(spec=OriginalTradeRecord, **raw_sell_trade_data)
        
        self.MockTradeRecordClass.side_effect = [mock_buy_tr_instance, mock_sell_tr_instance]

        # 3. Call the DAO method
        results = await self.dao.get_pending_kol_trade_combinations_for_scoring(
            limit=limit_param, 
            max_lookback_days=max_lookback_days_param
        )

        # 4. Assertions
        self.MockSignalClass.get_motor_collection.assert_called_once()
        self.mock_signal_motor_collection.aggregate.assert_called_once()
        actual_pipeline_arg = self.mock_signal_motor_collection.aggregate.call_args[0][0]
        # Basic check on pipeline structure - more detailed checks can be added if necessary
        self.assertIsInstance(actual_pipeline_arg, list)
        self.assertTrue(len(actual_pipeline_arg) > 5) # Check if multiple stages are present
        self.assertEqual(actual_pipeline_arg[0]['$match']['signal_type'], "kol_sell") # Check a specific part of first stage

        self.mock_signal_aggregate_cursor.to_list.assert_called_once_with(length=limit_param)
        
        expected_constructor_calls = [
            call(**raw_buy_trade_data), # Assert constructor called with the dictionary
            call(**raw_sell_trade_data)  # Assert constructor called with the dictionary
        ]
        self.MockTradeRecordClass.assert_has_calls(expected_constructor_calls, any_order=False)

        self.assertEqual(len(results), 1)
        processed_combo = results[0]
        self.assertIs(processed_combo["buy_trade"], mock_buy_tr_instance) # Should be the instance returned by constructor
        self.assertIs(processed_combo["sell_trade"], mock_sell_tr_instance)# Should be the instance returned by constructor
        self.assertEqual(processed_combo["kol_wallet_address"], kol_addr1)
        self.assertEqual(processed_combo["strategy_name"], strategy_name1)

        self.MockTradeRecordClass.side_effect = None
        self.MockTradeRecordClass.return_value = self.mock_tr_instance

    async def test_get_pending_kol_trade_combinations_empty_result_from_aggregation(self):
        """测试 get_pending_kol_trade_combinations_for_scoring 当聚合查询返回空列表时"""
        self.mock_signal_aggregate_cursor.to_list.return_value = []

        results = await self.dao.get_pending_kol_trade_combinations_for_scoring(limit=10, max_lookback_days=7)

        self.assertEqual(results, [])
        self.MockTradeRecordClass.assert_not_called() # Constructor should not be called

    async def test_get_pending_kol_trade_combinations_aggregation_exception(self):
        """测试 get_pending_kol_trade_combinations_for_scoring 当聚合查询抛出异常时"""
        self.mock_signal_aggregate_cursor.to_list.side_effect = Exception("DB error")

        results = await self.dao.get_pending_kol_trade_combinations_for_scoring(limit=10, max_lookback_days=7)

        self.assertEqual(results, []) # Expect empty list on error
        self.MockTradeRecordClass.assert_not_called()

if __name__ == '__main__':
    unittest.main() 