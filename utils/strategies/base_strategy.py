from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger("Strategy")

class BaseStrategy(ABC):
    """策略基类"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化策略
        
        Args:
            config: 策略配置
        """
        self.config = config
        
    @abstractmethod
    async def generate_signals(self, *args, **kwargs) -> Dict[str, Any]:
        """生成交易信号
        
        Args:
            time_point: 当前时间点
            activities: KOL活动数据 (可选)
            tokens: 代币数据 (可选)
            wallets: KOL钱包数据 (可选)
            
        Returns:
            List[Dict[str, Any]]: 交易信号列表
        """
        pass 