# Meme Monitor 信号生成流程 (新手版)

## 这个系统是做什么的？ (流程简介)

想象一下，你想知道加密货币领域里那些"聪明钱"(KOL - Key Opinion Leaders，关键意见领袖) 都在买些什么新币。这个系统就是帮你做这件事的！

它会自动监控这些 KOL 的钱包交易，当发现有一些新的、有潜力的币被多个 KOL 同时买入时，系统就会认为这是一个**买入信号**，并通知你。

但是，光知道买入还不够，还需要知道什么时候可能需要卖出。所以，系统也会持续关注你收到的那些买入信号：
1.  看看是不是过了太久还没动静 (比如设置了 24 小时)。
2.  或者，看看当初买入的那些 KOL 是不是开始卖出手里的这个币了。

如果满足了上面任何一个条件，系统就会认为这是一个**卖出信号**，并且也会通知你。

简单来说，这个系统就是：
*   **发现KOL买入机会 -> 发送买入信号**
*   **监控买入信号 -> 判断卖出时机 -> 发送卖出信号**

---

## 买入信号是如何产生的？

系统会定期（比如每隔几秒钟）做以下事情来寻找买入机会：

1.  **查看最近的交易**: 看看过去几个小时内，我们关注的那些 KOL 有没有买入行为。
2.  **筛选大额买入**: 只关注那些买入金额比较大的交易。
3.  **找"集体行动"**: 找出哪些代币被**多个**符合条件的 KOL 在近期都买入了。
4.  **关注新币**: 检查这些被多个 KOL 买入的代币是不是最近才发行的"新币"。
5.  **生成买入信号**: 如果一个新币被足够多的 KOL 大额买入，系统就认为这是一个值得关注的买入信号。
6.  **记录并通知**: 系统会把这个买入信号存到数据库里，标记为**"开放状态"**（表示这个机会刚被发现，还没结束），然后通过 Telegram 通知所有用户。

---

## 卖出信号是如何产生的？(以及什么是"开放状态"？)

系统生成了买入信号后，不会就此罢手，它还会持续"盯"着这些信号，判断是否应该卖出。这个过程也是定期进行的：

1.  **找到所有"活跃"的买入信号**: 系统会查看数据库里所有状态是**"开放状态 (Open Status)"**的买入信号。
    *   **什么是"开放状态"？** 当系统生成一个买入信号时，会给它一个"开放"的标记。这表示我们认为这个买入机会当前仍然有效，系统需要继续关注它后续的发展。它就像一个"待办事项"。

2.  **检查卖出条件**: 对每一个"开放状态"的买入信号，系统会检查两个主要的卖出条件：
    *   **条件一：超时**: 这个买入信号是不是已经存在很久了？比如，我们可能设置了"如果一个买入信号发出后 24 小时还没有动静，就触发卖出"。如果达到了设定的时间，就认为需要卖出了。
    *   **条件二：KOL开始卖出**: 当初买入这个币的那些 KOL，是不是有一定比例的人开始卖出这个币了？比如，我们可能设置了"如果超过 30% 的初始买入 KOL 开始卖出，就触发卖出"。如果达到了这个比例，也认为需要卖出了。

3.  **生成卖出信号**: 如果上面两个条件**任何一个**满足了：
    *   系统就会生成一个对应的**卖出信号**。
    *   这个卖出信号会记录是因为"超时"还是因为"KOL卖出比例"达标。

4.  **更新状态并通知**:
    *   系统会把原始的那个买入信号的状态从"开放"改成**"已售出 (Sold)"**（或者类似的标记），表示这个信号的生命周期结束了，不再是"开放状态"了。
    *   然后，系统会把这个卖出信号的信息（哪个币，为什么卖）通过 Telegram 通知所有用户。
    *   同时，系统也会记录下这个卖出信号本身，以及通知发送的情况。

---

## 当前使用的策略参数（示例）

为了让系统知道具体怎么筛选和判断，我们需要给它一些具体的"规矩"，这些规矩就是策略参数。以下是目前系统正在使用的一组参数示例（这些参数可以在数据库中调整）：

*   **关注KOL多长时间的交易？** `2` 小时 (`transaction_lookback_hours`) - 只看KOL最近2小时的买入。
*   **买入金额至少要多少？** `$300` (`transaction_min_amount`) - 只关注买入金额超过300美元的交易。
*   **至少要有多少个KOL买入？** `6` 个 (`kol_account_min_count`) - 一个币至少要有6个符合条件的KOL买入才算数。
*   **什么样的KOL才算数？** 交易次数在 `10` 到 `100` 次之间 (`kol_account_min_txs`, `kol_account_max_txs`) - 过滤掉交易太少（可能是新号）或太多（可能是机器人）的KOL。
*   **关注多新的"新币"？** `24` 小时内发行 (`token_mint_lookback_hours`) - 只对过去24小时内新发行的币生成买入信号。
*   **买入信号发出后多久算超时？** `24` 小时 (`sell_strategy_hours`) - 如果买入信号发出后24小时还没动静，就触发卖出。
*   **多少比例的KOL开始卖出就触发卖出信号？** `50%` (`sell_kol_ratio`) - 如果当初买入的KOL中，有一半或更多的人开始卖出，就触发卖出信号。
*   **同一个币的买入通知隔多久发一次？** `60` 分钟 (`same_token_notification_interval`) - 避免短时间内对同一个币反复发送买入通知。

---

## 流程图 (Mermaid)

*这些图表展示了数据在系统不同部分之间的流动。DB代表数据库。*

### 买入信号流程

```mermaid
graph TD
    subgraph "监控KOL活动发现买入机会"
        A[定时器触发] --> B(检查KOL近期买入);
        B -- 筛选 --> C{找到多个KOL买入的新币};
        C -- 记录 --> G["DB: 存入买入信号 (标记为开放)"];
        C -- 传递信号ID --> H(准备发送通知);
        H -- 通知 --> I{给用户发Telegram消息};
        I -- 记录 --> M["DB: 记录消息已发送 (关联买入信号ID)"];
    end
```

### 卖出信号流程

```mermaid
graph TD
    subgraph "监控开放买入信号判断卖出时机"
        N[定时器触发] --> O(查找开放状态的买入信号);
        O -- 读取 --> R["DB: 获取开放的买入信号"];
        O -- 检查 --> P{检查超时或KOL卖出比例};
        P -- 读取 --> S["DB: 查看KOL卖出活动"];
        P -- 满足条件 --> T(生成卖出信号数据);
        T -- 处理 --> U{处理卖出信号};
        U -- 读取 --> R_check["DB: 再次确认买入信号状态"];
        U -- 记录 --> V["DB: 存入卖出信号"];
        U -- 更新 --> R_update["DB: 将买入信号标记为已售出"];
        U -- 通知 --> W{给用户发Telegram消息};
        W -- 记录 --> Y["DB: 记录消息已发送 (关联卖出信号ID)"];
    end
```

---

## 技术细节 (供进阶阅读)

### 当前策略配置示例

以下是数据库中存储的当前策略配置的一个具体示例 (`kol_activity` 类型的配置):

```json
{
  "_id": ObjectId("67d83fac6a23ac1a3efe55fe"), // 数据库内部ID
  "type": "kol_activity", // 配置类型
  "data": {
    "transaction_lookback_hours": 2,     // 查找买入信号时回看KOL交易记录的小时数
    "transaction_min_amount": 300,       // KOL单笔买入的最小金额（美元）
    "kol_account_min_count": 6,        // 触发买入信号所需的最少KOL数量
    "token_mint_lookback_hours": 24,     // 关注的代币必须是过去多少小时内发行的
    "same_token_notification_interval": 60, // 同一代币买入通知的最小间隔（分钟）
    "kol_account_min_txs": 10,         // 筛选KOL时要求的最小交易次数
    "kol_account_max_txs": 100,        // 筛选KOL时要求的最大交易次数
    "sell_strategy_hours": 24,           // 买入信号发出后判断超时的阈值（小时）
    "sell_kol_ratio": 0.5              // 触发卖出信号的KOL卖出比例阈值 (50%)
  }
}
```

### 涉及的关键数据模型

*   **`Signal` (`models/signal.py`)**: 核心模型，用于存储生成的买入和卖出信号的详细信息。
    *   `signal_type`: 区分信号类型 ('kol_buy', 'kol_sell')。
    *   `status`: 跟踪信号状态 ('open', 'sold')，主要用于买入信号。
    *   `buy_signal_ref_id`: 对于卖出信号，关联对应的买入信号 ID。
    *   `trigger_conditions`: 记录触发信号的具体条件（例如哪个阈值被触发）。
    *   `hit_kol_wallets`: 记录与信号相关的KOL钱包地址。
*   **`TokenMessageSendHistory` (`models/token_message_send_history.py`)**: 记录特定信号消息发送给特定用户的状态（成功/失败）和时间。通过 `signal_id` 关联到 `Signal`。
*   **`KOLWalletActivity` (`models/kol_wallet_activity.py`)**: 存储KOL钱包的交易活动记录（买入/卖出）。
*   **`Token` (`models/token.py`)**: 存储代币的基本信息（名称、符号、Mint时间等）。
*   **`KolMonitorConfig` (`models/config.py`)**: 存储工作流所需的配置参数（如回溯时间、最小金额、KOL数量阈值、超时设置、KOL卖出比例阈值等）。
*   **`TelegramUser` (`models/telegram_user.py`)**: 存储需要接收通知的用户信息（主要是 Telegram Chat ID）。

### 买入信号流程中的具体函数

1.  **定时触发**: 工作流配置文件 `monitor_kol_activity_workflow.yaml` 中的 `FindTargetTokensNode` 定义了触发间隔。
2.  **筛选目标代币**: 主要逻辑在 `workflows/monitor_kol_activity/handler.py` 的 `filter_target_tokens` 函数中实现。
    *   它会调用 `ConfigDAO` 获取配置。
    *   通过 `KOLWalletActivityDAO` 执行 MongoDB 聚合查询来筛选符合条件的KOL活动和代币。
    *   可能调用 `SolanaMonitor.get_token_info` (封装在 `find_tokens_by_address` 中) 获取链上代币信息，并使用 `TokenDAO` 读写数据库。
    *   最后使用 `SignalDAO.insert_signals` 保存生成的 'kol_buy' 信号。
3.  **发送通知**: 主要逻辑在 `workflows/monitor_kol_activity/handler.py` 的 `send_message_to_channel` 函数中实现。
    *   它接收 `filter_target_tokens` 传递过来的 `token_info` 和 `signal_id`。
    *   调用 `TelegramUserDAO` 获取用户列表。
    *   调用 `TokenMessageSendHistoryDAO.recent_history` 检查发送频率。
    *   使用 `TelegramMessageSender` 发送消息。
    *   调用 `TokenMessageSendHistoryDAO.insert_one` 记录发送历史。

### 卖出信号流程中的具体函数

1.  **定时触发**: 工作流配置文件 `sell_signal_sender.yaml` 中的 `GenerateSellSignalDataNode` 定义了触发间隔。
2.  **检查卖出条件**: 主要逻辑在 `workflows/monitor_kol_activity/sell_signal_handler.py` 的 `generate_sell_signals` 函数中。
    *   它调用 `ConfigDAO` 获取配置。
    *   使用 `SignalDAO` 查询状态为 'open' 的 'kol_buy' 信号。
    *   进行超时判断。
    *   使用 `KOLWalletActivityDAO` 查询相关KOL的卖出活动来计算比例。
    *   如果条件满足，`yield` 包含卖出信息的字典。
3.  **处理卖出信号**: 主要逻辑在 `workflows/monitor_kol_activity/sell_signal_handler.py` 的 `process_sell_signal` 函数中。
    *   它接收 `generate_sell_signals` 生成的数据。
    *   使用 `SignalDAO` 检查买入信号状态和是否存在重复卖出信号。
    *   使用 `SignalDAO.insert_signals` 创建 'kol_sell' 信号记录。
    *   使用 `SignalDAO.update_signal_status` 更新买入信号状态为 'sold'。
    *   调用 `TelegramUserDAO` 获取用户。
    *   使用 `TelegramMessageSender` 发送通知。
    *   使用 `TokenMessageSendHistoryDAO.insert_many` 批量保存发送历史。 