import unittest
import asyncio
import time
import random
import logging
from utils.workflows.parallel_control import ParallelExecutor

class TestParallelExecutor(unittest.TestCase):
    """ParallelExecutor类的单元测试"""

    @classmethod
    def setUpClass(cls):
        """在所有测试开始前运行"""
        cls.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(cls.loop)

    @classmethod
    def tearDownClass(cls):
        """在所有测试结束后运行"""
        tasks = asyncio.all_tasks(loop=cls.loop)
        for task in tasks:
            task.cancel()
        if tasks:
            cls.loop.run_until_complete(asyncio.gather(*tasks, return_exceptions=True))
        cls.loop.close()
        asyncio.set_event_loop(None)

    def setUp(self):
        """测试前的准备工作"""
        # executor将在async_setup中初始化
        self.execution_tracker = {}
        self.logger = logging.getLogger("TestParallelExecutor")
        
    async def async_setup(self):
        """每个测试方法运行前的异步设置"""
        self.executor = ParallelExecutor(concurrency=3, test_mode=True) # 移除 loop
    
    async def async_teardown(self):
        """每个测试方法运行后的异步清理"""
        if hasattr(self, 'executor') and self.executor:
            self.executor.stop() # ParallelExecutor 的 stop 是同步的

    async def sample_coroutine(self, coro_id, sleep_time=0.1, should_fail=False, should_raise=False):
        """示例协程，用于测试
        
        Args:
            coro_id: 协程ID，用于标识
            sleep_time: 睡眠时间，模拟工作
            should_fail: 是否应该失败
            should_raise: 是否应该抛出异常
        """
        if coro_id not in self.execution_tracker:
            self.execution_tracker[coro_id] = {'count': 0, 'last_run': time.time(), 'times': []}
        
        self.execution_tracker[coro_id]['count'] += 1
        current_time = time.time()
        self.execution_tracker[coro_id]['last_run'] = current_time
        self.execution_tracker[coro_id]['times'].append(current_time)
        
        await asyncio.sleep(sleep_time)
        
        if should_raise:
            raise ValueError(f"协程 {coro_id} 抛出了异常")
        
        return not should_fail
    
    # 用于生成可重复使用的协程的工厂函数
    def make_coroutine(self, coro_id, sleep_time=0.1, should_fail=False, should_raise=False):
        """返回一个新的协程对象，用于解决协程重用问题"""
        return self.sample_coroutine(coro_id, sleep_time, should_fail, should_raise)
        
    async def _test_execute_single_coroutine_actual(self):
        """测试执行单个协程的异步实现"""
        result = await self.executor.execute(self.make_coroutine('single'))
        self.assertTrue(result, "执行单个协程应该成功")
        self.assertEqual(self.execution_tracker['single']['count'], 1, "协程应该只执行一次")
        
    def test_execute_single_coroutine(self):
        """测试执行单个协程"""
        self.loop.run_until_complete(self.async_setup())
        try:
            self.loop.run_until_complete(self._test_execute_single_coroutine_actual())
        finally:
            self.loop.run_until_complete(self.async_teardown())
        
    async def _test_execute_batch_coroutines_actual(self):
        """测试批量执行协程的异步实现"""
        coros = [self.make_coroutine(f'batch_{i}', sleep_time=0.1) for i in range(5)]
        tasks = await self.executor.execute_batch(coros)
        results = await asyncio.gather(*tasks)
        for result in results:
            self.assertTrue(result, "所有协程都应该成功执行")
        self.assertEqual(len(self.execution_tracker), 5, "应该执行5个协程")
        for i in range(5):
            self.assertEqual(self.execution_tracker[f'batch_{i}']['count'], 1, f"协程 batch_{i} 应该执行一次")

    def test_execute_batch_coroutines(self):
        """测试批量执行协程"""
        self.loop.run_until_complete(self.async_setup())
        try:
            self.loop.run_until_complete(self._test_execute_batch_coroutines_actual())
        finally:
            self.loop.run_until_complete(self.async_teardown())
        
    async def _test_simple_failing_task_actual(self):
        """测试简单失败任务的异步实现"""
        original_test_mode = self.executor._test_mode
        self.executor._test_mode = False # 关闭测试模式以抛出异常
        try:
            await self.executor.execute(self.make_coroutine('simple_fail', should_raise=True))
            self.fail("应该抛出异常")
        except ValueError:
            pass # 预期异常
        finally:
            self.executor._test_mode = original_test_mode
        self.assertEqual(self.execution_tracker['simple_fail']['count'], 1, "失败任务应该执行一次")

    def test_simple_failing_task(self):
        """测试简单的失败任务"""
        self.loop.run_until_complete(self.async_setup())
        try:
            self.loop.run_until_complete(self._test_simple_failing_task_actual())
        finally:
            self.loop.run_until_complete(self.async_teardown())
            
    def test_retry_mechanism(self):
        """测试重试机制 - 只验证基本功能"""
        # This test is synchronous and doesn't need the async wrapper
        self.loop.run_until_complete(self.async_setup()) # executor is needed
        try:
            original_base_delay = self.executor._base_retry_delay
            original_max_delay = self.executor._max_retry_delay
            self.executor._base_retry_delay = 0.1
            self.executor._max_retry_delay = 0.5
            try:
                delay1 = self.executor._calculate_retry_delay(0)
                delay2 = self.executor._calculate_retry_delay(1)
                delay3 = self.executor._calculate_retry_delay(2)
                self.assertLess(delay1, delay2)
                self.assertLess(delay2, delay3)
                max_delay = self.executor._calculate_retry_delay(100)
                self.assertLessEqual(max_delay, self.executor._max_retry_delay)
                mock_task = object()
                self.executor._task_metadata[mock_task] = {
                    "coro": lambda: None,
                    "retry_count": 2,
                    "start_time": time.time() - 10
                }
                metadata = self.executor.get_task_metadata(mock_task)
                self.assertIsNotNone(metadata)
                self.assertEqual(metadata["retry_count"], 2)
                del self.executor._task_metadata[mock_task]
            finally:
                self.executor._base_retry_delay = original_base_delay
                self.executor._max_retry_delay = original_max_delay
        finally:
            self.loop.run_until_complete(self.async_teardown())

    def test_unlimited_retries(self):
        """测试无限重试功能 - 只验证基本逻辑"""
        # This test is synchronous
        self.loop.run_until_complete(self.async_setup())
        try:
            mock_task = object()
            self.executor._task_metadata[mock_task] = {
                "coro": lambda: None,
                "retry_count": 10,
                "start_time": time.time() - 60
            }
            metadata = self.executor.get_task_metadata(mock_task)
            self.assertEqual(metadata["retry_count"], 10)
            delay = self.executor._calculate_retry_delay(10)
            self.assertGreater(delay, 0)
            del self.executor._task_metadata[mock_task]
        finally:
            self.loop.run_until_complete(self.async_teardown())

    def test_task_restart(self):
        """测试任务重启功能 - 只验证基本功能"""
        # This test is synchronous
        self.loop.run_until_complete(self.async_setup())
        try:
            def simple_coro_factory():
                async def simple_coro():
                    return True
                return simple_coro()
            mock_task = object()
            self.executor._task_metadata[mock_task] = {
                "coro": simple_coro_factory,
                "retry_count": 1,
                "start_time": time.time() - 5
            }
            delay = self.executor._calculate_retry_delay(1)
            self.assertGreater(delay, 0)
            metadata = self.executor.get_task_metadata(mock_task)
            self.assertEqual(metadata["retry_count"], 1)
            del self.executor._task_metadata[mock_task]
        finally:
            self.loop.run_until_complete(self.async_teardown())

    async def _test_concurrency_limit_actual(self):
        """测试并发度限制的异步实现"""
        coros = [self.make_coroutine(f'concurrency_{i}', sleep_time=0.5) for i in range(6)]
        tasks = await self.executor.execute_batch(coros)
        await asyncio.gather(*tasks)
        # Give a small delay for execution_tracker to be updated by tasks
        await asyncio.sleep(0.01)        
        expected_coros = {f'concurrency_{i}' for i in range(6)}
        actual_coros = set(self.execution_tracker.keys())
        self.assertTrue(expected_coros.issubset(actual_coros), 
                       f"应该执行所有协程，期望: {expected_coros}，实际: {actual_coros}")

    def test_concurrency_limit(self):
        """测试并发度限制"""
        self.loop.run_until_complete(self.async_setup())
        try:
            self.loop.run_until_complete(self._test_concurrency_limit_actual())
        finally:
            self.loop.run_until_complete(self.async_teardown())
        
    async def _test_task_cleanup_actual(self):
        """测试任务清理的异步实现"""
        coros = [self.make_coroutine(f'cleanup_{i}', sleep_time=0.1) for i in range(3)]
        tasks = await self.executor.execute_batch(coros)
        await asyncio.gather(*tasks)
        # ParallelExecutor 在任务完成后会自动清理
        # 这里可能需要稍微等待一下，确保清理逻辑已执行
        await asyncio.sleep(0.01) 
        self.assertEqual(len(self.executor._tasks), 0, "所有任务应该被清理")
        self.assertEqual(len(self.executor._task_metadata), 0, "所有任务元数据应该被清理")

    def test_task_cleanup(self):
        """测试任务清理"""
        self.loop.run_until_complete(self.async_setup())
        try:
            self.loop.run_until_complete(self._test_task_cleanup_actual())
        finally:
            self.loop.run_until_complete(self.async_teardown())
    
    async def _test_stop_running_tasks_actual(self):
        """测试停止运行中的任务的异步实现"""
        # 创建一些长时间运行的任务
        coros = [self.make_coroutine(f'stop_task_{i}', sleep_time=2) for i in range(3)]
        tasks = await self.executor.execute_batch(coros)
        
        await asyncio.sleep(0.1) # 给任务一点时间开始运行
        self.executor.stop() # 发出停止信号
        
        # 等待任务被取消或完成
        # 注意: asyncio.gather 会在任务被取消时抛出 CancelledError
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 验证任务是否被取消或提前完成 (由于 stop())
        # 很难精确验证每个任务的状态，但至少可以确保没有一个任务完整运行2秒
        for i in range(3):
            coro_id = f'stop_task_{i}'
            if coro_id in self.execution_tracker:
                 # 如果任务开始执行了，检查它的执行次数
                 self.assertEqual(self.execution_tracker[coro_id]['count'], 1, f"任务 {coro_id} 应该只执行一次")
        
        # 检查执行器状态
        # self.assertFalse(self.executor._running, "执行器应该已停止") # _running 属性不存在
        # 检查 _tasks 是否为空作为执行器已停止的标志
        self.assertEqual(len(self.executor._tasks), 0, "所有任务应该被清理")

    def test_stop_running_tasks(self):
        """测试停止运行中的任务"""
        self.loop.run_until_complete(self.async_setup())
        try:
            self.loop.run_until_complete(self._test_stop_running_tasks_actual())
        finally:
            # async_teardown 也会调用 stop，这里确保测试逻辑中的 stop 已执行
            # 如果 async_teardown 依赖于特定的 executor 状态，则不需要再次调用
            self.loop.run_until_complete(self.async_teardown()) 
            # pass # async_teardown will handle stopping if needed

    async def _test_exception_handling_actual(self):
        """测试异常处理的异步实现"""
        original_test_mode = self.executor._test_mode
        self.executor._test_mode = False # 关闭测试模式以确保异常传播
        try:
            coro_should_raise = self.make_coroutine('exc_handler_fail', should_raise=True)
            coro_should_succeed = self.make_coroutine('exc_handler_ok')
            
            tasks = await self.executor.execute_batch([coro_should_raise, coro_should_succeed])
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            self.assertIsInstance(results[0], ValueError, "第一个任务应该返回ValueError")
            self.assertTrue(results[1], "第二个任务应该成功")
            
            self.assertIn('exc_handler_fail', self.execution_tracker)
            self.assertIn('exc_handler_ok', self.execution_tracker)

        finally:
            self.executor._test_mode = original_test_mode

    def test_exception_handling(self):
        """测试异常处理"""
        self.loop.run_until_complete(self.async_setup())
        try:
            self.loop.run_until_complete(self._test_exception_handling_actual())
        finally:
            self.loop.run_until_complete(self.async_teardown())

if __name__ == "__main__":
    unittest.main() 