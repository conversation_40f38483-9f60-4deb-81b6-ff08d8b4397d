"""交易摘要生成器测试

测试交易摘要生成器的各项功能，包括：
- 日报摘要生成
- 周报摘要生成
- 自定义周期摘要生成
- 摘要格式化
- 错误处理
"""

import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
from typing import Dict, Any, List

from utils.trading.statistics.summary_generator import TradingSummaryGenerator, SummaryFormatter


class TestTradingSummaryGenerator:
    """交易摘要生成器测试类"""
    
    @pytest.fixture
    def mock_api(self) -> AsyncMock:
        """创建模拟的API实例"""
        api = AsyncMock()
        return api
    
    @pytest.fixture
    def summary_generator(self, mock_api: AsyncMock) -> TradingSummaryGenerator:
        """创建摘要生成器实例"""
        return TradingSummaryGenerator(api=mock_api)
    
    @pytest.fixture
    def sample_detailed_stats(self) -> Dict[str, Any]:
        """示例详细统计数据"""
        return {
            "success": True,
            "overall_stats": {
                "total_trades": 15,
                "total_win_rate": 66.67,
                "total_profit_rate": 12.5,
                "avg_profit_rate": 8.3,
                "total_profit_amount": 3.2,
                "profitable_trades": 10,
                "loss_trades": 5
            },
            "strategy_stats": [
                {
                    "strategy_name": "gmgn_smart_money",
                    "trade_count": 8,
                    "win_rate": 75.0,
                    "avg_profit_rate": 12.5,
                    "total_profit_amount": 2.0,
                    "profitable_trades": 6,
                    "loss_trades": 2
                },
                {
                    "strategy_name": "manual_trading",
                    "trade_count": 5,
                    "win_rate": 60.0,
                    "avg_profit_rate": 4.2,
                    "total_profit_amount": 1.0,
                    "profitable_trades": 3,
                    "loss_trades": 2
                },
                {
                    "strategy_name": "dca_strategy",
                    "trade_count": 2,
                    "win_rate": 50.0,
                    "avg_profit_rate": -1.0,
                    "total_profit_amount": 0.2,
                    "profitable_trades": 1,
                    "loss_trades": 1
                }
            ],
            "token_stats": [
                {
                    "token": "SOL",
                    "token_address": "So11111111111111111111111111111111111111112",
                    "total_trades": 6,
                    "total_win_rate": 83.33,
                    "total_profit_rate": 25.0,
                    "avg_profit_rate": 15.2,
                    "profitable_trades": 5,
                    "loss_trades": 1
                },
                {
                    "token": "BONK",
                    "token_address": "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263",
                    "total_trades": 4,
                    "total_win_rate": 50.0,
                    "total_profit_rate": 5.0,
                    "avg_profit_rate": 2.5,
                    "profitable_trades": 2,
                    "loss_trades": 2
                }
            ],
            "profit_rankings": [
                {
                    "signal_id": "signal_001",
                    "profit_rate": 45.2,
                    "profit_amount": 1.8,
                    "token": "SOL"
                },
                {
                    "signal_id": "signal_002",
                    "profit_rate": 32.1,
                    "profit_amount": 1.2,
                    "token": "BONK"
                }
            ],
            "loss_rankings": [
                {
                    "signal_id": "signal_003",
                    "loss_rate": 18.5,
                    "loss_amount": 0.5,
                    "token": "MEME"
                },
                {
                    "signal_id": "signal_004",
                    "loss_rate": 12.3,
                    "loss_amount": 0.3,
                    "token": "PEPE"
                }
            ],
            "data_range": {
                "start_date": "2024-01-01",
                "end_date": "2024-01-01"
            },
            "trade_pair_count": 15
        }
    
    @pytest.mark.asyncio
    async def test_generate_daily_summary_success(
        self, 
        summary_generator: TradingSummaryGenerator,
        sample_detailed_stats: Dict[str, Any]
    ):
        """测试成功生成日报摘要"""
        target_date = datetime(2024, 1, 2)
        
        # 配置mock API返回
        summary_generator.api.get_detailed_statistics.return_value = sample_detailed_stats
        
        result = await summary_generator.generate_daily_summary(
            target_date=target_date,
            strategies=["gmgn_smart_money"],
            tokens=["SOL"]
        )
        
        # 验证API调用
        summary_generator.api.get_detailed_statistics.assert_called_once()
        call_args = summary_generator.api.get_detailed_statistics.call_args[1]
        
        # 验证时间范围（昨天的24小时）
        expected_start = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
        expected_end = target_date.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        assert call_args["start_date"] == expected_start
        assert call_args["end_date"] == expected_end
        assert call_args["strategies"] == ["gmgn_smart_money"]
        assert call_args["tokens"] == ["SOL"]
        
        # 验证返回结果
        assert result["period_type"] == "日"
        assert result["start_date"] == "2024-01-02"
        assert result["end_date"] == "2024-01-02"
        
        # 验证总体统计
        overall = result["overall_stats"]
        assert overall["total_trades"] == 15
        assert overall["win_rate"] == 66.67
        assert overall["total_pnl_rate"] == 12.5
        
        # 验证策略排行
        strategies = result["top_strategies"]
        assert len(strategies) == 3
        assert strategies[0]["strategy"] == "gmgn_smart_money"  # 按盈利额排序
        assert strategies[0]["total_pnl_rate"] == 12.5
        
        # 验证极值统计
        extremes = result["extremes"]
        assert "max_profit" in extremes
        assert "max_loss" in extremes
        assert extremes["max_profit"]["pnl_rate"] == 45.2
        assert extremes["max_loss"]["pnl_rate"] == 18.5
    
    @pytest.mark.asyncio
    async def test_generate_daily_summary_default_date(
        self, 
        summary_generator: TradingSummaryGenerator,
        sample_detailed_stats: Dict[str, Any]
    ):
        """测试使用默认日期生成日报摘要"""
        summary_generator.api.get_detailed_statistics.return_value = sample_detailed_stats
        
        with patch('utils.trading.statistics.summary_generator.datetime') as mock_datetime:
            mock_now = datetime(2024, 1, 2, 10, 30, 0)
            mock_datetime.now.return_value = mock_now
            mock_datetime.side_effect = lambda *args, **kw: datetime(*args, **kw)
            
            result = await summary_generator.generate_daily_summary()
            
            # 验证使用昨天的日期
            expected_date = mock_now - timedelta(days=1)
            assert result["start_date"] == expected_date.strftime("%Y-%m-%d")
            assert result["end_date"] == expected_date.strftime("%Y-%m-%d")
    
    @pytest.mark.asyncio
    async def test_generate_weekly_summary_success(
        self, 
        summary_generator: TradingSummaryGenerator,
        sample_detailed_stats: Dict[str, Any]
    ):
        """测试成功生成周报摘要"""
        target_date = datetime(2024, 1, 7)  # 周日
        
        summary_generator.api.get_detailed_statistics.return_value = sample_detailed_stats
        
        result = await summary_generator.generate_weekly_summary(
            target_date=target_date
        )
        
        # 验证API调用
        call_args = summary_generator.api.get_detailed_statistics.call_args[1]
        
        # 验证时间范围（7天）
        expected_end = target_date.replace(hour=23, minute=59, second=59, microsecond=999999)
        expected_start = (target_date - timedelta(days=6)).replace(hour=0, minute=0, second=0, microsecond=0)
        
        assert call_args["start_date"] == expected_start
        assert call_args["end_date"] == expected_end
        
        # 验证返回结果
        assert result["period_type"] == "周"
        assert result["start_date"] == "2024-01-01"
        assert result["end_date"] == "2024-01-07"
    
    @pytest.mark.asyncio
    async def test_generate_custom_summary_with_days(
        self, 
        summary_generator: TradingSummaryGenerator,
        sample_detailed_stats: Dict[str, Any]
    ):
        """测试使用天数生成自定义摘要"""
        summary_generator.api.get_detailed_statistics.return_value = sample_detailed_stats
        
        with patch('utils.trading.statistics.summary_generator.datetime') as mock_datetime:
            mock_now = datetime(2024, 1, 10, 15, 0, 0)
            mock_datetime.now.return_value = mock_now
            mock_datetime.side_effect = lambda *args, **kw: datetime(*args, **kw)
            
            result = await summary_generator.generate_custom_summary(days=3)
            
            # 验证时间范围
            call_args = summary_generator.api.get_detailed_statistics.call_args[1]
            expected_end = mock_now
            expected_start = mock_now - timedelta(days=3)
            
            assert call_args["start_date"] == expected_start
            assert call_args["end_date"] == expected_end
            
            # 验证周期类型
            assert result["period_type"] == "3天"
    
    @pytest.mark.asyncio
    async def test_generate_custom_summary_with_date_range(
        self, 
        summary_generator: TradingSummaryGenerator,
        sample_detailed_stats: Dict[str, Any]
    ):
        """测试使用日期范围生成自定义摘要"""
        summary_generator.api.get_detailed_statistics.return_value = sample_detailed_stats
        
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 1, 5)
        
        result = await summary_generator.generate_custom_summary(
            start_date=start_date,
            end_date=end_date
        )
        
        # 验证API调用
        call_args = summary_generator.api.get_detailed_statistics.call_args[1]
        assert call_args["start_date"] == start_date
        assert call_args["end_date"] == end_date
        
        # 验证周期类型（5天）
        assert result["period_type"] == "5天"
    
    @pytest.mark.asyncio
    async def test_generate_summary_api_failure(
        self, 
        summary_generator: TradingSummaryGenerator
    ):
        """测试API调用失败时的处理"""
        # 配置API返回失败
        failed_response = {
            "success": False,
            "error": "数据库连接失败"
        }
        summary_generator.api.get_detailed_statistics.return_value = failed_response
        
        result = await summary_generator.generate_daily_summary()
        
        # 验证返回空摘要
        assert result["period_type"] == "日"
        assert result["overall_stats"]["total_trades"] == 0
        assert result["top_strategies"] == []
        assert result["extremes"] == {}
        assert "error" in result
    
    @pytest.mark.asyncio
    async def test_generate_summary_exception_handling(
        self, 
        summary_generator: TradingSummaryGenerator
    ):
        """测试异常处理"""
        # 配置API抛出异常
        summary_generator.api.get_detailed_statistics.side_effect = Exception("网络错误")
        
        result = await summary_generator.generate_daily_summary()
        
        # 验证返回空摘要并包含错误信息
        assert result["overall_stats"]["total_trades"] == 0
        assert "error" in result
        assert "网络错误" in result["error"]
    
    def test_extract_overall_stats(self, summary_generator: TradingSummaryGenerator):
        """测试提取总体统计数据"""
        raw_stats = {
            "total_trades": 20,
            "total_win_rate": 75.5,
            "total_profit_rate": 18.7,
            "avg_profit_rate": 9.2,
            "total_profit_amount": 4.5,
            "profitable_trades": 15,
            "loss_trades": 5
        }
        
        result = summary_generator._extract_overall_stats(raw_stats)
        
        assert result["total_trades"] == 20
        assert result["win_rate"] == 75.5
        assert result["total_pnl_rate"] == 18.7
        assert result["avg_pnl_rate"] == 9.2
        assert result["total_pnl_amount"] == 4.5
        assert result["profitable_trades"] == 15
        assert result["loss_trades"] == 5
    
    def test_extract_top_strategies(self, summary_generator: TradingSummaryGenerator):
        """测试提取策略排行榜"""
        strategy_stats = [
            {
                "strategy_name": "strategy_a",
                "trade_count": 10,
                "win_rate": 80.0,
                "avg_profit_rate": 12.0,
                "total_profit_amount": 2.5,
                "profitable_trades": 8,
                "loss_trades": 2
            },
            {
                "strategy_name": "strategy_b",
                "trade_count": 5,
                "win_rate": 60.0,
                "avg_profit_rate": 8.0,
                "total_profit_amount": 1.2,
                "profitable_trades": 3,
                "loss_trades": 2
            },
            {
                "strategy_name": "strategy_c",
                "trade_count": 3,
                "win_rate": 33.3,
                "avg_profit_rate": 2.0,
                "total_profit_amount": 0.5,
                "profitable_trades": 1,
                "loss_trades": 2
            }
        ]
        
        result = summary_generator._extract_top_strategies(strategy_stats, limit=2)
        
        # 验证按总盈利额排序
        assert len(result) == 2
        assert result[0]["strategy"] == "strategy_a"
        assert result[0]["total_pnl_rate"] == 12.0
        assert result[1]["strategy"] == "strategy_b"
        assert result[1]["total_pnl_rate"] == 8.0

    def test_extract_top_strategies_bug_reproduction(self, summary_generator: TradingSummaryGenerator):
        """测试Bug复现: 策略名称字段映射错误
        
        该测试用例使用正确的数据结构（来自StrategyStats模型），验证当前代码
        无法正确提取策略名称的Bug。
        
        Bug描述: _extract_top_strategies方法使用错误的字段名'strategy'而非'strategy_name'
        """
        # 使用正确的数据结构（来自StrategyStats模型的model_dump()）
        strategy_stats = [
            {
                "strategy_name": "胜率高",  # 真实的策略名称，字段名为strategy_name
                "trade_count": 10,
                "win_rate": 80.0,
                "avg_profit_rate": 12.0,
                "total_profit_amount": 2.5,
                "profitable_trades": 8,
                "loss_trades": 2,
                "total_buy_amount": 5.0,
                "total_sell_amount": 7.5,
                "max_single_profit": 0.8,
                "max_single_loss": -0.2
            },
            {
                "strategy_name": "收益率高",  # 真实的策略名称，字段名为strategy_name
                "trade_count": 5,
                "win_rate": 60.0,
                "avg_profit_rate": 8.0,
                "total_profit_amount": 1.2,
                "profitable_trades": 3,
                "loss_trades": 2,
                "total_buy_amount": 3.0,
                "total_sell_amount": 4.2,
                "max_single_profit": 0.5,
                "max_single_loss": -0.3
            }
        ]
        
        result = summary_generator._extract_top_strategies(strategy_stats, limit=2)
        
        # 🚨 Bug验证: 当前代码会显示"未知策略"而非实际策略名称
        # 这个测试在修复前应该失败，因为当前代码使用错误的字段名"strategy"
        assert len(result) == 2
        
        # Bug现象: 由于字段名错误，strategy字段会显示"未知策略"
        # 修复前的错误行为:
        # assert result[0]["strategy"] == "未知策略"  # 错误的结果
        # assert result[1]["strategy"] == "未知策略"  # 错误的结果
        
        # 期望的正确行为（修复后应该通过）:
        assert result[0]["strategy"] == "胜率高", f"期望策略名称为'胜率高'，实际为'{result[0]['strategy']}'"
        assert result[1]["strategy"] == "收益率高", f"期望策略名称为'收益率高'，实际为'{result[1]['strategy']}'"
        
        # 验证其他字段正确映射
        assert result[0]["total_trades"] == 10
        assert result[0]["win_rate"] == 80.0
        assert result[0]["profitable_trades"] == 8
        assert result[0]["loss_trades"] == 2
    
    def test_extract_extremes(self, summary_generator: TradingSummaryGenerator):
        """测试提取极值统计"""
        profit_rankings = [
            {
                "signal_id": "profit_001",
                "profit_rate": 50.0,
                "profit_amount": 2.0,
                "token": "SOL"
            }
        ]
        
        loss_rankings = [
            {
                "signal_id": "loss_001",
                "loss_rate": 25.0,
                "loss_amount": 0.8,
                "token": "BONK"
            }
        ]
        
        result = summary_generator._extract_extremes(profit_rankings, loss_rankings)
        
        assert "max_profit" in result
        assert "max_loss" in result
        
        max_profit = result["max_profit"]
        assert max_profit["pnl_rate"] == 50.0
        assert max_profit["pnl_amount"] == 2.0
        assert max_profit["token"] == "SOL"
        
        max_loss = result["max_loss"]
        assert max_loss["pnl_rate"] == 25.0
        assert max_loss["pnl_amount"] == 0.8
        assert max_loss["token"] == "BONK"
    
    def test_create_empty_summary(self, summary_generator: TradingSummaryGenerator):
        """测试创建空摘要"""
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 1, 1)
        error_msg = "测试错误"
        
        result = summary_generator._create_empty_summary(
            "日", start_date, end_date, error_msg
        )
        
        assert result["period_type"] == "日"
        assert result["start_date"] == "2024-01-01"
        assert result["end_date"] == "2024-01-01"
        assert result["overall_stats"]["total_trades"] == 0
        assert result["top_strategies"] == []
        assert result["extremes"] == {}
        assert result["error"] == error_msg

    def test_extract_top_strategies_profit_rate_bug_reproduction(self, summary_generator: TradingSummaryGenerator):
        """测试Bug复现: 策略盈利率字段映射错误
        
        该测试用例验证当前代码错误地使用total_profit_amount（金额）而非avg_profit_rate（百分比）
        来填充total_pnl_rate字段，导致盈利率显示为接近0的错误值。
        
        Bug描述: _extract_top_strategies方法使用错误的字段名'total_profit_amount'而非'avg_profit_rate'
        Bug现象: 飞书消息卡片中策略盈利率显示为"-0.00%"而非实际盈利率
        """
        # 使用正确的数据结构（来自StrategyStats模型的model_dump()）
        strategy_stats = [
            {
                "strategy_name": "胜率高",
                "trade_count": 7,
                "win_rate": 57.14,
                "avg_profit_rate": 13.20,  # 真实的盈利率百分比数据
                "total_profit_amount": 0.001,  # 很小的SOL金额数据，容易被误用
                "profitable_trades": 4,
                "loss_trades": 3,
                "total_buy_amount": 2.5,
                "total_sell_amount": 2.501,
                "max_single_profit": 0.8,
                "max_single_loss": -0.2
            },
            {
                "strategy_name": "收益率高",
                "trade_count": 17,
                "win_rate": 35.29,
                "avg_profit_rate": 8.50,  # 真实的盈利率百分比数据  
                "total_profit_amount": 0.002,  # 很小的SOL金额数据，容易被误用
                "profitable_trades": 6,
                "loss_trades": 11,
                "total_buy_amount": 5.0,
                "total_sell_amount": 5.002,
                "max_single_profit": 0.5,
                "max_single_loss": -0.3
            }
        ]
        
        result = summary_generator._extract_top_strategies(strategy_stats, limit=2)
        
        # 🚨 Bug验证: 当前代码错误地使用total_profit_amount而非avg_profit_rate
        assert len(result) == 2
        
        # Bug现象: 由于字段名错误，total_pnl_rate字段显示很小的金额数据而非百分比数据
        # 修复前的错误行为（当前代码会这样）:
        # assert result[0]["total_pnl_rate"] == 0.00  # round(0.001, 2) = 0.00，错误！
        # assert result[1]["total_pnl_rate"] == 0.00  # round(0.002, 2) = 0.00，错误！
        
        # 期望的正确行为（修复后应该通过）:
        # 注意：排序按total_profit_amount（0.002 > 0.001），所以"收益率高"排第一
        assert result[0]["total_pnl_rate"] == 8.50, f"期望盈利率为8.50%，实际为{result[0]['total_pnl_rate']}%"
        assert result[1]["total_pnl_rate"] == 13.20, f"期望盈利率为13.20%，实际为{result[1]['total_pnl_rate']}%"
        
        # 验证策略名称正确（之前的Bug已修复）
        assert result[0]["strategy"] == "收益率高"  # 按盈利金额排序第一
        assert result[1]["strategy"] == "胜率高"   # 按盈利金额排序第二
        
        # 验证其他字段正确映射
        assert result[0]["total_trades"] == 17
        assert result[0]["win_rate"] == 35.29
        assert result[1]["total_trades"] == 7
        assert result[1]["win_rate"] == 57.14


class TestSummaryFormatter:
    """摘要格式化器测试类"""
    
    @pytest.fixture
    def sample_summary_data(self) -> Dict[str, Any]:
        """示例摘要数据"""
        return {
            "period_type": "日",
            "start_date": "2024-01-01",
            "end_date": "2024-01-01",
            "overall_stats": {
                "total_trades": 10,
                "win_rate": 70.0,
                "total_pnl_rate": 15.5,
                "avg_pnl_rate": 8.2,
                "total_pnl_amount": 2.5,
                "profitable_trades": 7,
                "loss_trades": 3
            },
            "top_strategies": [
                {
                    "strategy": "gmgn_smart_money",
                    "total_trades": 5,
                    "win_rate": 80.0,
                    "total_pnl_rate": 20.0,
                    "avg_pnl_rate": 12.0
                },
                {
                    "strategy": "manual_trading",
                    "total_trades": 3,
                    "win_rate": 66.7,
                    "total_pnl_rate": 10.0,
                    "avg_pnl_rate": 5.0
                }
            ],
            "extremes": {
                "max_profit": {
                    "pnl_rate": 45.2,
                    "pnl_amount": 1.2,
                    "token": "SOL"
                },
                "max_loss": {
                    "pnl_rate": 15.8,
                    "pnl_amount": 0.3,
                    "token": "BONK"
                }
            }
        }
    
    def test_format_as_text(self, sample_summary_data: Dict[str, Any]):
        """测试格式化为纯文本"""
        result = SummaryFormatter.format_as_text(sample_summary_data)
        
        # 验证包含关键信息
        assert "📊 交易日报" in result
        assert "2024-01-01 ~ 2024-01-01" in result
        assert "总交易次数: 10" in result
        assert "总胜率: 70.00%" in result
        assert "总盈利率: 15.50%" in result
        assert "gmgn_smart_money" in result
        assert "最大盈利: 45.20%" in result
        assert "最大亏损: 15.80%" in result
    
    def test_format_as_markdown(self, sample_summary_data: Dict[str, Any]):
        """测试格式化为Markdown"""
        result = SummaryFormatter.format_as_markdown(sample_summary_data)
        
        # 验证Markdown格式
        assert "# 📊 交易日报" in result
        assert "**📅 时间范围**" in result
        assert "## 📈 总体统计" in result
        assert "- **总交易次数**: 10" in result
        assert "## 🏆 策略排行榜" in result
        assert "1. **gmgn_smart_money**" in result
        assert "## 📊 极值统计" in result
        assert "- **最大盈利**: 45.20%" in result
    
    def test_format_empty_data(self):
        """测试格式化空数据"""
        empty_data = {
            "period_type": "日",
            "start_date": "2024-01-01",
            "end_date": "2024-01-01",
            "overall_stats": {
                "total_trades": 0,
                "win_rate": 0,
                "total_pnl_rate": 0,
                "avg_pnl_rate": 0
            },
            "top_strategies": [],
            "extremes": {}
        }
        
        text_result = SummaryFormatter.format_as_text(empty_data)
        markdown_result = SummaryFormatter.format_as_markdown(empty_data)
        
        # 验证基本结构存在
        assert "📊 交易日报" in text_result
        assert "总交易次数: 0" in text_result
        
        assert "# 📊 交易日报" in markdown_result
        assert "- **总交易次数**: 0" in markdown_result 