from curl_cffi.requests import Session

# 添加异步HTTP客户端支持
import httpx
import asyncio
from curl_cffi import requests as curl_requests
from curl_cffi.requests import AsyncSession


class ProxySession(Session):
    def __init__(self, proxy_url=None, proxy_user=None, proxy_pass=None):
        super().__init__()
        if proxy_url and proxy_user and proxy_pass:
            # 构建带认证的代理URL
            proxy = f'http://{proxy_user}:{proxy_pass}@{proxy_url}'
        elif proxy_url:
            proxy = proxy_url
            
        self.proxies = {
            'http': proxy,
            'https': proxy
        }


class AsyncProxySession(AsyncSession):
    """
    异步代理会话，基于curl_cffi.requests.AsyncSession
    保留了Cloudflare绕过功能
    """
    def __init__(self, proxy_url=None, proxy_user=None, proxy_pass=None, **kwargs):
        """
        初始化异步代理会话
        
        Args:
            proxy_url: 代理服务器URL
            proxy_user: 代理认证用户名
            proxy_pass: 代理认证密码
            **kwargs: 传递给AsyncSession的其他参数
        """
        super().__init__(**kwargs)
        if proxy_url and proxy_user and proxy_pass:
            # 构建带认证的代理URL
            proxy = f'http://{proxy_user}:{proxy_pass}@{proxy_url}'
        elif proxy_url:
            proxy = proxy_url
        else:
            proxy = None
            
        if proxy:
            self.proxies = {
                'http': proxy,
                'https': proxy
            }


class AsyncProxyClient(httpx.AsyncClient):
    """
    异步代理HTTP客户端，基于httpx.AsyncClient
    注意：此客户端不具备绕过Cloudflare的能力
    """
    def __init__(self, proxy_url=None, proxy_user=None, proxy_pass=None, **kwargs):
        """
        初始化异步代理客户端
        
        Args:
            proxy_url: 代理服务器URL
            proxy_user: 代理认证用户名
            proxy_pass: 代理认证密码
            **kwargs: 传递给httpx.AsyncClient的其他参数
        """
        if proxy_url and proxy_user and proxy_pass:
            # 构建带认证的代理URL
            proxy = f'http://{proxy_user}:{proxy_pass}@{proxy_url}'
        elif proxy_url:
            proxy = f'http://{proxy_url}'
        else:
            proxy = None
            
        proxies = None
        if proxy:
            proxies = {
                'http://': proxy,
                'https://': proxy
            }
            
        super().__init__(proxies=proxies, **kwargs)
        

if __name__ == '__main__':
    import asyncio
    async def test():
        session = AsyncProxySession(proxy_url="**************:999")
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate, br"
        }
        response = await session.get("https://ip.me/", headers=headers, verify=False)
        print(response.text)
    asyncio.run(test())
