name: 'GMGN钱包持仓信息监控工作流'
description: '定期获取聪明钱钱包的持仓信息，并将数据存储到数据库'

nodes:
  - name: "WalletHoldingsSchedulerNode"
    node_type: "input"
    interval: 1
    generate_data: workflows.gmgn_wallet_holdings.handler.generate_wallet_list
    flow_control:
      max_pending_messages: 10
      check_interval: 1
      enable_flow_control: true

  - name: "WalletHoldingsMonitorNode"
    node_type: "process"
    depend_ons: ["WalletHoldingsSchedulerNode"]
    concurrency: 1
    interval: 1
    process_item: workflows.gmgn_wallet_holdings.handler.process_wallet_holdings

  - name: "WalletHoldingsStorageNode"
    node_type: "storage"
    depend_ons: ["WalletHoldingsMonitorNode"]
    batch_size: 1000  # 每批处理1000条数据
    store_data: workflows.gmgn_wallet_holdings.handler.store_wallet_holdings_data
    validate: workflows.gmgn_wallet_holdings.handler.validate_wallet_holdings_data 