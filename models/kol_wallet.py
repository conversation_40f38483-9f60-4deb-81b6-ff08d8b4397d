from datetime import datetime
from typing import Optional, List, Dict
from beanie import Document, Indexed
from pydantic import BaseModel, Field

class DailyProfit(BaseModel):
    """每日收益信息"""
    timestamp: int = Field(default=0, description="时间戳")
    profit: float = Field(default=0.0, description="收益")

class RiskMetrics(BaseModel):
    """风险指标"""
    token_active: str = Field(default="0", description="活跃代币数")
    token_honeypot: str = Field(default="0", description="蜜罐代币数")
    token_honeypot_ratio: float = Field(default=0.0, description="蜜罐代币比率")
    no_buy_hold: str = Field(default="0", description="未买入持有数")
    no_buy_hold_ratio: float = Field(default=0.0, description="未买入持有比率")
    sell_pass_buy: str = Field(default="0", description="卖出超过买入数")
    sell_pass_buy_ratio: float = Field(default=0.0, description="卖出超过买入比率")
    fast_tx: str = Field(default="0", description="快速交易数")
    fast_tx_ratio: float = Field(default=0.0, description="快速交易比率")

class KOLWallet(Document):
    """KOL钱包数据模型"""
    # 基本信息
    wallet_address: Indexed(str) = Field(description="钱包地址", unique=True)
    address: Optional[str] = Field(default=None, description="钱包地址(冗余)")
    chain: str = Field(default="solana", description="链名称")
    
    # 用户信息
    twitter_username: Optional[str] = Field(default=None, description="Twitter用户名")
    twitter_name: Optional[str] = Field(default=None, description="Twitter显示名")
    twitter_description: Optional[str] = Field(default=None, description="Twitter描述")
    followers_count: Optional[int] = Field(default=None, description="关注者数量")
    is_blue_verified: Optional[bool] = Field(default=None, description="是否蓝V认证")
    avatar: Optional[str] = Field(default=None, description="头像URL")
    ens: Optional[str] = Field(default=None, description="ENS域名")
    nickname: Optional[str] = Field(default=None, description="昵称")
    name: Optional[str] = Field(default=None, description="名称")
    tag: Optional[str] = Field(default=None, description="标签")
    tags: Optional[List[str]] = Field(default=None, description="标签列表")
    tag_rank: Optional[Dict[str, int | None] | None] = Field(default=None, description="标签排名")
    
    # 余额信息
    balance: Optional[float] = Field(default=None, description="总余额")
    eth_balance: Optional[float] = Field(default=None, description="ETH余额")
    sol_balance: Optional[float] = Field(default=None, description="SOL余额")
    trx_balance: Optional[float] = Field(default=None, description="TRX余额")
    
    # 收益指标
    realized_profit: Optional[float] = Field(default=None, description="总实现收益")
    pnl_1d: Optional[float] = Field(default=None, description="1天收益率")
    pnl_7d: Optional[float] = Field(default=None, description="7天收益率")
    pnl_30d: Optional[float] = Field(default=None, description="30天收益率")
    realized_profit_1d: Optional[float] = Field(default=None, description="1天实现收益")
    realized_profit_7d: Optional[float] = Field(default=None, description="7天实现收益")
    realized_profit_30d: Optional[float] = Field(default=None, description="30天实现收益")
    
    # 交易指标
    txs: Optional[int] = Field(default=None, description="总交易次数")
    txs_30d: Optional[int] = Field(default=None, description="30天交易次数")
    buy: Optional[int] = Field(default=None, description="总买入次数")
    sell: Optional[int] = Field(default=None, description="总卖出次数")
    buy_30d: Optional[int] = Field(default=None, description="30天买入次数")
    sell_30d: Optional[int] = Field(default=None, description="30天卖出次数")
    avg_hold_time: Optional[float] = Field(default=None, description="平均持有时间")
    token_num_7d: Optional[int] = Field(default=None, description="7天交易代币数")
    avg_holding_period_7d: Optional[float] = Field(default=None, description="7天平均持仓时间")
    
    # 胜率和成本
    winrate_7d: Optional[float] = Field(default=None, description="7天胜率")
    avg_cost_7d: Optional[float] = Field(default=None, description="7天平均买入成本")
    
    # 收益分布
    pnl_lt_minus_dot5_num_7d: Optional[int] = Field(default=None, description="7天内收益率<-0.5的次数")
    pnl_minus_dot5_0x_num_7d: Optional[int] = Field(default=None, description="7天内-0.5<=收益率<0的次数")
    pnl_lt_2x_num_7d: Optional[int] = Field(default=None, description="7天内0<=收益率<2的次数")
    pnl_2x_5x_num_7d: Optional[int] = Field(default=None, description="7天内2<=收益率<5的次数")
    pnl_gt_5x_num_7d: Optional[int] = Field(default=None, description="7天内收益率>=5的次数")
    
    # 收益分布比率
    pnl_lt_minus_dot5_num_7d_ratio: Optional[float] = Field(default=None, description="7天内收益率<-0.5的比率")
    pnl_minus_dot5_0x_num_7d_ratio: Optional[float] = Field(default=None, description="7天内-0.5<=收益率<0的比率")
    pnl_lt_2x_num_7d_ratio: Optional[float] = Field(default=None, description="7天内0<=收益率<2的比率")
    pnl_2x_5x_num_7d_ratio: Optional[float] = Field(default=None, description="7天内2<=收益率<5的比率")
    pnl_gt_5x_num_7d_ratio: Optional[float] = Field(default=None, description="7天内收益率>=5的比率")
    
    # 最近购买的代币
    recent_buy_tokens: Optional[List[str]] = Field(default=None, description="最近购买的代币")
    
    # 每日收益
    daily_profit_7d: Optional[List[DailyProfit]] = Field(default=None, description="7天每日收益")
    
    # 风险指标
    risk: Optional[RiskMetrics] = Field(default=None, description="风险指标")
    
    # 数据来源标记
    imported_from_following: Optional[bool] = Field(default=None, description="是否从关注列表导入的KOL数据")
    
    # 时间信息
    last_active: Optional[datetime] = Field(default=None, description="最近活跃时间")
    first_seen_at: Optional[datetime] = Field(default=None, description="首次发现时间")
    last_updated_at: Optional[datetime] = Field(
        default=None,
        description="最后更新时间"
    )
    
    class Settings:
        name = "kol_wallets"
        
    class Config:
        json_schema_extra = {
            "example": {
                "wallet_address": "HLLXwFZN9CHTct5K4YpucZ137aji27EkkJ1ZaZE7JVmk",
                "address": "HLLXwFZN9CHTct5K4YpucZ137aji27EkkJ1ZaZE7JVmk",
                "chain": "solana",
                "twitter_username": "NFTDEFILAND",
                "twitter_name": "McBobo",
                "twitter_description": "meme connoisseur. use your voice",
                "followers_count": 33129,
                "is_blue_verified": True,
                "avatar": "https://pbs.twimg.com/profile_images/1841541844703793152/KoN6ssOu_normal.jpg",
                "name": "McBobo",
                "tags": ["kol"],
                "tag_rank": {"kol": 209},
                "balance": 95.473270973,
                "eth_balance": 95.473270973,
                "sol_balance": 95.473270973,
                "trx_balance": 95.473270973,
                "realized_profit": 9944.699539260751,
                "pnl_1d": 0.4326881898847883,
                "pnl_7d": 0.08670517369194986,
                "pnl_30d": 0.02999748426593784,
                "realized_profit_1d": 6202.32041328575,
                "realized_profit_7d": 7732.20707324593,
                "realized_profit_30d": 9944.699539260751,
                "txs": 661,
                "txs_30d": 2593,
                "buy": 425,
                "sell": 236,
                "buy_30d": 1672,
                "sell_30d": 921,
                "token_num_7d": 107,
                "avg_holding_period_7d": 8066.34,
                "winrate_7d": 0.49523809523809526,
                "avg_cost_7d": 870.9481982323836,
                "last_active": "2024-02-24T00:00:00",
                "first_seen_at": "2024-02-24T00:00:00",
                "last_updated_at": "2024-02-24T00:00:00"
            }
        } 