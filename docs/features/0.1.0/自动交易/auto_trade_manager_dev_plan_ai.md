# 统一自动交易管理器技术实现方案

**创建日期**: 2025-05-25  
**版本**: 0.1.0  
**目标**: 详细的技术实现方案和架构设计

## 1. 整体架构设计

### 1.1 核心组件

```
AutoTradeManager (核心管理器)
├── ChannelRegistry (渠道注册表)
├── ChannelSelector (渠道选择器)
├── TradeOrchestrator (交易编排器)
├── TradeRecordManager (交易记录管理器)
└── ConfigManager (配置管理器)
```

### 1.2 数据流设计

```
策略触发 → AutoTradeManager.execute_trade()
    ↓
配置加载与渠道筛选
    ↓
按优先级依次尝试渠道
    ↓
记录所有尝试结果
    ↓
返回最终执行结果
```

## 2. 核心类设计

### 2.1 AutoTradeManager (主管理器)

**职责**: 统一交易入口，协调各个组件完成交易

**主要方法**:
- `execute_trade()`: 执行交易的主入口
- `get_available_channels()`: 获取可用渠道
- `health_check()`: 系统健康检查

**关键接口设计**:
```python
async def execute_trade(
    self, 
    trade_type: TradeType,
    token_in_address: str,
    token_out_address: str,
    amount: Optional[float] = None,  # 如果不提供，使用全局默认值
    # 策略级别的钱包配置覆盖（可选，如果不提供则使用全局默认）
    wallet_private_key_env_var: Optional[str] = None,
    wallet_address: Optional[str] = None,
    # 策略级别的交易参数覆盖（可选）
    strategy_trading_overrides: Optional[Dict[str, Any]] = None,
    # 其他参数
    signal_id: Optional[PydanticObjectId] = None,
    strategy_name: Optional[str] = None
) -> TradeExecutionResult:
    # 1. 验证输入参数
    # 2. 加载全局配置（包含钱包和交易参数）
    # 3. 合并策略级别钱包和交易参数覆盖
    # 4. 获取可用渠道列表
    # 5. 创建初始交易记录
    # 6. 按优先级尝试不同渠道，使用合并后的参数
    # 7. 更新最终交易记录
    # 8. 发送通知（如果需要）
    # 9. 返回执行结果
```

### 2.2 ChannelRegistry (渠道注册表)

**职责**: 管理所有可用的交易渠道实例

**主要方法**:
- `register_channel()`: 注册新渠道
- `get_channel()`: 获取指定渠道实例
- `list_channels()`: 列出所有渠道
- `remove_channel()`: 移除渠道

**关键属性**:
```python
class ChannelRegistry:
    _channels: Dict[str, TradeInterface] = {}
    _channel_configs: Dict[str, TradeChannelConfig] = {}
```

### 2.3 ChannelSelector (渠道选择器)

**职责**: 根据配置和状态选择合适的交易渠道

**主要方法**:
- `select_channels()`: 选择可用渠道并排序
- `is_channel_available()`: 检查渠道是否可用
- `get_next_channel()`: 获取下一个重试渠道

**选择逻辑**:
```python
def select_channels(self, trade_request):
    # 1. 过滤启用的渠道
    # 2. 检查渠道健康状态
    # 3. 按优先级排序
    # 4. 检查渠道兼容性（支持的代币等）
    # 5. 返回排序后的渠道列表
```

### 2.4 TradeOrchestrator (交易编排器)

**职责**: 协调具体的交易执行过程，包括重试和故障转移

**主要方法**:
- `execute_with_fallback()`: 带故障转移的交易执行
- `try_channel()`: 尝试使用指定渠道交易
- `handle_channel_failure()`: 处理渠道失败

**执行逻辑**:
```python
async def execute_with_fallback(self, channels, trade_request):
    for channel in channels:
        try:
            result = await self.try_channel(channel, trade_request)
            if result.status == TradeStatus.SUCCESS:
                return result
        except Exception as e:
            # 记录失败，继续下一个渠道
            continue
    # 所有渠道都失败
    return failure_result
```

### 2.5 TradeRecordManager (交易记录管理器)

**职责**: 管理交易记录的创建、更新和查询

**主要方法**:
- `create_trade_record()`: 创建新的交易记录
- `update_trade_record()`: 更新交易记录
- `add_channel_attempt()`: 添加渠道尝试记录

**记录结构**:
```python
# 扩展现有的 TradeRecord 模型
class EnhancedTradeRecord(TradeRecord):
    channel_attempts: List[ChannelAttemptRecord] = []
    total_execution_time: Optional[float] = None
    auto_trade_manager_version: str = "1.0"
```

## 3. 新增数据模型

### 3.1 配置相关模型 (在 models/config.py 中定义)

```python
# 这些配置类将直接添加到 models/config.py 文件中，与其他配置类保持一致

class WalletConfig(BaseModel):
    """钱包配置 - 全局默认钱包配置"""
    default_private_key_env_var: str = Field(..., description="默认钱包私钥环境变量名")
    default_wallet_address: str = Field(..., description="默认钱包地址")

class TradingParams(BaseModel):
    """交易参数配置 - 渠道级别的默认参数"""
    default_buy_amount_sol: float = Field(default=0.01, description="默认每笔买入交易花费的SOL数量")
    default_buy_slippage_percentage: float = Field(default=1.0, description="默认买入滑点百分比")
    default_buy_priority_fee_sol: float = Field(default=0.00005, description="默认买入优先费（SOL）")
    default_sell_slippage_percentage: float = Field(default=1.0, description="默认卖出滑点百分比")
    default_sell_priority_fee_sol: float = Field(default=0.00005, description="默认卖出优先费（SOL）")

class TradeChannelConfig(BaseModel):
    """交易渠道配置 - 不包含钱包信息，钱包由策略级别提供"""
    channel_type: str = Field(..., description="渠道类型：gmgn, solana_direct")
    priority: int = Field(..., description="优先级，数字越小越优先")
    enabled: bool = Field(default=True, description="是否启用")
    timeout_seconds: int = Field(default=30, description="超时时间")
    max_retries: int = Field(default=3, description="最大重试次数")
    
    # 渠道级别的默认交易参数（可被策略级别参数覆盖）
    trading_params: TradingParams = Field(default_factory=TradingParams, description="渠道默认交易参数")
    
    # 渠道特定参数
    channel_params: Dict[str, Any] = Field(default_factory=dict, description="渠道特定参数（如API Host等）")

class NotificationConfig(BaseModel):
    """通知配置"""
    notify_on_failure: bool = Field(default=True, description="交易失败时是否通知")
    notify_on_fallback: bool = Field(default=True, description="渠道故障转移时是否通知")
    admin_chat_ids: List[str] = Field(default_factory=list, description="管理员Telegram Chat ID列表")
    include_trade_details: bool = Field(default=True, description="是否在通知中包含交易详情")

class AutoTradeConfig(BaseModel):
    """自动交易全局配置"""
    enabled: bool = Field(default=True, description="是否启用自动交易")
    wallet_config: WalletConfig = Field(..., description="全局默认钱包配置")
    channels: List[TradeChannelConfig] = Field(..., description="交易渠道配置列表")
    default_timeout: int = Field(default=60, description="默认总超时时间")
    max_total_retries: int = Field(default=5, description="所有渠道最大重试次数")
    notification_config: NotificationConfig = Field(default_factory=NotificationConfig, description="通知配置")
```

### 3.2 执行结果模型

```python
class ChannelAttemptResult(BaseModel):
    """单个渠道的尝试结果"""
    channel_type: str
    attempt_number: int
    status: TradeStatus
    tx_hash: Optional[str] = None
    error_message: Optional[str] = None
    execution_time: float
    started_at: datetime
    completed_at: Optional[datetime] = None

class TradeExecutionResult(BaseModel):
    """最终交易执行结果"""
    final_status: TradeStatus
    successful_channel: Optional[str] = None
    final_trade_record_id: PydanticObjectId
    channel_attempts: List[ChannelAttemptResult] = []
    total_execution_time: float
    error_summary: Optional[str] = None
    started_at: datetime
    completed_at: datetime
```

## 4. 配置系统设计

### 4.1 数据库配置存储

AutoTradeManagerConfig 将作为动态配置存储在数据库的 `config` 集合中，配置类型为 `"auto_trade_manager"`。

```python
# 在 models/config.py 中新增
class AutoTradeManagerConfig(BaseModel):
    """AutoTradeManager配置，存储在数据库config集合中"""
    auto_trade: AutoTradeConfig

# 在 Config 类的 Union 中添加 AutoTradeManagerConfig
data: Union[
    KolActivityConfig, 
    SingleKolStrategyConfig, 
    AutoTradeManagerConfig,  # 新增
    IdataRiverSpiderConfig, 
    UtoolsConfig, 
    GmgnApiConfig, 
    ApplicationConfig, 
    Dict[str, Any]
] = Field(description="配置数据，根据type字段确定具体类型")

# 同时在 from_api_data 和 update_config 方法中添加对应的处理逻辑
```

### 4.2 配置管理流程

```python
# 数据库中的配置文档结构
{
    "_id": ObjectId("..."),
    "type": "auto_trade_manager",
    "version": 1,
    "created_at": "2025-05-25T12:00:00Z",
    "updated_at": "2025-05-25T12:00:00Z",
    "data": {
        "auto_trade": {
            "enabled": True,
            "channels": [...],
            "default_timeout": 60,
            "max_total_retries": 5,
            "notification_config": {...}
        }
    },
    "description": "自动交易管理器配置"
}
```

### 4.3 配置示例

```python
# 通过 ConfigDAO 操作的完整配置示例
from dao.config_dao import ConfigDAO
from models.config import (
    Config, AutoTradeManagerConfig, AutoTradeConfig,
    TradeChannelConfig, TradingParams, NotificationConfig
)

# 创建配置
auto_trade_config_data = AutoTradeManagerConfig(
    auto_trade=AutoTradeConfig(
        enabled=True,
        wallet_config=WalletConfig(
            default_private_key_env_var="DEFAULT_WALLET_PRIVATE_KEY",
            default_wallet_address="DapiQqsGjomQeKEbyvHePgGHEyLEU8XYt8VBTvaVdBn6"
        ),
        channels=[
            TradeChannelConfig(
                channel_type="gmgn",
                priority=1,
                enabled=True,
                timeout_seconds=30,
                max_retries=2,
                trading_params=TradingParams(
                    default_buy_amount_sol=0.01,
                    default_buy_slippage_percentage=1.0,
                    default_buy_priority_fee_sol=0.00005,
                    default_sell_slippage_percentage=1.5,
                    default_sell_priority_fee_sol=0.00008
                ),
                channel_params={
                    "api_host": "https://gmgn.ai/defi/v2"
                }
            ),
            TradeChannelConfig(
                channel_type="solana_direct",
                priority=2,
                enabled=True,
                timeout_seconds=45,
                max_retries=1,
                trading_params=TradingParams(
                    default_buy_amount_sol=0.015,  # 不同渠道可以有不同的默认参数
                    default_buy_slippage_percentage=1.2,
                    default_buy_priority_fee_sol=0.00006,
                    default_sell_slippage_percentage=1.8,
                    default_sell_priority_fee_sol=0.0001
                ),
                channel_params={
                    "rpc_endpoint": "https://api.mainnet-beta.solana.com",
                    "commitment": "confirmed",
                    "jupiter_api_host": "https://quote-api.jup.ag"
                }
            )
        ],
        default_timeout=60,
        max_total_retries=5,
        notification_config=NotificationConfig(
            notify_on_failure=True,
            notify_on_fallback=True,
            admin_chat_ids=["123456789", "987654321"],
            include_trade_details=True
        )
    )
)

# 钱包配置仍然保留在策略级别（重命名后）
# 例如在 SingleKolStrategyConfig 中：
# wallet_private_key_env_var: "STRATEGY_A_WALLET_PK"  # 策略A使用的钱包
# wallet_address: "DapiQqsGjomQeKEbyvHePgGHEyLEU8XYt8VBTvaVdBn6"
# 
# 交易参数在全局配置中有默认值，策略级别可以选择性覆盖

# 保存到数据库
config_dao = ConfigDAO()
config_doc = Config(
    type="auto_trade_manager",
    data=auto_trade_config_data,
    description="自动交易管理器全局配置"
)
await config_doc.save()
```

## 5. 文件结构设计

### 5.1 新增文件

```
utils/trading/
├── auto_trade_manager.py          # 主管理器
├── channel_registry.py            # 渠道注册表  
├── channel_selector.py            # 渠道选择器
├── trade_orchestrator.py          # 交易编排器
├── trade_record_manager.py        # 交易记录管理器
└── config_manager.py              # 配置管理器

models/                             # 数据模型定义 (项目根目录)
├── trade_execution.py             # 交易执行结果模型
└── channel_attempt.py             # 渠道尝试结果模型
```

### 5.2 修改文件

```
utils/trading/__init__.py                  # 添加新组件的导出
utils/trading/solana/__init__.py           # 更新导出
models/__init__.py                         # 注册新的数据模型
models/config.py                           # 添加自动交易配置类 + 清理SingleKolStrategyConfig
models/trade_record.py                     # 扩展TradeRecord模型
workflows/monitor_kol_activity/handler.py  # 简化交易逻辑
```

## 6. 关键实现逻辑

### 6.1 渠道故障转移

```python
async def execute_with_fallback(self, trade_request):
    channels = await self.channel_selector.select_channels(trade_request)
    execution_result = TradeExecutionResult(...)
    
    for channel_config in channels:
        attempt_start = datetime.now()
        attempt_result = ChannelAttemptResult(
            channel_type=channel_config.channel_type,
            attempt_number=len(execution_result.channel_attempts) + 1,
            started_at=attempt_start
        )
        
        try:
            # 获取渠道实例
            channel = self.channel_registry.get_channel(channel_config.channel_type)
            
            # 执行交易
            trade_result = await asyncio.wait_for(
                channel.execute_trade(...),
                timeout=channel_config.timeout_seconds
            )
            
            # 更新尝试结果
            attempt_result.status = trade_result.status
            attempt_result.tx_hash = trade_result.tx_hash
            attempt_result.execution_time = (datetime.now() - attempt_start).total_seconds()
            attempt_result.completed_at = datetime.now()
            
            execution_result.channel_attempts.append(attempt_result)
            
            # 如果成功，更新最终结果并返回
            if trade_result.status == TradeStatus.SUCCESS:
                execution_result.final_status = TradeStatus.SUCCESS
                execution_result.successful_channel = channel_config.channel_type
                return execution_result
                
        except asyncio.TimeoutError:
            attempt_result.status = TradeStatus.FAILED
            attempt_result.error_message = f"渠道超时 ({channel_config.timeout_seconds}s)"
        except Exception as e:
            attempt_result.status = TradeStatus.FAILED
            attempt_result.error_message = str(e)
        finally:
            if not attempt_result.completed_at:
                attempt_result.completed_at = datetime.now()
                attempt_result.execution_time = (attempt_result.completed_at - attempt_start).total_seconds()
            execution_result.channel_attempts.append(attempt_result)
    
    # 所有渠道都失败
    execution_result.final_status = TradeStatus.FAILED
    execution_result.error_summary = "所有配置的交易渠道都失败"
    return execution_result
```

### 6.2 配置动态加载

```python
from dao.config_dao import ConfigDAO
from models.config import AutoTradeManagerConfig, AutoTradeConfig

class ConfigManager:
    def __init__(self):
        self._config_cache = None
        self._last_reload = None
        self._reload_interval = 300  # 5分钟
        self._config_dao = ConfigDAO()
    
    async def get_config(self) -> AutoTradeConfig:
        """获取配置，支持缓存和定期刷新"""
        now = datetime.now()
        if (self._config_cache is None or 
            self._last_reload is None or 
            (now - self._last_reload).total_seconds() > self._reload_interval):
            
            await self._reload_config()
        
        return self._config_cache
    
    async def _reload_config(self):
        """从数据库重新加载配置"""
        try:
            config_doc = await self._config_dao.get_config("auto_trade_manager")
            
            if config_doc and isinstance(config_doc.data, AutoTradeManagerConfig):
                self._config_cache = config_doc.data.auto_trade
                self._last_reload = datetime.now()
                logger.info(f"已从数据库加载auto_trade_manager配置，版本: {config_doc.version}")
            else:
                logger.warning("数据库中未找到auto_trade_manager配置，使用默认配置")
                self._config_cache = self._get_default_config()
                self._last_reload = datetime.now()
        except Exception as e:
            logger.error(f"加载auto_trade_manager配置失败: {e}，使用默认配置")
            self._config_cache = self._get_default_config()
            self._last_reload = datetime.now()
    
    async def update_config(self, new_config_data: dict) -> bool:
        """动态更新配置"""
        try:
            config_doc = await self._config_dao.get_config("auto_trade_manager")
            if config_doc:
                await config_doc.update_config(new_config_data)
                # 清除缓存，强制重新加载
                self._config_cache = None
                await self.get_config()
                logger.info("auto_trade_manager配置已更新")
                return True
            else:
                logger.error("未找到要更新的配置")
                return False
        except Exception as e:
            logger.error(f"更新配置失败: {e}")
            return False
    
    def _get_default_config(self) -> AutoTradeConfig:
        """获取默认配置"""
        return AutoTradeConfig(
            enabled=False,  # 默认禁用
            channels=[],
            default_timeout=60,
            max_total_retries=3,
            notification_config={}
        )
```

## 7. 错误处理策略

### 7.1 异常分类

- **可重试错误**: 网络超时、临时服务不可用
- **不可重试错误**: 余额不足、代币不存在、配置错误
- **渠道特定错误**: 根据不同渠道的错误码进行分类

### 7.2 重试策略

```python
class RetryPolicy:
    @staticmethod
    def should_retry(error: Exception, attempt_count: int, max_retries: int) -> bool:
        if attempt_count >= max_retries:
            return False
            
        # 根据错误类型决定是否重试
        if isinstance(error, (asyncio.TimeoutError, aiohttp.ClientError)):
            return True
        elif "insufficient funds" in str(error).lower():
            return False  # 余额不足不重试
        elif "token not found" in str(error).lower():
            return False  # 代币不存在不重试
        
        return True  # 其他错误默认重试
```

## 8. 性能优化考虑

### 8.1 并发控制

- 使用 `asyncio.Semaphore` 限制并发交易数量
- 为每个渠道独立配置并发限制

### 8.2 连接池管理

- 复用 HTTP 连接池
- 合理配置连接超时和保持活跃

### 8.3 内存管理

- 及时清理交易历史记录
- 使用弱引用避免内存泄漏

## 9. 配置重构和迁移

### 9.1 SingleKolStrategyConfig 清理

**需要移除的交易相关配置字段及其在新配置中的映射**:
```python
# 字段重命名和重新分配：

# 钱包配置（全局默认，策略级别可覆盖）
- gmgn_private_key_env_var             → WalletConfig.default_private_key_env_var (全局默认)
                                       → wallet_private_key_env_var (策略可覆盖)
- gmgn_sol_wallet_address              → WalletConfig.default_wallet_address (全局默认)
                                       → wallet_address (策略可覆盖)
# 原因：大部分策略使用统一钱包，特殊策略（私募场景）可以覆盖使用专用钱包

# 需要移除并映射到新配置的字段：

# 全局交易开关
- auto_trade_enabled                    → AutoTradeConfig.enabled
- trade_provider                        → 通过渠道优先级体现
- include_trade_details_in_notification → NotificationConfig.include_trade_details

# 交易参数配置（移到全局配置作为默认值，策略级别可选覆盖）
- gmgn_auto_trade_buy_amount_sol       → TradingParams.default_buy_amount_sol (策略可覆盖)
- gmgn_buy_slippage_percentage         → TradingParams.default_buy_slippage_percentage (策略可覆盖)
- gmgn_buy_priority_fee                → TradingParams.default_buy_priority_fee_sol (策略可覆盖)
- gmgn_sell_slippage_percentage        → TradingParams.default_sell_slippage_percentage (策略可覆盖)
- gmgn_sell_priority_fee               → TradingParams.default_sell_priority_fee_sol (策略可覆盖)

# GMGN 渠道特定配置
- gmgn_api_host                        → TradeChannelConfig.channel_params["api_host"]
- gmgn_max_retries                     → TradeChannelConfig.max_retries
- gmgn_retry_delay_seconds             → TradeChannelConfig.channel_params["retry_delay_seconds"]
- gmgn_enable_auto_sell                → 不再需要，统一由卖出信号触发

# Solana Direct 渠道配置
- use_direct_solana_trading            → 通过渠道配置和优先级体现
- jupiter_api_host                     → TradeChannelConfig.channel_params["jupiter_api_host"]
- solana_rpc_url                       → TradeChannelConfig.channel_params["rpc_endpoint"]
- max_slippage_bps                     → 对应渠道默认值，可被策略参数覆盖
- priority_fee_lamports                → 对应渠道默认值，可被策略参数覆盖
- sell_max_slippage_bps               → 对应渠道默认值，可被策略参数覆盖
- sell_priority_fee_lamports          → 对应渠道默认值，可被策略参数覆盖
- solana_trade_timeout_seconds        → TradeChannelConfig.timeout_seconds
- http_timeout_seconds                → TradeChannelConfig.channel_params["http_timeout_seconds"]
```

**保留的策略相关配置字段**:
```python
# 以下字段保留在 SingleKolStrategyConfig 中：
- strategy_name
- transaction_lookback_hours
- transaction_min_amount
- kol_account_min_count
- token_mint_lookback_hours
- kol_account_min_txs
- kol_account_max_txs
- sell_strategy_hours
- sell_kol_ratio
- same_token_notification_interval
- is_active

# 钱包配置字段（可选覆盖，重命名）
- wallet_private_key_env_var  # 可选，覆盖全局 default_private_key_env_var
- wallet_address              # 可选，覆盖全局 default_wallet_address

# 可选的交易参数覆盖字段（如果策略需要不同于全局默认值）
- buy_amount_sol              # 可选，覆盖全局 default_buy_amount_sol
- buy_slippage_percentage     # 可选，覆盖全局 default_buy_slippage_percentage  
- buy_priority_fee_sol        # 可选，覆盖全局 default_buy_priority_fee_sol
- sell_slippage_percentage    # 可选，覆盖全局 default_sell_slippage_percentage
- sell_priority_fee_sol       # 可选，覆盖全局 default_sell_priority_fee_sol
```

### 9.2 向后兼容处理

```python
# 在现有 handler.py 中保留兼容层
async def _legacy_trade_execution(strategy_snapshot, ...):
    """Legacy trade execution for backward compatibility"""
    # 将旧的配置转换为新的AutoTradeManager调用
    auto_trade_manager = AutoTradeManager()
    return await auto_trade_manager.execute_trade(...)
```

### 9.3 配置迁移

- 检测旧配置格式并自动转换
- 提供配置验证工具
- 记录迁移过程和结果

## 10. 简化的调用接口

### 10.1 策略级别调用示例

```python
# 场景1：使用全局默认配置（钱包和交易参数）
result = await auto_trade_manager.execute_trade(
    trade_type=TradeType.BUY,
    token_in_address="So11111111111111111111111111111111111111112",
    token_out_address="target_token_address",
    # 所有参数都使用全局默认值
    signal_id=signal_id,
    strategy_name=strategy_config.strategy_name
)

# 场景2：策略覆盖钱包配置（私募场景）
result = await auto_trade_manager.execute_trade(
    trade_type=TradeType.BUY,
    token_in_address="So11111111111111111111111111111111111111112",
    token_out_address="target_token_address",
    # 覆盖钱包配置，使用专用钱包
    wallet_private_key_env_var=strategy_config.wallet_private_key_env_var,
    wallet_address=strategy_config.wallet_address,
    signal_id=signal_id,
    strategy_name=strategy_config.strategy_name
)

# 场景3：策略覆盖交易参数和钱包配置
strategy_overrides = {}
if strategy_config.buy_amount_sol:
    strategy_overrides["buy_amount_sol"] = strategy_config.buy_amount_sol
if strategy_config.buy_slippage_percentage:
    strategy_overrides["buy_slippage_percentage"] = strategy_config.buy_slippage_percentage

result = await auto_trade_manager.execute_trade(
    trade_type=TradeType.BUY,
    token_in_address="So11111111111111111111111111111111111111112",
    token_out_address="target_token_address",
    amount=strategy_config.buy_amount_sol,  # 覆盖交易金额
    wallet_private_key_env_var=strategy_config.wallet_private_key_env_var,  # 覆盖钱包
    wallet_address=strategy_config.wallet_address,
    strategy_trading_overrides=strategy_overrides,  # 覆盖其他交易参数
    signal_id=signal_id,
    strategy_name=strategy_config.strategy_name
)
```

### 10.2 配置重命名和层级对照表

| 原字段名 | 新字段名 | 全局默认配置 | 策略级别覆盖 |
|---------|---------|-------------|-------------|
| `gmgn_private_key_env_var` | `default_private_key_env_var` | ✅ 默认值 | `wallet_private_key_env_var` (可选覆盖) |
| `gmgn_sol_wallet_address` | `default_wallet_address` | ✅ 默认值 | `wallet_address` (可选覆盖) |
| `gmgn_auto_trade_buy_amount_sol` | `default_buy_amount_sol` | ✅ 默认值 | `buy_amount_sol` (可选覆盖) |
| `gmgn_buy_slippage_percentage` | `default_buy_slippage_percentage` | ✅ 默认值 | `buy_slippage_percentage` (可选覆盖) |
| `gmgn_buy_priority_fee` | `default_buy_priority_fee_sol` | ✅ 默认值 | `buy_priority_fee_sol` (可选覆盖) |
| `gmgn_sell_slippage_percentage` | `default_sell_slippage_percentage` | ✅ 默认值 | `sell_slippage_percentage` (可选覆盖) |
| `gmgn_sell_priority_fee` | `default_sell_priority_fee_sol` | ✅ 默认值 | `sell_priority_fee_sol` (可选覆盖) |

### 10.3 参数覆盖优先级

**钱包配置优先级**:
1. **策略级别钱包覆盖** (最高优先级)
2. **全局默认钱包配置** (最低优先级)

**交易参数优先级**:
1. **策略级别交易参数覆盖** (最高优先级)
2. **渠道级别默认参数** (中等优先级)  
3. **全局默认参数** (最低优先级)

### 10.4 配置优势

这样的设计具有以下优势：

1. **最大化复用**: 大部分策略不需要任何额外配置，直接使用全局默认值
2. **灵活应对特殊场景**: 私募场景可以为不同客户群体配置专用钱包
3. **渐进式配置**: 策略可以选择性地覆盖需要的参数，不需要的保持默认
4. **配置一致性**: 钱包和交易参数都采用相同的覆盖机制
5. **易于维护**: 全局参数集中管理，便于调优和维护