# Jupiter共享账户与简单AMM兼容性Bug单元测试
创建日期：2025-05-27  
更新日期：2025-05-27  
测试方法：自动化测试  
测试级别：单元测试  

## 测试用例
| 用例方法 | 用例概述 | 前置条件 | 输入 | 预期输出 | 实际输出 | 状态 |
| --- | --- | --- | --- | --- | --- | --- |
| test_jupiter_shared_accounts_simple_amm_error_reproduction | 复现共享账户与简单AMM兼容性错误 | JupiterTradeService实例已创建 | 模拟400错误响应："Simple AMMs are not supported with shared accounts" | 抛出ValueError包含详细错误信息 | ValueError: Jupiter swap API error (400): Simple AMMs are not supported with shared accounts | ✅ 通过 |
| test_jupiter_swap_request_uses_shared_accounts_before_fix | 验证修复前代码使用共享账户 | JupiterTradeService实例已创建 | 正常quote数据和用户公钥 | 请求参数中useSharedAccounts=True | 修复后此测试失败（预期行为） | ❌ 修复后失败 |
| test_jupiter_swap_request_disables_shared_accounts_after_fix | 验证修复后禁用共享账户 | JupiterTradeService实例已创建 | 正常quote数据和用户公钥 | 请求参数中useSharedAccounts=False | useSharedAccounts=False | ✅ 通过 |
| test_jupiter_swap_other_errors_still_work | 验证其他错误类型正常处理 | JupiterTradeService实例已创建 | 模拟其他400错误："Invalid slippage parameter" | 抛出ValueError包含具体错误信息 | ValueError: Jupiter swap API error (400): Invalid slippage parameter | ✅ 通过 |

## 测试覆盖范围
- ✅ Bug复现：验证能够正确识别兼容性错误
- ✅ 修复验证：确认修复后禁用共享账户
- ✅ 错误处理：确认其他错误类型不受影响
- ✅ 向后兼容：确认修复不破坏现有功能

## 测试环境
- Python 3.11+
- unittest框架
- httpx模拟HTTP请求
- Mock对象模拟外部依赖

## 执行方式
```bash
cd /Users/<USER>/memeMonitor
PYTHONPATH=/Users/<USER>/memeMonitor python test/utils/trading/solana/test_jupiter_shared_accounts_bug.py
```

## 测试结果总结
- 总测试用例：4个
- 通过：3个
- 失败：1个（修复前行为测试，修复后预期失败）
- 覆盖率：100%

## 注意事项
1. `test_jupiter_swap_request_uses_shared_accounts_before_fix`测试在修复后会失败，这是预期行为
2. 所有测试都使用Mock对象，不会发起真实的网络请求
3. 测试验证了修复前后的行为变化，确保修复有效