name: 'GMGN代币链接工作流'
description: '从数据库获取代币列表，爬取每个代币的链接信息，并将链接信息存储到数据库'

nodes:
  - name: "TokenFetchNode"
    node_type: "input"
    interval: 30  # 秒
    generate_data: workflows.gmgn_token_link.handler.generate_data
    flow_control:
      max_pending_messages: 100
      check_interval: 10
      enable_flow_control: true

  - name: "TokenLinksSpiderNode"
    node_type: "process"
    depend_ons: ["TokenFetchNode"]
    concurrency: 1
    interval: 1
    process_item: workflows.gmgn_token_link.handler.process_item

  - name: "TokenLinksStorageNode"
    node_type: "storage"
    depend_ons: ["TokenLinksSpiderNode"]
    batch_size: 10  # 每批处理10条数据
    store_data: workflows.gmgn_token_link.handler.store_data
    validate: workflows.gmgn_token_link.handler.validate 
