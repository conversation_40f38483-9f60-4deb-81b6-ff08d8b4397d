# KOL监控Handler交易金额显示修复单元测试

创建日期：2025-05-26
更新日期：2025-05-26
测试方法：自动化测试
测试级别：单元测试

## 测试概述

本测试文件专门测试修复后的KOL监控Handler中交易金额显示功能，确保Telegram消息中的`Amount Bought`和`Amount Spent`字段能够正确显示实际的交易数据，而不是显示"N/A"。

## 修复内容

修复了`workflows/monitor_kol_activity/handler.py`中的以下问题：
1. **Amount Bought显示N/A**: 原代码硬编码了"N/A"值，现在使用`successful_attempt.actual_amount_out`的实际数据
2. **Amount Spent显示N/A**: 原代码只使用计划金额，现在优先使用`successful_attempt.actual_amount_in`的实际数据
3. **Token decimals处理**: 正确处理token的decimals，将最小单位转换为用户友好的显示格式
4. **SOL decimals处理**: 正确处理SOL的9位decimals，将lamports转换为SOL

## 测试用例

| 用例方法 | 用例概述 | 前置条件 | 输入 | 预期输出 | 实际输出 | 状态 |
| --- | --- | --- | --- | --- | --- | --- |
| test_successful_trade_amount_display | 测试成功交易时的金额显示 | 交易成功，有实际交易数据 | actual_amount_in=1000000 lamports, actual_amount_out=1500000 (6 decimals) | Amount Bought: 1.500000 grow, Amount Spent: 0.001000000 SOL | Amount Bought: 1.500000 grow, Amount Spent: 0.001000000 SOL | ✅ 通过 |
| test_trade_with_ui_format_amounts | 测试UI格式金额的显示 | 交易成功，金额已是UI格式 | actual_amount_in=0.001, actual_amount_out=2.5 | Amount Bought: 2.500000 grow, Amount Spent: 0.001000000 SOL | Amount Bought: 2.500000 grow, Amount Spent: 0.001000000 SOL | ✅ 通过 |
| test_trade_without_decimals_info | 测试缺少decimals信息时的处理 | 交易成功，token缺少decimals信息 | 无decimals信息，actual_amount_out=1500000 | Amount Bought: 1500000.000000 grow (原始值), Amount Spent: 0.001000000 SOL | Amount Bought: 1500000.000000 grow, Amount Spent: 0.001000000 SOL | ✅ 通过 |

## 测试数据

### 测试Token信息
```python
token_info = {
    'address': 'MUbEZ6mDVy39FrjNCQPyX3PM7CwSM3hQ754ii4wpump',
    'name': 'Grow a garden',
    'symbol': 'grow',
    'decimals': 6,  # 6位小数
    'hit_kol_wallets': []
}
```

### 策略配置
```python
strategy_snapshot = {
    'strategy_name': '测试',
    'buy_amount_sol': 0.001,
    'buy_slippage_percentage': 1.0,
    'same_token_notification_interval': 60
}
```

## 关键测试点

1. **Lamports到SOL转换**: 1000000 lamports = 0.001 SOL (除以10^9)
2. **Token最小单位转换**: 1500000 最小单位 = 1.5 tokens (除以10^6，当decimals=6时)
3. **UI格式检测**: 启发式判断数值是否已经是UI格式
4. **错误处理**: 当decimals信息缺失时的降级处理
5. **消息模板**: 确保模板正确渲染实际交易数据

## 预期修复效果

修复前的Telegram消息：
```
Amount Bought: N/A grow grow
Amount Spent: N/A SOL SOL
```

修复后的Telegram消息：
```
Amount Bought: 1.500000 grow
Amount Spent: 0.001000000 SOL
```

## 运行测试

```bash
cd test/workflows/monitor_kol_activity
python test_handler_amount_display.py
```

## 注意事项

1. 测试使用了大量的Mock对象来隔离外部依赖
2. 测试覆盖了多种数据格式场景（lamports格式、UI格式、缺少decimals）
3. 测试验证了完整的消息渲染流程
4. 所有测试都是异步的，需要使用asyncio运行 