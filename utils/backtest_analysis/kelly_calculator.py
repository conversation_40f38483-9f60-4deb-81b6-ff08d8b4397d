import json
import pandas as pd
import sys
import logging
from typing import Dict, Any, Optional, Tuple

logger = logging.getLogger("KellyCalculator")

# --- Configuration Constants ---
PAYOFF_RATIO_COLUMN = 'return_rate' # Column assumed to be the payoff ratio 'b'
WIN_RATE_COLUMN = 'win_rate' # Column for win rate 'p'
KELLY_FRACTION_COLUMN = 'kelly_fraction_calculated' # Name for the new column

# --- Function Definitions ---
def calculate_kelly(p: float, b: float) -> Tuple[Optional[float], Optional[float], str]:
    """Calculates the Kelly fraction.

    Args:
        p: Win rate (probability of winning).
        b: Payoff ratio (e.g., average win / average loss, or assumed return_rate).

    Returns:
        Tuple[Optional[float], Optional[float], str]:
            - raw_f: The raw calculated Kelly fraction (can be negative).
            - capped_f: The Kelly fraction capped between 0 and 1.
            - status: A status string ('success', 'invalid_win_rate', etc.).
    """
    if not isinstance(p, (int, float)) or not (0 <= p <= 1):
        return None, None, 'invalid_win_rate'
    if not isinstance(b, (int, float)) or pd.isna(b) or b <= 0:
        # Payoff ratio 'b' must be positive
        # If payoff is non-positive, raw Kelly is undefined or negative infinity depending on interpretation
        # We return 0.0 for capped, but maybe None or a specific value for raw?
        # Let's return 0.0 for both for simplicity here, as betting 0 is the action.
        # A very large negative number could also represent the raw calculation tendency.
        # For now, returning 0.0, 0.0 for non-positive payoff.
        return 0.0, 0.0, 'non_positive_payoff'

    q = 1.0 - p
    try:
        # Kelly formula: f = p - q / b
        raw_f = p - (q / b)
        # Cap Kelly fraction between 0 and 1 (common practice)
        # Still cap upper bound at 1, as betting > 100% doesn't make sense
        capped_f = max(0.0, min(raw_f, 1.0))
        return raw_f, capped_f, 'success'
    except ZeroDivisionError:
        return 0.0, 0.0, 'division_by_zero' # Should be caught by b <= 0 check
    except Exception as e:
        logger.error(f"  [Error] Unexpected Kelly calculation error for p={p}, b={b}: {e}", exc_info=True)
        return None, None, 'calculation_error'

def calculate_and_add_kelly_to_json(json_file_path: str) -> bool:
    """Reads a JSON file containing backtest results, calculates the Kelly
    fraction for each result based on win_rate and return_rate, adds the
    fraction to the result's statistics, and overwrites the original JSON file.

    Args:
        json_file_path: Path to the JSON file to process.

    Returns:
        bool: True if processing was successful, False otherwise.
    """
    logger.info(f"开始为文件计算凯利分数: {json_file_path}")

    # Load the JSON data
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except FileNotFoundError:
        logger.error(f"错误：未找到输入文件 '{json_file_path}'。")
        return False
    except json.JSONDecodeError as e:
        logger.error(f"错误：无法解析 JSON 文件 '{json_file_path}'。请确保格式有效。错误详情: {e}")
        return False
    except Exception as e:
        logger.error(f"错误：读取文件时发生未知错误 '{json_file_path}': {e}")
        return False

    # --- Data Structure Validation ---
    if not isinstance(data, dict) or 'results' not in data or not isinstance(data['results'], list):
        logger.error(f"错误：JSON 文件 '{json_file_path}' 结构不符合预期。未找到顶层 'results' 键或其值不是列表。")
        return False

    results_list = data['results']
    logger.info(f"从 '{json_file_path}' 的 'results' 键加载了 {len(results_list)} 条结果。")

    processed_count = 0
    skipped_count = 0
    kelly_added_count = 0

    # --- Process Each Result ---
    for i, result in enumerate(results_list):
        if not isinstance(result, dict):
            logger.warning(f"警告：第 {i+1} 项不是字典格式，已跳过。 数据: {result}")
            skipped_count += 1
            continue

        # Check for nested 'statistics' dict
        if 'statistics' not in result or not isinstance(result['statistics'], dict):
            logger.warning(f"警告：第 {i+1} 条结果缺少 'statistics' 字典，或其格式不正确，已跳过。 数据: {result}")
            skipped_count += 1
            continue

        stats = result['statistics']

        # Ensure required keys are in the nested dicts
        if WIN_RATE_COLUMN not in stats or PAYOFF_RATIO_COLUMN not in stats:
             logger.warning(f"警告：第 {i+1} 条结果的 'statistics' 中缺少 '{WIN_RATE_COLUMN}' 或 '{PAYOFF_RATIO_COLUMN}'，已跳过。 数据: {stats}")
             skipped_count += 1
             continue

        # --- Calculate Kelly ---
        try:
            p_val = stats[WIN_RATE_COLUMN]
            b_val = stats[PAYOFF_RATIO_COLUMN]

            # Attempt conversion to numeric, handle errors
            try:
                p = float(p_val) if p_val is not None else None
            except (ValueError, TypeError):
                p = None

            try:
                b = float(b_val) if b_val is not None else None
            except (ValueError, TypeError):
                b = None

            if p is None or b is None:
                 logger.warning(f"警告：第 {i+1} 条结果的 '{WIN_RATE_COLUMN}' ('{p_val}') 或 '{PAYOFF_RATIO_COLUMN}' ('{b_val}') 不是有效数字，已跳过。")
                 skipped_count += 1
                 continue

            # Calculate Kelly
            raw_f, capped_f, status = calculate_kelly(p, b) # Get both values

            if capped_f is None: # Check capped_f for failure indication
                 logger.warning(f"警告：计算第 {i+1} 条结果的凯利分数失败 ({status})，已跳过。 p={p}, b={b}")
                 skipped_count += 1
                 continue

            # Add the CAPPED Kelly fraction to the statistics dictionary
            stats[KELLY_FRACTION_COLUMN] = capped_f # Store the capped value
            kelly_added_count +=1
            processed_count += 1

        except Exception as e:
            logger.error(f"处理第 {i+1} 条结果时发生未知错误：{e}。数据: {result}。已跳过。", exc_info=True)
            skipped_count += 1
            continue

    logger.info(f"凯利分数计算完成。成功处理: {processed_count}, 添加凯利分数: {kelly_added_count}, 跳过: {skipped_count}")

    # --- Save Updated JSON ---
    try:
        with open(json_file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=4, default=str) # Use default=str for datetimes etc.
        logger.info(f"已将包含凯利分数的更新结果保存回 '{json_file_path}'")
        return True
    except Exception as e:
        logger.error(f"错误：保存更新后的 JSON 文件 '{json_file_path}' 时出错: {e}")
        return False

# --- Example Usage (if run directly) ---
if __name__ == "__main__":
    # Configure basic logging for testing
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    # Example: Replace with the actual path to your results file
    test_file = 'backtest_result/ed_param_search_20250411_023816/param_search_results.json'

    if calculate_and_add_kelly_to_json(test_file):
        print(f"Successfully processed and updated {test_file}")
    else:
        print(f"Failed to process {test_file}") 