# GMGN v2 API 交易接口详细需求规格

## 1. 项目背景

根据GMGN官方最新API文档（https://docs.gmgn.ai/cn/he-zuo-api-ji-cheng-gmgn-solana-jiao-yi-api），GMGN发布了新版本的Solana交易API接口，提供了更完善的功能和更好的性能。新API包含三个核心接口：

1. **获取交易路由**：`GET /defi/router/v1/sol/tx/get_swap_route`
2. **提交签名交易**：`POST /txproxy/v1/send_transaction`  
3. **查询交易状态**：`GET /defi/router/v1/sol/tx/get_transaction_status`

新API相比旧版本具有以下优势：
- **更完整的交易流程**：提供从路由查询到状态监控的完整链路
- **更好的错误处理**：提供详细的错误信息和状态码
- **防夹交易支持**：支持MEV保护功能
- **更稳定的服务**：改进的API稳定性和可靠性

当前项目中的`GmgnTradeService`基于旧版API实现，需要升级以支持新的API接口和功能。

## 2. 需求概述

创建GMGN v2版本的交易接口，基于最新的GMGN API实现，同时优化现有架构，直接使用Python调用GMGN API，移除对Node.js脚本的依赖，提供更简洁、高效、功能完整的交易服务。

## 3. 功能需求

### 3.1 核心交易功能

#### 3.1.1 获取交易路由
- **接口**: `GET https://gmgn.ai/defi/router/v1/sol/tx/get_swap_route`
- **功能**: 获取代币交换的最优路由和待签名交易
- **参数**:
  - `token_in_address`: 输入代币地址
  - `token_out_address`: 输出代币地址  
  - `in_amount`: 输入数量（最小单位lamports）
  - `from_address`: 发起交易的钱包地址
  - `slippage`: 滑点百分比
  - `fee`: 网络和节点优先费（SOL单位）
  - `is_anti_mev`: 是否开启防夹交易（可选）
  - `partner`: 合作伙伴来源名称（可选）

#### 3.1.2 提交签名交易
- **接口**: `POST https://gmgn.ai/txproxy/v1/send_transaction`
- **功能**: 提交已签名的交易到区块链
- **参数**:
  - `chain`: 区块链类型（固定为"sol"）
  - `signedTx`: 签名后的交易（base64编码）
  - `isAntiMev`: 是否防夹交易（可选）

#### 3.1.3 查询交易状态
- **接口**: `GET https://gmgn.ai/defi/router/v1/sol/tx/get_transaction_status`
- **功能**: 查询交易的上链状态
- **参数**:
  - `hash`: 交易哈希
  - `last_valid_height`: 交易的最后有效块高度

### 3.2 交易流程

1. **路由查询阶段**：
   - 调用`get_swap_route`获取交易路由和待签名交易
   - 解析返回的`raw_tx.swapTransaction`（base64编码）
   - 提取`lastValidBlockHeight`用于后续状态查询

2. **交易签名阶段**：
   - 使用Solana Python库解析交易
   - 使用钱包私钥对交易进行签名
   - 将签名后的交易重新编码为base64

3. **交易提交阶段**：
   - 调用`send_transaction`提交签名交易
   - 获取交易哈希用于状态跟踪

4. **状态监控阶段**：
   - 定期调用`get_transaction_status`查询交易状态
   - 处理三种状态：成功上链、上链失败、交易过期

### 3.3 错误处理需求

#### 3.3.1 滑点相关错误识别
需要识别以下GMGN API特定的滑点相关错误：
- `route failed`、`swap failed`
- `price change`、`market impact`
- `liquidity insufficient`
- `output amount too low`
- `insufficient output amount`
- `price moved too much`

#### 3.3.2 不可重试错误识别
需要识别以下不可重试错误：
- 钱包相关：`invalid wallet`、`private key invalid`
- 代币相关：`token not supported`、`invalid token address`
- 权限相关：`api key invalid`、`unauthorized access`
- 配置错误：`invalid parameters`、`amount too small`

### 3.4 兼容性需求

#### 3.4.1 接口兼容性
- 必须实现`TradeInterface`抽象类
- 保持与现有`GmgnTradeService`相同的方法签名
- 返回相同格式的`TradeResult`对象

#### 3.4.2 配置兼容性
- 支持现有的`strategy_snapshot`配置格式
- 兼容现有的滑点递增重试机制
- 保持现有的日志格式和错误报告

## 4. 非功能需求

### 4.1 性能需求
- **响应时间**: 单次交易请求应在30秒内完成
- **并发性**: 支持多个交易并发执行
- **资源使用**: 移除Node.js依赖，减少内存和CPU开销

### 4.2 可靠性需求
- **错误恢复**: 网络错误时自动重试
- **状态一致性**: 确保交易状态的准确跟踪
- **日志记录**: 详细记录所有API调用和响应

### 4.3 安全需求
- **私钥保护**: 私钥仅在内存中使用，不写入日志
- **API安全**: 支持GMGN API的安全机制
- **防夹交易**: 支持MEV保护功能

## 5. 技术约束

### 5.1 依赖库
- **HTTP客户端**: 使用`httpx`进行异步HTTP请求
- **Solana库**: 使用`solders`或`solana-py`进行交易签名
- **JSON处理**: 使用标准库`json`
- **Base64编码**: 使用标准库`base64`

### 5.2 API限制
- **免费使用**: GMGN API当前免费，无需API Key
- **请求频率**: 遵循GMGN API的频率限制
- **数据格式**: 严格按照API文档的数据格式

## 6. 验收标准

### 6.1 功能验收
- [ ] 成功执行买入交易（SOL -> SPL代币）
- [ ] 成功执行卖出交易（SPL代币 -> SOL）
- [ ] 正确处理交易失败情况
- [ ] 准确识别滑点相关错误
- [ ] 正确返回交易结果和状态

### 6.2 性能验收
- [ ] 交易执行时间比Node.js版本减少至少30%
- [ ] 内存使用量减少（无需启动Node.js进程）
- [ ] 支持并发交易执行

### 6.3 兼容性验收
- [ ] 与现有交易系统无缝集成
- [ ] 现有测试用例全部通过
- [ ] 配置文件无需修改

## 7. 实施优先级

### 高优先级
1. 核心交易功能实现
2. 基本错误处理
3. 与现有接口的兼容性

### 中优先级
1. 高级错误识别和分类
2. 性能优化
3. 详细日志记录

### 低优先级
1. 防夹交易功能
2. 高级监控和指标
3. 扩展配置选项

## 8. 风险评估

### 8.1 技术风险
- **API变更风险**: GMGN API可能发生变更，需要及时适配
- **依赖风险**: Solana Python库的稳定性和兼容性
- **性能风险**: Python HTTP请求性能可能不如Node.js

### 8.2 业务风险
- **交易失败风险**: 新实现可能引入交易失败
- **兼容性风险**: 可能影响现有交易流程
- **测试风险**: 需要充分测试以确保稳定性

### 8.3 缓解措施
- 保留现有Node.js实现作为备用方案
- 实施全面的单元测试和集成测试
- 分阶段部署，先在测试环境验证
- 建立监控和告警机制 