"""
交易模块 - 统一自动交易管理器和相关组件
"""

# 核心管理器
from .auto_trade_manager import AutoTradeManager, get_auto_trade_manager

# 核心组件
from .config_manager import ConfigManager
from .channel_registry import ChannelRegistry, ChannelNotFoundError
from .channel_selector import ChannelSelector
from .trade_orchestrator import TradeOrchestrator, TradeRequest
from .trade_record_manager import TradeRecordManager

# 滑点重试组件
from .slippage_retry import (
    SlippageCalculator,
    RetryDecisionEngine,
    RetryDelayCalculator,
    ParameterMerger,
    RetryContext
)

# 便捷导出
__all__ = [
    # 主要接口
    "AutoTradeManager",
    "get_auto_trade_manager",
    
    # 核心组件
    "ConfigManager",
    "ChannelRegistry",
    "ChannelSelector", 
    "TradeOrchestrator",
    "TradeRecordManager",
    
    # 滑点重试组件
    "SlippageCalculator",
    "RetryDecisionEngine",
    "RetryDelayCalculator",
    "ParameterMerger",
    "RetryContext",
    
    # 数据类型
    "TradeRequest",
    
    # 异常
    "ChannelNotFoundError",
] 