"""滑点计算器单元测试"""

import unittest
from typing import Tuple

from utils.trading.slippage_retry.slippage_calculator import SlippageCalculator
from models.slippage_retry import SlippageRetryConfig


class TestSlippageCalculator(unittest.TestCase):
    """滑点计算器测试类"""
    
    def setUp(self) -> None:
        """测试前准备"""
        self.calculator = SlippageCalculator()
        
        # 创建测试用的滑点配置
        self.test_config = SlippageRetryConfig(
            enabled=True,
            increment_percentage=0.5,
            max_slippage_percentage=5.0,
            retry_delay_seconds=0.5
        )
    
    def test_calculate_next_slippage_basic(self) -> None:
        """测试基础滑点计算"""
        current_slippage = 1.0
        
        new_slippage, exceeded_limit = self.calculator.calculate_next_slippage(
            current_slippage=current_slippage,
            config=self.test_config,
            trade_type="buy"
        )
        
        expected_slippage = 1.5  # 1.0 + 0.5
        self.assertEqual(new_slippage, expected_slippage)
        self.assertFalse(exceeded_limit)
    
    def test_calculate_next_slippage_exceeds_limit(self) -> None:
        """测试滑点超过限制的情况"""
        current_slippage = 4.8
        
        new_slippage, exceeded_limit = self.calculator.calculate_next_slippage(
            current_slippage=current_slippage,
            config=self.test_config,
            trade_type="buy"
        )
        
        # 应该被限制为最大值
        self.assertEqual(new_slippage, self.test_config.max_slippage_percentage)
        self.assertTrue(exceeded_limit)
    
    def test_calculate_next_slippage_already_at_max(self) -> None:
        """测试已达到最大滑点的情况"""
        current_slippage = 5.0
        
        new_slippage, exceeded_limit = self.calculator.calculate_next_slippage(
            current_slippage=current_slippage,
            config=self.test_config,
            trade_type="buy"
        )
        
        # 应该保持最大值
        self.assertEqual(new_slippage, self.test_config.max_slippage_percentage)
        self.assertTrue(exceeded_limit)
    
    def test_calculate_next_slippage_disabled(self) -> None:
        """测试禁用滑点递增的情况"""
        disabled_config = SlippageRetryConfig(
            enabled=False,
            increment_percentage=0.5,
            max_slippage_percentage=5.0,
            retry_delay_seconds=0.5
        )
        
        current_slippage = 1.0
        new_slippage, exceeded_limit = self.calculator.calculate_next_slippage(
            current_slippage=current_slippage,
            config=disabled_config,
            trade_type="buy"
        )
        
        # 禁用时应该保持原值
        self.assertEqual(new_slippage, current_slippage)
        self.assertFalse(exceeded_limit)
    
    def test_is_slippage_at_limit(self) -> None:
        """测试滑点是否达到上限"""
        # 未达到上限
        self.assertFalse(self.calculator.is_slippage_at_limit(4.9, self.test_config))
        
        # 刚好达到上限
        self.assertTrue(self.calculator.is_slippage_at_limit(5.0, self.test_config))
        
        # 超过上限
        self.assertTrue(self.calculator.is_slippage_at_limit(5.1, self.test_config))
    
    def test_get_remaining_slippage_room(self) -> None:
        """测试剩余滑点空间计算"""
        # 正常情况
        remaining = self.calculator.get_remaining_slippage_room(1.0, self.test_config)
        self.assertEqual(remaining, 4.0)  # 5.0 - 1.0
        
        # 已达到上限
        remaining = self.calculator.get_remaining_slippage_room(5.0, self.test_config)
        self.assertEqual(remaining, 0.0)
        
        # 超过上限
        remaining = self.calculator.get_remaining_slippage_room(6.0, self.test_config)
        self.assertEqual(remaining, 0.0)
    
    def test_calculate_slippage_steps_remaining(self) -> None:
        """测试剩余滑点调整步数"""
        # 正常情况
        steps = self.calculator.calculate_slippage_steps_remaining(1.0, self.test_config)
        self.assertEqual(steps, 8)  # (5.0 - 1.0) / 0.5 = 8
        
        # 已达到上限
        steps = self.calculator.calculate_slippage_steps_remaining(5.0, self.test_config)
        self.assertEqual(steps, 0)
        
                 # 禁用状态
        disabled_config = SlippageRetryConfig(
             enabled=False,
             increment_percentage=0.5,
             max_slippage_percentage=5.0,
             retry_delay_seconds=0.5
        )
        steps = self.calculator.calculate_slippage_steps_remaining(1.0, disabled_config)
        self.assertEqual(steps, 0)
    
    def test_validate_slippage_config_valid(self) -> None:
        """测试有效配置验证"""
        self.assertTrue(self.calculator.validate_slippage_config(self.test_config))
    
    def test_validate_slippage_config_invalid_increment(self) -> None:
        """测试无效增量配置"""
        invalid_config = SlippageRetryConfig(
             enabled=True,
             increment_percentage=0.0,  # 无效增量
             max_slippage_percentage=5.0,
             retry_delay_seconds=0.5
         )
        self.assertFalse(self.calculator.validate_slippage_config(invalid_config))
    
    def test_validate_slippage_config_invalid_max(self) -> None:
        """测试无效最大滑点配置"""
        invalid_config = SlippageRetryConfig(
            enabled=True,
            increment_percentage=0.5,
            max_slippage_percentage=0.0,  # 无效最大滑点
            retry_delay_seconds=0.5
        )
        self.assertFalse(self.calculator.validate_slippage_config(invalid_config))
    
    def test_get_calculation_summary(self) -> None:
        """测试计算摘要生成"""
        original_slippage = 1.0
        current_slippage = 2.5
        
        summary = self.calculator.get_calculation_summary(
            original_slippage=original_slippage,
            current_slippage=current_slippage,
            config=self.test_config
        )
        
        # 检查摘要包含关键信息
        self.assertIn("1.0%", summary)
        self.assertIn("2.5%", summary)
        self.assertIn("1.5%", summary)  # 累计增加
    
    def test_different_trade_types(self) -> None:
        """测试不同交易类型"""
        current_slippage = 1.0
        
        # 测试买入
        buy_result = self.calculator.calculate_next_slippage(
            current_slippage=current_slippage,
            config=self.test_config,
            trade_type="buy"
        )
        
        # 测试卖出
        sell_result = self.calculator.calculate_next_slippage(
            current_slippage=current_slippage,
            config=self.test_config,
            trade_type="sell"
        )
        
        # 在相同配置下，买卖应该产生相同结果
        self.assertEqual(buy_result, sell_result)
    
    def test_zero_increment(self) -> None:
        """测试零增量配置"""
        zero_increment_config = SlippageRetryConfig(
            enabled=True,
            increment_percentage=0.0,
            max_slippage_percentage=5.0,
            retry_delay_seconds=0.5
        )
        
        # 验证配置应该失败
        self.assertFalse(self.calculator.validate_slippage_config(zero_increment_config))
        
        # 步数计算应该返回0
        steps = self.calculator.calculate_slippage_steps_remaining(1.0, zero_increment_config)
        self.assertEqual(steps, 0)
    
    def test_large_increment(self) -> None:
        """测试大增量配置"""
        large_increment_config = SlippageRetryConfig(
            enabled=True,
            increment_percentage=10.0,  # 大于最大滑点
            max_slippage_percentage=5.0,
            retry_delay_seconds=0.5
        )
        
        # 配置验证应该通过但有警告
        self.assertTrue(self.calculator.validate_slippage_config(large_increment_config))
        
        # 一步应该直接到达最大值
        current_slippage = 1.0
        new_slippage, exceeded = self.calculator.calculate_next_slippage(
            current_slippage=current_slippage,
            config=large_increment_config,
            trade_type="buy"
        )
        
        self.assertEqual(new_slippage, 5.0)
        self.assertTrue(exceeded)


if __name__ == '__main__':
    unittest.main() 