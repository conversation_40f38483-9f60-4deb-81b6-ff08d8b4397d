name: "GMGN 代币开发者持有者记录"
description: "定期获取链上的开发者持有者记录，并将数据存储到数据库"

nodes:
  - name: "DeveloperHoldersSchedulerNode"
    node_type: "input"
    interval: 10
    generate_data: workflows.gmgn_token_link.handler.generate_data
    flow_control:
      max_pending_messages: 10
      check_interval: 1
      enable_flow_control: true

  - name: "DeveloperHoldersMonitorNode"
    node_type: "process"
    depend_ons: ["DeveloperHoldersSchedulerNode"]
    concurrency: 1
    interval: 1
    process_item: workflows.gmgn_developer_holders.handler.process_developer_holders

  - name: "DeveloperHoldersStoreNode"
    node_type: "storage"
    depend_ons: ["DeveloperHoldersMonitorNode"]
    batch_size: 1000  # 每批处理10条数据
    store_data: workflows.gmgn_developer_holders.handler.store_data
    validate: workflows.gmgn_developer_holders.handler.validate 