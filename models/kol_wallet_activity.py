from datetime import datetime
from typing import Optional, Dict, Any, List
from beanie import Document, Indexed
from pydantic import Field, BaseModel

class TokenInfo(BaseModel):
    """代币信息"""
    address: str = Field(default="", description="代币地址")
    symbol: str = Field(default="", description="代币符号")
    logo: Optional[str] = Field(default=None, description="代币logo")

class QuoteToken(BaseModel):
    """报价代币信息"""
    token_address: str = Field(default="", description="代币地址")
    name: str = Field(default="", description="代币名称")
    symbol: str = Field(default="", description="代币符号")
    decimals: int = Field(default=0, description="小数位数")
    logo: Optional[str] = Field(default=None, description="代币logo")

class KOLWalletActivity(Document):
    """KOL钱包活动模型
    
    用于存储KOL钱包的活动信息，包括：
    - 钱包地址
    - 交易类型（买入/卖出/转入/转出等）
    - 代币信息
    - 交易金额和数量
    - 交易时间和哈希
    - 价格和成本信息
    """
    
    # 基本信息
    wallet: str = Field(description="钱包地址")
    chain: str = Field(description="链名称")
    tx_hash: Indexed(str) = Field(description="交易哈希", unique=True)
    timestamp: int = Field(description="交易时间戳")
    event_type: str = Field(description="事件类型(buy/sell/transferIn/transferOut/add/remove)")
    
    # 代币信息
    token: TokenInfo = Field(default_factory=TokenInfo, description="代币信息")
    token_amount: str = Field(default="0", description="代币数量")
    quote_amount: str = Field(default="0", description="报价数量")
    quote_token: QuoteToken = Field(default_factory=QuoteToken, description="报价代币")
    
    # 价格和成本信息
    cost_usd: Optional[str] = Field(default="0", description="成本(USD)")
    buy_cost_usd: Optional[str] = Field(default=None, description="买入成本(USD)")
    price_usd: Optional[str] = Field(default="0", description="价格(USD)")
    is_open_or_close: int = Field(default=0, description="是否开仓或平仓")
    
    # 地址信息
    from_address: str = Field(default="", description="来源地址")
    to_address: str = Field(default="", description="目标地址")
    
    # 时间信息
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")
    
    class Settings:
        name = "kol_wallet_activities"
        use_state_management = True
    
    @classmethod
    def from_api_data(cls, data: Dict[str, Any], wallet_address: str) -> 'KOLWalletActivity':
        """从API数据创建模型实例
        
        Args:
            data: API返回的数据
            wallet_address: 钱包地址
            
        Returns:
            KOLWalletActivity: 模型实例
        """
        # 创建代币信息
        token_info = TokenInfo(
            address=data.get('token', {}).get('address', ''),
            symbol=data.get('token', {}).get('symbol', ''),
            logo=data.get('token', {}).get('logo')
        )
        
        # 创建报价代币信息
        quote_token_info = QuoteToken(
            token_address=data.get('quote_token', {}).get('token_address', ''),
            name=data.get('quote_token', {}).get('name', ''),
            symbol=data.get('quote_token', {}).get('symbol', ''),
            decimals=data.get('quote_token', {}).get('decimals', 0),
            logo=data.get('quote_token', {}).get('logo')
        )
        
        return cls(
            wallet=wallet_address,
            chain=data.get('chain', ''),
            tx_hash=data.get('tx_hash', ''),
            timestamp=data.get('timestamp', 0),
            event_type=data.get('event_type', ''),
            token=token_info,
            token_amount=data.get('token_amount', '0'),
            quote_amount=data.get('quote_amount', '0'),
            quote_token=quote_token_info,
            cost_usd=data.get('cost_usd'),
            buy_cost_usd=data.get('buy_cost_usd'),
            price_usd=data.get('price_usd'),
            is_open_or_close=data.get('is_open_or_close', 0),
            from_address=data.get('from_address', ''),
            to_address=data.get('to_address', '')
        )
    
    class Config:
        json_schema_extra = {
            "example": {
                "wallet": "6ZddMpdAgo1BNncaQkZcLCCj6ZGwMpLSpiwfMHg9cVxc",
                "chain": "sol",
                "tx_hash": "NqDxvcvYrCMWyPdq5qUJXf3mdUoCW8byiCoBgXnGrF389c9GecBpERiVN4h5VqYhHxPVVkZ6JjBsjHzdFFtB7Pp",
                "timestamp": 1742057731,
                "event_type": "sell",
                "token": {
                    "address": "52a3zTBKR4yruhzofTkZu3drq1NvG2PHwSdBjEq3pump",
                    "symbol": "Churro",
                    "logo": "https://pump.mypinata.cloud/ipfs/QmRqQryEWJc4jbhnrNKbLtda1vx8kFb9MuEmfaLSxAVFWb"
                },
                "token_amount": "25088469.92174700000000000000",
                "quote_amount": "2.27254714300000000000",
                "quote_token": {
                    "token_address": "So11111111111111111111111111111111111111112",
                    "name": "Wrapped SOL",
                    "symbol": "WSOL",
                    "decimals": 9,
                    "logo": "https://s2.coinmarketcap.com/static/img/coins/64x64/16116.png"
                },
                "cost_usd": "307.9301378765",
                "buy_cost_usd": "335.80035",
                "price_usd": "0.0000122737711364",
                "is_open_or_close": 1,
                "from_address": "",
                "to_address": ""
            }
        } 