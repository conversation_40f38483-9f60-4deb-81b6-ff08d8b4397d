from beanie import Document, Indexed, PydanticObjectId # 导入 PydanticObjectId
from pymongo import IndexModel # 从 pymongo 导入 IndexModel
from pydantic import Field, field_validator, ValidationInfo
from typing import List, Optional, Any, Annotated
import datetime
import logging # 添加日志

logger = logging.getLogger(__name__) # 添加日志记录器

# Helper function for safe float conversion
def safe_float(value: Any) -> float:
    if value is None or value == "":
        return 0.0
    try:
        return float(value)
    except (ValueError, TypeError) as e:
        logger.warning(f"无法将值 '{value}' (类型: {type(value).__name__}) 转换为浮点数: {e}, 使用 0.0")
        return 0.0

class GmgnTokenTrade(Document):
    """
    存储来自 Gmgn API 的代币交易记录
    """
    # --- 使用 Annotated 和 IndexModel 定义索引 ---
    trade_id: Annotated[str, IndexModel(keys=[("trade_id", 1)], unique=True)]
    maker: Annotated[str, IndexModel(keys=[("maker", 1)])]
    timestamp: Annotated[datetime.datetime, IndexModel(keys=[("timestamp", 1)])]
    tx_hash: Annotated[str, IndexModel(keys=[("tx_hash", 1)])]
    token_address: Annotated[str, IndexModel(keys=[("token_address", 1)])]
    chain: Annotated[str, IndexModel(keys=[("chain", 1)])]
    
    # --- 其他字段 ---
    base_amount: float
    quote_amount: float
    quote_symbol: str
    quote_address: str
    amount_usd: float
    event: str # "buy" or "sell"
    price_usd: float
    total_trade: int
    is_following: bool # 转换 0/1 为布尔值
    is_open_or_close: bool # 转换 0/1 为布尔值
    buy_cost_usd: Optional[float] = None
    balance: float
    cost: float
    history_bought_amount: float
    history_sold_income: float
    history_sold_amount: float
    realized_profit: float
    unrealized_profit: float
    maker_tags: List[str] = []
    maker_token_tags: List[str] = []
    maker_name: Optional[str] = None
    maker_twitter_username: Optional[str] = None
    maker_twitter_name: Optional[str] = None
    maker_avatar: Optional[str] = None
    maker_ens: Optional[str] = None

    # --- Pydantic V2 Validators --- 
    @field_validator('timestamp', mode='before')
    @classmethod
    def convert_timestamp(cls, v: Any):
        if isinstance(v, (int, float)):
            try:
                # 假设时间戳是秒级的 UTC 时间
                return datetime.datetime.fromtimestamp(v, tz=datetime.timezone.utc)
            except (ValueError, TypeError) as e:
                logger.warning(f"无法将时间戳 '{v}' 转换为 datetime: {e}, 使用默认值")
                return datetime.datetime.fromtimestamp(0, tz=datetime.timezone.utc) # 或者返回 None，取决于业务逻辑
        if isinstance(v, str):
            try:
                # 尝试解析字符串格式的时间戳
                return datetime.datetime.fromisoformat(v.replace('Z', '+00:00'))
            except ValueError:
                logger.warning(f"无法将字符串时间戳 '{v}' 转换为 datetime, 使用默认值")
                return datetime.datetime.fromtimestamp(0, tz=datetime.timezone.utc)
        if isinstance(v, datetime.datetime):
            # 如果已经是datetime，确保是UTC
            if v.tzinfo is None:
                return v.replace(tzinfo=datetime.timezone.utc)
            return v.astimezone(datetime.timezone.utc)
        logger.warning(f"收到未知类型的时间戳 '{v}' (类型: {type(v).__name__}), 使用默认值")
        return datetime.datetime.fromtimestamp(0, tz=datetime.timezone.utc)

    @field_validator(
        'base_amount', 'quote_amount', 'amount_usd', 'price_usd',
        'balance', 'cost', 'history_bought_amount', 'history_sold_income',
        'history_sold_amount', 'realized_profit', 'unrealized_profit', 'buy_cost_usd', mode='before'
    )
    @classmethod
    def validate_float(cls, v: Any):
        return safe_float(v)

    @field_validator('is_following', 'is_open_or_close', mode='before')
    @classmethod
    def validate_bool_from_int_or_bool(cls, v: Any):
        if isinstance(v, bool):
            return v
        if v in (0, 1):
            return bool(v)
        logger.warning(f"无法将值 '{v}' 转换为布尔值, 使用 False")
        return False # 或者抛出 ValueError，取决于严格性要求
        
    @field_validator('buy_cost_usd', mode='before')
    @classmethod
    def validate_optional_float(cls, v: Any):
        # 如果 API 可能返回空字符串或 None 表示缺失，则返回 None
        if v is None or v == "":
            return None
        return safe_float(v)

    class Settings:
        name = "gmgn_token_trades" # MongoDB collection name
        # 索引现在通过 Annotated 定义，这里可以留空或定义复合索引
        indexes = [
            # 如果需要复合索引，可以在这里定义
            # IndexModel([("token_address", 1), ("chain", 1), ("timestamp", -1)]) 
        ]

    # 移除 from_api_data，因为验证器现在处理转换
    # @classmethod
    # def from_api_data(cls, data: dict) -> "GmgnTokenTrade":
    #     ... (旧代码) ... 