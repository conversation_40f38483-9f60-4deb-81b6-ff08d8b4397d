from datetime import datetime
from typing import Optional, Dict, Any
from beanie import Document, Indexed
from pydantic import Field
import pymongo

class ErrorLog(Document):
    """错误日志模型"""
    
    job_name: str = Field(description="任务名称")
    error_message: str = Field(description="错误信息")
    error_traceback: str = Field(description="错误堆栈")
    context: Dict[str, Any] = Field(default_factory=dict, description="错误发生时的上下文数据")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    
    class Settings:
        name = "error_logs"
        indexes = [
            "job_name",
            "created_at"
        ] 