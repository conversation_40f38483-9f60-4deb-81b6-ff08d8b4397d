#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易统计分析CLI接口

提供命令行工具来运行交易统计分析功能
"""

import asyncio
import argparse
import logging
import sys
import os
from datetime import datetime, timedelta, time
from pathlib import Path
from typing import Optional, List

from models import init_db
from utils.trading.statistics.trade_statistics_analyzer import TradeStatisticsAnalyzer
from utils.trading.statistics.models import StatisticsConfig, ReportFormat
from dao.trade_record_dao import TradeRecordDAO
from utils.trading.trade_record_verification_updater import TradeRecordVerificationUpdater

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_date(date_str: str) -> datetime:
    """
    解析日期字符串
    
    Args:
        date_str: 日期字符串，格式为 YYYY-MM-DD
        
    Returns:
        datetime: 解析后的日期
    """
    try:
        return datetime.strptime(date_str, '%Y-%m-%d')
    except ValueError:
        raise argparse.ArgumentTypeError(f"无效的日期格式: {date_str}，请使用 YYYY-MM-DD 格式")


def parse_single_date(date_str: str) -> tuple:
    """
    解析单一日期字符串并转换为完整的时间范围
    
    Args:
        date_str: 日期字符串，格式为 YYYY-MM-DD
        
    Returns:
        tuple: (start_datetime, end_datetime) 表示该日期的完整24小时范围
    """
    try:
        # 解析基础日期
        base_date = datetime.strptime(date_str, '%Y-%m-%d')
        
        # 设置开始时间为当日00:00:00
        start_time = base_date.replace(hour=0, minute=0, second=0, microsecond=0)
        
        # 设置结束时间为当日23:59:59.999999
        end_time = base_date.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        return start_time, end_time
        
    except ValueError:
        raise argparse.ArgumentTypeError(f"无效的日期格式: {date_str}，请使用 YYYY-MM-DD 格式")


def get_yesterday_range() -> tuple:
    """
    获取昨天的时间范围
    
    Returns:
        tuple: (start_datetime, end_datetime) 表示昨天的完整24小时范围
    """
    today = datetime.now().date()
    yesterday = today - timedelta(days=1)
    
    start_time = datetime.combine(yesterday, time(0, 0, 0))
    end_time = datetime.combine(yesterday, time(23, 59, 59, 999999))
    
    return start_time, end_time


def get_last_week_range() -> tuple:
    """
    获取上周的时间范围（周一到周日）
    
    Returns:
        tuple: (start_datetime, end_datetime) 表示上周的完整时间范围
    """
    today = datetime.now().date()
    
    # 找到本周周一
    current_monday = today - timedelta(days=today.weekday())
    
    # 计算上周周一和周日
    last_monday = current_monday - timedelta(days=7)
    last_sunday = last_monday + timedelta(days=6)
    
    start_time = datetime.combine(last_monday, time(0, 0, 0))
    end_time = datetime.combine(last_sunday, time(23, 59, 59, 999999))
    
    return start_time, end_time


def parse_list(list_str: str) -> List[str]:
    """
    解析逗号分隔的列表字符串
    
    Args:
        list_str: 逗号分隔的字符串
        
    Returns:
        List[str]: 解析后的列表
    """
    if not list_str:
        return []
    return [item.strip() for item in list_str.split(',') if item.strip()]


def create_parser() -> argparse.ArgumentParser:
    """
    创建命令行参数解析器
    
    Returns:
        argparse.ArgumentParser: 参数解析器
    """
    parser = argparse.ArgumentParser(
        description='交易统计分析工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 分析昨天的交易
  python -m utils.trading.statistics.cli --yesterday
  
  # 分析上周的交易
  python -m utils.trading.statistics.cli --last-week
  
  # 分析特定日期的交易
  python -m utils.trading.statistics.cli --date 2025-05-29
  
  # 分析最近7天的所有交易
  python -m utils.trading.statistics.cli --days 7
  
  # 分析指定日期范围的交易
  python -m utils.trading.statistics.cli --start-date 2024-01-01 --end-date 2024-01-31
  
  # 分析特定策略的交易
  python -m utils.trading.statistics.cli --strategies "strategy1,strategy2"
  
  # 分析特定Token的交易
  python -m utils.trading.statistics.cli --tokens "token1,token2"
  
  # 生成JSON格式报告到指定文件
  python -m utils.trading.statistics.cli --format json --output /path/to/report.json
  
  # 生成HTML格式报告到指定文件
  python -m utils.trading.statistics.cli --format html --output /path/to/report.html
  
  # 生成报告到指定目录（自动生成文件名）
  python -m utils.trading.statistics.cli --format html --output /www/report/
  python -m utils.trading.statistics.cli --days 7 --output /tmp/reports/
  
  # 生成昨天的HTML报告
  python -m utils.trading.statistics.cli --yesterday --format html --output /www/report/
  
  # 发送昨天的飞书日报
  python -m utils.trading.statistics.cli --yesterday --send-feishu \\
    --feishu-webhook "https://open.feishu.cn/open-apis/bot/v2/hook/xxx" \\
    --report-base-url "https://reports.example.com" \\
    --upload-path "/var/www/reports"
  
  # 发送上周的飞书周报
  python -m utils.trading.statistics.cli --last-week --send-feishu \\
    --feishu-webhook "https://open.feishu.cn/open-apis/bot/v2/hook/xxx"
        """
    )
    
    # 时间范围参数
    time_group = parser.add_argument_group('时间范围')
    time_group.add_argument(
        '--date',
        type=str,
        help='指定单一日期进行统计 (YYYY-MM-DD格式)，自动扩展为该日期的完整24小时范围'
    )
    time_group.add_argument(
        '--yesterday',
        action='store_true',
        help='获取昨天的交易统计数据'
    )
    time_group.add_argument(
        '--last-week',
        action='store_true',
        help='获取上周的交易统计数据 (周一到周日)'
    )
    time_group.add_argument(
        '--start-date',
        type=parse_date,
        help='开始日期 (YYYY-MM-DD)'
    )
    time_group.add_argument(
        '--end-date',
        type=parse_date,
        help='结束日期 (YYYY-MM-DD)'
    )
    time_group.add_argument(
        '--days',
        type=int,
        help='分析最近N天的数据（与其他时间参数互斥）'
    )
    time_group.add_argument(
        '--period',
        choices=['daily', 'weekly', 'custom'],
        default='custom',
        help='报告周期类型 (默认: custom)'
    )
    
    # 过滤参数
    filter_group = parser.add_argument_group('过滤条件')
    filter_group.add_argument(
        '--strategies',
        type=parse_list,
        help='策略名称列表，逗号分隔'
    )
    filter_group.add_argument(
        '--tokens',
        type=parse_list,
        help='Token地址列表，逗号分隔'
    )
    
    # 输出参数
    output_group = parser.add_argument_group('输出选项')
    output_group.add_argument(
        '--format',
        choices=['html', 'json'],
        default='html',
        help='报告格式 (默认: html)'
    )
    output_group.add_argument(
        '--output',
        type=str,
        help='输出路径（可以是文件路径或目录路径）。如果指定目录，将自动生成带时间戳的文件名'
    )
    output_group.add_argument(
        '--no-charts',
        action='store_true',
        help='不生成图表（仅适用于HTML格式）'
    )
    
    # 飞书消息发送参数
    feishu_group = parser.add_argument_group('飞书消息发送')
    feishu_group.add_argument(
        '--send-feishu',
        action='store_true',
        help='发送飞书消息通知'
    )
    feishu_group.add_argument(
        '--feishu-webhook',
        type=str,
        help='飞书机器人Webhook URL（也可通过环境变量FEISHU_WEBHOOK_URL设置）'
    )
    feishu_group.add_argument(
        '--report-base-url',
        type=str,
        help='报告访问基础URL（也可通过环境变量REPORT_BASE_URL设置）'
    )
    feishu_group.add_argument(
        '--upload-path',
        type=str,
        help='报告上传路径（也可通过环境变量REPORT_UPLOAD_PATH设置）'
    )
    feishu_group.add_argument(
        '--feishu-message-type',
        choices=['card', 'text'],
        default='card',
        help='飞书消息类型 (默认: card)'
    )
    feishu_group.add_argument(
        '--no-upload-report',
        action='store_true',
        help='不上传HTML报告，仅发送摘要消息'
    )
    
    # 其他选项
    parser.add_argument(
        '--verbose',
        '-v',
        action='store_true',
        help='详细输出'
    )
    parser.add_argument(
        '--validate-data',
        action='store_true',
        help='验证数据完整性'
    )
    
    # Token链接配置
    token_link_group = parser.add_argument_group('Token链接配置')
    token_link_group.add_argument(
        '--token-link-base-url',
        type=str,
        default='https://gmgn.ai/sol/token/',
        help='Token链接基础URL (默认: https://gmgn.ai/sol/token/)'
    )
    token_link_group.add_argument(
        '--disable-token-links',
        action='store_true',
        help='禁用Token地址链接功能'
    )
    
    return parser


def validate_args(args) -> None:
    """
    验证命令行参数
    
    Args:
        args: 解析后的参数
    """
    # 检查时间范围参数的互斥性
    time_params = [
        ('date', args.date),
        ('yesterday', args.yesterday),
        ('last-week', args.last_week),
        ('days', args.days),
        ('start-date/end-date', args.start_date or args.end_date)
    ]
    
    # 统计非空的时间参数
    active_params = [name for name, value in time_params if value]
    
    if len(active_params) > 1:
        param_list = '、'.join(f'--{name}' for name in active_params)
        raise ValueError(f"时间范围参数互斥，请只使用一个：{param_list}")
    
    # 处理各种时间参数
    if args.date:
        # 解析并设置日期范围
        try:
            args.start_date, args.end_date = parse_single_date(args.date)
            logger.info(f"使用指定日期: {args.date} (完整时间范围: {args.start_date} 到 {args.end_date})")
        except argparse.ArgumentTypeError as e:
            raise ValueError(str(e))
    
    elif args.yesterday:
        # 设置昨天的时间范围
        args.start_date, args.end_date = get_yesterday_range()
        logger.info(f"使用昨天数据: {args.start_date.strftime('%Y-%m-%d')} (完整时间范围: {args.start_date} 到 {args.end_date})")
    
    elif args.last_week:
        # 设置上周的时间范围
        args.start_date, args.end_date = get_last_week_range()
        logger.info(f"使用上周数据: {args.start_date.strftime('%Y-%m-%d')} 到 {args.end_date.strftime('%Y-%m-%d')} (完整时间范围: {args.start_date} 到 {args.end_date})")
    
    elif args.days and (args.start_date or args.end_date):
        raise ValueError("--days 参数不能与 --start-date/--end-date 同时使用")
    
    # 现有的日期范围验证逻辑保持不变
    if args.start_date and args.end_date and args.start_date > args.end_date:
        raise ValueError("开始日期不能晚于结束日期")
    
    # 检查飞书相关参数
    if args.send_feishu:
        # 获取飞书Webhook URL
        webhook_url = args.feishu_webhook or os.getenv('FEISHU_WEBHOOK_URL')
        if not webhook_url:
            raise ValueError("发送飞书消息需要指定 --feishu-webhook 或设置环境变量 FEISHU_WEBHOOK_URL")
        args.feishu_webhook = webhook_url
        
        # 如果需要上传报告，检查相关参数
        if not args.no_upload_report:
            base_url = args.report_base_url or os.getenv('REPORT_BASE_URL')
            upload_path = args.upload_path or os.getenv('REPORT_UPLOAD_PATH')
            
            if not base_url:
                logger.warning("未指定报告基础URL，将不会在飞书消息中包含报告链接")
                args.no_upload_report = True
            else:
                args.report_base_url = base_url
            
            if not upload_path:
                logger.warning("未指定报告上传路径，将不会上传HTML报告")
                args.no_upload_report = True
            else:
                args.upload_path = upload_path
                # 确保上传目录存在
                Path(upload_path).mkdir(parents=True, exist_ok=True)
    
    # 检查输出路径
    if args.output:
        output_path = Path(args.output)
        
        # 如果指定的是目录，自动生成文件名
        if output_path.is_dir() or args.output.endswith('/') or args.output.endswith('\\'):
            # 确保目录存在
            if not output_path.exists():
                output_path.mkdir(parents=True, exist_ok=True)
            
            # 生成带时间戳的文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'trade_statistics_{timestamp}.{args.format}'
            args.output = str(output_path / filename)
            logger.info(f"输出目录已指定，将保存为: {args.output}")
        else:
            # 检查输出文件扩展名
            expected_ext = f'.{args.format}'
            if not output_path.suffix.lower() == expected_ext:
                logger.warning(f"输出文件扩展名建议使用 {expected_ext}")


def generate_default_output_path(format_type: str, output_dir: Optional[str] = None) -> str:
    """
    生成默认输出路径
    
    Args:
        format_type: 报告格式
        output_dir: 可选的输出目录
        
    Returns:
        str: 默认输出路径
    """
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f'trade_statistics_{timestamp}.{format_type}'
    
    # 如果指定了输出目录，使用该目录
    if output_dir:
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        return str(output_path / filename)
    
    # 否则使用默认目录
    output_dir = Path('reports/trading_statistics')
    output_dir.mkdir(parents=True, exist_ok=True)
    
    return str(output_dir / filename)


async def run_analysis(args) -> None:
    """
    运行交易统计分析
    
    Args:
        args: 命令行参数
    """
    try:
        # 初始化数据库
        logger.info("初始化数据库连接...")
        await init_db()
        
        # 构建配置
        config = StatisticsConfig()
        
        # 设置时间范围
        if args.date:
            # --date参数已在validate_args中转换为start_date和end_date
            config.start_date = args.start_date
            config.end_date = args.end_date
            logger.info(f"分析指定日期: {args.date} ({config.start_date.strftime('%Y-%m-%d %H:%M:%S')} 到 {config.end_date.strftime('%Y-%m-%d %H:%M:%S')})")
        elif args.yesterday:
            # --yesterday参数已在validate_args中转换为start_date和end_date
            config.start_date = args.start_date
            config.end_date = args.end_date
            logger.info(f"分析昨天数据: {config.start_date.strftime('%Y-%m-%d')} ({config.start_date.strftime('%Y-%m-%d %H:%M:%S')} 到 {config.end_date.strftime('%Y-%m-%d %H:%M:%S')})")
        elif args.last_week:
            # --last-week参数已在validate_args中转换为start_date和end_date
            config.start_date = args.start_date
            config.end_date = args.end_date
            logger.info(f"分析上周数据: {config.start_date.strftime('%Y-%m-%d')} 到 {config.end_date.strftime('%Y-%m-%d')} ({config.start_date.strftime('%Y-%m-%d %H:%M:%S')} 到 {config.end_date.strftime('%Y-%m-%d %H:%M:%S')})")
        elif args.days:
            config.end_date = datetime.now()
            config.start_date = config.end_date - timedelta(days=args.days)
            logger.info(f"分析时间范围: {config.start_date.strftime('%Y-%m-%d')} 到 {config.end_date.strftime('%Y-%m-%d')}")
        else:
            config.start_date = args.start_date
            config.end_date = args.end_date
            if config.start_date:
                logger.info(f"开始日期: {config.start_date.strftime('%Y-%m-%d')}")
            if config.end_date:
                logger.info(f"结束日期: {config.end_date.strftime('%Y-%m-%d')}")
        
        # 设置过滤条件
        config.strategy_filter = args.strategies
        config.token_filter = args.tokens
        
        # 设置Token链接配置
        config.token_link_base_url = args.token_link_base_url
        config.enable_token_links = not args.disable_token_links
        
        if config.strategy_filter:
            logger.info(f"策略过滤: {', '.join(config.strategy_filter)}")
        if config.token_filter:
            logger.info(f"Token过滤: {', '.join(config.token_filter)}")
        
        # 输出Token链接配置信息
        if config.enable_token_links:
            logger.info(f"Token链接功能已启用，基础URL: {config.token_link_base_url}")
        else:
            logger.info("Token链接功能已禁用")
        
        # 创建分析器
        trade_record_dao = TradeRecordDAO()
        verification_updater = TradeRecordVerificationUpdater()
        analyzer = TradeStatisticsAnalyzer(
            trade_record_dao=trade_record_dao,
            verification_updater=verification_updater
        )
        
        # 数据验证
        if args.validate_data:
            logger.info("执行数据完整性验证...")
            validation_result = await analyzer.validate_data_integrity(config)
            logger.info(f"数据验证结果: {validation_result}")
        
        # 执行分析
        logger.info("开始执行交易统计分析...")
        
        # 确定输出路径
        output_path = args.output or generate_default_output_path(args.format)
        
        # 设置报告格式
        report_format = ReportFormat.HTML if args.format == 'html' else ReportFormat.JSON
        
        # 生成并保存报告
        await analyzer.save_report(
            file_path=output_path,
            format=report_format,
            config=config
        )
        
        # 获取统计结果用于显示摘要
        result = await analyzer.analyze(config)
        
        # 输出结果摘要
        logger.info("=" * 60)
        logger.info("交易统计分析完成")
        logger.info("=" * 60)
        
        if result.overall_stats:
            stats = result.overall_stats
            logger.info(f"总交易对数: {stats.total_trades}")
            logger.info(f"胜率: {stats.total_win_rate:.2f}%")
            logger.info(f"总盈利率: {stats.total_profit_rate:.2f}%")
            logger.info(f"平均盈利率: {stats.avg_profit_rate:.2f}%")
        
        logger.info(f"报告已生成: {output_path}")
        
        # 如果是HTML格式，提示用户打开浏览器
        if args.format == 'html':
            logger.info(f"请在浏览器中打开: file://{Path(output_path).absolute()}")
        
        # 处理飞书消息发送
        if args.send_feishu:
            await handle_feishu_notification(args, result, output_path)
        
    except Exception as e:
        logger.error(f"分析执行失败: {str(e)}")
        if args.verbose:
            import traceback
            logger.error(traceback.format_exc())
        sys.exit(1)


async def handle_feishu_notification(args, stats_result, output_path: str) -> None:
    """处理飞书消息发送
    
    Args:
        args: 命令行参数
        stats_result: 统计分析结果
        output_path: 生成的报告文件路径
    """
    try:
        logger.info("开始处理飞书消息发送...")
        
        # 导入飞书相关模块
        from utils.message_sender import FeishuMessageSender
        from utils.trading.statistics.summary_generator import TradingSummaryGenerator
        from utils.trading.statistics.report_uploader import ReportUploader
        
        # 上传HTML报告（如果需要）
        report_url = None
        if not args.no_upload_report and args.format == 'html' and args.report_base_url and args.upload_path:
            logger.info("开始上传HTML报告...")
            uploader = ReportUploader(
                upload_path=args.upload_path,
                base_url=args.report_base_url
            )
            
            # 生成文件名
            if hasattr(args, 'yesterday') and args.yesterday:
                period_type = "昨日"
            elif hasattr(args, 'last_week') and args.last_week:
                period_type = "上周"
            elif hasattr(args, 'date') and args.date:
                period_type = "日"
            elif args.period == "daily":
                period_type = "日"
            elif args.period == "weekly":
                period_type = "周"
            else:
                period_type = "自定义"
            
            start_date = stats_result.data_range.get("start_date")
            end_date = stats_result.data_range.get("end_date")
            
            if start_date and end_date:
                start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00')) if isinstance(start_date, str) else start_date
                end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00')) if isinstance(end_date, str) else end_date
                filename = uploader.generate_filename(period_type, start_dt, end_dt)
            else:
                filename = uploader.generate_filename(period_type)
            
            # 读取HTML文件内容
            try:
                with open(output_path, 'r', encoding='utf-8') as f:
                    html_content = f.read()
                
                # 上传HTML内容（移除await，因为upload_html_report是同步方法）
                upload_result = uploader.upload_html_report(html_content, filename)
                
                if upload_result.get("success", False):
                    report_url = upload_result["url"]  # 使用正确的键名
                    logger.info(f"HTML报告上传成功: {report_url}")
                else:
                    logger.error(f"HTML报告上传失败: {upload_result.get('error', '未知错误')}")
            except Exception as e:
                logger.error(f"读取HTML文件失败: {str(e)}")
        
        # 生成交易摘要
        logger.info("生成交易摘要数据...")
        summary_generator = TradingSummaryGenerator()
        
        # 根据参数类型确定周期类型和摘要生成方式
        if hasattr(args, 'yesterday') and args.yesterday:
            # 使用--yesterday参数，确定为昨日报
            summary_data = await summary_generator.generate_custom_summary(
                start_date=args.start_date,
                end_date=args.end_date,
                strategies=args.strategies,
                tokens=args.tokens
            )
        elif hasattr(args, 'last_week') and args.last_week:
            # 使用--last-week参数，确定为上周报
            summary_data = await summary_generator.generate_custom_summary(
                start_date=args.start_date,
                end_date=args.end_date,
                strategies=args.strategies,
                tokens=args.tokens
            )
        elif hasattr(args, 'date') and args.date:
            # 使用--date参数，确定为单日报
            summary_data = await summary_generator.generate_custom_summary(
                start_date=args.start_date,
                end_date=args.end_date,
                strategies=args.strategies,
                tokens=args.tokens
            )
        elif args.period == "daily":
            target_date = datetime.now() - timedelta(days=1) if args.days == 1 else datetime.now()
            summary_data = await summary_generator.generate_daily_summary(
                target_date=target_date,
                strategies=args.strategies,
                tokens=args.tokens
            )
        elif args.period == "weekly":
            summary_data = await summary_generator.generate_weekly_summary(
                strategies=args.strategies,
                tokens=args.tokens
            )
        else:
            # 自定义周期
            summary_data = await summary_generator.generate_custom_summary(
                days=args.days,
                start_date=args.start_date,
                end_date=args.end_date,
                strategies=args.strategies,
                tokens=args.tokens
            )
        
        # 创建飞书消息发送器
        logger.info("初始化飞书消息发送器...")
        feishu_sender = FeishuMessageSender(
            webhook_url=args.feishu_webhook,
            max_retries=3,
            retry_delay=1.0
        )
        
        # 发送消息
        logger.info("发送飞书消息...")
        if args.feishu_message_type == "card":
            # 发送卡片消息
            success = await feishu_sender.send_trading_summary_card(summary_data, report_url)
        else:
            # 发送文本消息
            from utils.trading.statistics.summary_generator import SummaryFormatter
            text_message = SummaryFormatter.format_as_text(summary_data)
            if report_url:
                text_message += f"\n\n📋 详细报告: {report_url}"
            success = await feishu_sender.send_text_message(text_message)
        
        if success:
            logger.info("✅ 飞书消息发送成功")
        else:
            logger.error("❌ 飞书消息发送失败")
            
    except ImportError as e:
        logger.error(f"导入飞书相关模块失败: {e}")
        logger.error("请确保已正确安装相关依赖")
    except Exception as e:
        logger.error(f"飞书消息发送过程中发生错误: {str(e)}")
        if args.verbose:
            import traceback
            logger.error(traceback.format_exc())


def main():
    """
    主函数
    """
    try:
        # 解析命令行参数
        parser = create_parser()
        args = parser.parse_args()
        
        # 设置日志级别
        if args.verbose:
            logging.getLogger().setLevel(logging.DEBUG)
        
        # 验证参数
        validate_args(args)
        
        # 运行分析
        asyncio.run(run_analysis(args))
        
    except KeyboardInterrupt:
        logger.info("用户中断操作")
        sys.exit(0)
    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main() 