# memeMonitor 项目

## 项目概述

memeMonitor 是一个基于节点和工作流概念的数据处理系统，专注于爬取、处理和分析与加密货币相关的数据。系统利用代理 IP 进行网络爬虫操作，以提高数据获取的稳定性和效率。

## 项目结构

```
memeMonitor/
├── api/                    # REST API 接口实现
│   └── v1/                 # API v1 版本
│       ├── schemas.py      # API 数据模式定义
│       └── signal_api.py   # 信号相关 API 端点（支持双重策略过滤）
├── dao/                    # 数据访问对象 (DAO) 层，封装数据库 CRUD 操作
│   ├── signal_dao.py       # 信号数据访问对象
│   ├── trade_record_dao.py # 交易记录数据访问对象
│   ├── config_dao.py       # 配置数据访问对象
│   ├── kol_wallet_dao.py   # KOL钱包数据访问对象
│   ├── token_dao.py        # 代币数据访问对象
│   ├── gmgn_*_dao.py       # GMGN平台各类数据访问对象
│   ├── base_dao.py         # DAO基类定义
│   └── ...                 # 其他 DAO 文件 (50+ 个数据访问对象)
├── deploy/                 # 部署相关的配置文件和脚本
│   ├── nginx/              # Nginx 配置
│   └── supervisor/         # Supervisor 配置
├── docs/                   # 项目文档
│   ├── features/           # 功能特性文档
│   │   └── 0.1.0/          # 版本 0.1.0 的功能文档
│   │       ├── apis/       # API 相关文档
│   │       │   ├── 获取信号记录列表（分页）.md    # 信号列表API文档（含过滤逻辑）
│   │       │   └── generated_by_ai/             # AI生成的API功能文档
│   │       │       ├── filter_original_buy_strategy_*.md  # 过滤功能相关文档
│   │       │       └── ...                      # 其他AI生成文档
│   │       ├── backtesting/# 回测功能文档
│   │       ├── monitors/   # 监控功能文档
│   │       ├── trading/    # 交易功能文档
│   │       ├── monitor_kol_activity/   # KOL 活动监控文档
│   │       │   └── fixes/              # Bug 修复记录
│   │       ├── workflows/  # 工作流功能文档
│   │       └── 自动交易/                # 自动交易文档
│   │           ├── 借助Gmgn平台的自动交易/  # GMGN 平台交易文档
│   │           │   ├── fixes/              # Bug 修复记录
│   │           │   └── *.md                # 需求、设计、测试文档
│   │           ├── 直接Solana链交易/         # 直接 Solana 交易文档
│   │           │   ├── fixes/              # Bug 修复记录
│   │           │   ├── README.md           # 说明文档
│   │           │   ├── solana_direct_trading_guide.md     # 使用指南
│   │           │   └── solana_direct_trading_todo_list.md # 任务清单
│   │           └── 滑点递增重试功能/         # 滑点递增重试功能文档
│   │               ├── slippage_retry_enhancement_requirements_ai.md  # 需求规格
│   │               ├── slippage_retry_enhancement_dev_plan_ai.md       # 技术实现方案
│   │               ├── slippage_retry_enhancement_test_cases_ai.md     # 测试用例设计
│   │               ├── slippage_retry_enhancement_final_api.md         # 最终API文档
│   │               └── slippage_retry_enhancement_todo_list.md         # 任务清单
│   ├── examples/           # 示例文档
│   └── project/            # 项目级文档
│       └── PROJECT_OVERVIEW.md  # 项目概述文件 (此文件)
├── models/                 # 数据模型定义 (基于 Beanie ODM)
│   ├── __init__.py         # 模型初始化，注册所有数据模型
│   ├── signal.py           # 信号数据模型
│   ├── trade_record.py     # 交易记录数据模型
│   ├── trade_execution.py  # 交易执行结果模型 (含滑点重试字段)
│   ├── config.py           # 配置数据模型 (含 Solana 直接交易和滑点重试配置)
│   ├── slippage_retry.py   # 滑点重试相关数据模型
│   ├── channel_attempt.py  # 交易渠道尝试记录模型
│   ├── dynamic_retry_config.py  # 动态重试配置模型
│   ├── kol_wallet.py       # KOL钱包数据模型
│   ├── kol_wallet_activity.py  # KOL钱包活动数据模型
│   ├── token.py            # 代币数据模型
│   ├── token_message_send_history.py  # 代币消息发送历史模型
│   ├── gmgn_*.py           # GMGN平台各类数据模型 (30+ 个)
│   ├── smart_money_*.py    # 智能资金相关模型
│   ├── x_user.py           # X用户模型
│   ├── tweet.py            # 推文模型
│   ├── solana_*.py         # Solana区块链相关模型
│   ├── alert_event_record.py    # 告警事件记录模型
│   ├── monitor_alert_state.py   # 监控告警状态模型
│   ├── notification_log_record.py  # 通知日志记录模型
│   ├── telegram_users.py   # Telegram用户模型
│   ├── error_log.py        # 错误日志模型
│   ├── task.py             # 任务模型
│   └── ...                 # 其他数据模型 (60+ 个模型文件)
├── test/                   # 单元测试和集成测试代码
│   ├── api/                # API 层测试
│   │   └── v1/             # API v1 测试
│   │       └── test_signal_api_filter.py  # 信号API过滤功能测试
│   ├── dao/                # DAO 层测试
│   ├── models/             # 数据模型测试
│   ├── utils/              # 工具类测试
│   │   └── trading/        # 交易服务测试
│   │       ├── slippage_retry/  # 滑点重试功能测试
│   │       │   ├── test_slippage_calculator.py         # 滑点计算器测试
│   │       │   ├── test_retry_decision_engine.py       # 重试决策引擎测试
│   │       │   ├── test_retry_delay_calculator.py      # 重试间隔计算器测试
│   │       │   ├── test_parameter_merger.py            # 参数合并器测试
│   │       │   └── test_slippage_retry_integration.py  # 滑点重试集成测试
│   │       └── solana/     # Solana 交易服务测试
│   │           ├── test_solana_direct_trade_service.py  # Solana 直接交易服务测试
│   │           ├── test_solana_direct_trade_service.md  # 测试文档
│   │           └── test_gmgn_trade_service.py           # GMGN 交易服务测试
│   └── workflows/          # 工作流测试
├── utils/                  # 通用工具类和核心功能模块
│   ├── backtest/           # 回测引擎
│   ├── backtest_analysis/  # 回测分析工具
│   ├── backtest_event_driven/  # 事件驱动回测
│   ├── connectors/         # 数据库 (MongoDB) 和其他外部服务的连接器
│   ├── message_sender/     # 消息发送工具
│   ├── proxy/              # 代理管理
│   ├── spiders/            # 爬虫实现
│   │   ├── smart_money/    # Gmgn 智能资金相关爬虫
│   │   ├── solana/         # Solana 区块链相关爬虫
│   │   ├── x/              # Twitter(X) 平台爬虫
│   │   ├── gmgn/           # GMGN 平台专用爬虫
│   │   └── gmgn_token_links/  # GMGN代币链接爬虫
│   ├── strategies/         # 交易策略实现
│   ├── workflows/          # 工作流引擎核心实现
│   │   ├── nodes/          # 预定义的工作流节点实现
│   │   ├── message_queue/  # 消息队列系统
│   │   └── *.py            # 工作流、节点基类和管理器等
│   ├── trading/            # **已重构完成** 交易服务目录
│   │   ├── __init__.py     # 包初始化，导出常用交易组件  
│   │   ├── auto_trade_manager.py      # 自动交易管理器
│   │   ├── trade_orchestrator.py      # 交易编排器
│   │   ├── channel_selector.py        # 交易渠道选择器
│   │   ├── channel_registry.py        # 交易渠道注册器
│   │   ├── trade_record_manager.py    # 交易记录管理器
│   │   ├── config_manager.py          # 配置管理器
│   │   ├── config_cli.py              # 配置命令行工具
│   │   ├── init_auto_trade_config.py  # 自动交易配置初始化
│   │   ├── migrate_trade_config.py    # 交易配置迁移脚本
│   │   ├── migrate_slippage_config.py # 滑点配置迁移脚本
│   │   ├── trade_record_verification_updater.py  # 交易记录验证更新器
│   │   ├── statistics/     # 交易统计模块
│   │   ├── slippage_retry/ # 滑点递增重试功能模块
│   │   │   ├── __init__.py                 # 滑点重试模块初始化
│   │   │   ├── slippage_calculator.py      # 滑点计算器
│   │   │   ├── retry_decision_engine.py    # 重试决策引擎
│   │   │   ├── retry_delay_calculator.py   # 重试间隔计算器
│   │   │   ├── parameter_merger.py         # 参数合并器
│   │   │   └── retry_context.py            # 重试上下文
│   │   └── solana/         # Solana 区块链交易服务
│   │       ├── __init__.py             # Solana 交易包初始化
│   │       ├── trade_interface.py      # 交易接口定义
│   │       ├── gmgn_trade_service.py   # GMGN 交易服务
│   │       ├── gmgn_trade_service_v2.py # GMGN 交易服务 V2
│   │       ├── jupiter_trade_service.py # Jupiter 交易服务
│   │       └── gmgn_test.mjs          # GMGN 交易脚本 (Node.js)
│   ├── session.py          # 代理会话管理
│   ├── common.py           # 通用工具函数
│   ├── basic_utils.py      # 基础工具函数
│   ├── kol_trading_analysis.py     # KOL交易分析工具
│   ├── kol_trading_visualization.py # KOL交易可视化工具
│   └── mail_helper.py      # 邮件发送助手
├── workflows/              # 工作流配置文件和处理器 (YAML 格式)
│   ├── monitor_kol_activity/     # KOL 活动监控工作流
│   ├── gmgn_*/                   # GMGN 相关工作流 (40+ 个)
│   │   ├── gmgn_token_*/         # GMGN代币相关工作流
│   │   ├── gmgn_kol_*/           # GMGN KOL相关工作流
│   │   ├── gmgn_whale_*/         # GMGN鲸鱼活动工作流
│   │   └── ...                   # 其他GMGN工作流
│   ├── smart_money/              # 智能资金分析工作流
│   ├── smart_money_kol/          # 智能资金KOL工作流
│   ├── solana_monitor/           # Solana 监控工作流
│   ├── spider_x_user_info/       # X用户信息爬取工作流
│   ├── spider_x_user_info_scrapy/ # X用户信息Scrapy爬取工作流
│   ├── trade_record_verification_updater/ # 交易记录验证更新工作流
│   ├── kol_activity_timestamp_discrepancy_monitor/ # KOL活动时间差异监控
│   └── ...                       # 其他工作流实现
├── scripts/                # 项目脚本文件
├── scrapy_spiders/         # Scrapy 爬虫实现
├── logs/                   # 日志文件目录
├── backtest_result/        # 回测结果存储目录
├── reports/                # 报告生成目录
├── .env                    # 环境变量配置文件 (数据库连接、代理信息等)
├── main.py                 # 项目主程序入口
├── run_jobs.py             # 定时任务运行脚本，调度和执行周期性任务
├── run_workflow.py         # 工作流运行脚本，加载并执行指定的工作流配置
├── run_backtest.py         # 回测运行脚本
├── run_backtest_ed.py      # 事件驱动回测运行脚本
├── filter_by_kelly.py      # Kelly准则过滤脚本
├── gunicorn_conf.py        # Gunicorn配置文件
├── package.json            # Node.js 依赖配置
├── package-lock.json       # Node.js 依赖锁定文件
├── pyproject.toml          # Python 项目配置 (Poetry)
├── poetry.lock             # Poetry 依赖锁定文件
├── setup.py                # 项目安装配置
├── README.md               # 项目说明文件
└── CHANGELOG.md            # 项目变更日志
```

## 核心模块功能

*   **`api/`**: REST API 接口层，提供 HTTP API 服务。
    *   `v1/signal_api.py`: 信号数据的 API 端点，支持分页查询、双重策略过滤等功能。
    *   `v1/schemas.py`: API 数据模式和验证定义。
*   **`dao/`**: 数据访问对象层，负责与数据库交互，提供结构化的数据访问接口，将数据库操作与业务逻辑分离。
    *   `signal_dao.py`: 封装对 `signals` 集合的操作。
    *   `trade_record_dao.py`: 封装交易记录数据的 CRUD 操作。
    *   `config_dao.py`: 封装配置数据的管理操作。
    *   `kol_wallet_dao.py`: 封装KOL钱包数据的操作。
    *   `token_dao.py`: 封装代币数据的操作。
    *   `gmgn_*_dao.py`: 封装GMGN平台各类数据的操作。
    *   `base_dao.py`: 提供所有DAO的基础功能和通用操作。
    *   包含 50+ 个 DAO 文件，覆盖所有主要数据模型。
*   **`models/`**: 数据模型定义层，基于 Beanie ODM，定义项目中使用的数据结构，映射到 MongoDB 集合。
    *   `models/__init__.py`: 负责初始化数据库连接和注册所有模型。
    *   `signal.py`: 定义监控系统产生的信号数据结构。
    *   `trade_record.py`: 定义交易记录的数据结构。
    *   `trade_execution.py`: 定义交易执行结果的数据结构，包含滑点重试相关字段。
    *   `config.py`: 定义配置数据结构，包含 Solana 直接交易和滑点递增重试的配置项。
    *   `slippage_retry.py`: 定义滑点重试相关的数据结构。
    *   `channel_attempt.py`: 定义交易渠道尝试记录的数据结构。
    *   `dynamic_retry_config.py`: 定义动态重试配置的数据结构。
    *   `kol_wallet.py`: 定义KOL钱包的数据结构。
    *   `token.py`: 定义代币的数据结构。
    *   `gmgn_*.py`: 定义GMGN平台各类数据的结构。
    *   `token_message_send_history.py`: 定义消息发送历史记录，关联到具体的信号 (`signal_id`)。
    *   包含 60+ 个数据模型文件，覆盖所有业务实体。
*   **`utils/`**: 通用工具和核心功能模块。
    *   **`connectors/`**: 管理与外部服务的连接，如 `mongodb.py` 用于建立和管理 MongoDB 连接。
    *   **`spiders/`**: 包含所有爬虫的实现。
        *   爬虫基类 (`utils/spiders/smart_money/__init__.py`) 提供了代理切换和请求重试等通用逻辑。
        *   子目录按爬取目标或类型组织爬虫代码：`smart_money/`、`solana/`、`x/`、`gmgn/`、`gmgn_token_links/`。
    *   **`workflows/`**: 实现了工作流引擎的核心功能。
        *   `workflow.py`: 定义工作流的基本结构和执行逻辑。
        *   `node.py`: 定义了不同类型的处理节点基类和实现。
        *   `workflow_config.py`: 负责解析 YAML 配置文件并构建工作流实例。
        *   `workflow_manager.py`: 管理和调度多个工作流的运行。
        *   `message_queue/`: 实现节点间的数据传递机制。
    *   **`backtest/`**: 回测引擎，支持策略历史数据回测。
    *   **`backtest_event_driven/`**: 事件驱动回测引擎，提供更精确的回测模拟。
    *   **`backtest_analysis/`**: 回测结果分析工具。
    *   **`strategies/`**: 交易策略实现，包含买入和卖出策略。
    *   **交易服务**:
        *   `trading/`: **已重构完成** 统一的交易服务目录，按功能模块组织。
            *   `__init__.py`: 包级导出，简化常用交易组件的导入路径。
            *   `auto_trade_manager.py`: 自动交易管理器，负责整体交易流程协调。
            *   `trade_orchestrator.py`: 交易编排器，实现交易执行的核心逻辑。
            *   `channel_selector.py`: 交易渠道选择器，支持多渠道交易路由。
            *   `channel_registry.py`: 交易渠道注册器，管理可用的交易服务。
            *   `trade_record_manager.py`: 交易记录管理器，处理交易结果存储。
            *   `config_manager.py`: 配置管理器，统一管理交易相关配置。
            *   `statistics/`: 交易统计分析模块。
            *   `slippage_retry/`: **新增** 滑点递增重试功能模块。
                *   `slippage_calculator.py`: 滑点递增计算逻辑。
                *   `retry_decision_engine.py`: 重试决策引擎，判断是否需要滑点调整。
                *   `retry_delay_calculator.py`: 重试间隔计算器，支持多种间隔策略。
                *   `parameter_merger.py`: 多层级配置参数合并器。
                *   `retry_context.py`: 重试上下文状态跟踪。
            *   `solana/`: Solana 区块链交易服务完整实现。
                *   `trade_interface.py`: 定义统一的交易接口规范，包含滑点错误识别方法。
                *   `gmgn_trade_service.py`: GMGN 平台交易服务实现。
                *   `gmgn_trade_service_v2.py`: GMGN 平台交易服务 V2 版本。
                *   `jupiter_trade_service.py`: Jupiter 聚合器交易服务实现。
                *   `gmgn_test.mjs`: GMGN 交易脚本 (Node.js)，由 Python 服务调用。
    *   `session.py`: 代理会话管理，用于爬虫的反反爬虫机制。
    *   `kol_trading_analysis.py`: KOL交易行为分析工具。
    *   `kol_trading_visualization.py`: KOL交易数据可视化工具。
*   **`workflows/`**: 存放具体的工作流定义文件（YAML格式）和处理器，描述了数据处理的流程和节点配置。
    *   包含 50+ 个工作流，涵盖 GMGN 数据抓取、KOL 监控、智能资金分析、Solana监控等。
    *   `monitor_kol_activity/`: KOL 活动监控和自动交易工作流。
    *   `trade_record_verification_updater/`: 交易记录验证更新工作流。
*   **`docs/`**: 项目文档系统，按功能模块和版本组织。
    *   `features/0.1.0/`: 版本 0.1.0 的功能文档，包含需求、设计、测试、Bug修复等完整记录。

## 关键脚本

*   **`run_workflow.py`**: 用于启动和执行在 `workflows/` 目录下定义的特定工作流。
*   **`run_jobs.py`**: 用于运行预定义的定时任务，例如定期执行某个爬虫或工作流。
*   **`run_backtest.py`**: 运行传统回测引擎，支持策略历史数据回测。
*   **`run_backtest_ed.py`**: 运行事件驱动回测引擎，提供更精确的时序模拟。
*   **`filter_by_kelly.py`**: 基于Kelly准则的投资组合过滤脚本。
*   **`.env`**: 存储敏感配置信息，如数据库凭证、代理服务器地址等，不应提交到版本控制。
*   **`setup.py`**: 使用 Poetry 进行项目依赖管理和打包。

## 核心概念

*   **工作流 (Workflow)**: 由一系列相互连接的节点 (Node) 组成，定义了数据从获取到处理、存储或分析的完整流程。支持并行执行、消息传递、错误处理等高级特性。
*   **节点 (Node)**: 工作流中的基本处理单元，负责执行特定的任务，如数据爬取、数据转换、数据存储、信号生成、交易执行等。
*   **信号 (Signal)**: 系统监控到的有价值的市场事件，如 KOL 的买入/卖出活动，作为自动交易的触发条件。
*   **交易接口 (Trade Interface)**: 统一的交易抽象层，支持多种交易实现：
    *   **GMGN 交易服务**: 通过 GMGN 平台 API 进行交易。
    *   **Jupiter 交易服务**: **新增** 直接与 Solana 区块链交互，通过 Jupiter 聚合器获取最佳路由并执行交易。
*   **滑点递增重试 (Slippage Retry Enhancement)**: **新增** 智能滑点调整机制，在交易失败时自动递增滑点并重试，显著提高交易成功率。
*   **代理会话 (Proxy Session)**: `utils/session.py` 中实现，用于通过代理 IP 发起 HTTP 请求，规避反爬虫机制。
*   **数据模型 (Data Model)**: 使用 Beanie ODM 定义，将 Python 对象映射到 MongoDB 文档，简化数据库操作。
*   **DAO 模式**: 数据访问对象模式，将数据库操作封装在专门的类中，与业务逻辑分离。
*   **策略配置**: 灵活的配置系统，支持多种交易策略参数、风险控制、通知设置等。
*   **事件驱动回测**: 基于事件队列的回测引擎，能够更准确地模拟真实交易环境中的时序和延迟。

## 如何运行

1.  **安装依赖**: 使用 Poetry 安装项目所需的库：`poetry install`
2.  **配置环境**: 创建 `.env` 文件并填入必要的环境变量（数据库连接信息、代理设置等）。
3.  **运行工作流**: `python run_workflow.py <workflow_name.yaml>`
4.  **运行定时任务**: `python run_jobs.py`
5.  **运行回测**: `python run_backtest.py` 或 `python run_backtest_ed.py`

## 代码规范和原则

*   遵循项目内定义的命名规范、文档规范和错误处理机制。
*   爬虫开发需继承 `BasicSpider`，使用代理 IP，并遵守目标网站规则。
*   数据库操作通过 DAO 层进行，使用异步方式。
*   敏感信息通过环境变量管理。
*   注重代码性能和可测试性。 
*   当修改代码框架时，确保更新此文档。
*   当修改代码时，除非必要（用户需求），否则不要修改其他无关的代码。

## 最新重要更新

### 🎯 **API过滤功能增强 (2025-05-27)**
*   **核心功能**: 扩展信号列表API的过滤机制，增加对`original_buy_strategy_name`字段的过滤
*   **关键特性**:
    *   **双重过滤机制**: 同时过滤`strategy_name`和`original_buy_strategy_name`为"测试"的记录
    *   **智能字段处理**: 支持字段不存在、null值、空字符串等边界情况
    *   **大小写敏感**: 精确匹配"测试"字符串，避免误过滤
    *   **向后兼容**: 保持现有API接口不变，无破坏性更新
*   **技术实现**:
    *   使用MongoDB `$and` + 双`$or`查询结构
    *   优化的索引友好查询条件
    *   性能影响微乎其微（<2%）
*   **测试覆盖**: 新增6个专项测试用例，覆盖所有过滤场景和边界条件
*   **文档完善**: 详细的API文档更新，包含过滤逻辑说明和行为示例表格

### 🎯 **滑点递增重试功能 (2025-05-26)**
*   **核心功能**: 实现智能滑点递增重试机制，在交易因滑点不足失败时自动增加滑点并重试
*   **关键特性**:
    *   **智能错误识别**: 各交易接口自动识别滑点相关错误，精准触发滑点调整
    *   **多种重试策略**: 支持固定、线性、指数退避三种重试间隔策略
    *   **买卖差异化配置**: 支持买入和卖出操作的独立滑点重试配置
    *   **四级配置层次**: 运行时 > 策略 > 渠道 > 全局的配置覆盖体系
    *   **完整状态跟踪**: 详细记录每次滑点调整的历史和原因
*   **技术架构**:
    *   `utils/trading/slippage_retry/`: 完整的滑点重试功能模块
    *   扩展 `TradingParams` 和 `ChannelAttemptResult` 数据模型
    *   集成到 `TradeOrchestrator` 和 `AutoTradeManager` 交易流程
*   **兼容性保证**: 默认禁用，完全向后兼容，可按策略渐进启用
*   **测试覆盖**: 100% 单元测试和集成测试覆盖，包含9个端到端测试场景
*   **文档完整**: 需求规格、技术方案、测试用例、API文档等完整文档体系

### 🚀 **Jupiter直接交易功能 (2025-05-25)**
*   **新增**: `utils/trading/solana/jupiter_trade_service.py` - 完全绕过 GMGN API，直接与 Solana 区块链交互
*   **特性**: 通过 Jupiter 聚合器获取最佳交易路由，本地签名，直接提交到 Solana RPC 节点
*   **优势**: 彻底解决 Cloudflare 反爬虫问题，更低成本，更高可靠性，更好的技术控制
*   **配置**: 扩展了 `models/config.py`，增加 Solana 直接交易相关配置项
*   **集成**: 已集成到 `workflows/monitor_kol_activity/` 工作流中，支持策略级别的交易服务选择
*   **测试**: 完整的单元测试覆盖，包括 Mock 测试和集成验证

### 📚 **文档体系完善**
*   **新增**: `docs/features/0.1.0/workflows/自动交易/直接Solana链交易/` 目录
*   **包含**: 使用指南、任务清单、Bug修复记录等完整文档
*   **结构**: 与现有 GMGN 交易文档保持一致的组织结构
*   **API文档**: 完善的信号列表API文档，包含详细的过滤逻辑说明

### 🔧 **交易接口架构升级**
*   **统一接口**: 通过 `TradeInterface` 抽象层支持多种交易实现
*   **动态选择**: 工作流可根据配置动态选择使用 GMGN 或 Jupiter 交易服务
*   **向后兼容**: 现有 GMGN 交易功能完全保持不变

### 🗂️ **交易服务目录重构 (2025-05-25)**
*   **重构完成**: 将所有交易相关文件整理到 `utils/trading/` 目录
*   **按功能组织**: 创建专门的功能子目录，如 `slippage_retry/`、`statistics/` 等
*   **按区块链组织**: 创建 `utils/trading/solana/` 子目录，包含所有 Solana 相关交易服务
*   **文件迁移**: 
    *   所有交易服务统一到 `utils/trading/` 目录结构
    *   增加了多个配置管理和迁移脚本
*   **导入路径更新**: 所有相关文件的导入路径已同步更新
*   **包初始化**: 添加了 `__init__.py` 文件，支持便捷的导入方式
*   **测试验证**: 所有测试用例已通过，确保重构的正确性

### 📊 **回测引擎优化 (2025-04-08)**
*   **事件驱动回测**: 解决了事件驱动回测结果不一致问题，提高了回测的确定性和可重现性
*   **信号检测频率优化**: 修复了回测框架与真实工作流在信号检测频率上的差异
*   **通知间隔模拟**: 在回测中模拟了真实系统的 `same_token_notification_interval` 机制
*   **性能优化**: 通过索引优化和查询改进，显著提升了聚合查询性能

---

**文档最后更新**: 2025-05-29 16:50 (CST) - 基于当前项目结构全面更新概述信息 