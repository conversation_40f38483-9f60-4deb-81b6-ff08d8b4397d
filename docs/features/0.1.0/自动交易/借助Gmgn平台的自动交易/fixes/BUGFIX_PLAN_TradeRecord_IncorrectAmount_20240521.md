## Bug修复方案：TradeRecord 未准确记录链上实际成交数量

**1. Bug 标识**

*   **Bug描述**: 系统在记录买入/卖出交易的实际成交代币数量 (特别是 `token_out_actual_amount` 和 `token_in_actual_amount`) 时，使用的是交易前从GMGN API获取的报价中的预期数量，而不是交易成功后从Solana链上获取的实际成交数量。这导致当发生滑点时，数据库记录与链上实际不符。
*   **相关模块**: `utils/gmgn_trade_service.py`, `gmgn_test.mjs` (由前者调用), `models/trade_record.py` (数据存储模型), `workflows/monitor_kol_activity/handler.py` (调用交易服务并创建记录的地方).

**2. 报告日期/发现日期**

*   发现日期: 2024-05-19

**3. 根源分析概要**

当前系统的工作流程如下：
1.  Python端的 `GmgnTradeService` 调用底层的 `gmgn_test.mjs` Node.js脚本执行交易。
2.  `gmgn_test.mjs` 脚本：
    a.  向 GMGN API 请求交易报价 (`/get_swap_route`)，获得预期输入/输出量。
    b.  对交易进行签名。
    c.  向 GMGN API 提交已签名的交易 (`/submit_signed_transaction`)，获得交易哈希。
    d.  轮询 GMGN API 的交易状态接口 (`/get_transaction_status`) 直到交易成功、失败或超时。
    e.  脚本将包含报价信息、提交信息和最终状态信息的JSON结果输出。
3.  `GmgnTradeService` 解析 `gmgn_test.mjs` 的输出，并使用报价中的预期数量 (如 `quote.outAmount`) 来填充 `TradeResult` 对象的 `actual_amount_out` 和 `actual_amount_in`。
4.  这些 `TradeResult` 中的值最终被保存到 `TradeRecord` 数据库文档中。

问题在于，步骤 2e 和 3 中使用的数量是基于**交易前报价**的，并未反映交易在链上执行时可能发生的滑点。

**4. 详细的、已获批准的修复方案**

**目标**: 修改系统，使其在交易成功后，从Solana链上直接获取该笔交易的实际代币流入/流出量，并用此数据更新 `TradeRecord`。

**步骤**:

*   **A. 修改 `gmgn_test.mjs` 脚本**
    1.  **依赖添加**: 确保 `@solana/web3.js` 已作为项目的依赖（通常在Node.js项目中管理，如果此脚本是独立执行的，则需要本地安装）。
    2.  **RPC连接**:
        *   在脚本中初始化一个 `@solana/web3.js` 的 `Connection` 对象。RPC节点URL应考虑可配置性（例如，通过环境变量或作为新的命令行参数传入脚本）。
        *   一个合理的默认或备用公共RPC可以先用于开发，例如 `https://api.mainnet-beta.solana.com`。
    3.  **获取并解析链上交易详情**:
        *   当 `gmgn_test.mjs` 轮询到交易状态为成功 (`finalStatusData.data.success === true`) 并且获得了交易哈希 (`txHash`) 后：
            *   调用 `connection.getParsedTransaction(txHash, { commitment: 'confirmed', maxSupportedTransactionVersion: 0 });` (或者 `connection.getTransaction(txHash, { commitment: 'confirmed', maxSupportedTransactionVersion: 0 })` 如果需要更底层的meta数据处理)。
            *   **错误处理**: 如果 `getParsedTransaction` 调用失败或返回 `null` (交易未找到或未达到确认状态)，则应记录错误，并使脚本输出中包含一个明确的指示，表明未能获取链上数据。
            *   从成功的 `getParsedTransaction` 响应中，重点关注 `meta.preTokenBalances` 和 `meta.postTokenBalances` 数组。同时，从交易指令或账户列表中识别出用户钱包地址 (`fromAddress` 或 `wallet.publicKey.toString()`) 拥有的、与 `INPUT_TOKEN_ADDRESS` 和 `OUTPUT_TOKEN_ADDRESS` 相关的代币账户。
            *   **计算实际变动量**:
                *   对于**输出代币** (用户收到的代币，例如 `OUTPUT_TOKEN_ADDRESS`):
                    *   找到该代币在用户钱包中的代币账户地址。
                    *   比较 `postTokenBalances` 和 `preTokenBalances` 中该代币账户的 `uiAmount` (或 `amount` 如果是原始lamports)。差值即为实际收到的UI数量。
                    *   获取该代币的 `decimals`。
                    *   将UI数量差值转换为lamports: `actual_ui_amount_received * (10^decimals)`。
                *   对于**输入代KOL** (用户花费的代币，例如 `INPUT_TOKEN_ADDRESS`):
                    *   类似地，找到该代币在用户钱包中的代币账户地址。
                    *   比较 `preTokenBalances` 和 `postTokenBalances` 中该代币账户的 `uiAmount`。差值（绝对值）即为实际花费的UI数量。
                    *   获取该代币的 `decimals`。
                    *   将UI数量差值转换为lamports: `actual_ui_amount_spent * (10^decimals)`。
            *   **注意**: 对于SOL转账（当输入或输出是原生SOL时），解析逻辑会涉及 `meta.preBalances` 和 `meta.postBalances` (钱包地址的SOL余额变化)。
    4.  **扩展脚本输出**:
        *   在 `gmgn_test.mjs` 通过 `outputResult()` 输出的JSON对象中，新增以下字段：
            *   `onchain_actual_amount_in_lamports: string` (实际花费的输入代币lamports数量)
            *   `onchain_input_decimals: number` (输入代币的小数位数)
            *   `onchain_actual_amount_out_lamports: string` (实际收到的输出代币lamports数量)
            *   `onchain_output_decimals: number` (输出代币的小数位数)
            *   `onchain_data_fetch_status: "success" | "failed_to_fetch" | "not_attempted"` (标记链上数据获取状态)
        *   如果未能成功获取链上数据，这些 `onchain_` 字段可以为 `null` 或不包含，但 `onchain_data_fetch_status` 应指明原因。

*   **B. 修改 `utils/gmgn_trade_service.py` 中的 `GmgnTradeService`**
    1.  **解析增强的脚本输出**:
        *   在 `execute_trade` 方法中，当Node.js脚本执行完毕并返回JSON字符串后，解析它。
        *   检查是否存在新的 `onchain_actual_amount_in_lamports`, `onchain_actual_amount_out_lamports`, `onchain_input_decimals`, `onchain_output_decimals` 和 `onchain_data_fetch_status` 字段。
    2.  **更新 `TradeResult` 对象**:
        *   如果 `onchain_data_fetch_status` 为 `"success"` 并且相关 `onchain_` 金额字段存在且有效：
            *   将 `TradeResult.actual_amount_in` 设置为 `int(parsed_json['onchain_actual_amount_in_lamports'])`。
            *   将 `TradeResult.actual_amount_out` 设置为 `int(parsed_json['onchain_actual_amount_out_lamports'])`。
            *   (可选) 也可以将 `onchain_input_decimals` 和 `onchain_output_decimals` 存储在 `TradeResult` 的 `provider_response_raw` 或新增字段中，以便后续审计或转换。
        *   **回退机制**: 如果 `onchain_data_fetch_status` 不是 `"success"` 或相关字段缺失/无效：
            *   `TradeResult.actual_amount_in` 和 `TradeResult.actual_amount_out` 仍使用先前来自GMGN API报价的数值（例如，从 `quote_response['data']['quote']['inAmount']` 和 `quote_response['data']['quote']['outAmount']` 解析）。
            *   在 `TradeResult.error_message` 或一个新的字段中（例如 `TradeResult.onchain_data_accuracy: "estimated_from_quote" | "from_chain"`）记录数据来源的准确性。
            *   记录一条警告日志，表明使用的是报价数据而非链上精确数据。

*   **C. `models/trade_record.py` (TradeRecord 模型)**
    *   **无需更改**。模型已设计为存储lamports单位的整数 (`token_in_actual_amount`, `token_out_actual_amount`)。

*   **D. `workflows/monitor_kol_activity/handler.py` (及其他调用交易服务的地方)**
    1.  **数据传递**:
        *   当从 `GmgnTradeService.execute_trade` 获得 `TradeResult` 对象后，其中的 `actual_amount_in` 和 `actual_amount_out` 已经是（理想情况下）来自链上的精确lamports值。
        *   这些值会被直接用于填充和保存 `TradeRecord` 实例。
    2.  **UI显示/转换逻辑**:
        *   任何将 `TradeRecord` 中的 `token_in_actual_amount` 或 `token_out_actual_amount` 转换为用户可读的UI数量（例如，除以 `10^decimals`）的地方，都需要确保能获取到正确的 `decimals`。
        *   `decimals` 通常在创建 `Signal` 或处理 `Token` 信息时已知。如果 `TradeRecord` 自身不存储 `decimals`，则依赖于从关联的 `Token` 或 `Signal` 记录中获取。确保这个关联和查找是稳健的。 （当前来看，`TradeRecord` 已有 `token_in_address` 和 `token_out_address`，理论上可以据此查到代币信息以获取decimals，但如果能在交易时就确定并记录会更直接）。

**5. 方案提出者/执行者**

*   AI Assistant (Gemini 2.5 Pro)

**6. 方案审阅者/批准者**

*   用户 (JerryGao)

**7. 方案批准日期**

*   2024-05-21

**8. (可选) 预期的验证方法**

1.  **单元测试 (Python - `GmgnTradeService`)**:
    *   Mock `subprocess.run` 的输出来模拟 `gmgn_test.mjs` 返回包含新 `onchain_` 字段的JSON。测试 `execute_trade` 是否能正确解析这些字段并填充 `TradeResult`。
    *   Mock `subprocess.run` 的输出来模拟 `gmgn_test.mjs` 返回不包含新 `onchain_` 字段的JSON (或 `onchain_data_fetch_status` 为 "failed")。测试是否能正确回退到使用报价数据。
2.  **手动/集成测试 (`gmgn_test.mjs`)**:
    *   准备一个已知的、成功的Solana交易哈希。
    *   修改 `gmgn_test.mjs` 脚本，使其可以接受一个交易哈希作为参数，并直接跳到"获取链上数据"的逻辑部分。
    *   执行脚本，传入测试交易哈希，并验证输出的JSON中是否包含正确的 `onchain_actual_amount_in_lamports`, `onchain_actual_amount_out_lamports` 等信息（与Solscan上手动核对）。
3.  **端到端测试 (开发/预生产环境)**:
    *   部署修改后的代码。
    *   执行一次真实的买入操作。
    *   检查数据库中生成的 `TradeRecord`，确认 `token_out_actual_amount` (以及 `token_in_actual_amount`) 是否与Solscan上该笔交易的实际代币余额变动精确匹配。
    *   特意选择一个可能有滑点的交易对进行测试。 