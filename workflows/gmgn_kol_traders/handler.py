import logging
import traceback
from typing import Any, Dict, List, Optional

from dao.gmgn_kol_traders_dao import GmgnKolTradersDAO
from utils.spiders.smart_money.gmgn_top_traders_spider import GmgnTopTradersSpider


logger = logging.getLogger("GmgnKolTradersHandler")
spider = GmgnTopTradersSpider()
gmgn_kol_traders_dao = GmgnKolTradersDAO()


async def process_kol_traders(token_dict: Dict) -> Optional[List[Dict]]:
    """处理代币数据
    
    Args:
        token: 代币数据
        
    Returns:
        Optional[Dict]: 处理后的代币数据，如果处理失败则返回None
    """
    if not token_dict:
        logger.warning("收到空的代币数据")
        return None
    
    # 获取代币地址
    wallet_address = token_dict.get('address')
    if not wallet_address:
        logger.warning(f"代币数据缺少地址字段: {token_dict}")
        return None
    
    chain = "sol"
    
    traders = await spider.get_top_traders(chain, wallet_address, tag="renowned")
    if not traders:
        logger.warning(f"没有聪明钱买过这个币: {wallet_address}")
        return None
    
    return traders

async def validate(data: Any) -> bool:
    """验证代币链接数据是否有效
    
    Args:
        data: 代币链接数据
        
    Returns:
        bool: 如果数据有效则返回True，否则返回False
    """
    return True


async def store_data(data: List[Dict]) -> int:
    """存储代币顶级交易者数据到数据库
    
    Args:
        data: 代币顶级交易者数据列表
        
    Returns:
        int: 成功存储的记录数
    """
    if not data:
        return 0
    
    traders = data if isinstance(data, list) else [data]
        
    try:
        chain = "sol"
        update_count = await gmgn_kol_traders_dao.upsert_traders_many(chain, traders)
        logger.info(f"批量更新完成: 更新了 {update_count} 条记录")
        return update_count
    except Exception as e:
        error_msg = f"存储代币顶级交易者数据时发生错误: {str(e)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        return False