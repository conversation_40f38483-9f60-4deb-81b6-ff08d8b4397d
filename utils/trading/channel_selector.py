import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from utils.trading.channel_registry import ChannelRegistry, ChannelNotFoundError
from models.config import TradeChannelConfig

logger = logging.getLogger(__name__)


class ChannelSelector:
    """渠道选择器 - 负责渠道筛选、排序和健康检查"""
    
    def __init__(self, channel_registry: ChannelRegistry):
        self.channel_registry = channel_registry
        self._health_check_cache: Dict[str, Dict[str, Any]] = {}
        self._health_check_ttl = 60  # 健康检查缓存60秒
    
    async def select_channels(
        self, 
        trade_type: str,
        token_in_address: str,
        token_out_address: str,
        amount: float,
        excluded_channels: Optional[List[str]] = None
    ) -> List[str]:
        """
        选择可用的交易渠道（按优先级排序）
        
        Args:
            trade_type: 交易类型（buy/sell）
            token_in_address: 输入代币地址
            token_out_address: 输出代币地址
            amount: 交易金额
            excluded_channels: 排除的渠道列表
            
        Returns:
            List[str]: 按优先级排序的可用渠道类型列表
        """
        logger.info(f"开始选择渠道：trade_type={trade_type}, amount={amount}")
        
        excluded_channels = excluded_channels or []
        available_channels = []
        
        # 获取所有启用的渠道
        enabled_channels = self.channel_registry.list_enabled_channels()
        logger.debug(f"启用的渠道: {enabled_channels}")
        
        for channel_type in enabled_channels:
            # 跳过排除的渠道
            if channel_type in excluded_channels:
                logger.debug(f"跳过被排除的渠道: {channel_type}")
                continue
            
            # 检查渠道可用性
            if await self._is_channel_available(channel_type, trade_type, token_in_address, token_out_address, amount):
                available_channels.append(channel_type)
                logger.debug(f"渠道 {channel_type} 可用")
            else:
                logger.debug(f"渠道 {channel_type} 不可用")
        
        # 按优先级排序
        sorted_channels = await self._sort_channels_by_priority(available_channels)
        
        logger.info(f"选择到 {len(sorted_channels)} 个可用渠道: {sorted_channels}")
        return sorted_channels
    
    async def _is_channel_available(
        self, 
        channel_type: str, 
        trade_type: str,
        token_in_address: str,
        token_out_address: str,
        amount: float
    ) -> bool:
        """
        检查渠道是否可用
        
        Args:
            channel_type: 渠道类型
            trade_type: 交易类型
            token_in_address: 输入代币地址
            token_out_address: 输出代币地址
            amount: 交易金额
            
        Returns:
            bool: 是否可用
        """
        try:
            # 1. 检查渠道是否注册和启用
            if not self.channel_registry.is_channel_registered(channel_type):
                logger.debug(f"渠道 {channel_type} 未注册")
                return False
            
            if not self.channel_registry.is_channel_enabled(channel_type):
                logger.debug(f"渠道 {channel_type} 未启用")
                return False
            
            # 2. 检查健康状态
            if not await self._check_channel_health(channel_type):
                logger.debug(f"渠道 {channel_type} 健康检查失败")
                return False
            
            # 3. 检查渠道特定限制
            if not await self._check_channel_constraints(channel_type, trade_type, token_in_address, token_out_address, amount):
                logger.debug(f"渠道 {channel_type} 不满足约束条件")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"检查渠道 {channel_type} 可用性时出错: {e}")
            return False
    
    async def _check_channel_health(self, channel_type: str) -> bool:
        """
        检查渠道健康状态（带缓存）
        
        Args:
            channel_type: 渠道类型
            
        Returns:
            bool: 是否健康
        """
        now = datetime.now()
        
        # 检查缓存
        if channel_type in self._health_check_cache:
            cache_entry = self._health_check_cache[channel_type]
            cache_time = cache_entry.get('timestamp')
            if cache_time and (now - cache_time).total_seconds() < self._health_check_ttl:
                logger.debug(f"使用缓存的健康检查结果: {channel_type} = {cache_entry['is_healthy']}")
                return cache_entry['is_healthy']
        
        # 执行健康检查
        try:
            channel_instance = self.channel_registry.get_channel(channel_type)
            
            # 如果渠道实例有健康检查方法，调用它
            if hasattr(channel_instance, 'health_check'):
                is_healthy = await channel_instance.health_check()
            else:
                # 否则使用注册表中的健康状态
                is_healthy = self.channel_registry.is_channel_healthy(channel_type)
            
            # 更新缓存
            self._health_check_cache[channel_type] = {
                'is_healthy': is_healthy,
                'timestamp': now
            }
            
            # 同步健康状态到注册表
            self.channel_registry.set_channel_health(channel_type, is_healthy)
            
            logger.debug(f"渠道 {channel_type} 健康检查结果: {is_healthy}")
            return is_healthy
            
        except ChannelNotFoundError:
            logger.warning(f"渠道 {channel_type} 未找到")
            return False
        except Exception as e:
            logger.error(f"渠道 {channel_type} 健康检查失败: {e}")
            # 健康检查失败，标记为不健康
            self._health_check_cache[channel_type] = {
                'is_healthy': False,
                'timestamp': now
            }
            self.channel_registry.set_channel_health(channel_type, False)
            return False
    
    async def _check_channel_constraints(
        self, 
        channel_type: str, 
        trade_type: str,
        token_in_address: str,
        token_out_address: str,
        amount: float
    ) -> bool:
        """
        检查渠道特定约束条件
        
        Args:
            channel_type: 渠道类型
            trade_type: 交易类型
            token_in_address: 输入代币地址
            token_out_address: 输出代币地址
            amount: 交易金额
            
        Returns:
            bool: 是否满足约束条件
        """
        try:
            channel_config = self.channel_registry.get_channel_config(channel_type)
            
            # 检查最小/最大金额限制（如果配置中有的话）
            channel_params = channel_config.channel_params
            
            # 最小金额检查
            min_amount = channel_params.get('min_amount')
            if min_amount is not None and amount < min_amount:
                logger.debug(f"渠道 {channel_type} 交易金额 {amount} 小于最小限制 {min_amount}")
                return False
            
            # 最大金额检查
            max_amount = channel_params.get('max_amount')
            if max_amount is not None and amount > max_amount:
                logger.debug(f"渠道 {channel_type} 交易金额 {amount} 大于最大限制 {max_amount}")
                return False
            
            # 支持的代币检查（如果有白名单/黑名单）
            supported_tokens = channel_params.get('supported_tokens')
            if supported_tokens is not None:
                if token_in_address not in supported_tokens or token_out_address not in supported_tokens:
                    logger.debug(f"渠道 {channel_type} 不支持代币对: {token_in_address} -> {token_out_address}")
                    return False
            
            # 禁用的代币检查
            blacklisted_tokens = channel_params.get('blacklisted_tokens', [])
            if token_in_address in blacklisted_tokens or token_out_address in blacklisted_tokens:
                logger.debug(f"渠道 {channel_type} 代币被禁用: {token_in_address} 或 {token_out_address}")
                return False
            
            return True
            
        except ChannelNotFoundError:
            logger.warning(f"渠道配置 {channel_type} 未找到")
            return False
        except Exception as e:
            logger.error(f"检查渠道 {channel_type} 约束条件时出错: {e}")
            return False
    
    def _check_amount_constraints(self, channel_config: TradeChannelConfig, amount: float) -> bool:
        """
        检查金额约束条件
        
        Args:
            channel_config: 渠道配置
            amount: 交易金额
            
        Returns:
            bool: 是否满足金额约束条件
        """
        try:
            channel_params = channel_config.channel_params
            
            # 最小金额检查
            min_amount = channel_params.get('min_amount')
            if min_amount is not None and amount < min_amount:
                logger.debug(f"交易金额 {amount} 小于最小限制 {min_amount}")
                return False
            
            # 最大金额检查
            max_amount = channel_params.get('max_amount')
            if max_amount is not None and amount > max_amount:
                logger.debug(f"交易金额 {amount} 大于最大限制 {max_amount}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"检查金额约束条件时出错: {e}")
            return False
    
    async def _sort_channels_by_priority(self, channel_types: List[str]) -> List[str]:
        """
        按优先级排序渠道
        
        Args:
            channel_types: 渠道类型列表
            
        Returns:
            List[str]: 按优先级排序的渠道类型列表
        """
        channel_priorities = []
        
        for channel_type in channel_types:
            try:
                config = self.channel_registry.get_channel_config(channel_type)
                channel_priorities.append((channel_type, config.priority))
            except ChannelNotFoundError:
                # 如果配置不存在，设置最低优先级
                logger.warning(f"渠道 {channel_type} 配置未找到，设置最低优先级")
                channel_priorities.append((channel_type, 999))
        
        # 按优先级排序（数字越小优先级越高）
        channel_priorities.sort(key=lambda x: x[1])
        
        sorted_channels = [channel_type for channel_type, _ in channel_priorities]
        logger.debug(f"渠道优先级排序结果: {[(ch, pr) for ch, pr in channel_priorities]}")
        
        return sorted_channels
    
    def clear_health_cache(self, channel_type: Optional[str] = None) -> None:
        """
        清除健康检查缓存
        
        Args:
            channel_type: 可选，指定要清除的渠道类型。如果为None，清除所有缓存
        """
        if channel_type:
            if channel_type in self._health_check_cache:
                del self._health_check_cache[channel_type]
                logger.debug(f"已清除渠道 {channel_type} 的健康检查缓存")
        else:
            self._health_check_cache.clear()
            logger.debug("已清除所有渠道的健康检查缓存")
    
    def get_selector_stats(self) -> dict:
        """
        获取选择器统计信息
        
        Returns:
            dict: 统计信息
        """
        return {
            "health_check_cache_size": len(self._health_check_cache),
            "health_check_ttl": self._health_check_ttl,
            "cached_channels": list(self._health_check_cache.keys())
        } 