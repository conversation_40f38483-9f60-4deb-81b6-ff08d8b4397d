# KOL 活动延迟监控 - 测试用例设计 (AI生成)

## 1. 测试目标

验证KOL活动延迟监控功能是否能够：
*   正确根据 `imported_from_following` 状态选择KOL钱包。
*   准确获取所选KOL钱包的最新活动记录 (按 `created_at` 排序)。
*   正确计算活动记录中 `created_at` (UTC datetime) 与 `timestamp` (东八区 Unix timestamp) 之间的时间差异。
*   正确实现连续3次超过阈值才触发告警的逻辑。
*   正确实现在配置的抑制期间内不重复发送告警的功能。
*   正确从配置中加载管理员Telegram Chat ID和告警相关配置。
*   在告警触发时，正确创建并保存`AlertEventRecord`到数据库。
*   在尝试发送通知时（无论成功或失败），正确创建并保存`NotificationLogRecord`到数据库，并能关联到对应的告警事件。
*   监控任务能按预期频率（每10秒）执行。
*   对各种边界条件和错误情况有合理的处理和日志记录。

## 2. 测试环境与准备

*   **数据库**: 需要一个可访问的MongoDB实例。
*   **测试数据**: 
    *   `KOLWallet` 集合: 
        *   包含 `imported_from_following: true` 的钱包，具有不同的 `last_active` 时间。
        *   包含 `imported_from_following: false` 的钱包，具有不同的 `last_active` 时间。
        *   包含 `imported_from_following` 为 `null` 或不存在的钱包，具有不同的 `last_active` 时间。
        *   部分钱包的 `last_active` 为空。
    *   `KOLWalletActivity` 集合:
        *   为上述钱包准备活动数据，包含不同的 `created_at` (UTC `datetime`) 和 `timestamp` (整数, 代表东八区时间的Unix时间戳) 值。
        *   有的钱包没有活动数据。
        *   有的活动数据 `created_at` 和 `timestamp` 之间的差异刚好在阈值边缘（如59秒，60秒，61秒）。
    *   `Config` 集合:
        *   包含 `type: "application_config"` 的文档，其 `data` 字段包含 `admin_telegram_chat_ids` 和 `kol_activity_monitor` 配置。
        *   测试 `admin_telegram_chat_ids` 为空或 `application_config` 不存在的情况。
    *   **新增**: `MonitorAlertState`, `AlertEventRecord`, `NotificationLogRecord` 集合在测试前应为空，或包含可控的预设数据用于特定测试场景。
*   **环境变量**: `TELEGRAM_BOT_TOKEN` (可以是一个mock值，重点测试发送逻辑是否被调用)。
*   **Mocking**: 
    *   需要mock `TelegramMessageSender.send_message_to_user`。
    *   需要mock数据库调用（特别是对新模型的写入和读取操作，以验证交互）。
    *   需要mock `datetime.now(timezone.utc)` 以控制时间流逝，测试连续告警和告警抑制功能。
    *   Mock `datetime.fromtimestamp(..., timezone.utc)` 和 `datetime.now(timezone.utc)` 以控制时间转换和比较的准确性，或者直接在测试数据中设置期望的 `created_at` 和 `timestamp` 值以产生特定差异。

## 3. 测试用例

### 3.1. 配置加载 (`KolActivityTimestampDiscrepancyChecker`)

**用例 TC_CONF_01: 成功加载 `admin_telegram_chat_ids` 和告警配置**
-   **描述**: 验证当 `Config` 中存在有效的 `application_config` 且 `admin_telegram_chat_ids` 非空且包含告警配置时，配置能被正确加载。
-   **前提条件**: `Config` 集合中存在一个文档 
    ```json
    { 
      "type": "application_config", 
      "data": { 
        "admin_telegram_chat_ids": ["id1", "id2"],
        "kol_activity_monitor": {
          "alert_threshold_seconds": 70,
          "consecutive_alerts_required": 2,
          "alert_suppression_minutes": 15
        }
      } 
    }
    ```
-   **操作**: 实例化 `KolActivityTimestampDiscrepancyChecker` 并异步调用其 `_load_config()` 方法。
-   **预期结果**: 
    1.  `checker.admin_chat_ids` 列表为 `["id1", "id2"]`。
    2.  `checker._config["alert_threshold_seconds"]` 为 `70`。
    3.  `checker._config["consecutive_alerts_required"]` 为 `2`。
    4.  `checker._config["alert_suppression_minutes"]` 为 `15`。
    5.  有相应的日志记录表明配置加载成功。

**用例 TC_CONF_02: `application_config` 不存在**
-   **描述**: 验证当 `Config` 中不存在 `application_config` 文档时，系统能妥善处理。
-   **前提条件**: `Config` 集合中不存在 `type: "application_config"` 的文档。
-   **操作**: 实例化 `KolActivityTimestampDiscrepancyChecker` 并异步调用 `_load_config()`。
-   **预期结果**: 
    1.  `checker.admin_chat_ids` 为空列表。
    2.  `checker._config` 应使用默认值：
        - `alert_threshold_seconds`: 60
        - `consecutive_alerts_required`: 3
        - `alert_suppression_minutes`: 30
    3.  有警告日志记录表明 `ApplicationConfig` 未找到。

**用例 TC_CONF_03: `admin_telegram_chat_ids` 为空或不存在**
-   **描述**: 验证当 `application_config` 存在，但其 `data.admin_telegram_chat_ids` 字段为空或 `None` 时，系统能妥善处理。
-   **前提条件**: `Config` 集合中存在 `{ "type": "application_config", "data": { "admin_telegram_chat_ids": [] } }` 或 `{ "type": "application_config", "data": { } }`。
-   **操作**: 实例化 `KolActivityTimestampDiscrepancyChecker` 并异步调用 `_load_config()`。
-   **预期结果**: 
    1.  `checker.admin_chat_ids` 为空列表。
    2.  `checker._config` 应使用默认值。
    3.  有警告日志记录表明 `admin_telegram_chat_ids` 为空。

**用例 TC_CONF_04: 配置中缺少部分告警设置**
-   **描述**: 验证当配置中只存在部分告警设置时，能正确加载存在的设置并对缺失的设置使用默认值。
-   **前提条件**: `Config` 集合中存在一个文档 
    ```json
    { 
      "type": "application_config", 
      "data": { 
        "admin_telegram_chat_ids": ["id1"],
        "kol_activity_monitor": {
          "alert_threshold_seconds": 80
          // 缺少 consecutive_alerts_required 和 alert_suppression_minutes
        }
      } 
    }
    ```
-   **操作**: 实例化 `KolActivityTimestampDiscrepancyChecker` 并异步调用 `_load_config()`。
-   **预期结果**: 
    1.  `checker.admin_chat_ids` 列表为 `["id1"]`。
    2.  `checker._config["alert_threshold_seconds"]` 为 `80`。
    3.  `checker._config["consecutive_alerts_required"]` 为默认值 `3`。
    4.  `checker._config["alert_suppression_minutes"]` 为默认值 `30`。
    5.  有相应的日志记录表明配置部分加载成功。

### 3.2. KOL钱包选择 (`KolActivityTimestampDiscrepancyChecker._get_latest_kol_wallet`)

**用例 TC_KOL_01: 选择 `imported_from_following: true` 的最新KOL (last_active不为空)**
-   **描述**: 验证能正确选择出 `imported_from_following: true` 且 `last_active` 时间最新的钱包。
-   **前提条件**:
    *   `KOLWallet` 集合中存在多个 `imported_from_following: true` 的钱包，它们的 `last_active` 字段均非空且时间各不相同。
    *   例如: WalletA (`last_active`: T-1h), WalletB (`last_active`: T-2h)。
-   **操作**: 调用 `checker._get_latest_kol_wallet(imported_from_following=True)`。
-   **预期结果**: 返回代表 WalletA 的 `KOLWallet` 对象。

**用例 TC_KOL_02: 选择 `imported_from_following: false` 或 `null` 的最新KOL (last_active不为空)**
-   **描述**: 验证能正确选择出 `imported_from_following: false` 或 `imported_from_following` 字段不存在 (视为false) 且 `last_active` 时间最新的钱包。
-   **前提条件**:
    *   `KOLWallet` 集合中存在多个 `imported_from_following: false` 或 `imported_from_following` 为 `None` 的钱包，它们的 `last_active` 字段均非空且时间各不相同。
    *   例如: WalletC (`imported_from_following: false`, `last_active`: T-30m), WalletD (`imported_from_following: None`, `last_active`: T-45m)。
-   **操作**: 调用 `checker._get_latest_kol_wallet(imported_from_following=False)`。
-   **预期结果**: 返回代表 WalletC 的 `KOLWallet` 对象。

**用例 TC_KOL_03: 无符合条件的 `imported_from_following: true` KOL (所有此类钱包的 `last_active` 均为空)**
-   **描述**: 验证当所有 `imported_from_following: true` 的钱包其 `last_active` 字段都为空时，不应选择任何钱包。
-   **前提条件**: `KOLWallet` 集合中所有 `imported_from_following: true` 的钱包，其 `last_active` 字段均为 `None`。
-   **操作**: 调用 `checker._get_latest_kol_wallet(imported_from_following=True)`。
-   **预期结果**: 返回 `None`。有相应的调试日志记录。

**用例 TC_KOL_04: 无符合条件的 `imported_from_following: true` KOL (无匹配钱包)**
-   **描述**: 验证当数据库中不存在 `imported_from_following: true` 的钱包时，不应选择任何钱包。
-   **前提条件**: `KOLWallet` 集合中不存在任何 `imported_from_following: true` 的钱包。
-   **操作**: 调用 `checker._get_latest_kol_wallet(imported_from_following=True)`。
-   **预期结果**: 返回 `None`。有相应的调试日志记录。

### 3.3. 连续告警与状态持久化检测逻辑 (`KolActivityTimestampDiscrepancyChecker._should_send_notification`)

**用例 TC_ALERT_01 (改): 首次超过阈值，连续计数增加，状态持久化，不触发告警事件**
-   **描述**: 验证时间差异首次超阈值时，连续计数增加，`MonitorAlertState`被更新，但不触发告警事件，不发送通知。
-   **前提条件**:
    *   `checker._config["alert_threshold_seconds"]` 设为 `60`。
    *   `checker._config["consecutive_alerts_required"]` 设为 `3`。
    *   `checker._alert_state[True]["consecutive_alerts"]` 初始为 `0`。
-   **操作**: 调用 `checker._should_send_notification(imported_from_following=True, discrepancy_seconds=65)`。
-   **预期结果**: 
    1.  返回 `(False, None)`。
    2.  `checker._alert_state[True]["consecutive_alerts"]` 增加到 `1`。
    3.  Mocked `_save_alert_state` 被调用，验证数据库中对应 `MonitorAlertState` 的 `consecutive_alerts` 更新为 `1`。
    4.  Mocked `_record_alert_event` 未被调用。

**用例 TC_ALERT_02 (改): 连续第二次超过阈值，连续计数增加，状态持久化，不触发告警事件**
-   **描述**: 验证当连续第二次超过阈值时，连续计数增加，`MonitorAlertState`被更新，但不触发告警事件，不发送通知。
-   **前提条件**:
    *   `checker._config["consecutive_alerts_required"]` 设为 `3`。
    *   `checker._alert_state[True]["consecutive_alerts"]` 初始为 `1`。
-   **操作**: 调用 `checker._should_send_notification(imported_from_following=True, discrepancy_seconds=65)`。
-   **预期结果**: 
    1.  返回 `(False, None)`。
    2.  `checker._alert_state[True]["consecutive_alerts"]` 增加到 `2`。
    3.  Mocked `_save_alert_state` 被调用，验证数据库中对应 `MonitorAlertState` 的 `consecutive_alerts` 更新为 `2`。
    4.  Mocked `_record_alert_event` 未被调用。

**用例 TC_ALERT_03 (改): 连续第三次超过阈值，触发告警事件，状态持久化**
-   **描述**: 验证连续第三次超阈值时，触发告警事件记录，`MonitorAlertState`更新，返回True和事件ID。
-   **前提条件**:
    *   `checker._config["consecutive_alerts_required"]` 设为 `3`。
    *   `checker._alert_state[True]["consecutive_alerts"]` 初始为 `2`。
    *   `checker._alert_state[True]["last_notification_time"]` 为 `None`。
-   **操作**: 调用 `checker._should_send_notification(imported_from_following=True, discrepancy_seconds=65)`。
-   **预期结果**: 
    1.  返回 `(True, <alert_event_id_string>)`。
    2.  `checker._alert_state[True]["consecutive_alerts"]` 重置为 `0`。
    3.  `checker._alert_state[True]["last_notification_time"]` 更新为当前时间。
    4.  Mocked `_save_alert_state` 被调用两次（一次增加计数，一次重置和更新时间）。
    5.  Mocked `_record_alert_event` 被调用，验证数据库中创建了相应的 `AlertEventRecord`。

**用例 TC_ALERT_04 (改): 差异低于阈值，连续计数重置，状态持久化**
-   **描述**: 验证时间差异低于阈值时，连续计数重置，`MonitorAlertState`被更新。
-   **前提条件**:
    *   `checker._config["alert_threshold_seconds"]` 设为 `60`。
    *   `checker._alert_state[False]["consecutive_alerts"]` 初始为 `2`。
-   **操作**: 调用 `checker._should_send_notification(imported_from_following=False, discrepancy_seconds=55)`。
-   **预期结果**: 
    1.  返回 `(False, None)`。
    2.  `checker._alert_state[False]["consecutive_alerts"]` 重置为 `0`。
    3.  Mocked `_save_alert_state` 被调用（如果之前计数大于0）。

**用例 TC_ALERT_05 (改): 告警后在抑制期内，不重复触发，状态持久化**
-   **描述**: 验证在抑制期内，即使连续超过阈值达到要求次数，也不重复触发告警，`MonitorAlertState`被更新。
-   **前提条件**:
    *   `checker._config["alert_suppression_minutes"]` 设为 `30`。
    *   `checker._alert_state[True]["consecutive_alerts"]` 初始为 `3`。
    *   `checker._alert_state[True]["last_notification_time"]` 设为 `current_time - timedelta(minutes=15)`。
-   **操作**: 调用 `checker._should_send_notification(imported_from_following=True, discrepancy_seconds=65)`。
-   **预期结果**: 
    1.  返回 `(False, None)`。
    2.  有日志记录告警被抑制，并显示抑制结束时间。
    3.  Mocked `_save_alert_state` 被调用，验证数据库中对应 `MonitorAlertState` 的 `consecutive_alerts` 更新为 `3`。

**用例 TC_ALERT_06 (改): 告警后抑制期结束，再次触发，状态持久化**
-   **描述**: 验证抑制期结束后，当连续超过阈值达到要求次数时，可以再次触发告警，`MonitorAlertState`被更新。
-   **前提条件**:
    *   `checker._config["alert_suppression_minutes"]` 设为 `30`。
    *   `checker._config["consecutive_alerts_required"]` 设为 `3`。
    *   `checker._alert_state[True]["consecutive_alerts"]` 初始为 `3`。
    *   `checker._alert_state[True]["last_notification_time"]` 设为 `current_time - timedelta(minutes=31)`。
-   **操作**: 调用 `checker._should_send_notification(imported_from_following=True, discrepancy_seconds=65)`。
-   **预期结果**: 
    1.  返回 `(True, <alert_event_id_string>)`。
    2.  `checker._alert_state[True]["last_notification_time"]` 被更新为当前时间。
    3.  Mocked `_save_alert_state` 被调用两次（一次增加计数，一次重置和更新时间）。
    4.  有日志记录触发告警。

### 3.4. 活动获取、时间差异计算与通知记录 (`KolActivityTimestampDiscrepancyChecker.check_and_notify_for_category`)

(假设 `checker.admin_chat_ids` 已成功加载)

**用例 TC_ACT_01: 成功获取最新活动，`created_at` 与 `timestamp` 差异无触发**
-   **描述**: 验证当选定KOL有活动，且其最新活动记录中 `created_at` (UTC) 与 `timestamp` (东八区Unix时间戳转换到UTC后) 的差异未超过配置的阈值时，不增加连续计数。
-   **前提条件**:
    *   `_get_latest_kol_wallet` 返回一个有效的 `KOLWallet` 对象 (e.g., `wallet_X`)。
    *   `KOLWalletActivity` 集合中，`wallet_X` 的最新活动记录 (按 `created_at` 降序排序后的第一条) 包含:
        *   `created_at`: 例如 `datetime(2023, 1, 1, 12, 0, 30, tzinfo=timezone.utc)`
        *   `timestamp`: 整数值，使得 `datetime.fromtimestamp(timestamp, timezone.utc)` 等于例如 `datetime(2023, 1, 1, 12, 0, 0, tzinfo=timezone.utc)` (差异30秒)。
    *   `checker._config["alert_threshold_seconds"]` 设为 `60`。
    *   Mock `message_sender.send_message_to_user`。
-   **操作**: 调用 `checker.check_and_notify_for_category(...)`。
-   **预期结果**:
    1.  Mocked `message_sender.send_message_to_user` 未被调用。
    2.  `checker._alert_state[...]["consecutive_alerts"]` 保持为 `0`。
    3.  有日志记录正常的检查过程和计算出的差异值。

**用例 TC_ACT_02 (改): 连续3次检测超阈值，第3次触发通知并记录通知日志**
-   **描述**: 验证连续3次检测到时间差异超阈值，在第3次触发告警通知，并为每次发送尝试记录 `NotificationLogRecord`。
-   **前提条件**:
    *   `_get_latest_kol_wallet` 返回 `wallet_Y`。
    *   `wallet_Y` 的最新活动记录 (按 `created_at` 降序) 包含差异为120秒的字段 (`created_at` vs `timestamp`)。
    *   `checker._config["alert_threshold_seconds"]` 设为 `60`。
    *   `checker._config["consecutive_alerts_required"]` 设为 `3`。
    *   `checker._alert_state[...]["consecutive_alerts"]` 已经为 `2`。
    *   `checker._alert_state[...]["last_notification_time"]` 为 `None`。
    *   Mock `message_sender.send_message_to_user`。
-   **操作**: 调用 `checker.check_and_notify_for_category(...)`。
-   **预期结果**:
    1.  Mocked `message_sender.send_message_to_user` 被调用两次 (为id1和id2)。
    2.  Mocked `_record_notification_log` 被调用两次。
    3.  验证数据库中创建了两个 `NotificationLogRecord` 文档，关联到同一个 `alert_event_id`，状态分别为 `success`。

**用例 TC_ACT_03: 选定KOL无任何活动记录**
-   **描述**: 验证当选定的KOL在 `KOLWalletActivity` 中没有任何记录时，不进行差异计算，也不发送通知。
-   **前提条件**:
    *   `_get_latest_kol_wallet` 返回 `wallet_Z`。
    *   `KOLWalletActivity` 集合中没有 `wallet_Z` 的活动记录。
    *   Mock `message_sender.send_message_to_user`。
-   **操作**: 调用 `checker.check_and_notify_for_category(...)`。
-   **预期结果**:
    1.  Mocked `message_sender.send_message_to_user` 未被调用。
    2.  有日志记录表明未找到活动记录。
    3.  `checker._alert_state` 状态保持不变。

**用例 TC_ACT_04: 告警触发后在抑制期内再次检测到问题**
-   **描述**: 验证在抑制期内，即使连续超过阈值达到要求次数，也不触发告警。
-   **前提条件**:
    *   `checker._config["alert_suppression_minutes"]` 设为 `30`。
    *   `checker._config["consecutive_alerts_required"]` 设为 `3`。
    *   `checker._alert_state[...]["consecutive_alerts"]` 初始为 `3`。
    *   `checker._alert_state[...]["last_notification_time"]` 设为 `current_time - timedelta(minutes=15)`。
    *   `wallet_Y` 的最新活动记录 (按 `created_at` 降序) 包含差异为120秒的字段 (`created_at` vs `timestamp`)。
    *   Mock `message_sender.send_message_to_user`。
-   **操作**: 调用 `checker.check_and_notify_for_category(...)`。
-   **预期结果**:
    1.  Mocked `message_sender.send_message_to_user` 未被调用。
    2.  有日志记录表明告警被抑制，并显示抑制结束时间。

**用例 TC_ACT_05 (新增): 通知发送失败时记录日志**
-   **描述**: 验证当Telegram消息发送失败时，`NotificationLogRecord` 仍被创建，状态为 `failure` 并包含错误信息。
-   **前提条件**: (类似TC_ACT_02，但 `message_sender.send_message_to_user` 在被调用时抛出异常)。
-   **操作**: 调用 `checker.check_and_notify_for_category(...)`。
-   **预期结果**:
    1.  Mocked `_record_notification_log` 被调用。
    2.  验证数据库中创建了 `NotificationLogRecord`，状态为 `failure`，并包含相应的 `error_message`。

### 3.5. 告警与通知记录的辅助方法 (`_record_alert_event`, `_record_notification_log`)

**用例 TC_REC_01: `_record_alert_event` 成功保存**
-   **描述**: 验证调用 `_record_alert_event` 能成功在数据库创建 `AlertEventRecord`。
-   **前提条件**: 提供合法的告警事件参数。
-   **操作**: 直接调用 `checker._record_alert_event(...)`。
-   **预期结果**: 
    1.  返回一个字符串ID。
    2.  验证数据库中存在一个具有匹配数据的 `AlertEventRecord`。

**用例 TC_REC_02: `_record_notification_log` 成功保存**
-   **描述**: 验证调用 `_record_notification_log` 能成功在数据库创建 `NotificationLogRecord`。
-   **前提条件**: 提供合法的通知日志参数。
-   **操作**: 直接调用 `checker._record_notification_log(...)`。
-   **预期结果**: 验证数据库中存在一个具有匹配数据的 `NotificationLogRecord`。

### 3.6. 状态加载 (`KolActivityTimestampDiscrepancyChecker._load_alert_state`)

**用例 TC_LOAD_01: 成功从数据库加载持久化的告警状态**
-   **描述**: 验证 `_load_alert_state` 方法能正确从数据库加载先前保存的 `MonitorAlertState`。
-   **前提条件**:
    *   数据库 `monitor_alert_states` 集合中预先插入特定类别的状态记录 (e.g., `imported` 类别，`consecutive_alerts: 2`, `last_notification_time: <some_datetime>`)。
-   **操作**: 实例化 `checker` 后调用 `checker._load_alert_state()`。
-   **预期结果**: 
    1.  `checker._alert_state[True]["consecutive_alerts"]` 应为 `2`。
    2.  `checker._alert_state[True]["last_notification_time"]` 应为预设的 `<some_datetime>`。
    3.  日志记录表明状态已从数据库加载。

**用例 TC_LOAD_02: 数据库无状态记录时使用默认值**
-   **描述**: 验证当数据库中没有特定类别的 `MonitorAlertState` 记录时，`_load_alert_state` 会使用内存中的默认值。
-   **前提条件**: `monitor_alert_states` 集合为空，或不包含目标 `monitor_type` 和 `category` 的记录。
-   **操作**: 实例化 `checker` 后调用 `checker._load_alert_state()`。
-   **预期结果**: `checker._alert_state` 中所有类别的 `consecutive_alerts` 为 `0`，`last_notification_time` 为 `None`。

### 3.7. 工作流入口函数 (`perform_kol_activity_timestamp_discrepancy_check_task`)

**用例 TC_WF_01: 工作流任务成功执行**
-   **描述**: 验证 `perform_kol_activity_timestamp_discrepancy_check_task` 成功调用 `KolActivityTimestampDiscrepancyChecker.run_check_cycle` 并且该方法不抛出异常。
-   **前提条件**:
    *   Mock `KolActivityTimestampDiscrepancyChecker` 类。
    *   Mock其实例的 `run_check_cycle` 方法，使其不抛出异常并正常返回。
-   **操作**: 异步调用 `perform_kol_activity_timestamp_discrepancy_check_task()`。
-   **预期结果**:
    1.  返回一个字典，包含 `{"status": "success", "timestamp": "..."}`。
    2.  Mocked `KolActivityTimestampDiscrepancyChecker` 的 `__init__` 和 `run_check_cycle` 被调用。
    3.  有日志记录任务成功完成。

**用例 TC_WF_02: 工作流任务执行中发生异常**
-   **描述**: 验证当 `KolActivityTimestampDiscrepancyChecker.run_check_cycle` 抛出异常时，`perform_kol_activity_timestamp_discrepancy_check_task` 能捕获异常并返回错误状态。
-   **前提条件**:
    *   Mock `KolActivityTimestampDiscrepancyChecker` 类。
    *   Mock其实例的 `run_check_cycle` 方法，使其抛出一个预定义的异常 (e.g., `ValueError("Test error")`)。
-   **操作**: 异步调用 `perform_kol_activity_timestamp_discrepancy_check_task()`。
-   **预期结果**:
    1.  返回一个字典，包含 `{"status": "error", "error_message": "Test error", "timestamp": "..."}`。
    2.  有错误日志记录异常信息。

### 3.8. 工作流配置 (`kol_activity_timestamp_discrepancy_monitor_workflow.yaml`)

**用例 TC_YAML_01: YAML配置正确性检查 (人工)**
-   **描述**: 人工检查 `kol_activity_timestamp_discrepancy_monitor_workflow.yaml` 文件的内容。
-   **操作**: 人工审阅文件。
-   **预期结果**:
    1.  `name` 和 `description` 字段有意义。
    2.  `nodes[0].name` 是唯一的。
    3.  `nodes[0].node_type` 为 `input`。
    4.  `nodes[0].interval` 为 `10` (秒)。
    5.  `nodes[0].generate_data` 指向正确的Python路径: `workflows.kol_activity_timestamp_discrepancy_monitor.handler.perform_kol_activity_timestamp_discrepancy_check_task`。
    6.  `flow_control` 配置合理 (e.g., `max_pending_messages: 1`, `check_interval: 0.5`, `enable_flow_control: true`)。

## 4. 测试方法

*   **单元测试**: 
    *   主要针对 `handler.py` 中的 `KolActivityTimestampDiscrepancyChecker` 类及其方法进行单元测试。
    *   重点测试 `_should_send_notification` 方法的连续告警计数和告警抑制逻辑。
    *   对入口函数 `perform_kol_activity_timestamp_discrepancy_check_task` 进行单元测试，mock其依赖。
    *   使用 `unittest.mock` (或 `pytest-mock`) 来模拟数据库调用和 `TelegramMessageSender.send_message_to_user`。
    *   使用 `unittest.mock` 模拟时间流逝，以测试连续告警和告警抑制功能。
    *   精确控制测试数据中的 `created_at` 和 `timestamp` 值来测试差异计算逻辑。
    *   重点测试 `_record_alert_event` 和 `_record_notification_log` 的数据库写入。
    *   重点测试 `_load_alert_state` 的数据库读取和默认值处理。
    *   验证 `_should_send_notification` 和 `check_and_notify_for_category` 中对新记录方法的调用。
*   **集成测试 (可选但推荐)**: 
    *   可以编写一个测试，实际启动 `kol_activity_timestamp_discrepancy_monitor_workflow.yaml` 定义的工作流 (使用测试数据库和mock的Telegram发送器)。
    *   验证工作流是否按预期频率执行，并通过mock的 `TelegramMessageSender` 验证通知逻辑。
*   **手动测试/端到端**: 
    *   在开发或测试环境中，部署并运行完整的工作流。
    *   手动设置特定KOL的活动数据，使其产生差异，模拟连续3次检测超过阈值的情况，观察是否能收到预期的Telegram通知。
    *   测试告警抑制功能，确认在配置的抑制时间内不会收到重复告警。

## 5. 日志验证

*   在所有测试用例执行过程中，检查是否有预期的日志输出，特别是关于告警事件记录、通知发送记录的创建，以及状态加载/保存的日志。 