# -*- coding: utf-8 -*-

"""
消息管理器，主要的模型为：
- 一个协程负责从Kafka获取消息，并推送到本地队列中
- 多个消费者协程负责从本地队列中获取消息，并进行处理，处理后，将消息ID添加到SortedSet中
- 一个协程负责从SortedSet中获取消息ID，并提交连续的消息ID的Offset，避免重复处理和重复提交，提交后，删除SortedSet中的已确认的消息ID

所以，MessageManager应该注意的是：

1. 将消息的offset添加到SortedSet中
2. SortedSet应该做好线程安全
"""


from abc import ABC, abstractmethod
import asyncio
import logging
from typing import List, Any, Dict, Optional
import time

from sortedcontainers import SortedSet

from utils.workflows.message_queue.message_queue import KafkaQueue, Message
from utils.workflows.message_queue.dead_letter_queue import DeadLetterType
from utils.workflows.parallel_control import IParallelControl 
from utils.common import check_message_id_continuous
from utils.workflows.message_queue.dlq_init import dlq_manager


class MessageManager(ABC):
    """节点消息管理器抽象基类"""
    
    @abstractmethod
    async def get_messages_from_kafka(self) -> List[Message]:
        """从Kafka获取消息"""
        pass

    @abstractmethod
    async def put_messages_to_local(self, messages: List[Message]):
        """将Kafka消息存储到本地队列中"""
        pass
    
    @abstractmethod
    async def ack_messages(self, message_ids: List[str]):
        """
        Kafka消息确认
        
        从SortedSet中获取消息ID，并提交连续的消息ID的Offset，避免重复处理和重复提交，提交后，删除SortedSet中的已确认的消息ID
        """
        pass
    
    @abstractmethod
    async def get_message(self) -> Message:
        """从本地队列中获取1条消息"""
        pass
    
    @abstractmethod
    async def get_messages(self, count: int = 1, timeout: int = 1000) -> List[Message]:
        """
        从本地队列中获取多条消息
        
        Args:
            count (int, optional): 获取消息的数量. Defaults to 1.
            timeout (int, optional): 获取消息的超时时间. Defaults to 1000 ms.
            
        Returns:
        """
    
    @abstractmethod
    async def solved_message_id(self, message_id: str):
        """
        将处理完的消息offset添加到SortedSet中
        """
        pass
    
    @abstractmethod
    async def start(self):
        """
        启动消息管理器, 主要启动以下协程
        
        1. 从Kafka获取消息，并推送到本地队列中
        2. 从本地消息确认SortedSet中获取消息ID，并提交连续的消息ID的Offset，避免重复处理和重复提交
        """
        pass
    
    @abstractmethod
    async def stop(self):
        """停止消息管理器"""
        pass
    
    @abstractmethod
    async def send_to_dead_letter_queue(self, message: Message, error: Exception, node_name: str, dlq_type: DeadLetterType) -> bool:
        """
        发送消息到死信队列
        
        Args:
            message: 失败的消息
            error: 异常信息
            node_name: 节点名称
            dlq_type: 死信队列类型
            
        Returns:
            bool: 是否发送成功
        """
        pass


class KafkaMessageManager(MessageManager):
    """Kafka消息管理器"""

    def __init__(self, 
                 kafka_queue: KafkaQueue,
                 parallel_control: IParallelControl,
                 kafka_receive_count: int = 1000,
                 kafka_receive_timeout: int = 1000,
                 local_queue_length: int = 1000,
                 interval_to_ack: int = 1000,
                 interval_to_put_message: int = 1000,
                 message_timeout: int = 300,  # 消息处理超时时间，默认300秒（5分钟）
                 ):
        """初始化Kafka消息管理器

        Args:
            kafka_queue (KafkaQueue): Kafka队列
            parallel_control (IParallelControl): 并行控制器,用于控制SortedSet的线程安全
            dlq_manager (DeadLetterQueueManager, optional): 死信队列管理器
            kafka_receive_count (int, optional): Kafka获取消息数量. Defaults to 10.
            kafka_receive_timeout (int, optional): Kafka获取消息超时时间. Defaults to 1000 ms.
            local_queue_length (int, optional): 本地队列长度. Defaults to 10.
            interval_to_ack (int, optional): 确认消息间隔时间. Defaults to 1000 ms.
            interval_to_put_message (int, optional): 拉取消息间隔时间. Defaults to 1000 ms.
            message_timeout (int, optional): 消息处理超时时间. Defaults to 300 s.
        """
        self.kafka_queue = kafka_queue
        self.local_queue = asyncio.Queue(maxsize=local_queue_length)
        self.kafka_receive_count = kafka_receive_count
        self.kafka_receive_timeout = kafka_receive_timeout
        self.ack_message_ids_set = SortedSet()
        self.parallel_control = parallel_control
        self.ack_message_ids_resource_id = "ack_message_ids"
        self.ack_message_ids_resource_owner = "ack_message_resource_owner"
        self.last_ack_message_id = -1 # 上一次确认的消息ID, -1 表示未确认
        self.interval_to_ack = interval_to_ack / 1000
        self.interval_to_put_message = interval_to_put_message / 1000
        self.dlq_manager = dlq_manager  # 死信队列管理器
        self.message_timeout = message_timeout  # 消息处理超时时间，单位秒
        
        # 添加处理中消息集合，用于跟踪正在处理的消息
        self.processing_messages = {}  # message_id -> Message
        self.processing_messages_resource_id = "processing_messages"
        self.processing_messages_resource_owner = "processing_messages_resource_owner"
        
        self.logger = logging.getLogger(self.kafka_queue.name + self.__class__.__name__)
        self._stop = False
        
        self.logger.info(f"本地队列大小: {local_queue_length}")
        self.logger.info(f"消息处理超时时间: {message_timeout}秒")
        
    async def init_last_ack_message_id(self):
        """初始化上一次确认的消息ID"""
        self.last_ack_message_id = await self.kafka_queue.get_last_offset()

    async def get_messages_from_kafka(self) -> List[Message]:
        """从Kafka获取消息
        
        使用pull方法获取消息，可以适应高积压的情况
        
        Returns:
            List[Message]: 获取到的消息列表
        """
        try:
            # 使用新的pull方法获取消息
            result = await self.kafka_queue.pull(max_count=self.kafka_receive_count, timeout=self.kafka_receive_timeout/1000)
            self.logger.info(f"从Kafka获取到{len(result)}条消息")
            return result
        except Exception as e:
            self.logger.error(f"从Kafka获取消息失败: {e}")
            return []
    
    async def get_message(self) -> Message:
        """从本地队列中获取1条消息，并添加获取时间
        
        将消息放入处理中消息集合，用于跟踪超时
        """
        message = await self.local_queue.get()
        
        # 记录消息获取时间
        message.metadata["get_time"] = time.time()
        
        # 将消息放入处理中消息集合
        async with self.RequireLockProcessingMessages(
            self.parallel_control, 
            self.processing_messages_resource_id,
            self.processing_messages_resource_owner
        ):
            self.processing_messages[message.id] = message
            self.logger.debug(f"将消息ID {message.id} 添加到处理中消息集合，当前大小: {len(self.processing_messages)}")
        
        return message
    
    async def put_messages_to_local(self):
        """从Kafka拉取消息，并推送到本地队列中"""
        self.logger.info("开始拉取消息")
        while not self._stop:
            try:
                messages = await self.get_messages_from_kafka()
                self.logger.info(f"获取到{len(messages)}条消息")
                if messages:
                    # 逐个将消息放入本地队列，而不是直接放入整个列表
                    for message in messages:
                        await self.local_queue.put(message)
                await asyncio.sleep(self.interval_to_put_message)
            except Exception as e:
                self.logger.error(f"拉取或处理消息时出错: {e}")
                await asyncio.sleep(self.interval_to_put_message)
            
        self.logger.info("停止拉取消息")
        
    class RequireLockAckMessageIds:
        """需要加锁的确认消息ID"""
        
        def __init__(self, parallel_control: IParallelControl, ack_message_ids_resource_id: str, ack_message_ids_resource_owner: str):
            self.parallel_control = parallel_control
            self.ack_message_ids_resource_id = ack_message_ids_resource_id
            self.ack_message_ids_resource_owner = ack_message_ids_resource_owner
        
        async def __aenter__(self):
            await self.parallel_control.acquire_resource(self.ack_message_ids_resource_id, self.ack_message_ids_resource_owner)
            return self
        
        async def __aexit__(self, exc_type, exc_value, traceback):
            await self.parallel_control.release_resource(self.ack_message_ids_resource_id, self.ack_message_ids_resource_owner)
            
        def __enter__(self):
            loop = asyncio.get_event_loop()
            loop.run_until_complete(self.parallel_control.acquire_resource(
                self.ack_message_ids_resource_id, self.ack_message_ids_resource_owner
            ))
            return self
        
        def __exit__(self, exc_type, exc_value, traceback):
            loop = asyncio.get_event_loop()
            loop.run_until_complete(self.parallel_control.release_resource(
                self.ack_message_ids_resource_id, self.ack_message_ids_resource_owner
            ))
        
    async def ack_messages(self):
        """
        从SortedSet中获取消息ID，并提交连续的消息ID的Offset，避免重复处理和重复提交，提交后，删除SortedSet中的已确认的消息ID
        """
        self.logger.info("开始确认消息")
        while not self._stop:
            async with self.RequireLockAckMessageIds(self.parallel_control, self.ack_message_ids_resource_id, self.ack_message_ids_resource_owner):
                
                ack_message_ids = list(self.ack_message_ids_set)
                if not ack_message_ids:
                    self.logger.info("没有需要确认的消息ID")
                    await asyncio.sleep(self.interval_to_ack)
                    continue
                
                # 打印SortedSet中的前10个元素，帮助调试
                sample_ids = ack_message_ids[:10]
                self.logger.info(f"SortedSet中的前10个元素(共{len(ack_message_ids)}个): {sample_ids}")
                # 打印数据类型信息
                self.logger.info(f"消息ID类型: {[type(mid).__name__ for mid in sample_ids]}")
                
                start_index = 0 if self.last_ack_message_id == -1 else self.last_ack_message_id
                self.logger.info(f"起始offset: {start_index}")
                continuous_message_ids, next_start_index = check_message_id_continuous(start_index, ack_message_ids)
                self.logger.info(f"连续的offset大小: {len(continuous_message_ids)}, 下一个offset: {next_start_index}")
                
                if continuous_message_ids:
                    await self.kafka_queue.ack(continuous_message_ids[-1])
                    self.ack_message_ids_set.difference_update(continuous_message_ids)
                    self.last_ack_message_id = next_start_index
                    
                self.logger.info(f"当前SortedSet大小: {len(self.ack_message_ids_set)}")

            await asyncio.sleep(self.interval_to_ack)
        
        self.logger.info("停止确认消息")
                
    async def solved_message_id(self, message_id: str):
        """将处理完的消息ID添加到SortedSet中，并从处理中消息集合移除"""
        # 添加到确认集合
        async with self.RequireLockAckMessageIds(self.parallel_control, self.ack_message_ids_resource_id, self.ack_message_ids_resource_owner):
            self.logger.debug(f"将消息ID {message_id} 添加到SortedSet中")
            self.ack_message_ids_set.add(message_id)
        
        # 从处理中消息集合移除
        async with self.RequireLockProcessingMessages(
            self.parallel_control, 
            self.processing_messages_resource_id,
            self.processing_messages_resource_owner
        ):
            if message_id in self.processing_messages:
                del self.processing_messages[message_id]
                self.logger.debug(f"将消息ID {message_id} 从处理中消息集合移除，当前大小: {len(self.processing_messages)}")
    
    async def send_to_dead_letter_queue(self, message: Message, error: Exception, node_name: str, dlq_type: DeadLetterType) -> bool:
        """发送消息到死信队列
        
        Args:
            message: 失败的消息
            error: 异常信息
            node_name: 节点名称
            dlq_type: 死信队列类型
            
        Returns:
            bool: 是否发送成功
        """
        if not self.dlq_manager:
            self.logger.warning("未配置死信队列管理器，无法发送消息到死信队列")
            return False
        
        try:
            result = await self.dlq_manager.send_from_original(dlq_type, message, error, node_name)
            if result:
                self.logger.info(f"成功发送消息 {message.id} 到死信队列: {dlq_type.value}")
                # 消息发送到死信队列后，需要确认原始消息，避免队头阻塞
                message_id = message.metadata.get("message_id") or message.id
                await self.solved_message_id(message_id)
            else:
                self.logger.error(f"发送消息 {message.id} 到死信队列失败: {dlq_type.value}")
            return result
        except Exception as e:
            self.logger.error(f"发送消息 {message.id} 到死信队列时发生异常: {e}")
            return False
        
    async def start(self) -> List[Any]:
        """启动消息管理器"""
        self.logger.info("启动消息管理器")
        await self.init_last_ack_message_id()
        self.logger.info(f"初始化上一次确认的消息ID: {self.last_ack_message_id}")
        
        # 创建并返回协程任务
        self.logger.info("创建消息管理器任务")
        
        # 确保返回有效的协程对象
        coroutines = []
        
        # 创建从Kafka拉取消息的协程
        put_messages_coro = self.put_messages_to_local()
        self.logger.info(f"创建put_messages_to_local协程: {put_messages_coro}")
        coroutines.append(put_messages_coro)
        
        # 创建确认消息的协程
        ack_messages_coro = self.ack_messages()
        self.logger.info(f"创建ack_messages协程: {ack_messages_coro}")
        coroutines.append(ack_messages_coro)
        
        # # 创建超时检查的协程
        # timeout_checker_coro = self.timeout_checker()
        # self.logger.info(f"创建timeout_checker协程: {timeout_checker_coro}")
        # coroutines.append(timeout_checker_coro)
        
        # self.logger.info(f"创建了 {len(coroutines)} 个协程任务")
        
        return coroutines
        
    async def stop(self):
        """停止消息管理器"""
        self._stop = True
        self.logger.debug("停止消息管理器")
        
    async def get_messages(self, count=1, timeout=1000):
        """从本地队列中获取多条消息，并记录获取时间
        
        将消息放入处理中消息集合，用于跟踪超时
        
        Args:
            count: 要获取的消息数量上限
            timeout: 整个获取操作的总体超时时间(毫秒)
        
        Returns:
            List[Message]: 获取到的消息列表
        """
        # 将毫秒转换为秒
        timeout_seconds = timeout / 1000.0
        self.logger.info(f"从本地队列中最多获取{count}条消息，总体超时时间{timeout_seconds:.1f}秒")
        
        messages = []
        
        # 使用asyncio.wait_for包装整个获取过程
        async def get_batch():
            for _ in range(count):
                try:
                    # 如果队列为空，get操作会阻塞直到有消息可用
                    msg = await self.local_queue.get()
                    
                    # 记录消息获取时间
                    msg.metadata["get_time"] = time.time()
                    
                    # 将消息放入处理中消息集合
                    # async with self.RequireLockProcessingMessages(
                    #     self.parallel_control, 
                    #     self.processing_messages_resource_id,
                    #     self.processing_messages_resource_owner
                    # ):
                    #     self.processing_messages[msg.id] = msg
                    #     self.logger.debug(f"将消息ID {msg.id} 添加到处理中消息集合，当前大小: {len(self.processing_messages)}")
                    
                    messages.append(msg)
                    # 如果已经获取到足够的消息，提前结束
                    if len(messages) >= count:
                        break
                except Exception as e:
                    self.logger.error(f"获取消息时出错: {e}")
                    break
        
        try:
            # 对整个批量获取过程设置总体超时
            await asyncio.wait_for(get_batch(), timeout_seconds)
        except asyncio.TimeoutError:
            self.logger.debug(f"批量获取消息达到总体超时限制({timeout_seconds:.1f}秒)，已获取{len(messages)}条消息")
        
        self.logger.info(f"从本地队列中获取到{len(messages)}条消息")
        return messages

    class RequireLockProcessingMessages:
        """需要加锁的处理中消息集合"""
        
        def __init__(self, parallel_control: IParallelControl, resource_id: str, resource_owner: str):
            self.parallel_control = parallel_control
            self.resource_id = resource_id
            self.resource_owner = resource_owner
        
        async def __aenter__(self):
            await self.parallel_control.acquire_resource(self.resource_id, self.resource_owner)
            return self
        
        async def __aexit__(self, exc_type, exc_value, traceback):
            await self.parallel_control.release_resource(self.resource_id, self.resource_owner)
            
        def __enter__(self):
            loop = asyncio.get_event_loop()
            loop.run_until_complete(self.parallel_control.acquire_resource(
                self.resource_id, self.resource_owner
            ))
            return self
        
        def __exit__(self, exc_type, exc_value, traceback):
            loop = asyncio.get_event_loop()
            loop.run_until_complete(self.parallel_control.release_resource(
                self.resource_id, self.resource_owner
            ))
        
    async def check_message_timeout(self):
        """检查处理中消息是否超时
        
        如果消息处理时间超过设定的超时时间，则将消息发送到死信队列，并从处理中消息集合移除
        """
        self.logger.debug("开始检查消息超时")
        current_time = time.time()
        timeout_messages = []
        
        # 获取超时消息
        async with self.RequireLockProcessingMessages(
            self.parallel_control, 
            self.processing_messages_resource_id,
            self.processing_messages_resource_owner
        ):
            for message_id, message in list(self.processing_messages.items()):
                get_time = message.metadata.get("get_time", 0)
                processing_time = current_time - get_time
                
                if processing_time > self.message_timeout:
                    self.logger.warning(f"消息ID {message_id} 处理超时，已处理时间: {processing_time:.2f}秒，超时时间: {self.message_timeout}秒")
                    timeout_messages.append((message_id, message))
        
        # 处理超时消息
        for message_id, message in timeout_messages:
            # 构造超时异常
            timeout_error = TimeoutError(f"消息处理超时，已处理时间超过{self.message_timeout}秒")
            
            # 发送到死信队列
            success = await self.send_to_dead_letter_queue(
                message=message,
                error=timeout_error,
                node_name="MessageManager",
                dlq_type=DeadLetterType.TIMEOUT
            )
            
            if success:
                self.logger.info(f"超时消息ID {message_id} 已发送到死信队列")
                
                # 从处理中消息集合移除
                async with self.RequireLockProcessingMessages(
                    self.parallel_control, 
                    self.processing_messages_resource_id,
                    self.processing_messages_resource_owner
                ):
                    if message_id in self.processing_messages:
                        del self.processing_messages[message_id]
            else:
                self.logger.error(f"超时消息ID {message_id} 发送到死信队列失败")
        
        return len(timeout_messages)
    
    async def timeout_checker(self):
        """定期检查超时消息的协程"""
        self.logger.info("启动超时检查协程")
        
        while not self._stop:
            try:
                timeout_count = await self.check_message_timeout()
                if timeout_count > 0:
                    self.logger.info(f"处理了 {timeout_count} 条超时消息")
                
                # 等待下一次检查
                await asyncio.sleep(min(60, self.message_timeout / 3))  # 检查间隔不超过超时时间的1/3
            except Exception as e:
                self.logger.error(f"超时检查时出错: {e}")
                await asyncio.sleep(10)  # 出错后短暂等待
        
        self.logger.info("停止超时检查协程")
