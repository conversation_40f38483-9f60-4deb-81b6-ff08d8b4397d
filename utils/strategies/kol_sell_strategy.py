import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import asyncio

from dao.kol_wallet_activity_dao import KOLWalletActivityDAO
from utils.common import find_index_from_list_by_ratio
from .base_strategy import BaseStrategy

logger = logging.getLogger("KOLSellStrategy")

class KOLSellStrategy(BaseStrategy):
    """基于KOL卖出行为的卖出策略"""
    
    def __init__(self, config=None):
        """初始化卖出策略
        
        Args:
            config: 策略配置
        """
        config = config or {}
        super().__init__(config)
        self.config = config
        self.kol_activity_dao = KOLWalletActivityDAO()
    
    async def generate_signals(self, buy_signals: any) -> Dict[str, Any]:
        """生成卖出信号 (注意: 此方法可能与事件驱动的should_sell有重叠，根据使用场景调整)
        
        Args:
            buy_signals: 买入信号字典，键为代币地址，值为包含kol_wallets和threshold_timestamp的字典
            
        Returns:
            Dict[str, Any]: 以代币地址为键，卖出时间戳为值的字典
        """
        if not buy_signals:
            return {}
            
        result = {}
        tasks = []
        
        for token_address, signal_info in buy_signals.items():
            tasks.append(self._process_single_sell_signal(token_address, signal_info))
        
        processed_results = await asyncio.gather(*tasks)
        
        for token_address, timestamp in processed_results:
            if timestamp is not None:
                result[token_address] = timestamp
        
        return result

    async def _process_single_sell_signal(self, token_address: str, signal_info: Dict[str, Any]) -> Tuple[str, Optional[int]]:
        """处理单个代币的卖出信号生成逻辑 (内部辅助方法)"""
        try:
            kol_wallets = signal_info.get("kol_wallets", [])
            buy_signal_time = signal_info.get("threshold_timestamp", 0)
            current_time = signal_info.get("current_time", int(datetime.now().timestamp()))

            # 添加回溯时间逻辑，与sell_signal_handler.py保持一致
            transaction_lookback_hours = self.config.get("transaction_lookback_hours", 12)
            start_time = buy_signal_time - (transaction_lookback_hours * 3600)  # 买入时间减去回溯小时数
            
            max_holding_seconds = self.config.get("sell_strategy_hours", 24) * 3600
            max_holding_end_time = buy_signal_time + max_holding_seconds
            sell_kol_ratio = self.config.get("sell_kol_ratio", 0.5)

            if not kol_wallets or buy_signal_time == 0:
                logger.warning(f"代币 {token_address} 缺少 kol_wallets 或 buy_signal_time，无法生成卖出信号")
                return token_address, None

            pipeline = [
                {
                    "$match": {
                        "token.address": token_address,
                        "wallet": {"$in": kol_wallets},
                        "event_type": "sell",
                        "timestamp": {"$gte": start_time, "$lte": max_holding_end_time}
                    }
                },
                {
                    "$sort": {"timestamp": 1}
                },
                {
                    "$group": {
                        "_id": "$wallet",
                        'first_sell_timestamp': {'$min': '$timestamp'}
                    }
                },
                {
                    "$sort": {
                        "first_sell_timestamp": 1
                    }
                }
            ]

            selling_kols_activity = await self.kol_activity_dao.aggregate(pipeline)
            logger.debug(f"卖出信号聚合查询完成，代币 {token_address} 找到 {len(selling_kols_activity)} 个原始买家在买入后首次卖出")

            if len(selling_kols_activity) / len(kol_wallets) >= sell_kol_ratio:
                # 保存阈值达到的历史时间戳作为参考
                threshold_sell_timestamp = self._calculate_sell_threshold_timestamp(
                    selling_kols_activity, len(kol_wallets), sell_kol_ratio
                )
                logger.info(f"代币 {token_address} 达到卖出比例，历史阈值时间: {datetime.fromtimestamp(threshold_sell_timestamp)}，当前检测时间: {datetime.fromtimestamp(current_time)}")
                # 使用当前检测时间作为卖出信号时间戳，符合真实交易逻辑
                return token_address, current_time
            
            logger.debug(f"代币 {token_address} 未达到卖出比例，不生成基于比例的卖出信号")
            return token_address, None

        except Exception as e:
            logger.error(f"为代币 {token_address} 生成卖出信号时出错: {e}", exc_info=True)
            return token_address, None

    async def should_sell(self,
                    token_address: str,
                    token_info: dict,
                    current_time: int) -> tuple:
        """判断是否应该卖出代币 (事件驱动框架调用)
        
        Args:
            token_address: 代币地址
            token_info: 代币持仓信息 (包含 buy_time/bought_at, kol_wallets)
            current_time: 当前模拟时间戳
            
        Returns:
            tuple: (是否应该卖出, 原因, 卖出时间戳)
        """
        buy_time = None
        if "buy_signal_timestamp" in token_info:
            buy_time = token_info["buy_signal_timestamp"]
        elif "buy_time" in token_info:
            buy_time = token_info["buy_time"]
        elif "bought_at" in token_info:
            buy_time = token_info["bought_at"]

        buy_kol_wallets = token_info.get("kol_wallets", [])

        if not buy_time or not buy_kol_wallets:
            logger.warning(f"代币 {token_address} 在时间 {current_time} 缺少 buy_time ({buy_time}) 或 kol_wallets ({len(buy_kol_wallets)})，无法判断卖出")
            return False, "缺少买入信息", current_time

        # 添加回溯时间逻辑，与sell_signal_handler.py保持一致
        transaction_lookback_hours = self.config.get("transaction_lookback_hours", 12)
        start_time = buy_time - (transaction_lookback_hours * 3600)  # 买入时间减去回溯小时数
        
        max_holding_seconds = self.config.get("sell_strategy_hours", 24) * 3600
        force_sell_timestamp = buy_time + max_holding_seconds

        if current_time >= force_sell_timestamp:
            reason = f"持有时间达到上限 ({self.config.get('sell_strategy_hours', 24)}小时, 买入于 {datetime.fromtimestamp(buy_time)})"
            logger.info(f"代币 {token_address}: {reason} at {datetime.fromtimestamp(current_time)}")
            return True, reason, current_time

        sell_ratio_threshold = self.config.get("sell_kol_ratio", 0.5)

        pipeline = [
            {
                "$match": {
                    "token.address": token_address,
                    "wallet": {"$in": buy_kol_wallets},
                    "event_type": "sell",
                    "timestamp": {"$gte": start_time, "$lte": current_time}
                }
            },
            {
                "$sort": {"timestamp": 1}
            },
            {
                "$group": {
                    "_id": "$wallet",
                    'first_sell_timestamp': {'$min': '$timestamp'}
                }
            },
            {
                "$sort": {
                    "first_sell_timestamp": 1
                }
            }
        ]

        try:
            selling_kols_activity = await self.kol_activity_dao.aggregate(pipeline)
            logger.debug(f"代币 {token_address} 在 {datetime.fromtimestamp(current_time)} 找到 {len(selling_kols_activity)}/{len(buy_kol_wallets)} 个原始买家已卖出 (自 {datetime.fromtimestamp(start_time)} 起)")
        except Exception as e:
            logger.error(f"查询代币 {token_address} 的卖出活动时出错: {e}", exc_info=True)
            return False, "数据库查询错误", current_time

        selling_kols_count = len(selling_kols_activity)
        if len(buy_kol_wallets) == 0:
            current_sell_ratio = 0
        else:
            current_sell_ratio = selling_kols_count / len(buy_kol_wallets)

        if current_sell_ratio >= sell_ratio_threshold:
            # 计算达到阈值时的历史时间戳作为参考
            threshold_sell_timestamp = self._calculate_sell_threshold_timestamp(
                selling_kols_activity, len(buy_kol_wallets), sell_ratio_threshold
            )
            reason = f"卖出KOL比例达到 {current_sell_ratio:.2%} >= {sell_ratio_threshold:.2%}"
            logger.info(f"代币 {token_address}: {reason}，历史阈值时间 {datetime.fromtimestamp(threshold_sell_timestamp)}，当前卖出时间 {datetime.fromtimestamp(current_time)}")
            # 使用当前检测时间作为卖出信号时间戳，符合真实交易逻辑
            return True, reason, current_time
        else:
            reason = f"卖出KOL比例 {current_sell_ratio:.2%} < {sell_ratio_threshold:.2%}"
            return False, reason, current_time
    
    def _calculate_sell_threshold_timestamp(self, selling_kols_activity: List[Dict], 
                                          total_kol_count: int, 
                                          sell_ratio_threshold: float) -> int:
        """计算达到卖出比例阈值时的时间戳
        
        Args:
            selling_kols_activity: 卖出的KOL活动列表，按时间排序
            total_kol_count: 总的KOL数量
            sell_ratio_threshold: 卖出比例阈值
            
        Returns:
            int: 达到阈值时的时间戳
        """
        if not selling_kols_activity:
            return int(datetime.now().timestamp())
        
        # 计算需要多少个KOL卖出才能达到阈值
        required_sell_count = max(1, int(total_kol_count * sell_ratio_threshold))
        
        # 如果达到阈值的KOL数量少于等于实际卖出数量，返回第N个KOL的卖出时间
        if len(selling_kols_activity) >= required_sell_count:
            # 返回第required_sell_count个KOL的卖出时间（按时间排序）
            target_kol = selling_kols_activity[required_sell_count - 1]
            return target_kol['first_sell_timestamp']
        
        # 否则返回最后一个KOL的卖出时间
        return selling_kols_activity[-1]['first_sell_timestamp']
