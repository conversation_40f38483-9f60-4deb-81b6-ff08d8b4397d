import unittest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, ANY
from solders.transaction_status import TransactionConfirmationStatus
from decimal import Decimal
import base64
import logging # Import logging
import os # Import os for path manipulation

from utils.trading.solana.jupiter_trade_service import JupiterTradeService, create_jupiter_trade_service
from utils.trading.solana.trade_interface import TradeResult, TradeType, TradeStatus
from beanie import PydanticObjectId
from solders.keypair import Keypair
from solders.transaction import VersionedTransaction
from solders.message import MessageV0
from solders.system_program import TransferParams, transfer
from solders.hash import Hash
from solana.rpc.core import RPCException


class TestJupiterTradeService(unittest.TestCase):
    """测试JupiterTradeService的基本功能"""
    
    def setUp(self):
        """设置测试环境"""
        # Configure logging for this test module
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        log_file_path = os.path.join(log_dir, "test_jupiter_trade_service.log")
        
        # Remove old log file if it exists
        if os.path.exists(log_file_path):
            os.remove(log_file_path)
            
        logging.basicConfig(
            level=logging.DEBUG, 
            format='%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
            filename=log_file_path,
            filemode='w' # Overwrite log file each run
        )
        # Explicitly set the level for the module under test
        logging.getLogger("utils.trading.solana.jupiter_trade_service").setLevel(logging.DEBUG)
        # If you also want to see logs in the console, add a StreamHandler
        # console_handler = logging.StreamHandler()
        # console_handler.setLevel(logging.DEBUG) # Or your preferred level for console
        # formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s')
        # console_handler.setFormatter(formatter)
        # logging.getLogger().addHandler(console_handler) # Add to root logger

        self.service = JupiterTradeService(
            jupiter_api_host="https://quote-api.jup.ag",
            solana_rpc_url="https://api.mainnet-beta.solana.com",
            http_timeout=30.0
        )
        
        self.mock_strategy_config = {
            'max_slippage_bps': 100,
            'priority_fee_lamports': 50000,
            'sell_max_slippage_bps': 150,
            'sell_priority_fee_lamports': 75000,
            'input_token_decimals': 9,
            'jupiter_api_host': 'https://quote-api.jup.ag',
            'solana_rpc_url': 'https://api.mainnet-beta.solana.com',
            'http_timeout': 30.0
        }
    
    def test_init_service(self):
        """测试服务初始化"""
        self.assertEqual(self.service.jupiter_api_host, "https://quote-api.jup.ag")
        self.assertEqual(self.service.solana_rpc_url, "https://api.mainnet-beta.solana.com")
        self.assertEqual(self.service.http_timeout, 30.0)
        self.assertTrue(hasattr(self.service, 'http_client'))
        self.assertTrue(hasattr(self.service, 'solana_client'))
    
    def test_create_jupiter_trade_service(self):
        """测试工厂函数"""
        service = create_jupiter_trade_service(self.mock_strategy_config)
        self.assertIsInstance(service, JupiterTradeService)
        self.assertEqual(service.jupiter_api_host, "https://quote-api.jup.ag")
        self.assertEqual(service.solana_rpc_url, "https://api.mainnet-beta.solana.com")
    
    def test_calculate_token_amount(self):
        """测试代币数量计算"""
        # 测试SOL数量转换为Lamports（9位精度）
        sol_amount = 0.1
        lamports = self.service._calculate_token_amount(sol_amount, 9)
        expected = int(0.1 * (10 ** 9))  # 100,000,000 lamports
        self.assertEqual(lamports, expected)
        
        # 测试SPL代币（6位精度，如USDC）
        usdc_amount = 1.5
        usdc_units = self.service._calculate_token_amount(usdc_amount, 6)
        expected = int(1.5 * (10 ** 6))  # 1,500,000 micro-USDC
        self.assertEqual(usdc_units, expected)
        
        # 测试小数点精度
        token_amount = 0.00001
        token_units = self.service._calculate_token_amount(token_amount, 9)
        expected = int(0.00001 * (10 ** 9))  # 10,000 units
        self.assertEqual(token_units, expected)
        
        # 测试不同精度的代币
        token_amount = 0.123
        for decimals in [6, 8, 9, 18]:
            token_units = self.service._calculate_token_amount(token_amount, decimals)
            expected = int(0.123 * (10 ** decimals))
            self.assertEqual(token_units, expected)
    
    async def test_close_service(self):
        """测试服务关闭"""
        # 模拟客户端关闭方法
        self.service.http_client.aclose = AsyncMock()
        self.service.solana_client.close = AsyncMock()
        
        await self.service.close()
        
        self.service.http_client.aclose.assert_called_once()
        self.service.solana_client.close.assert_called_once()
    
    @patch('httpx.AsyncClient.get')
    async def test_get_jupiter_quote_success(self, mock_get):
        """测试成功获取Jupiter报价"""
        # 模拟Jupiter API响应
        mock_response = Mock()
        mock_response.raise_for_status = Mock()
        mock_response.json.return_value = {
            "inputMint": "So11111111111111111111111111111111111111112",
            "inAmount": "100000000",
            "outputMint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
            "outAmount": "98000000",
            "routePlan": []
        }
        mock_get.return_value = mock_response
        
        quote_data = await self.service._get_jupiter_quote(
            input_mint="So11111111111111111111111111111111111111112",
            output_mint="EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
            amount=100000000,
            slippage_bps=100,
            only_direct_routes=True  # 测试新的直接路由参数
        )
        
        self.assertEqual(quote_data["inAmount"], "100000000")
        self.assertEqual(quote_data["outAmount"], "98000000")
        mock_get.assert_called_once()
    
    @patch('httpx.AsyncClient.get')
    def test_get_jupiter_quote_error(self, mock_get):
        """测试Jupiter报价API错误"""
        async def async_test():
            # 模拟API错误响应
            mock_response = Mock()
            mock_response.raise_for_status = Mock()
            mock_response.json.return_value = {
                "error": "Invalid input mint"
            }
            mock_get.return_value = mock_response
            
            with self.assertRaises(ValueError) as context:
                await self.service._get_jupiter_quote(
                    input_mint="invalid_mint",
                    output_mint="EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
                    amount=100000000,
                    slippage_bps=100,
                    only_direct_routes=True
                )
            
            self.assertIn("Jupiter quote error", str(context.exception))
        
        asyncio.run(async_test())
    
    @patch('httpx.AsyncClient.post')
    def test_get_jupiter_swap_transaction_success(self, mock_post):
        """测试成功获取Jupiter交换交易"""
        async def async_test():
            # 模拟API响应
            mock_response = Mock()
            mock_response.raise_for_status = Mock()
            mock_response.json.return_value = {
                "swapTransaction": "base64_encoded_transaction_data"
            }
            mock_post.return_value = mock_response
            
            quote_data = {
                "inputMint": "So11111111111111111111111111111111111111112",
                "inAmount": "100000000",
                "outputMint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
                "outAmount": "98000000"
            }
            
            transaction_base64 = await self.service._get_jupiter_swap_transaction(
                quote_data=quote_data,
                user_public_key="test_public_key",
                priority_fee_lamports=50000
            )
            
            self.assertEqual(transaction_base64, "base64_encoded_transaction_data")
            mock_post.assert_called_once()
            
            # 验证请求参数
            call_args = mock_post.call_args
            self.assertEqual(call_args[1]['json']['userPublicKey'], "test_public_key")
            self.assertEqual(call_args[1]['json']['prioritizationFeeLamports'], 50000)
        
        asyncio.run(async_test())
    
    def test_execute_trade_invalid_private_key(self):
        """测试无效私钥错误处理"""
        async def async_test():
            trade_record_id = PydanticObjectId()
            signal_id = PydanticObjectId()
            
            result = await self.service.execute_trade(
                trade_type=TradeType.BUY,
                input_token_address="So11111111111111111111111111111111111111112",
                output_token_address="EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
                amount_input_token=0.1,
                wallet_private_key_b58="invalid_key",
                wallet_address="test_address",
                strategy_snapshot=self.mock_strategy_config,
                signal_id=signal_id,
                trade_record_id=trade_record_id
            )
            
            self.assertEqual(result.status, TradeStatus.FAILED)
            self.assertIn("Invalid wallet private key", result.error_message)
        
        asyncio.run(async_test())
    
    def test_end_to_end_trade_execution_with_smart_routing(self):
        """测试端到端交易执行 - 重点验证智能路由配置的传递"""
        async def async_test():
            # 使用最少的Mock，重点验证配置传递
            with patch('httpx.AsyncClient.get') as mock_get, \
                 patch('httpx.AsyncClient.post') as mock_post, \
                 patch.object(self.service.solana_client, 'send_raw_transaction') as mock_send, \
                 patch.object(self.service.solana_client, 'get_signature_statuses') as mock_status, \
                 patch('base58.b58decode') as mock_b58decode, \
                 patch('utils.trading.solana.jupiter_trade_service.Keypair.from_bytes') as mock_keypair, \
                 patch.object(self.service, '_get_token_decimals') as mock_get_decimals, \
                 patch('utils.trading.solana.jupiter_trade_service.VersionedTransaction.from_bytes') as mock_tx_from_bytes, \
                 patch('utils.trading.solana.jupiter_trade_service.VersionedTransaction') as mock_versioned_tx, \
                 patch('base64.b64decode') as mock_b64decode, \
                 patch('utils.trading.solana.jupiter_trade_service.Signature.from_string') as mock_signature:
                
                # 设置基本Mock
                mock_b58decode.return_value = b'mock_key_bytes'
                mock_keypair.return_value = Mock()
                mock_get_decimals.return_value = 9  # SOL的精度为9
                
                # Mock transaction related objects
                mock_b64decode.return_value = b'mock_transaction_bytes'
                mock_unsigned_tx = Mock()
                mock_unsigned_tx.message = Mock()
                mock_tx_from_bytes.return_value = mock_unsigned_tx
                
                # 创建一个自定义类来模拟VersionedTransaction实例
                class MockVersionedTransaction:
                    def __init__(self, *args, **kwargs):
                        pass
                    
                    def __bytes__(self):
                        return b'signed_transaction_bytes'
                
                # 设置VersionedTransaction构造函数的行为
                mock_versioned_tx.side_effect = lambda *args, **kwargs: MockVersionedTransaction()
                
                # Mock Jupiter quote API
                mock_quote_response = Mock()
                mock_quote_response.raise_for_status = Mock()
                mock_quote_response.json.return_value = {
                    "inputMint": "So11111111111111111111111111111111111111112",
                    "inAmount": "100000000",
                    "outputMint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
                    "outAmount": "98000000",
                    "routePlan": []
                }
                mock_get.return_value = mock_quote_response
                
                # Mock Jupiter swap API
                mock_swap_response = Mock()
                mock_swap_response.raise_for_status = Mock()
                mock_swap_response.json.return_value = {
                    "swapTransaction": "base64_encoded_transaction_data"
                }
                mock_post.return_value = mock_swap_response
                
                # Mock Solana transaction
                mock_send_result = Mock()
                mock_send_result.value = "transaction_hash_123"
                mock_send.return_value = mock_send_result
                
                # Mock transaction confirmation
                mock_status_obj = Mock()
                mock_status_obj.err = None
                mock_status_obj.confirmation_status = TransactionConfirmationStatus.Confirmed
                mock_status_result = Mock()
                mock_status_result.value = [mock_status_obj]
                mock_status.return_value = mock_status_result
                
                # 测试智能路由禁用（默认）
                strategy_config_direct = {
                    'max_slippage_bps': 100,
                    'priority_fee_lamports': 50000,
                    # 不设置enable_smart_routing，应该默认禁用
                }
                
                trade_record_id = PydanticObjectId()
                signal_id = PydanticObjectId()
                
                result = await self.service.execute_trade(
                    trade_type=TradeType.BUY,
                    input_token_address="So11111111111111111111111111111111111111112",
                    output_token_address="EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
                    amount_input_token=0.1,
                    wallet_private_key_b58="valid_base58_key",
                    wallet_address="valid_address",
                    strategy_snapshot=strategy_config_direct,
                    signal_id=signal_id,
                    trade_record_id=trade_record_id
                )
                
                # 验证交易成功
                if result.status != TradeStatus.SUCCESS:
                    self.fail(f"Trade failed with status: {result.status}, error: {result.error_message}, provider_response: {result.provider_response_raw}")
                self.assertEqual(result.status, TradeStatus.SUCCESS)
                self.assertEqual(result.tx_hash, "transaction_hash_123")
                
                # 关键验证：检查Jupiter quote API调用参数
                mock_get.assert_called_once()
                call_args = mock_get.call_args
                params = call_args[1]['params']
                self.assertEqual(params['onlyDirectRoutes'], 'true',
                               "默认应该使用直接路由（禁用智能路由）")
                
                # 重置Mock并测试智能路由启用
                mock_get.reset_mock()
                
                strategy_config_smart = {
                    'max_slippage_bps': 100,
                    'priority_fee_lamports': 50000,
                    'enable_smart_routing': True  # 显式启用智能路由
                }
                
                result2 = await self.service.execute_trade(
                    trade_type=TradeType.BUY,
                    input_token_address="So11111111111111111111111111111111111111112",
                    output_token_address="EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
                    amount_input_token=0.1,
                    wallet_private_key_b58="valid_base58_key",
                    wallet_address="valid_address",
                    strategy_snapshot=strategy_config_smart,
                    signal_id=signal_id,
                    trade_record_id=trade_record_id
                )
                
                # 验证第二次交易也成功
                self.assertEqual(result2.status, TradeStatus.SUCCESS)
                
                # 关键验证：检查智能路由参数
                mock_get.assert_called()
                call_args = mock_get.call_args
                params = call_args[1]['params']
                self.assertEqual(params['onlyDirectRoutes'], 'false',
                               "启用智能路由时应该设置onlyDirectRoutes=false")
        
        asyncio.run(async_test())
    
    def test_sign_and_send_transaction_fixed(self):
        """测试VersionedTransaction签名方法已修复
        
        这个测试用例验证修复后的_sign_and_send_transaction方法能够正确处理
        VersionedTransaction的签名，不再抛出AttributeError。
        """
        async def async_test():
            # 创建一个真实的VersionedTransaction来模拟Jupiter API返回的数据
            sender_keypair = Keypair()
            receiver_keypair = Keypair()
            
            # 创建一个简单的转账指令
            instruction = transfer(
                TransferParams(
                    from_pubkey=sender_keypair.pubkey(),
                    to_pubkey=receiver_keypair.pubkey(),
                    lamports=1000000
                )
            )
            
            # 创建消息
            message = MessageV0.try_compile(
                payer=sender_keypair.pubkey(),
                instructions=[instruction],
                address_lookup_table_accounts=[],
                recent_blockhash=Hash.default()
            )
            
            # 创建一个已签名的交易（模拟Jupiter API返回的格式）
            # 但稍后我们会通过from_bytes重新创建，这样就会失去签名状态
            signed_transaction = VersionedTransaction(message, [sender_keypair])
            
            # 将交易序列化为base64，模拟Jupiter API的返回
            transaction_bytes = bytes(signed_transaction)
            transaction_base64 = base64.b64encode(transaction_bytes).decode('utf-8')
            
            # 创建用于签名的钱包
            wallet_keypair = sender_keypair
            trade_record_id = PydanticObjectId()
            attempt_number = 1
            
            # 模拟Solana客户端的send_raw_transaction方法
            mock_send_result = Mock()
            mock_send_result.value = "mock_transaction_hash_123"
            
            with patch.object(self.service.solana_client, 'send_raw_transaction', 
                            return_value=mock_send_result) as mock_send:
                
                # 修复后，这里应该能正常工作，不再抛出AttributeError
                try:
                    tx_hash = await self.service._sign_and_send_transaction(
                        transaction_base64=transaction_base64,
                        wallet_keypair=wallet_keypair,
                        trade_record_id=trade_record_id,
                        attempt_number=attempt_number
                    )
                    
                    # 验证返回了交易哈希
                    self.assertEqual(tx_hash, "mock_transaction_hash_123")
                    
                    # 验证send_raw_transaction被调用了
                    mock_send.assert_called_once()
                    
                except AttributeError as e:
                    # 如果仍然抛出AttributeError，说明修复失败
                    self.fail(f"修复失败，仍然抛出AttributeError: {e}")
        
        asyncio.run(async_test())
    
    def test_wait_for_transaction_confirmation_signature_conversion(self):
        """测试等待交易确认时正确处理Signature对象转换
        
        这个测试验证修复后的_wait_for_transaction_confirmation方法能够正确
        将字符串tx_hash转换为Signature对象，不再抛出类型错误。
        """
        async def async_test():
            trade_record_id = PydanticObjectId()
            attempt_number = 1
            test_tx_hash = "3nLDCpJryp5G3XG6PhMmi3aLhKqp6f7EGvDKpUxZuy9kd63jynALUXWVcCZ8d3aHUuMZphBBQCzvjWkJyV7oW7TY"
            
            # 模拟get_signature_statuses返回确认状态
            mock_status = Mock()
            mock_status.err = None
            mock_status.confirmation_status = TransactionConfirmationStatus.Confirmed
            
            mock_result = Mock()
            mock_result.value = [mock_status]
            
            with patch.object(self.service.solana_client, 'get_signature_statuses', 
                            return_value=mock_result) as mock_get_status:
                
                # 修复后，这里应该能正常工作，正确转换Signature对象
                is_confirmed = await self.service._wait_for_transaction_confirmation(
                    tx_hash=test_tx_hash,
                    trade_record_id=trade_record_id,
                    attempt_number=attempt_number,
                    max_wait_seconds=1  # 短超时以便快速测试
                )
                
                # 验证返回确认成功
                self.assertTrue(is_confirmed)
                
                # 验证get_signature_statuses被调用，且参数是Signature对象
                mock_get_status.assert_called()
                call_args = mock_get_status.call_args[0][0]  # 第一个参数是signatures列表
                self.assertEqual(len(call_args), 1)
                # 验证传入的是Signature对象而不是字符串
                from solders.signature import Signature
                self.assertIsInstance(call_args[0], Signature)
        
        asyncio.run(async_test())
    
    def test_versioned_transaction_creation_and_serialization(self):
        """测试VersionedTransaction的正确创建和序列化
        
        这个测试验证我们使用正确的方法创建VersionedTransaction
        并使用bytes()进行序列化，而不是不存在的sign()和serialize()方法。
        """
        async def async_test():
            # 创建测试用的密钥对
            sender_keypair = Keypair()
            receiver_keypair = Keypair()
            
            # 创建转账指令
            instruction = transfer(
                TransferParams(
                    from_pubkey=sender_keypair.pubkey(),
                    to_pubkey=receiver_keypair.pubkey(),
                    lamports=1000000
                )
            )
            
            # 创建消息
            message = MessageV0.try_compile(
                payer=sender_keypair.pubkey(),
                instructions=[instruction],
                address_lookup_table_accounts=[],
                recent_blockhash=Hash.default()
            )
            
            # 测试1: 验证VersionedTransaction可以正确创建（包含签名者）
            try:
                versioned_tx = VersionedTransaction(message, [sender_keypair])
                self.assertIsNotNone(versioned_tx)
            except Exception as e:
                self.fail(f"VersionedTransaction创建失败: {e}")
            
            # 测试2: 验证可以使用bytes()进行序列化
            try:
                transaction_bytes = bytes(versioned_tx)
                self.assertIsInstance(transaction_bytes, bytes)
                self.assertGreater(len(transaction_bytes), 0)
            except Exception as e:
                self.fail(f"VersionedTransaction序列化失败: {e}")
            
            # 测试3: 验证可以从bytes重新构建交易
            try:
                reconstructed_tx = VersionedTransaction.from_bytes(transaction_bytes)
                self.assertIsNotNone(reconstructed_tx)
                # 验证消息内容相同
                self.assertEqual(reconstructed_tx.message, message)
            except Exception as e:
                self.fail(f"VersionedTransaction反序列化失败: {e}")
            
            # 测试4: 验证旧的错误方法确实不存在
            with self.assertRaises(AttributeError):
                versioned_tx.sign([sender_keypair])  # 这个方法不应该存在
                
            with self.assertRaises(AttributeError):
                versioned_tx.serialize()  # 这个方法也不应该存在
        
        asyncio.run(async_test())
    
    def test_signature_from_string_conversion(self):
        """测试Signature.from_string方法正确转换字符串交易哈希
        
        验证我们可以正确地将字符串格式的交易哈希转换为Signature对象。
        """
        # 测试有效的交易哈希字符串
        test_tx_hash = "3nLDCpJryp5G3XG6PhMmi3aLhKqp6f7EGvDKpUxZuy9kd63jynALUXWVcCZ8d3aHUuMZphBBQCzvjWkJyV7oW7TY"
        
        try:
            from solders.signature import Signature
            signature_obj = Signature.from_string(test_tx_hash)
            self.assertIsInstance(signature_obj, Signature)
            # 验证转换后的签名对象可以转回字符串
            self.assertEqual(str(signature_obj), test_tx_hash)
        except Exception as e:
            self.fail(f"Signature.from_string转换失败: {e}")
            
        # 测试无效的交易哈希字符串应该抛出异常
        invalid_tx_hash = "invalid_hash_string"
        with self.assertRaises(Exception):
            Signature.from_string(invalid_tx_hash)
    
    def test_wait_for_transaction_confirmation_debug_status(self):
        """测试调试交易确认状态返回值
        
        这个测试用例专门用来调试get_signature_statuses返回的实际状态格式，
        以便找出为什么真实交易确认失败的原因。特别检查processed状态的处理。
        """
        async def async_test():
            trade_record_id = PydanticObjectId()
            attempt_number = 1
            test_tx_hash = "sKxj8d1kFhNZnEytsrqwCgym9xLbiyodqZGf489LEmXrzMZ9DSkk1N2pCJRJxC7c3hp4kP3QPQdp9tGpyZagrC3"
            
            # 模拟各种可能的状态返回值来测试确认逻辑
            test_cases = [
                # 情况1: 状态为None (交易未找到)
                {
                    "name": "status_none",
                    "mock_value": [None],
                    "expected": False
                },
                # 情况2: 状态存在但没有确认状态
                {
                    "name": "no_confirmation_status",
                    "mock_value": [Mock(err=None, confirmation_status=None)],
                    "expected": False
                },
                # 情况3: 状态有错误
                {
                    "name": "status_with_error",
                    "mock_value": [Mock(err="Transaction failed", confirmation_status=None)],
                    "expected": False
                },
                                # 情况4: 确认状态为"confirmed"
                {
                    "name": "confirmed_status",
                    "mock_value": [Mock(err=None, confirmation_status=TransactionConfirmationStatus.Confirmed)],
                    "expected": True
                },
                # 情况5: 确认状态为"finalized"
                {
                    "name": "finalized_status",
                    "mock_value": [Mock(err=None, confirmation_status=TransactionConfirmationStatus.Finalized)],
                    "expected": True
                },
                # 情况6: 确认状态为"processed"（可能是实际返回的状态）
                {
                    "name": "processed_status",
                    "mock_value": [Mock(err=None, confirmation_status=TransactionConfirmationStatus.Processed)],
                    "expected": True  # 修复后接受processed状态
                },
                # 情况7: result.value为空
                {
                    "name": "empty_result_value",
                    "mock_value": [],
                    "expected": False
                }
            ]
            
            for test_case in test_cases:
                with self.subTest(case=test_case["name"]):
                    mock_result = Mock()
                    mock_result.value = test_case["mock_value"]
                    
                    with patch.object(self.service.solana_client, 'get_signature_statuses', 
                                    return_value=mock_result) as mock_get_status:
                        
                        # 使用短超时快速测试
                        is_confirmed = await self.service._wait_for_transaction_confirmation(
                            tx_hash=test_tx_hash,
                            trade_record_id=trade_record_id,
                            attempt_number=attempt_number,
                            max_wait_seconds=0.1  # 很短的超时，立即检查一次就退出
                        )
                        
                        # 验证返回结果符合预期
                        self.assertEqual(is_confirmed, test_case["expected"], 
                                      f"Case {test_case['name']} failed: expected {test_case['expected']}, got {is_confirmed}")
        
        asyncio.run(async_test())
    
    def test_wait_for_transaction_confirmation_accepts_processed_status(self):
        """测试确认逻辑接受processed状态
        
        这个测试验证修复后的确认逻辑能够接受processed状态，
        这解决了真实交易成功但显示为pending的问题。
        """
        async def async_test():
            trade_record_id = PydanticObjectId()
            attempt_number = 1
            test_tx_hash = "sKxj8d1kFhNZnEytsrqwCgym9xLbiyodqZGf489LEmXrzMZ9DSkk1N2pCJRJxC7c3hp4kP3QPQdp9tGpyZagrC3"
            
            # 模拟processed状态（交易在块中但未获得大多数确认）
            mock_status = Mock()
            mock_status.err = None
            mock_status.confirmation_status = TransactionConfirmationStatus.Processed
            
            mock_result = Mock()
            mock_result.value = [mock_status]
            
            with patch.object(self.service.solana_client, 'get_signature_statuses', 
                            return_value=mock_result) as mock_get_status:
                
                # 修复后应该接受processed状态
                is_confirmed = await self.service._wait_for_transaction_confirmation(
                    tx_hash=test_tx_hash,
                    trade_record_id=trade_record_id,
                    attempt_number=attempt_number,
                    max_wait_seconds=1  # 短超时以便快速测试
                )
                
                # 验证返回确认成功（即使是processed状态）
                self.assertTrue(is_confirmed)
                
                # 验证get_signature_statuses被调用并且使用了search_transaction_history=True
                mock_get_status.assert_called_with([ANY], search_transaction_history=True)
        
        asyncio.run(async_test())
    
    def test_factory_function_with_defaults(self):
        """测试工厂函数的默认配置"""
        service = create_jupiter_trade_service({})
        self.assertEqual(service.jupiter_api_host, "https://quote-api.jup.ag")
        self.assertEqual(service.solana_rpc_url, "https://api.mainnet-beta.solana.com")
        self.assertEqual(service.http_timeout, 30.0)
    
    def test_factory_function_with_custom_config(self):
        """测试工厂函数的自定义配置"""
        custom_config = {
            'jupiter_api_host': 'https://custom-jupiter.example.com',
            'solana_rpc_url': 'https://custom-rpc.example.com',
            'http_timeout': 60.0
        }
        
        service = create_jupiter_trade_service(custom_config)
        self.assertEqual(service.jupiter_api_host, "https://custom-jupiter.example.com")
        self.assertEqual(service.solana_rpc_url, "https://custom-rpc.example.com")
        self.assertEqual(service.http_timeout, 60.0)
        
    @patch('httpx.AsyncClient.get')
    def test_direct_routes_configuration(self, mock_get):
        """测试直接路由配置功能 - Bug #7修复验证"""
        async def async_test():
            # 模拟Jupiter API响应
            mock_response = Mock()
            mock_response.raise_for_status = Mock()
            mock_response.json.return_value = {
                "inputMint": "So11111111111111111111111111111111111111112",
                "inAmount": "100000000",
                "outputMint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
                "outAmount": "98000000",
                "routePlan": []
            }
            mock_get.return_value = mock_response
            
            # 测试直接路由（only_direct_routes=True）
            await self.service._get_jupiter_quote(
                input_mint="So11111111111111111111111111111111111111112",
                output_mint="EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
                amount=100000000,
                slippage_bps=100,
                only_direct_routes=True
            )
            
            # 验证API调用参数 - 应该包含onlyDirectRoutes=true
            call_args = mock_get.call_args
            params = call_args[1]['params']
            self.assertEqual(params['onlyDirectRoutes'], 'true')
            
            # 测试智能路由（only_direct_routes=False）
            await self.service._get_jupiter_quote(
                input_mint="So11111111111111111111111111111111111111112",
                output_mint="EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
                amount=100000000,
                slippage_bps=100,
                only_direct_routes=False
            )
            
            # 验证API调用参数 - 应该包含onlyDirectRoutes=false
            call_args = mock_get.call_args
            params = call_args[1]['params']
            self.assertEqual(params['onlyDirectRoutes'], 'false')
        
        asyncio.run(async_test())
        

    # ==================== R1.5 Jupiter专业错误识别测试 ====================
    
    def test_jupiter_slippage_error_identification(self) -> None:
        """测试Jupiter特定的滑点错误识别"""
        # 测试Jupiter API特定的滑点错误关键词
        jupiter_slippage_errors = [
            "slippage tolerance exceeded",
            "price impact too high",
            "insufficient output amount", 
            "would result in less than minimum",
            "minimum received not met",
            "exceeds maximum slippage",
            "price moved too much",
            "jupiter slippage exceeded",
            "jupiter price impact",
            "solana dex slippage",
            "swap failed due to slippage",
            "price impact exceeds limit",
            "transaction simulation failed",
            "simulation failed due to slippage",
            "route not found due to slippage",
            "jupiter swap slippage exceeded"
        ]
        
        for error_msg in jupiter_slippage_errors:
            with self.subTest(error_message=error_msg):
                result = self.service.is_slippage_related_error(error_msg)
                self.assertTrue(result, f"应该识别为滑点错误: {error_msg}")
    
    def test_jupiter_slippage_error_identification_with_provider_response(self) -> None:
        """测试通过provider_response识别Jupiter滑点错误"""
        # 测试Jupiter API响应中的错误码
        test_cases = [
            {
                'provider_response': {
                    'error': 'slippage tolerance exceeded',
                    'message': 'Transaction failed due to slippage'
                },
                'expected': True
            },
            {
                'provider_response': {
                    'status': 'failed',
                    'errorCode': 'SLIPPAGE_TOLERANCE_EXCEEDED',
                    'impact_percent': 5.2
                },
                'expected': True
            },
            {
                'provider_response': {
                    'message': 'price impact too high',
                    'expected_output': 1000,
                    'actual_output': 950
                },
                'expected': True
            },
            {
                'provider_response': {
                    'details': 'simulation failed due to slippage'
                },
                'expected': True
            },
            {
                'name': 'route_not_found_error',
                'provider_response': {
                    'errorCode': 'ROUTE_NOT_FOUND'
                },
                'expected': False
            },
            {
                'provider_response': {
                    'error': 'NETWORK_ERROR',
                    'message': 'RPC connection failed'
                },
                'expected': False
            }
        ]
        
        for i, test_case in enumerate(test_cases):
            with self.subTest(case=i):
                # Reset mocks for each subtest if necessary, though not strictly needed here as we are testing return value primarily
                
                # Log the test case provider_response directly from the test
                logging.debug(f"[TestRunner] Processing case {i} with error: {test_case.get('error_message')}, provider_response: {test_case['provider_response']}")

                result = self.service.is_slippage_related_error(
                    error_message=test_case.get("error_message"), # 修复：使用正确的参数名
                    provider_response=test_case["provider_response"]
                )
                self.assertEqual(result, test_case['expected'])
    
    def test_jupiter_non_retryable_error_identification(self) -> None:
        """测试Jupiter特定的不可重试错误识别"""
        # 测试Jupiter特定的不可重试错误关键词（基于实际实现）
        jupiter_non_retryable_errors = [
            # 父类通用错误（会被识别）
            "insufficient funds",
            "insufficient balance",
            "token not found",
            "invalid token",
            "unauthorized",
            "forbidden",
            "account not found",
            "invalid private key",
            "signature verification failed",
            
            # Jupiter特定错误关键词
            "invalid wallet",
            "wallet not found",
            "private key invalid",
            "token not supported",
            "invalid token address",
            "mint not found",
            "token account not found",
            "invalid parameters",
            "malformed request",
            "invalid amount",
            "amount too small",
            "amount too large",
            "api key invalid",
            "invalid input mint",
            "invalid output mint"
        ]
        
        for error_msg in jupiter_non_retryable_errors:
            with self.subTest(error_message=error_msg):
                result = self.service.is_non_retryable_error(error_msg)
                self.assertTrue(result, f"应该识别为不可重试错误: {error_msg}")
    
    def test_jupiter_non_retryable_error_identification_with_provider_response(self) -> None:
        """测试通过provider_response识别Jupiter不可重试错误"""
        test_cases = [
            {
                'provider_response': {
                    'error': 'INSUFFICIENT_FUNDS',
                    'balance': 0.001,
                    'required': 0.01
                },
                'expected': True
            },
            {
                'provider_response': {
                    'status': 'failed',
                    'error_code': 'INVALID_TOKEN',
                    'token_address': 'invalid_mint_address'
                },
                'expected': True
            },
            {
                'provider_response': {
                    'jupiter_response': {
                        'error': 'AUTHENTICATION_FAILED',
                        'message': 'Invalid API credentials'
                    }
                },
                'expected': True
            },
            {
                'provider_response': {
                    'solana_response': {
                        'error': 'ACCOUNT_NOT_FOUND',
                        'account': 'wallet_address'
                    }
                },
                'expected': True
            },
            {
                'provider_response': {
                    'swap_result': {
                        'error': 'PROGRAM_ACCOUNT_NOT_FOUND',
                        'program_id': 'jupiter_program'
                    }
                },
                'expected': True
            },
            {
                'provider_response': {
                    'error': 'TEMPORARY_NETWORK_ERROR',
                    'message': 'RPC temporarily unavailable'
                },
                'expected': False
            }
        ]
        
        for i, test_case in enumerate(test_cases):
            with self.subTest(case=i):
                result = self.service.is_non_retryable_error(
                    None,
                    test_case['provider_response']
                )
                self.assertEqual(result, test_case['expected'])
    
    def test_jupiter_retryable_errors_not_flagged_as_non_retryable(self) -> None:
        """测试可重试错误不会被误识别为不可重试错误"""
        retryable_errors = [
            "network timeout",
            "connection refused",
            "rpc temporarily unavailable",
            "rate limit exceeded",
            "service unavailable",
            "internal server error",
            "gateway timeout",
            "slippage tolerance exceeded",  # 滑点错误是可重试的
            "jupiter api rate limited",
            "solana rpc overloaded",
            "transaction timeout",
            "block height exceeded"
        ]
        
        for error_msg in retryable_errors:
            with self.subTest(error_message=error_msg):
                result = self.service.is_non_retryable_error(error_msg)
                self.assertFalse(result, f"不应该识别为不可重试错误: {error_msg}")
    
    def test_jupiter_non_slippage_errors_not_flagged_as_slippage(self) -> None:
        """测试非滑点错误不会被误识别为滑点错误"""
        non_slippage_errors = [
            "network timeout",
            "connection refused",
            "rpc error",
            "insufficient funds",
            "token not found",
            "unauthorized",
            "rate limit exceeded",
            "internal server error",
            "jupiter api error",
            "solana rpc error",
            "transaction failed",
            "block height exceeded"
        ]
        
        for error_msg in non_slippage_errors:
            with self.subTest(error_message=error_msg):
                result = self.service.is_slippage_related_error(error_msg)
                self.assertFalse(result, f"不应该识别为滑点错误: {error_msg}")
    
    
    
    def test_jupiter_complex_provider_response_parsing(self) -> None:
        """测试Jupiter复杂provider_response的解析"""
        # 测试嵌套的复杂响应结构
        complex_response = {
            'status': 'failed',
            'jupiter_response': {
                'error': 'SLIPPAGE_EXCEEDED',
                'details': {
                    'route': {
                        'input_mint': 'So11111111111111111111111111111111111111112',
                        'output_mint': 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
                        'amount_in': 1000000,
                        'amount_out': 950000,
                        'slippage_bps': 250
                    },
                    'simulation_result': {
                        'error': 'InsufficientOutputAmount',
                        'expected_minimum': 975000,
                        'actual_output': 950000
                    }
                }
            },
            'solana_response': {
                'transaction_error': {
                    'InstructionError': [0, 'Custom(6001)']
                }
            }
        }
        
        # 应该识别为滑点错误
        result = self.service.is_slippage_related_error(
            None,
            complex_response
        )
        self.assertTrue(result)
        
        # 不应该识别为不可重试错误
        result = self.service.is_non_retryable_error(
            None,
            complex_response
        )
        self.assertFalse(result)

    def test_identify_jupiter_custom_error_6001_as_slippage(self) -> None:
        """测试将 Jupiter 自定义程序错误 0x1771 (6001) 识别为滑点错误。"""
        service = JupiterTradeService("http://dummy-quote.jup.ag", "http://dummy-rpc.solana.com")
        
        mock_rpc_exception_data_6001 = {
            "message": "Transaction simulation failed: Error processing Instruction 6: custom program error: 0x1771",
            "data": {"err": {"InstructionError": [6, {"Custom": 6001}]}}
        }
        error_6001 = RPCException(mock_rpc_exception_data_6001)

        # 使用 patch 模块级别的 logger
        with patch('utils.trading.solana.jupiter_trade_service.logger.info') as mock_logger_info:
            result = service.is_slippage_related_error(error_6001)
            self.assertTrue(result, "错误 0x1771 (6001) 应被识别为滑点相关错误")
            mock_logger_info.assert_any_call("Identified Jupiter program error 0x1771 (6001) via RPCException.args as a slippage-related error.")

        # 测试一个非 RPCException 错误，确保它不会被错误识别（除非满足其他条件）
        non_rpc_error_slippage_msg = "this is a slippage tolerance exceeded error"
        with patch('utils.trading.solana.jupiter_trade_service.logger.info') as mock_logger_info_2:
            result_non_rpc = service.is_slippage_related_error(non_rpc_error_slippage_msg)
            self.assertTrue(result_non_rpc, "包含滑点关键词的字符串应被识别为滑点相关错误")
            # 修正期望的日志消息以匹配实际的日志输出
            # 实际日志包含 "with keyword '...'"
            # 我们需要找到匹配的关键词是什么。假设它是 "slippage tolerance exceeded"
            # 或者更简单地，检查日志消息是否以期望的前缀开始
            found_log = False
            expected_log_prefix = f"Identified slippage from error message '{non_rpc_error_slippage_msg}'"
            for call_args in mock_logger_info_2.call_args_list:
                logged_message = call_args[0][0] # 第一个参数是日志消息字符串
                if logged_message.startswith(expected_log_prefix) and "with keyword" in logged_message:
                    found_log = True
                    break
            self.assertTrue(found_log, f"Expected log starting with '{expected_log_prefix}' and containing 'with keyword' not found.")

        # 测试一个与0x1771无关的RPCException
        other_rpc_error_data = {
            "message": "Some other RPC error",
            "data": {"err": {"InstructionError": [1, {"Custom": 1234}]}}
        }
        other_rpc_error = RPCException(other_rpc_error_data)
        with patch('utils.trading.solana.jupiter_trade_service.logger.info') as mock_logger_info_3:
            result_other_rpc = service.is_slippage_related_error(other_rpc_error)
            self.assertFalse(result_other_rpc, "与0x1771无关的RPCException (且无滑点关键词) 不应被识别为滑点相关错误")
            # 验证 logger 没有因为这个错误而被调用（除非有其他匹配）
            # 这里假设其他滑点关键词检查不匹配这个特定错误消息
            # 如果要确保不调用，需要更复杂的mock或检查调用次数


    @patch('utils.spiders.solana.token_info.TokenInfo.get_token_info')
    def test_get_token_decimals_sol(self, mock_get_token_info):
        """测试SOL代币精度获取"""
        async def async_test():
            # SOL地址应该直接返回9，不需要查询TokenInfo
            decimals = await self.service._get_token_decimals("So11111111111111111111111111111111111111112")
            self.assertEqual(decimals, 9)
            
            # 验证没有调用TokenInfo
            mock_get_token_info.assert_not_called()
        
        asyncio.run(async_test())

    @patch('utils.spiders.solana.token_info.TokenInfo.get_token_info')
    def test_get_token_decimals_spl_token_success(self, mock_get_token_info):
        """测试SPL代币精度获取成功"""
        async def async_test():
            # 模拟TokenInfo返回代币信息
            mock_token_info = {
                'address': '2fdtCHuvyLcD2q86XZGFmYbDux9ZbbUgMmFhzChqmoon',
                'decimals': 9,
                'name': 'Test Token',
                'symbol': 'TEST'
            }
            mock_get_token_info.return_value = mock_token_info
            
            token_address = "2fdtCHuvyLcD2q86XZGFmYbDux9ZbbUgMmFhzChqmoon"
            decimals = await self.service._get_token_decimals(token_address)
            
            self.assertEqual(decimals, 9)
            mock_get_token_info.assert_called_once()
            
            # 验证缓存机制
            decimals2 = await self.service._get_token_decimals(token_address)
            self.assertEqual(decimals2, 9)
            # 第二次调用应该使用缓存，不再调用TokenInfo
            mock_get_token_info.assert_called_once()  # 仍然只调用一次
        
        asyncio.run(async_test())

    @patch('utils.spiders.solana.token_info.TokenInfo.get_token_info')
    def test_get_token_decimals_spl_token_failure(self, mock_get_token_info):
        """测试SPL代币精度获取失败时的降级处理"""
        async def async_test():
            # 模拟TokenInfo返回None或抛出异常
            mock_get_token_info.return_value = None
            
            token_address = "invalid_token_address"
            decimals = await self.service._get_token_decimals(token_address)
            
            # 应该返回默认精度6
            self.assertEqual(decimals, 6)
            mock_get_token_info.assert_called_once()
        
        asyncio.run(async_test())

    @patch('utils.spiders.solana.token_info.TokenInfo.get_token_info')
    def test_get_token_decimals_missing_decimals_field(self, mock_get_token_info):
        """测试代币信息缺少decimals字段时的处理"""
        async def async_test():
            # 模拟TokenInfo返回的信息缺少decimals字段
            mock_token_info = {
                'address': '2fdtCHuvyLcD2q86XZGFmYbDux9ZbbUgMmFhzChqmoon',
                'name': 'Test Token',
                'symbol': 'TEST'
                # 缺少decimals字段
            }
            mock_get_token_info.return_value = mock_token_info
            
            token_address = "2fdtCHuvyLcD2q86XZGFmYbDux9ZbbUgMmFhzChqmoon"
            decimals = await self.service._get_token_decimals(token_address)
            
            # 应该返回默认精度6
            self.assertEqual(decimals, 6)
            mock_get_token_info.assert_called_once()
        
        asyncio.run(async_test())

    def test_token_decimals_integration_with_cache(self):
        """测试代币精度获取的集成功能，包括缓存机制"""
        async def async_test():
            # 使用真实的TokenInfo Mock，测试缓存行为
            with patch('utils.spiders.solana.token_info.TokenInfo') as mock_token_info_class:
                mock_token_info_instance = AsyncMock()
                mock_token_info_instance.get_token_info.return_value = {
                    'address': '2fdtCHuvyLcD2q86XZGFmYbDux9ZbbUgMmFhzChqmoon',
                    'decimals': 9,
                    'name': 'Test Token',
                    'symbol': 'TEST'
                }
                mock_token_info_class.return_value = mock_token_info_instance
                
                token_address = "2fdtCHuvyLcD2q86XZGFmYbDux9ZbbUgMmFhzChqmoon"
                
                # 第一次调用 - 应该查询TokenInfo
                decimals1 = await self.service._get_token_decimals(token_address)
                self.assertEqual(decimals1, 9)
                mock_token_info_class.assert_called_once_with(token_address, chain="sol")
                mock_token_info_instance.get_token_info.assert_called_once()
                
                # 第二次调用 - 应该使用缓存
                decimals2 = await self.service._get_token_decimals(token_address)
                self.assertEqual(decimals2, 9)
                # TokenInfo不应该再次被调用
                mock_token_info_class.assert_called_once()  # 仍然只调用一次
                mock_token_info_instance.get_token_info.assert_called_once()  # 仍然只调用一次
                
                # 验证缓存确实存在
                self.assertTrue(hasattr(self.service, '_token_decimals_cache'))
                self.assertIn(token_address, self.service._token_decimals_cache)
                self.assertEqual(self.service._token_decimals_cache[token_address], 9)
                
                # 测试SOL地址不使用缓存机制（直接返回9）
                sol_decimals = await self.service._get_token_decimals("So11111111111111111111111111111111111111112")
                self.assertEqual(sol_decimals, 9)
                # SOL不应该调用TokenInfo
                mock_token_info_class.assert_called_once()  # 仍然只调用一次（之前的调用）
        
        asyncio.run(async_test())

    @patch('utils.spiders.solana.token_info.TokenInfo')
    @patch('utils.trading.solana.jupiter_trade_service.JupiterTradeService._get_jupiter_quote')
    @patch('utils.trading.solana.jupiter_trade_service.JupiterTradeService._get_jupiter_swap_transaction')
    @patch('utils.trading.solana.jupiter_trade_service.JupiterTradeService._sign_and_send_transaction')
    @patch('utils.trading.solana.jupiter_trade_service.JupiterTradeService._wait_for_transaction_confirmation')
    @patch('base58.b58decode')
    @patch('utils.trading.solana.jupiter_trade_service.Keypair.from_bytes')
    def test_production_scenario_bug_fix_verification(self,
                                                    mock_keypair,
                                                    mock_b58decode,
                                                    mock_wait_confirmation,
                                                    mock_sign_send,
                                                    mock_get_swap,
                                                    mock_get_quote,
                                                    mock_token_info_class):
        """测试生产场景Bug修复验证 - 真正的端到端测试
        
        这个测试验证在没有strategy_snapshot中input_token_decimals配置的情况下，
        系统能否通过_get_token_decimals正确获取代币精度并在完整交易流程中使用。
        """
        async def async_test():
            # 模拟TokenInfo实例和其get_token_info方法
            mock_token_info_instance = AsyncMock()
            mock_token_info_instance.get_token_info.return_value = {
                'address': '2fdtCHuvyLcD2q86XZGFmYbDux9ZbbUgMmFhzChqmoon',
                'decimals': 9,  # 正确的精度
                'name': 'Test Token',
                'symbol': 'TEST'
            }
            mock_token_info_class.return_value = mock_token_info_instance
            
            # 模拟其他依赖的返回值
            mock_b58decode.return_value = b'mock_key_bytes'
            mock_keypair.return_value = Mock()
            
            # 这里是关键：模拟Jupiter API接收到正确的数量
            # 如果修复成功，应该传入624,290,605,456（9位精度）
            # 如果修复失败，会传入624,290,605（6位精度）
            mock_get_quote.return_value = {
                "inAmount": "624290605456",  # 期望的正确数量（9位精度）
                "outAmount": "1000000000",   # 模拟输出数量
                "inputMint": "2fdtCHuvyLcD2q86XZGFmYbDux9ZbbUgMmFhzChqmoon",
                "outputMint": "So11111111111111111111111111111111111111112"
            }
            
            mock_get_swap.return_value = "base64_transaction"
            mock_sign_send.return_value = "transaction_hash_123"
            mock_wait_confirmation.return_value = True
            
            # 生产环境中的实际参数
            input_token_address = "2fdtCHuvyLcD2q86XZGFmYbDux9ZbbUgMmFhzChqmoon"
            amount_input_token = 624.290605456
            
            # 关键：strategy_snapshot中没有input_token_decimals配置
            # 这模拟了生产环境中的Bug场景
            strategy_snapshot_without_decimals = {
                'max_slippage_bps': 150,
                'priority_fee_lamports': 75000,
                # 故意不包含input_token_decimals
            }
            
            trade_record_id = PydanticObjectId()
            signal_id = PydanticObjectId()
            
            # 执行完整的交易流程
            result = await self.service.execute_trade(
                trade_type=TradeType.SELL,  # 卖出代币
                input_token_address=input_token_address,
                output_token_address="So11111111111111111111111111111111111111112",  # SOL
                amount_input_token=amount_input_token,
                wallet_private_key_b58="valid_base58_key",
                wallet_address="valid_address",
                strategy_snapshot=strategy_snapshot_without_decimals,  # 没有精度配置
                signal_id=signal_id,
                trade_record_id=trade_record_id
            )
            
            # 验证交易成功
            self.assertEqual(result.status, TradeStatus.SUCCESS)
            self.assertEqual(result.tx_hash, "transaction_hash_123")
            
            # 关键验证：检查_get_jupiter_quote被调用时使用的数量
            mock_get_quote.assert_called_once()
            call_args = mock_get_quote.call_args
            actual_amount = call_args[1]['amount']  # 获取传入的amount参数
            
            # 验证使用了正确的精度计算的数量
            expected_correct_amount = int(624.290605456 * (10 ** 9))  # 624,290,605,456
            expected_wrong_amount = int(624.290605456 * (10 ** 6))    # 624,290,605
            
            self.assertEqual(actual_amount, expected_correct_amount,
                           f"应该使用正确的9位精度计算数量。期望: {expected_correct_amount}, 实际: {actual_amount}")
            
            # 验证确实调用了TokenInfo获取精度
            mock_token_info_class.assert_called_once_with(input_token_address, chain="sol")
            mock_token_info_instance.get_token_info.assert_called_once()
            
            # 验证没有使用错误的数量
            self.assertNotEqual(actual_amount, expected_wrong_amount,
                              f"不应该使用错误的6位精度计算数量: {expected_wrong_amount}")
            
            # 记录验证结果
            print(f"生产场景Bug修复验证:")
            print(f"  代币地址: {input_token_address}")
            print(f"  输入数量: {amount_input_token}")
            print(f"  期望正确数量: {expected_correct_amount} native units (9位精度)")
            print(f"  实际使用数量: {actual_amount} native units")
            print(f"  错误数量: {expected_wrong_amount} native units (6位精度)")
            print(f"  修复倍数: {actual_amount / expected_wrong_amount}")
            print(f"  ✅ Bug已修复！系统正确获取并使用了代币精度")
        
        asyncio.run(async_test())


if __name__ == "__main__":
    unittest.main()