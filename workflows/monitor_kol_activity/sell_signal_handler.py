import logging
import time
import traceback
from typing import Dict, List, Optional, AsyncGenerator, <PERSON><PERSON>
from datetime import datetime, timedelta
from beanie.odm.operators.update.general import Set
from beanie import PydanticObjectId
from zoneinfo import ZoneInfo
from decimal import Decimal

from dao.config_dao import Config<PERSON><PERSON>
from dao.kol_wallet_activity_dao import KOLWalletActivityDAO
from dao.token_message_send_history_dao import TokenMessageSendHistoryDAO
from dao.telegram_users_dao import TelegramUserDAO
from dao.signal_dao import SignalDAO
from models.config import SingleKolStrategyConfig, KolActivityConfig, ApplicationConfig
from models.token_message_send_history import TokenMessageSendHistory
from models.signal import Signal
from utils.common import check_kol_sell_ratio
from utils.basic_utils import get_current_time_dt
from utils.message_sender.message_sender import MessageSender, TelegramMessageSender
from jinja2 import Template

import os
from models.trade_record import TradeRecord, TradeStatus as ModelTradeStatus, TradeType as ModelTradeType
from dao.trade_record_dao import TradeRecordDAO
from utils.trading.auto_trade_manager import AutoTradeManager
from models.trade_execution import TradeExecutionResult, TradeStatus as InterfaceTradeStatus
from utils.trading.solana.trade_interface import TradeType as InterfaceTradeType
from utils.trading.trade_record_manager import TradeRecordManager
from utils.spiders.solana.token_info import TokenInfo
from utils.spiders.solana.solana_monitor import SolanaMonitor

SOL_MINT_ADDRESS = "So11111111111111111111111111111111111111112"

logger = logging.getLogger("SellSignalHandler")

async def generate_sell_signals() -> AsyncGenerator[Dict, None]:
    """
    生成卖出信号数据。
    查询状态为 'open' 的买入类型 'Signal' 记录，检查卖出条件（超时或KOL卖出比例）。
    """
    try:
        config_dao = ConfigDAO()
        config_doc = await config_dao.get_config("kol_activity")
        
        # Handle different config types and backward compatibility
        if not config_doc:
            logger.error("无法加载 kol_activity 配置")
            return
            
        if isinstance(config_doc.data, KolActivityConfig):
            # The new multi-strategy config model - all strategy defaults would be in the first active strategy
            active_strategies = [s for s in config_doc.data.buy_strategies if s.is_active]
            if not active_strategies:
                logger.error("kol_activity 配置中没有激活的策略")
                return
            # Take the first active strategy as a default global config (this doesn't actually matter 
            # since we'll use per-signal trigger_conditions, but provides sensible fallbacks)
            global_config = active_strategies[0]
        elif hasattr(config_doc.data, 'sell_strategy_hours'):
            # Legacy config model treated as a SingleKolStrategyConfig
            global_config = config_doc.data
        else:
            logger.error("kol_activity 配置数据类型不正确")
            return

        signal_dao = SignalDAO()
        kol_activity_dao = KOLWalletActivityDAO()
        current_time_dt = get_current_time_dt()
        current_time_ts = int(current_time_dt.timestamp())

        # 查找开放的买入信号 (从 Signal 集合)
        open_buy_signals: List[Signal] = await signal_dao.find_signals({
            "signal_type": "kol_buy", # Assuming buy signals have this type
            "status": "open"
        })

        logger.info(f"找到 {len(open_buy_signals)} 个开放的买入信号 (Signal) 需要检查卖出条件")

        for buy_signal in open_buy_signals:
            sell_reason = None
            sell_time = current_time_dt
            sell_trigger_conditions = {} # Store specific sell trigger info

            # Extract strategy parameters from the original buy_signal's trigger_conditions.
            # This reflects the configuration snapshot when the buy signal was generated.
            # Fallback to current global config if a key is missing (e.g., older signals or config structure changes).
            buy_signal_config_snapshot = buy_signal.trigger_conditions or {}

            effective_sell_strategy_hours = buy_signal_config_snapshot.get('sell_strategy_hours', global_config.sell_strategy_hours)
            effective_sell_kol_ratio = buy_signal_config_snapshot.get('sell_kol_ratio', global_config.sell_kol_ratio)
            # This lookback is for KOL selling activity relative to the buy signal event
            effective_transaction_lookback_hours = buy_signal_config_snapshot.get('transaction_lookback_hours', global_config.transaction_lookback_hours)

            # 1. 超时检查
            # Use buy_signal.trigger_timestamp instead of created_at if applicable
            # Assuming trigger_timestamp marks the signal generation time
            signal_creation_time = buy_signal.trigger_timestamp or buy_signal.created_at

            # Ensure signal_creation_time is offset-aware (Asia/Shanghai)
            eastern_zone = ZoneInfo("Asia/Shanghai")
            utc_zone = ZoneInfo("UTC")

            if signal_creation_time.tzinfo is None or signal_creation_time.tzinfo.utcoffset(signal_creation_time) is None:
                # If naive, assume it's UTC and convert to Eastern time
                logger.warning(f"Signal {buy_signal.id} has a naive datetime for signal_creation_time: {signal_creation_time}. Assuming UTC and converting to Asia/Shanghai.")
                signal_creation_time = signal_creation_time.replace(tzinfo=utc_zone).astimezone(eastern_zone)
            elif signal_creation_time.tzinfo != eastern_zone:
                # If aware but not Eastern, convert to Eastern
                logger.warning(f"Signal {buy_signal.id} has a non-Eastern timezone for signal_creation_time: {signal_creation_time.tzinfo}. Converting to Asia/Shanghai.")
                signal_creation_time = signal_creation_time.astimezone(eastern_zone)
            
            timeout_threshold = signal_creation_time + timedelta(hours=effective_sell_strategy_hours)
            if current_time_dt >= timeout_threshold:
                sell_reason = "timeout"
                sell_trigger_conditions['type'] = 'timeout'
                sell_trigger_conditions['hours_threshold_used'] = effective_sell_strategy_hours
                logger.info(f"代币 {buy_signal.token_address} (买入信号ID: {buy_signal.id}) 触发超时卖出 (使用规则: {effective_sell_strategy_hours}h)")
            else:
                # 2. KOL卖出比例检查
                if buy_signal.hit_kol_wallets and len(buy_signal.hit_kol_wallets) > 0:
                    # signal_creation_time is already timezone-aware (Asia/Shanghai) here
                    # current_time_dt is also timezone-aware (Asia/Shanghai)

                    is_ratio_threshold_reached, calculated_sell_ratio, selling_kol_count_for_ratio = await check_kol_sell_ratio(
                        kol_activity_dao=kol_activity_dao,
                        token_address=buy_signal.token_address,
                        hit_kol_wallets=buy_signal.hit_kol_wallets,
                        buy_signal_time_dt=signal_creation_time, 
                        evaluation_time_dt=current_time_dt,    
                        sell_ratio_threshold=effective_sell_kol_ratio,
                        sell_activity_lookback_hours=effective_transaction_lookback_hours, 
                        logger=logger
                    )

                    if is_ratio_threshold_reached:
                        sell_reason = "ratio"
                        sell_trigger_conditions['type'] = 'kol_sell_ratio'
                        sell_trigger_conditions['ratio_threshold_used'] = effective_sell_kol_ratio
                        sell_trigger_conditions['actual_ratio_calculated'] = calculated_sell_ratio
                        sell_trigger_conditions['selling_kol_count'] = selling_kol_count_for_ratio
                        sell_trigger_conditions['total_hit_kols_on_buy'] = len(buy_signal.hit_kol_wallets)
                        sell_trigger_conditions['kol_activity_lookback_hours_used'] = effective_transaction_lookback_hours
                        logger.info(f"代币 {buy_signal.token_address} (买入信号ID: {buy_signal.id}) 触发KOL卖出比例卖出 (使用规则: {effective_sell_kol_ratio}, 计算得到: {calculated_sell_ratio:.2f})")
                    else:
                        logger.info(f"代币 {buy_signal.token_address} (买入信号ID: {buy_signal.id}) 卖出比例「{calculated_sell_ratio:.2f}」未达到阈值「{effective_sell_kol_ratio}」, 跳过 (详情见 common.check_kol_sell_ratio 日志)")
                else:
                    logger.info(f"代币 {buy_signal.token_address} (买入信号ID: {buy_signal.id}) 买入信号无关联KOL钱包或KOL列表为空, 无法检查KOL卖出比例, 跳过")
                        
            # If a sell condition is triggered
            if sell_reason:
                buy_id_for_sell_signal = buy_signal.id
                logger.info(f"代币 {buy_signal.token_address} 的买入信号ID: {buy_id_for_sell_signal}, type: {type(buy_id_for_sell_signal)}")
                yield {
                    "token_address": buy_signal.token_address,
                    "token_name": buy_signal.token_name if buy_signal.token_name else "N/A",
                    "token_symbol": buy_signal.token_symbol if buy_signal.token_symbol else "N/A",
                    "buy_signal_ref_id": str(buy_id_for_sell_signal), # Use the validated ID
                    "hit_kol_wallets": buy_signal.hit_kol_wallets, # Pass KOLs for context
                    "sell_reason": sell_reason,
                    "sell_trigger_conditions": sell_trigger_conditions, # Add specific conditions
                    "sell_time": sell_time
                }
    except Exception as e:
        logger.error(f"生成卖出信号时出错: {e}", exc_info=True)


async def process_sell_signal(data: List[Dict]):
    """
    处理生成的卖出信号：
    1. 创建卖出 Signal 记录。
    2. 更新对应买入 Signal 的状态为 'sold'。
    3. 发送 Telegram 通知。
    4. 创建 TokenMessageSendHistory 记录关联到卖出 Signal。
    """
    if not data:
        return True

    message_sender: MessageSender = TelegramMessageSender()
    signal_dao = SignalDAO()
    history_dao = TokenMessageSendHistoryDAO()
    user_dao = TelegramUserDAO()
    trade_record_dao = TradeRecordDAO()
    config_dao = ConfigDAO()

    # 初始化 AutoTradeManager
    auto_trade_manager = AutoTradeManager()

    # 新的消息模板
    sell_message_template_base = """🚨 Sell Signal Alert:
Token Name: {{token_name}}
Token Symbol: {{token_symbol}}
Mint Address: {{token_address}}
Reason: {{sell_reason}}
Strategy: {{strategy_name}}
"""
    sell_trade_success_template = sell_message_template_base + """Trade Status: ✅ SUCCESS (Sell)
Tx Hash: <a href="https://solscan.io/tx/{{trade_result.tx_hash}}">{{trade_result.tx_hash_short}}</a>
Amount Sold: {{trade_result.actual_amount_in_ui}} {{token_symbol}}
Amount Received: {{trade_result.actual_amount_out_ui}} SOL
GMGN Bot: <a href="https://t.me/GMGN_sol_bot?start=i_e4bi1uZO_c_{{token_address}}">Trade on GMGN</a>"""
    
    sell_trade_failed_template = sell_message_template_base + """Trade Status: ❌ FAILED (Sell)
Error: {{trade_result.error_message}}
GMGN Bot: <a href="https://t.me/GMGN_sol_bot?start=i_e4bi1uZO_c_{{token_address}}">Try Manual Trade on GMGN</a>"""

    sell_trade_skipped_template = sell_message_template_base + """Trade Status: ⚠️ SKIPPED (Auto-sell disabled or no valid buy to sell)
GMGN Bot: <a href="https://t.me/GMGN_sol_bot?start=i_e4bi1uZO_c_{{token_address}}">Trade on GMGN</a>"""

    sell_no_trade_info_template = sell_message_template_base + """GMGN Bot: <a href="https://t.me/GMGN_sol_bot?start=i_e4bi1uZO_c_{{token_address}}">Trade on GMGN</a>"""

    for signal_data in data:
        sell_signal_created_for_this_buy_ref = False
        trade_execution_result: Optional[TradeExecutionResult] = None
        new_sell_signal_instance: Optional[Signal] = None
        
        try:
            buy_signal_ref_id: PydanticObjectId = PydanticObjectId(signal_data["buy_signal_ref_id"])
            token_address = signal_data["token_address"]
            token_name = signal_data.get("token_name", token_address)
            token_symbol = signal_data.get("token_symbol", token_address[:5].upper())
            hit_kol_wallets = signal_data.get("hit_kol_wallets")
            sell_reason = signal_data.get("sell_reason", "unknown")
            sell_time = signal_data.get("sell_time", get_current_time_dt())

            buy_signal: Signal = await signal_dao.get_signal(buy_signal_ref_id)

            if not buy_signal:
                logger.warning(f"尝试处理卖出信号时，买入信号 {buy_signal_ref_id} 未找到，跳过")
                continue
            if buy_signal.status != 'open':
                logger.warning(f"尝试处理卖出信号时，买入信号 {buy_signal_ref_id} 状态已不是 'open' ({buy_signal.status})，跳过")
                continue

            # 2. 检查是否已存在对应的卖出信号
            existing_sell_signal = await signal_dao.find_one({
                "buy_signal_ref_id": buy_signal_ref_id,
                "signal_type": "kol_sell"
            })
            if existing_sell_signal:
                logger.warning(f"买入信号 {buy_signal_ref_id} 已存在对应的卖出信号 {existing_sell_signal.id}，跳过创建和通知")
                # 确保买入信号状态更新（幂等性）
                if buy_signal.status == 'open':
                    update_count = await signal_dao.update_signal_status(buy_signal.id, "sold")
                    logger.info(f"为已存在的卖出信号 {existing_sell_signal.id} 再次尝试更新买入信号 {buy_signal.id} 状态，结果: {update_count}")
                continue

            # 3. 创建卖出 Signal 记录
            sell_signal = Signal(
                token_address=token_address,
                token_name=signal_data.get("token_name"),
                token_symbol=signal_data.get("token_symbol"),
                signal_type="kol_sell", 
                status="closed", 
                buy_signal_ref_id=buy_signal_ref_id,
                hit_kol_wallets=signal_data.get("hit_kol_wallets"),
                trigger_timestamp=sell_time,
                trigger_conditions={ 
                    "reason": sell_reason,
                    "original_buy_strategy_name": buy_signal.trigger_conditions.get("strategy_name", "unknown"),
                    **signal_data.get("sell_trigger_conditions", {})
                }
            )
            insert_result = await signal_dao.insert_signals([sell_signal]) 
            
            if not insert_result or not hasattr(insert_result, 'inserted_ids') or not insert_result.inserted_ids:
                 logger.error(f"保存卖出信号记录失败: {signal_data}")
                 continue 
            
            new_sell_signal_id = insert_result.inserted_ids[0]
            logger.info(f"成功保存卖出信号记录: ID {new_sell_signal_id} for buy signal {buy_signal_ref_id}")

            # --- 开始自动卖出交易逻辑（使用 AutoTradeManager）---
            trade_execution_result: Optional[TradeExecutionResult] = None
            
            # 卖出逻辑依赖于买入时的策略快照
            buy_strategy_snapshot = buy_signal.trigger_conditions or {}
            strategy_name_for_sell = buy_strategy_snapshot.get('strategy_name', 'unknown')

            # 检查是否启用自动卖出 - 使用新的AutoTradeManager配置
            try:
                auto_trade_manager_config = await auto_trade_manager.config_manager.get_config()
                auto_sell_enabled = auto_trade_manager_config.enabled
                logger.info(f"[SellSignal:{new_sell_signal_id}] AutoTradeManager global config: enabled={auto_sell_enabled}")
            except Exception as e:
                logger.error(f"[SellSignal:{new_sell_signal_id}] Failed to get AutoTradeManager config: {e}")
                auto_sell_enabled = False

            if auto_sell_enabled:
                logger.info(f"[SellSignal:{new_sell_signal_id}] Strategy '{strategy_name_for_sell}' enabled auto-sell for token {token_address}")
                
                # 找到对应的成功买入的TradeRecord以确定钱包地址和代币信息
                original_buy_trade_record: Optional[TradeRecord] = None
                wallet_address_for_balance_check = None
                
                if buy_signal.trade_record_ids:
                    for rec_id in buy_signal.trade_record_ids:
                        record = await trade_record_dao.get_by_id(rec_id)
                        if record and record.trade_type == ModelTradeType.BUY and record.status == ModelTradeStatus.SUCCESS:
                            original_buy_trade_record = record
                            wallet_address_for_balance_check = record.wallet_address
                            logger.info(f"[SellSignal:{new_sell_signal_id}] Found candidate BUY TradeRecord {record.id} for token {record.token_out_address}, wallet: {wallet_address_for_balance_check}")
                            break

                if not original_buy_trade_record:
                    logger.warning(f"[SellSignal:{new_sell_signal_id}] Auto-sell enabled, but no successful prior BUY TradeRecord found for Signal {buy_signal_ref_id}. Skipping sell trade.")
                elif not wallet_address_for_balance_check:
                    logger.warning(f"[SellSignal:{new_sell_signal_id}] Auto-sell enabled, but the BUY TradeRecord {original_buy_trade_record.id} for token {token_address} has no wallet_address. Skipping sell trade.")
                else:
                    # 步骤1: 查询钱包当前实际代币余额
                    try:
                        logger.info(f"[SellSignal:{new_sell_signal_id}] 正在查询钱包 {wallet_address_for_balance_check} 的代币 {token_address} 当前余额...")
                        
                        # 初始化 SolanaMonitor
                        solana_monitor = SolanaMonitor()
                        
                        # 查询当前余额
                        current_token_balance = await solana_monitor.get_token_balance(
                            owner_address=wallet_address_for_balance_check,
                            token_mint=token_address
                        )
                        
                        logger.info(f"[SellSignal:{new_sell_signal_id}] 钱包当前代币余额: {current_token_balance}")
                        
                        # 检查余额是否足够进行卖出
                        if current_token_balance <= 0:
                            logger.warning(f"[SellSignal:{new_sell_signal_id}] 钱包 {wallet_address_for_balance_check} 的代币 {token_address} 余额为 {current_token_balance}，无法执行卖出交易")
                            
                            # 发送余额不足通知
                            await send_sell_failure_notification(
                                sell_signal=sell_signal,
                                buy_signal=buy_signal,
                                error_message=f"钱包代币余额不足: {current_token_balance}",
                                failure_reason="INSUFFICIENT_BALANCE"
                            )
                            continue  # 跳过这个卖出信号
                        
                        # 步骤2: 可选的链上确认步骤（如果买入交易哈希可用）
                        confirmed_amount_from_tx: Optional[Decimal] = None
                        if original_buy_trade_record.tx_hash:
                            try:
                                logger.info(f"[SellSignal:{new_sell_signal_id}] 尝试从买入交易 {original_buy_trade_record.tx_hash} 中获取确认的代币输出数量...")
                                confirmed_amount_from_tx = await solana_monitor.get_confirmed_token_output_from_tx(
                                    tx_hash=original_buy_trade_record.tx_hash,
                                    expected_output_token_mint=token_address,
                                    wallet_address=wallet_address_for_balance_check
                                )
                                
                                if confirmed_amount_from_tx is not None:
                                    logger.info(f"[SellSignal:{new_sell_signal_id}] 从买入交易中确认的代币输出数量: {confirmed_amount_from_tx}")
                                    
                                    # 可选：更新 TradeRecord 的 token_out_actual_amount 字段
                                    if original_buy_trade_record.token_out_actual_amount != confirmed_amount_from_tx:
                                        logger.info(f"[SellSignal:{new_sell_signal_id}] 更新 TradeRecord {original_buy_trade_record.id} 的 token_out_actual_amount 从 {original_buy_trade_record.token_out_actual_amount} 到 {confirmed_amount_from_tx}")
                                        # 这里可以添加更新逻辑，但为了保持修复的最小化，暂时跳过
                                else:
                                    logger.warning(f"[SellSignal:{new_sell_signal_id}] 无法从买入交易 {original_buy_trade_record.tx_hash} 中获取确认的代币输出数量")
                            except Exception as e:
                                logger.error(f"[SellSignal:{new_sell_signal_id}] 从买入交易获取确认数量时发生异常: {str(e)}")
                        
                        # 步骤3: 确定最终卖出数量
                        # 优先使用当前余额，但可以考虑确认数量作为参考
                        ui_amount_to_sell = current_token_balance
                        
                        # 可选：如果确认数量可用且小于当前余额，使用确认数量（更保守）
                        if confirmed_amount_from_tx is not None and confirmed_amount_from_tx < current_token_balance:
                            logger.info(f"[SellSignal:{new_sell_signal_id}] 使用从交易确认的数量 {confirmed_amount_from_tx} 而不是当前余额 {current_token_balance}（更保守的选择）")
                            ui_amount_to_sell = confirmed_amount_from_tx
                        
                        logger.info(f"[SellSignal:{new_sell_signal_id}] 最终确定卖出数量 (UI amount): {ui_amount_to_sell}")

                        # 获取/确认代币的decimals (这是关键部分)
                        known_decimals: Optional[int] = None
                        if original_buy_trade_record and original_buy_trade_record.token_out_decimals is not None:
                            known_decimals = original_buy_trade_record.token_out_decimals
                            logger.info(f"[SellSignal:{new_sell_signal_id}] 从买入交易记录 {original_buy_trade_record.id} 中获取到decimals: {known_decimals}")
                        # logger.warning(f"DEBUG_DECIMALS_PATH: [SellSignal:{new_sell_signal_id}] buy_strategy_snapshot.get('input_token_decimals') is: {buy_strategy_snapshot.get('input_token_decimals')}") # 暂时注释掉这个，因为它在错误的位置
                        elif buy_strategy_snapshot.get("input_token_decimals") is not None: # 确保这是 elif
                            known_decimals = buy_strategy_snapshot.get("input_token_decimals")
                            logger.info(f"[SellSignal:{new_sell_signal_id}] 从买入信号策略快照中获取到 input_token_decimals: {known_decimals} (用于确定卖出币种的decimals)")
                        logger.info(f"[SellSignal:{new_sell_signal_id}] 最终确定卖出数量: {ui_amount_to_sell}")
                        
                    except Exception as e:
                        logger.error(f"[SellSignal:{new_sell_signal_id}] 查询钱包余额时发生异常: {str(e)}", exc_info=True)
                        
                        # 发送余额查询失败通知
                        await send_sell_failure_notification(
                            sell_signal=sell_signal,
                            buy_signal=buy_signal,
                            error_message=f"查询钱包余额失败: {str(e)}",
                            failure_reason="BALANCE_QUERY_ERROR"
                        )
                        continue  # 跳过这个卖出信号

                    # 如果无法从交易记录或买入信号中获取decimals，则尝试通过API获取
                    if known_decimals is None: 
                        token_info_api = TokenInfo(address=token_address) 
                        try:
                            token_api_data = await token_info_api.get_token_info()
                            if token_api_data and 'decimals' in token_api_data:
                                known_decimals = token_api_data['decimals']
                                logger.info(f"[SellSignal:{new_sell_signal_id}] 从TokenInfo API获取到decimals: {known_decimals}")
                            else:
                                logger.warning(f"[SellSignal:{new_sell_signal_id}] 无法从TokenInfo API获取到decimals")
                                await send_sell_failure_notification(
                                    sell_signal=sell_signal,
                                    buy_signal=buy_signal,
                                    error_message="无法从TokenInfo API获取到decimals",
                                    failure_reason="TOKEN_INFO_API_ERROR"
                                )
                                continue 
                        except Exception as e_token_info:
                            logger.error(f"[SellSignal:{new_sell_signal_id}] 调用TokenInfo API获取decimals时发生异常 for {token_address}: {e_token_info}")
                            await send_sell_failure_notification(
                                sell_signal=sell_signal,
                                buy_signal=buy_signal,
                                error_message=f"调用TokenInfo API获取decimals时发生异常: {e_token_info}",
                                failure_reason="TOKEN_INFO_API_ERROR"
                            )
                            continue 

                    # 如果到这里 known_decimals 仍然是 None (例如API调用失败且没有从其他地方获取到)
                    if known_decimals is None:
                        logger.error(f"[SellSignal:{new_sell_signal_id}] 无法确定代币 {token_address} 的 decimals，无法进行卖出。")
                        await send_sell_failure_notification(
                            sell_signal=sell_signal,
                            buy_signal=buy_signal,
                            error_message="无法确定代币的decimals",
                            failure_reason="UNKNOWN_DECIMALS"
                        )
                        continue 

                    # 使用 AutoTradeManager 执行卖出交易
                    try:
                        # 准备策略级别的钱包覆盖（如果有的话）
                        # 新架构中检查策略级别的钱包覆盖
                        wallet_private_key_env_var = buy_strategy_snapshot.get('wallet_private_key_env_var') or buy_strategy_snapshot.get('gmgn_private_key_env_var')
                        wallet_address = buy_strategy_snapshot.get('wallet_address') or buy_strategy_snapshot.get('gmgn_sol_wallet_address')

                        # 准备策略级别的交易参数覆盖
                        strategy_trading_overrides = {}
                        # 检查新字段名，如果没有则尝试旧字段名（向后兼容）
                        sell_slippage = buy_strategy_snapshot.get('sell_slippage_percentage') or buy_strategy_snapshot.get('gmgn_sell_slippage_percentage')
                        if sell_slippage:
                            strategy_trading_overrides['sell_slippage_percentage'] = sell_slippage
                        
                        sell_priority_fee = buy_strategy_snapshot.get('sell_priority_fee_sol') or buy_strategy_snapshot.get('gmgn_sell_priority_fee')
                        if sell_priority_fee:
                            strategy_trading_overrides['sell_priority_fee_sol'] = sell_priority_fee
                        
                        logger.info(f"[SellSignal:{new_sell_signal_id}] Strategy overrides: wallet_private_key_env_var={wallet_private_key_env_var is not None}, wallet_address={wallet_address is not None}, trading_overrides={strategy_trading_overrides}")

                        # ui_amount_to_sell 已经在上面的余额查询步骤中确定，无需再次转换
                        logger.info(f"[SellSignal:{new_sell_signal_id}] 准备执行卖出交易，数量: {ui_amount_to_sell}")

                        # 使用 AutoTradeManager 执行卖出
                        trade_execution_result = await auto_trade_manager.execute_trade(
                            trade_type=InterfaceTradeType.SELL,
                            token_in_address=token_address,  # 卖出的代币
                            token_out_address=SOL_MINT_ADDRESS,  # 期望得到SOL
                            amount=ui_amount_to_sell,
                            wallet_private_key_env_var=wallet_private_key_env_var,
                            wallet_address=wallet_address,
                            strategy_trading_overrides=strategy_trading_overrides,
                            signal_id=new_sell_signal_id,
                            strategy_name=strategy_name_for_sell
                        )
                        
                        logger.info(f"[SellSignal:{new_sell_signal_id}] AutoTradeManager SELL execution finished. Status: {trade_execution_result.final_status}")

                    except Exception as e:
                        logger.error(f"[SellSignal:{new_sell_signal_id}] AutoTradeManager SELL execution EXCEPTION: {e}", exc_info=True)
                        # 创建一个失败的结果
                        trade_execution_result = TradeExecutionResult(
                            final_status=InterfaceTradeStatus.FAILED,
                            successful_channel=None,
                            final_trade_record_id=None,
                            channel_attempts=[],
                            total_execution_time=0.0,
                            error_summary=f"AutoTradeManager execution exception: {str(e)}",
                            started_at=get_current_time_dt(),
                            completed_at=get_current_time_dt()
                        )
            else:
                logger.info(f"[SellSignal:{new_sell_signal_id}] Strategy '{strategy_name_for_sell}' for token {token_address} has auto-sell disabled. Skipping trade.")

            # --- 结束自动卖出交易逻辑 ---

            # 4. 更新买入信号状态为 'sold'
            update_count = await signal_dao.update_signal_status(buy_signal.id, "sold")
            if update_count == 1:
                logger.info(f"成功更新买入信号 {buy_signal.id} 状态为 'sold'")
            else:
                logger.warning(f"更新买入信号 {buy_signal.id} 状态失败或已被更新, modified_count: {update_count}")

            # 根据交易结果选择模板和构建消息
            message = ""
            template_render_data = {
                'token_name': token_name,
                'token_symbol': token_symbol,
                'token_address': token_address,
                'sell_reason': sell_reason,
                'strategy_name': strategy_name_for_sell,
                'buy_signal_ref_id': str(buy_signal_ref_id),
                'trade_result': None
            }

            if trade_execution_result:
                # 构建交易结果数据用于模板渲染
                template_render_data['trade_result'] = {
                    'tx_hash': None,
                    'tx_hash_short': "N/A",
                    'error_message': trade_execution_result.error_summary or "Unknown error",
                    'actual_amount_in_ui': "N/A",
                    'actual_amount_out_ui': "N/A",
                }

                # 如果有成功的交易记录，获取详细信息
                if trade_execution_result.final_trade_record_id:
                    try:
                        final_trade_record = await trade_record_dao.get_by_id(trade_execution_result.final_trade_record_id)
                        if final_trade_record and final_trade_record.tx_hash:
                            template_render_data['trade_result']['tx_hash'] = final_trade_record.tx_hash
                            template_render_data['trade_result']['tx_hash_short'] = final_trade_record.tx_hash[:4] + "..." + final_trade_record.tx_hash[-4:]
                            
                            # 格式化实际交易数量
                            if final_trade_record.token_in_actual_amount:
                                template_render_data['trade_result']['actual_amount_in_ui'] = f"{final_trade_record.token_in_actual_amount:.6f}"
                            if final_trade_record.token_out_actual_amount:
                                # SOL数量（9位小数）
                                sol_amount = float(Decimal(str(final_trade_record.token_out_actual_amount)) / (Decimal('10') ** 9))
                                template_render_data['trade_result']['actual_amount_out_ui'] = f"{sol_amount:.9f}"
                    except Exception as e:
                        logger.error(f"[SellSignal:{new_sell_signal_id}] Error fetching trade record details: {e}")

                # 选择模板
                if trade_execution_result.final_status == InterfaceTradeStatus.SUCCESS:
                    template_to_use = Template(sell_trade_success_template)
                elif trade_execution_result.final_status == InterfaceTradeStatus.FAILED:
                    template_to_use = Template(sell_trade_failed_template)
                else:  # SKIPPED or other status
                    template_to_use = Template(sell_trade_skipped_template)
            else:
                template_to_use = Template(sell_no_trade_info_template)
            
            try:
                message = template_to_use.render(template_render_data)
            except Exception as e_render:
                logger.error(f"[SellSignal:{new_sell_signal_id}] Failed to render sell Telegram message: {e_render}", exc_info=True)
                message = f"🚨 Sell Alert for {token_symbol or token_address}! Reason: {sell_reason}. Strategy: {strategy_name_for_sell}. Check logs for trade details."

            # 5. 发送通知给所有用户
            telegram_users = await user_dao.get_all_users()
            logger.info(f"准备向 {len(telegram_users)} 个用户发送卖出信号通知 for {token_address} (Sell Signal ID: {new_sell_signal_id})")
            
            histories_to_save = []
            current_send_time = get_current_time_dt()

            for user in telegram_users:
                 chat_id = user['chat_id']
                 send_result = await message_sender.send_message_to_user(user_id=chat_id, message=message, parse_mode="HTML")
                 
                 status = "sent" if send_result else "failed"
                 if send_result:
                     logger.info(f"成功发送卖出信号给用户 {chat_id} for token {token_address}")
                 else:
                     logger.error(f"发送卖出信号给用户 {chat_id} 失败 for token {token_address}")

                 # 准备历史记录
                 history_record = {
                     "token_address": token_address,
                     "chat_id": chat_id,
                     "channel": "telegram",
                     "signal_id": new_sell_signal_id,
                     "status": status,
                     "created_at": current_send_time 
                 }
                 histories_to_save.append(history_record)

            # 6. 批量保存发送历史
            if histories_to_save:
                 try:
                    insert_hist_result = await history_dao.insert_many(histories_to_save)
                    if insert_hist_result and len(insert_hist_result.inserted_ids) == len(histories_to_save):
                         logger.info(f"成功批量保存 {len(histories_to_save)} 条卖出信号发送历史 for sell signal {new_sell_signal_id}")
                    else:
                         logger.error(f"批量保存卖出信号发送历史失败或数量不匹配 for sell signal {new_sell_signal_id}")
                 except Exception as e:
                     logger.error(f"批量保存卖出信号发送历史时出错 for sell signal {new_sell_signal_id}: {e}", exc_info=True)

        except Exception as e:
            logger.error(f"处理卖出信号时发生意外错误: {signal_data}, 错误: {e}\n{traceback.format_exc()}", exc_info=True)

    return True


async def send_sell_failure_notification(sell_signal, buy_signal, error_message: str, failure_reason: str):
    """发送卖出交易失败的Telegram通知"""
    try:
        # 构建失败通知消息
        failure_message = f"""🚨 Sell Trade Failed:
Token Name: {sell_signal.token_name or 'N/A'}
Token Symbol: {sell_signal.token_symbol or 'N/A'}
Mint Address: {sell_signal.token_address}
Failure Reason: {failure_reason}
Error: {error_message}
Buy Signal ID: {str(buy_signal.id)}
Sell Signal ID: {str(sell_signal.id)}

Please check the logs for more details."""

        # 发送通知给所有用户
        message_sender: MessageSender = TelegramMessageSender()
        user_dao = TelegramUserDAO()
        telegram_users = await user_dao.get_all_users()
        
        logger.info(f"[SellSignal:{sell_signal.id}] 准备向 {len(telegram_users)} 个用户发送卖出失败通知")
        
        for user in telegram_users:
            chat_id = user['chat_id']
            send_result = await message_sender.send_message_to_user(
                user_id=chat_id, 
                message=failure_message, 
                parse_mode="HTML"
            )
            
            if send_result:
                logger.info(f"[SellSignal:{sell_signal.id}] 成功发送失败通知给用户 {chat_id}")
            else:
                logger.error(f"[SellSignal:{sell_signal.id}] 发送失败通知给用户 {chat_id} 失败")
        
        logger.info(f"[SellSignal:{sell_signal.id}] 已发送失败通知到Telegram")
        
    except Exception as e:
        logger.error(f"[SellSignal:{sell_signal.id}] 发送失败通知时出错: {e}", exc_info=True)


async def validate(data: Dict) -> bool:
    """
    验证单个卖出信号字典是否包含必要字段。
    """
    # 检查必要字段是否存在
    required_fields = ["token_address", "buy_signal_ref_id", "sell_reason", "sell_time"]
    if not all(field in data for field in required_fields):
        logger.error(f"验证失败，单个信号数据缺少必要字段: {data}")
        return False
            
    return True


# 如果需要本地测试
async def main_test():
    from models import init_db
    await init_db()
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    logger.info("开始生成卖出信号...")
    generated_signals = []
    async for signal in generate_sell_signals():
        logger.info(f"生成待处理信号数据: {signal}")
        generated_signals.append(signal)
        
    if generated_signals:
         logger.info(f"共生成 {len(generated_signals)} 个待处理信号，开始处理...")
         await process_sell_signal(generated_signals)
         logger.info("处理卖出信号完成.")
    else:
         logger.info("未生成需要处理的卖出信号.")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main_test()) 