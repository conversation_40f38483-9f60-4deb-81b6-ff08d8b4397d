# Bug 修复方案：SellSignalHandler 调用 TelegramMessageSender 参数错误

## 1. Bug 标识

- **描述**: 在 `workflows/monitor_kol_activity/sell_signal_handler.py` 的 `process_sell_signal` 函数中，调用 `TelegramMessageSender.send_message_to_user()` 时，因传入了未在方法签名中定义的 `disable_web_page_preview` 参数而导致 `TypeError`。
- **日志**:
  ```
  TypeError: TelegramMessageSender.send_message_to_user() got an unexpected keyword argument 'disable_web_page_preview'
  ```
- **影响**: 导致卖出信号通知无法通过 Telegram 发送给用户。

## 2. 报告日期/发现日期

- **日期**: 2025-05-19 (根据AI与用户的交互确认)

## 3. 根源分析概要

`workflows/monitor_kol_activity/sell_signal_handler.py` 在调用 `TelegramMessageSender.send_message_to_user()` 时，传递了 `disable_web_page_preview=True` 参数。然而，`utils.message_sender.message_sender.TelegramMessageSender` 类中的 `send_message_to_user` 方法并未在其定义中声明或处理此参数。

## 4. 详细的、已获批准的修复方案

1.  **修改调用方**:
    *   在 `workflows/monitor_kol_activity/sell_signal_handler.py` 的 `process_sell_signal` 函数中，当调用 `message_sender.send_message_to_user()` 时，移除 `disable_web_page_preview=True` 参数。
    *   **代码示例 (修改前)**:
        ```python
        send_result = await message_sender.send_message_to_user(user_id=chat_id, message=message, parse_mode="HTML", disable_web_page_preview=True)
        ```
    *   **代码示例 (修改后)**:
        ```python
        send_result = await message_sender.send_message_to_user(user_id=chat_id, message=message, parse_mode="HTML")
        ```

2.  **更新测试用例**:
    *   在 `test/workflows/monitor_kol_activity/test_sell_signal_handler.py` 中，查找所有对 `mock_telegram_sender_instance.send_message_to_user.assert_called_once_with(...)` 或类似 mock 断言的调用。
    *   如果这些断言中包含了 `disable_web_page_preview=True` 参数，则从中移除该参数以匹配新的调用方式。
    *   **代码示例 (测试用例修改前)**:
        ```python
        self.mock_telegram_sender_instance.send_message_to_user.assert_called_once_with(
            user_id="user123",
            message=ANY,
            parse_mode="HTML",
            disable_web_page_preview=True 
        )
        ```
    *   **代码示例 (测试用例修改后)**:
        ```python
        self.mock_telegram_sender_instance.send_message_to_user.assert_called_once_with(
            user_id="user123",
            message=ANY,
            parse_mode="HTML"
        )
        ```

3.  **规范化目录命名**:
    *   重命名目录 `utils/messsage_sender/` 为 `utils/message_sender/`。
    *   全局更新所有Python代码中对 `utils.messsage_sender` 的导入语句为 `utils.message_sender`。

## 5. 方案提出者/执行者

-   AI (Gemini 2.5 Pro, via Cursor)

## 6. 方案审阅者/批准者

-   用户 (您)

## 7. 方案批准日期

-   2025-05-19

## 8. 预期的验证方法

1.  **单元测试**:
    *   运行 `test/workflows/monitor_kol_activity/test_sell_signal_handler.py` 中的所有测试用例。修改后的测试用例应能通过，验证 `sell_signal_handler.py` 中的调用已更新。
2.  **手动/集成测试**:
    *   触发一个卖出信号场景。
    *   观察系统日志，确认不再出现 `TypeError: TelegramMessageSender.send_message_to_user() got an unexpected keyword argument 'disable_web_page_preview'` 错误。
    *   确认 Telegram 消息能够成功发送给用户（此时消息将带有网页预览，因为 `disable_web_page_preview` 参数已移除且默认为 false）。
3.  **代码审查**:
    *   检查 `workflows/monitor_kol_activity/sell_signal_handler.py` 中对 `send_message_to_user` 的调用，确认参数已移除。
    *   检查 `test/workflows/monitor_kol_activity/test_sell_signal_handler.py` 中相关的 mock断言，确认参数已移除。
    *   检查所有涉及 `message_sender` 的导入语句，确认已更新为 `utils.message_sender`。 