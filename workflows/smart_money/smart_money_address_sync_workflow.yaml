name: '智能钱包地址同步工作流'
description: '定期获取智能钱包数据，并将地址同步到监控表'

nodes:
  - name: "SmartMoneyMonitorNode"
    node_type: "input"
    interval: 300  # 每5分钟运行一次
    generate_data: workflows.smart_money.handler.fetch_smart_money_wallets
    flow_control:
      max_pending_messages: 10
      check_interval: 30
      enable_flow_control: true

  - name: "SmartMoneyAddressSyncNode"
    node_type: "storage"
    depend_ons: ["SmartMoneyMonitorNode"]
    batch_size: 100  # 每批处理100条数据
    store_data: workflows.smart_money.handler.sync_smart_money_addresses
    validate: workflows.smart_money.handler.validate_smart_money_data 