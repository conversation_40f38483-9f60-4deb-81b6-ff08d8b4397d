import unittest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
from decimal import Decimal
from typing import Dict, Any, Optional

from beanie import PydanticObjectId
from workflows.monitor_kol_activity.sell_signal_handler import process_sell_signal
from models.trade_record import TradeType as ModelTradeType, TradeStatus as ModelTradeStatus


class TestSellSignalHandlerDecimals(unittest.IsolatedAsyncioTestCase):
    """测试卖出信号处理器中Token decimals获取的相关功能"""

    def setUp(self):
        """测试前置设置"""
        self.test_token_address = "MUbEZ6mDVy39FrjNCQPyX3PM7CwSM3hQ754ii4wpump"
        self.test_buy_signal_id = PydanticObjectId()
        self.test_sell_signal_id = PydanticObjectId()
        self.test_trade_record_id = PydanticObjectId()

    def create_mock_buy_signal(self, include_decimals: bool = False) -> MagicMock:
        """创建模拟的买入信号"""
        trigger_conditions = {
            "strategy_name": "测试策略",
            "transaction_lookback_hours": 6,
            "transaction_min_amount": 100,
            "kol_account_min_count": 2,
            "sell_strategy_hours": 24,
            "sell_kol_ratio": 0
        }
        
        if include_decimals:
            trigger_conditions["input_token_decimals"] = 6
            
        mock_signal = MagicMock()
        mock_signal.id = self.test_buy_signal_id
        mock_signal.token_address = self.test_token_address
        mock_signal.token_name = "Grow a garden"
        mock_signal.token_symbol = "grow"
        mock_signal.signal_type = "kol_buy"
        mock_signal.status = "open"
        mock_signal.trigger_conditions = trigger_conditions
        mock_signal.hit_kol_wallets = ["ApRnQN2HkbCn7W2WWiT2FEKvuKJp9LugRyAE1a9Hdz1"]
        mock_signal.trigger_timestamp = datetime.utcnow()
        mock_signal.trade_record_ids = [self.test_trade_record_id]
        return mock_signal

    def create_mock_trade_record(self) -> MagicMock:
        """创建模拟的交易记录"""
        mock_record = MagicMock()
        mock_record.id = self.test_trade_record_id
        mock_record.signal_id = self.test_buy_signal_id
        mock_record.trade_type = ModelTradeType.BUY
        mock_record.token_in_address = "So11111111111111111111111111111111111111112"  # SOL
        mock_record.token_out_address = self.test_token_address
        mock_record.wallet_address = "ApRnQN2HkbCn7W2WWiT2FEKvuKJp9LugRyAE1a9Hdz1" # 使用一个格式正确的mock地址
        mock_record.token_in_amount = 0.001 # 这是计划的卖出数量 (UI amount of token to sell) / 买入时花费的SOL
        mock_record.token_out_actual_amount = None  # 卖出时实际收到的SOL / 买入时实际获得的Token (设置为 None 以触发TokenInfo)
        mock_record.token_in_actual_amount = Decimal("123.456") # 假设这是成功卖出后记录的实际卖出代币数量 (UI amount)
        # token_out_actual_amount (对于SELL来说是收到的SOL) 会由 AutoTradeManager 返回的结果填充，这里不需要预设用于格式化，除非我们模拟它也被记录到这个原始 BUY record (不合理)

        mock_record.token_out_amount_expected = 464006018  # 买入时期望的Token数量 (raw) / 卖出时期望的SOL (raw)
        mock_record.token_out_decimals = None # 确保这个是None，以测试从API获取decimals的逻辑
        mock_record.status = ModelTradeStatus.SUCCESS
        mock_record.tx_hash = "mock_buy_tx_hash_for_sell_test" # 明确设置 tx_hash
        mock_record.created_at = datetime.utcnow()
        
        return mock_record

    def create_sell_signal_data(self) -> Dict:
        """创建卖出信号数据"""
        return {
            "token_address": self.test_token_address,
            "token_name": "Grow a garden",
            "token_symbol": "grow",
            "buy_signal_ref_id": str(self.test_buy_signal_id),
            "hit_kol_wallets": ["ApRnQN2HkbCn7W2WWiT2FEKvuKJp9LugRyAE1a9Hdz1"],
            "sell_reason": "timeout",
            "sell_trigger_conditions": {"type": "timeout"},
            "sell_time": datetime.utcnow()
        }

    @patch('workflows.monitor_kol_activity.sell_signal_handler.send_sell_failure_notification')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.Signal')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.TelegramUserDAO')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.TokenMessageSendHistoryDAO')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.TradeRecordDAO')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.SignalDAO')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.AutoTradeManager')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.TelegramMessageSender')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.TokenInfo')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.SolanaMonitor')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.TradeRecordManager')
    async def test_missing_decimals_successful_api_fetch(self, mock_trade_record_manager_constructor, mock_solana_monitor_class, mock_token_info_class, mock_telegram_sender, 
                                                       mock_auto_trade_manager, mock_signal_dao_class, 
                                                       mock_trade_record_dao_class, mock_history_dao_class, 
                                                       mock_user_dao_class, mock_signal_class, mock_send_failure_notification):
        """测试用例1：trigger_conditions中缺少input_token_decimals，但API成功获取"""
        
        # 设置模拟对象
        buy_signal = self.create_mock_buy_signal(include_decimals=False)  # 不包含decimals
        trade_record = self.create_mock_trade_record()
        
        # 模拟Signal类
        mock_sell_signal = MagicMock()
        mock_sell_signal.id = self.test_sell_signal_id
        mock_signal_class.return_value = mock_sell_signal
        
        # 模拟TradeRecordManager的构造函数返回我们配置的实例 (恢复原状)
        mock_trade_record_manager_instance = AsyncMock()
        mock_trade_record_manager_instance.get_channel_attempt_records_by_trade_id.return_value = []
        mock_trade_record_manager_instance.get_confirmed_token_out_amount = AsyncMock(return_value=None) # 实际上这个方法不存在于TRM，但保留此mock以防万一其他地方（错误地）调用它
        mock_trade_record_manager_constructor.return_value = mock_trade_record_manager_instance
        
        # 模拟DAO
        mock_signal_dao = AsyncMock()
        mock_signal_dao.get_signal.return_value = buy_signal
        mock_signal_dao.find_one.return_value = None  # 没有现有的卖出信号
        mock_signal_dao.insert_signals.return_value = MagicMock(inserted_ids=[self.test_sell_signal_id])
        mock_signal_dao.update_signal_status.return_value = 1
        mock_signal_dao_class.return_value = mock_signal_dao
        
        mock_trade_record_dao = AsyncMock()
        mock_trade_record_dao.get_by_id.return_value = trade_record
        mock_trade_record_dao_class.return_value = mock_trade_record_dao
        
        mock_user_dao = AsyncMock()
        mock_user_dao.get_all_users.return_value = [{"chat_id": "123456"}]
        mock_user_dao_class.return_value = mock_user_dao
        
        mock_history_dao = AsyncMock()
        mock_history_dao.insert_many.return_value = MagicMock(inserted_ids=["hist1"])
        mock_history_dao_class.return_value = mock_history_dao
        
        # 模拟AutoTradeManager
        mock_auto_trade_manager_instance = AsyncMock()
        mock_auto_trade_manager_instance.config_manager.get_config.return_value = MagicMock(enabled=True)
        mock_auto_trade_manager_instance.execute_trade.return_value = MagicMock(
            final_status="SUCCESS",
            final_trade_record_id=self.test_trade_record_id
        )
        mock_auto_trade_manager.return_value = mock_auto_trade_manager_instance
        
        # 模拟TokenInfo API成功返回decimals
        mock_token_info_instance = AsyncMock()
        mock_token_info_instance.get_token_info = AsyncMock(return_value={
            'address': self.test_token_address,
            'symbol': 'grow',
            'name': 'Grow a garden',
            'decimals': 6  # 真实的decimals值
        })
        mock_token_info_class.return_value = mock_token_info_instance
        
        # 模拟 SolanaMonitor
        mock_solana_monitor_instance = AsyncMock()
        mock_solana_monitor_instance.get_token_balance = AsyncMock(return_value=Decimal('123.456'))
        mock_solana_monitor_instance.get_confirmed_token_output_from_tx = AsyncMock(return_value=None)
        mock_solana_monitor_class.return_value = mock_solana_monitor_instance
        
        # 模拟Telegram发送
        mock_telegram_sender_instance = AsyncMock()
        mock_telegram_sender_instance.send_message_to_user.return_value = True
        mock_telegram_sender.return_value = mock_telegram_sender_instance
        
        # 模拟失败通知函数
        mock_send_failure_notification.return_value = AsyncMock()
        
        # 执行测试
        sell_signal_data = [self.create_sell_signal_data()]
        result = await process_sell_signal(sell_signal_data)
        
        # 验证结果
        self.assertTrue(result)
        
        # 验证TokenInfo被调用
        mock_token_info_class.assert_called_once_with(address=self.test_token_address)
        mock_token_info_instance.get_token_info.assert_called_once()
        
        # 验证AutoTradeManager被调用
        mock_auto_trade_manager_instance.execute_trade.assert_called_once()
        
        # 验证信号状态更新
        mock_signal_dao.update_signal_status.assert_called_once_with(buy_signal.id, "sold")
        
        # 验证失败通知没有被调用（因为API成功）
        mock_send_failure_notification.assert_not_called()

    @patch('workflows.monitor_kol_activity.sell_signal_handler.TradeRecordManager')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.send_sell_failure_notification')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.Signal')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.TelegramUserDAO')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.TokenMessageSendHistoryDAO')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.TradeRecordDAO')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.SignalDAO')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.AutoTradeManager')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.TelegramMessageSender')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.TokenInfo')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.SolanaMonitor')
    async def test_missing_decimals_api_failure(self, mock_solana_monitor_class, mock_token_info_class, mock_telegram_sender, 
                                              mock_auto_trade_manager, mock_signal_dao_class, 
                                              mock_trade_record_dao_class, mock_history_dao_class, 
                                              mock_user_dao_class, mock_signal_class, mock_send_failure_notification,
                                              mock_trade_record_manager_class):
        """测试用例2：trigger_conditions中缺少input_token_decimals，且API获取失败"""
        
        # 设置模拟对象
        buy_signal = self.create_mock_buy_signal(include_decimals=False)  # 不包含decimals
        trade_record = self.create_mock_trade_record()
        
        # 模拟Signal类
        mock_sell_signal = MagicMock()
        mock_sell_signal.id = self.test_sell_signal_id
        mock_signal_class.return_value = mock_sell_signal
        
        # 模拟TradeRecordManager
        mock_trade_record_manager = AsyncMock()
        mock_trade_record_manager.get_channel_attempt_records_by_trade_id.return_value = []  # 返回空列表
        mock_trade_record_manager.get_confirmed_token_out_amount = AsyncMock(return_value=None)
        mock_trade_record_manager_class.return_value = mock_trade_record_manager
        
        # 模拟DAO
        mock_signal_dao = AsyncMock()
        mock_signal_dao.get_signal.return_value = buy_signal
        mock_signal_dao.find_one.return_value = None
        mock_signal_dao.insert_signals.return_value = MagicMock(inserted_ids=[self.test_sell_signal_id])
        mock_signal_dao.update_signal_status.return_value = 1
        mock_signal_dao_class.return_value = mock_signal_dao
        
        mock_trade_record_dao = AsyncMock()
        mock_trade_record_dao.get_by_id.return_value = trade_record
        mock_trade_record_dao_class.return_value = mock_trade_record_dao
        
        mock_user_dao = AsyncMock()
        mock_user_dao.get_all_users.return_value = [{"chat_id": "123456"}]
        mock_user_dao_class.return_value = mock_user_dao
        
        mock_history_dao = AsyncMock()
        mock_history_dao.insert_many.return_value = MagicMock(inserted_ids=["hist1"])
        mock_history_dao_class.return_value = mock_history_dao
        
        # 模拟AutoTradeManager
        mock_auto_trade_manager_instance = AsyncMock()
        mock_auto_trade_manager_instance.config_manager.get_config.return_value = MagicMock(enabled=True)
        mock_auto_trade_manager.return_value = mock_auto_trade_manager_instance
        
        # 模拟TokenInfo API返回None（获取失败）
        mock_token_info_instance = AsyncMock()
        mock_token_info_instance.get_token_info = AsyncMock(return_value=None)
        mock_token_info_class.return_value = mock_token_info_instance
        
        # 模拟 SolanaMonitor
        mock_solana_monitor_instance = AsyncMock()
        mock_solana_monitor_instance.get_token_balance = AsyncMock(return_value=Decimal('123.456'))
        mock_solana_monitor_instance.get_confirmed_token_output_from_tx = AsyncMock(return_value=None)
        mock_solana_monitor_class.return_value = mock_solana_monitor_instance
        
        # 模拟Telegram发送
        mock_telegram_sender_instance = AsyncMock()
        mock_telegram_sender_instance.send_message_to_user.return_value = True
        mock_telegram_sender.return_value = mock_telegram_sender_instance
        
        # 模拟失败通知函数
        mock_send_failure_notification.return_value = AsyncMock()
        
        # 执行测试
        sell_signal_data = [self.create_sell_signal_data()]
        result = await process_sell_signal(sell_signal_data)
        
        # 验证结果
        self.assertTrue(result)
        
        # 验证TokenInfo被调用
        mock_token_info_class.assert_called_once_with(address=self.test_token_address)
        mock_token_info_instance.get_token_info.assert_called_once()
        
        # 验证AutoTradeManager没有被调用（因为decimals获取失败，交易被跳过）
        mock_auto_trade_manager_instance.execute_trade.assert_not_called()
        
        # 验证失败通知被调用
        mock_send_failure_notification.assert_called_once()

    @patch('workflows.monitor_kol_activity.sell_signal_handler.TradeRecordManager')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.send_sell_failure_notification')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.Signal')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.TelegramUserDAO')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.TokenMessageSendHistoryDAO')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.TradeRecordDAO')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.SignalDAO')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.AutoTradeManager')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.TelegramMessageSender')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.TokenInfo')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.SolanaMonitor')
    async def test_missing_decimals_api_exception(self, mock_solana_monitor_class, mock_token_info_class, mock_telegram_sender, 
                                                mock_auto_trade_manager, mock_signal_dao_class, 
                                                mock_trade_record_dao_class, mock_history_dao_class, 
                                                mock_user_dao_class, mock_signal_class, mock_send_failure_notification,
                                                mock_trade_record_manager_class):
        """测试用例3：trigger_conditions中缺少input_token_decimals，且API调用异常"""
        
        # 设置模拟对象
        buy_signal = self.create_mock_buy_signal(include_decimals=False)  # 不包含decimals
        trade_record = self.create_mock_trade_record()
        
        # 模拟Signal类
        mock_sell_signal = MagicMock()
        mock_sell_signal.id = self.test_sell_signal_id
        mock_signal_class.return_value = mock_sell_signal
        
        # 模拟TradeRecordManager
        mock_trade_record_manager = AsyncMock()
        mock_trade_record_manager.get_channel_attempt_records_by_trade_id.return_value = []  # 返回空列表
        mock_trade_record_manager.get_confirmed_token_out_amount = AsyncMock(return_value=None)
        mock_trade_record_manager_class.return_value = mock_trade_record_manager
        
        # 模拟DAO
        mock_signal_dao = AsyncMock()
        mock_signal_dao.get_signal.return_value = buy_signal
        mock_signal_dao.find_one.return_value = None
        mock_signal_dao.insert_signals.return_value = MagicMock(inserted_ids=[self.test_sell_signal_id])
        mock_signal_dao.update_signal_status.return_value = 1
        mock_signal_dao_class.return_value = mock_signal_dao
        
        mock_trade_record_dao = AsyncMock()
        mock_trade_record_dao.get_by_id.return_value = trade_record
        mock_trade_record_dao_class.return_value = mock_trade_record_dao
        
        mock_user_dao = AsyncMock()
        mock_user_dao.get_all_users.return_value = [{"chat_id": "123456"}]
        mock_user_dao_class.return_value = mock_user_dao
        
        mock_history_dao = AsyncMock()
        mock_history_dao.insert_many.return_value = MagicMock(inserted_ids=["hist1"])
        mock_history_dao_class.return_value = mock_history_dao
        
        # 模拟AutoTradeManager
        mock_auto_trade_manager_instance = AsyncMock()
        mock_auto_trade_manager_instance.config_manager.get_config.return_value = MagicMock(enabled=True)
        mock_auto_trade_manager.return_value = mock_auto_trade_manager_instance
        
        # 模拟TokenInfo API抛出异常
        mock_token_info_instance = AsyncMock()
        mock_token_info_instance.get_token_info = AsyncMock(side_effect=Exception("API调用失败"))
        mock_token_info_class.return_value = mock_token_info_instance
        
        # 模拟 SolanaMonitor
        mock_solana_monitor_instance = AsyncMock()
        mock_solana_monitor_instance.get_token_balance = AsyncMock(return_value=Decimal('123.456'))
        mock_solana_monitor_instance.get_confirmed_token_output_from_tx = AsyncMock(return_value=None)
        mock_solana_monitor_class.return_value = mock_solana_monitor_instance
        
        # 模拟Telegram发送
        mock_telegram_sender_instance = AsyncMock()
        mock_telegram_sender_instance.send_message_to_user.return_value = True
        mock_telegram_sender.return_value = mock_telegram_sender_instance
        
        # 模拟失败通知函数
        mock_send_failure_notification.return_value = AsyncMock()
        
        # 执行测试
        sell_signal_data = [self.create_sell_signal_data()]
        result = await process_sell_signal(sell_signal_data)
        
        # 验证结果
        self.assertTrue(result)
        
        # 验证TokenInfo被调用
        mock_token_info_class.assert_called_once_with(address=self.test_token_address)
        mock_token_info_instance.get_token_info.assert_called_once()
        
        # 验证AutoTradeManager没有被调用（因为API异常，交易被跳过）
        mock_auto_trade_manager_instance.execute_trade.assert_not_called()
        
        # 验证失败通知被调用
        mock_send_failure_notification.assert_called_once()

    @patch('workflows.monitor_kol_activity.sell_signal_handler.send_sell_failure_notification')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.Signal')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.TelegramUserDAO')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.TokenMessageSendHistoryDAO')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.TradeRecordDAO')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.SignalDAO')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.AutoTradeManager')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.TelegramMessageSender')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.TokenInfo')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.SolanaMonitor')
    async def test_has_decimals_normal_flow(self, mock_solana_monitor_class, mock_token_info_class, mock_telegram_sender, 
                                          mock_auto_trade_manager, mock_signal_dao_class, 
                                          mock_trade_record_dao_class, mock_history_dao_class, 
                                          mock_user_dao_class, mock_signal_class, mock_send_failure_notification):
        """测试用例4：正常情况，trigger_conditions中包含input_token_decimals"""
        
        # 设置模拟对象
        buy_signal = self.create_mock_buy_signal(include_decimals=True)  # 包含decimals
        trade_record = self.create_mock_trade_record()
        # 对于正常情况，我们设置token_out_actual_amount有值
        trade_record.token_out_actual_amount = 464006018
        
        # 模拟Signal类
        mock_sell_signal = MagicMock()
        mock_sell_signal.id = self.test_sell_signal_id
        mock_signal_class.return_value = mock_sell_signal
        
        # 模拟DAO
        mock_signal_dao = AsyncMock()
        mock_signal_dao.get_signal.return_value = buy_signal
        mock_signal_dao.find_one.return_value = None
        mock_signal_dao.insert_signals.return_value = MagicMock(inserted_ids=[self.test_sell_signal_id])
        mock_signal_dao.update_signal_status.return_value = 1
        mock_signal_dao_class.return_value = mock_signal_dao
        
        mock_trade_record_dao = AsyncMock()
        mock_trade_record_dao.get_by_id.return_value = trade_record
        mock_trade_record_dao_class.return_value = mock_trade_record_dao
        
        mock_user_dao = AsyncMock()
        mock_user_dao.get_all_users.return_value = [{"chat_id": "123456"}]
        mock_user_dao_class.return_value = mock_user_dao
        
        mock_history_dao = AsyncMock()
        mock_history_dao.insert_many.return_value = MagicMock(inserted_ids=["hist1"])
        mock_history_dao_class.return_value = mock_history_dao
        
        # 模拟AutoTradeManager
        mock_auto_trade_manager_instance = AsyncMock()
        mock_auto_trade_manager_instance.config_manager.get_config.return_value = MagicMock(enabled=True)
        mock_auto_trade_manager_instance.execute_trade.return_value = MagicMock(
            final_status="SUCCESS",
            final_trade_record_id=self.test_trade_record_id
        )
        mock_auto_trade_manager.return_value = mock_auto_trade_manager_instance
        
        # 模拟Telegram发送
        mock_telegram_sender_instance = AsyncMock()
        mock_telegram_sender_instance.send_message_to_user.return_value = True
        mock_telegram_sender.return_value = mock_telegram_sender_instance
        
        # 模拟 SolanaMonitor
        mock_solana_monitor_instance = AsyncMock()
        mock_solana_monitor_instance.get_token_balance = AsyncMock(return_value=Decimal('123.456'))
        mock_solana_monitor_instance.get_confirmed_token_output_from_tx = AsyncMock(return_value=None)
        mock_solana_monitor_class.return_value = mock_solana_monitor_instance
        
        # 模拟失败通知函数
        mock_send_failure_notification.return_value = AsyncMock()
        
        # 执行测试
        sell_signal_data = [self.create_sell_signal_data()]
        result = await process_sell_signal(sell_signal_data)
        
        # 验证结果
        self.assertTrue(result)
        
        # 验证TokenInfo没有被调用（因为已有decimals）
        mock_token_info_class.assert_not_called()
        
        # 验证AutoTradeManager被调用
        mock_auto_trade_manager_instance.execute_trade.assert_called_once()
        
        # 验证信号状态更新
        mock_signal_dao.update_signal_status.assert_called_once_with(buy_signal.id, "sold")
        
        # 验证失败通知没有被调用（因为正常流程）
        mock_send_failure_notification.assert_not_called()


if __name__ == '__main__':
    unittest.main() 