# Trade Record Verification Updater Workflow Configuration
# 交易记录验证更新器工作流配置
# 
# Bug修复说明（2025-05-29）：
# 修复盈利计算不准确的Bug，使用GmgN API获取统一口径的交易金额数据
# - 买入成本：使用GmgN API获取的实际花费（替代计划投入金额）
# - 卖出收入：使用GmgN API获取的实际收入（替代链上余额变化）
# - 统一口径：GmgN API返回的金额已经扣除了平台服务费等
name: "trade_record_verification_updater"
description: "使用GmgN API验证并更新交易记录的实际金额，修复盈利计算不准确的Bug"

nodes:
  # 输入节点：扫描需要验证实际金额的交易记录
  - name: "TradeRecordScannerNode3"
    node_type: "input"
    interval: 30  # 每30秒执行一次（调整频率避免API过载）
    generate_data: workflows.trade_record_verification_updater.handler.scan_pending_trade_records
    flow_control:
      max_pending_messages: 20
      check_interval: 30
      enable_flow_control: true
        
  # 处理节点：使用GmgN API验证交易实际金额
  - name: "AmountVerificationNode3"
    node_type: "process"
    depend_ons: ["TradeRecordScannerNode3"]
    concurrency: 3  # 提高并发处理能力
    interval: 1
    process_item: workflows.trade_record_verification_updater.handler.verify_trade_amounts
        
  # 存储节点：更新实际金额到数据库
  - name: "RecordUpdateNode3"
    node_type: "storage"
    depend_ons: ["AmountVerificationNode3"]
    batch_size: 30  # 调整批次大小
    store_data: workflows.trade_record_verification_updater.handler.update_verification_results
