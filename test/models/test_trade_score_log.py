import unittest
from unittest.mock import Mock, patch
from datetime import datetime
from decimal import Decimal
from typing import Dict, Any


class TestTradeScoreLog(unittest.TestCase):
    """TradeScoreLog模型单元测试"""
    
    def setUp(self):
        """测试前置设置"""
        # 模拟KOL钱包
        self.mock_kol_wallet = Mock()
        self.mock_kol_wallet.wallet_address = "test_wallet_address_12345"
        self.mock_kol_wallet.is_smart_money = False
        self.mock_kol_wallet.is_kol = True
        
        # 测试数据
        self.test_strategy = "test_strategy"
        self.test_buy_trade_pair_id = "buy_trade_123"
        self.test_sell_trade_pair_id = "sell_trade_456"
        self.test_pnl_usd = Decimal("150.75")
        self.test_pnl_percentage = Decimal("12.5")
        self.test_score_change = Decimal("8.25")
        
        # 测试参数快照
        self.test_scoring_params = {
            "base_positive_score": 5.0,
            "base_negative_score": -3.0,
            "pnl_multiplier": 0.1,
            "max_score_per_trade": 50.0
        }
    
    def test_init_trade_score_log_profit(self):
        """测试盈利交易的TradeScoreLog初始化"""
        # 创建盈利交易的日志
        log = Mock()
        log.kol_wallet = self.mock_kol_wallet
        log.strategy_name = self.test_strategy
        log.buy_trade_pair_id = self.test_buy_trade_pair_id
        log.sell_trade_pair_id = self.test_sell_trade_pair_id
        log.pnl_usd = self.test_pnl_usd
        log.pnl_percentage = self.test_pnl_percentage
        log.score_change = self.test_score_change
        log.scoring_params_snapshot = self.test_scoring_params
        log.created_at = datetime.utcnow()
        
        # 验证属性
        self.assertEqual(log.strategy_name, self.test_strategy)
        self.assertEqual(log.buy_trade_pair_id, self.test_buy_trade_pair_id)
        self.assertEqual(log.sell_trade_pair_id, self.test_sell_trade_pair_id)
        self.assertEqual(log.pnl_usd, self.test_pnl_usd)
        self.assertEqual(log.pnl_percentage, self.test_pnl_percentage)
        self.assertEqual(log.score_change, self.test_score_change)
        self.assertEqual(log.scoring_params_snapshot, self.test_scoring_params)
        self.assertIsInstance(log.created_at, datetime)
        
        # 验证这是盈利交易
        self.assertGreater(log.pnl_usd, 0)
        self.assertGreater(log.score_change, 0)
    
    def test_init_trade_score_log_loss(self):
        """测试亏损交易的TradeScoreLog初始化"""
        # 创建亏损交易的数据
        loss_pnl_usd = Decimal("-75.25")
        loss_pnl_percentage = Decimal("-8.3")
        loss_score_change = Decimal("-4.15")
        
        # 创建亏损交易的日志
        log = Mock()
        log.kol_wallet = self.mock_kol_wallet
        log.strategy_name = self.test_strategy
        log.buy_trade_pair_id = self.test_buy_trade_pair_id
        log.sell_trade_pair_id = self.test_sell_trade_pair_id
        log.pnl_usd = loss_pnl_usd
        log.pnl_percentage = loss_pnl_percentage
        log.score_change = loss_score_change
        log.scoring_params_snapshot = self.test_scoring_params
        log.created_at = datetime.utcnow()
        
        # 验证属性
        self.assertEqual(log.pnl_usd, loss_pnl_usd)
        self.assertEqual(log.pnl_percentage, loss_pnl_percentage)
        self.assertEqual(log.score_change, loss_score_change)
        
        # 验证这是亏损交易
        self.assertLess(log.pnl_usd, 0)
        self.assertLess(log.score_change, 0)
    
    def test_zero_pnl_scenario(self):
        """测试零损益情况"""
        # 创建零损益的数据
        zero_pnl_usd = Decimal("0.0")
        zero_pnl_percentage = Decimal("0.0")
        zero_score_change = Decimal("0.0")
        
        log = Mock()
        log.kol_wallet = self.mock_kol_wallet
        log.strategy_name = self.test_strategy
        log.buy_trade_pair_id = self.test_buy_trade_pair_id
        log.sell_trade_pair_id = self.test_sell_trade_pair_id
        log.pnl_usd = zero_pnl_usd
        log.pnl_percentage = zero_pnl_percentage
        log.score_change = zero_score_change
        log.scoring_params_snapshot = self.test_scoring_params
        log.created_at = datetime.utcnow()
        
        # 验证零值
        self.assertEqual(log.pnl_usd, Decimal("0.0"))
        self.assertEqual(log.pnl_percentage, Decimal("0.0"))
        self.assertEqual(log.score_change, Decimal("0.0"))
    
    def test_scoring_params_snapshot(self):
        """测试评分参数快照功能"""
        # 测试不同的参数配置
        different_params = {
            "base_positive_score": 8.0,
            "base_negative_score": -5.0,
            "pnl_multiplier": 0.15,
            "max_score_per_trade": 100.0,
            "min_score_per_trade": -100.0
        }
        
        log = Mock()
        log.kol_wallet = self.mock_kol_wallet
        log.strategy_name = self.test_strategy
        log.buy_trade_pair_id = self.test_buy_trade_pair_id
        log.sell_trade_pair_id = self.test_sell_trade_pair_id
        log.pnl_usd = self.test_pnl_usd
        log.pnl_percentage = self.test_pnl_percentage
        log.score_change = self.test_score_change
        log.scoring_params_snapshot = different_params
        log.created_at = datetime.utcnow()
        
        # 验证参数快照
        self.assertEqual(log.scoring_params_snapshot, different_params)
        self.assertEqual(log.scoring_params_snapshot["base_positive_score"], 8.0)
        self.assertEqual(log.scoring_params_snapshot["max_score_per_trade"], 100.0)
    
    def test_precision_handling(self):
        """测试精度处理"""
        # 使用高精度的数值
        high_precision_pnl = Decimal("123.456789012345")
        high_precision_percentage = Decimal("15.123456789")
        high_precision_score = Decimal("9.876543210")
        
        log = Mock()
        log.kol_wallet = self.mock_kol_wallet
        log.strategy_name = self.test_strategy
        log.buy_trade_pair_id = self.test_buy_trade_pair_id
        log.sell_trade_pair_id = self.test_sell_trade_pair_id
        log.pnl_usd = high_precision_pnl
        log.pnl_percentage = high_precision_percentage
        log.score_change = high_precision_score
        log.scoring_params_snapshot = self.test_scoring_params
        log.created_at = datetime.utcnow()
        
        # 验证精度保持
        self.assertEqual(log.pnl_usd, high_precision_pnl)
        self.assertEqual(log.pnl_percentage, high_precision_percentage)
        self.assertEqual(log.score_change, high_precision_score)
    
    def test_timestamp_validation(self):
        """测试时间戳验证"""
        specific_time = datetime(2024, 1, 15, 14, 30, 45)
        
        log = Mock()
        log.kol_wallet = self.mock_kol_wallet
        log.strategy_name = self.test_strategy
        log.buy_trade_pair_id = self.test_buy_trade_pair_id
        log.sell_trade_pair_id = self.test_sell_trade_pair_id
        log.pnl_usd = self.test_pnl_usd
        log.pnl_percentage = self.test_pnl_percentage
        log.score_change = self.test_score_change
        log.scoring_params_snapshot = self.test_scoring_params
        log.created_at = specific_time
        
        self.assertEqual(log.created_at, specific_time)
        self.assertIsInstance(log.created_at, datetime)
    
    def test_large_values(self):
        """测试大数值处理"""
        # 测试极大的PnL值
        large_pnl = Decimal("999999.99")
        large_percentage = Decimal("1000.0")
        large_score = Decimal("500.0")
        
        log = Mock()
        log.kol_wallet = self.mock_kol_wallet
        log.strategy_name = self.test_strategy
        log.buy_trade_pair_id = self.test_buy_trade_pair_id
        log.sell_trade_pair_id = self.test_sell_trade_pair_id
        log.pnl_usd = large_pnl
        log.pnl_percentage = large_percentage
        log.score_change = large_score
        log.scoring_params_snapshot = self.test_scoring_params
        log.created_at = datetime.utcnow()
        
        # 验证大值处理
        self.assertEqual(log.pnl_usd, large_pnl)
        self.assertEqual(log.pnl_percentage, large_percentage)
        self.assertEqual(log.score_change, large_score)
    
    def test_strategy_name_variations(self):
        """测试不同策略名称的处理"""
        strategy_names = [
            "gmgn_kol_activity",
            "smart_money_follow",
            "whale_tracking",
            "test_strategy_123"
        ]
        
        for strategy in strategy_names:
            log = Mock()
            log.kol_wallet = self.mock_kol_wallet
            log.strategy_name = strategy
            log.buy_trade_pair_id = self.test_buy_trade_pair_id
            log.sell_trade_pair_id = self.test_sell_trade_pair_id
            log.pnl_usd = self.test_pnl_usd
            log.pnl_percentage = self.test_pnl_percentage
            log.score_change = self.test_score_change
            log.scoring_params_snapshot = self.test_scoring_params
            log.created_at = datetime.utcnow()
            
            self.assertEqual(log.strategy_name, strategy)
    
    def test_trade_pair_id_validation(self):
        """测试交易对ID验证"""
        # 测试不同格式的交易对ID
        test_cases = [
            ("buy_123", "sell_456"),
            ("trade_pair_abc", "trade_pair_def"),
            ("12345", "67890"),
            ("complex_trade_id_with_underscores", "another_complex_id")
        ]
        
        for buy_id, sell_id in test_cases:
            log = Mock()
            log.kol_wallet = self.mock_kol_wallet
            log.strategy_name = self.test_strategy
            log.buy_trade_pair_id = buy_id
            log.sell_trade_pair_id = sell_id
            log.pnl_usd = self.test_pnl_usd
            log.pnl_percentage = self.test_pnl_percentage
            log.score_change = self.test_score_change
            log.scoring_params_snapshot = self.test_scoring_params
            log.created_at = datetime.utcnow()
            
            self.assertEqual(log.buy_trade_pair_id, buy_id)
            self.assertEqual(log.sell_trade_pair_id, sell_id)
    
    def test_scoring_log_completeness(self):
        """测试评分日志的完整性"""
        log = Mock()
        log.kol_wallet = self.mock_kol_wallet
        log.strategy_name = self.test_strategy
        log.buy_trade_pair_id = self.test_buy_trade_pair_id
        log.sell_trade_pair_id = self.test_sell_trade_pair_id
        log.pnl_usd = self.test_pnl_usd
        log.pnl_percentage = self.test_pnl_percentage
        log.score_change = self.test_score_change
        log.scoring_params_snapshot = self.test_scoring_params
        log.created_at = datetime.utcnow()
        
        # 验证所有必要字段都存在
        self.assertIsNotNone(log.kol_wallet)
        self.assertIsNotNone(log.strategy_name)
        self.assertIsNotNone(log.buy_trade_pair_id)
        self.assertIsNotNone(log.sell_trade_pair_id)
        self.assertIsNotNone(log.pnl_usd)
        self.assertIsNotNone(log.pnl_percentage)
        self.assertIsNotNone(log.score_change)
        self.assertIsNotNone(log.scoring_params_snapshot)
        self.assertIsNotNone(log.created_at)
        
        # 验证字段类型
        self.assertIsInstance(log.strategy_name, str)
        self.assertIsInstance(log.buy_trade_pair_id, str)
        self.assertIsInstance(log.sell_trade_pair_id, str)
        self.assertIsInstance(log.pnl_usd, Decimal)
        self.assertIsInstance(log.pnl_percentage, Decimal)
        self.assertIsInstance(log.score_change, Decimal)
        self.assertIsInstance(log.scoring_params_snapshot, dict)
        self.assertIsInstance(log.created_at, datetime)


if __name__ == '__main__':
    unittest.main() 