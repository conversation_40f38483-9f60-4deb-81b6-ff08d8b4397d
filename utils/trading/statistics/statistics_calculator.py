"""
统计计算器

负责基于交易对数据计算各种统计指标，包括总体统计、分Token统计、分策略统计以及盈利亏损排行榜。
"""

import logging
from typing import List, Dict, Any
from collections import defaultdict
from datetime import datetime

from .models import (
    TradePair, OverallStats, TokenStats, StrategyStats, 
    ProfitRanking, LossRanking, StatisticsResult
)


class StatisticsCalculator:
    """
    统计计算器
    
    负责基于交易对数据计算各种统计指标。
    """
    
    def __init__(self):
        """初始化统计计算器"""
        self.logger = logging.getLogger(__name__)
    
    def calculate_statistics(self, trade_pairs: List[TradePair]) -> StatisticsResult:
        """
        计算完整的统计结果
        
        Args:
            trade_pairs: 交易对列表
            
        Returns:
            完整的统计结果
        """
        try:
            self.logger.info(f"开始计算统计数据，交易对数量: {len(trade_pairs)}")
            
            if not trade_pairs:
                self.logger.warning("交易对列表为空，返回空统计结果")
                return self._create_empty_statistics()
            
            # 计算总体统计
            overall_stats = self._calculate_overall_stats(trade_pairs)
            
            # 计算分Token统计
            token_stats = self._calculate_token_stats(trade_pairs)
            
            # 计算分策略统计
            strategy_stats = self._calculate_strategy_stats(trade_pairs)
            
            # 计算盈利排行榜
            profit_rankings = self._calculate_profit_rankings(trade_pairs)
            
            # 计算亏损排行榜
            loss_rankings = self._calculate_loss_rankings(trade_pairs)
            
            result = StatisticsResult(
                overall_stats=overall_stats,
                token_stats=token_stats,
                strategy_stats=strategy_stats,
                profit_rankings=profit_rankings,
                loss_rankings=loss_rankings,
                generation_time=datetime.now(),
                data_range={
                    "total_pairs": len(trade_pairs),
                    "profitable_pairs": len([p for p in trade_pairs if p.is_profitable]),
                    "loss_pairs": len([p for p in trade_pairs if not p.is_profitable])
                }
            )
            
            self.logger.info("统计计算完成")
            return result
            
        except Exception as e:
            self.logger.error(f"计算统计数据失败: {str(e)}")
            raise
    
    def _calculate_overall_stats(self, trade_pairs: List[TradePair]) -> OverallStats:
        """
        计算总体统计
        
        Args:
            trade_pairs: 交易对列表
            
        Returns:
            总体统计结果
        """
        try:
            total_trades = len(trade_pairs)
            
            if total_trades == 0:
                return OverallStats()
            
            # 计算盈利交易数
            profitable_trades = [pair for pair in trade_pairs if pair.is_profitable]
            profitable_count = len(profitable_trades)
            loss_count = total_trades - profitable_count
            
            # 计算胜率
            win_rate = (profitable_count / total_trades) * 100.0 if total_trades > 0 else 0.0
            
            # 计算总盈利率（所有交易的盈利率之和）
            total_profit_rate = sum(pair.profit_rate for pair in trade_pairs)
            
            # 计算平均盈利率
            avg_profit_rate = total_profit_rate / total_trades if total_trades > 0 else 0.0
            
            # 计算总盈亏金额
            total_profit_amount = sum(pair.profit_amount for pair in trade_pairs)
            
            # 计算平均持仓时长
            avg_holding_duration = sum(pair.holding_duration for pair in trade_pairs) / total_trades if total_trades > 0 else 0.0
            
            # 计算总买入和卖出金额
            total_buy_amount = sum(pair.buy_amount_sol for pair in trade_pairs)
            total_sell_amount = sum(pair.sell_amount_sol for pair in trade_pairs)
            
            # 计算整体收益率
            overall_return_rate = (total_profit_amount / total_buy_amount * 100.0) if total_buy_amount > 0 else 0.0
            
            # 计算最大单笔盈利和亏损
            profit_amounts = [pair.profit_amount for pair in trade_pairs]
            max_single_profit = max(profit_amounts) if profit_amounts else 0.0
            max_single_loss = min(profit_amounts) if profit_amounts else 0.0
            
            self.logger.debug(f"总体统计: 总交易数={total_trades}, 胜率={win_rate:.2f}%, "
                            f"总盈利率={total_profit_rate:.2f}%, 平均盈利率={avg_profit_rate:.2f}%, "
                            f"整体收益率={overall_return_rate:.2f}%")
            
            return OverallStats(
                total_trades=total_trades,
                total_win_rate=win_rate,
                total_profit_rate=total_profit_rate,
                avg_profit_rate=avg_profit_rate,
                overall_return_rate=overall_return_rate,
                total_profit_amount=total_profit_amount,
                profitable_trades=profitable_count,
                loss_trades=loss_count,
                avg_holding_duration=avg_holding_duration,
                total_buy_amount=total_buy_amount,
                total_sell_amount=total_sell_amount,
                max_single_profit=max_single_profit,
                max_single_loss=max_single_loss
            )
            
        except Exception as e:
            self.logger.error(f"计算总体统计失败: {str(e)}")
            raise
    
    def _calculate_token_stats(self, trade_pairs: List[TradePair]) -> List[TokenStats]:
        """
        计算分Token统计
        
        Args:
            trade_pairs: 交易对列表
            
        Returns:
            分Token统计结果列表
        """
        try:
            # 按token地址分组
            token_groups = defaultdict(list)
            for pair in trade_pairs:
                if pair.token_address:
                    token_groups[pair.token_address].append(pair)
            
            token_stats_list = []
            
            for token_address, pairs in token_groups.items():
                trade_count = len(pairs)
                
                # 计算盈利交易数
                profitable_pairs = [pair for pair in pairs if pair.is_profitable]
                profitable_trades = len(profitable_pairs)
                loss_trades = trade_count - profitable_trades
                
                # 计算胜率
                win_rate = (profitable_trades / trade_count) * 100.0 if trade_count > 0 else 0.0
                
                # 计算平均盈利率
                total_profit_rate = sum(pair.profit_rate for pair in pairs)
                avg_profit_rate = total_profit_rate / trade_count if trade_count > 0 else 0.0
                
                # 计算总盈亏金额
                total_profit_amount = sum(pair.profit_amount for pair in pairs)
                
                # 计算总买入和卖出金额
                total_buy_amount = sum(pair.buy_amount_sol for pair in pairs)
                total_sell_amount = sum(pair.sell_amount_sol for pair in pairs)
                
                token_stats = TokenStats(
                    token_address=token_address,
                    trade_count=trade_count,
                    win_rate=win_rate,
                    avg_profit_rate=avg_profit_rate,
                    total_profit_amount=total_profit_amount,
                    profitable_trades=profitable_trades,
                    loss_trades=loss_trades,
                    total_buy_amount=total_buy_amount,
                    total_sell_amount=total_sell_amount
                )
                
                token_stats_list.append(token_stats)
            
            # 按总盈亏金额降序排序
            token_stats_list.sort(key=lambda x: x.total_profit_amount, reverse=True)
            
            self.logger.debug(f"分Token统计完成，共 {len(token_stats_list)} 个Token")
            return token_stats_list
            
        except Exception as e:
            self.logger.error(f"计算分Token统计失败: {str(e)}")
            raise
    
    def _calculate_strategy_stats(self, trade_pairs: List[TradePair]) -> List[StrategyStats]:
        """
        计算分策略统计
        
        Args:
            trade_pairs: 交易对列表
            
        Returns:
            分策略统计结果列表
        """
        try:
            # 按策略名称分组
            strategy_groups = defaultdict(list)
            for pair in trade_pairs:
                if pair.strategy_name:
                    strategy_groups[pair.strategy_name].append(pair)
            
            strategy_stats_list = []
            
            for strategy_name, pairs in strategy_groups.items():
                trade_count = len(pairs)
                
                # 计算盈利交易数
                profitable_pairs = [pair for pair in pairs if pair.is_profitable]
                profitable_trades = len(profitable_pairs)
                loss_trades = trade_count - profitable_trades
                
                # 计算胜率
                win_rate = (profitable_trades / trade_count) * 100.0 if trade_count > 0 else 0.0
                
                # 计算平均盈利率
                total_profit_rate = sum(pair.profit_rate for pair in pairs)
                avg_profit_rate = total_profit_rate / trade_count if trade_count > 0 else 0.0
                
                # 计算总盈亏金额
                total_profit_amount = sum(pair.profit_amount for pair in pairs)
                
                # 计算总买入和卖出金额
                total_buy_amount = sum(pair.buy_amount_sol for pair in pairs)
                total_sell_amount = sum(pair.sell_amount_sol for pair in pairs)
                
                # 计算最大单笔盈利和亏损
                profit_amounts = [pair.profit_amount for pair in pairs]
                max_single_profit = max(profit_amounts) if profit_amounts else 0.0
                max_single_loss = min(profit_amounts) if profit_amounts else 0.0
                
                strategy_stats = StrategyStats(
                    strategy_name=strategy_name,
                    trade_count=trade_count,
                    win_rate=win_rate,
                    avg_profit_rate=avg_profit_rate,
                    total_profit_amount=total_profit_amount,
                    profitable_trades=profitable_trades,
                    loss_trades=loss_trades,
                    total_buy_amount=total_buy_amount,
                    total_sell_amount=total_sell_amount,
                    max_single_profit=max_single_profit,
                    max_single_loss=max_single_loss
                )
                
                strategy_stats_list.append(strategy_stats)
            
            # 按总盈亏金额降序排序
            strategy_stats_list.sort(key=lambda x: x.total_profit_amount, reverse=True)
            
            self.logger.debug(f"分策略统计完成，共 {len(strategy_stats_list)} 个策略")
            return strategy_stats_list
            
        except Exception as e:
            self.logger.error(f"计算分策略统计失败: {str(e)}")
            raise
    
    def _calculate_profit_rankings(self, trade_pairs: List[TradePair]) -> List[ProfitRanking]:
        """
        计算盈利排行榜（按盈利金额从大到小排序）
        
        Args:
            trade_pairs: 交易对列表
            
        Returns:
            盈利排行榜列表
        """
        try:
            # 筛选盈利的交易对
            profitable_pairs = [pair for pair in trade_pairs if pair.is_profitable]
            
            # 按盈利金额降序排序
            profitable_pairs.sort(key=lambda x: x.profit_amount, reverse=True)
            
            profit_rankings = []
            for pair in profitable_pairs:
                ranking = ProfitRanking(
                    signal_id=pair.signal_id,
                    strategy_name=pair.strategy_name,
                    token_address=pair.token_address,
                    profit_amount=pair.profit_amount,
                    profit_rate=pair.profit_rate,
                    buy_time=pair.buy_time,
                    sell_time=pair.sell_time
                )
                profit_rankings.append(ranking)
            
            self.logger.debug(f"盈利排行榜计算完成，共 {len(profit_rankings)} 个盈利交易")
            return profit_rankings
            
        except Exception as e:
            self.logger.error(f"计算盈利排行榜失败: {str(e)}")
            raise
    
    def _calculate_loss_rankings(self, trade_pairs: List[TradePair]) -> List[LossRanking]:
        """
        计算亏损排行榜（按亏损金额从小到大排序，即从接近0到最亏）
        
        Args:
            trade_pairs: 交易对列表
            
        Returns:
            亏损排行榜列表
        """
        try:
            # 筛选亏损的交易对
            loss_pairs = [pair for pair in trade_pairs if not pair.is_profitable]
            
            # 按亏损金额升序排序（从接近0到最亏）
            loss_pairs.sort(key=lambda x: x.profit_amount, reverse=True)  # profit_amount为负数，reverse=True表示从接近0到最负
            
            loss_rankings = []
            for pair in loss_pairs:
                ranking = LossRanking(
                    signal_id=pair.signal_id,
                    strategy_name=pair.strategy_name,
                    token_address=pair.token_address,
                    loss_amount=abs(pair.profit_amount),  # 转为正数显示
                    loss_rate=abs(pair.profit_rate),      # 转为正数显示
                    buy_time=pair.buy_time,
                    sell_time=pair.sell_time
                )
                loss_rankings.append(ranking)
            
            self.logger.debug(f"亏损排行榜计算完成，共 {len(loss_rankings)} 个亏损交易")
            return loss_rankings
            
        except Exception as e:
            self.logger.error(f"计算亏损排行榜失败: {str(e)}")
            raise
    
    def _create_empty_statistics(self) -> StatisticsResult:
        """
        创建空的统计结果
        
        Returns:
            空的统计结果
        """
        return StatisticsResult(
            overall_stats=OverallStats(),
            token_stats=[],
            strategy_stats=[],
            profit_rankings=[],
            loss_rankings=[],
            generation_time=datetime.now(),
            data_range={"total_pairs": 0, "profitable_pairs": 0, "loss_pairs": 0}
        ) 