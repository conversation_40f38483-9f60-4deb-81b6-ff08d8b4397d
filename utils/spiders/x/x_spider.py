import tweepy
import os
from typing import List, Dict, Any
from datetime import datetime, timezone
from pprint import pprint

class XSpider:
    def __init__(self, username: str):
        # 从环境变量获取认证信息
        bearer_token = os.getenv('TWITTER_BEARER_TOKEN')
        if not bearer_token:
            raise ValueError("请设置 TWITTER_BEARER_TOKEN 环境变量")
        
        # 初始化 API 客户端
        self.client = tweepy.Client(bearer_token=bearer_token)
        self.username = username
        # 初始化时获取用户信息
        self.user_info = self._get_user_info()

    def _get_user_info(self) -> Dict[str, Any]:
        """获取用户信息"""
        try:
            user = self.client.get_user(
                username=self.username,
                user_fields=['description', 'public_metrics', 'profile_image_url', 
                           'verified', 'location', 'created_at', 'name']
            )
            if not user.data:
                raise ValueError(f"未找到用户: {self.username}")
            
            # 转换用户信息为字典格式
            user_data = user.data
            return {
                'id': user_data.id,
                'username': user_data.username,
                'name': user_data.name,
                'description': user_data.description,
                'followers_count': user_data.public_metrics['followers_count'],
                'following_count': user_data.public_metrics['following_count'],
                'tweets_count': user_data.public_metrics['tweet_count'],
                'profile_image_url': user_data.profile_image_url,
                'verified': user_data.verified,
                'location': user_data.location,
                'created_at': user_data.created_at
            }
        except Exception as e:
            print(f"获取用户信息时发生错误: {str(e)}")
            return None

    def scrape(self, start_time: datetime = None, end_time: datetime = None) -> List[Dict[str, Any]]:
        """获取用户推文和信息"""
        try:
            if not self.user_info:
                return []
            
            # 获取用户最新推文
            tweets = self.client.get_users_tweets(
                self.user_info.id,
                max_results=100,
                tweet_fields=['created_at', 'text'],
                start_time=start_time.astimezone(timezone.utc) if start_time else None,
                end_time=end_time.astimezone(timezone.utc) if end_time else None
            )
            
            # 转换为统一格式
            return [
                {
                    'date': tweet.created_at,
                    'text': tweet.text
                }
                for tweet in (tweets.data or [])
            ]
        except Exception as e:
            print(f"获取推文时发生错误: {str(e)}")
            return []
        
    def scrape_user_info(self):
        """获取用户信息"""
        if not self.user_info:
            return []
        return self.user_info

if __name__ == "__main__":
    from datetime import timedelta
    end_time = datetime.now()
    start_time = end_time - timedelta(days=1)
    spider = XSpider("elonmusk")
    user_info = spider.scrape_user_info()
    print("用户信息:")
    pprint(user_info)
    # tweets = spider.scrape(start_time=start_time, end_time=end_time)
    # print(tweets)