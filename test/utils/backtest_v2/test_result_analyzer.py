"""
结果分析器单元测试

测试ResultAnalyzer类的各项功能
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from utils.backtest_v2.result_analyzer import ResultAnalyzer
from utils.backtest_v2.config_manager import BacktestConfigV2


class TestResultAnalyzer:
    """结果分析器测试"""
    
    @pytest.fixture
    def sample_config(self):
        """创建测试配置"""
        return BacktestConfigV2(
            backtest_start_time=**********,  # 2022-01-01
            backtest_end_time=**********,    # 2022-01-02
            transaction_min_amount=100,
            kol_account_min_txs=5,
            kol_account_max_txs=1000,
            kol_account_min_count=3,
            token_mint_lookback_hours=24,
            transaction_lookback_hours=1,
            sell_strategy_hours=12,
            sell_kol_ratio=0.3,
            initial_capital=1000.0,
            commission_pct=0.003,
            slippage_pct=0.002
        )
    
    @pytest.fixture
    def sample_trades(self):
        """创建测试交易数据（符合实际API格式）"""
        return [
            {
                "token_address": "token1",
                "buy_timestamp": **********,
                "sell_timestamp": **********,
                "buy_price": 0.001,
                "sell_price": 0.0015,
                "quantity": 1000.0,
                "buy_reason": "kol_signal",
                "sell_reason": "kol_ratio",
                "kol_count": 5,
                "holding_hours": 0.028,
                "return_rate": 0.4925,  # 49.25%收益率
                "profit_usd": 492.5
            },
            {
                "token_address": "token2",
                "buy_timestamp": 1640995600,
                "sell_timestamp": 1640995700,
                "buy_price": 0.002,
                "sell_price": 0.0018,
                "quantity": 500.0,
                "buy_reason": "kol_signal",
                "sell_reason": "timeout",
                "kol_count": 4,
                "holding_hours": 0.028,
                "return_rate": -0.1045,  # -10.45%收益率
                "profit_usd": -52.25
            }
        ]
    
    @pytest.fixture
    def result_analyzer(self, sample_config):
        """创建结果分析器实例"""
        return ResultAnalyzer(sample_config)
    
    def test_initialization(self, result_analyzer, sample_config):
        """测试初始化"""
        assert result_analyzer.config == sample_config
    
    def test_calculate_performance_metrics(self, result_analyzer, sample_trades):
        """测试性能指标计算"""
        metrics = result_analyzer.calculate_performance_metrics(sample_trades)

        # 验证基本指标
        assert "total_trades" in metrics
        assert "winning_trades" in metrics
        assert "losing_trades" in metrics
        assert "win_rate" in metrics
        assert "total_return" in metrics
        assert "avg_return" in metrics
        assert "max_drawdown" in metrics
        assert "sharpe_ratio" in metrics

        # 验证计算正确性
        assert metrics["total_trades"] == 2  # 2笔交易
        assert metrics["winning_trades"] == 1  # token1盈利
        assert metrics["losing_trades"] == 1   # token2亏损
        assert metrics["win_rate"] == 0.5     # 50%胜率

        # 验证收益率计算
        assert metrics["avg_return"] > 0  # 平均收益为正
        assert metrics["total_return"] > 0  # 总收益为正
    
    def test_generate_equity_curve(self, result_analyzer, sample_trades):
        """测试资金曲线生成"""
        equity_curve = result_analyzer.generate_equity_curve(sample_trades)

        # 验证资金曲线结构
        assert isinstance(equity_curve, list)
        assert len(equity_curve) > 0

        # 验证起始点
        start_point = equity_curve[0]
        assert "timestamp" in start_point
        assert "equity" in start_point
        assert "cumulative_return" in start_point
        assert "trade_count" in start_point

        # 验证起始资金
        assert start_point["equity"] == result_analyzer.config.initial_capital
        assert start_point["cumulative_return"] == 0.0
        assert start_point["trade_count"] == 0

        # 验证后续点
        if len(equity_curve) > 1:
            for point in equity_curve[1:]:
                assert "timestamp" in point
                assert "equity" in point
                assert "cumulative_return" in point
                assert "trade_count" in point
    
    def test_analyze_integration(self, result_analyzer, sample_trades):
        """测试完整分析流程"""
        results = result_analyzer.analyze(sample_trades)

        # 验证结果结构
        assert "statistics" in results
        assert "trades" in results
        assert "equity_curve" in results
        assert "config" in results
        assert "analysis_timestamp" in results

        # 验证统计指标
        stats = results["statistics"]
        assert stats["total_trades"] == 2
        assert stats["winning_trades"] == 1
        assert stats["losing_trades"] == 1
        assert stats["win_rate"] == 0.5

        # 验证交易数据
        assert results["trades"] == sample_trades

        # 验证资金曲线
        equity_curve = results["equity_curve"]
        assert len(equity_curve) >= 1
        assert equity_curve[0]["equity"] == result_analyzer.config.initial_capital
    
    def test_analyze_empty_trades(self, result_analyzer):
        """测试空交易列表的分析"""
        results = result_analyzer.analyze([])

        # 验证空结果结构
        assert "statistics" in results
        assert "trades" in results
        assert "equity_curve" in results
        assert "config" in results

        # 验证空统计指标
        stats = results["statistics"]
        assert stats["total_trades"] == 0
        assert stats["winning_trades"] == 0
        assert stats["losing_trades"] == 0
        assert stats["win_rate"] == 0.0
        assert stats["total_return"] == 0.0

        # 验证空交易列表
        assert results["trades"] == []

        # 验证资金曲线只有起始点
        equity_curve = results["equity_curve"]
        assert len(equity_curve) == 1
        assert equity_curve[0]["equity"] == result_analyzer.config.initial_capital
    
    def test_sell_reason_statistics(self, result_analyzer, sample_trades):
        """测试卖出原因统计"""
        metrics = result_analyzer.calculate_performance_metrics(sample_trades)

        # 验证卖出原因统计存在
        assert "sell_reason_stats" in metrics
        sell_stats = metrics["sell_reason_stats"]

        # 验证统计结构
        assert isinstance(sell_stats, dict)

        # 验证包含的卖出原因
        if "kol_ratio" in sell_stats:
            kol_ratio_stats = sell_stats["kol_ratio"]
            assert "return_rate_count" in kol_ratio_stats
            assert "return_rate_mean" in kol_ratio_stats
            assert "holding_hours_mean" in kol_ratio_stats

        if "timeout" in sell_stats:
            timeout_stats = sell_stats["timeout"]
            assert "return_rate_count" in timeout_stats
            assert "return_rate_mean" in timeout_stats
            assert "holding_hours_mean" in timeout_stats
    
    def test_kol_count_statistics(self, result_analyzer, sample_trades):
        """测试KOL数量统计"""
        metrics = result_analyzer.calculate_performance_metrics(sample_trades)

        # 验证KOL数量统计存在
        assert "kol_count_stats" in metrics
        kol_stats = metrics["kol_count_stats"]

        # 验证统计结构
        assert isinstance(kol_stats, dict)

        # 验证包含的KOL数量
        if "4" in kol_stats:
            kol4_stats = kol_stats["4"]
            assert "return_rate_count" in kol4_stats
            assert "return_rate_mean" in kol4_stats

        if "5" in kol_stats:
            kol5_stats = kol_stats["5"]
            assert "return_rate_count" in kol5_stats
            assert "return_rate_mean" in kol5_stats
    
    def test_holding_time_statistics(self, result_analyzer, sample_trades):
        """测试持仓时间统计"""
        metrics = result_analyzer.calculate_performance_metrics(sample_trades)

        # 验证持仓时间统计
        assert "avg_holding_hours" in metrics
        assert "max_holding_hours" in metrics
        assert "min_holding_hours" in metrics

        # 验证数值合理性
        assert metrics["avg_holding_hours"] > 0
        assert metrics["max_holding_hours"] >= metrics["avg_holding_hours"]
        assert metrics["min_holding_hours"] <= metrics["avg_holding_hours"]
        assert metrics["max_holding_hours"] >= metrics["min_holding_hours"]
    
    def test_risk_metrics_calculation(self, result_analyzer, sample_trades):
        """测试风险指标计算"""
        metrics = result_analyzer.calculate_performance_metrics(sample_trades)

        # 验证风险指标
        assert "max_drawdown" in metrics
        assert "sharpe_ratio" in metrics
        assert "profit_loss_ratio" in metrics
        assert "std_return" in metrics

        # 验证数值合理性
        assert isinstance(metrics["max_drawdown"], (int, float))
        assert isinstance(metrics["sharpe_ratio"], (int, float))
        assert isinstance(metrics["profit_loss_ratio"], (int, float))
        assert metrics["std_return"] >= 0
    
    def test_data_validation(self, result_analyzer, sample_trade_history):
        """测试数据验证"""
        # 测试有效数据
        final_capital = 691.75
        report = result_analyzer.generate_summary_report(sample_trade_history, final_capital)
        assert report is not None
        
        # 测试无效数据处理
        invalid_history = [{"invalid": "data"}]
        try:
            result_analyzer.calculate_basic_metrics(invalid_history, final_capital)
        except (KeyError, ValueError, TypeError):
            # 应该能够处理无效数据或抛出适当的异常
            pass
    
    def test_performance_calculation_accuracy(self, result_analyzer):
        """测试性能计算精度"""
        # 创建精确的测试数据
        precise_history = [
            {
                "action": "buy",
                "total_cost": 100.0,
                "transaction_cost": 1.0
            },
            {
                "action": "sell",
                "absolute_pnl": 10.0,
                "percentage_pnl": 10.0,
                "holding_period_hours": 24.0,
                "transaction_cost": 1.1,
                "net_proceeds": 110.0
            }
        ]
        
        performance = result_analyzer.calculate_performance_metrics(precise_history)
        
        # 验证精度
        assert performance["avg_holding_period_hours"] == 24.0
        assert abs(performance["total_transaction_costs"] - 2.1) < 0.001


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
