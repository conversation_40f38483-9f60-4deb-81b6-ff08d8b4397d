# KOL交易策略回测系统

本系统用于回测基于KOL交易行为的加密货币交易策略。通过分析历史数据，系统可以评估不同参数下策略的性能表现。

## 功能特点

- 支持买入和卖出策略回测
- 参数化配置，易于调整
- 支持参数网格搜索，自动寻找最优参数组合
- 详细的回测结果分析和可视化
- 使用历史价格数据计算真实收益率

## 使用方法

### 基本使用

直接从命令行运行单次回测：

```bash
python -m utils.backtest.run_backtest \
  --transaction_lookback_hours 24 \
  --transaction_min_amount 1000 \
  --kol_account_min_count 3 \
  --token_mint_lookback_hours 48 \
  --sell_strategy_hours 24 \
  --sell_kol_ratio 0.5
```

### 使用配置文件

```bash
python -m utils.backtest.run_backtest --config utils/backtest/config_example.json
```

### 参数网格搜索

```bash
python -m utils.backtest.run_backtest --mode grid --param_grid utils/backtest/param_grid_example.json
```

## 参数说明

- `transaction_lookback_hours`: 交易记录回溯时间(小时)
- `transaction_min_amount`: 最小交易金额(USD)
- `kol_account_min_count`: 最小KOL账号数量
- `token_mint_lookback_hours`: 代币铸造回溯时间(小时)
- `kol_account_min_txs`: KOL账号最少交易次数
- `kol_account_max_txs`: KOL账号最多交易次数
- `backtest_start_time`: the backtest start time (unix timestamp)
- `backtest_end_time`: the backtest end time (unix timestamp)
- `sell_strategy_hours`: 卖出策略时间窗口(小时)
- `sell_kol_ratio`: 卖出KOL比例阈值

## 输出结果

回测结果将保存在名为 `backtest_results_[时间戳]` 的目录中，包括：

- `results.json`: 包含所有回测结果的JSON文件
- `trades.csv`: 包含所有交易的CSV文件
- `returns.png`: 收益率图表

参数网格搜索结果将保存在名为 `param_search_[时间戳]` 的目录中。

## 开发扩展

### 添加新策略

1. 在 `utils/backtest/strategies` 目录下创建新的策略类
2. 继承 `BaseStrategy` 基类
3. 实现 `generate_signals` 方法

### 自定义数据加载

可以扩展 `DataLoader` 类以支持其他数据源。 