from typing import List, Set
from dao.base_dao import BaseDAO
from models.solana_monitor_address import SolanaMonitorAddress

class SolanaMonitorAddressDAO(BaseDAO[SolanaMonitorAddress]):
    """Solana 监控地址 DAO"""
    
    def __init__(self):
        super().__init__(SolanaMonitorAddress)
    
    async def find_all_addresses(self) -> List[SolanaMonitorAddress]:
        """获取所有监控地址"""
        return await self.model.find_all().to_list()
    
    async def find_by_address(self, address: str) -> SolanaMonitorAddress:
        """根据地址查找监控记录"""
        return await self.find_one({"address": address})
    
    async def find_addresses_in_list(self, addresses: Set[str]) -> List[SolanaMonitorAddress]:
        """
        查找地址列表中已存在的监控地址
        
        Args:
            addresses: 要查找的地址集合
            
        Returns:
            已存在的监控地址列表
        """
        return await self.model.find(
            {"address": {"$in": list(addresses)}}
        ).to_list()
    
    async def add_monitor_address(
        self,
        address: str,
        name: str = None,
        description: str = None
    ) -> SolanaMonitorAddress:
        """添加监控地址"""
        monitor_address = SolanaMonitorAddress(
            address=address,
            name=name,
            description=description
        )
        return await self.insert_one(monitor_address)
    
    async def batch_add_monitor_addresses(
        self,
        addresses: List[str],
        name_prefix: str = None,
        description: str = None
    ) -> List[SolanaMonitorAddress]:
        """
        批量添加监控地址
        
        Args:
            addresses: 要添加的地址列表
            name_prefix: 名称前缀，如果提供，将会为每个地址生成带序号的名称
            description: 统一的描述信息
            
        Returns:
            成功添加的监控地址列表
        """
        monitor_addresses = []
        for i, address in enumerate(addresses, 1):
            name = f"{name_prefix}_{i}" if name_prefix else None
            monitor_address = SolanaMonitorAddress(
                address=address,
                name=name,
                description=description
            )
            monitor_addresses.append(monitor_address)
            
        return await self.insert_many(monitor_addresses)
    
    async def update_monitor_address(
        self,
        address: str,
        name: str = None,
        description: str = None
    ) -> bool:
        """更新监控地址信息"""
        update_data = {}
        if name is not None:
            update_data["name"] = name
        if description is not None:
            update_data["description"] = description
            
        return await self.update_one(
            {"address": address},
            update_data
        )
    
    async def delete_monitor_address(self, address: str) -> bool:
        """删除监控地址"""
        return await self.delete_one({"address": address}) 