"""
GMGN代币链接工作流处理函数模块

提供YAML配置中引用的处理函数
"""

import logging
import traceback
from typing import List, Dict, Any, Optional, AsyncGenerator
from datetime import datetime

from dao.token_dao import TokenDAO
from models.token import Token
from dao.gmgn_token_links_dao import GmgnTokenLinksDAO
from utils.common import check_token_data
from utils.spiders.smart_money.gmgn_token_links_spider import GmgnTokenLinksSpider

# 创建日志记录器
logger = logging.getLogger("GmgnTokenLinkHandler")

# 创建爬虫实例
token_links_spider = GmgnTokenLinksSpider()

# 创建DAO实例
token_links_dao = GmgnTokenLinksDAO()

async def generate_data():
    """生成代币数据
    
    从数据库获取代币列表
    
    Returns:
        AsyncGenerator[List[Dict], None]: 代币数据列表
    """
    try:
        # 从数据库获取代币列表
        logger.info("从数据库获取代币列表")
        
        token_dao = TokenDAO()
        
        async for tokens in token_dao.get_token_no_links():
            logger.info(f"获取到 {len(tokens)} 个没有GMGN URL的代币")
            yield tokens
            
    except Exception as e:
        logger.error(f"获取代币列表时发生错误: {str(e)}\n{traceback.format_exc()}")
        yield []

async def process_item(token_dict: Dict) -> Optional[Dict]:
    """处理单个代币，获取其链接信息
    
    Args:
        token_dict: 代币信息字典
        
    Returns:
        Optional[Dict]: 处理后的代币链接信息，如果获取失败则返回None
    """
    valid, error_msg = check_token_data(token_dict, ['address'])
    if not valid:
        logger.warning(error_msg)
        return None
    
    logger.debug(f"处理代币: {token_dict.get('name', '')} ({token_dict.get('symbol', '')}) - {token_dict.get('address', '')}")
    token_address = token_dict.get('address', '')
    # 获取代币链接信息
    try:
        # 获取代币链接信息
        token_links = await token_links_spider.get_token_links("sol", token_address)
        
        if not token_links:
            logger.warning(f"无法获取代币链接信息: {token_address}")
            return None
        
        # 格式化链接信息
        formatted_links = token_links_spider.format_token_links(token_links)
        
        if not formatted_links:
            logger.warning(f"格式化代币链接信息失败: {token_address}")
            return None
        
        # 添加代币基本信息
        formatted_links.update({
            "address": token_address,
            "name": token_dict.get('name', ''),
            "symbol": token_dict.get('symbol', ''),
            "decimals": token_dict.get('decimals'),
            "icon": token_dict.get('icon'),
            "description": token_dict.get('description')
        })
        
        logger.debug(f"成功获取代币链接信息: {token_address}")
        logger.debug(f"链接信息: {formatted_links}")
        
        return formatted_links
    except Exception as e:
        error_msg = f"获取代币链接信息时发生错误: {str(e)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        return None

async def validate(data: Any) -> bool:
    """验证代币链接数据是否有效
    
    Args:
        data: 代币链接数据
        
    Returns:
        bool: 数据是否有效
    """
    # 检查必要字段
    if not isinstance(data, dict):
        logger.warning(f"数据不是字典类型: {type(data)}")
        return False
    
    required_fields = ['address', 'name', 'symbol']
    for field in required_fields:
        if field not in data:
            logger.warning(f"数据缺少必要字段: {field}")
            return False
    return True

async def store_data(data: List[Dict]) -> int:
    """存储代币链接数据到数据库
    
    Args:
        data: 代币链接数据列表
        
    Returns:
        int: 成功存储的记录数
    """
    if not data:
        return 0
    
    try:
        # 默认使用solana链
        chain = "solana"
        
        # 打印第一条数据的信息，用于调试
        if data:
            first_item = data[0]
            logger.debug(f"存储数据示例: {first_item.get('name', '未知')} ({first_item.get('symbol', '未知')}) - {first_item.get('address', '未知')}")
        
        # 调用DAO存储数据
        update_count = await token_links_dao.upsert_many(chain, data)
        logger.info(f"批量更新完成: 更新了 {update_count} 条记录")
        return update_count
    except Exception as e:
        error_msg = f"存储代币链接数据时发生错误: {str(e)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        return 0 