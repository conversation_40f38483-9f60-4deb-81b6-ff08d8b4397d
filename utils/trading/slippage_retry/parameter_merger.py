"""参数合并器 - 实现策略>渠道>全局的配置优先级合并"""

from typing import Optional, Dict, Any
import logging
from models.config import TradingParams, SingleKolStrategyConfig
from models.slippage_retry import SlippageRetryConfig

logger = logging.getLogger(__name__)


class ParameterMerger:
    """参数合并器 - 支持多层级配置和运行时覆盖"""
    
    def __init__(self):
        """初始化参数合并器"""
        pass
    
    def merge_trading_params_with_slippage_retry(
        self,
        global_params: TradingParams,
        channel_params: Optional[TradingParams] = None,
        strategy_config: Optional[SingleKolStrategyConfig] = None,
        runtime_overrides: Optional[Dict[str, Any]] = None
    ) -> TradingParams:
        """
        合并多层级交易参数配置（包括滑点重试配置）
        
        优先级：运行时覆盖 > 策略级别 > 渠道级别 > 全局级别
        
        Args:
            global_params: 全局交易参数
            channel_params: 渠道级别交易参数（可选）
            strategy_config: 策略配置（可选）
            runtime_overrides: 运行时覆盖参数（可选）
            
        Returns:
            TradingParams: 合并后的交易参数
        """
        logger.debug("开始合并多层级交易参数配置")
        
        # 1. 从全局参数开始
        merged_params = global_params.model_copy()
        logger.debug(f"基础全局参数: enable_slippage_retry={merged_params.enable_slippage_retry}")
        
        # 2. 应用渠道级别覆盖
        if channel_params:
            merged_params = self._apply_channel_overrides(merged_params, channel_params)
            logger.debug(f"应用渠道覆盖后: enable_slippage_retry={merged_params.enable_slippage_retry}")
        
        # 3. 应用策略级别覆盖
        if strategy_config:
            merged_params = self._apply_strategy_overrides(merged_params, strategy_config)
            logger.debug(f"应用策略覆盖后: enable_slippage_retry={merged_params.enable_slippage_retry}")
        
        # 4. 应用运行时覆盖
        if runtime_overrides:
            merged_params = self._apply_runtime_overrides(merged_params, runtime_overrides)
            logger.debug(f"应用运行时覆盖后: enable_slippage_retry={merged_params.enable_slippage_retry}")
        
        logger.info("多层级交易参数合并完成")
        return merged_params
    
    def extract_slippage_retry_config(
        self,
        trading_params: TradingParams,
        trade_type: str = "buy"
    ) -> SlippageRetryConfig:
        """
        从合并后的交易参数中提取滑点重试配置
        
        Args:
            trading_params: 合并后的交易参数
            trade_type: 交易类型 (buy/sell)
            
        Returns:
            SlippageRetryConfig: 滑点重试配置
        """
        effective_config = trading_params.get_effective_slippage_config(trade_type)
        
        config = SlippageRetryConfig(
            enabled=effective_config['enabled'],
            increment_percentage=effective_config['increment'],
            max_slippage_percentage=effective_config['max_slippage'],
            retry_delay_seconds=effective_config['retry_delay'],
            config_source="merged_trading_params"
        )
        
        logger.debug(f"提取{trade_type.upper()}滑点重试配置: {config}")
        return config
    
    def _apply_channel_overrides(
        self,
        base_params: TradingParams,
        channel_params: TradingParams
    ) -> TradingParams:
        """
        应用渠道级别覆盖
        
        Args:
            base_params: 基础参数（全局）
            channel_params: 渠道参数
            
        Returns:
            TradingParams: 覆盖后的参数
        """
        # 渠道参数直接覆盖全局参数
        # 由于TradingParams已经有默认值，这里直接使用渠道参数
        logger.debug("应用渠道级别参数覆盖")
        return channel_params.model_copy()
    
    def _apply_strategy_overrides(
        self,
        base_params: TradingParams,
        strategy_config: SingleKolStrategyConfig
    ) -> TradingParams:
        """
        应用策略级别覆盖
        
        Args:
            base_params: 基础参数（全局+渠道）
            strategy_config: 策略配置
            
        Returns:
            TradingParams: 覆盖后的参数
        """
        logger.debug("开始应用策略级别覆盖")
        
        # 创建参数副本
        merged_params = base_params.model_copy()
        
        # 映射策略字段到交易参数字段
        strategy_mapping = {
            # 基础交易参数映射
            'buy_amount_sol': 'default_buy_amount_sol',
            'buy_slippage_percentage': 'default_buy_slippage_percentage',
            'buy_priority_fee_sol': 'default_buy_priority_fee_sol',
            'sell_slippage_percentage': 'default_sell_slippage_percentage',
            'sell_priority_fee_sol': 'default_sell_priority_fee_sol',
            
            # 滑点重试参数映射 (strategy_xxx -> xxx)
            'strategy_enable_slippage_retry': 'enable_slippage_retry',
            'strategy_slippage_increment_percentage': 'slippage_increment_percentage',
            'strategy_max_slippage_percentage': 'max_slippage_percentage',
            
            # 买卖独立滑点重试映射
            'strategy_enable_buy_slippage_retry': 'enable_buy_slippage_retry',
            'strategy_buy_slippage_increment_percentage': 'buy_slippage_increment_percentage',
            'strategy_max_buy_slippage_percentage': 'max_buy_slippage_percentage',
            
            'strategy_enable_sell_slippage_retry': 'enable_sell_slippage_retry',
            'strategy_sell_slippage_increment_percentage': 'sell_slippage_increment_percentage',
            'strategy_max_sell_slippage_percentage': 'max_sell_slippage_percentage',
            
            # 重试间隔映射
            'strategy_retry_delay_seconds': 'retry_delay_seconds',
            'strategy_retry_delay_strategy': 'retry_delay_strategy',
            'strategy_max_retry_delay_seconds': 'max_retry_delay_seconds',
            'strategy_slippage_error_delay_seconds': 'slippage_error_delay_seconds',
            'strategy_buy_retry_delay_seconds': 'buy_retry_delay_seconds',
            'strategy_sell_retry_delay_seconds': 'sell_retry_delay_seconds'
        }
        
        # 应用策略覆盖
        applied_overrides = []
        for strategy_field, trading_field in strategy_mapping.items():
            strategy_value = getattr(strategy_config, strategy_field, None)
            if strategy_value is not None:
                # 特殊处理重试策略枚举
                if strategy_field == 'strategy_retry_delay_strategy':
                    from models.config import RetryDelayStrategy
                    if isinstance(strategy_value, str):
                        try:
                            strategy_value = RetryDelayStrategy(strategy_value)
                        except ValueError:
                            logger.warning(f"无效的重试策略值: {strategy_value}，跳过覆盖")
                            continue
                
                setattr(merged_params, trading_field, strategy_value)
                applied_overrides.append(f"{strategy_field}={strategy_value}")
        
        if applied_overrides:
            logger.debug(f"应用了策略级别覆盖: {', '.join(applied_overrides)}")
        else:
            logger.debug("未发现策略级别覆盖参数")
        
        return merged_params
    
    def _apply_runtime_overrides(
        self,
        base_params: TradingParams,
        runtime_overrides: Dict[str, Any]
    ) -> TradingParams:
        """
        应用运行时覆盖
        
        Args:
            base_params: 基础参数
            runtime_overrides: 运行时覆盖字典
            
        Returns:
            TradingParams: 覆盖后的参数
        """
        logger.debug("开始应用运行时覆盖")
        
        # 创建参数副本
        merged_params = base_params.model_copy()
        
        # 获取TradingParams的所有字段
        trading_params_fields = set(merged_params.model_fields.keys())
        
        # 应用运行时覆盖
        applied_overrides = []
        for field_name, field_value in runtime_overrides.items():
            if field_name in trading_params_fields:
                # 特殊处理重试策略枚举
                if field_name == 'retry_delay_strategy':
                    from models.config import RetryDelayStrategy
                    if isinstance(field_value, str):
                        try:
                            field_value = RetryDelayStrategy(field_value)
                        except ValueError:
                            logger.warning(f"无效的重试策略值: {field_value}，跳过覆盖")
                            continue
                
                setattr(merged_params, field_name, field_value)
                applied_overrides.append(f"{field_name}={field_value}")
            else:
                logger.debug(f"运行时覆盖字段 {field_name} 不属于TradingParams，跳过")
        
        if applied_overrides:
            logger.debug(f"应用了运行时覆盖: {', '.join(applied_overrides)}")
        else:
            logger.debug("未发现运行时覆盖参数")
        
        return merged_params
    
    def get_merge_summary(
        self,
        global_params: TradingParams,
        channel_params: Optional[TradingParams],
        strategy_config: Optional[SingleKolStrategyConfig],
        runtime_overrides: Optional[Dict[str, Any]],
        final_params: TradingParams
    ) -> str:
        """
        生成参数合并摘要
        
        Args:
            global_params: 全局参数
            channel_params: 渠道参数
            strategy_config: 策略配置
            runtime_overrides: 运行时覆盖
            final_params: 最终参数
            
        Returns:
            str: 合并摘要
        """
        summary_parts = []
        
        # 滑点重试配置变化
        if final_params.enable_slippage_retry != global_params.enable_slippage_retry:
            summary_parts.append(
                f"滑点重试: {global_params.enable_slippage_retry} → {final_params.enable_slippage_retry}"
            )
        
        # 滑点值变化
        if final_params.default_buy_slippage_percentage != global_params.default_buy_slippage_percentage:
            summary_parts.append(
                f"买入滑点: {global_params.default_buy_slippage_percentage}% → {final_params.default_buy_slippage_percentage}%"
            )
        
        if final_params.default_sell_slippage_percentage != global_params.default_sell_slippage_percentage:
            summary_parts.append(
                f"卖出滑点: {global_params.default_sell_slippage_percentage}% → {final_params.default_sell_slippage_percentage}%"
            )
        
        # 重试间隔变化
        if final_params.retry_delay_seconds != global_params.retry_delay_seconds:
            summary_parts.append(
                f"重试间隔: {global_params.retry_delay_seconds}s → {final_params.retry_delay_seconds}s"
            )
        
        if not summary_parts:
            return "无参数覆盖，使用全局默认配置"
        
        return f"参数合并摘要: {', '.join(summary_parts)}" 