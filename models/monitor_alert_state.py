from typing import Optional
from datetime import datetime
from beanie import Document, Indexed
from pydantic import BaseModel

class MonitorAlertState(Document):
    """
    Represents the state of a monitor, such as consecutive alerts and last notification time.
    """
    monitor_type: Indexed(str)  # e.g., "kol_activity_timestamp_discrepancy"
    category: Indexed(str)      # e.g., "imported", "non_imported", or a specific KOL ID
    consecutive_alerts: int = 0
    last_notification_time: Optional[datetime] = None

    class Settings:
        name = "monitor_alert_states"
        indexes = [
            [
                ("monitor_type", 1),
                ("category", 1),
            ], # Unique index for monitor_type and category
        ]

    def __repr__(self) -> str:
        return f"<MonitorAlertState monitor_type='{self.monitor_type}' category='{self.category}' alerts={self.consecutive_alerts}>"

    def __str__(self) -> str:
        return self.__repr__() 