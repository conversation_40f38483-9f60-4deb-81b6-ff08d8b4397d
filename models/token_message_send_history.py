from beanie import Document, Indexed, PydanticObjectId
from datetime import datetime
from typing import Optional, Dict
from pydantic import Field


class TokenMessageSendHistory(Document):
    """消息发送历史模型，记录特定信号通知的发送状态"""
    
    chat_id: Indexed(str) = Field(description="用户ID")
    token_address: Indexed(str) = Field(description="代币地址（冗余存储，方便查询）")
    signal_id: Indexed(PydanticObjectId) = Field(description="关联的信号ID (signals集合中的_id)")
    channel: Optional[str] = Field(description="渠道")
    status: Optional[Indexed(str)] = Field(default='sent', description="消息状态 (sent/failed)")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    strategy_name: Optional[Indexed(str)] = Field(default=None, description="触发该消息的策略名称")
    
    class Settings:
        name = "token_message_send_history"
        indexes = [
            "token_address",
            "chat_id",
            "channel",
            "signal_id",
            "status",
            "strategy_name",
        ]
        
    @classmethod
    async def from_api_data(cls, data: Dict) -> 'TokenMessageSendHistory':
        """从API数据创建模型实例 (需要根据新模型调整)"""
        return cls(
            chat_id=data.get('chat_id'),
            channel=data.get('channel'),
            token_address=data.get('token_address'),
            strategy_name=data.get('strategy_name'),
        )
