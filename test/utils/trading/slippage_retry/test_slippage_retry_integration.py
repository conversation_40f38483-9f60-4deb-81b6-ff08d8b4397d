"""
滑点重试模块集成测试

测试滑点重试系统的各个组件协同工作，包括：
1. 参数合并器 (ParameterMerger)
2. 滑点计算器 (SlippageCalculator) 
3. 重试决策引擎 (RetryDecisionEngine)
4. 重试间隔计算器 (RetryDelayCalculator)
5. 重试上下文 (RetryContext)
"""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime, timezone
from typing import Dict, Any, Optional

from utils.trading.slippage_retry import (
    ParameterMerger, 
    SlippageCalculator,
    RetryDecisionEngine,
    RetryDelayCalculator,
    RetryContext
)
from models.config import TradingParams, SingleKolStrategyConfig, RetryDelayStrategy
from models.slippage_retry import (
    SlippageRetryConfig, 
    SlippageAdjustmentReason
)


# 创建一个简单的RetryDecision类用于测试，避免Beanie初始化问题
class RetryDecision:
    def __init__(self, should_retry: bool, should_adjust_slippage: bool, 
                 is_slippage_related_error: bool = False, **kwargs):
        self.should_retry = should_retry
        self.should_adjust_slippage = should_adjust_slippage
        self.is_slippage_related_error = is_slippage_related_error
        for key, value in kwargs.items():
            setattr(self, key, value)


class TestSlippageRetryIntegration:
    """滑点重试系统集成测试"""
    
    def test_parameter_merger_basic_functionality(self):
        """测试参数合并器基本功能"""
        merger = ParameterMerger()
        
        # 创建测试参数
        global_params = TradingParams(
            default_buy_slippage_percentage=0.5,
            enable_slippage_retry=True,
            slippage_increment_percentage=0.5,
            max_slippage_percentage=5.0
        )
        
        channel_params = TradingParams(
            default_buy_slippage_percentage=1.0,  # 渠道级别覆盖
            enable_slippage_retry=True
        )
        
        strategy_config = SingleKolStrategyConfig(
            strategy_enable_slippage_retry=False,  # 策略级别覆盖
            strategy_slippage_increment_percentage=1.0,
            strategy_max_slippage_percentage=3.0
        )
        
        runtime_overrides = {
            "enable_slippage_retry": True,  # 运行时覆盖
            "max_slippage_percentage": 8.0
        }
        
        # 执行合并
        merged_params = merger.merge_trading_params_with_slippage_retry(
            global_params=global_params,
            channel_params=channel_params,
            strategy_config=strategy_config,
            runtime_overrides=runtime_overrides
        )
        
        # 验证优先级：运行时 > 策略 > 渠道 > 全局
        assert merged_params.enable_slippage_retry == True  # 运行时覆盖
        assert merged_params.max_slippage_percentage == 8.0  # 运行时覆盖
        assert merged_params.slippage_increment_percentage == 1.0  # 策略覆盖
        assert merged_params.default_buy_slippage_percentage == 1.0  # 渠道覆盖
    
    def test_slippage_calculator_increment_logic(self):
        """测试滑点计算器递增逻辑"""
        calculator = SlippageCalculator()
        
        config = SlippageRetryConfig(
            enabled=True,
            increment_percentage=0.5,
            max_slippage_percentage=3.0,
            retry_delay_seconds=1.0
        )
        
        # 测试正常递增
        new_slippage, at_limit = calculator.calculate_next_slippage(1.0, config)
        assert new_slippage == 1.5
        assert not at_limit
        
        # 测试达到上限
        new_slippage, at_limit = calculator.calculate_next_slippage(2.8, config)
        assert new_slippage == 3.0  # 被限制在上限
        assert at_limit
        
        # 测试剩余调整步数
        steps = calculator.calculate_slippage_steps_remaining(1.0, config)
        assert steps == 4  # (3.0 - 1.0) / 0.5 = 4
    
    def test_retry_decision_engine_logic(self):
        """测试重试决策引擎逻辑"""
        
        # Mock RetryDecisionEngine.make_retry_decision 方法的返回值
        with patch('utils.trading.slippage_retry.retry_decision_engine.RetryDecisionEngine.make_retry_decision') as mock_decision:
            engine = RetryDecisionEngine()
            
            config = SlippageRetryConfig(
                enabled=True,
                increment_percentage=0.5,
                max_slippage_percentage=5.0,
                retry_delay_seconds=1.0
            )
            
            # 测试滑点相关错误的决策
            mock_decision.return_value = RetryDecision(
                should_retry=True,
                should_adjust_slippage=True,
                is_slippage_related_error=True
            )
            
            decision = engine.make_retry_decision(
                retry_count=1,
                max_retries=3,
                current_slippage=2.0,
                config=config,
                error_message="slippage tolerance exceeded"
            )
            
            assert decision.should_retry
            assert decision.should_adjust_slippage
            assert decision.is_slippage_related_error
            
            # 测试达到重试上限
            mock_decision.return_value = RetryDecision(
                should_retry=False,
                should_adjust_slippage=False,
                is_slippage_related_error=True
            )
            
            decision = engine.make_retry_decision(
                retry_count=3,
                max_retries=3,
                current_slippage=2.0,
                config=config,
                error_message="slippage tolerance exceeded"
            )
            
            assert not decision.should_retry
            assert not decision.should_adjust_slippage
            
            # 测试滑点达到上限
            mock_decision.return_value = RetryDecision(
                should_retry=True,
                should_adjust_slippage=False,
                is_slippage_related_error=True
            )
            
            decision = engine.make_retry_decision(
                retry_count=1,
                max_retries=3,
                current_slippage=5.0,  # 已达上限
                config=config,
                error_message="slippage tolerance exceeded"
            )
            
            assert decision.should_retry
            assert not decision.should_adjust_slippage  # 不能再调整滑点
    
    def test_retry_delay_calculator_strategies(self):
        """测试重试间隔计算器的不同策略"""
        calculator = RetryDelayCalculator()
        
        # 创建不同策略的参数
        fixed_params = TradingParams(
            retry_delay_seconds=2.0,
            retry_delay_strategy=RetryDelayStrategy.FIXED,
            max_retry_delay_seconds=10.0
        )
        
        linear_params = TradingParams(
            retry_delay_seconds=1.0,
            retry_delay_strategy=RetryDelayStrategy.LINEAR,
            max_retry_delay_seconds=10.0
        )
        
        exponential_params = TradingParams(
            retry_delay_seconds=1.0,
            retry_delay_strategy=RetryDelayStrategy.EXPONENTIAL,
            max_retry_delay_seconds=10.0
        )
        
        # 测试固定间隔
        delay = calculator.calculate_delay(3, fixed_params)
        assert delay == 2.0
        
        # 测试线性递增
        delay = calculator.calculate_delay(3, linear_params)
        assert delay == 3.0  # 1.0 * 3
        
        # 测试指数退避
        delay = calculator.calculate_delay(3, exponential_params)
        assert delay == 4.0  # 1.0 * (2^(3-1))
        
        # 测试最大间隔限制
        delay = calculator.calculate_delay(5, exponential_params)
        assert delay == 10.0  # 被限制在max_retry_delay_seconds
    
    def test_retry_context_state_tracking(self):
        """测试重试上下文状态跟踪"""
        config = SlippageRetryConfig(
            enabled=True,
            increment_percentage=0.5,
            max_slippage_percentage=3.0,
            retry_delay_seconds=1.0
        )
        
        # Mock SlippageAdjustmentRecord 避免Beanie初始化问题
        with patch('utils.trading.slippage_retry.retry_context.SlippageAdjustmentRecord') as mock_adjustment_record:
            # 创建mock调整记录
            mock_record = Mock()
            mock_record.previous_slippage = 1.0
            mock_record.new_slippage = 1.5
            mock_record.increment_applied = 0.5
            mock_adjustment_record.return_value = mock_record
            
            context = RetryContext(
                trade_type="buy",
                initial_slippage=1.0,
                config=config,
                max_retries=3
            )
            
            # 测试初始状态
            assert context.retry_count == 0
            assert context.current_slippage == 1.0
            assert not context.is_retry_exhausted()
            assert context.can_adjust_slippage()
            
            # 记录滑点调整
            adjustment = context.record_slippage_adjustment(
                new_slippage=1.5,
                reason=SlippageAdjustmentReason.SLIPPAGE_ERROR,
                error_message="slippage tolerance exceeded"
            )
            
            assert adjustment.previous_slippage == 1.0
            assert adjustment.new_slippage == 1.5
            assert adjustment.increment_applied == 0.5
            assert context.current_slippage == 1.5
            assert context.total_adjustments == 1
            
            # 增加重试次数
            context.increment_retry_count()
            assert context.retry_count == 1
            
            # 测试摘要
            summary = context.get_summary()
            assert summary["trade_type"] == "buy"
            assert summary["retry_count"] == 1
            assert summary["current_slippage"] == 1.5
            assert summary["total_slippage_increase"] == 0.5
    
    def test_integration_complete_retry_flow(self):
        """测试完整的重试流程集成"""
        # 初始化组件
        merger = ParameterMerger()
        calculator = SlippageCalculator()
        engine = RetryDecisionEngine()
        delay_calc = RetryDelayCalculator()
        
        # 创建配置
        global_params = TradingParams(
            default_buy_slippage_percentage=1.0,
            enable_slippage_retry=True,
            slippage_increment_percentage=0.5,
            max_slippage_percentage=3.0,
            retry_delay_seconds=1.0,
            retry_delay_strategy=RetryDelayStrategy.FIXED
        )
        
        runtime_overrides = {
            "slippage_increment_percentage": 1.0  # 覆盖为更大的步长
        }
        
        # 合并参数
        merged_params = merger.merge_trading_params_with_slippage_retry(
            global_params=global_params,
            runtime_overrides=runtime_overrides
        )
        
        # 提取滑点重试配置
        config = merger.extract_slippage_retry_config(merged_params, "buy")
        assert config.increment_percentage == 1.0  # 验证运行时覆盖生效
        
        # Mock SlippageAdjustmentRecord 避免Beanie初始化问题
        with patch('utils.trading.slippage_retry.retry_context.SlippageAdjustmentRecord') as mock_adjustment_record:
            # 创建mock调整记录
            mock_record = Mock()
            mock_record.previous_slippage = 1.0
            mock_record.new_slippage = 2.0
            mock_record.increment_applied = 1.0
            mock_adjustment_record.return_value = mock_record
            
            # 创建重试上下文
            context = RetryContext("buy", 1.0, config, max_retries=3)
            
            # Mock RetryDecisionEngine的make_retry_decision方法
            with patch.object(engine, 'make_retry_decision') as mock_decision:
                # 模拟重试流程
                retry_attempts = []
                for attempt in range(3):
                    # 设置mock返回值
                    if attempt < 2:
                        mock_decision.return_value = RetryDecision(
                            should_retry=True,
                            should_adjust_slippage=True if attempt == 0 else False,
                            is_slippage_related_error=True
                        )
                    else:
                        mock_decision.return_value = RetryDecision(
                            should_retry=True,
                            should_adjust_slippage=False,  # 滑点已达上限
                            is_slippage_related_error=True
                        )
                    
                    # 做重试决策
                    decision = engine.make_retry_decision(
                        retry_count=context.retry_count,
                        max_retries=context.max_retries,
                        current_slippage=context.current_slippage,
                        config=config,
                        error_message="slippage tolerance exceeded"
                    )
                    
                    retry_attempts.append({
                        "attempt": attempt + 1,
                        "decision": decision,
                        "current_slippage": context.current_slippage
                    })
                    
                    if not decision.should_retry:
                        break
                        
                    # 计算重试间隔
                    delay = delay_calc.calculate_delay(
                        context.retry_count + 1, 
                        merged_params, 
                        is_slippage_error=True
                    )
                    
                    # 调整滑点（如果需要）
                    if decision.should_adjust_slippage:
                        new_slippage, at_limit = calculator.calculate_next_slippage(
                            context.current_slippage, config
                        )
                        context.record_slippage_adjustment(
                            new_slippage=new_slippage,
                            reason=SlippageAdjustmentReason.SLIPPAGE_ERROR,
                            error_message="slippage tolerance exceeded"
                        )
                    
                    # 增加重试计数
                    context.increment_retry_count()
                
                # 验证重试流程
                assert len(retry_attempts) == 3
                assert retry_attempts[0]["decision"].should_retry
                assert retry_attempts[0]["decision"].should_adjust_slippage
                assert retry_attempts[2]["decision"].should_retry
                assert not retry_attempts[2]["decision"].should_adjust_slippage  # 滑点已达上限
                
                # 验证最终状态
                assert context.current_slippage == 2.0  # 1.0 + 1.0 (一次调整)
                assert context.retry_count == 3
    
    def test_error_scenarios_integration(self):
        """测试错误场景的集成处理"""
        
        # Mock RetryDecisionEngine.make_retry_decision 方法
        with patch('utils.trading.slippage_retry.retry_decision_engine.RetryDecisionEngine.make_retry_decision') as mock_decision:
            engine = RetryDecisionEngine()
            
            config = SlippageRetryConfig(
                enabled=True,
                increment_percentage=0.5,
                max_slippage_percentage=5.0,
                retry_delay_seconds=1.0
            )
            
            # 测试不可重试错误
            mock_decision.return_value = RetryDecision(
                should_retry=False,
                should_adjust_slippage=False,
                is_slippage_related_error=False
            )
            
            decision = engine.make_retry_decision(
                retry_count=1,
                max_retries=3,
                current_slippage=2.0,
                config=config,
                error_message="insufficient funds"  # 不可重试错误
            )
            
            assert not decision.should_retry
            assert not decision.should_adjust_slippage
            assert not decision.is_slippage_related_error
            
            # 测试非滑点相关错误
            mock_decision.return_value = RetryDecision(
                should_retry=True,
                should_adjust_slippage=False,
                is_slippage_related_error=False
            )
            
            decision = engine.make_retry_decision(
                retry_count=1,
                max_retries=3,
                current_slippage=2.0,
                config=config,
                error_message="network timeout"  # 网络错误，可重试但不调整滑点
            )
            
            assert decision.should_retry
            assert not decision.should_adjust_slippage
            assert not decision.is_slippage_related_error
    
    def test_configuration_edge_cases(self):
        """测试配置边界情况"""
        merger = ParameterMerger()
        calculator = SlippageCalculator()
        
        # 测试禁用滑点重试
        disabled_config = SlippageRetryConfig(
            enabled=False,
            increment_percentage=0.5,
            max_slippage_percentage=5.0,
            retry_delay_seconds=1.0
        )
        
        new_slippage, at_limit = calculator.calculate_next_slippage(2.0, disabled_config)
        assert new_slippage == 2.0  # 不应该改变
        assert not at_limit
        
        # 测试零步长
        zero_increment_config = SlippageRetryConfig(
            enabled=True,
            increment_percentage=0.0,
            max_slippage_percentage=5.0,
            retry_delay_seconds=1.0
        )
        
        steps = calculator.calculate_slippage_steps_remaining(2.0, zero_increment_config)
        assert steps == 0  # 零步长导致无法调整
        
        # 测试超大步长
        large_increment_config = SlippageRetryConfig(
            enabled=True,
            increment_percentage=10.0,  # 大于最大滑点
            max_slippage_percentage=5.0,
            retry_delay_seconds=1.0
        )
        
        new_slippage, at_limit = calculator.calculate_next_slippage(1.0, large_increment_config)
        assert new_slippage == 5.0  # 被限制在最大值
        assert at_limit
    
    def test_parameter_validation_integration(self):
        """测试参数验证集成"""
        calculator = SlippageCalculator()
        delay_calc = RetryDelayCalculator()
        
        # 测试滑点配置验证
        valid_config = SlippageRetryConfig(
            enabled=True,
            increment_percentage=0.5,
            max_slippage_percentage=5.0,
            retry_delay_seconds=1.0
        )
        assert calculator.validate_slippage_config(valid_config)
        
        # 测试无效配置
        invalid_config = SlippageRetryConfig(
            enabled=True,
            increment_percentage=0.0,  # 无效：步长为零
            max_slippage_percentage=5.0,
            retry_delay_seconds=1.0
        )
        assert not calculator.validate_slippage_config(invalid_config)
        
        # 测试间隔配置验证
        valid_params = TradingParams(
            retry_delay_seconds=1.0,
            max_retry_delay_seconds=10.0,
            retry_delay_strategy=RetryDelayStrategy.FIXED
        )
        assert delay_calc.validate_delay_config(valid_params)
        
        # 测试无效间隔配置
        invalid_params = TradingParams(
            retry_delay_seconds=5.0,
            max_retry_delay_seconds=2.0,  # 无效：最大值小于基础值
            retry_delay_strategy=RetryDelayStrategy.FIXED
        )
        assert not delay_calc.validate_delay_config(invalid_params)
    
    def test_meme_coin_market_optimization(self):
        """测试meme币市场优化配置"""
        delay_calc = RetryDelayCalculator()
        
        # 测试meme币市场推荐配置
        meme_params = TradingParams(
            retry_delay_seconds=1.0,  # 较短间隔
            max_retry_delay_seconds=5.0,  # 较短最大间隔
            retry_delay_strategy=RetryDelayStrategy.FIXED,
            slippage_error_delay_seconds=0.5  # 滑点错误更短间隔
        )
        
        # 验证配置适合meme币市场
        assert delay_calc.validate_delay_config(meme_params, meme_coin_market=True)
        
        # 测试滑点错误专用间隔
        delay = delay_calc.calculate_delay(
            retry_count=1,
            trading_params=meme_params,
            is_slippage_error=True,  # 滑点错误
            trade_type="buy"
        )
        assert delay == 0.5  # 使用滑点错误专用间隔
        
        # 测试预估总重试时间
        total_time = delay_calc.estimate_total_retry_time(
            max_retries=3,
            trading_params=meme_params,
            trade_type="buy"
        )
        assert total_time == 3.0  # 3次重试 * 1秒间隔
    
    @patch('utils.trading.slippage_retry.retry_decision_engine.logger')
    def test_logging_integration(self, mock_logger):
        """测试日志记录集成"""
        
        # Mock RetryDecisionEngine.make_retry_decision 方法
        with patch('utils.trading.slippage_retry.retry_decision_engine.RetryDecisionEngine.make_retry_decision') as mock_decision:
            engine = RetryDecisionEngine()
            
            config = SlippageRetryConfig(
                enabled=True,
                increment_percentage=0.5,
                max_slippage_percentage=5.0,
                retry_delay_seconds=1.0
            )
            
            # 设置mock返回值
            mock_decision.return_value = RetryDecision(
                should_retry=True,
                should_adjust_slippage=True,
                is_slippage_related_error=True
            )
            
            # 执行操作，应该产生日志
            decision = engine.make_retry_decision(
                retry_count=1,
                max_retries=3,
                current_slippage=2.0,
                config=config,
                error_message="slippage tolerance exceeded"
            )
            
            # 验证日志被调用（这里只是验证组件能正常工作，实际日志内容可能因实现而异）
            assert decision.should_retry
            assert decision.should_adjust_slippage 