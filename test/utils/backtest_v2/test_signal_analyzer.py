"""
信号分析组件单元测试

测试SignalAnalyzer类的各项功能
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from utils.backtest_v2.signal_analyzer import SignalAnalyzer
from utils.backtest_v2.config_manager import BacktestConfigV2


class TestSignalAnalyzer:
    """信号分析组件测试"""
    
    @pytest.fixture
    def sample_config(self):
        """创建测试配置"""
        return BacktestConfigV2(
            backtest_start_time=**********,  # 2022-01-01
            backtest_end_time=**********,    # 2022-01-02
            transaction_min_amount=100,
            kol_account_min_txs=5,
            kol_account_max_txs=1000,
            kol_account_min_count=3,
            token_mint_lookback_hours=24,
            transaction_lookback_hours=1,
            same_token_notification_interval_minutes=30,
            initial_capital=1000.0
        )
    
    @pytest.fixture
    def sample_sell_data(self):
        """创建测试卖出数据"""
        return {
            "addr1": [
                {"timestamp": **********, "wallet": "kol1", "cost_usd": 300},
                {"timestamp": **********, "wallet": "kol2", "cost_usd": 400}
            ]
        }
    
    @pytest.fixture
    def signal_analyzer(self, sample_config, sample_sell_data):
        """创建信号分析器实例"""
        return SignalAnalyzer(sample_config, sample_sell_data)
    
    def test_initialization(self, signal_analyzer, sample_config):
        """测试初始化"""
        assert signal_analyzer.config == sample_config
        assert signal_analyzer.kol_account_min_count == sample_config.kol_account_min_count
        assert signal_analyzer.transaction_lookback_hours == sample_config.transaction_lookback_hours
        assert signal_analyzer.same_token_notification_interval_minutes == sample_config.same_token_notification_interval_minutes
        assert signal_analyzer.sell_data_cache is not None
    
    def test_check_signal_interval(self, signal_analyzer):
        """测试信号间隔检查"""
        token_address = "test_token"
        current_timestamp = **********

        # 第一次信号，应该允许
        is_allowed = signal_analyzer._check_signal_interval(token_address, current_timestamp)
        assert is_allowed == True

        # 间隔太短，应该被拒绝
        next_timestamp = current_timestamp + 10 * 60  # 10分钟后
        is_allowed = signal_analyzer._check_signal_interval(token_address, next_timestamp)
        assert is_allowed == False

        # 间隔足够长，应该允许
        next_timestamp = current_timestamp + 40 * 60  # 40分钟后
        is_allowed = signal_analyzer._check_signal_interval(token_address, next_timestamp)
        assert is_allowed == True
    
    def test_sell_data_cache_access(self, signal_analyzer):
        """测试卖出数据缓存访问"""
        # 验证卖出数据缓存已正确初始化
        assert signal_analyzer.sell_data_cache is not None
        assert isinstance(signal_analyzer.sell_data_cache, dict)

        # 验证测试数据中的卖出记录
        assert "addr1" in signal_analyzer.sell_data_cache
        assert len(signal_analyzer.sell_data_cache["addr1"]) == 2

        # 验证卖出记录格式
        sell_record = signal_analyzer.sell_data_cache["addr1"][0]
        assert "timestamp" in sell_record
        assert "wallet" in sell_record
        assert "cost_usd" in sell_record
    
    def test_analyze_token_signals_basic(self, signal_analyzer):
        """测试基本的token信号分析"""
        # 准备测试数据
        token_data = {
            "records": [
                {"timestamp": **********, "wallet": "kol1", "cost_usd": 500, "price_usd": 0.001},
                {"timestamp": **********, "wallet": "kol2", "cost_usd": 600, "price_usd": 0.0012},
                {"timestamp": **********, "wallet": "kol3", "cost_usd": 400, "price_usd": 0.0011},
                {"timestamp": **********, "wallet": "kol4", "cost_usd": 300, "price_usd": 0.0013}
            ],
            "kol_wallets": [
                {"wallet_address": "kol1"}, {"wallet_address": "kol2"},
                {"wallet_address": "kol3"}, {"wallet_address": "kol4"}
            ],
            "kol_wallets_count": 4
        }

        token_address = "test_token"

        # 分析信号
        signals = signal_analyzer.analyze_token_signals(token_address, token_data)

        # 验证结果
        assert isinstance(signals, list)
        # 应该生成信号（当达到最小KOL数量时）
        assert len(signals) >= 1

        if len(signals) > 0:
            signal = signals[0]
            assert "signal_timestamp" in signal
            assert "token_address" in signal
            assert signal["token_address"] == token_address
    
    def test_analyze_token_signals_insufficient_kols(self, signal_analyzer):
        """测试KOL数量不足的情况"""
        # 准备测试数据 - 只有2个KOL，少于最小要求3个
        token_data = {
            "records": [
                {"timestamp": **********, "wallet": "kol1", "cost_usd": 500, "price_usd": 0.001},
                {"timestamp": **********, "wallet": "kol2", "cost_usd": 600, "price_usd": 0.0012}
            ],
            "kol_wallets": [
                {"wallet_address": "kol1"}, {"wallet_address": "kol2"}
            ],
            "kol_wallets_count": 2
        }

        token_address = "test_token"

        # 分析信号
        signals = signal_analyzer.analyze_token_signals(token_address, token_data)

        # 验证结果 - 应该没有信号
        assert len(signals) == 0
    
    def test_configuration_parameters(self, signal_analyzer):
        """测试配置参数"""
        # 验证配置参数正确传递
        assert signal_analyzer.kol_account_min_count == 3
        assert signal_analyzer.transaction_lookback_hours == 1
        assert signal_analyzer.same_token_notification_interval_minutes == 30

        # 验证配置对象
        assert signal_analyzer.config is not None
        assert signal_analyzer.config.kol_account_min_count == 3

        # 验证卖出数据缓存
        assert signal_analyzer.sell_data_cache is not None
        assert isinstance(signal_analyzer.sell_data_cache, dict)
    
    def test_sliding_window_basic(self, signal_analyzer):
        """测试滑动窗口基本功能"""
        # 准备测试数据 - 模拟时间序列的买入记录
        buy_records = [
            {"timestamp": **********, "wallet": "kol1", "cost_usd": 500, "price_usd": 0.001},
            {"timestamp": **********, "wallet": "kol2", "cost_usd": 600, "price_usd": 0.0012},  # 1分钟后
            {"timestamp": **********, "wallet": "kol3", "cost_usd": 400, "price_usd": 0.0011},  # 2分钟后
            {"timestamp": **********, "wallet": "kol4", "cost_usd": 300, "price_usd": 0.0013},  # 3分钟后
            {"timestamp": **********, "wallet": "kol5", "cost_usd": 350, "price_usd": 0.0014}   # 4分钟后
        ]

        token_address = "test_token"
        token_data = {
            "records": buy_records,
            "kol_wallets": [
                {"wallet_address": f"kol{i}"} for i in range(1, 6)
            ],
            "kol_wallets_count": 5
        }

        # 分析信号
        signals = signal_analyzer.analyze_token_signals(token_address, token_data)

        # 验证滑动窗口生成了信号
        assert len(signals) >= 1

        # 验证信号格式
        for signal in signals:
            assert "signal_timestamp" in signal
            assert "token_address" in signal
            assert signal["token_address"] == token_address


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
