import os
import asyncio
import traceback
from dotenv import load_dotenv
# import httpx # 不再直接使用 httpx 进行 API 调用
import base64 # 可能仍用于解码/编码，但主要操作在JS中
import logging
import json
import re
from decimal import Decimal, Context, getcontext
from typing import Dict, Any, Optional, Tuple
from datetime import datetime, timezone
import subprocess # <--- 添加 subprocess
import time # <--- 添加 time for sleep

# from solders.keypair import Keypair # <--- 移除，签名在JS中处理
# from solders.transaction import VersionedTransaction # <--- 移除
# from solders.signature import Signature # <--- 移除

from .trade_interface import TradeInterface, TradeResult, TradeType, TradeStatus
from beanie import PydanticObjectId

logger = logging.getLogger(__name__)

# 定义常量
SOL_MINT_ADDRESS = "So11111111111111111111111111111111111111112"
# DEFAULT_TIMEOUT = httpx.Timeout(10.0, connect=5.0) # 不再需要
DEFAULT_POLL_INTERVAL = 5  # 秒 (如果Python端还进行轮询，则保留)
DEFAULT_MAX_POLL_ATTEMPTS = 12 # (如果Python端还进行轮询，则保留)

# GMGN_API_ENDPOINTS = { # <--- 不再直接使用
#     "GET_SWAP_ROUTE": "/defi/router/v1/sol/tx/get_swap_route",
#     "SUBMIT_SIGNED_TRANSACTION": "/defi/router/v1/sol/tx/submit_signed_transaction",
#     "GET_TRANSACTION_STATUS": "/defi/router/v1/sol/tx/get_transaction_status"
# }

# NODE_SCRIPT_PATH = "gmgn_test.mjs" # <--- 假设脚本在项目根目录
# Construct an absolute path to the Node.js script.
# This file (gmgn_trade_service.py) is in utils/trading/solana/, so project_root is three levels up from this file's directory.
_current_file_dir = os.path.dirname(os.path.abspath(__file__))
_project_root = os.path.dirname(os.path.dirname(os.path.dirname(_current_file_dir)))
NODE_SCRIPT_PATH = os.path.join(_project_root, "utils", "trading", "solana", "gmgn_test.mjs")

def _calculate_next_priority_fee(current_fee_decimal: Decimal) -> Decimal:
    """
    Calculates the next priority fee by incrementing the smallest significant digit.
    Examples:
        0.00005 -> 0.00006
        0.00009 -> 0.00010
        0.00500 -> 0.00501
        0.005   -> 0.006
        0.5     -> 0.6
        0.9     -> 1.0
        1.0     -> 1.1
        1       -> 2
    """
    # Determine exponent and decimal places using as_tuple() for robustness
    current_fee_tuple = current_fee_decimal.as_tuple()
    exponent = current_fee_tuple.exponent
    
    decimal_places = 0
    is_negative_exponent = isinstance(exponent, int) and exponent < 0

    if is_negative_exponent: # It's a decimal with a fractional part
        decimal_places = abs(exponent)
        increment = Decimal('1') / (Decimal('10') ** decimal_places)
    else: # It's an integer or zero (exponent >= 0)
        increment = Decimal('1')
            
    next_fee = current_fee_decimal + increment

    # Re-quantize to preserve trailing zeros or specific decimal representation if needed,
    # matching the original number's implied precision for display/logging consistency.
    if is_negative_exponent:
        original_decimal_places = abs(exponent)
        next_fee_exponent = next_fee.as_tuple().exponent

        if isinstance(next_fee_exponent, int):
            if next_fee_exponent == 0 and original_decimal_places > 0: 
                # Case: 0.9 -> 1, should be represented as 1.0 (original_decimal_places = 1)
                # Case: 0.99 -> 1, should be 1.00 (original_decimal_places = 2)
                return next_fee.quantize(Decimal('1e-' + str(original_decimal_places)))
            elif next_fee_exponent < 0 and abs(next_fee_exponent) < original_decimal_places:
                # Case: 0.00009 -> 0.0001, should be 0.00010 (original_decimal_places = 5, next_fee_exponent = -4)
                # This logic needs to ensure it correctly makes 0.0001 into 0.00010
                # For Decimals, 0.0001 and 0.00010 are equal.
                # The quantize here is more about string representation matching the *original* precision.
                # If original was "0.00009", it had 5 decimal places of precision implied.
                # next_fee is 0.0001. To show it with 5 places like original (0.00010), we quantize to 1e-5.
                # This was a bit confusing in the previous version. Let's clarify:
                # If original was 0.00009 (exponent -5), next is 0.0001 (exponent -4). We want 0.00010 (exponent -5 for string).
                return next_fee.quantize(Decimal('1e-' + str(original_decimal_places)))
            # else: no re-quantization needed for other decimal cases, or next_fee has more/equal places.
    
    return next_fee

def _analyze_error_message_characteristics(error_message: str) -> Dict[str, Any]:
    """
    分析错误消息的特征，用于增强错误日志记录
    
    Args:
        error_message: 需要分析的错误消息
        
    Returns:
        包含错误消息统计信息的字典
    """
    if not error_message:
        return {"message_length": 0, "has_html_chars": False, "special_char_count": 0}
    
    # 统计特殊字符
    html_chars = ['<', '>', '"', "'", '&']
    special_char_count = sum(error_message.count(char) for char in html_chars)
    has_html_chars = special_char_count > 0
    
    # 检测是否包含HTML标签或DOCTYPE
    has_html_tags = bool(re.search(r'<[^>]*>', error_message))
    has_doctype = 'DOCTYPE' in error_message.upper()
    
    # 检测JSON解析错误模式
    has_json_parse_error = 'SyntaxError' in error_message and 'JSON' in error_message
    
    return {
        "message_length": len(error_message),
        "has_html_chars": has_html_chars,
        "special_char_count": special_char_count,
        "has_html_tags": has_html_tags,
        "has_doctype": has_doctype,
        "has_json_parse_error": has_json_parse_error,
        "first_100_chars": error_message[:100] if len(error_message) > 100 else error_message
    }

def _analyze_node_stderr_output(stderr_content: str, trade_record_id: PydanticObjectId, attempt_number: int) -> Dict[str, Any]:
    """
    详细分析Node.js脚本的stderr输出
    
    Args:
        stderr_content: Node.js脚本的stderr输出内容
        trade_record_id: 交易记录ID，用于日志记录
        attempt_number: 尝试次数，用于日志记录
        
    Returns:
        包含分析结果的字典
    """
    if not stderr_content:
        return {"analysis_status": "empty_stderr", "content_length": 0}
    
    analysis_result = {
        "analysis_status": "analyzed",
        "content_length": len(stderr_content),
        "line_count": len(stderr_content.splitlines()),
        "contains_gmgn_api_errors": False,
        "contains_response_analysis": False,
        "contains_html_response": False,
        "contains_json_parse_errors": False,
        "error_stages": [],
        "detailed_logs": []
    }
    
    lines = stderr_content.splitlines()
    
    for line_num, line in enumerate(lines, 1):
        line_lower = line.lower()
        
        # 检测GMGN API相关错误
        if any(keyword in line_lower for keyword in ['gmgn', 'api']) or \
           ('route' in line_lower and any(error_word in line_lower for error_word in ['error', 'failed', 'status'])) or \
           ('submit' in line_lower and any(error_word in line_lower for error_word in ['error', 'failed', 'status'])):
            analysis_result["contains_gmgn_api_errors"] = True
        
        # 检测响应分析日志
        if 'response analysis' in line_lower:
            analysis_result["contains_response_analysis"] = True
        
        # 检测HTML响应
        if any(keyword in line_lower for keyword in ['html', 'doctype', '<html']):
            analysis_result["contains_html_response"] = True
        
        # 检测JSON解析错误
        if 'json' in line_lower and any(keyword in line_lower for keyword in ['parse', 'syntax', 'error']):
            analysis_result["contains_json_parse_errors"] = True
        
        # 提取错误阶段信息
        if 'stage:' in line_lower:
            stage_match = re.search(r'stage:\s*(\w+)', line_lower)
            if stage_match:
                analysis_result["error_stages"].append(stage_match.group(1))
        # 从JSON格式的错误消息中提取阶段信息
        elif '"stage"' in line:
            stage_match = re.search(r'"stage":\s*"(\w+)"', line)
            if stage_match:
                analysis_result["error_stages"].append(stage_match.group(1))
        
        # 收集重要的详细日志行（限制数量避免日志过长）
        if any(keyword in line_lower for keyword in ['error', 'failed', 'status:', 'response analysis', 'html']):
            if len(analysis_result["detailed_logs"]) < 20:  # 限制最多20行
                analysis_result["detailed_logs"].append(f"L{line_num}: {line.strip()}")
    
    # 记录分析结果
    logger.info(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] Node.js stderr analysis: "
                f"Length={analysis_result['content_length']}, Lines={analysis_result['line_count']}, "
                f"GMGN_errors={analysis_result['contains_gmgn_api_errors']}, "
                f"HTML_response={analysis_result['contains_html_response']}, "
                f"JSON_errors={analysis_result['contains_json_parse_errors']}")
    
    if analysis_result["error_stages"]:
        logger.info(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] Detected error stages: {list(set(analysis_result['error_stages']))}")
    
    if analysis_result["detailed_logs"]:
        logger.info(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] Key stderr lines:\n" + 
                   "\n".join(analysis_result["detailed_logs"]))
    
    return analysis_result

class GmgnTradeService(TradeInterface):
    # def __init__(self, gmgn_api_host: str, http_client: Optional[httpx.AsyncClient] = None):
    def __init__(self, gmgn_api_host: str):
        self.gmgn_api_host = gmgn_api_host.rstrip('/')
        # self.http_client = http_client if http_client else httpx.AsyncClient(timeout=DEFAULT_TIMEOUT) # 不再需要 http_client

    # _make_request 方法将被 Node.js 脚本执行替代
    # async def _make_request(...): 
    #     ...

    async def _execute_single_trade_attempt(
        self,
        input_token_address: str,
        output_token_address: str,
        amount_input_token: float, # UI amount
        wallet_private_key_b58: str,
        wallet_address: str,
        current_slippage: float, # Percentage, e.g. 1.0 for 1%
        current_priority_fee: Decimal, # SOL amount as Decimal
        strategy_snapshot: Dict[str, Any], # Original snapshot for other params
        trade_record_id: PydanticObjectId,
        attempt_number: int
    ) -> TradeResult:
        logger.info(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] Executing trade via Node.js: {amount_input_token} {input_token_address} -> {output_token_address}. Slippage: {current_slippage}%, Priority Fee: {current_priority_fee} SOL")

        input_decimals: Optional[int] = None
        input_decimals_from_snapshot = strategy_snapshot.get('input_token_decimals')

        if input_token_address == SOL_MINT_ADDRESS:
            input_decimals = 9
        elif input_decimals_from_snapshot is not None:
            if not isinstance(input_decimals_from_snapshot, int):
                logger.error(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] 'input_token_decimals' ({input_decimals_from_snapshot}) in strategy_snapshot for SPL token {input_token_address} must be an integer. Type was {type(input_decimals_from_snapshot)}. Failing attempt.")
                return TradeResult(status=TradeStatus.FAILED, error_message=f"Invalid input_token_decimals '{input_decimals_from_snapshot}' (must be an integer) for {input_token_address}", trade_record_id=trade_record_id, executed_at=datetime.now(timezone.utc))
            input_decimals = input_decimals_from_snapshot
            if not (0 <= input_decimals <= 18):
                logger.error(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] 'input_token_decimals' ({input_decimals_from_snapshot}) for SPL token {input_token_address} is out of plausible range (0-18). Failing attempt.")
                return TradeResult(status=TradeStatus.FAILED, error_message=f"Invalid input_token_decimals '{input_decimals_from_snapshot}' (out of range 0-18) for {input_token_address}", trade_record_id=trade_record_id, executed_at=datetime.now(timezone.utc))
            logger.info(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] Using provided input_token_decimals: {input_decimals} for SPL token {input_token_address}")
        else:
            logger.error(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] 'input_token_decimals' not found in strategy_snapshot for SPL token {input_token_address}. This is required for trades involving SPL tokens. Failing attempt.")
            return TradeResult(status=TradeStatus.FAILED, error_message=f"input_token_decimals missing in strategy_snapshot for SPL token {input_token_address}", trade_record_id=trade_record_id, executed_at=datetime.now(timezone.utc))

        try:
            in_amount_lamports_str = str(int(Decimal(str(amount_input_token)) * (Decimal('10') ** input_decimals)))
        except Exception as e:
            logger.error(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] Error converting amount '{amount_input_token}' to lamports for {input_token_address} with {input_decimals} decimals: {e}", exc_info=True)
            return TradeResult(status=TradeStatus.FAILED, error_message=f"Lamport conversion error for {input_token_address} (amount: {amount_input_token}, decimals: {input_decimals})", trade_record_id=trade_record_id, executed_at=datetime.now(timezone.utc))

        cmd = [
            "node",
            NODE_SCRIPT_PATH,
            wallet_private_key_b58,
            input_token_address,
            output_token_address,
            in_amount_lamports_str,
            str(current_slippage), # Pass current slippage
            str(current_priority_fee), # Pass current priority fee (as string)
            self.gmgn_api_host,
            wallet_address
        ]

        logger.debug(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] Executing Node.js script with command: {' '.join(cmd)}")

        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()

            if stderr:
                logger.info(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] Node.js script stderr:\n{stderr.decode().strip()}")
                analysis_result = _analyze_node_stderr_output(stderr.decode(), trade_record_id, attempt_number)
            else:
                logger.debug(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] Node.js script stderr: (empty)")

            if process.returncode != 0:
                err_msg = f"Node.js script execution failed with return code {process.returncode}. See stderr for details."
                logger.error(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] {err_msg}")
                try:
                    node_output_on_error = json.loads(stdout.decode())
                except json.JSONDecodeError:
                    node_output_on_error = {"message": "Stderr might contain more info, stdout was not valid JSON."}
                return TradeResult(status=TradeStatus.FAILED, error_message=err_msg, provider_response_raw=node_output_on_error, trade_record_id=trade_record_id)

            try:
                node_result = json.loads(stdout.decode())
                logger.debug(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] Node.js script stdout (parsed JSON): {node_result}")
            except json.JSONDecodeError as e:
                err_msg = f"Failed to parse JSON output from Node.js script: {e}. Raw stdout: {stdout.decode()[:500]}..."
                logger.error(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] {err_msg}")
                
                # 分析JSON解析错误，这可能表明GMGN API返回了HTML而不是JSON
                stdout_content = stdout.decode()
                json_error_analysis = _analyze_error_message_characteristics(str(e))
                stdout_analysis = _analyze_error_message_characteristics(stdout_content[:1000])  # 分析前1000字符
                
                logger.info(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] JSON parse error analysis: "
                           f"Error_has_HTML={json_error_analysis['has_html_chars']}, "
                           f"Stdout_has_HTML={stdout_analysis['has_html_chars']}, "
                           f"Stdout_has_DOCTYPE={stdout_analysis['has_doctype']}, "
                           f"Stdout_length={len(stdout_content)}")
                
                if stdout_analysis['has_doctype'] or stdout_analysis['has_html_tags']:
                    logger.warning(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] DETECTED: Node.js script stdout contains HTML content instead of JSON!")
                    logger.warning(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] HTML content preview: {stdout_content[:200]}")
                
                return TradeResult(status=TradeStatus.FAILED, error_message=err_msg, provider_response_raw={"raw_stdout": stdout.decode()}, trade_record_id=trade_record_id)

            script_status = node_result.get("status")
            tx_hash = node_result.get("txHash")
            message = node_result.get("message")
            provider_responses = node_result.copy()

            actual_in_from_onchain, actual_out_from_onchain = None, None
            onchain_fetch_status = provider_responses.get("onchain_data_fetch_status")

            if onchain_fetch_status == "success":
                try:
                    raw_onchain_in_lamports = provider_responses.get("onchain_actual_amount_in_lamports")
                    raw_onchain_out_lamports = provider_responses.get("onchain_actual_amount_out_lamports")
                    if raw_onchain_in_lamports is not None: actual_in_from_onchain = Decimal(str(raw_onchain_in_lamports))
                    if raw_onchain_out_lamports is not None: actual_out_from_onchain = Decimal(str(raw_onchain_out_lamports))
                    if actual_in_from_onchain is not None and actual_out_from_onchain is not None:
                         logger.info(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] Successfully parsed onchain actual amounts: IN={actual_in_from_onchain}, OUT={actual_out_from_onchain}")
                    else:
                        logger.warning(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] Onchain data fetch reported success by script, but actual onchain amounts are missing/null. Will attempt fallback.")
                        onchain_fetch_status = "partial_data" 
                except (ValueError, TypeError) as e: 
                    logger.warning(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] Error parsing onchain actual amounts from script: {e}. Will attempt fallback.")
                    onchain_fetch_status = "parsing_failed"

            actual_in_from_quote, actual_out_from_quote = None, None
            quote_data_block = provider_responses.get("quoteResponse", {}).get("data", {})
            quote_data = quote_data_block.get("quote", {}) if quote_data_block else {}
            
            if quote_data:
                raw_quote_in_str = quote_data.get("inAmount")
                raw_quote_out_str = quote_data.get("outAmount")
                try:
                    if raw_quote_in_str: actual_in_from_quote = Decimal(raw_quote_in_str)
                    if raw_quote_out_str: actual_out_from_quote = Decimal(raw_quote_out_str)
                except Exception as e:
                    logger.warning(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] Could not parse actual amounts from quote data: {e}")

            final_actual_in, final_actual_out = None, None
            if actual_in_from_onchain is not None and actual_out_from_onchain is not None and onchain_fetch_status == "success":
                final_actual_in, final_actual_out = actual_in_from_onchain, actual_out_from_onchain
                logger.info(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] Using onchain data for actual amounts.")
            elif actual_in_from_quote is not None and actual_out_from_quote is not None:
                final_actual_in, final_actual_out = actual_in_from_quote, actual_out_from_quote
                logger.warning(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] Failed to get onchain trade details or data incomplete (status: {onchain_fetch_status}), falling back to quote amounts.")
            else:
                logger.warning(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] Could not determine actual amounts from onchain (status: {onchain_fetch_status}) or quote. Amounts will be None.")

            if script_status == "success":
                logger.info(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] Node.js script reported SUCCESS. TxHash: {tx_hash}")
                return TradeResult(
                    status=TradeStatus.SUCCESS, tx_hash=tx_hash, executed_at=datetime.now(timezone.utc),
                    actual_amount_in=final_actual_in, actual_amount_out=final_actual_out,
                    provider_response_raw=provider_responses, trade_record_id=trade_record_id
                )
            elif script_status == "submitted_no_poll":
                logger.warning(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] Node.js script reported SUBMITTED_NO_POLL. TxHash: {tx_hash}. Message: {message}")
                return TradeResult(
                    status=TradeStatus.PENDING, tx_hash=tx_hash, error_message=message or "Transaction submitted, polling skipped.",
                    provider_response_raw=provider_responses, trade_record_id=trade_record_id
                )
            else: 
                err_msg_from_script = message or "Node.js script reported an error."
                logger.error(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] Node.js script reported FAILURE/UNKNOWN. Status: '{script_status}'. Message: {err_msg_from_script}")
                
                # 分析错误消息特征以帮助诊断问题
                if err_msg_from_script and err_msg_from_script != "Node.js script reported an error.":
                    error_analysis = _analyze_error_message_characteristics(err_msg_from_script)
                    logger.info(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] Error message analysis: "
                               f"Length={error_analysis['message_length']}, "
                               f"HTML_chars={error_analysis['has_html_chars']} (count={error_analysis['special_char_count']}), "
                               f"HTML_tags={error_analysis['has_html_tags']}, "
                               f"DOCTYPE={error_analysis['has_doctype']}, "
                               f"JSON_parse_error={error_analysis['has_json_parse_error']}")
                    
                    if error_analysis['has_json_parse_error'] and error_analysis['has_html_chars']:
                        logger.warning(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] DETECTED: JSON parse error with HTML characters - likely GMGN API returned HTML instead of JSON!")
                        logger.warning(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] Error preview: {error_analysis['first_100_chars']}")
                
                return TradeResult(
                    status=TradeStatus.FAILED, tx_hash=tx_hash, error_message=err_msg_from_script,
                    provider_response_raw=provider_responses, trade_record_id=trade_record_id
                )

        except FileNotFoundError:
            err_msg = f"Node.js script not found at {NODE_SCRIPT_PATH}."
            logger.error(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] {err_msg} \n {traceback.format_exc()}")
            return TradeResult(status=TradeStatus.FAILED, error_message=err_msg, trade_record_id=trade_record_id)
        except Exception as e:
            err_msg = f"Unexpected error during Node.js script execution/processing: {e}"
            logger.error(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] {err_msg}", exc_info=True)
            return TradeResult(status=TradeStatus.FAILED, error_message=err_msg, trade_record_id=trade_record_id)

    async def execute_trade(
        self,
        trade_type: TradeType,
        input_token_address: str,
        output_token_address: str,
        amount_input_token: float,
        wallet_private_key_b58: str,
        wallet_address: str, 
        strategy_snapshot: Dict[str, Any],
        signal_id: PydanticObjectId, # Keep for potential future use or logging context
        trade_record_id: PydanticObjectId
    ) -> TradeResult:
        
        if trade_type == TradeType.SELL:
            logger.info(f"[TradeRec:{trade_record_id}] Initiating SELL trade with retry logic for {amount_input_token} {input_token_address} -> {output_token_address}")

            initial_slippage_pct = strategy_snapshot.get('gmgn_sell_slippage_percentage', 1.0) # Default 1%
            try:
                initial_priority_fee_sol_str = str(strategy_snapshot.get('gmgn_sell_priority_fee', 0.00005)) # Default 0.00005 SOL
                initial_priority_fee_decimal = Decimal(initial_priority_fee_sol_str)
            except Exception as e:
                logger.error(f"[TradeRec:{trade_record_id}] Invalid initial sell priority fee format in strategy: {strategy_snapshot.get('gmgn_sell_priority_fee')}. Error: {e}. Using default 0.00005.")
                initial_priority_fee_decimal = Decimal("0.00005")

            current_slippage = float(initial_slippage_pct)
            current_priority_fee = initial_priority_fee_decimal
            
            max_attempts = 10
            attempts_per_group = 2
            num_groups = 5 
            retry_delay_seconds = 5
            last_trade_result: Optional[TradeResult] = None

            for attempt_num in range(1, max_attempts + 1):
                # Determine group index (1-based)
                group_index = (attempt_num - 1) // attempts_per_group + 1

                if attempt_num > 1 and (attempt_num - 1) % attempts_per_group == 0: # Start of a new group (after 1st group)
                    # Increment slippage for the new group
                    current_slippage = float(initial_slippage_pct) + (group_index - 1) * 1.0
                    # Increment priority fee for the new group
                    current_priority_fee = _calculate_next_priority_fee(current_priority_fee)
                    logger.info(f"[TradeRec:{trade_record_id}] New retry group {group_index}. Adjusted Slippage: {current_slippage}%, Priority Fee: {current_priority_fee} SOL")

                trade_result = await self._execute_single_trade_attempt(
                    input_token_address=input_token_address,
                    output_token_address=output_token_address,
                    amount_input_token=amount_input_token,
                    wallet_private_key_b58=wallet_private_key_b58,
                    wallet_address=wallet_address,
                    current_slippage=current_slippage,
                    current_priority_fee=current_priority_fee,
                    strategy_snapshot=strategy_snapshot,
                    trade_record_id=trade_record_id,
                    attempt_number=attempt_num
                )
                last_trade_result = trade_result

                if trade_result.status == TradeStatus.SUCCESS:
                    logger.info(f"[TradeRec:{trade_record_id}] Sell trade successful on attempt {attempt_num}.")
                    return trade_result
                
                if attempt_num < max_attempts:
                    logger.info(f"[TradeRec:{trade_record_id}] Sell trade attempt {attempt_num} failed. Status: {trade_result.status}. Error: {trade_result.error_message}. Retrying in {retry_delay_seconds}s...")
                    await asyncio.sleep(retry_delay_seconds)
                else:
                    logger.error(f"[TradeRec:{trade_record_id}] All {max_attempts} sell trade attempts failed. Last error: {trade_result.error_message}")
            
            return last_trade_result # Should be the result of the 10th attempt if all failed

        else: # BUY trade or other types - no custom retry logic here, direct execution
            logger.info(f"[TradeRec:{trade_record_id}] Executing non-SELL trade ({trade_type.value}) directly: {amount_input_token} {input_token_address} -> {output_token_address}")
            
            # For non-SELL, use original slippage/priority fee from strategy
            slippage_key = 'gmgn_buy_slippage_percentage' if trade_type == TradeType.BUY else 'gmgn_sell_slippage_percentage' # Fallback for other types
            priority_fee_key = 'gmgn_buy_priority_fee' if trade_type == TradeType.BUY else 'gmgn_sell_priority_fee'
        
            # Use Decimal for priority fee calculation consistency, even if direct
            try:
                direct_priority_fee_str = str(strategy_snapshot.get(priority_fee_key, 0.00005))
                direct_priority_fee_decimal = Decimal(direct_priority_fee_str)
            except Exception as e:
                logger.warning(f"[TradeRec:{trade_record_id}] Invalid direct priority fee format for {trade_type.value}: {strategy_snapshot.get(priority_fee_key)}. Error: {e}. Using default 0.00005.")
                direct_priority_fee_decimal = Decimal("0.00005")

            direct_slippage = float(strategy_snapshot.get(slippage_key, 0.5))

            return await self._execute_single_trade_attempt(
                input_token_address=input_token_address,
                output_token_address=output_token_address,
                amount_input_token=amount_input_token,
                wallet_private_key_b58=wallet_private_key_b58,
                wallet_address=wallet_address,
                current_slippage=direct_slippage,
                current_priority_fee=direct_priority_fee_decimal,
                strategy_snapshot=strategy_snapshot,
                trade_record_id=trade_record_id,
                attempt_number=1 # Single attempt for non-SELL
            )

    def is_slippage_related_error(
        self, 
        error_message: Optional[str], 
        provider_response: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        判断错误是否与滑点相关 - GMGN API特定实现
        
        Args:
            error_message: 错误信息文本
            provider_response: GMGN API原始响应（可选）
            
        Returns:
            bool: 是否为滑点相关错误
        """
        # GMGN API特定的滑点相关错误关键词
        gmgn_slippage_keywords = [
            # 通用滑点错误
            "slippage",
            "price impact",
            "insufficient output amount",
            "would result in",
            "minimum received",
            "exceeds maximum slippage",
            "price moved too much",
            
            # GMGN API特定错误
            "route failed",
            "swap failed",
            "price change",
            "market impact",
            "liquidity insufficient",
            "output amount too low",
            "input amount too high",
            
            # Jupiter/Solana DEX相关（GMGN可能使用）
            "jupiter",
            "swap route",
            "quote expired",
            "price tolerance",
            "amount out minimum",
            
            # 交易失败相关（可能与滑点有关）
            "transaction failed",
            "simulation failed",
            "insufficient liquidity"
        ]
        
        # 检查错误消息中的关键词
        if error_message:
            error_lower = error_message.lower()
            for keyword in gmgn_slippage_keywords:
                if keyword in error_lower:
                    logger.debug(f"GMGN滑点错误检测: 发现关键词 '{keyword}' in '{error_message}'")
                    return True
        
        # 检查provider_response中的特定错误码或状态
        if provider_response:
            # 检查GMGN API响应中的错误信息
            if isinstance(provider_response, dict):
                # 递归检查所有字段，包括嵌套的字典
                def check_nested_dict(data, path=""):
                    if isinstance(data, dict):
                        for key, value in data.items():
                            current_path = f"{path}.{key}" if path else key
                            if isinstance(value, str):
                                value_lower = value.lower()
                                for keyword in gmgn_slippage_keywords:
                                    if keyword in value_lower:
                                        logger.debug(f"GMGN滑点错误检测: 在响应字段 '{current_path}' 中发现关键词 '{keyword}'")
                                        return True
                            elif isinstance(value, dict):
                                if check_nested_dict(value, current_path):
                                    return True
                    return False
                
                if check_nested_dict(provider_response):
                    return True
                
                # 检查特定的错误码（如果GMGN API有标准化的错误码）
                error_code = provider_response.get('error_code') or provider_response.get('code')
                if error_code:
                    # 假设的GMGN API滑点相关错误码（需要根据实际API文档调整）
                    slippage_error_codes = ['SLIPPAGE_EXCEEDED', 'PRICE_IMPACT_TOO_HIGH', 'INSUFFICIENT_OUTPUT']
                    if str(error_code) in slippage_error_codes:
                        logger.debug(f"GMGN滑点错误检测: 发现滑点相关错误码 '{error_code}'")
                        return True
        
        return False
    
    def is_non_retryable_error(
        self, 
        error_message: Optional[str], 
        provider_response: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        判断错误是否为不可重试错误 - GMGN API特定实现
        
        Args:
            error_message: 错误信息文本
            provider_response: GMGN API原始响应（可选）
            
        Returns:
            bool: 是否为不可重试错误
        """
        # 先调用父类的通用实现
        if super().is_non_retryable_error(error_message, provider_response):
            return True
        
        # GMGN API特定的不可重试错误关键词
        gmgn_non_retryable_keywords = [
            # 钱包/账户相关
            "invalid wallet",
            "wallet not found",
            "private key invalid",
            "signature failed",
            
            # 代币相关
            "token not supported",
            "invalid token address",
            "token not found",
            "mint not found",
            
            # 权限相关
            "api key invalid",
            "unauthorized access",
            
            # 配置错误
            "invalid parameters",
            "malformed request",
            "invalid amount",
            "amount too small",
            "amount too large"
        ]
        
        # 检查错误消息中的关键词
        if error_message:
            error_lower = error_message.lower()
            for keyword in gmgn_non_retryable_keywords:
                if keyword in error_lower:
                    logger.debug(f"GMGN不可重试错误检测: 发现关键词 '{keyword}' in '{error_message}'")
                    return True
        
        # 检查provider_response中的特定错误码和错误信息
        if provider_response and isinstance(provider_response, dict):
            # 递归检查所有字段，包括嵌套的字典
            def check_nested_dict(data, path=""):
                if isinstance(data, dict):
                    for key, value in data.items():
                        current_path = f"{path}.{key}" if path else key
                        if isinstance(value, str):
                            value_lower = value.lower()
                            # 检查字符串值中的关键词
                            for keyword in gmgn_non_retryable_keywords:
                                if keyword in value_lower:
                                    logger.debug(f"GMGN不可重试错误检测: 在响应字段 '{current_path}' 中发现关键词 '{keyword}'")
                                    return True
                            
                            # 检查是否是错误码字段
                            if key.lower() in ['error', 'error_code', 'errorcode', 'code']:
                                non_retryable_error_codes = [
                                    'INVALID_TOKEN', 'WALLET_ERROR', 'AUTH_FAILED', 'AUTHENTICATION_FAILED',
                                    'INVALID_PARAMS', 'QUOTA_EXCEEDED', 'INSUFFICIENT_FUNDS', 'UNAUTHORIZED'
                                ]
                                if value.upper() in non_retryable_error_codes:
                                    logger.debug(f"GMGN不可重试错误检测: 在响应字段 '{current_path}' 中发现不可重试错误码 '{value}'")
                                    return True
                        elif isinstance(value, dict):
                            if check_nested_dict(value, current_path):
                                return True
                return False
            
            if check_nested_dict(provider_response):
                return True
            
            # 检查特定的错误码
            error_code = provider_response.get('error_code') or provider_response.get('code') or provider_response.get('error')
            if error_code:
                # GMGN API特定的不可重试错误码
                non_retryable_error_codes = [
                    'INVALID_TOKEN', 'WALLET_ERROR', 'AUTH_FAILED', 'AUTHENTICATION_FAILED',
                    'INVALID_PARAMS', 'QUOTA_EXCEEDED', 'INSUFFICIENT_FUNDS', 'UNAUTHORIZED'
                ]
                if str(error_code) in non_retryable_error_codes:
                    logger.debug(f"GMGN不可重试错误检测: 发现不可重试错误码 '{error_code}'")
                    return True
        
        return False

    async def close(self):
        """关闭服务。在此实现中，如果 Node.js 脚本是长时间运行的，可能需要处理关闭，但当前它是按需执行的。"""
        logger.info("GmgnTradeService (Node.js wrapper) closed.")
        # 如果之前有 http_client，则关闭: await self.http_client.aclose()
        pass

if __name__ == "__main__":
    # ... (if __name__ == "__main__" block remains largely the same, 
    #      but it will now test the Node.js script execution flow)
    # ...
    import asyncio
    import os
    import logging
    from beanie import PydanticObjectId # 确保导入
    from .trade_interface import TradeType # 确保导入
    load_dotenv()

    # 配置基本的日志记录，以便看到服务内部的日志输出
    logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    logger_main = logging.getLogger(__name__)

    async def run_test():
        """执行GmgnTradeService的测试。"""
        gmgn_api_host = os.getenv("GMGN_API_HOST", "https://gmgn.ai")
        wallet_private_key_b58 = os.getenv("WALLET_PRIVATE_KEY_B58")
        wallet_address = os.getenv("WALLET_ADDRESS")
        output_token_address = os.getenv("OUTPUT_TOKEN_ADDRESS", "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v") # Default to USDC mainnet for testing

        if not all([wallet_private_key_b58, wallet_address, output_token_address]):
            logger_main.error("Missing required environment variables for testing: WALLET_PRIVATE_KEY_B58, WALLET_ADDRESS, OUTPUT_TOKEN_ADDRESS")
            return

        trade_service = GmgnTradeService(gmgn_api_host=gmgn_api_host)

        trade_type = TradeType.BUY
        input_token_address = SOL_MINT_ADDRESS
        amount_input_token = 0.0001 # Small amount of SOL for testing
        
        strategy_snapshot = {
            "gmgn_buy_slippage_percentage": 0.5,
            "gmgn_buy_priority_fee": 0.00005,
            # sell params not used for BUY test but good to have if type changes
            "gmgn_sell_slippage_percentage": 0.5, 
            "gmgn_sell_priority_fee": 0.00005,
        }
        
        signal_id = PydanticObjectId()
        trade_record_id = PydanticObjectId()

        logger_main.info(f"Starting {trade_type.value} trade test via Node.js script: {amount_input_token} {input_token_address} -> {output_token_address}")
        logger_main.info(f"Wallet Address: {wallet_address}")
        logger_main.info(f"GMGN API Host: {gmgn_api_host}")

        try:
            result = await trade_service.execute_trade(
                trade_type=trade_type,
                input_token_address=input_token_address,
                output_token_address=output_token_address,
                amount_input_token=amount_input_token,
                wallet_private_key_b58=wallet_private_key_b58,
                wallet_address=wallet_address,
                strategy_snapshot=strategy_snapshot,
                signal_id=signal_id,
                trade_record_id=trade_record_id
            )
            logger_main.info(f"Trade test finished. Result: {result}")
            if result.status == TradeStatus.SUCCESS:
                logger_main.info(f"  TX Hash: {result.tx_hash}")
                logger_main.info(f"  Actual In (lamports): {result.actual_amount_in}")
                logger_main.info(f"  Actual Out (lamports): {result.actual_amount_out}")
            elif result.error_message:
                logger_main.error(f"  Error: {result.error_message}")

        except Exception as e:
            logger_main.error(f"Exception during trade test: {e}", exc_info=True)
        finally:
            await trade_service.close()
            logger_main.info("Trade test execution complete.")

    asyncio.run(run_test())
