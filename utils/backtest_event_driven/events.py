from enum import Enum
from typing import Optional, Dict, Any, Union
from datetime import datetime


class EventType(Enum):
    """事件类型枚举"""
    TRANSACTION = 1   # KOL交易事件
    SIGNAL = 2        # 信号事件
    ORDER = 3         # 订单事件
    FILL = 4          # 成交事件
    PRICE_UNAVAILABLE = 5  # 价格不可用事件


class Event:
    """事件基类"""
    _sequence_counter = 0

    @classmethod
    def get_next_sequence_id(cls) -> int:
        """获取下一个唯一的序列号"""
        cls._sequence_counter += 1
        return cls._sequence_counter

    def __init__(self, event_type: EventType, timestamp: int):
        """初始化事件
        
        Args:
            event_type: 事件类型
            timestamp: 事件发生的时间戳
        """
        self.event_type = event_type
        self.timestamp = timestamp
        self.sequence_id = Event.get_next_sequence_id()  # 分配唯一序列号
        
    def __lt__(self, other):
        """比较事件的时间先后，用于事件队列排序。时间戳相同则比较序列号。"""
        if self.timestamp != other.timestamp:
            return self.timestamp < other.timestamp
        return self.sequence_id < other.sequence_id

    def __eq__(self, other):
        """比较事件是否相等（用于测试或特定场景，主要依据仍是时间戳和序列号）"""
        if not isinstance(other, Event):
            return NotImplemented
        return (self.timestamp == other.timestamp and 
                self.sequence_id == other.sequence_id and 
                self.event_type == other.event_type)


class TransactionEvent(Event):
    """KOL交易事件"""
    
    def __init__(self, timestamp: int, transaction_data: Dict[str, Any]):
        """初始化KOL交易事件
        
        Args:
            timestamp: 事件发生的时间戳
            transaction_data: 交易数据，包含代币、金额、交易类型等信息
        """
        super().__init__(EventType.TRANSACTION, timestamp)
        self.transaction_data = transaction_data
        
    def __str__(self):
        return (f"TransactionEvent(time={datetime.fromtimestamp(self.timestamp)}, "
                f"token={self.transaction_data.get('token', {}).get('symbol', 'Unknown')}, "
                f"wallet={self.transaction_data.get('wallet_address', 'Unknown')}, "
                f"amount=${self.transaction_data.get('usd_amount', 0):.2f})")


class SignalType(Enum):
    """信号类型枚举"""
    BUY = 1
    SELL = 2


class SignalEvent(Event):
    """交易信号事件"""
    
    def __init__(self, signal_type: SignalType, timestamp: int, token_address: str, 
                 token_info: Optional[Dict[str, Any]] = None):
        """初始化交易信号事件
        
        Args:
            signal_type: 信号类型 (买入或卖出)
            timestamp: 事件发生的时间戳
            token_address: 代币地址
            token_info: 代币详细信息 (可选)
        """
        super().__init__(EventType.SIGNAL, timestamp)
        self.signal_type = signal_type
        self.token_address = token_address
        self.token_info = token_info
        
    def __str__(self):
        signal_str = "BUY" if self.signal_type == SignalType.BUY else "SELL"
        token_symbol = self.token_info.get('symbol', 'Unknown') if self.token_info else 'Unknown'
        return (f"SignalEvent({signal_str}, time={datetime.fromtimestamp(self.timestamp)}, "
                f"token={token_symbol}, address={self.token_address})")


class OrderEvent(Event):
    """订单事件"""
    
    def __init__(self, signal_type: SignalType, timestamp: int, token_address: str, 
                 amount: float, token_info: Optional[Dict[str, Any]] = None):
        """初始化订单事件
        
        Args:
            signal_type: 订单类型 (买入或卖出)
            timestamp: 事件发生的时间戳
            token_address: 代币地址
            amount: 对于买入订单，表示要花费的美元金额；对于卖出订单，表示要卖出的代币数量
            token_info: 代币详细信息 (可选)
        """
        super().__init__(EventType.ORDER, timestamp)
        self.signal_type = signal_type
        self.token_address = token_address
        self.amount = amount
        self.token_info = token_info
        
    def __str__(self):
        order_str = "BUY" if self.signal_type == SignalType.BUY else "SELL"
        token_symbol = self.token_info.get('symbol', 'Unknown') if self.token_info else 'Unknown'
        # 对于买入订单，显示美元金额；对于卖出订单，显示代币数量
        if self.signal_type == SignalType.BUY:
            return (f"OrderEvent({order_str}, time={datetime.fromtimestamp(self.timestamp)}, "
                   f"token={token_symbol}, amount=${self.amount:.2f})")
        else:
            return (f"OrderEvent({order_str}, time={datetime.fromtimestamp(self.timestamp)}, "
                   f"token={token_symbol}, amount={self.amount})")


class FillEvent(Event):
    """成交事件"""
    
    def __init__(self, signal_type: SignalType, timestamp: int, token_address: str, 
                 amount: float, price: float, commission: float = 0.0, 
                 token_info: Optional[Dict[str, Any]] = None):
        """初始化成交事件
        
        Args:
            signal_type: 成交类型 (买入或卖出)
            timestamp: 事件发生的时间戳
            token_address: 代币地址
            amount: 成交数量 (代币单位)
            price: 成交价格 (USD/代币)
            commission: 手续费 (USD)
            token_info: 代币详细信息 (可选)
        """
        super().__init__(EventType.FILL, timestamp)
        self.signal_type = signal_type
        self.token_address = token_address
        self.amount = amount
        self.price = price
        self.commission = commission
        self.token_info = token_info
        
    def __str__(self):
        fill_str = "BUY" if self.signal_type == SignalType.BUY else "SELL"
        token_symbol = self.token_info.get('symbol', 'Unknown') if self.token_info else 'Unknown'
        return (f"FillEvent({fill_str}, time={datetime.fromtimestamp(self.timestamp)}, "
                f"token={token_symbol}, amount={self.amount}, price=${self.price:.4f}, "
                f"total=${self.amount * self.price:.2f}, commission=${self.commission:.2f})")


class PriceUnavailableEvent(Event):
    """价格不可用事件，记录因无法获取价格而跳过的交易"""
    
    def __init__(self, signal_type: SignalType, timestamp: int, token_address: str, 
                 amount: float, token_info: Optional[Dict[str, Any]] = None,
                 reason: str = "价格数据不可用"):
        """初始化价格不可用事件
        
        Args:
            signal_type: 信号类型 (买入或卖出)
            timestamp: 事件发生的时间戳
            token_address: 代币地址
            amount: 对于买入订单，表示要花费的美元金额；对于卖出订单，表示要卖出的代币数量
            token_info: 代币详细信息 (可选)
            reason: 价格不可用的原因
        """
        super().__init__(EventType.PRICE_UNAVAILABLE, timestamp)
        self.signal_type = signal_type
        self.token_address = token_address
        self.amount = amount
        self.token_info = token_info
        self.reason = reason
        
    def __str__(self):
        signal_str = "BUY" if self.signal_type == SignalType.BUY else "SELL"
        token_symbol = self.token_info.get('symbol', 'Unknown') if self.token_info else 'Unknown'
        return (f"PriceUnavailableEvent({signal_str}, time={datetime.fromtimestamp(self.timestamp)}, "
               f"token={token_symbol}, address={self.token_address}, "
               f"reason='{self.reason}')") 