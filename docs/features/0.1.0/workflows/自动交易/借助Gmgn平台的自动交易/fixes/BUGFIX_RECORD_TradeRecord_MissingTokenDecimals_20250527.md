# TradeRecord缺少token_out_decimals字段Bug修复记录

## Bug信息
**Bug ID**: TradeRecord_MissingTokenDecimals  
**发现日期**: 2025-05-27T10:56:18+08:00  
**修复完成日期**: 2025-05-27T10:59:20+08:00  
**修复者**: AI Assistant  
**审阅者**: 用户  

## Bug描述
在 `workflows/monitor_kol_activity/sell_signal_handler.py` 第374行，代码尝试访问 `original_buy_trade_record.token_out_decimals` 字段来获取代币的小数位数信息，但是 `models/trade_record.py` 中的 `TradeRecord` 类没有定义这个字段，导致 `AttributeError: 'TradeRecord' object has no attribute 'token_out_decimals'` 错误。

## 根本原因
模型定义与代码使用不一致：
- `TradeRecord` 模型缺少 `token_out_decimals` 和 `token_in_decimals` 字段
- 但业务代码中需要访问这些字段来获取代币的小数位数信息

## 修复方案
在 `TradeRecord` 模型中添加缺失的字段，保持向后兼容性。

## 修复内容

### 代码修改
**文件**: `models/trade_record.py`  
**位置**: TradeRecord类字段定义部分  

**修改前**:
```python
token_out_address: str
token_out_amount_expected: Optional[float] = Field(default=None, description="Amount expected to receive")
token_out_actual_amount: Optional[float] = Field(default=None, description="Amount actually received")

wallet_address: str                     # The wallet executing the trade
```

**修改后**:
```python
token_out_address: str
token_out_amount_expected: Optional[float] = Field(default=None, description="Amount expected to receive")
token_out_actual_amount: Optional[float] = Field(default=None, description="Amount actually received")
token_out_decimals: Optional[int] = Field(default=None, description="Decimals of the output token")
token_in_decimals: Optional[int] = Field(default=None, description="Decimals of the input token")

wallet_address: str                     # The wallet executing the trade
```

### 测试用例
**文件**: `test/models/test_trade_record_decimals_bug.py`  
**内容**: 
- 验证字段存在性测试
- 验证字段类型和默认值测试
- 向后兼容性测试

## 验证结果

### 测试执行结果
```bash
# 修复后的测试结果
test/models/test_trade_record_decimals_bug.py::TestTradeRecordDecimalsBug::test_trade_record_missing_token_out_decimals_bug_was_fixed PASSED
test/models/test_trade_record_decimals_bug.py::TestTradeRecordDecimalsBug::test_trade_record_with_decimals_fields_after_fix PASSED

# 回归测试结果
test/models/test_trade_record.py - 7个测试全部通过
```

### 修复验证
- ✅ `token_out_decimals` 字段已成功添加
- ✅ `token_in_decimals` 字段已成功添加  
- ✅ 字段类型为 `Optional[int]`，默认值为 `None`
- ✅ 向后兼容性保持，现有代码不受影响
- ✅ 所有现有测试通过，无回归问题

## 影响评估
- **数据库**: 新字段为可选，不影响现有数据
- **API**: 不影响现有API接口
- **性能**: 无性能影响
- **向后兼容**: 完全向后兼容

## 简要记录
修复了TradeRecord模型缺少token_out_decimals和token_in_decimals字段的Bug。原因是模型定义与业务代码使用不一致。通过在`models/trade_record.py`中添加两个可选的int类型字段解决了此问题。修复后，`workflows/monitor_kol_activity/sell_signal_handler.py`可以正常访问这些字段，不再抛出AttributeError。相关测试：`test_trade_record_decimals_bug.py`。

## 后续建议
1. 在交易服务中保存代币decimals信息到TradeRecord
2. 考虑在代币信息获取时缓存decimals数据
3. 添加数据验证确保decimals值的合理性（通常在0-18之间） 