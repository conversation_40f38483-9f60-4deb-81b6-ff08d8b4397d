import unittest
from unittest.mock import patch, AsyncMock, MagicMock, call, ANY
from datetime import datetime, timedelta, timezone
import time
from beanie import PydanticObjectId # Import for assertions
from typing import List, Dict, Any as TypingAny, Union # Add Union

# Import the functions to be tested
from workflows.monitor_kol_activity.handler import (
    filter_target_tokens,
    validate,
    send_message_to_channel,
    find_tokens_by_address,
    _execute_kol_buy_strategy # Import for direct patching
)
from models.signal import Signal
from models.config import Config, KolActivityConfig, SingleKolStrategyConfig, ApplicationConfig # Use Config directly for mock, Import new config models
from utils.common import check_kol_sell_ratio # Import for mocking its path
from models.trade_record import TradeRecord, TradeStatus as ModelTradeStatus, TradeType as ModelTradeType # Added
from utils.trading.solana.trade_interface import TradeResult, TradeStatus as InterfaceTradeStatus, TradeType as InterfaceTradeType # Added
from jinja2 import Template # Added

# Use IsolatedAsyncioTestCase for async functions
class TestMonitorKolActivityHandler(unittest.IsolatedAsyncioTestCase):

    def setUp(self):
        # Common mock datetime for consistent testing across methods
        from zoneinfo import ZoneInfo
        self.eastern_zone = ZoneInfo("Asia/Shanghai")
        self.mock_current_time_dt_eastern = datetime(2024, 8, 15, 12, 0, 0, tzinfo=self.eastern_zone)
        # self.MOCK_CURRENT_TIME_DT = self.mock_current_time_dt_eastern # Ensuring consistency

    def _create_mock_strategy_config(self, name, min_amount, is_active=True, notification_interval=60, kol_account_min_count=2, token_mint_lookback_hours=72, sell_kol_ratio=0.5, transaction_lookback_hours=24, sell_strategy_hours=24, kol_account_min_txs=5, kol_account_max_txs=1000,
                                     # 新的字段名（基于重构后的配置）
                                     wallet_private_key_env_var="FAKE_PK_ENV_VAR",
                                     wallet_address="FAKE_WALLET_ADDR",
                                     buy_amount_sol=0.01,
                                     buy_slippage_percentage=1.0,
                                     buy_priority_fee_sol=0.00005
                                     ):
        return SingleKolStrategyConfig(
            strategy_name=name,
            transaction_lookback_hours=transaction_lookback_hours,
            transaction_min_amount=min_amount,
            kol_account_min_count=kol_account_min_count,
            token_mint_lookback_hours=token_mint_lookback_hours,
            kol_account_min_txs=kol_account_min_txs,
            kol_account_max_txs=kol_account_max_txs,
            sell_strategy_hours=sell_strategy_hours,
            sell_kol_ratio=sell_kol_ratio,
            same_token_notification_interval=notification_interval,
            is_active=is_active,
            # 使用新的字段名
            wallet_private_key_env_var=wallet_private_key_env_var,
            wallet_address=wallet_address,
            buy_amount_sol=buy_amount_sol,
            buy_slippage_percentage=buy_slippage_percentage,
            buy_priority_fee_sol=buy_priority_fee_sol
        )

    @patch('workflows.monitor_kol_activity.handler.ConfigDAO')
    @patch('workflows.monitor_kol_activity.handler._execute_kol_buy_strategy', new_callable=AsyncMock) # Patch the core logic function
    # Removed KOLAWalletActivityDAO, TokenDAO, find_tokens_by_address mocks as _execute_kol_buy_strategy is now the boundary
    # @patch('workflows.monitor_kol_activity.handler.SignalDAO') # Not directly used by filter_target_tokens
    # @patch('workflows.monitor_kol_activity.handler.Signal') # Not directly used by filter_target_tokens
    @patch('time.time', return_value=1700000000) # Keep if _execute_kol_buy_strategy uses it internally (it does for current_time)
    async def test_filter_target_tokens_multi_strategy(
        self, mock_time, # mock_signal_cls, mock_signal_dao_cls,
        mock_execute_strategy, mock_config_dao_cls
    ):
        # --- Mock Setup ---
        mock_config_dao_instance = mock_config_dao_cls.return_value

        strategy1_config = self._create_mock_strategy_config("Aggro", 500)
        strategy2_config = self._create_mock_strategy_config("Conserve", 2000)
        strategy3_config = self._create_mock_strategy_config("Inactive", 100, is_active=False)

        mock_kol_activity_config_data = KolActivityConfig(
            buy_strategies=[strategy1_config, strategy2_config, strategy3_config]
        )
        mock_config_dao_instance.get_config = AsyncMock(return_value=MagicMock(data=mock_kol_activity_config_data))

        # Mock _execute_kol_buy_strategy side effect
        # It will be called with SingleKolStrategyConfig instances
        token_data_s1_t1 = {'address': 'token_s1_t1', 'name': 'AggroToken1', 'symbol': 'AG1', 'first_mint_time': datetime.now(timezone.utc), 'hit_kol_wallets': ['k1','k2']}
        token_data_s2_t1 = {'address': 'token_s2_t1', 'name': 'ConserveToken1', 'symbol': 'CON1', 'first_mint_time': datetime.now(timezone.utc), 'hit_kol_wallets': ['k3','k4']}
        token_data_s2_t2 = {'address': 'token_s2_t2', 'name': 'ConserveToken2', 'symbol': 'CON2', 'first_mint_time': datetime.now(timezone.utc), 'hit_kol_wallets': ['k5','k6']}


        async def execute_strategy_side_effect(strategy_params: SingleKolStrategyConfig):
            if strategy_params.strategy_name == "Aggro":
                return [token_data_s1_t1]
            elif strategy_params.strategy_name == "Conserve":
                return [token_data_s2_t1, token_data_s2_t2]
            elif strategy_params.strategy_name == "Inactive":
                return [] # Should not be called if is_active=False logic works
            return []

        mock_execute_strategy.side_effect = execute_strategy_side_effect

        # --- Execute ---
        result = await filter_target_tokens()

        # --- Assertions ---
        self.assertEqual(len(result), 3) # 1 from Aggro, 2 from Conserve

        # Check calls to _execute_kol_buy_strategy
        self.assertEqual(mock_execute_strategy.call_count, 2) # Only active strategies
        
        # Ensure correct strategy_params were passed
        # We can't directly compare Pydantic models with assert_called_with if they are different instances
        # So we check the strategy_name or other distinctive features
        
        passed_strategy_configs = [call_arg[0][0] for call_arg in mock_execute_strategy.call_args_list]
        self.assertIn(strategy1_config, passed_strategy_configs)
        self.assertIn(strategy2_config, passed_strategy_configs)
        self.assertNotIn(strategy3_config, passed_strategy_configs)


        # Check structure and content of the results
        result_token_s1_t1 = next(item for item in result if item['token_data']['address'] == 'token_s1_t1')
        result_token_s2_t1 = next(item for item in result if item['token_data']['address'] == 'token_s2_t1')
        result_token_s2_t2 = next(item for item in result if item['token_data']['address'] == 'token_s2_t2')

        self.assertDictEqual(result_token_s1_t1['token_data'], token_data_s1_t1)
        self.assertDictEqual(result_token_s1_t1['strategy_config_snapshot'], strategy1_config.model_dump())

        self.assertDictEqual(result_token_s2_t1['token_data'], token_data_s2_t1)
        self.assertDictEqual(result_token_s2_t1['strategy_config_snapshot'], strategy2_config.model_dump())
        
        self.assertDictEqual(result_token_s2_t2['token_data'], token_data_s2_t2)
        self.assertDictEqual(result_token_s2_t2['strategy_config_snapshot'], strategy2_config.model_dump())

        mock_config_dao_instance.get_config.assert_called_once_with("kol_activity")

    # test_filter_target_tokens_fetch_new_token is largely covered by the above,
    # as _execute_kol_buy_strategy is now the unit under test for token fetching details.
    # We can keep a simpler version for backward compatibility parsing if needed.

    @patch('workflows.monitor_kol_activity.handler.ConfigDAO')
    @patch('workflows.monitor_kol_activity.handler._execute_kol_buy_strategy', new_callable=AsyncMock)
    @patch('time.time', return_value=1700000000)
    async def test_filter_target_tokens_backward_compatibility_single_strategy(
        self, mock_time, mock_execute_strategy, mock_config_dao_cls
    ):
        mock_config_dao_instance = mock_config_dao_cls.return_value
        
        # Simulate an old config structure that's not KolActivityConfig but SingleKolStrategyConfig
        legacy_strategy_config_data = self._create_mock_strategy_config("LegacySingle", 1500)
        
        # Mock get_config to return this legacy structure (as if data was directly SingleKolStrategyConfig)
        mock_config_dao_instance.get_config = AsyncMock(return_value=MagicMock(data=legacy_strategy_config_data))

        token_data_legacy = {'address': 'token_legacy', 'name': 'LegacyToken', 'symbol': 'LEG', 'first_mint_time': datetime.now(timezone.utc), 'hit_kol_wallets': ['k_leg']}
        
        async def execute_strategy_side_effect(strategy_params: SingleKolStrategyConfig):
            if strategy_params.strategy_name == "LegacySingle":
                return [token_data_legacy]
            return []
        mock_execute_strategy.side_effect = execute_strategy_side_effect

        result = await filter_target_tokens()

        self.assertEqual(len(result), 1)
        mock_execute_strategy.assert_called_once()
        passed_strategy_config = mock_execute_strategy.call_args[0][0]
        
        # Compare relevant fields as Pydantic model_dump() on the MagicMock'd data inside handler.py will be different
        self.assertEqual(passed_strategy_config.strategy_name, legacy_strategy_config_data.strategy_name)
        self.assertEqual(passed_strategy_config.transaction_min_amount, legacy_strategy_config_data.transaction_min_amount)

        self.assertDictEqual(result[0]['token_data'], token_data_legacy)
        self.assertDictEqual(result[0]['strategy_config_snapshot'], legacy_strategy_config_data.model_dump())


    async def test_validate(self):
        self.assertTrue(await validate([{'key': 'value'}]))
        self.assertTrue(await validate([]))

    @patch('workflows.monitor_kol_activity.handler.TelegramUserDAO')
    @patch('workflows.monitor_kol_activity.handler.TokenMessageSendHistoryDAO')
    @patch('workflows.monitor_kol_activity.handler.SignalDAO')
    @patch('workflows.monitor_kol_activity.handler.TradeRecordDAO')
    @patch('workflows.monitor_kol_activity.handler.ConfigDAO')
    @patch('workflows.monitor_kol_activity.handler.get_auto_trade_manager', new_callable=AsyncMock)
    @patch('workflows.monitor_kol_activity.handler.os.getenv')
    @patch('workflows.monitor_kol_activity.handler.TelegramMessageSender')
    @patch('workflows.monitor_kol_activity.handler.get_current_time_dt')
    @patch('workflows.monitor_kol_activity.handler.Signal')
    @patch('workflows.monitor_kol_activity.handler.TradeRecord')
    @patch('workflows.monitor_kol_activity.handler.Template')
    async def test_send_message_to_channel_amount_formatting(
        self, mock_template_cls, mock_trade_record_cls, mock_signal_cls, mock_get_current_time_dt,
        mock_sender_cls, mock_os_getenv, mock_get_auto_trade_manager,
        mock_config_dao_cls, mock_trade_record_dao_cls, mock_signal_dao_cls,
        mock_history_dao_cls, mock_user_dao_cls
    ):
        # 1. --- Mock Setup ---
        mock_now_eastern = self.mock_current_time_dt_eastern
        mock_get_current_time_dt.return_value = mock_now_eastern

        mock_user_dao_instance = mock_user_dao_cls.return_value
        mock_users = [{'chat_id': 'user_format_test'}]
        mock_user_dao_instance.get_all_users = AsyncMock(return_value=mock_users)

        mock_history_instance = mock_history_dao_cls.return_value
        mock_history_instance.insert_one = AsyncMock()

        mock_signal_dao_instance = mock_signal_dao_cls.return_value
        mock_signal_dao_instance.collection.find_one = AsyncMock(return_value=None) # No existing signal
        mock_signal_id = PydanticObjectId()
        
        # Mock Signal instance construction and save
        mock_signal_instance = MagicMock(spec=Signal)
        mock_signal_instance.id = mock_signal_id
        mock_signal_instance.trade_record_ids = []
        mock_signal_instance.save = AsyncMock() # Mock the save method on the instance itself
        mock_signal_cls.return_value = mock_signal_instance # mock_signal_cls() will return this
        # mock_signal_dao_instance.save = AsyncMock() # No longer mocking DAO's save for this part

        # Mock TradeRecordDAO for saving the trade record
        mock_trade_record_dao_instance = mock_trade_record_dao_cls.return_value
        saved_trade_record_instance = MagicMock(spec=TradeRecord)
        saved_trade_record_instance.id = PydanticObjectId()
        
        # Side effect for TradeRecordDAO().save
        async def trade_record_save_side_effect(record_instance):
            # Simulate Beanie's save, record_instance is what's passed to save
            if not record_instance.id: # if TradeRecord was initialized without an ID
                record_instance.id = PydanticObjectId()
            # Update the mock_trade_record_cls.return_value to be this instance
            # so that subsequent operations on it (like setting status) are reflected
            # This might not be strictly necessary if mock_trade_record_cls.return_value is already the instance
            return record_instance 
        mock_trade_record_dao_instance.save = AsyncMock(side_effect=trade_record_save_side_effect)


        # Mock TradeRecord constructor to return our controlled instance
        # This instance will be the one whose attributes are updated after trade
        current_trade_record_instance = MagicMock(spec=TradeRecord)
        current_trade_record_instance.id = PydanticObjectId() # Initial ID for PENDING
        current_trade_record_instance.status = ModelTradeStatus.PENDING # Initial status
        mock_trade_record_cls.return_value = current_trade_record_instance


        # Mock ConfigDAO for admin IDs (not strictly needed but good for completeness)
        mock_config_dao_instance = mock_config_dao_cls.return_value
        mock_app_config_instance_format = ApplicationConfig(admin_telegram_chat_ids=[])
        mock_config_doc_format = MagicMock()
        mock_config_doc_format.data = mock_app_config_instance_format
        mock_config_dao_instance.get_config = AsyncMock(return_value=mock_config_doc_format)

        mock_sender_instance = mock_sender_cls.return_value
        mock_sender_instance.send_message_to_user = AsyncMock(return_value=True)

        # Import the needed classes
        from models.trade_execution import TradeExecutionResult, TradeStatus, ChannelAttemptResult
        
        # Mock AutoTradeManager
        mock_auto_trade_manager_instance = MagicMock()
        # Mock config_manager.is_enabled() to return True (auto-trade enabled)
        mock_auto_trade_manager_instance.config_manager.is_enabled = AsyncMock(return_value=True)
        
        # Create a proper ChannelAttemptResult
        successful_attempt = ChannelAttemptResult(
            channel_type="gmgn",
            attempt_number=1,
            status=TradeStatus.SUCCESS,
            tx_hash="TEST_TX_HASH_FORMATTING",
            error_message=None,
            execution_time=2.5,
            started_at=mock_now_eastern,
            completed_at=mock_now_eastern
        )
        # Simulate a successful execution result
        successful_execution_result = TradeExecutionResult(
            final_status=TradeStatus.SUCCESS,
            successful_channel="gmgn",
            final_trade_record_id=current_trade_record_instance.id,
            channel_attempts=[successful_attempt],
            total_execution_time=2.5,
            error_summary=None,
            started_at=mock_now_eastern,
            completed_at=mock_now_eastern
        )
        mock_auto_trade_manager_instance.execute_trade = AsyncMock(return_value=successful_execution_result)
        # 注意：get_auto_trade_manager 是异步函数，所以需要使用AsyncMock
        mock_get_auto_trade_manager.return_value = mock_auto_trade_manager_instance

        # Mock os.getenv for GMGN private key
        mock_os_getenv.side_effect = lambda k, d=None: "FAKE_PRIVATE_KEY_FOR_FORMAT_TEST" if k == "PK_FORMAT_TEST" else d

        # Mock Template rendering
        mock_template_instance = mock_template_cls.return_value
        mock_template_instance.render = MagicMock(return_value="Formatted Message Content")

        # 2. --- Input Data for send_message_to_channel ---
        # This strategy enables auto-trade to ensure the trade execution path is taken
        strategy_config_snapshot_dict = self._create_mock_strategy_config(
            name="FormattingTestStrategy",
            min_amount=10,
            wallet_private_key_env_var="PK_FORMAT_TEST",
            wallet_address="WALLET_FORMAT_TEST",
            buy_amount_sol=0.001 # This is the configured SOL amount
        ).model_dump()

        # Token info with decimals
        token_info_data = {
            "address": "PODCAST_MINT_ADDR",
            "name": "Podcast",
            "symbol": "PODCAST",
            "decimals": 9, # Crucial for formatting 'Amount Bought'
            "hit_kol_wallets": ['k_format1']
        }
        
        data_for_handler = [{
            'token_data': token_info_data,
            'strategy_config_snapshot': strategy_config_snapshot_dict
        }]

        # 3. --- Execute ---
        await send_message_to_channel(data_for_handler)

        # 4. --- Assertions ---
        # Ensure AutoTradeManager.execute_trade was called (because auto_trade_enabled=True)
        mock_auto_trade_manager_instance.execute_trade.assert_called_once()
        
        # Ensure Template.render was called
        mock_template_instance.render.assert_called_once()
        
        # Check the context passed to template.render()
        render_context = mock_template_instance.render.call_args[0][0]
        

        
        self.assertIn('trade_result', render_context)
        trade_result_render = render_context['trade_result']
        
        # Assert transaction hash is correctly included
        self.assertEqual(trade_result_render['tx_hash'], "TEST_TX_HASH_FORMATTING")
        
        # Assert 'Amount Spent' (actual_amount_in_ui) - should be from config
        # gmgn_auto_trade_buy_amount_sol = 0.001
        expected_amount_in_ui = "0.0010 SOL" 
        self.assertEqual(trade_result_render['actual_amount_in_ui'], expected_amount_in_ui)
        
        # Assert 'Amount Bought' (actual_amount_out_ui) - should show N/A for token amounts
        expected_amount_out_ui = "N/A PODCAST"
        self.assertEqual(trade_result_render['actual_amount_out_ui'], expected_amount_out_ui)

        # Signal should have been saved with the trade record ID
        self.assertIn(current_trade_record_instance.id, mock_signal_instance.trade_record_ids)
        # mock_signal_instance.save should be called twice: once for creation, once for adding trade_record_id
        self.assertEqual(mock_signal_instance.save.call_count, 2)

    @patch('workflows.monitor_kol_activity.handler.TelegramUserDAO')
    @patch('workflows.monitor_kol_activity.handler.TokenMessageSendHistoryDAO')
    @patch('workflows.monitor_kol_activity.handler.SignalDAO')
    @patch('workflows.monitor_kol_activity.handler.TradeRecordDAO')
    @patch('workflows.monitor_kol_activity.handler.ConfigDAO')
    @patch('workflows.monitor_kol_activity.handler.get_auto_trade_manager', new_callable=AsyncMock)
    @patch('workflows.monitor_kol_activity.handler.os.getenv')
    @patch('workflows.monitor_kol_activity.handler.TelegramMessageSender')
    @patch('workflows.monitor_kol_activity.handler.get_current_time_dt')
    @patch('workflows.monitor_kol_activity.handler.Signal')
    @patch('workflows.monitor_kol_activity.handler.TradeRecord')
    @patch('workflows.monitor_kol_activity.handler.Template')
    async def test_send_message_to_channel_auto_trade_buy_fail_and_admin_notification(
        self, mock_template_cls, mock_trade_record_cls, mock_signal_cls, mock_get_current_time_dt, 
        mock_sender_cls, mock_os_getenv, mock_get_auto_trade_manager, 
        mock_config_dao_cls, mock_trade_record_dao_cls, mock_signal_dao_cls, 
        mock_history_dao_cls, mock_user_dao_cls
    ):
        # 1. --- Mock Setup ---
        mock_now_eastern = self.mock_current_time_dt_eastern
        mock_get_current_time_dt.return_value = mock_now_eastern

        mock_user_dao_instance = mock_user_dao_cls.return_value
        mock_users = [{'chat_id': 'user_regular'}]
        mock_user_dao_instance.get_all_users = AsyncMock(return_value=mock_users)
        
        mock_history_instance = mock_history_dao_cls.return_value
        mock_history_instance.insert_one = AsyncMock()

        mock_signal_dao_instance = mock_signal_dao_cls.return_value
        mock_signal_dao_instance.collection.find_one = AsyncMock(return_value=None)
        mock_signal_id = PydanticObjectId()
        mock_signal_dao_instance.insert_signals = AsyncMock(return_value=MagicMock(inserted_ids=[mock_signal_id]))
        mock_signal_dao_instance.save = AsyncMock()

        mock_trade_record_dao_instance = mock_trade_record_dao_cls.return_value
        saved_records_states_and_errors = []
        async def save_side_effect(record_to_save):
            saved_records_states_and_errors.append({
                "status": record_to_save.status,
                "error_message": getattr(record_to_save, 'error_message', None)
            })
            # Simulate Beanie's save which might set/return an ID or the object itself
            if not record_to_save.id:
                record_to_save.id = PydanticObjectId() # Simulate ID generation if not present
            return record_to_save
        mock_trade_record_dao_instance.save = AsyncMock(side_effect=save_side_effect)

        mock_config_dao_instance = mock_config_dao_cls.return_value
        # Admin IDs are configured for this test
        admin_ids = ["admin_chat_1", "admin_chat_2"]
        # Corrected mock for ApplicationConfig
        mock_app_config_instance = ApplicationConfig(admin_telegram_chat_ids=admin_ids) 
        mock_config_doc = MagicMock()
        mock_config_doc.name = "application_config" 
        mock_config_doc.type = "application_config" # Match the type used in ConfigDAO
        mock_config_doc.data = mock_app_config_instance # Assign the instance
        mock_config_dao_instance.get_config = AsyncMock(return_value=mock_config_doc)

        mock_sender_instance = mock_sender_cls.return_value
        mock_sender_instance.send_message_to_user = AsyncMock(return_value=True) # Assume sending to user and admin is successful

        # Import the needed classes
        from models.trade_execution import TradeExecutionResult, TradeStatus
        
        # Mock AutoTradeManager
        mock_auto_trade_manager_instance = MagicMock()
        # Mock config_manager.is_enabled() to return True (auto-trade enabled)
        mock_auto_trade_manager_instance.config_manager.is_enabled = AsyncMock(return_value=True)
        
        # Simulate a failed execution result
        failed_execution_result = TradeExecutionResult(
            final_status=TradeStatus.FAILED,
            successful_channel=None,
            final_trade_record_id=None,
            channel_attempts=[],
            total_execution_time=2.5,
            error_summary="GMGN API Error: Insufficient balance",
            started_at=mock_now_eastern,
            completed_at=mock_now_eastern
        )
        mock_auto_trade_manager_instance.execute_trade = AsyncMock(return_value=failed_execution_result)
        mock_get_auto_trade_manager.return_value = mock_auto_trade_manager_instance

        getenv_map = {
            "GMGN_API_HOST": "https://mock-gmgn.ai/api",
            "FAIL_PK_ENV_VAR": "VALID_PK_FOR_FAIL_CASE",
        }
        mock_os_getenv.side_effect = lambda k, d=None: getenv_map.get(k, d)

        mock_signal_instance = MagicMock(spec=Signal)
        mock_signal_instance.id = mock_signal_id
        mock_signal_instance.trade_record_ids = []
        mock_signal_instance.save = AsyncMock()
        mock_signal_cls.return_value = mock_signal_instance
        
        mock_template_instance = mock_template_cls.return_value
        mock_template_instance.render = MagicMock(return_value="Mocked Failed Trade Message")

        # 2. --- Input Data ---
        strategy_config_dict = self._create_mock_strategy_config(
            "FailTradeStrategy", 50,
            wallet_private_key_env_var="FAIL_PK_ENV_VAR",
            wallet_address="FAIL_WALLET_ADDR",
            buy_amount_sol=0.02
        ).model_dump()

        test_data = [{
            'token_data': {"address": "TOKEN_FAIL", "name": "Fail Token", "symbol": "FAILX", "hit_kol_wallets":['k_fail']},
            'strategy_config_snapshot': strategy_config_dict
        }]
        
        await send_message_to_channel(test_data)

        # 4. --- Assertions ---
        # AutoTradeManager called
        mock_auto_trade_manager_instance.execute_trade.assert_called_once()

        # Signal created and saved
        mock_signal_instance.save.assert_called()

        # 注意：管理员通知现在由AutoTradeManager内部处理，handler只发送用户消息
        self.assertEqual(mock_sender_instance.send_message_to_user.call_count, 1) # 只有1条用户消息
        
        # Verify user message
        user_message_calls = [call for call in mock_sender_instance.send_message_to_user.call_args_list 
                             if call[0][1] == 'user_regular']
        self.assertEqual(len(user_message_calls), 1)
        self.assertEqual(user_message_calls[0][0][0], "Mocked Failed Trade Message")

        # User message template rendering
        render_call_args = mock_template_instance.render.call_args[0][0]
        self.assertEqual(render_call_args['token']['symbol'], "FAILX")
        self.assertEqual(render_call_args['trade_result']['error_message'], "GMGN API Error: Insufficient balance")

        # History record created for user message
        history_arg = mock_history_instance.insert_one.call_args[0][0]
        self.assertEqual(history_arg['chat_id'], 'user_regular')
        self.assertEqual(history_arg['status'], "sent")

    @patch('workflows.monitor_kol_activity.handler.TelegramUserDAO')
    @patch('workflows.monitor_kol_activity.handler.TokenMessageSendHistoryDAO')
    @patch('workflows.monitor_kol_activity.handler.SignalDAO')
    @patch('workflows.monitor_kol_activity.handler.TradeRecordDAO')
    @patch('workflows.monitor_kol_activity.handler.ConfigDAO')
    @patch('workflows.monitor_kol_activity.handler.get_auto_trade_manager', new_callable=AsyncMock)
    @patch('workflows.monitor_kol_activity.handler.os.getenv')
    @patch('workflows.monitor_kol_activity.handler.TelegramMessageSender')
    @patch('workflows.monitor_kol_activity.handler.get_current_time_dt')
    @patch('workflows.monitor_kol_activity.handler.Signal')
    @patch('workflows.monitor_kol_activity.handler.TradeRecord')
    @patch('workflows.monitor_kol_activity.handler.Template')
    async def test_send_message_to_channel_auto_trade_skipped_config_incomplete(
        self, mock_template_cls, mock_trade_record_cls, mock_signal_cls, mock_get_current_time_dt, 
        mock_sender_cls, mock_os_getenv, mock_get_auto_trade_manager, 
        mock_config_dao_cls, mock_trade_record_dao_cls, mock_signal_dao_cls, 
        mock_history_dao_cls, mock_user_dao_cls
    ):
        # 1. --- Mock Setup ---
        mock_now_eastern = self.mock_current_time_dt_eastern
        mock_get_current_time_dt.return_value = mock_now_eastern

        mock_user_dao_instance = mock_user_dao_cls.return_value
        mock_users = [{'chat_id': 'user_skip_config'}]
        mock_user_dao_instance.get_all_users = AsyncMock(return_value=mock_users)
        
        mock_history_instance = mock_history_dao_cls.return_value
        mock_history_instance.insert_one = AsyncMock()

        mock_signal_dao_instance = mock_signal_dao_cls.return_value
        mock_signal_dao_instance.collection.find_one = AsyncMock(return_value=None)
        mock_signal_id = PydanticObjectId()
        mock_signal_dao_instance.insert_signals = AsyncMock(return_value=MagicMock(inserted_ids=[mock_signal_id]))
        mock_signal_dao_instance.save = AsyncMock()

        mock_trade_record_dao_instance = mock_trade_record_dao_cls.return_value
        mock_trade_record_dao_instance.save = AsyncMock() # Called once for SKIPPED

        mock_config_dao_instance = mock_config_dao_cls.return_value
        mock_app_config_data = {"admin_telegram_chat_ids": []} # No admin notification for this type of skip
        mock_config_doc_skip = MagicMock()
        mock_config_doc_skip.name = "application_config"
        mock_config_doc_skip.type = "global"
        mock_config_doc_skip.data = mock_app_config_data
        mock_config_dao_instance.get_config = AsyncMock(return_value=mock_config_doc_skip)
        
        mock_sender_instance = mock_sender_cls.return_value
        mock_sender_instance.send_message_to_user = AsyncMock(return_value=True)

        # Import the needed classes
        from models.trade_execution import TradeExecutionResult, TradeStatus
        
        # Mock AutoTradeManager - should return a skipped result due to config issues
        mock_auto_trade_manager_instance = MagicMock()
        # Mock config_manager.is_enabled() to return True (auto-trade enabled)
        mock_auto_trade_manager_instance.config_manager.is_enabled = AsyncMock(return_value=True)
        
        skipped_execution_result = TradeExecutionResult(
            final_status=TradeStatus.SKIPPED,
            successful_channel=None,
            final_trade_record_id=None,
            channel_attempts=[],
            total_execution_time=0.0,
            error_summary="Configuration incomplete for auto-trade",
            started_at=mock_now_eastern,
            completed_at=mock_now_eastern
        )
        mock_auto_trade_manager_instance.execute_trade = AsyncMock(return_value=skipped_execution_result)
        mock_get_auto_trade_manager.return_value = mock_auto_trade_manager_instance

        mock_signal_instance = MagicMock(spec=Signal)
        mock_signal_instance.id = mock_signal_id
        mock_signal_instance.trade_record_ids = []
        mock_signal_cls.return_value = mock_signal_instance
        
        skipped_trade_record_id = PydanticObjectId()
        mock_skipped_trade_record = MagicMock(spec=TradeRecord)
        mock_skipped_trade_record.id = skipped_trade_record_id
        mock_trade_record_cls.return_value = mock_skipped_trade_record

        mock_template_instance = mock_template_cls.return_value
        mock_template_instance.render = MagicMock(return_value="Mocked Skipped (Config) Trade Message")

        # 2. --- Input Data ---
        # Strategy enables auto-trade, but global GMGN_API_HOST will be missing via os.getenv mock
        strategy_config_dict = self._create_mock_strategy_config(
            "SkipConfigStrategy", 70,
            wallet_private_key_env_var="MISSING_CONFIG_PK_ENV_VAR",
            wallet_address="SKIP_CFG_WALLET_ADDR",
            buy_amount_sol=0.03
        ).model_dump()

        test_data = [{
            'token_data': {"address": "TOKEN_SKIP_CFG", "name": "Skip Config Token", "symbol": "SKCFG", "hit_kol_wallets":['k_skip']},
            'strategy_config_snapshot': strategy_config_dict
        }]
        
        await send_message_to_channel(test_data)
        
        # 4. --- Assertions ---
        # AutoTradeManager should be called and return skipped result
        mock_auto_trade_manager_instance.execute_trade.assert_called_once()
        
        # Signal should be created and saved
        mock_signal_instance.save.assert_called()

        # Message should be sent to user with skipped content
        mock_sender_instance.send_message_to_user.assert_called_once_with(
            "Mocked Skipped (Config) Trade Message", 'user_skip_config'
        )
        
        # Template should be rendered with correct context
        render_call_args = mock_template_instance.render.call_args[0][0]
        self.assertEqual(render_call_args['trade_result']['error_message'], "Configuration incomplete for auto-trade")

        # History record should be created
        history_arg = mock_history_instance.insert_one.call_args[0][0]
        self.assertEqual(history_arg['chat_id'], 'user_skip_config')
        self.assertEqual(history_arg['status'], "sent")

    @patch('workflows.monitor_kol_activity.handler.TelegramUserDAO')
    @patch('workflows.monitor_kol_activity.handler.TokenMessageSendHistoryDAO')
    @patch('workflows.monitor_kol_activity.handler.SignalDAO')
    @patch('workflows.monitor_kol_activity.handler.TradeRecordDAO')
    @patch('workflows.monitor_kol_activity.handler.ConfigDAO')
    @patch('workflows.monitor_kol_activity.handler.TelegramMessageSender')
    @patch('workflows.monitor_kol_activity.handler.get_current_time_dt')
    @patch('workflows.monitor_kol_activity.handler.Signal')
    @patch('workflows.monitor_kol_activity.handler.TradeRecord')
    @patch('workflows.monitor_kol_activity.handler.Template')
    async def test_send_message_to_channel_auto_trade_disabled_by_strategy(
        self, mock_template_cls, mock_trade_record_cls, mock_signal_cls, mock_get_current_time_dt, 
        mock_sender_cls, mock_config_dao_cls, mock_trade_record_dao_cls, mock_signal_dao_cls, 
        mock_history_dao_cls, mock_user_dao_cls
    ):
        # 1. --- Mock Setup ---
        mock_now_eastern = self.mock_current_time_dt_eastern
        mock_get_current_time_dt.return_value = mock_now_eastern

        mock_user_dao_instance = mock_user_dao_cls.return_value
        mock_users = [{'chat_id': 'user_skip_disabled'}]
        mock_user_dao_instance.get_all_users = AsyncMock(return_value=mock_users)

        mock_history_instance = mock_history_dao_cls.return_value
        mock_history_instance.insert_one = AsyncMock()

        mock_signal_dao_instance = mock_signal_dao_cls.return_value
        mock_signal_dao_instance.collection.find_one = AsyncMock(return_value=None)
        mock_signal_id = PydanticObjectId()
        mock_signal_dao_instance.insert_signals = AsyncMock(return_value=MagicMock(inserted_ids=[mock_signal_id]))
        mock_signal_dao_instance.save = AsyncMock()

        mock_trade_record_dao_instance = mock_trade_record_dao_cls.return_value
        mock_trade_record_dao_instance.save = AsyncMock() # Called once for SKIPPED

        mock_config_dao_instance = mock_config_dao_cls.return_value # Not used for admin in this path
        mock_config_doc_disabled = MagicMock()
        mock_config_doc_disabled.name = "application_config"
        mock_config_doc_disabled.type = "global"
        mock_config_doc_disabled.data = {"admin_telegram_chat_ids": []}
        mock_config_dao_instance.get_config = AsyncMock(return_value=mock_config_doc_disabled)

        mock_sender_instance = mock_sender_cls.return_value
        mock_sender_instance.send_message_to_user = AsyncMock(return_value=True)

        mock_signal_instance = MagicMock(spec=Signal)
        mock_signal_instance.id = mock_signal_id
        mock_signal_instance.trade_record_ids = []
        mock_signal_cls.return_value = mock_signal_instance

        skipped_trade_record_id = PydanticObjectId()
        mock_skipped_trade_record = MagicMock(spec=TradeRecord)
        mock_skipped_trade_record.id = skipped_trade_record_id
        mock_trade_record_cls.return_value = mock_skipped_trade_record

        mock_template_instance = mock_template_cls.return_value
        mock_template_instance.render = MagicMock(return_value="Mocked Skipped (Disabled) Trade Message")

        # 2. --- Input Data ---
        # Ensure gmgn_sol_wallet_address is None for this specific test case
        strategy_config_dict = self._create_mock_strategy_config(
            "DisabledTradeStrategy", 80, wallet_address=None 
        ).model_dump()

        test_data = [{
            'token_data': {"address": "TOKEN_SKIP_DIS", "name": "Skip Disabled Token", "symbol": "SKDIS", "hit_kol_wallets":['k_dis']},
            'strategy_config_snapshot': strategy_config_dict
        }]
        
        await send_message_to_channel(test_data)
        
        # 4. --- Assertions ---
        # Since auto_trade_enabled=False, AutoTradeManager should not be called
        # Handler creates a SKIPPED execution result directly
        
        # Signal should be created and saved
        mock_signal_instance.save.assert_called()

        # Message should be sent to user with skipped content
        mock_sender_instance.send_message_to_user.assert_called_once_with(
            "Mocked Skipped (Disabled) Trade Message", 'user_skip_disabled'
        )
        
        # Template should be rendered with correct context
        render_call_args = mock_template_instance.render.call_args[0][0]
        self.assertEqual(render_call_args['trade_result']['error_message'], "Auto-trade disabled by strategy configuration")

        # History record should be created
        history_arg = mock_history_instance.insert_one.call_args[0][0]
        self.assertEqual(history_arg['chat_id'], 'user_skip_disabled')
        self.assertEqual(history_arg['status'], "sent")

    @patch('workflows.monitor_kol_activity.handler.TelegramUserDAO')
    @patch('workflows.monitor_kol_activity.handler.TokenMessageSendHistoryDAO')
    @patch('workflows.monitor_kol_activity.handler.SignalDAO')
    @patch('workflows.monitor_kol_activity.handler.TradeRecordDAO')
    @patch('workflows.monitor_kol_activity.handler.ConfigDAO')
    @patch('workflows.monitor_kol_activity.handler.get_auto_trade_manager', new_callable=AsyncMock)
    @patch('workflows.monitor_kol_activity.handler.TelegramMessageSender')
    @patch('workflows.monitor_kol_activity.handler.get_current_time_dt')
    @patch('workflows.monitor_kol_activity.handler.Signal')
    @patch('workflows.monitor_kol_activity.handler.TradeRecord')
    @patch('workflows.monitor_kol_activity.handler.Template')
    async def test_send_message_to_channel_auto_trade_unhandled_exception(
        self, mock_template_cls, mock_trade_record_cls, mock_signal_cls, mock_get_current_time_dt,
        mock_sender_cls, mock_get_auto_trade_manager,
        mock_config_dao_cls, mock_trade_record_dao_cls, mock_signal_dao_cls,
        mock_history_dao_cls, mock_user_dao_cls
    ):
        # 1. --- Mock Setup ---
        mock_now_eastern = self.mock_current_time_dt_eastern
        mock_get_current_time_dt.return_value = mock_now_eastern
        
        mock_user_dao_instance = mock_user_dao_cls.return_value
        mock_users = [{'chat_id': 'user_exception'}]
        mock_user_dao_instance.get_all_users = AsyncMock(return_value=mock_users)
        
        mock_history_instance = mock_history_dao_cls.return_value
        mock_history_instance.insert_one = AsyncMock()
        
        mock_signal_dao_instance = mock_signal_dao_cls.return_value
        mock_signal_dao_instance.collection.find_one = AsyncMock(return_value=None)
        mock_signal_id = PydanticObjectId()
        mock_signal_dao_instance.insert_signals = AsyncMock(return_value=MagicMock(inserted_ids=[mock_signal_id]))
        mock_signal_dao_instance.save = AsyncMock() # For saving signal with trade_record_id

        mock_trade_record_dao_instance = mock_trade_record_dao_cls.return_value
        mock_trade_record_dao_instance.save = AsyncMock()

        mock_config_dao_instance = mock_config_dao_cls.return_value
        admin_ids = ["admin_ex_1"]
        # Corrected mock for ApplicationConfig
        mock_app_config_instance_ex = ApplicationConfig(admin_telegram_chat_ids=admin_ids)
        mock_config_doc_ex = MagicMock()
        mock_config_doc_ex.name = "application_config"
        mock_config_doc_ex.type = "application_config" # Match the type used in ConfigDAO
        mock_config_doc_ex.data = mock_app_config_instance_ex # Assign the instance
        mock_config_dao_instance.get_config = AsyncMock(return_value=mock_config_doc_ex)

        mock_sender_instance = mock_sender_cls.return_value
        mock_sender_instance.send_message_to_user = AsyncMock(return_value=True)
        
        # Import the needed classes
        from models.trade_execution import TradeExecutionResult, TradeStatus
        
        # Mock AutoTradeManager to raise an exception
        mock_auto_trade_manager_instance = MagicMock()
        # Mock config_manager.is_enabled() to return True (auto-trade enabled)
        mock_auto_trade_manager_instance.config_manager.is_enabled = AsyncMock(return_value=True)
        
        mock_auto_trade_manager_instance.execute_trade = AsyncMock(side_effect=ValueError("Network Timeout"))
        mock_get_auto_trade_manager.return_value = mock_auto_trade_manager_instance

        mock_signal_instance = MagicMock(spec=Signal)
        mock_signal_instance.id = mock_signal_id
        mock_signal_instance.trade_record_ids = []
        mock_signal_instance.save = AsyncMock()
        mock_signal_cls.return_value = mock_signal_instance
        
        mock_template_instance = mock_template_cls.return_value
        mock_template_instance.render = MagicMock(return_value="Mocked Trade Exception Message")

        # 2. --- Input Data ---
        strategy_config_dict = self._create_mock_strategy_config(
            "ExceptionStrategy", 90,
            wallet_private_key_env_var="EXCEPTION_PK_ENV_VAR",
            wallet_address="EXCEPTION_WALLET_ADDR",
            buy_amount_sol=0.04
        ).model_dump()

        test_data = [{
            'token_data': {"address": "TOKEN_EX", "name": "Exception Token", "symbol": "EXCPT", "hit_kol_wallets":['k_ex']},
            'strategy_config_snapshot': strategy_config_dict
        }]
        
        await send_message_to_channel(test_data)
        
        # 4. --- Assertions ---
        # AutoTradeManager should be called and raise exception
        mock_auto_trade_manager_instance.execute_trade.assert_called_once()

        # Signal should be created and saved
        mock_signal_instance.save.assert_called()

        # 注意：管理员通知现在由AutoTradeManager内部处理，handler只发送用户消息
        self.assertEqual(mock_sender_instance.send_message_to_user.call_count, 1) # 只有1条用户消息
        
        # Verify user message
        user_message_calls = [call for call in mock_sender_instance.send_message_to_user.call_args_list 
                             if call[0][1] == 'user_exception']
        self.assertEqual(len(user_message_calls), 1)
        self.assertEqual(user_message_calls[0][0][0], "Mocked Trade Exception Message")

        # User message template rendering
        render_call_args = mock_template_instance.render.call_args[0][0]
        self.assertEqual(render_call_args['trade_result']['error_message'], "AutoTradeManager exception: Network Timeout")

        # History record should be created
        history_arg = mock_history_instance.insert_one.call_args[0][0]
        self.assertEqual(history_arg['chat_id'], 'user_exception')
        self.assertEqual(history_arg['status'], "sent")

    @patch('workflows.monitor_kol_activity.handler.TelegramUserDAO')
    @patch('workflows.monitor_kol_activity.handler.TokenMessageSendHistoryDAO')
    @patch('workflows.monitor_kol_activity.handler.SignalDAO')
    @patch('workflows.monitor_kol_activity.handler.TradeRecordDAO') # Mocked, but only for SKIPPED record
    @patch('workflows.monitor_kol_activity.handler.ConfigDAO') # For admin IDs, but not critical for this test
    @patch('workflows.monitor_kol_activity.handler.get_auto_trade_manager', new_callable=AsyncMock)
    @patch('workflows.monitor_kol_activity.handler.TelegramMessageSender')
    @patch('workflows.monitor_kol_activity.handler.get_current_time_dt')
    @patch('workflows.monitor_kol_activity.handler.Signal')
    @patch('workflows.monitor_kol_activity.handler.TradeRecord')
    @patch('workflows.monitor_kol_activity.handler.Template')
    async def test_send_message_to_channel_template_render_failure(
        self, mock_template_cls, mock_trade_record_cls, mock_signal_cls, mock_get_current_time_dt,
        mock_sender_cls, mock_get_auto_trade_manager, mock_config_dao_cls, mock_trade_record_dao_cls, mock_signal_dao_cls,
        mock_history_dao_cls, mock_user_dao_cls
    ):
        # 1. --- Mock Setup ---
        mock_now_eastern = self.mock_current_time_dt_eastern
        mock_get_current_time_dt.return_value = mock_now_eastern
        
        mock_user_dao_instance = mock_user_dao_cls.return_value
        mock_users = [{'chat_id': 'user_render_fail'}]
        mock_user_dao_instance.get_all_users = AsyncMock(return_value=mock_users)

        mock_history_instance = mock_history_dao_cls.return_value
        mock_history_instance.insert_one = AsyncMock()
        
        mock_signal_dao_instance = mock_signal_dao_cls.return_value
        mock_signal_dao_instance.collection.find_one = AsyncMock(return_value=None)
        mock_signal_id = PydanticObjectId()
        mock_signal_dao_instance.insert_signals = AsyncMock(return_value=MagicMock(inserted_ids=[mock_signal_id]))
        mock_signal_dao_instance.save = AsyncMock() # For saving signal with trade_record_id

        # Mock TradeRecordDAO for the SKIPPED record (auto_trade_enabled=False)
        mock_trade_record_dao_instance = mock_trade_record_dao_cls.return_value
        mock_trade_record_dao_instance.save = AsyncMock()
        
        mock_config_dao_instance = mock_config_dao_cls.return_value # Not critical for this test
        mock_config_doc_render_fail = MagicMock()
        mock_config_doc_render_fail.name = "application_config"
        mock_config_doc_render_fail.type = "global"
        mock_config_doc_render_fail.data = {"admin_telegram_chat_ids": []}
        mock_config_dao_instance.get_config = AsyncMock(return_value=mock_config_doc_render_fail)

        mock_sender_instance = mock_sender_cls.return_value
        mock_sender_instance.send_message_to_user = AsyncMock(return_value=True)

        # Import the needed classes
        from models.trade_execution import TradeExecutionResult, TradeStatus
        
        # Mock AutoTradeManager - disabled globally so returns SKIPPED
        mock_auto_trade_manager_instance = MagicMock()
        # Mock config_manager.is_enabled() to return False (auto-trade disabled)
        mock_auto_trade_manager_instance.config_manager.is_enabled = AsyncMock(return_value=False)
        mock_get_auto_trade_manager.return_value = mock_auto_trade_manager_instance

        mock_signal_instance = MagicMock(spec=Signal)
        mock_signal_instance.id = mock_signal_id
        mock_signal_instance.trade_record_ids = []
        mock_signal_instance.save = AsyncMock()
        mock_signal_cls.return_value = mock_signal_instance
        
        skipped_trade_record_id = PydanticObjectId()
        mock_skipped_trade_record = MagicMock(spec=TradeRecord)
        mock_skipped_trade_record.id = skipped_trade_record_id
        mock_trade_record_cls.return_value = mock_skipped_trade_record # This will be saved

        # Simulate Template.render() raising an exception
        mock_template_instance = mock_template_cls.return_value
        mock_template_instance.render = MagicMock(side_effect=Exception("Jinja Rendering Error"))

        # 2. --- Input Data ---
        strategy_config_dict = self._create_mock_strategy_config(
            "RenderFailStrategy", 110 # Keep simple for this test
        ).model_dump()

        token_address = "TOKEN_RENDER_FAIL"
        token_symbol = "RFAIL"
        test_data = [{
            'token_data': {"address": token_address, "name": "Render Fail Token", "symbol": token_symbol, "hit_kol_wallets":['k_rfail']},
            'strategy_config_snapshot': strategy_config_dict
        }]
        
        await send_message_to_channel(test_data)

        # 4. --- Assertions ---
        # Template.render was called
        mock_template_instance.render.assert_called_once()

        # TelegramMessageSender send_message_to_user was called with the fallback message
        mock_sender_instance.send_message_to_user.assert_called_once()
        sent_message_content = mock_sender_instance.send_message_to_user.call_args[0][0]
        
        expected_fallback_message = f"📣 Purchase Alert for {token_symbol}! Strategy: {strategy_config_dict['strategy_name']}. Mint: {token_address}. Please check system logs for trade details."
        self.assertEqual(sent_message_content, expected_fallback_message)

        # History should still be recorded
        mock_history_instance.insert_one.assert_called_once()
        history_arg = mock_history_instance.insert_one.call_args[0][0]
        self.assertEqual(history_arg['status'], "sent") # Assuming send_message_to_user mock returns True
        self.assertEqual(history_arg['signal_id'], str(mock_signal_id))

        # In the new architecture, when auto-trade is disabled globally, no TradeRecord is created
        # because AutoTradeManager is not called at all. This is the expected behavior.
        mock_trade_record_dao_instance.save.assert_not_called()

if __name__ == '__main__':
    unittest.main()