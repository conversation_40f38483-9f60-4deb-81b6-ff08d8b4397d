import unittest
import asyncio
import time
import random
import logging
from utils.workflows.parallel_control import ParallelControl

class TestParallelControl(unittest.TestCase):
    """ParallelControl类的单元测试"""

    @classmethod
    def setUpClass(cls):
        """在所有测试开始前运行"""
        cls.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(cls.loop)

    @classmethod
    def tearDownClass(cls):
        """在所有测试结束后运行"""
        # 取消所有剩余的任务
        tasks = asyncio.all_tasks(loop=cls.loop)
        for task in tasks:
            task.cancel()
        
        # 等待所有任务完成取消
        # 需要运行循环来处理取消操作
        if tasks: # 只有当有任务时才运行
            cls.loop.run_until_complete(asyncio.gather(*tasks, return_exceptions=True))

        cls.loop.close()
        asyncio.set_event_loop(None) # 清理

    def setUp(self):
        """测试前的准备工作"""
        self.execution_tracker = {}
        self.logger = logging.getLogger("TestParallelControl")
        # ParallelControl 将在 async_setup 中初始化
        
    async def async_setup(self):
        """异步设置，在每个测试方法中调用"""
        self.parallel_control = ParallelControl(concurrency=3) # 移除 loop
        return self.parallel_control
        
    async def async_teardown(self):
        """异步清理，在每个测试方法中调用"""
        if hasattr(self, 'parallel_control') and self.parallel_control:
            await self.parallel_control.stop_all_tasks()
    
    async def sample_coroutine(self, coro_id, sleep_time=0.1, should_fail=False, should_raise=False):
        """示例协程，用于测试
        
        Args:
            coro_id: 协程ID，用于标识
            sleep_time: 睡眠时间，模拟工作
            should_fail: 是否应该失败
            should_raise: 是否应该抛出异常
        """
        if coro_id not in self.execution_tracker:
            self.execution_tracker[coro_id] = {'count': 0, 'last_run': time.time(), 'times': []}
        
        self.execution_tracker[coro_id]['count'] += 1
        current_time = time.time()
        self.execution_tracker[coro_id]['last_run'] = current_time
        self.execution_tracker[coro_id]['times'].append(current_time)
        
        await asyncio.sleep(sleep_time)
        
        if should_raise:
            raise ValueError(f"协程 {coro_id} 抛出了异常")
        
        return not should_fail
    
    # 用于生成可重复使用的协程的工厂函数
    def make_coroutine(self, coro_id, sleep_time=0.1, should_fail=False, should_raise=False):
        """返回一个新的协程对象，用于解决协程重用问题"""
        return self.sample_coroutine(coro_id, sleep_time, should_fail, should_raise)
    
    async def _test_acquire_and_release_resource_async_wrapper(self):
        await self.async_setup()
        try:
            await self._test_acquire_and_release_resource_actual()
        finally:
            await self.async_teardown()

    def test_acquire_and_release_resource(self):
        """测试资源的获取和释放"""
        self.loop.run_until_complete(self._test_acquire_and_release_resource_async_wrapper())
        
    async def _test_acquire_and_release_resource_actual(self): # Renamed from _test_acquire_and_release_resource
        resource_id = "test_resource"
        owner_id = "test_owner"
        
        acquired = await self.parallel_control.acquire_resource(resource_id, owner_id)
        self.assertTrue(acquired, "应该能够获取资源")
        
        released = await self.parallel_control.release_resource(resource_id, owner_id)
        self.assertTrue(released, "应该能够释放资源")
        
        acquired = await self.parallel_control.acquire_resource(resource_id, owner_id)
        self.assertTrue(acquired, "应该能够获取资源（即使之前不存在）")
        
        other_owner = "other_owner"
        released = await self.parallel_control.release_resource(resource_id, other_owner)
        self.assertFalse(released, "其他所有者不应该能够释放资源")
        
        await self.parallel_control.release_resource(resource_id, owner_id)

    async def _test_resource_contention_async_wrapper(self):
        await self.async_setup()
        try:
            await self._test_resource_contention_actual()
        finally:
            await self.async_teardown()

    def test_resource_contention(self):
        """测试资源争用情况"""
        self.loop.run_until_complete(self._test_resource_contention_async_wrapper())
        
    async def _test_resource_contention_actual(self): # Renamed from _test_resource_contention
        resource_id = "contested_resource"
        owner1 = "owner1"
        owner2 = "owner2"
        
        acquired1 = await self.parallel_control.acquire_resource(resource_id, owner1)
        self.assertTrue(acquired1, "第一个所有者应该能够获取资源")
        
        try:
            acquired2 = await asyncio.wait_for(
                self.parallel_control.acquire_resource(resource_id, owner2, timeout=0.5),
                timeout=1.0
            )
            if acquired2:
                await self.parallel_control.release_resource(resource_id, owner2)
        except asyncio.TimeoutError:
            pass # Expected
        
        await self.parallel_control.release_resource(resource_id, owner1)

    async def _test_execute_parallel_async_wrapper(self):
        await self.async_setup()
        try:
            await self._test_execute_parallel_actual()
        finally:
            await self.async_teardown()

    def test_execute_parallel(self):
        """测试并行执行多个协程"""
        self.loop.run_until_complete(self._test_execute_parallel_async_wrapper())
        
    async def _test_execute_parallel_actual(self): # Renamed from _test_execute_parallel
        coros = [self.make_coroutine(f'parallel_{i}', sleep_time=0.1) for i in range(5)]
        tasks = await self.parallel_control.execute_parallel(coros)
        await asyncio.gather(*tasks) # Ensure tasks are awaited
        
        expected_coros = {f'parallel_{i}' for i in range(5)}
        # Give a small delay for execution_tracker to be updated by tasks
        await asyncio.sleep(0.01)         
        actual_coros = set(self.execution_tracker.keys())
        self.assertTrue(expected_coros.issubset(actual_coros), 
                        f"应该执行所有协程，期望: {expected_coros}，实际: {actual_coros}")

    async def _test_set_concurrency_async_wrapper(self):
        await self.async_setup()
        try:
            await self._test_set_concurrency_actual()
        finally:
            await self.async_teardown()

    def test_set_concurrency(self):
        """测试设置并发度"""
        self.loop.run_until_complete(self._test_set_concurrency_async_wrapper())
        
    async def _test_set_concurrency_actual(self): # Renamed from _test_set_concurrency
        initial_concurrency = self.parallel_control._executor.concurrency
        self.assertEqual(initial_concurrency, 3, "初始并发度应该是3")
        
        new_concurrency = 5
        await self.parallel_control.set_concurrency(new_concurrency)
        
        self.assertEqual(self.parallel_control._executor.concurrency, new_concurrency, 
                            f"并发度应该更改为{new_concurrency}")
        
        with self.assertRaises(ValueError):
            await self.parallel_control.set_concurrency(0)

    async def _test_check_and_restart_task_async_wrapper(self):
        await self.async_setup()
        try:
            await self._test_check_and_restart_task_actual()
        finally:
            await self.async_teardown()

    def test_check_and_restart_task(self):
        """测试检查和重启任务功能"""
        self.loop.run_until_complete(self._test_check_and_restart_task_async_wrapper())
    
    async def _test_check_and_restart_task_actual(self): # Renamed from _test_check_and_restart_task
        self.parallel_control._executor.set_test_mode(True)
        coro = self.make_coroutine('restart_check', should_fail=True)
        tasks = await self.parallel_control.execute_parallel([coro])
        await asyncio.gather(*tasks, return_exceptions=True) # Ensure tasks are awaited
        await asyncio.sleep(0.1)
        await self.parallel_control.check_and_restart_task()
        self.assertIn('restart_check', self.execution_tracker, "任务应该已执行")

    async def _test_check_deadlocks_async_wrapper(self):
        await self.async_setup()
        try:
            await self._test_check_deadlocks_actual()
        finally:
            await self.async_teardown()

    def test_check_deadlocks(self):
        """测试死锁检测功能"""
        self.loop.run_until_complete(self._test_check_deadlocks_async_wrapper())
    
    async def _test_check_deadlocks_actual(self): # Renamed from _test_check_deadlocks
        resource1 = "resource1"
        resource2 = "resource2"
        owner1 = "owner1"
        owner2 = "owner2"
        
        # 模拟 owner1 获取 resource1 并等待 resource2
        await self.parallel_control._lock_manager.acquire_lock(resource1, owner1)
        self.parallel_control._lock_manager._deadlock_detector.add_waiting(owner1, resource2)
        
        # 模拟 owner2 获取 resource2 并等待 resource1
        await self.parallel_control._lock_manager.acquire_lock(resource2, owner2)
        self.parallel_control._lock_manager._deadlock_detector.add_waiting(owner2, resource1)

        # 运行死锁检测
        # 调用 ResourceLockManager 的公共方法 check_deadlocks
        deadlocks = await self.parallel_control._lock_manager.check_deadlocks()
        self.assertTrue(deadlocks, "应该检测到死锁")
        
        # 验证资源是否已释放 (如果死锁检测机制会自动释放)
        # 注意: 实际的死锁解决策略可能不同，这里假设检测到后会释放
        # self.assertIsNone(self.parallel_control._lock_manager.get_lock_owner(resource1), "死锁后资源1应被释放")
        # self.assertIsNone(self.parallel_control._lock_manager.get_lock_owner(resource2), "死锁后资源2应被释放")

        # 手动清理，因为不确定死锁检测是否会完全清理
        # 访问 _lock_info 来检查所有者，而不是使用不存在的 get_lock_owner
        lock_info1 = self.parallel_control._lock_manager._lock_info.get(resource1)
        if lock_info1 and lock_info1.owner == owner1:
            await self.parallel_control._lock_manager.release_lock(resource1, owner1)
        
        lock_info2 = self.parallel_control._lock_manager._lock_info.get(resource2)
        if lock_info2 and lock_info2.owner == owner2:
            await self.parallel_control._lock_manager.release_lock(resource2, owner2)

if __name__ == "__main__":
    unittest.main() 