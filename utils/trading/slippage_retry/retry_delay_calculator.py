"""重试间隔计算器 - 智能重试间隔计算，适应meme币市场"""

from typing import Optional
import logging
from models.config import RetryDelayStrategy, TradingParams

logger = logging.getLogger(__name__)


class RetryDelayCalculator:
    """重试间隔计算器 - 智能计算重试间隔，支持多种策略"""
    
    def __init__(self):
        """初始化重试间隔计算器"""
        pass
    
    def calculate_delay(
        self,
        retry_count: int,
        trading_params: TradingParams,
        is_slippage_error: bool = False,
        trade_type: str = "buy"
    ) -> float:
        """
        计算重试间隔时间
        
        Args:
            retry_count: 当前重试次数（从1开始）
            trading_params: 交易参数配置
            is_slippage_error: 是否为滑点相关错误
            trade_type: 交易类型 (buy/sell)
            
        Returns:
            float: 重试间隔时间（秒）
        """
        # 获取有效配置
        effective_config = self.get_effective_delay_config(trading_params, trade_type)
        
        # 确定基础间隔
        base_delay = self._get_base_delay(
            trading_params, is_slippage_error, effective_config
        )
        
        # 根据策略计算间隔
        calculated_delay = self._calculate_by_strategy(
            base_delay, retry_count, trading_params.retry_delay_strategy
        )
        
        # 应用最大间隔限制
        final_delay = min(calculated_delay, trading_params.max_retry_delay_seconds)
        
        logger.debug(
            f"重试间隔计算: 第{retry_count}次重试, {trade_type.upper()}, "
            f"策略={trading_params.retry_delay_strategy.value}, "
            f"基础间隔={base_delay}s, 计算结果={calculated_delay}s, "
            f"最终间隔={final_delay}s (上限={trading_params.max_retry_delay_seconds}s)"
        )
        
        return final_delay
    
    def get_effective_delay_config(
        self,
        trading_params: TradingParams,
        trade_type: str
    ) -> dict:
        """
        获取指定交易类型的有效间隔配置
        
        Args:
            trading_params: 交易参数配置
            trade_type: 交易类型 (buy/sell)
            
        Returns:
            dict: 有效的间隔配置
        """
        if trade_type.lower() == 'buy':
            specific_delay = trading_params.buy_retry_delay_seconds
        else:  # sell
            specific_delay = trading_params.sell_retry_delay_seconds
        
        return {
            'specific_delay': specific_delay,
            'default_delay': trading_params.retry_delay_seconds,
            'effective_delay': specific_delay if specific_delay is not None else trading_params.retry_delay_seconds
        }
    
    def _get_base_delay(
        self,
        trading_params: TradingParams,
        is_slippage_error: bool,
        effective_config: dict
    ) -> float:
        """
        获取基础间隔时间
        
        Args:
            trading_params: 交易参数配置
            is_slippage_error: 是否为滑点相关错误
            effective_config: 有效配置
            
        Returns:
            float: 基础间隔时间（秒）
        """
        # 滑点错误专用间隔优先级最高
        if is_slippage_error and trading_params.slippage_error_delay_seconds is not None:
            logger.debug(f"使用滑点错误专用间隔: {trading_params.slippage_error_delay_seconds}s")
            return trading_params.slippage_error_delay_seconds
        
        # 使用有效配置的间隔
        base_delay = effective_config['effective_delay']
        logger.debug(f"使用基础间隔: {base_delay}s")
        return base_delay
    
    def _calculate_by_strategy(
        self,
        base_delay: float,
        retry_count: int,
        strategy: RetryDelayStrategy
    ) -> float:
        """
        根据策略计算间隔
        
        Args:
            base_delay: 基础间隔
            retry_count: 重试次数
            strategy: 间隔策略
            
        Returns:
            float: 计算后的间隔时间
        """
        if strategy == RetryDelayStrategy.FIXED:
            return base_delay
        elif strategy == RetryDelayStrategy.LINEAR:
            return base_delay * retry_count
        elif strategy == RetryDelayStrategy.EXPONENTIAL:
            return base_delay * (2 ** (retry_count - 1))
        else:
            logger.warning(f"未知的重试间隔策略: {strategy}，使用固定间隔")
            return base_delay
    
    def validate_delay_config(
        self,
        trading_params: TradingParams,
        meme_coin_market: bool = True
    ) -> bool:
        """
        验证间隔配置的有效性，特别是meme币市场的适应性
        
        Args:
            trading_params: 交易参数配置
            meme_coin_market: 是否为meme币市场
            
        Returns:
            bool: 配置是否有效
        """
        issues = []
        warnings = []
        
        # 基础验证
        if trading_params.retry_delay_seconds < 0:
            issues.append(f"基础重试间隔不能为负数: {trading_params.retry_delay_seconds}")
        
        if trading_params.max_retry_delay_seconds < trading_params.retry_delay_seconds:
            issues.append(
                f"最大重试间隔({trading_params.max_retry_delay_seconds}s) "
                f"不能小于基础间隔({trading_params.retry_delay_seconds}s)"
            )
        
        # meme币市场特化验证
        if meme_coin_market:
            # meme币市场建议配置
            if trading_params.retry_delay_seconds > 2.0:
                warnings.append(
                    f"meme币市场建议基础重试间隔不超过2秒，当前: {trading_params.retry_delay_seconds}s"
                )
            
            if trading_params.max_retry_delay_seconds > 10.0:
                warnings.append(
                    f"meme币市场建议最大重试间隔不超过10秒，当前: {trading_params.max_retry_delay_seconds}s"
                )
            
            # 指数退避在meme币市场可能过于激进
            if trading_params.retry_delay_strategy == RetryDelayStrategy.EXPONENTIAL:
                if trading_params.retry_delay_seconds > 0.5:
                    warnings.append(
                        f"meme币市场使用指数退避时，建议基础间隔不超过0.5秒，当前: {trading_params.retry_delay_seconds}s"
                    )
        
        # 滑点错误间隔验证
        if trading_params.slippage_error_delay_seconds is not None:
            if trading_params.slippage_error_delay_seconds < 0:
                issues.append(f"滑点错误间隔不能为负数: {trading_params.slippage_error_delay_seconds}")
            
            if meme_coin_market and trading_params.slippage_error_delay_seconds > 1.0:
                warnings.append(
                    f"meme币市场建议滑点错误间隔不超过1秒，当前: {trading_params.slippage_error_delay_seconds}s"
                )
        
        # 输出验证结果
        if issues:
            for issue in issues:
                logger.error(f"间隔配置错误: {issue}")
            return False
        
        if warnings:
            for warning in warnings:
                logger.warning(f"间隔配置建议: {warning}")
        
        logger.info("重试间隔配置验证通过")
        return True
    
    def get_strategy_info(self, strategy: RetryDelayStrategy) -> dict:
        """
        获取间隔策略的详细信息
        
        Args:
            strategy: 间隔策略
            
        Returns:
            dict: 策略信息
        """
        strategy_info = {
            RetryDelayStrategy.FIXED: {
                "name": "固定间隔",
                "description": "每次重试使用相同的间隔时间",
                "formula": "delay = base_delay",
                "适用场景": "稳定网络环境，或需要快速重试的场景",
                "meme币适应性": "高 - 保持快速重试节奏"
            },
            RetryDelayStrategy.LINEAR: {
                "name": "线性递增",
                "description": "间隔时间随重试次数线性增加",
                "formula": "delay = base_delay * retry_count",
                "适用场景": "需要逐步缓解系统压力的场景",
                "meme币适应性": "中 - 需要控制基础间隔较小"
            },
            RetryDelayStrategy.EXPONENTIAL: {
                "name": "指数退避",
                "description": "间隔时间随重试次数指数增长",
                "formula": "delay = base_delay * (2^(retry_count-1))",
                "适用场景": "网络拥堵或系统过载场景",
                "meme币适应性": "低 - 间隔增长过快，可能错失机会"
            }
        }
        
        return strategy_info.get(strategy, {"name": "未知策略"})
    
    def estimate_total_retry_time(
        self,
        max_retries: int,
        trading_params: TradingParams,
        trade_type: str = "buy"
    ) -> float:
        """
        预估总重试时间
        
        Args:
            max_retries: 最大重试次数
            trading_params: 交易参数配置
            trade_type: 交易类型
            
        Returns:
            float: 预估总重试时间（秒）
        """
        total_time = 0
        
        for retry_count in range(1, max_retries + 1):
            delay = self.calculate_delay(retry_count, trading_params, False, trade_type)
            total_time += delay
        
        logger.debug(
            f"预估总重试时间: {total_time:.2f}s "
            f"(最大{max_retries}次重试, {trade_type.upper()}, "
            f"策略={trading_params.retry_delay_strategy.value})"
        )
        
        return total_time 