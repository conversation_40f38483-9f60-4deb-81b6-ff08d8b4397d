"""
死信队列模块

用于处理无法正常处理的消息，将它们放入专门的死信队列中，
解决队头阻塞问题，确保消息流处理的连续性和可靠性。
"""

import logging
import time
import traceback
from enum import Enum
from typing import Dict, Optional, Any, List

from utils.workflows.message_queue.message_queue import Message, KafkaQueue


class DeadLetterType(Enum):
    """死信队列类型"""
    STORAGE_FAILURE = "storage_failure"  # 存储失败
    PROCESS_FAILURE = "process_failure"  # 处理失败
    TIMEOUT = "timeout"  # 处理超时

class DeadLetterMessage:
    """死信队列消息"""
    
    def __init__(
        self,
        original_topic: str,
        original_partition: int,
        original_offset: int,
        error_message: str,
        stack_trace: str,
        node_name: str,
        original_data: Optional[Dict[str, Any]] = None
    ):
        """初始化死信队列消息
        
        Args:
            original_topic: 原始Topic名称
            original_partition: 原始Partition
            original_offset: 原始Offset
            error_message: 错误信息
            stack_trace: 堆栈信息
            node_name: 节点名称
            original_data: 原始消息数据
        """
        self.original_topic = original_topic
        self.original_partition = original_partition
        self.original_offset = original_offset
        self.failure_timestamp = int(time.time() * 1000)  # 毫秒时间戳
        self.error_message = error_message
        self.stack_trace = stack_trace
        self.node_name = node_name
        self.original_data = original_data
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典
        
        Returns:
            Dict[str, Any]: 字典形式的死信队列消息
        """
        return {
            "original_topic": self.original_topic,
            "original_partition": self.original_partition,
            "original_offset": self.original_offset,
            "failure_timestamp": self.failure_timestamp,
            "error_message": self.error_message,
            "stack_trace": self.stack_trace,
            "node_name": self.node_name,
            "original_data": self.original_data
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DeadLetterMessage':
        """从字典创建死信队列消息
        
        Args:
            data: 字典数据
            
        Returns:
            DeadLetterMessage: 死信队列消息
        """
        msg = cls(
            original_topic=data.get("original_topic", ""),
            original_partition=data.get("original_partition", 0),
            original_offset=data.get("original_offset", 0),
            error_message=data.get("error_message", ""),
            stack_trace=data.get("stack_trace", ""),
            node_name=data.get("node_name", "")
        )
        msg.failure_timestamp = data.get("failure_timestamp", int(time.time() * 1000))
        msg.original_data = data.get("original_data")
        return msg
    
    @classmethod
    def from_original_message(
        cls, 
        message: Message, 
        error: Exception, 
        node_name: str
    ) -> 'DeadLetterMessage':
        """从原始消息创建死信队列消息
        
        Args:
            message: 原始消息
            error: 异常信息
            node_name: 节点名称
            
        Returns:
            DeadLetterMessage: 死信队列消息
        """
        # 安全获取属性，如果不存在则使用默认值
        topic = getattr(message, 'topic', 'unknown_topic')
        partition = getattr(message, 'partition', 0)
        offset = getattr(message, 'offset', -1)
        
        # 如果消息有metadata，尝试从metadata中获取信息
        if hasattr(message, 'metadata') and isinstance(message.metadata, dict):
            # 尝试从metadata中获取topic, partition, offset
            topic = message.metadata.get('topic', topic)
            partition = message.metadata.get('partition', partition)
            offset = message.metadata.get('offset', offset)
            
            # 也可以用message_id作为offset
            if offset == -1 and 'message_id' in message.metadata:
                try:
                    offset = int(message.metadata['message_id'])
                except (ValueError, TypeError):
                    # 如果message_id不能转为整数，保持offset为-1
                    pass
        
        stack_trace_str = ""
        if isinstance(error, Exception):
            if error.__traceback__ is not None:
                # Format the traceback lines
                tb_lines = traceback.format_tb(error.__traceback__)
                # Format the exception type and message
                exc_lines = traceback.format_exception_only(type(error), error)
                # Combine them, ensuring "Traceback..." header if there's a traceback
                stack_trace_str = "Traceback (most recent call last):\\n" + "".join(tb_lines) + "".join(exc_lines)
            else:
                # No traceback, just format exception type and message
                exc_lines = traceback.format_exception_only(type(error), error)
                stack_trace_str = "".join(exc_lines)
        
        return cls(
            original_topic=topic,
            original_partition=partition,
            original_offset=offset,
            error_message=str(error),
            stack_trace=stack_trace_str,
            node_name=node_name,
            original_data=message.data if hasattr(message, 'data') else None
        )


class DeadLetterQueue:
    """死信队列基类"""
    
    def __init__(self, dlq_type: DeadLetterType):
        """初始化死信队列
        
        Args:
            dlq_type: 死信队列类型
        """
        self.dlq_type = dlq_type
        self.logger = logging.getLogger(f"DeadLetterQueue.{dlq_type.value}")
    
    async def send(self, message: DeadLetterMessage) -> bool:
        """发送消息到死信队列
        
        Args:
            message: 死信队列消息
            
        Returns:
            bool: 是否发送成功
        """
        self.logger.info(f"发送消息到死信队列 {self.dlq_type.value}: {message.to_dict()}")
        return await self._send_impl(message)
    
    async def _send_impl(self, message: DeadLetterMessage) -> bool:
        """实际发送消息的实现
        
        Args:
            message: 死信队列消息
            
        Returns:
            bool: 是否发送成功
        """
        raise NotImplementedError("子类必须实现此方法")


class KafkaDeadLetterQueue(DeadLetterQueue):
    """Kafka死信队列"""
    
    def __init__(self, kafka_queue: KafkaQueue, dlq_type: DeadLetterType):
        """初始化Kafka死信队列
        
        Args:
            kafka_queue: Kafka队列
            dlq_type: 死信队列类型
        """
        super().__init__(dlq_type)
        self.kafka_queue = kafka_queue
    
    async def _send_impl(self, message: DeadLetterMessage) -> bool:
        """发送消息到Kafka死信队列
        
        Args:
            message: 死信队列消息
            
        Returns:
            bool: 是否发送成功
        """
        try:
            dlq_message = Message(data=message.to_dict())
            await self.kafka_queue.send(dlq_message)
            self.logger.info(f"成功发送消息到Kafka死信队列: {self.dlq_type.value}")
            return True
        except Exception as e:
            self.logger.error(f"发送消息到Kafka死信队列失败: {e}\n{traceback.format_exc()}")
            return False


class DeadLetterQueueManager:
    """死信队列管理器"""
    
    def __init__(self):
        """初始化死信队列管理器"""
        self.queues: Dict[DeadLetterType, DeadLetterQueue] = {}
        self.logger = logging.getLogger("DeadLetterQueueManager")
    
    def register_queue(self, dlq_type: DeadLetterType, queue: DeadLetterQueue):
        """注册死信队列
        
        Args:
            dlq_type: 死信队列类型
            queue: 死信队列
        """
        self.queues[dlq_type] = queue
        self.logger.info(f"注册死信队列: {dlq_type.value}")
    
    async def send_to_queue(self, dlq_type: DeadLetterType, message: DeadLetterMessage) -> bool:
        """发送消息到指定类型的死信队列
        
        Args:
            dlq_type: 死信队列类型
            message: 死信队列消息
            
        Returns:
            bool: 是否发送成功
        """
        if dlq_type not in self.queues:
            self.logger.error(f"未注册的死信队列类型: {dlq_type.value}")
            return False
        
        return await self.queues[dlq_type].send(message)
    
    async def send_from_original(
        self, 
        dlq_type: DeadLetterType, 
        original_message: Message, 
        error: Exception, 
        node_name: str
    ) -> bool:
        """从原始消息发送到死信队列
        
        Args:
            dlq_type: 死信队列类型
            original_message: 原始消息
            error: 异常信息
            node_name: 节点名称
            
        Returns:
            bool: 是否发送成功
        """
        dlq_message = DeadLetterMessage.from_original_message(
            original_message, error, node_name
        )
        return await self.send_to_queue(dlq_type, dlq_message) 