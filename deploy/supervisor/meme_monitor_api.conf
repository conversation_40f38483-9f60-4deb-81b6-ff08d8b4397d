[program:meme_monitor_api]
; 程序名称
directory=/root/meme_monitor
; 程序运行目录
command=/root/.local/bin/poetry run gunicorn main:app -c gunicorn_conf.py
; 启动命令
user=root
; 运行用户
autostart=true
; 是否自动启动
autorestart=true
; 是否自动重启
startsecs=5
; 启动时间
stopwaitsecs=5
; 终止等待时间
redirect_stderr=true
; 重定向错误输出
stdout_logfile=/root/meme_monitor/logs/supervisor_meme_monitor_api.out.log
stderr_logfile=/root/meme_monitor/logs/supervisor_meme_monitor_api.err.log
; 标准输出日志
stdout_logfile_maxbytes=20MB
; 日志文件大小
stdout_logfile_backups=10
; 日志文件备份数
environment=PYTHONPATH="/root/meme_monitor",ENV="production"
; 环境变量 