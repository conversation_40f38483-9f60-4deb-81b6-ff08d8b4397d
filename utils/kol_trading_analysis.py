#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
KOL钱包交易活动分析
计算每个token的胜率和收益率
"""

import json
import pandas as pd
from datetime import datetime
from collections import defaultdict
import numpy as np

def load_data(file_path):
    """加载JSON数据"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data

def analyze_token_performance(data):
    """分析每个token的交易表现"""
    
    # 按token分组整理数据
    token_trades = defaultdict(list)
    
    for trade in data:
        token_address = trade['token']['address']
        token_symbol = trade['token']['symbol']
        
        trade_info = {
            'symbol': token_symbol,
            'address': token_address,
            'event_type': trade['event_type'],
            'cost_usd': float(trade['cost_usd']),
            'buy_cost_usd': float(trade['buy_cost_usd']) if trade['buy_cost_usd'] else None,
            'timestamp': trade['timestamp'],
            'is_open_or_close': trade['is_open_or_close'],
            'price_usd': float(trade['price_usd']),
            'token_amount': float(trade['token_amount'])
        }
        
        token_trades[token_address].append(trade_info)
    
    # 分析每个token的表现
    results = []
    
    for token_address, trades in token_trades.items():
        # 按时间排序
        trades.sort(key=lambda x: x['timestamp'])
        
        token_symbol = trades[0]['symbol']
        
        # 计算交易对（买入-卖出配对）
        buy_stack = []  # 存储未匹配的买入
        completed_trades = []  # 完成的交易对
        
        total_invested = 0
        total_return = 0
        
        for trade in trades:
            if trade['event_type'] == 'buy':
                buy_stack.append(trade)
                total_invested += trade['cost_usd']
            elif trade['event_type'] == 'sell' and trade['buy_cost_usd'] is not None:
                # 这是一个有明确买入成本的卖出
                profit_loss = trade['cost_usd'] - trade['buy_cost_usd']
                roi = (profit_loss / trade['buy_cost_usd']) * 100 if trade['buy_cost_usd'] > 0 else 0
                
                completed_trades.append({
                    'buy_cost': trade['buy_cost_usd'],
                    'sell_value': trade['cost_usd'],
                    'profit_loss': profit_loss,
                    'roi': roi,
                    'is_profitable': profit_loss > 0
                })
                
                total_return += trade['cost_usd']
        
        # 计算统计数据
        if completed_trades:
            total_trades = len(completed_trades)
            profitable_trades = sum(1 for t in completed_trades if t['is_profitable'])
            win_rate = (profitable_trades / total_trades) * 100
            
            total_profit_loss = sum(t['profit_loss'] for t in completed_trades)
            total_buy_cost = sum(t['buy_cost'] for t in completed_trades)
            average_roi = (total_profit_loss / total_buy_cost) * 100 if total_buy_cost > 0 else 0
            
            # 计算总收益率（总盈亏/总投资成本）
            total_return_rate = (total_profit_loss / total_buy_cost) * 100 if total_buy_cost > 0 else 0
            
            # 最大盈利和亏损
            max_profit = max(t['profit_loss'] for t in completed_trades) if completed_trades else 0
            max_loss = min(t['profit_loss'] for t in completed_trades) if completed_trades else 0
            
            # 平均盈利和亏损
            profitable_trades_list = [t for t in completed_trades if t['is_profitable']]
            losing_trades_list = [t for t in completed_trades if not t['is_profitable']]
            
            avg_profit = np.mean([t['profit_loss'] for t in profitable_trades_list]) if profitable_trades_list else 0
            avg_loss = np.mean([t['profit_loss'] for t in losing_trades_list]) if losing_trades_list else 0
            
        else:
            total_trades = 0
            win_rate = 0
            average_roi = 0
            total_profit_loss = 0
            total_return_rate = 0
            max_profit = 0
            max_loss = 0
            avg_profit = 0
            avg_loss = 0
        
        # 计算当前仍持有的投资（未匹配的买入）
        current_holdings = sum(trade['cost_usd'] for trade in buy_stack)
        
        results.append({
            'token_symbol': token_symbol,
            'token_address': token_address,
            'total_completed_trades': total_trades,
            'win_rate_percent': round(win_rate, 2),
            'total_profit_loss_usd': round(total_profit_loss, 4),
            'total_return_rate_percent': round(total_return_rate, 2),
            'average_roi_percent': round(average_roi, 2),
            'max_profit_usd': round(max_profit, 4),
            'max_loss_usd': round(max_loss, 4),
            'avg_profit_usd': round(avg_profit, 4),
            'avg_loss_usd': round(avg_loss, 4),
            'current_holdings_usd': round(current_holdings, 4),
            'total_buy_trades': len([t for t in trades if t['event_type'] == 'buy']),
            'total_sell_trades': len([t for t in trades if t['event_type'] == 'sell'])
        })
    
    return results

def generate_summary_report(results):
    """生成汇总报告"""
    if not results:
        return "没有找到完整的交易数据"
    
    # 过滤有完成交易的token
    completed_results = [r for r in results if r['total_completed_trades'] > 0]
    
    if not completed_results:
        return "没有找到完成的交易对"
    
    total_trades = sum(r['total_completed_trades'] for r in completed_results)
    total_profit_loss = sum(r['total_profit_loss_usd'] for r in completed_results)
    
    # 计算整体胜率
    total_winning_trades = sum(r['total_completed_trades'] * r['win_rate_percent'] / 100 for r in completed_results)
    overall_win_rate = (total_winning_trades / total_trades) * 100 if total_trades > 0 else 0
    
    # 计算整体总收益率（加权平均）
    total_investment = sum([r['total_profit_loss_usd'] / (r['total_return_rate_percent'] / 100) if r['total_return_rate_percent'] != 0 else 0 for r in completed_results])
    overall_return_rate = (total_profit_loss / total_investment) * 100 if total_investment > 0 else 0
    
    # 最佳和最差表现的token
    best_performer = max(completed_results, key=lambda x: x['total_profit_loss_usd'])
    worst_performer = min(completed_results, key=lambda x: x['total_profit_loss_usd'])
    
    summary = f"""
=== KOL钱包交易表现汇总报告 ===

整体表现:
- 分析的代币数量: {len(results)}
- 有完成交易的代币数量: {len(completed_results)}
- 总完成交易次数: {total_trades}
- 整体胜率: {overall_win_rate:.2f}%
- 总盈亏: ${total_profit_loss:.4f}
- 整体总收益率: {overall_return_rate:.2f}%

最佳表现代币:
- 代币: {best_performer['token_symbol']}
- 总盈亏: ${best_performer['total_profit_loss_usd']}
- 总收益率: {best_performer['total_return_rate_percent']}%
- 胜率: {best_performer['win_rate_percent']}%
- 平均收益率: {best_performer['average_roi_percent']}%

最差表现代币:
- 代币: {worst_performer['token_symbol']}
- 总盈亏: ${worst_performer['total_profit_loss_usd']}
- 总收益率: {worst_performer['total_return_rate_percent']}%
- 胜率: {worst_performer['win_rate_percent']}%
- 平均收益率: {worst_performer['average_roi_percent']}%
"""
    
    return summary

def main():
    """主函数"""
    # 加载数据
    print("正在加载交易数据...")
    data = load_data('meme_monitor_backup.kol_wallet_activities.json')
    print(f"加载了 {len(data)} 条交易记录")
    
    # 分析数据
    print("正在分析交易表现...")
    results = analyze_token_performance(data)
    
    # 创建DataFrame便于查看
    df = pd.DataFrame(results)
    
    # 按总盈亏排序
    df_sorted = df.sort_values('total_profit_loss_usd', ascending=False)
    
    # 显示结果
    print("\n=== 详细交易表现分析 ===")
    pd.set_option('display.max_columns', None)
    pd.set_option('display.width', None)
    pd.set_option('display.max_colwidth', None)
    
    print(df_sorted.to_string(index=False))
    
    # 生成汇总报告
    summary = generate_summary_report(results)
    print(summary)
    
    # 保存结果到CSV
    df_sorted.to_csv('kol_trading_analysis_results.csv', index=False, encoding='utf-8')
    print("\n结果已保存到 'kol_trading_analysis_results.csv'")
    
    return df_sorted, results

if __name__ == "__main__":
    df_results, raw_results = main() 