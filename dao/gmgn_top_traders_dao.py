from typing import List, Optional, Dict, Any
from datetime import datetime
from models.gmgn_top_traders import GmgnTopTraders
from .base_dao import BaseDAO
from pymongo import InsertOne, UpdateOne, DESCENDING

class GmgnTopTradersDAO(BaseDAO[GmgnTopTraders]):
    """GMGN Top Traders数据访问对象"""
    
    def __init__(self):
        super().__init__(GmgnTopTraders)
    
    async def find_by_address(self, chain: str, address: str) -> Optional[GmgnTopTraders]:
        """根据链和地址获取交易者信息
        
        Args:
            chain: 链名称
            address: 钱包地址
            
        Returns:
            Optional[GmgnTopTraders]: 交易者信息
        """
        return await self.find_one({
            "chain": chain,
            "address": address
        })
    
    async def find_by_account_address(self, chain: str, account_address: str) -> Optional[GmgnTopTraders]:
        """根据链和账户地址获取交易者信息
        
        Args:
            chain: 链名称
            account_address: 账户地址
            
        Returns:
            Optional[GmgnTopTraders]: 交易者信息
        """
        return await self.find_one({
            "chain": chain,
            "account_address": account_address
        })
    
    async def find_by_wallet_tag(self, chain: str, wallet_tag: str) -> List[GmgnTopTraders]:
        """根据钱包标签获取交易者列表
        
        Args:
            chain: 链名称
            wallet_tag: 钱包标签
            
        Returns:
            List[GmgnTopTraders]: 交易者列表
        """
        return await self.find_many({
            "chain": chain,
            "wallet_tag_v2": wallet_tag
        })
    
    async def find_by_profit_range(
        self,
        chain: str,
        min_profit: float = 0,
        max_profit: Optional[float] = None,
        limit: int = 100
    ) -> List[GmgnTopTraders]:
        """获取指定利润范围内的交易者列表
        
        Args:
            chain: 链名称
            min_profit: 最小利润
            max_profit: 最大利润
            limit: 返回数量限制
            
        Returns:
            List[GmgnTopTraders]: 交易者列表
        """
        query = {
            "chain": chain,
            "profit": {"$gte": min_profit}
        }
        if max_profit is not None:
            query["profit"]["$lte"] = max_profit
            
        return await self.find_many(
            query,
            sort=[("profit", DESCENDING)],
            limit=limit
        )
    
    async def find_by_tags(
        self,
        chain: str,
        tags: List[str],
        match_all: bool = True
    ) -> List[GmgnTopTraders]:
        """根据标签获取交易者列表
        
        Args:
            chain: 链名称
            tags: 标签列表
            match_all: 是否需要匹配所有标签
            
        Returns:
            List[GmgnTopTraders]: 交易者列表
        """
        if match_all:
            query = {
                "chain": chain,
                "tags": {"$all": tags}
            }
        else:
            query = {
                "chain": chain,
                "tags": {"$in": tags}
            }
            
        return await self.find_many(query)
    
    async def find_active_traders(
        self,
        chain: str,
        since_timestamp: int,
        limit: int = 100
    ) -> List[GmgnTopTraders]:
        """获取活跃交易者列表
        
        Args:
            chain: 链名称
            since_timestamp: 起始时间戳
            limit: 返回数量限制
            
        Returns:
            List[GmgnTopTraders]: 交易者列表
        """
        return await self.find_many(
            {
                "chain": chain,
                "last_active_timestamp": {"$gte": since_timestamp}
            },
            sort=[("last_active_timestamp", DESCENDING)],
            limit=limit
        )
    
    async def find_by_holding_amount(
        self,
        chain: str,
        min_amount: float = 0,
        max_amount: Optional[float] = None,
        limit: int = 100
    ) -> List[GmgnTopTraders]:
        """获取指定持仓数量范围内的交易者列表
        
        Args:
            chain: 链名称
            min_amount: 最小持仓数量
            max_amount: 最大持仓数量
            limit: 返回数量限制
            
        Returns:
            List[GmgnTopTraders]: 交易者列表
        """
        query = {
            "chain": chain,
            "amount_cur": {"$gte": min_amount}
        }
        if max_amount is not None:
            query["amount_cur"]["$lte"] = max_amount
            
        return await self.find_many(
            query,
            sort=[("amount_cur", DESCENDING)],
            limit=limit
        )
    
    async def upsert_trader(self, chain: str, data: Dict[str, Any]) -> bool:
        """更新或插入交易者信息
        
        Args:
            chain: 链名称
            data: API返回的数据
            
        Returns:
            bool: 是否成功
        """
        # 创建新的交易者实例
        trader = await GmgnTopTraders.from_api_data(chain, data)
        
        # 使用原子更新操作
        return await self.update_one(
            {
                "chain": chain,
                "address": trader.address
            },
            trader.dict(),
            upsert=True
        )
    
    async def upsert_traders_many(
        self,
        chain: str,
        data_list: List[Dict[str, Any]]
    ) -> int:
        """批量更新或插入交易者信息
        
        Args:
            chain: 链名称
            data_list: API返回的数据列表
            
        Returns:
            int: 更新或插入的记录数
        """
        if not data_list:
            return 0
            
        # 准备批量更新操作
        bulk_operations = []
        
        for data in data_list:
            # 创建新的交易者实例
            trader = await GmgnTopTraders.from_api_data(chain, data)
            
            # 创建更新操作
            bulk_operations.append(
                InsertOne(
                    trader.dict()
                )
            )
        
        # 执行批量更新
        try:
            result = await self.collection.bulk_write(bulk_operations)
            self.logger.info(f"批量更新完成: 修改 {result.modified_count} 条, 插入 {result.upserted_count} 条")
            return result.modified_count + result.upserted_count
        except Exception as e:
            self.logger.error(f"批量更新交易者数据失败: {str(e)}")
            return 0
    
    async def delete_by_address(self, chain: str, address: str) -> bool:
        """根据链和地址删除交易者信息
        
        Args:
            chain: 链名称
            address: 钱包地址
            
        Returns:
            bool: 是否删除成功
        """
        return await self.delete_one({
            "chain": chain,
            "address": address
        })
    
    async def delete_inactive_traders(self, chain: str, before_timestamp: int) -> int:
        """删除不活跃的交易者
        
        Args:
            chain: 链名称
            before_timestamp: 截止时间戳
            
        Returns:
            int: 删除的记录数
        """
        result = await self.delete_many({
            "chain": chain,
            "last_active_timestamp": {"$lt": before_timestamp}
        })
        return result.deleted_count if result else 0 