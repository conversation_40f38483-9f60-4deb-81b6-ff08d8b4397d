import unittest
from utils.workflows.parallel_control import DeadlockDetector

class TestDeadlockDetector(unittest.TestCase):
    """DeadlockDetector类的单元测试"""

    def setUp(self):
        """测试前的准备工作"""
        self.detector = DeadlockDetector()

    def test_no_deadlock(self):
        """测试无死锁情况"""
        # 资源分配：A持有资源1，B持有资源2
        self.detector.add_allocation("resource1", "A")
        self.detector.add_allocation("resource2", "B")
        
        # A等待资源3，没有环形等待
        self.detector.add_waiting("A", "resource3")
        
        # 断言不存在死锁
        result = self.detector.detect_deadlock()
        self.assertIsNone(result, "不应该检测到死锁")

    def test_simple_deadlock(self):
        """测试简单的死锁情况"""
        # 资源分配：A持有资源1，B持有资源2
        self.detector.add_allocation("resource1", "A")
        self.detector.add_allocation("resource2", "B")
        
        # A等待资源2，B等待资源1，形成环形等待
        self.detector.add_waiting("A", "resource2")
        self.detector.add_waiting("B", "resource1")
        
        # 断言检测到死锁
        result = self.detector.detect_deadlock()
        self.assertIsNotNone(result, "应该检测到死锁")
        self.assertIn("resource1", result, "死锁应该包含资源1")
        self.assertIn("resource2", result, "死锁应该包含资源2")

    def test_complex_deadlock(self):
        """测试复杂的死锁情况"""
        # 资源分配：A持有资源1，B持有资源2，C持有资源3
        self.detector.add_allocation("resource1", "A")
        self.detector.add_allocation("resource2", "B")
        self.detector.add_allocation("resource3", "C")
        
        # A等待资源2，B等待资源3，C等待资源1，形成环形等待
        self.detector.add_waiting("A", "resource2")
        self.detector.add_waiting("B", "resource3")
        self.detector.add_waiting("C", "resource1")
        
        # 断言检测到死锁
        result = self.detector.detect_deadlock()
        self.assertIsNotNone(result, "应该检测到死锁")
        self.assertTrue({"resource1", "resource2", "resource3"}.issubset(result), 
                        "死锁应该包含所有三个资源")

    def test_partial_deadlock(self):
        """测试部分死锁情况"""
        # 资源分配：A持有资源1，B持有资源2，C持有资源3，D持有资源4
        self.detector.add_allocation("resource1", "A")
        self.detector.add_allocation("resource2", "B")
        self.detector.add_allocation("resource3", "C")
        self.detector.add_allocation("resource4", "D")
        
        # A等待资源2，B等待资源1，形成环形等待
        # C等待资源4，D不等待任何资源，没有形成环
        self.detector.add_waiting("A", "resource2")
        self.detector.add_waiting("B", "resource1")
        self.detector.add_waiting("C", "resource4")
        
        # 断言检测到死锁
        result = self.detector.detect_deadlock()
        self.assertIsNotNone(result, "应该检测到死锁")
        self.assertIn("resource1", result, "死锁应该包含资源1")
        self.assertIn("resource2", result, "死锁应该包含资源2")
        self.assertNotIn("resource3", result, "死锁不应该包含资源3")
        self.assertNotIn("resource4", result, "死锁不应该包含资源4")

    def test_deadlock_after_resource_release(self):
        """测试释放资源后的死锁消除"""
        # 设置死锁情况
        self.detector.add_allocation("resource1", "A")
        self.detector.add_allocation("resource2", "B")
        self.detector.add_waiting("A", "resource2")
        self.detector.add_waiting("B", "resource1")
        
        # 确认存在死锁
        result_before = self.detector.detect_deadlock()
        self.assertIsNotNone(result_before, "应该检测到死锁")
        
        # 释放资源
        self.detector.remove_allocation("resource1")
        self.detector.remove_waiting("B", "resource1")
        
        # 确认死锁已消除
        result_after = self.detector.detect_deadlock()
        self.assertIsNone(result_after, "释放资源后不应该检测到死锁")

    def test_multiple_deadlocks(self):
        """测试多个死锁情况"""
        # 第一个死锁：A <-> B
        self.detector.add_allocation("resource1", "A")
        self.detector.add_allocation("resource2", "B")
        self.detector.add_waiting("A", "resource2")
        self.detector.add_waiting("B", "resource1")
        
        # 第二个死锁：C <-> D
        self.detector.add_allocation("resource3", "C")
        self.detector.add_allocation("resource4", "D")
        self.detector.add_waiting("C", "resource4")
        self.detector.add_waiting("D", "resource3")
        
        # 断言检测到死锁（应该检测到其中一个）
        result = self.detector.detect_deadlock()
        self.assertIsNotNone(result, "应该检测到死锁")
        
        # 资源1和2应该同时出现，或者资源3和4应该同时出现
        self.assertTrue(
            ("resource1" in result and "resource2" in result) or
            ("resource3" in result and "resource4" in result),
            "应该检测到其中一个死锁"
        )

if __name__ == "__main__":
    unittest.main() 