import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
import json
import asyncio
import time

from utils.spiders.smart_money.gmgn_authenticated_spider import GmgnAuthenticatedSpider
from models.config import GmgnAccountConfig

logger = logging.getLogger(__name__)

class GmgnFollowingKolsSpider(GmgnAuthenticatedSpider):
    """GMGN关注的KOL账号列表爬虫
    
    用于获取用户关注的KOL账号列表信息，包括：
    - 地址信息
    - Twitter用户名和昵称
    - 头像
    - 收益数据
    - 余额信息
    - 活跃时间
    - 粉丝数量
    - 描述信息
    - 标签信息
    
    注意：此API需要认证才能访问，使用时必须提供有效的refresh_token
    """
    
    BASE_URL = "https://gmgn.ai/defi/quotation/v1/follow/sol/following_wallets"
    
    def __init__(self, account_config: GmgnAccountConfig, max_retries: int = 3, retry_interval: float = 1.0):
        """初始化爬虫
        
        Args:
            account_config: GMGN账户配置，包含refresh_token和device_id
            max_retries: 最大重试次数
            retry_interval: 重试间隔时间(秒)
        """
        # 如果没有提供refresh_token，则使用一个空字符串（后续会检查是否有效）
        super().__init__(
            refresh_token=account_config.refresh_token if account_config else "", 
            device_id=account_config.device_id if account_config else "", # 传递device_id
            max_retries=max_retries, 
            retry_interval=retry_interval
        )
        
        # 更新headers，添加特定于此API的头信息
        self.headers.update({
            'referer': 'https://gmgn.ai/follow/dDO6ZdYE?chain=sol&tab=follow',
            'priority': 'u=1, i',
        })
        
        # 标记是否提供了refresh_token
        self.has_refresh_token = account_config is not None and account_config.refresh_token != ""
        self.current_device_id = account_config.device_id if account_config else "" # 保存当前device_id
        
        # 增加调试日志
        if self.has_refresh_token:
            logger.info(f"初始化带认证的爬虫，refresh_token_value: {self.refresh_token_value[:10]}...")
        else:
            logger.info("初始化不带认证的爬虫")
    
    async def get_following_kols(self) -> Optional[Dict[str, Any]]:
        """
        获取用户关注的KOL账号列表
        
        Returns:
            Optional[Dict[str, Any]]: 关注的KOL账号列表信息
        """
        try:
            # 确保会话已初始化
            if not hasattr(self, 'session') or self.session is None:
                logger.info("初始化会话...")
                await self.setup()
            
            logger.info(f"正在获取用户关注的KOL账号列表: {self.BASE_URL}")
            
            # 设置请求参数
            params = {
                'device_id': self.current_device_id, # 使用当前实例的device_id
                'client_id': 'gmgn_web_20250510-908-8356458',
                'from_app': 'gmgn',
                'app_ver': '20250508-860-943999d',
                'tz_name': 'Asia/Shanghai',
                'tz_offset': '28800',
                'app_lang': 'zh-CN',
                'fp_did': '40adcc5a28fdc45901855725abbf74db',
                'os': 'web',
                'network': 'sol',
                'chain': 'sol'
            }
            
            # 根据是否有refresh_token决定使用哪种方式请求
            if self.has_refresh_token:
                # 使用认证请求
                logger.info("使用认证方式获取KOL账号列表")
                
                # 先检查当前token是否有效，只有在无效时才刷新
                if self.is_token_valid() and self.access_token:
                    logger.info(f"使用现有token, 过期时间: {self.expire_at} (当前时间: {int(time.time())})")
                    token = self.access_token
                else:
                    logger.info("当前token无效或不存在，获取新token")
                    # 获取一个有效的token
                    token = await self.get_valid_token()
                    if not token:
                        logger.error("无法获取有效的认证令牌")
                        return None
                
                # 手动添加认证头
                headers = self.session.headers.copy()
                headers['authorization'] = f'Bearer {token}'
                
                # 发送请求
                response = await self.get(self.BASE_URL, params=params, headers=headers)
                
                # 检查响应状态
                if response.status_code != 200:
                    logger.error(f"获取关注的KOL账号列表失败: HTTP {response.status_code}")
                    logger.error(f"错误响应: {response.text}")
                    return None
                
                # 解析响应数据
                try:
                    data = response.json()
                    
                    # 检查API响应格式
                    if not isinstance(data, dict):
                        logger.error(f"API响应格式错误，期望dict类型，实际为: {type(data)}")
                        return None
                    
                    # 检查API响应状态
                    if data.get('code') != self.SUCCESS_CODE:
                        # 检查是否是Token过期
                        if data.get('code') == self.TOKEN_EXPIRED_CODE:
                            logger.warning("Token已过期，尝试刷新...")
                            self.access_token = None  # 强制刷新
                            success = await self.refresh_token()
                            if success:
                                # 重新获取KOL列表
                                return await self.get_following_kols()
                            else:
                                logger.error("刷新Token失败")
                                return None
                        else:
                            logger.error(f"API返回错误: {data.get('msg', '未知错误')}")
                            return None
                    
                    # 提取实际数据
                    result = data.get('data', {})
                    if not result:
                        logger.warning("API响应中没有data字段")
                        return None
                    
                    logger.info(f"成功获取用户关注的KOL账号列表，共 {len(result.get('followings', []))} 个KOL")
                    return result
                
                except json.JSONDecodeError as e:
                    logger.error(f"解析API响应JSON失败: {str(e)}")
                    logger.error(f"原始响应: {response.text}")
                    return None
            else:
                # 非认证请求
                logger.info("使用非认证方式获取KOL账号列表")
                response = await self.get(self.BASE_URL, params=params)
                
                # 检查响应状态
                if response.status_code != 200:
                    logger.error(f"获取关注的KOL账号列表失败: HTTP {response.status_code}")
                    logger.error(f"错误响应: {response.text}")
                    return None
                
                # 解析响应数据
                try:
                    data = response.json()
                    
                    # 检查API响应格式
                    if not isinstance(data, dict):
                        logger.error(f"API响应格式错误，期望dict类型，实际为: {type(data)}")
                        return None
                    
                    # 检查API响应状态
                    if data.get('code') != self.SUCCESS_CODE:
                        logger.error(f"API返回错误: {data.get('msg', '未知错误')}")
                        return None
                    
                    # 提取实际数据
                    result = data.get('data', {})
                    if not result:
                        logger.warning("API响应中没有data字段")
                        return None
                    
                    logger.info(f"成功获取用户关注的KOL账号列表，共 {len(result.get('followings', []))} 个KOL")
                    return result
                
                except json.JSONDecodeError as e:
                    logger.error(f"解析API响应JSON失败: {str(e)}")
                    logger.error(f"原始响应: {response.text}")
                    return None
                
        except Exception as e:
            logger.error(f"获取用户关注的KOL账号列表失败: {str(e)}")
            return None
            
    def format_following_kols(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        格式化关注的KOL账号列表数据
        
        Args:
            data: 原始关注的KOL账号列表数据
            
        Returns:
            List[Dict[str, Any]]: 格式化后的KOL账号列表数据
        """
        try:
            if not data or 'followings' not in data:
                return []
                
            # 获取KOL账号列表
            followings = data.get('followings', [])
            
            # 构建格式化后的数据
            formatted_data = []
            for kol in followings:
                formatted_kol = {
                    "address": kol.get('address', ''),
                    "ens": kol.get('ens'),
                    "twitter_username": kol.get('twitter_username'),
                    "twitter_name": kol.get('twitter_name'),
                    "name": kol.get('name'),
                    "is_blue_verified": kol.get('is_blue_verified'),
                    "avatar": kol.get('avatar'),
                    "realized_profit_1d": kol.get('realized_profit_1d', 0),
                    "realized_profit_7d": kol.get('realized_profit_7d', 0),
                    "realized_profit_30d": kol.get('realized_profit_30d', 0),
                    "realized_pnl_30d": kol.get('realized_pnl_30d'),
                    "total_profit": kol.get('total_profit', 0),
                    "total_profit_pnl": kol.get('total_profit_pnl', 0),
                    "last_active_timestamp": kol.get('last_active_timestamp', 0),
                    "eth_balance": kol.get('eth_balance', 0),
                    "sol_balance": kol.get('sol_balance', 0),
                    "trx_balance": kol.get('trx_balance', 0),
                    "bnb_balance": kol.get('bnb_balance', 0),
                    "balance": kol.get('balance', 0),
                    "total_value": kol.get('total_value', 0),
                    "followers_count": kol.get('followers_count', 0),
                    "swaps_1d": kol.get('swaps_1d', 0),
                    "swaps_7d": kol.get('swaps_7d', 0),
                    "swaps_30d": kol.get('swaps_30d', 0),
                    "description": kol.get('description', ''),
                    "tag_rank": kol.get('tag_rank', {}),
                    "tags": kol.get('tags', []),
                    "is_sticky": kol.get('is_sticky', False),
                    "tg_alert_enabled": kol.get('tg_alert_enabled', False),
                    "fetched_at": datetime.now().isoformat()
                }
                formatted_data.append(formatted_kol)
            
            return formatted_data
        except Exception as e:
            logger.error(f"格式化关注的KOL账号列表数据时发生错误: {str(e)}")
            logger.error(f"原始数据: {data}")
            return []

async def main():
    try:
        # 用户提供的Bearer token 和 device_id
        # 假设我们从配置中获取了账户列表，这里为了测试直接定义
        accounts_data = [
            {"refresh_token": "eyJhbGciOiJFUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.OaXWADvqo37kFqgbQnzZJFc4OfOOkjQgm0oOf3Loxd4docm0U1DnHN_v9_p7Dy1S7ei3IHS8I33gjArmpjUXiw", 
             "device_id": "fd9a37a7-5a28-465d-ac86-8d269b3303f3"}
        ]

        # 创建 GmgnAccountConfig 实例
        account_config_to_use = GmgnAccountConfig(
            refresh_token=accounts_data[0]["refresh_token"],
            device_id=accounts_data[0]["device_id"]
        )
        
        # 处理Bearer token (refresh_token)
        # 注意：refresh_token 本身不是 Bearer token，Bearer token 是 access_token
        # 这里我们直接使用 refresh_token 初始化爬虫
        raw_refresh_token = account_config_to_use.refresh_token
        if raw_refresh_token.startswith("Bearer "):
            # 去掉"Bearer "前缀，获取真正的token部分 (虽然这通常用于access_token)
            # 对于refresh_token，通常不需要"Bearer "前缀，但以防万一处理一下
            cleaned_refresh_token = raw_refresh_token[7:]
        else:
            cleaned_refresh_token = raw_refresh_token
        
        # 更新 account_config_to_use 的 refresh_token (如果之前有 Bearer 前缀)
        account_config_to_use.refresh_token = cleaned_refresh_token

        print(f"初始化爬虫，使用refresh_token: {account_config_to_use.refresh_token[:30]}... 和 device_id: {account_config_to_use.device_id}")
        
        # 配置爬取次数和间隔，用于测试稳定性
        num_iterations = 30
        interval_seconds = 60
        save_to_file = True
        max_retries_per_iteration = 3  # 每次爬取最大重试次数
        retry_delay = 10  # 重试间隔时间(秒)
        
        # 记录整体统计信息
        success_count = 0
        failure_count = 0
        total_kols = 0
        
        # 循环多次爬取数据，测试稳定性
        for i in range(num_iterations):
            print(f"\n开始第 {i+1}/{num_iterations} 次爬取")
            
            # 每次爬取可能需要多次重试
            retry_count = 0
            while retry_count < max_retries_per_iteration:
                try:
                    async with GmgnFollowingKolsSpider(account_config=account_config_to_use, max_retries=2, retry_interval=1.5) as spider:
                        print(f"爬虫初始化完成，准备获取数据... (第 {retry_count+1} 次尝试)")
                        
                        # 获取数据
                        start_time = datetime.now()
                        data = await spider.get_following_kols()
                        end_time = datetime.now()
                        
                        # 计算请求耗时
                        duration = (end_time - start_time).total_seconds()
                        print(f"API请求耗时：{duration:.2f}秒")
                        
                        if data:
                            # 格式化数据
                            formatted_data = spider.format_following_kols(data)
                            kol_count = len(formatted_data)
                            total_kols += kol_count
                            success_count += 1
                            print(f"成功获取 {kol_count} 个KOL的信息")
                            
                            # 打印第一条记录
                            if formatted_data:
                                first_kol = formatted_data[0]
                                print("\n第一条KOL信息摘要：")
                                print(f"地址: {first_kol.get('address')}")
                                print(f"推特用户名: {first_kol.get('twitter_username')}")
                                print(f"名称: {first_kol.get('name')}")
                                print(f"余额: {first_kol.get('balance')}")
                                print(f"粉丝数: {first_kol.get('followers_count')}")
                                print(f"活跃时间: {first_kol.get('last_active_timestamp')}, type: {type(first_kol.get('last_active_timestamp'))}")
                            
                            # 保存结果到文件
                            if save_to_file:
                                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                                filename = f"gmgn_kols_{timestamp}_{i+1}.json"
                                with open(filename, "w", encoding="utf-8") as f:
                                    json.dump(formatted_data, f, ensure_ascii=False, indent=2)
                                print(f"数据已保存到文件: {filename}")
                            
                            # 爬取成功，跳出重试循环
                            break
                        else:
                            print(f"获取关注的KOL账号列表失败 (第 {retry_count+1} 次尝试)")
                            retry_count += 1
                            if retry_count < max_retries_per_iteration:
                                print(f"等待 {retry_delay} 秒后重试...")
                                await asyncio.sleep(retry_delay)
                            else:
                                failure_count += 1
                                print("已达到最大重试次数，跳过本次爬取")
                except Exception as e:
                    print(f"爬虫运行时发生错误 (第 {retry_count+1} 次尝试): {e}")
                    retry_count += 1
                    if retry_count < max_retries_per_iteration:
                        print(f"等待 {retry_delay} 秒后重试...")
                        await asyncio.sleep(retry_delay)
                    else:
                        failure_count += 1
                        print("已达到最大重试次数，跳过本次爬取")
                        import traceback
                        traceback.print_exc()
            
            # 不是最后一次爬取，等待一段时间
            if i < num_iterations - 1:
                print(f"等待 {interval_seconds} 秒后进行下一次爬取...")
                await asyncio.sleep(interval_seconds)
        
        # 打印统计信息
        print("\n爬取完成，统计信息：")
        print(f"总爬取次数: {num_iterations}")
        print(f"成功次数: {success_count}")
        print(f"失败次数: {failure_count}")
        print(f"总获取KOL数量: {total_kols}")
        print(f"平均每次获取KOL数量: {total_kols/success_count if success_count > 0 else 0:.2f}")
                
    except Exception as e:
        print(f"主程序运行时发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 设置详细的日志配置
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),  # 输出到控制台
            logging.FileHandler("gmgn_spider.log")  # 同时输出到文件
        ]
    )
    
    print("启动GMGN关注KOL列表爬虫测试程序...")
    print("将进行多次爬取测试，并将结果保存到JSON文件")
    
    # 使用asyncio运行异步主函数
    asyncio.run(main())
    
    print("爬虫测试程序执行完毕") 