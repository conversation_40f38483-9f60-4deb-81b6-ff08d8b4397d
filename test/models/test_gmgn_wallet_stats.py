"""
GMGN钱包统计数据模型单元测试

测试模块: models.gmgn_wallet_stats
功能: 验证GmgnWalletStats和GmgnRiskMetrics模型的正确性
"""

import unittest
from unittest.mock import patch, Mock
from datetime import datetime
from typing import Dict, Any
from pydantic import ValidationError

from models.gmgn_wallet_stats import GmgnWalletStats, GmgnRiskMetrics


@patch('models.gmgn_wallet_stats.GmgnWalletStats.get_motor_collection')
class TestGmgnWalletStats(unittest.TestCase):
    """测试GmgnWalletStats数据模型"""
    
    def setUp(self):
        """测试前置设置"""
        self.test_wallet_address = "7BgBvyjrZX1YKz4oh9mjb8ZScatkkwb8DzFx4sxXEP5P"
        self.test_api_data = {
            "buy": "15",
            "sell": "12", 
            "buy_1d": "2",
            "sell_1d": "1",
            "buy_7d": "8",
            "sell_7d": "6",
            "buy_30d": "15",
            "sell_30d": "12",
            "pnl": "1250.75",
            "pnl_1d": "125.50",
            "pnl_7d": "850.25", 
            "pnl_30d": "1250.75",
            "all_pnl": "2105.40",
            "balance": "5.25",
            "sol_balance": "3.15",
            "total_value": "2850.90",
            "winrate": "0.65",
            "avg_cost": "45.30",
            "avg_sold": "72.80",
            "realized_profit": "1100.50",
            "unrealized_profit": "150.25",
            "total_profit": "1250.75",
            "buy_volume_cur": "12500.00",
            "sell_volume_cur": "11200.00",
            "usd_value": "2850.90",
            "token_num": "8",
            "token_sold_avg_profit": "91.71",
            "risk": {
                "token_active": "2",
                "token_honeypot": "1", 
                "token_honeypot_ratio": 0.125,
                "no_buy_hold": "0",
                "no_buy_hold_ratio": 0.0,
                "sell_pass_buy": "1",
                "sell_pass_buy_ratio": 0.083,
                "fast_tx": "3",
                "fast_tx_ratio": 0.2
            },
            "name": "TestKOL",
            "avatar": "https://example.com/avatar.jpg",
            "twitter_username": "testkol",
            "twitter_name": "Test KOL"
        }

    def test_gmgn_risk_metrics_creation_with_defaults(self, mock_get_collection):
        """测试用例 1.1.1: 使用默认值创建风险指标模型"""
        risk = GmgnRiskMetrics()
        
        # 验证默认值（修复：这些字段应该是数字类型，不是字符串）
        self.assertEqual(risk.token_active, 0)
        self.assertEqual(risk.token_honeypot, 0)
        self.assertEqual(risk.token_honeypot_ratio, 0.0)
        self.assertEqual(risk.no_buy_hold, 0)
        self.assertEqual(risk.no_buy_hold_ratio, 0.0)
        self.assertEqual(risk.sell_pass_buy, 0)
        self.assertEqual(risk.sell_pass_buy_ratio, 0.0)
        self.assertEqual(risk.fast_tx, 0)
        self.assertEqual(risk.fast_tx_ratio, 0.0)

    def test_gmgn_risk_metrics_creation_with_data(self, mock_get_collection):
        """测试用例 1.1.2: 使用实际数据创建风险指标模型"""
        risk_data = self.test_api_data["risk"]
        risk = GmgnRiskMetrics(**risk_data)
        
        # 验证字段值（修复：这些字段应该是数字类型，Pydantic会自动将字符串转换为数字）
        self.assertEqual(risk.token_active, 2)
        self.assertEqual(risk.token_honeypot, 1)
        self.assertEqual(risk.token_honeypot_ratio, 0.125)
        self.assertEqual(risk.no_buy_hold, 0)
        self.assertEqual(risk.no_buy_hold_ratio, 0.0)
        self.assertEqual(risk.sell_pass_buy, 1)
        self.assertEqual(risk.sell_pass_buy_ratio, 0.083)
        self.assertEqual(risk.fast_tx, 3)
        self.assertEqual(risk.fast_tx_ratio, 0.2)

    def test_gmgn_wallet_stats_model_fields_validation(self, mock_get_collection):
        """测试用例 1.2.1: 模型基本字段验证"""
        # 直接检查模型字段定义，避免数据库连接
        model_fields = GmgnWalletStats.model_fields
        
        # 验证基本标识字段
        self.assertIn('wallet_address', model_fields)
        self.assertIn('chain', model_fields)
        self.assertIn('period', model_fields)
        
        # 验证交易统计字段
        self.assertIn('buy', model_fields)
        self.assertIn('sell', model_fields)
        self.assertIn('buy_1d', model_fields)
        self.assertIn('sell_1d', model_fields)
        
        # 验证收益指标字段
        self.assertIn('pnl', model_fields)
        self.assertIn('pnl_1d', model_fields)
        self.assertIn('all_pnl', model_fields)
        
        # 验证余额信息字段
        self.assertIn('balance', model_fields)
        self.assertIn('sol_balance', model_fields)
        self.assertIn('total_value', model_fields)

    def test_period_field_validation(self, mock_get_collection):
        """测试用例 1.2.2: period字段验证"""
        # 检查period字段的默认值
        period_field = GmgnWalletStats.model_fields['period']
        self.assertEqual(period_field.default, "all")

    def test_create_from_api_data_with_period_all(self, mock_get_collection):
        """测试用例 2.1.1: 从API数据创建模型实例(period=all)"""
        try:
            # 使用create_from_api_data方法
            stats = GmgnWalletStats.create_from_api_data(
                wallet_address=self.test_wallet_address,
                api_data=self.test_api_data,
                period="all"
            )
            
            # 验证基本字段
            self.assertEqual(stats.wallet_address, self.test_wallet_address)
            self.assertEqual(stats.chain, "sol")
            self.assertEqual(stats.period, "all")
            
            # 验证交易统计字段
            self.assertEqual(stats.buy, 15)
            self.assertEqual(stats.sell, 12)
            self.assertEqual(stats.buy_1d, 2)
            self.assertEqual(stats.sell_1d, 1)
            
            # 验证收益字段
            self.assertEqual(stats.pnl, 1250.75)
            self.assertEqual(stats.pnl_1d, 125.50)
            self.assertEqual(stats.all_pnl, 2105.40)
            
            # 验证余额字段（保持字符串格式，根据设计规格）
            self.assertEqual(stats.balance, "5.25")
            self.assertEqual(stats.sol_balance, "3.15")
            self.assertEqual(stats.total_value, 2850.90)
            
            # 验证交易绩效字段
            self.assertEqual(stats.winrate, 0.65)
            self.assertEqual(stats.avg_cost, 45.30)
            self.assertEqual(stats.avg_sold, 72.80)
            
            # 验证风险指标（修复：这些字段应该是数字类型）
            self.assertIsNotNone(stats.risk)
            self.assertEqual(stats.risk.token_active, 2)
            self.assertEqual(stats.risk.token_honeypot, 1)
            
            # 验证用户信息
            self.assertEqual(stats.name, "TestKOL")
            self.assertEqual(stats.twitter_username, "testkol")
            
            # 验证时间戳
            self.assertIsInstance(stats.created_at, datetime)
            self.assertIsInstance(stats.updated_at, datetime)
            
        except Exception as e:
            self.fail(f"Failed to create GmgnWalletStats from API data: {e}")

    def test_create_from_api_data_with_period_7d(self, mock_get_collection):
        """测试用例 2.1.2: 从API数据创建模型实例(period=7d)"""
        try:
            stats = GmgnWalletStats.create_from_api_data(
                wallet_address=self.test_wallet_address,
                api_data=self.test_api_data,
                period="7d"
            )
            
            # 验证period字段正确设置
            self.assertEqual(stats.period, "7d")
            self.assertEqual(stats.wallet_address, self.test_wallet_address)
            
        except Exception as e:
            self.fail(f"Failed to create GmgnWalletStats with period=7d: {e}")

    def test_create_from_api_data_missing_wallet_address(self, mock_get_collection):
        """测试用例 2.1.3: 缺少钱包地址时抛出异常"""
        with self.assertRaises(ValueError) as context:
            GmgnWalletStats.create_from_api_data(
                wallet_address="",
                api_data=self.test_api_data,
                period="all"
            )
        
        self.assertIn("wallet_address is required", str(context.exception))

    def test_create_from_api_data_with_minimal_data(self, mock_get_collection):
        """测试用例 2.1.4: 使用最小数据集创建模型"""
        minimal_data = {
            "buy": "5",
            "sell": "3",
            "pnl": "100.0",
            "balance": "1.0",
            "winrate": "0.6"
        }
        
        try:
            stats = GmgnWalletStats.create_from_api_data(
                wallet_address=self.test_wallet_address,
                api_data=minimal_data,
                period="all"
            )
            
            # 验证必需字段
            self.assertEqual(stats.wallet_address, self.test_wallet_address)
            self.assertEqual(stats.buy, 5)
            self.assertEqual(stats.sell, 3)
            self.assertEqual(stats.pnl, 100.0)
            
            # 验证可选字段的默认值
            self.assertIsNone(stats.buy_1d)
            self.assertIsNone(stats.sell_1d)
            self.assertIsNone(stats.risk)  # 修正字段名为risk
            
        except Exception as e:
            self.fail(f"Failed to create GmgnWalletStats with minimal data: {e}")

    def test_default_values(self, mock_get_collection):
        """测试用例 2.2.1: 验证模型默认值"""
        # 检查字段默认值
        model_fields = GmgnWalletStats.model_fields
        
        # 验证chain字段默认值
        chain_field = model_fields['chain']
        self.assertEqual(chain_field.default, "sol")
        
        # 验证period字段默认值
        period_field = model_fields['period']
        self.assertEqual(period_field.default, "all")

    def test_string_to_number_conversion(self, mock_get_collection):
        """测试用例 2.3.1: 字符串到数字的转换"""
        api_data_with_strings = {
            "buy": "25",
            "sell": "18",
            "pnl": "2500.50",
            "balance": "7.89",
            "winrate": "0.72"
        }
        
        try:
            stats = GmgnWalletStats.create_from_api_data(
                wallet_address=self.test_wallet_address,
                api_data=api_data_with_strings,
                period="all"
            )
            
            # 验证字符串正确转换为数字
            self.assertEqual(stats.buy, 25)
            self.assertEqual(stats.sell, 18)
            self.assertEqual(stats.pnl, 2500.50)
            self.assertEqual(stats.balance, "7.89")  # 余额保持字符串格式
            self.assertEqual(stats.winrate, 0.72)
            
        except Exception as e:
            self.fail(f"Failed to convert strings to numbers: {e}")


if __name__ == '__main__':
    unittest.main() 