# GMGN KOL钱包统计数据爬取功能测试用例设计

**创建日期**: 2025-06-03  
**功能模块**: workflows/gmgn_kol_wallet_stats  
**版本**: 0.1.0

## 1. 测试策略概述

### 1.1 测试目标
- 验证GMGN钱包统计API的正确调用和数据解析
- 确保数据映射和转换的准确性
- 验证独立数据表存储和关联查询功能
- 验证工作流的稳定性和容错能力
- 测试并发处理和性能表现

### 1.2 测试层次
- **单元测试**: 测试各个组件的独立功能
- **集成测试**: 测试组件间的协作和数据流
- **端到端测试**: 测试完整工作流的执行
- **性能测试**: 测试系统的性能和稳定性
- **关联查询测试**: 测试表间关联和聚合查询

## 2. 单元测试用例

### 2.1 数据模型测试 - GmgnWalletStats

#### 测试用例 2.1.1: 数据模型字段验证
**目标**: 验证GmgnWalletStats模型的字段定义和约束

**输入**: 完整的钱包统计数据
**预期输出**: 
- 所有字段都能正确赋值
- 类型转换正常工作
- 默认值设置正确
- 时间字段自动生成

**测试步骤**:
1. 创建GmgnWalletStats实例
2. 验证所有字段的类型和约束
3. 测试默认值和可选字段

#### 测试用例 2.1.2: 风险指标子模型验证
**目标**: 验证GmgnRiskMetrics嵌套模型的正确性

**输入**: 风险指标数据字典
**预期输出**: 
- 嵌套模型正确初始化
- 所有风险指标字段映射正确
- 比率计算准确

**测试步骤**:
1. 创建GmgnRiskMetrics实例
2. 验证所有风险指标字段
3. 测试嵌套在GmgnWalletStats中的行为

### 2.2 爬虫组件测试 - GmgnWalletStatsSpider

#### 测试用例 2.2.1: 请求参数生成
**目标**: 验证_generate_request_params方法生成正确的请求参数

**输入**: 无
**预期输出**: 
- 包含所有必需参数的字典
- device_id和client_id为随机生成的UUID格式
- tz_name为"Asia/Shanghai"，tz_offset为28800
- app_lang为"zh-CN"

**测试步骤**:
1. 创建爬虫实例
2. 调用_generate_request_params方法
3. 验证返回参数的完整性和格式

#### 测试用例 2.2.2: 数据解析功能
**目标**: 验证_parse_wallet_data方法正确解析API响应

**输入**: 模拟的GMGN API响应JSON数据
**预期输出**: 
- 正确映射所有字段到GmgnWalletStats模型
- 字符串类型的balance转换为float
- 时间戳转换为datetime对象
- risk对象正确解析为GmgnRiskMetrics模型

**测试步骤**:
1. 准备标准的API响应数据
2. 调用_parse_wallet_data方法
3. 验证每个字段的映射和类型转换

#### 测试用例 2.2.3: 错误响应处理
**目标**: 验证爬虫正确处理API错误响应

**输入**: 
- HTTP 404错误响应
- 无效JSON响应
- 缺少关键字段的响应

**预期输出**: 
- 抛出适当的异常或返回None
- 错误信息被正确记录
- 不会导致程序崩溃

**测试步骤**:
1. 模拟各种错误响应
2. 调用get_wallet_stats方法
3. 验证错误处理的正确性

### 2.3 DAO组件测试 - GmgnWalletStatsDAO

#### 测试用例 2.3.1: 查找需要更新的钱包地址
**目标**: 验证find_wallets_need_stats_update方法返回正确的钱包地址列表

**输入**: 
- limit=10
- hours_threshold=1

**预期输出**: 
- 返回最多10个钱包地址
- 所有地址对应的钱包最近1小时内未更新统计数据
- 优先返回活跃的KOL钱包地址
- 返回类型为字符串列表

**测试步骤**:
1. 准备kol_wallets和gmgn_wallet_stats测试数据
2. 调用方法并验证返回结果
3. 检查数量限制和时间过滤条件

#### 测试用例 2.3.2: 单个钱包统计数据upsert
**目标**: 验证upsert_wallet_stats方法的新增和更新功能

**输入**: 
- wallet_address: "test_address"
- stats_data: 完整的统计数据字典

**预期输出**: 
- 新增模式：如果地址不存在，创建新记录，返回True
- 更新模式：如果地址存在，更新统计数据，返回True
- updated_at字段更新为当前时间
- created_at字段在更新时保持不变

**测试步骤**:
1. 测试新增场景：使用不存在的钱包地址
2. 测试更新场景：使用已存在的钱包地址
3. 验证数据库中的数据变化

#### 测试用例 2.3.3: 批量upsert钱包数据
**目标**: 验证batch_upsert_wallet_stats方法的批量处理功能

**输入**: 包含5个钱包统计数据的列表（3个新增，2个更新）

**预期输出**: 
- 返回5表示成功处理5条记录
- 新增的钱包创建新记录
- 已存在的钱包更新统计数据
- 操作具有原子性

**测试步骤**:
1. 准备批量upsert数据（混合新增和更新）
2. 调用批量upsert方法
3. 验证所有记录的处理结果

#### 测试用例 2.3.4: 根据地址获取统计数据
**目标**: 验证get_wallet_stats_by_address方法的查询功能

**输入**: 
- 存在的钱包地址
- 不存在的钱包地址

**预期输出**: 
- 存在的地址返回GmgnWalletStats对象
- 不存在的地址返回None
- 返回的数据完整且正确

**测试步骤**:
1. 使用存在的钱包地址查询
2. 使用不存在的钱包地址查询
3. 验证返回结果的正确性

### 2.4 工作流处理器测试 - Handler

#### 测试用例 2.4.1: 钱包地址列表生成
**目标**: 验证generate_wallet_addresses函数返回正确的地址列表

**输入**: 无
**预期输出**: 
- 返回字符串列表
- 包含有效的钱包地址
- 数量符合预期范围
- 地址都在kol_wallets表中存在

**测试步骤**:
1. 准备测试数据库环境
2. 调用函数并验证返回结果
3. 检查地址格式和存在性

#### 测试用例 2.4.2: 钱包统计数据处理
**目标**: 验证process_wallet_stats函数正确处理单个钱包

**输入**: 有效的钱包地址

**预期输出**: 
- 返回包含统计数据的字典
- 数据格式符合GmgnWalletStats模型
- 处理时间在合理范围内
- 包含wallet_address字段

**测试步骤**:
1. 使用真实或模拟的钱包地址
2. 调用处理函数
3. 验证返回数据的完整性和准确性

#### 测试用例 2.4.3: 数据验证功能
**目标**: 验证validate_wallet_data函数正确验证数据

**输入**: 
- 有效的钱包统计数据
- 无效的数据（缺少wallet_address）
- 异常数据类型
- 钱包地址不在kol_wallets表中的数据

**预期输出**: 
- 有效数据返回True
- 无效数据返回False
- 不会抛出异常
- 关联验证正确工作

**测试步骤**:
1. 准备各种测试数据
2. 调用验证函数
3. 确认返回值的正确性

### 2.1 基本CRUD操作测试

**测试用例 2.1.1: 单个钱包统计数据新增测试**
- **输入**: 有效钱包地址、完整统计数据、指定period
- **期望**: 成功插入数据库，返回创建成功状态
- **验证点**: 数据完整性、时间戳自动设置、period字段正确保存

**测试用例 2.1.2: 多时间窗口数据存储测试**
- **输入**: 同一钱包地址，不同period值(all/7d/1d)的统计数据
- **期望**: 能够为同一钱包创建多条记录，每条记录对应不同时间窗口
- **验证点**: 复合索引(wallet_address, period)正常工作，数据不重复

## 3. 集成测试用例

### 3.1 爬虫与DAO集成测试

#### 测试用例 3.1.1: 端到端数据流测试
**目标**: 验证从爬取到存储的完整数据流

**输入**: 测试钱包地址列表

**预期输出**: 
- 成功爬取数据并存储到gmgn_wallet_stats表
- 数据一致性得到保证
- 错误处理机制正常工作
- 新增和更新模式都正确工作

**测试步骤**:
1. 初始化爬虫和DAO实例
2. 执行完整的爬取和存储流程
3. 验证gmgn_wallet_stats表中的数据正确性

### 3.2 数据关联验证测试

#### 测试用例 3.2.1: 表间关联完整性测试
**目标**: 验证kol_wallets和gmgn_wallet_stats表的关联关系

**输入**: 混合的钱包地址（部分在kol_wallets表中，部分不在）

**预期输出**: 
- 只有在kol_wallets表中存在的地址才能存储统计数据
- 关联验证正确阻止无效地址的存储
- 关联查询返回正确的结果

**测试步骤**:
1. 准备测试数据（包含有效和无效地址）
2. 尝试存储统计数据
3. 验证关联约束的执行

### 3.3 工作流引擎集成测试

#### 测试用例 3.3.1: 工作流节点协作测试
**目标**: 验证各个工作流节点的正确协作

**输入**: 工作流配置文件

**预期输出**: 
- 节点按正确顺序执行
- 数据在节点间正确传递
- 错误不会导致整个工作流崩溃
- 统计数据正确存储到独立表

**测试步骤**:
1. 启动完整工作流
2. 监控各节点的执行状态
3. 验证数据传递和存储的正确性

## 4. 端到端测试用例

### 4.1 完整工作流测试

#### 测试用例 4.1.1: 正常执行场景
**目标**: 验证工作流在正常情况下的完整执行

**输入**: 标准的工作流配置和数据环境

**预期输出**: 
- 工作流成功完成执行
- 所有待更新钱包的统计数据被正确存储到gmgn_wallet_stats表
- 性能指标符合预期
- 与kol_wallets表的关联关系正确

**测试步骤**:
1. 准备完整的测试环境
2. 启动工作流并监控执行过程
3. 验证最终结果和性能指标

#### 测试用例 4.1.2: 异常恢复场景
**目标**: 验证工作流在异常情况下的恢复能力

**输入**: 模拟网络中断、API限流等异常情况

**预期输出**: 
- 工作流能够从异常中恢复
- 未处理的数据不会丢失
- 错误信息被正确记录
- 数据表状态保持一致

**测试步骤**:
1. 在执行过程中引入各种异常
2. 观察工作流的反应和恢复过程
3. 验证数据完整性和错误处理

## 5. 关联查询测试用例

### 5.1 聚合查询功能测试

#### 测试用例 5.1.1: KOL基本信息与统计数据关联查询
**目标**: 验证通过wallet_address关联查询两表数据的功能

**输入**: 
- 有统计数据的KOL钱包地址
- 无统计数据的KOL钱包地址

**预期输出**: 
- 有统计数据的地址返回完整的关联结果
- 无统计数据的地址只返回KOL基本信息
- 查询性能在可接受范围内

**测试步骤**:
1. 准备测试数据（KOL信息和统计数据）
2. 执行关联查询
3. 验证查询结果的完整性和正确性

#### 测试用例 5.1.2: 收益排名聚合查询
**目标**: 验证基于统计数据的排名查询功能

**输入**: 按7天收益率排序的TOP 10查询请求

**预期输出**: 
- 返回10条记录（如果数据充足）
- 按pnl_7d降序排列
- 包含KOL基本信息和统计数据
- 只包含有KOL标签的钱包

**测试步骤**:
1. 准备多个钱包的测试数据
2. 执行聚合查询
3. 验证排序和过滤的正确性

### 5.2 索引性能测试

#### 测试用例 5.2.1: 关联查询性能测试
**目标**: 验证表关联查询的性能表现

**输入**: 1000个钱包地址的关联查询

**预期输出**: 
- 查询响应时间不超过5秒
- 索引被正确使用
- 内存使用在合理范围内

**测试步骤**:
1. 准备大量测试数据
2. 执行批量关联查询
3. 监控性能指标

## 6. 性能测试用例

### 6.1 并发性能测试

#### 测试用例 6.1.1: 并发处理能力测试
**目标**: 验证系统在高并发情况下的性能表现

**输入**: 100个钱包地址的并发处理请求

**预期输出**: 
- 处理时间不超过10分钟
- 内存使用稳定，无内存泄漏
- API请求成功率大于95%
- 数据存储成功率大于98%

**测试步骤**:
1. 准备大量测试数据
2. 配置并发参数并启动测试
3. 监控性能指标并记录结果

### 6.2 数据库性能测试

#### 测试用例 6.2.1: 批量upsert性能测试
**目标**: 验证批量数据操作的性能表现

**输入**: 批量处理500个钱包统计数据

**预期输出**: 
- 批量操作时间不超过30秒
- 事务成功率达到100%
- 数据库连接池正常工作
- 索引性能满足要求

**测试步骤**:
1. 准备大批量测试数据
2. 执行批量upsert操作
3. 监控数据库性能指标

### 6.3 负载测试

#### 测试用例 6.3.1: 长时间运行稳定性测试
**目标**: 验证系统长时间运行的稳定性

**输入**: 连续运行6小时的工作流

**预期输出**: 
- 系统运行稳定，无崩溃
- 内存使用保持在合理范围
- 处理效率不会显著下降
- 数据表增长正常

**测试步骤**:
1. 配置长时间运行的测试环境
2. 启动工作流并持续监控
3. 分析性能趋势和稳定性指标

## 7. 边界条件测试用例

### 7.1 数据边界测试

#### 测试用例 7.1.1: 空数据处理
**目标**: 验证系统对空数据的处理能力

**输入**: 
- 空的钱包地址列表
- API返回空数据
- kol_wallets表中无符合条件的记录

**预期输出**: 
- 不会产生错误或异常
- 系统能够优雅地处理空数据情况
- 记录适当的日志信息
- 工作流正常结束

#### 测试用例 7.1.2: 大数据量处理
**目标**: 验证系统处理大量数据的能力

**输入**: 1000个钱包地址的处理请求

**预期输出**: 
- 系统能够正常处理大数据量
- 内存使用保持稳定
- 处理时间在可接受范围内
- gmgn_wallet_stats表正确存储所有数据

#### 测试用例 7.1.3: 异常数据处理
**目标**: 验证系统对异常数据的容错能力

**输入**: 
- 包含null值的API响应
- 格式错误的时间戳
- 超出范围的数值

**预期输出**: 
- 异常数据被正确过滤或转换
- 不会影响其他正常数据的处理
- 错误信息被正确记录

## 8. 回归测试用例

### 8.1 兼容性测试

#### 测试用例 8.1.1: 现有功能兼容性
**目标**: 确保新功能不影响现有的KOL钱包功能

**输入**: 现有的KOL钱包操作流程

**预期输出**: 
- 所有现有功能正常工作
- kol_wallets表操作不受影响
- 接口行为保持一致
- 性能没有显著下降

**测试步骤**:
1. 执行现有功能的标准测试用例
2. 验证kol_wallets表的独立性
3. 确认API接口的稳定性

#### 测试用例 8.1.2: 数据隔离验证
**目标**: 验证两个数据表的独立性和隔离性

**输入**: 对两个表进行并发操作

**预期输出**: 
- kol_wallets表操作不影响gmgn_wallet_stats表
- gmgn_wallet_stats表操作不影响kol_wallets表
- 只有wallet_address字段用于关联
- 数据一致性得到保证

**测试步骤**:
1. 并发操作两个数据表
2. 验证数据隔离效果
3. 检查关联关系的正确性

### 8.2 数据模型字段验证测试

#### 测试用例 8.2.1: 模型基本字段验证
**目标**: 验证GmgnWalletStats对象的创建和字段类型

**输入**: 包含所有必需字段的完整数据
**期望**: 成功创建GmgnWalletStats对象，所有字段类型正确
**验证点**: wallet_address、chain、period等基本字段

**测试步骤**:
1. 创建GmgnWalletStats实例
2. 验证所有字段的类型和约束
3. 测试默认值和可选字段

#### 测试用例 8.2.2: period字段验证
**目标**: 验证period字段的语义正确性

**输入**: 
- `period="all"` - 总体统计数据
- `period="7d"` - 7天统计数据  
- `period="1d"` - 1天统计数据
**期望**: 不同period值都能正确保存和读取
**验证点**: period字段的语义正确性

**测试步骤**:
1. 创建GmgnWalletStats实例
2. 设置不同的period值
3. 验证period字段的存储和读取

**测试用例 8.2.3: 风险指标子模型验证**
- **输入**: 风险指标数据字典
- **期望**: 嵌套模型正确初始化，所有风险指标字段映射正确，比率计算准确
- **验证点**: 风险指标字段、比率计算

**测试步骤**:
1. 创建GmgnRiskMetrics实例
2. 验证所有风险指标字段
3. 测试嵌套在GmgnWalletStats中的行为 