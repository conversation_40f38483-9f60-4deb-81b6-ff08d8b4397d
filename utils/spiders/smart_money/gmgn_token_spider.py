from typing import Dict, Any
from urllib.parse import quote
import asyncio
from utils.spiders.smart_money import BasicSpider
import logging

logger = logging.getLogger(__name__)

class GmgnTokenSpider(BasicSpider):
    """GMGN代币爬虫
    
    用于获取GMGN热门代币数据
    """
    
    def __init__(self, max_retries: int = 3, retry_interval: float = 1.0):
        super().__init__(max_retries=max_retries, retry_interval=retry_interval)
        self.base_url = "https://gmgn.ai"
        self.headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-TW;q=0.6',
            'cache-control': 'no-cache',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://gmgn.ai/?chain=sol&ref=dDO6ZdYE',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36'
        }
    
    async def setup(self):
        """设置爬虫，初始化会话并更新头信息"""
        await self.init_sessions()
        # 更新会话头信息
        self.session.headers.update(self.headers)
        # 设置浏览器模拟
        self.session.impersonate = "chrome110"  # 模拟Chrome浏览器
        
    async def get_hot_tokens(self, time_range: str = "5m") -> Dict[str, Any]:
        """
        获取GMGN热门代币数据
        
        Args:
            time_range: 时间范围，可选值：5m, 1h, 24h, 7d
            
        Returns:
            Dict[str, Any]: API返回的数据
        """
            
        url = f"{self.base_url}/defi/quotation/v1/rank/sol/swaps/{time_range}"
        
        params = {
            'device_id': '65990bc3-0a13-4d99-8c66-eeea94916b0f',
            'client_id': 'gmgn_web_2025.0227.180046',
            'from_app': 'gmgn',
            'app_ver': '2025.0227.180046',
            'tz_name': 'Asia/Shanghai',
            'tz_offset': '28800',
            'app_lang': 'zh-CN',
            'orderby': 'smart_degen_count',
            'direction': 'desc',
            'filters[]': ['renounced', 'frozen']
        }
        
        try:
            logger.info(f"正在获取GMGN热门代币数据: {url}")
            # 使用带有重试机制的get方法
            response = await self.get(url, params=params)
            
            # 记录响应状态和内容
            logger.debug(f"API响应状态码: {response.status_code}")
            logger.debug(f"API响应内容: {response.text[:500]}...")  # 只记录前500个字符
            
            # 检查响应状态
            if response.status_code != 200:
                logger.error(f"获取热门代币数据失败: HTTP {response.status_code}")
                logger.error(f"错误响应: {response.text}")
                return {}
                
            data = response.json()
            
            # 检查API响应格式
            if not isinstance(data, dict):
                logger.error(f"API响应格式错误，期望dict类型，实际为: {type(data)}")
                return {}
                
            # 检查API响应状态
            if data.get('code') != 0:  # 0 表示成功
                logger.error(f"API返回错误: {data.get('message', '未知错误')}")
                return {}
                
            logger.info(f"成功获取GMGN热门代币数据")
            return data
            
        except Exception as e:
            logger.error(f"获取热门代币数据时发生错误: {str(e)}")
            return {}

async def main():
    spider = GmgnTokenSpider()
    result = await spider.get_hot_tokens()
    print(result)
    # 关闭爬虫会话连接
    await spider.close()

if __name__ == "__main__":
    # 使用asyncio运行异步主函数
    asyncio.run(main()) 