# Solana直接交易服务使用指南

## 概述

SolanaDirectTradeService是一个全新的交易接口实现，允许项目直接与Solana区块链交互进行交易，完全绕过GMGN API，彻底解决Cloudflare反爬虫保护问题。

### 技术优势
- **彻底解决Cloudflare问题**：不再依赖GMGN API，无反爬虫风险
- **更好的技术控制**：完全掌控交易流程和错误处理  
- **更低的成本**：去除中间商，减少手续费
- **更高的可靠性**：直接与区块链交互，减少单点故障

## 配置方式

### 1. 策略配置

在策略配置中添加以下字段来启用直接Solana交易：

```python
{
    # 启用直接Solana交易（关键配置）
    "use_direct_solana_trading": True,
    
    # Jupiter聚合器API配置
    "jupiter_api_host": "https://quote-api.jup.ag",
    
    # Solana RPC节点配置
    "solana_rpc_url": "https://api.mainnet-beta.solana.com",
    
    # 买入交易配置
    "max_slippage_bps": 100,           # 1%滑点
    "priority_fee_lamports": 50000,    # 优先费
    
    # 卖出交易配置  
    "sell_max_slippage_bps": 150,      # 1.5%滑点
    "sell_priority_fee_lamports": 75000, # 更高优先费
    
    # 超时配置
    "solana_trade_timeout_seconds": 60.0,  # 交易确认超时
    "http_timeout_seconds": 30.0,          # HTTP请求超时
}
```

### 2. 环境变量

确保钱包私钥环境变量正确设置：
```bash
# 示例：默认钱包私钥
export GMM_DEFAULT_WALLET_PK="your_base58_private_key_here"
```

## 使用方式

### 自动切换

系统会根据配置自动选择交易服务：

```python
# 在handler.py中的自动选择逻辑
use_direct_solana = strategy_snapshot.get('use_direct_solana_trading', False)

if use_direct_solana:
    from utils.solana_direct_trade_service import create_solana_direct_trade_service
    trade_service = create_solana_direct_trade_service(strategy_snapshot)
    print("✅ 使用SolanaDirectTradeService")
else:
    trade_service = GmgnTradeService(gmgn_api_host=gmgn_api_host)
    print("📡 使用GmgnTradeService")
```

### 手动创建服务

如果需要手动创建和使用服务：

```python
from utils.solana_direct_trade_service import create_solana_direct_trade_service
from utils.trade_interface import TradeType

# 创建服务
config = {
    'jupiter_api_host': 'https://quote-api.jup.ag',
    'solana_rpc_url': 'https://api.mainnet-beta.solana.com',
    'max_slippage_bps': 100,
    'priority_fee_lamports': 50000
}

service = create_solana_direct_trade_service(config)

try:
    # 执行交易
    result = await service.execute_trade(
        trade_type=TradeType.BUY,
        input_token_address="So11111111111111111111111111111111111111112",  # SOL
        output_token_address="EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",  # USDC示例
        amount_input_token=0.1,  # 0.1 SOL
        wallet_private_key_b58="your_private_key",
        wallet_address="your_wallet_address", 
        strategy_snapshot=config,
        signal_id=signal_id,
        trade_record_id=trade_record_id
    )
    
    if result.status == TradeStatus.SUCCESS:
        print(f"✅ 交易成功: {result.tx_hash}")
        print(f"花费: {result.actual_amount_in} lamports")
        print(f"获得: {result.actual_amount_out} 代币单位")
    else:
        print(f"❌ 交易失败: {result.error_message}")
        
finally:
    # 清理资源
    await service.close()
```

## 技术实现原理

### 1. 交易流程

```
1. 获取Jupiter报价 → 2. 构建交易 → 3. 本地签名 → 4. 发送到Solana → 5. 确认状态
     ↓                    ↓              ↓             ↓               ↓
Jupiter API v6      Jupiter Swap API   solders库    Solana RPC    轮询确认
```

### 2. 核心组件

- **Jupiter聚合器**：获取最佳交易路由和价格
- **Solana Python SDK**：处理交易签名和发送
- **HTTP客户端**：与Jupiter API和Solana RPC通信
- **错误处理**：完整的异常捕获和重试机制

### 3. 安全特性

- **本地签名**：私钥永不离开本地环境
- **交易验证**：发送前验证交易格式
- **超时保护**：防止交易卡死
- **详细日志**：完整的操作审计记录

## 配置参数详解

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `use_direct_solana_trading` | bool | False | 是否启用直接Solana交易 |
| `jupiter_api_host` | str | "https://quote-api.jup.ag" | Jupiter聚合器API地址 |
| `solana_rpc_url` | str | "https://api.mainnet-beta.solana.com" | Solana RPC节点 |
| `max_slippage_bps` | int | 100 | 买入最大滑点（基点，100=1%） |
| `priority_fee_lamports` | int | 50000 | 买入优先费（Lamports） |
| `sell_max_slippage_bps` | int | 150 | 卖出最大滑点（基点） |
| `sell_priority_fee_lamports` | int | 75000 | 卖出优先费（Lamports） |
| `solana_trade_timeout_seconds` | float | 60.0 | 交易确认超时（秒） |
| `http_timeout_seconds` | float | 30.0 | HTTP请求超时（秒） |

## 监控和日志

### 日志级别

```python
# 关键信息日志
logger.info(f"[TradeRec:{trade_record_id}] Executing Solana direct trade")

# 调试日志  
logger.debug(f"[TradeRec:{trade_record_id}] Wallet keypair created successfully")

# 错误日志
logger.error(f"[TradeRec:{trade_record_id}] Trade FAILED: {error}")
```

### 监控指标

- **交易成功率**：SUCCESS vs FAILED的比例
- **交易确认时间**：从发送到确认的耗时
- **Jupiter API响应时间**：获取报价和构建交易的耗时
- **失败原因分析**：私钥错误、网络超时、滑点过大等

## 故障排除

### 常见错误

1. **"Invalid wallet private key"**
   - 检查环境变量是否正确设置
   - 确认私钥格式为Base58编码

2. **"Jupiter API error"**
   - 检查网络连接
   - 验证代币地址有效性
   - 确认滑点设置合理

3. **"Transaction confirmation timeout"**
   - 检查Solana网络状况
   - 增加优先费提高交易优先级
   - 调整确认超时时间

### 性能优化

1. **优先费设置**：根据网络拥堵情况调整
2. **RPC节点选择**：使用高性能的付费RPC节点
3. **滑点配置**：根据代币流动性调整合理滑点

## 升级迁移

### 从GMGN迁移

1. **配置更新**：添加 `use_direct_solana_trading: true`
2. **测试验证**：先用小额交易测试
3. **监控观察**：观察交易成功率和确认时间
4. **逐步切换**：确认稳定后全量切换

### 回退方案

如需回退到GMGN API：
```python
{
    "use_direct_solana_trading": False,  # 设置为False即可回退
    # 其他GMGN配置保持不变
}
```

## 总结

Solana直接交易服务为项目提供了：
- ✅ **技术独立性**：摆脱第三方API依赖
- ✅ **成本优化**：降低交易手续费
- ✅ **可靠性提升**：直接与区块链交互
- ✅ **问题彻底解决**：完全绕过Cloudflare保护

这个解决方案不仅解决了当前的技术问题，还为项目的长期发展奠定了坚实的技术基础。 