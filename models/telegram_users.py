from beanie import Document, Indexed
from datetime import datetime
from typing import Optional, Dict


class TelegramUser(Document):
    """Telegram用户模型"""
    
    # 用户ID
    chat_id: Indexed(str, unique=True)
    
    # 创建时间
    created_at: datetime = datetime.utcnow()
    
    class Settings:
        name = "telegram_users"
        indexes = [
            "chat_id"
        ]
        
    @classmethod
    async def from_api_data(cls, data: Dict) -> 'TelegramUser':
        """从API数据创建模型实例"""
        return cls(
            chat_id=data.get('chat_id'),
            created_at=datetime.now() # 使用UTC时间
        )
