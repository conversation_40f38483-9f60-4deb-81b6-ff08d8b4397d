"""
GMGN代币监控工作流处理函数模块

提供YAML配置中引用的处理函数
"""

import logging
import traceback
from typing import List, Dict, Any, Optional
from datetime import datetime

from utils.spiders.smart_money.gmgn_token_spider import GmgnTokenSpider
from dao.gmgn_token_dao import GmgnTokenDAO

# 创建日志记录器
logger = logging.getLogger("GmgnTokenMonitorHandler")

# 创建爬虫和DAO实例
spider = GmgnTokenSpider()
token_dao = GmgnTokenDAO()

# 监控的时间范围
TIME_RANGES = ["1m", "5m", "1h", "6h", "24h"]

def safe_float(value, default=0.0):
    """安全地转换值为浮点数"""
    try:
        if value is None:
            return default
        return float(value)
    except (ValueError, TypeError):
        return default

def safe_int(value, default=0):
    """安全地转换值为整数"""
    try:
        if value is None:
            return default
        return int(value)
    except (ValueError, TypeError):
        return default

async def generate_time_range() -> Optional[Dict]:
    """
    生成当前要处理的时间范围
    
    Returns:
        Dict: 包含时间范围的字典
    """
    
    try:
        return [{"time_range": time_range} for time_range in TIME_RANGES]
    except Exception as e:
        logger.error(f"生成时间范围时发生错误: {str(e)}\n{traceback.format_exc()}")
        return None

async def process_token_data(time_range_data: Dict) -> Optional[List[Dict]]:
    """
    处理指定时间范围的代币数据
    
    Args:
        time_range_data: 包含时间范围的字典
        
    Returns:
        List[Dict]: 处理后的代币数据列表
    """
    try:
        logger.info(f"开始处理时间范围: {time_range_data}")
        time_range = time_range_data.get("time_range")
        if not time_range:
            logger.error("缺少时间范围参数")
            return None
            
        logger.info(f"开始获取{time_range}时间范围的代币数据...")
        
        # 调用爬虫获取数据
        result = await spider.get_hot_tokens(time_range=time_range)
        
        if not result or result.get("code") != 0:
            logger.warning(f"获取{time_range}数据失败或数据无效: {result}")
            return None
        
        # 处理返回的数据
        tokens = result.get("data", {}).get("rank", [])
        
        if not tokens:
            logger.warning(f"没有找到{time_range}的代币数据")
            return None
        
        processed_tokens = []
        
        # 处理每个代币的数据
        for token in tokens:
            try:
                # 构建标准化的代币数据
                token_data = {
                    # 基本信息
                    "id": safe_int(token.get("id")),
                    "chain": token.get("chain", "sol"),
                    "address": token.get("address", ""),
                    "symbol": token.get("symbol", ""),
                    "logo": token.get("logo"),
                    
                    # 价格和市场数据
                    "price": safe_float(token.get("price")),
                    "price_change_percent": safe_float(token.get("price_change_percent")),
                    "price_change_percent1m": safe_float(token.get("price_change_percent1m")),
                    "price_change_percent5m": safe_float(token.get("price_change_percent5m")),
                    "price_change_percent1h": safe_float(token.get("price_change_percent1h")),
                    
                    # 交易数据
                    "swaps": safe_int(token.get("swaps")),
                    "volume": safe_float(token.get("volume")),
                    "liquidity": safe_float(token.get("liquidity")),
                    "market_cap": safe_float(token.get("market_cap")),
                    "buys": safe_int(token.get("buys")),
                    "sells": safe_int(token.get("sells")),
                    
                    # 代币信息
                    "hot_level": safe_int(token.get("hot_level")),
                    "pool_creation_timestamp": safe_int(token.get("pool_creation_timestamp")),
                    "holder_count": safe_int(token.get("holder_count")),
                    "total_supply": safe_float(token.get("total_supply")),
                    "open_timestamp": safe_int(token.get("open_timestamp")),
                    "initial_liquidity": safe_float(token.get("initial_liquidity")),
                    
                    # 社交媒体信息
                    "twitter_username": token.get("twitter_username"),
                    "website": token.get("website"),
                    "telegram": token.get("telegram"),
                    
                    # 安全相关
                    "is_show_alert": bool(token.get("is_show_alert")),
                    "top_10_holder_rate": safe_float(token.get("top_10_holder_rate")),
                    "renounced_mint": safe_int(token.get("renounced_mint")),
                    "renounced_freeze_account": safe_int(token.get("renounced_freeze_account")),
                    "burn_ratio": token.get("burn_ratio"),
                    "burn_status": token.get("burn_status"),
                    "dev_token_burn_amount": safe_float(token.get("dev_token_burn_amount")),
                    "dev_token_burn_ratio": safe_float(token.get("dev_token_burn_ratio")),
                    
                    # 其他标记
                    "dexscr_ad": safe_int(token.get("dexscr_ad")),
                    "dexscr_update_link": safe_int(token.get("dexscr_update_link")),
                    "cto_flag": safe_int(token.get("cto_flag")),
                    "twitter_change_flag": safe_int(token.get("twitter_change_flag")),
                    "creator_token_status": token.get("creator_token_status"),
                    "creator_close": bool(token.get("creator_close")),
                    "launchpad_status": safe_int(token.get("launchpad_status")),
                    
                    # 交易者相关
                    "rat_trader_amount_rate": safe_float(token.get("rat_trader_amount_rate")),
                    "bluechip_owner_percentage": safe_float(token.get("bluechip_owner_percentage")),
                    "smart_degen_count": safe_int(token.get("smart_degen_count")),
                    "renowned_count": safe_int(token.get("renowned_count")),
                    "is_wash_trading": bool(token.get("is_wash_trading")),
                    
                    "time_range": time_range
                }
                
                processed_tokens.append(token_data)
                logger.info(f"已处理代币数据: {token_data['symbol']} ({time_range})")
                
            except Exception as e:
                error_msg = f"处理代币数据时发生错误: {str(e)}\n{traceback.format_exc()}"
                logger.error(error_msg)
                continue
        
        logger.info(f"已处理 {len(processed_tokens)} 个代币数据 ({time_range})")
        return processed_tokens
        
    except Exception as e:
        logger.error(f"处理代币数据时发生错误: {str(e)}\n{traceback.format_exc()}")
        return None

async def validate_token_data(data: Any) -> bool:
    """
    验证代币数据的有效性
    
    Args:
        data: 要验证的数据
        
    Returns:
        bool: 数据是否有效
    """
    try:
        if not isinstance(data, list):
            data = [data]
            
        if not data:
            logger.warning("数据列表为空")
            return False
            
        # 检查每个代币数据是否包含必要字段
        required_fields = ["id", "address", "symbol", "time_range"]
        
        for token in data:
            if not all(field in token for field in required_fields):
                missing = [field for field in required_fields if field not in token]
                logger.error(f"代币数据缺少必要字段: {missing}")
                return False
                
        return True
        
    except Exception as e:
        logger.error(f"验证代币数据时发生错误: {str(e)}\n{traceback.format_exc()}")
        return False

async def store_token_data(data_list: List[Dict]) -> int:
    """
    存储代币数据到数据库
    
    Args:
        data_list: 代币数据列表
        
    Returns:
        int: 成功存储的数据数量
    """
    try:
        if not data_list:
            logger.warning("没有数据需要存储")
            return 0
        
        if not isinstance(data_list, list):
            data_list = [data_list]
            
        # 按时间范围分组
        time_range_data = {}
        for token in data_list:
            time_range = token.get("time_range")
            if not time_range:
                logger.warning(f"代币数据缺少时间范围: {token.get('symbol')}")
                continue
                
            if time_range not in time_range_data:
                time_range_data[time_range] = []
                
            time_range_data[time_range].append(token)
        
        total_updated = 0
        
        # 按时间范围批量更新数据
        for time_range, tokens in time_range_data.items():
            if tokens:
                try:
                    # 使用 DAO 批量更新代币数据
                    update_count = await token_dao.bulk_upsert_tokens(tokens, time_range)
                    logger.info(f"成功更新 {update_count} 个代币数据到 {time_range} 时间段")
                    total_updated += update_count
                except Exception as e:
                    logger.error(f"批量更新代币数据到 {time_range} 时间段失败: {str(e)}\n{traceback.format_exc()}")
        
        return total_updated
        
    except Exception as e:
        logger.error(f"存储代币数据时发生错误: {str(e)}\n{traceback.format_exc()}")
        return 0 