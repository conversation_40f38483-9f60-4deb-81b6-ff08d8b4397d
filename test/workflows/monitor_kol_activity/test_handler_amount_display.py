import unittest
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime
from typing import Dict, Any

from workflows.monitor_kol_activity.handler import send_message_to_channel
from models.trade_execution import TradeExecutionResult, TradeStatus, ChannelAttemptResult


class TestHandlerAmountDisplay(unittest.TestCase):
    """测试handler中交易金额显示的修复"""

    def setUp(self):
        """设置测试环境"""
        self.token_info = {
            'address': 'MUbEZ6mDVy39FrjNCQPyX3PM7CwSM3hQ754ii4wpump',
            'name': 'Grow a garden',
            'symbol': 'grow',
            'decimals': 6,  # 假设这个token有6位小数
            'hit_kol_wallets': []
        }
        
        self.strategy_snapshot = {
            'strategy_name': '测试',
            'buy_amount_sol': 0.001,
            'buy_slippage_percentage': 1.0,
            'same_token_notification_interval': 60
        }

    @patch('workflows.monitor_kol_activity.handler.TelegramUserDAO')
    @patch('workflows.monitor_kol_activity.handler.SignalDAO')
    @patch('workflows.monitor_kol_activity.handler.get_auto_trade_manager')
    @patch('workflows.monitor_kol_activity.handler.TelegramMessageSender')
    @patch('workflows.monitor_kol_activity.handler.TokenMessageSendHistoryDAO')
    async def test_successful_trade_amount_display(
        self,
        mock_history_dao_class,
        mock_message_sender_class,
        mock_get_auto_trade_manager,
        mock_signal_dao_class,
        mock_user_dao_class
    ):
        """测试成功交易时的金额显示"""
        
        # 设置mock对象
        mock_user_dao = Mock()
        mock_user_dao.get_all_users = AsyncMock(return_value=[{'chat_id': '123456789'}])
        mock_user_dao_class.return_value = mock_user_dao
        
        mock_signal_dao = Mock()
        mock_signal_dao.collection.find_one = AsyncMock(return_value=None)  # 没有现有信号
        mock_signal_dao_class.return_value = mock_signal_dao
        
        mock_history_dao = Mock()
        mock_history_dao.insert_one = AsyncMock(return_value=True)
        mock_history_dao_class.return_value = mock_history_dao
        
        mock_message_sender = Mock()
        mock_message_sender.send_message_to_user = AsyncMock(return_value=True)
        mock_message_sender_class.return_value = mock_message_sender
        
        # 创建模拟的Signal对象
        mock_signal = Mock()
        mock_signal.id = "507f1f77bcf86cd799439011"
        mock_signal.trade_record_ids = []
        mock_signal.save = AsyncMock()
        
        # 创建成功的交易执行结果
        successful_attempt = ChannelAttemptResult(
            channel_type="gmgn",
            attempt_number=1,
            status=TradeStatus.SUCCESS,
            tx_hash="BQUbMmrLMiaxkwCSD8VRbkJdUYre1or6A19Pkd6KXPGCqcUGMYU6YVKHR59XxcTfuBiMxYzyJYyxASzC6f7Ke1c",
            error_message=None,
            execution_time=2.5,
            started_at=datetime.now(),
            completed_at=datetime.now(),
            actual_amount_in=1000000,  # 0.001 SOL in lamports (1000000 lamports = 0.001 SOL)
            actual_amount_out=1500000  # 1.5 tokens in smallest unit (1500000 / 10^6 = 1.5 tokens)
        )
        
        execution_result = TradeExecutionResult(
            final_status=TradeStatus.SUCCESS,
            successful_channel="gmgn",
            final_trade_record_id="507f1f77bcf86cd799439012",
            channel_attempts=[successful_attempt],
            total_execution_time=2.5,
            error_summary=None,
            started_at=datetime.now(),
            completed_at=datetime.now()
        )
        
        # 设置AutoTradeManager mock
        mock_auto_trade_manager = Mock()
        mock_auto_trade_manager.config_manager.is_enabled = AsyncMock(return_value=True)
        mock_auto_trade_manager.execute_trade = AsyncMock(return_value=execution_result)
        mock_get_auto_trade_manager.return_value = mock_auto_trade_manager
        
        # 准备测试数据
        test_data = [{
            'token_data': self.token_info,
            'strategy_config_snapshot': self.strategy_snapshot
        }]
        
        # 使用patch来模拟Signal的创建
        with patch('workflows.monitor_kol_activity.handler.Signal', return_value=mock_signal):
            # 执行测试
            result = await send_message_to_channel(test_data)
            
            # 验证结果
            self.assertTrue(result)
            
            # 验证消息发送被调用
            mock_message_sender.send_message_to_user.assert_called_once()
            
            # 获取发送的消息内容
            call_args = mock_message_sender.send_message_to_user.call_args
            sent_message = call_args[0][0]  # 第一个参数是消息内容
            
            # 验证消息内容包含正确的金额显示
            self.assertIn("Trade Status: ✅ SUCCESS", sent_message)
            self.assertIn("Amount Bought: 1.500000 grow", sent_message)  # 1500000 / 10^6 = 1.5
            self.assertIn("Amount Spent: 0.001000000 SOL", sent_message)  # 1000000 / 10^9 = 0.001
            self.assertIn("Tx Hash:", sent_message)
            self.assertIn("BQUb...Ke1c", sent_message)

    @patch('workflows.monitor_kol_activity.handler.TelegramUserDAO')
    @patch('workflows.monitor_kol_activity.handler.SignalDAO')
    @patch('workflows.monitor_kol_activity.handler.get_auto_trade_manager')
    @patch('workflows.monitor_kol_activity.handler.TelegramMessageSender')
    @patch('workflows.monitor_kol_activity.handler.TokenMessageSendHistoryDAO')
    async def test_trade_with_ui_format_amounts(
        self,
        mock_history_dao_class,
        mock_message_sender_class,
        mock_get_auto_trade_manager,
        mock_signal_dao_class,
        mock_user_dao_class
    ):
        """测试当交易金额已经是UI格式时的显示"""
        
        # 设置mock对象
        mock_user_dao = Mock()
        mock_user_dao.get_all_users = AsyncMock(return_value=[{'chat_id': '123456789'}])
        mock_user_dao_class.return_value = mock_user_dao
        
        mock_signal_dao = Mock()
        mock_signal_dao.collection.find_one = AsyncMock(return_value=None)
        mock_signal_dao_class.return_value = mock_signal_dao
        
        mock_history_dao = Mock()
        mock_history_dao.insert_one = AsyncMock(return_value=True)
        mock_history_dao_class.return_value = mock_history_dao
        
        mock_message_sender = Mock()
        mock_message_sender.send_message_to_user = AsyncMock(return_value=True)
        mock_message_sender_class.return_value = mock_message_sender
        
        # 创建模拟的Signal对象
        mock_signal = Mock()
        mock_signal.id = "507f1f77bcf86cd799439011"
        mock_signal.trade_record_ids = []
        mock_signal.save = AsyncMock()
        
        # 创建成功的交易执行结果（金额已经是UI格式）
        successful_attempt = ChannelAttemptResult(
            channel_type="jupiter",
            attempt_number=1,
            status=TradeStatus.SUCCESS,
            tx_hash="BQUbMmrLMiaxkwCSD8VRbkJdUYre1or6A19Pkd6KXPGCqcUGMYU6YVKHR59XxcTfuBiMxYzyJYyxASzC6f7Ke1c",
            error_message=None,
            execution_time=1.8,
            started_at=datetime.now(),
            completed_at=datetime.now(),
            actual_amount_in=0.001,  # 已经是UI格式的SOL数量
            actual_amount_out=2.5    # 已经是UI格式的token数量
        )
        
        execution_result = TradeExecutionResult(
            final_status=TradeStatus.SUCCESS,
            successful_channel="jupiter",
            final_trade_record_id="507f1f77bcf86cd799439012",
            channel_attempts=[successful_attempt],
            total_execution_time=1.8,
            error_summary=None,
            started_at=datetime.now(),
            completed_at=datetime.now()
        )
        
        # 设置AutoTradeManager mock
        mock_auto_trade_manager = Mock()
        mock_auto_trade_manager.config_manager.is_enabled = AsyncMock(return_value=True)
        mock_auto_trade_manager.execute_trade = AsyncMock(return_value=execution_result)
        mock_get_auto_trade_manager.return_value = mock_auto_trade_manager
        
        # 准备测试数据
        test_data = [{
            'token_data': self.token_info,
            'strategy_config_snapshot': self.strategy_snapshot
        }]
        
        # 使用patch来模拟Signal的创建
        with patch('workflows.monitor_kol_activity.handler.Signal', return_value=mock_signal):
            # 执行测试
            result = await send_message_to_channel(test_data)
            
            # 验证结果
            self.assertTrue(result)
            
            # 验证消息发送被调用
            mock_message_sender.send_message_to_user.assert_called_once()
            
            # 获取发送的消息内容
            call_args = mock_message_sender.send_message_to_user.call_args
            sent_message = call_args[0][0]
            
            # 验证消息内容包含正确的金额显示（UI格式直接使用）
            self.assertIn("Trade Status: ✅ SUCCESS", sent_message)
            self.assertIn("Amount Bought: 2.500000 grow", sent_message)  # 直接使用2.5
            self.assertIn("Amount Spent: 0.001000000 SOL", sent_message)  # 直接使用0.001

    @patch('workflows.monitor_kol_activity.handler.TelegramUserDAO')
    @patch('workflows.monitor_kol_activity.handler.SignalDAO')
    @patch('workflows.monitor_kol_activity.handler.get_auto_trade_manager')
    @patch('workflows.monitor_kol_activity.handler.TelegramMessageSender')
    @patch('workflows.monitor_kol_activity.handler.TokenMessageSendHistoryDAO')
    async def test_trade_without_decimals_info(
        self,
        mock_history_dao_class,
        mock_message_sender_class,
        mock_get_auto_trade_manager,
        mock_signal_dao_class,
        mock_user_dao_class
    ):
        """测试当token缺少decimals信息时的处理"""
        
        # 设置mock对象
        mock_user_dao = Mock()
        mock_user_dao.get_all_users = AsyncMock(return_value=[{'chat_id': '123456789'}])
        mock_user_dao_class.return_value = mock_user_dao
        
        mock_signal_dao = Mock()
        mock_signal_dao.collection.find_one = AsyncMock(return_value=None)
        mock_signal_dao_class.return_value = mock_signal_dao
        
        mock_history_dao = Mock()
        mock_history_dao.insert_one = AsyncMock(return_value=True)
        mock_history_dao_class.return_value = mock_history_dao
        
        mock_message_sender = Mock()
        mock_message_sender.send_message_to_user = AsyncMock(return_value=True)
        mock_message_sender_class.return_value = mock_message_sender
        
        # 创建模拟的Signal对象
        mock_signal = Mock()
        mock_signal.id = "507f1f77bcf86cd799439011"
        mock_signal.trade_record_ids = []
        mock_signal.save = AsyncMock()
        
        # 创建成功的交易执行结果
        successful_attempt = ChannelAttemptResult(
            channel_type="gmgn",
            attempt_number=1,
            status=TradeStatus.SUCCESS,
            tx_hash="BQUbMmrLMiaxkwCSD8VRbkJdUYre1or6A19Pkd6KXPGCqcUGMYU6YVKHR59XxcTfuBiMxYzyJYyxASzC6f7Ke1c",
            error_message=None,
            execution_time=2.5,
            started_at=datetime.now(),
            completed_at=datetime.now(),
            actual_amount_in=1000000,  # lamports格式
            actual_amount_out=1500000  # 最小单位格式
        )
        
        execution_result = TradeExecutionResult(
            final_status=TradeStatus.SUCCESS,
            successful_channel="gmgn",
            final_trade_record_id="507f1f77bcf86cd799439012",
            channel_attempts=[successful_attempt],
            total_execution_time=2.5,
            error_summary=None,
            started_at=datetime.now(),
            completed_at=datetime.now()
        )
        
        # 设置AutoTradeManager mock
        mock_auto_trade_manager = Mock()
        mock_auto_trade_manager.config_manager.is_enabled = AsyncMock(return_value=True)
        mock_auto_trade_manager.execute_trade = AsyncMock(return_value=execution_result)
        mock_get_auto_trade_manager.return_value = mock_auto_trade_manager
        
        # 准备测试数据（没有decimals信息）
        token_info_no_decimals = self.token_info.copy()
        del token_info_no_decimals['decimals']  # 移除decimals信息
        
        test_data = [{
            'token_data': token_info_no_decimals,
            'strategy_config_snapshot': self.strategy_snapshot
        }]
        
        # 使用patch来模拟Signal的创建
        with patch('workflows.monitor_kol_activity.handler.Signal', return_value=mock_signal):
            # 执行测试
            result = await send_message_to_channel(test_data)
            
            # 验证结果
            self.assertTrue(result)
            
            # 验证消息发送被调用
            mock_message_sender.send_message_to_user.assert_called_once()
            
            # 获取发送的消息内容
            call_args = mock_message_sender.send_message_to_user.call_args
            sent_message = call_args[0][0]
            
            # 验证消息内容包含原始值显示（因为没有decimals信息）
            self.assertIn("Trade Status: ✅ SUCCESS", sent_message)
            self.assertIn("Amount Bought: 1500000.000000 grow", sent_message)  # 显示原始值
            self.assertIn("Amount Spent: 0.001000000 SOL", sent_message)  # SOL仍然正确转换


if __name__ == '__main__':
    import asyncio
    
    # 运行异步测试
    async def run_tests():
        suite = unittest.TestLoader().loadTestsFromTestCase(TestHandlerAmountDisplay)
        runner = unittest.TextTestRunner(verbosity=2)
        
        # 为每个测试方法创建异步任务
        for test in suite:
            if hasattr(test, '_testMethodName'):
                test_method = getattr(test, test._testMethodName)
                if asyncio.iscoroutinefunction(test_method):
                    await test_method()
                else:
                    test_method()
    
    asyncio.run(run_tests()) 