from motor.motor_asyncio import AsyncIOMotorClient
import os
from beanie import init_beanie
from models.x_user import XUser
from models.task import TaskStatus
from models.solana_transaction import SolanaTransaction
from models.solana_monitor_address import SolanaMonitorAddress
from models.tweet_monitor_user import TweetMonitorUser
from models.tweet import Tweet

# 全局数据库客户端
_db = None
motor_client = None

# 获取数据库 URL
DATABASE_URL = os.getenv('MONGODB_HOST', '')
DATABASE_NAME = os.getenv('MONGODB_DB', 'meme_monitor')
DATABASE_AUTH_SOURCE = os.getenv('MONGODB_AUTH_SOURCE', 'admin')
DATABASE_USERNAME = os.getenv('MONGODB_USERNAME', 'admin')
DATABASE_PASSWORD = os.getenv('MONGODB_PASSWORD', 'admin')


def get_db():
    """获取数据库实例"""
    global _db
    if _db is None:
        raise RuntimeError("Database not initialized. Call init_mongodb() first.")
    return _db

class MongoTask:
    """MongoDB 任务基类"""
    @property
    def db(self):
        return get_db()
