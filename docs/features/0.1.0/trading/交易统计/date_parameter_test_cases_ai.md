# 交易统计CLI日期参数增强功能测试用例设计

## 1. 测试用例总览

### 1.1 测试分类
- **单元测试**: 测试个别函数和模块
- **集成测试**: 测试模块间的交互
- **功能测试**: 测试完整的业务功能
- **错误处理测试**: 测试各种异常情况
- **兼容性测试**: 测试与现有功能的兼容性

### 1.2 测试覆盖范围
- ✅ 参数解析和验证
- ✅ 时间范围转换
- ✅ 参数互斥性检查
- ✅ 数据查询功能
- ✅ 报告生成功能
- ✅ 飞书消息发送
- ✅ 错误处理

## 2. 单元测试用例

### 2.1 parse_single_date函数测试

#### TC-001: 有效日期解析
**测试目标**: 验证正确的日期格式能被正确解析
```python
def test_parse_single_date_valid():
    """测试有效日期解析"""
    
    # 测试数据
    test_cases = [
        ("2025-05-29", datetime(2025, 5, 29, 0, 0, 0, 0), datetime(2025, 5, 29, 23, 59, 59, 999999)),
        ("2024-01-01", datetime(2024, 1, 1, 0, 0, 0, 0), datetime(2024, 1, 1, 23, 59, 59, 999999)),
        ("2023-12-31", datetime(2023, 12, 31, 0, 0, 0, 0), datetime(2023, 12, 31, 23, 59, 59, 999999)),
    ]
    
    for date_str, expected_start, expected_end in test_cases:
        start_time, end_time = parse_single_date(date_str)
        assert start_time == expected_start
        assert end_time == expected_end
```

#### TC-002: 无效日期格式
**测试目标**: 验证无效日期格式会抛出适当异常
```python
def test_parse_single_date_invalid():
    """测试无效日期格式"""
    
    invalid_formats = [
        "2025/05/29",     # 斜杠分隔
        "29-05-2025",     # 日期顺序错误
        "2025-5-29",      # 月份缺少前导零
        "2025-05-29 10:30",  # 包含时间
        "invalid-date",   # 完全无效
        "2025-13-01",     # 无效月份
        "2025-05-32",     # 无效日期
        "",               # 空字符串
    ]
    
    for invalid_date in invalid_formats:
        with pytest.raises(argparse.ArgumentTypeError):
            parse_single_date(invalid_date)
```

#### TC-003: 边界日期测试
**测试目标**: 测试特殊日期（闰年、年末等）
```python
def test_parse_single_date_boundary():
    """测试边界日期"""
    
    boundary_cases = [
        "2024-02-29",  # 闰年2月29日
        "2024-12-31",  # 年末
        "2024-01-01",  # 年初
    ]
    
    for date_str in boundary_cases:
        start_time, end_time = parse_single_date(date_str)
        # 验证时间范围正确
        assert start_time.hour == 0 and start_time.minute == 0
        assert end_time.hour == 23 and end_time.minute == 59
```

### 2.2 参数验证测试

#### TC-004: 参数互斥性验证
**测试目标**: 验证--date与其他时间参数的互斥性
```python
def test_validate_args_mutual_exclusion():
    """测试参数互斥性"""
    
    # 模拟ArgumentParser对象
    class MockArgs:
        def __init__(self, **kwargs):
            self.date = kwargs.get('date')
            self.days = kwargs.get('days')
            self.start_date = kwargs.get('start_date')
            self.end_date = kwargs.get('end_date')
            # 其他属性...
    
    # 测试冲突情况
    conflict_cases = [
        MockArgs(date="2025-05-29", days=7),
        MockArgs(date="2025-05-29", start_date=datetime(2025, 5, 28)),
        MockArgs(date="2025-05-29", end_date=datetime(2025, 5, 30)),
        MockArgs(date="2025-05-29", days=1, start_date=datetime(2025, 5, 28)),
    ]
    
    for args in conflict_cases:
        with pytest.raises(ValueError, match="--date 参数不能与"):
            validate_args(args)
```

#### TC-005: 正常参数验证
**测试目标**: 验证正确的参数组合能通过验证
```python
def test_validate_args_valid():
    """测试有效参数组合"""
    
    valid_cases = [
        MockArgs(date="2025-05-29"),
        MockArgs(days=7),
        MockArgs(start_date=datetime(2025, 5, 28), end_date=datetime(2025, 5, 29)),
        MockArgs(),  # 无时间参数
    ]
    
    for args in valid_cases:
        # 应该不抛出异常
        validate_args(args)
```

## 3. 集成测试用例

### 3.1 CLI参数解析集成测试

#### TC-006: 完整命令行解析
**测试目标**: 测试完整的命令行参数解析流程
```python
def test_cli_date_parameter_parsing():
    """测试CLI日期参数解析"""
    
    test_commands = [
        ["--date", "2025-05-29"],
        ["--date", "2025-05-29", "--format", "html"],
        ["--date", "2025-05-29", "--strategies", "收益率高,胜率高"],
        ["--date", "2025-05-29", "--output", "/tmp/report.html"],
    ]
    
    for cmd_args in test_commands:
        parser = create_parser()
        args = parser.parse_args(cmd_args)
        
        # 验证参数解析正确
        assert args.date == "2025-05-29"
        
        # 验证validate_args后的时间设置
        validate_args(args)
        assert args.start_date is not None
        assert args.end_date is not None
        assert args.start_date.date() == args.end_date.date()
```

### 3.2 时间范围设置集成测试

#### TC-007: 配置对象时间设置
**测试目标**: 验证时间范围正确设置到StatisticsConfig
```python
def test_config_time_range_setting():
    """测试配置对象时间范围设置"""
    
    # 模拟args对象
    args = MockArgs(date="2025-05-29")
    validate_args(args)  # 这会设置start_date和end_date
    
    # 创建配置对象
    config = StatisticsConfig()
    config.start_date = args.start_date
    config.end_date = args.end_date
    
    # 验证时间范围
    assert config.start_date.date() == datetime(2025, 5, 29).date()
    assert config.end_date.date() == datetime(2025, 5, 29).date()
    assert config.start_date.hour == 0
    assert config.end_date.hour == 23
```

## 4. 功能测试用例

### 4.1 数据查询功能测试

#### TC-008: 单日数据查询
**测试目标**: 验证使用--date参数能正确查询到数据
```python
@pytest.mark.asyncio
async def test_single_date_data_query():
    """测试单日数据查询功能"""
    
    # 准备测试数据（需要在数据库中存在2025-05-29的交易记录）
    
    # 执行查询
    args = MockArgs(date="2025-05-29")
    validate_args(args)
    
    config = StatisticsConfig()
    config.start_date = args.start_date
    config.end_date = args.end_date
    
    # 创建分析器并执行查询
    analyzer = TradeStatisticsAnalyzer(trade_record_dao, verification_updater)
    result = await analyzer.analyze(config)
    
    # 验证结果
    assert result is not None
    # 如果有数据，验证数据在正确的时间范围内
    if result.trade_pairs:
        for pair in result.trade_pairs:
            assert args.start_date <= pair.buy_time <= args.end_date
            assert args.start_date <= pair.sell_time <= args.end_date
```

#### TC-009: 空数据日期查询
**测试目标**: 验证查询无数据日期时的行为
```python
@pytest.mark.asyncio
async def test_empty_date_query():
    """测试查询无数据日期"""
    
    # 选择一个确定无数据的日期
    args = MockArgs(date="2020-01-01")
    validate_args(args)
    
    config = StatisticsConfig()
    config.start_date = args.start_date
    config.end_date = args.end_date
    
    analyzer = TradeStatisticsAnalyzer(trade_record_dao, verification_updater)
    result = await analyzer.analyze(config)
    
    # 验证返回空结果而非错误
    assert result is not None
    assert result.overall_stats.total_trades == 0
    assert len(result.trade_pairs) == 0
```

### 4.2 报告生成功能测试

#### TC-010: HTML报告生成
**测试目标**: 验证使用--date参数生成HTML报告
```python
@pytest.mark.asyncio
async def test_html_report_generation_with_date():
    """测试HTML报告生成"""
    
    args = MockArgs(
        date="2025-05-29",
        format="html",
        output="/tmp/test_report.html"
    )
    validate_args(args)
    
    # 执行报告生成
    await run_analysis(args)
    
    # 验证文件生成
    assert os.path.exists("/tmp/test_report.html")
    
    # 验证报告内容
    with open("/tmp/test_report.html", 'r') as f:
        content = f.read()
        assert "2025-05-29" in content  # 应包含日期信息
        assert "交易统计分析报告" in content
```

#### TC-011: JSON报告生成
**测试目标**: 验证使用--date参数生成JSON报告
```python
@pytest.mark.asyncio
async def test_json_report_generation_with_date():
    """测试JSON报告生成"""
    
    args = MockArgs(
        date="2025-05-29",
        format="json",
        output="/tmp/test_report.json"
    )
    validate_args(args)
    
    await run_analysis(args)
    
    # 验证JSON文件
    assert os.path.exists("/tmp/test_report.json")
    
    with open("/tmp/test_report.json", 'r') as f:
        data = json.load(f)
        assert "metadata" in data
        assert "2025-05-29" in data["metadata"]["start_date"]
```

### 4.3 飞书消息发送测试

#### TC-012: 单日飞书消息
**测试目标**: 验证使用--date参数发送飞书消息时显示"1天报"
```python
@pytest.mark.asyncio
async def test_feishu_message_single_date():
    """测试单日飞书消息发送"""
    
    # 模拟飞书消息发送器
    class MockFeishuSender:
        def __init__(self):
            self.sent_data = None
            
        async def send_trading_summary_card(self, summary_data, report_url=None):
            self.sent_data = summary_data
            return True
    
    # 准备参数
    args = MockArgs(
        date="2025-05-29",
        send_feishu=True,
        feishu_webhook="mock://webhook"
    )
    validate_args(args)
    
    # 生成摘要数据
    summary_generator = TradingSummaryGenerator()
    summary_data = await summary_generator.generate_custom_summary(
        start_date=args.start_date,
        end_date=args.end_date
    )
    
    # 验证周期类型
    assert summary_data["period_type"] == "1天"
```

## 5. 错误处理测试用例

### 5.1 参数错误测试

#### TC-013: 无效日期格式错误
**测试目标**: 验证无效日期格式的错误处理
```python
def test_invalid_date_format_error():
    """测试无效日期格式错误处理"""
    
    invalid_commands = [
        ["--date", "2025/05/29"],
        ["--date", "invalid-date"],
        ["--date", "2025-13-01"],
    ]
    
    for cmd_args in invalid_commands:
        parser = create_parser()
        args = parser.parse_args(cmd_args)
        
        with pytest.raises(ValueError, match="无效的日期格式"):
            validate_args(args)
```

#### TC-014: 参数冲突错误
**测试目标**: 验证参数冲突的错误处理
```python
def test_parameter_conflict_error():
    """测试参数冲突错误处理"""
    
    conflict_commands = [
        ["--date", "2025-05-29", "--days", "7"],
        ["--date", "2025-05-29", "--start-date", "2025-05-28"],
    ]
    
    for cmd_args in conflict_commands:
        parser = create_parser()
        args = parser.parse_args(cmd_args)
        
        with pytest.raises(ValueError, match="--date 参数不能与"):
            validate_args(args)
```

## 6. 兼容性测试用例

### 6.1 现有功能兼容性

#### TC-015: 现有参数兼容性
**测试目标**: 验证现有参数仍然正常工作
```python
def test_existing_parameters_compatibility():
    """测试现有参数兼容性"""
    
    existing_commands = [
        ["--days", "7"],
        ["--start-date", "2025-05-28", "--end-date", "2025-05-29"],
        ["--days", "7", "--format", "html"],
    ]
    
    for cmd_args in existing_commands:
        parser = create_parser()
        args = parser.parse_args(cmd_args)
        
        # 应该不抛出异常
        validate_args(args)
```

#### TC-016: 过滤参数兼容性
**测试目标**: 验证--date与过滤参数的兼容性
```python
def test_filter_parameters_compatibility():
    """测试过滤参数兼容性"""
    
    filter_commands = [
        ["--date", "2025-05-29", "--strategies", "收益率高"],
        ["--date", "2025-05-29", "--tokens", "token1,token2"],
        ["--date", "2025-05-29", "--strategies", "策略1", "--tokens", "token1"],
    ]
    
    for cmd_args in filter_commands:
        parser = create_parser()
        args = parser.parse_args(cmd_args)
        
        validate_args(args)
        # 验证过滤参数正确设置
        assert args.date == "2025-05-29"
```

## 7. 性能测试用例

### 7.1 大数据量测试

#### TC-017: 单日大数据量处理
**测试目标**: 验证单日大量交易数据的处理性能
```python
@pytest.mark.performance
async def test_large_dataset_performance():
    """测试大数据量处理性能"""
    
    # 准备大量测试数据的日期
    args = MockArgs(date="2025-05-29")  # 假设这天有大量数据
    validate_args(args)
    
    start_time = time.time()
    
    # 执行分析
    analyzer = TradeStatisticsAnalyzer(trade_record_dao, verification_updater)
    config = StatisticsConfig()
    config.start_date = args.start_date
    config.end_date = args.end_date
    
    result = await analyzer.analyze(config)
    
    end_time = time.time()
    execution_time = end_time - start_time
    
    # 验证性能要求（例如：处理时间不超过30秒）
    assert execution_time < 30, f"处理时间过长: {execution_time}秒"
```

## 8. 测试数据准备

### 8.1 测试数据库设置
```python
@pytest.fixture
async def test_database():
    """准备测试数据库"""
    
    # 创建测试交易记录
    test_records = [
        # 2025-05-29的测试数据
        create_test_trade_record(
            date=datetime(2025, 5, 29, 10, 0, 0),
            signal_id="test_signal_1",
            action="BUY"
        ),
        create_test_trade_record(
            date=datetime(2025, 5, 29, 15, 0, 0),
            signal_id="test_signal_1",
            action="SELL"
        ),
        # 其他测试数据...
    ]
    
    # 插入测试数据
    for record in test_records:
        await trade_record_dao.insert(record)
    
    yield
    
    # 清理测试数据
    await cleanup_test_data()
```

## 9. 测试执行计划

### 9.1 测试阶段
1. **单元测试**: 开发过程中持续执行
2. **集成测试**: 模块完成后执行
3. **功能测试**: 功能完整后执行
4. **回归测试**: 确保现有功能不受影响

### 9.2 测试覆盖率目标
- 单元测试覆盖率: ≥ 90%
- 集成测试覆盖率: ≥ 80%
- 功能测试覆盖率: 100%（所有用户场景）

### 9.3 验收标准
- [ ] 所有测试用例通过
- [ ] 代码覆盖率达到目标
- [ ] 性能测试符合要求
- [ ] 错误处理测试通过
- [ ] 兼容性测试通过 