import logging
import asyncio
import traceback
from typing import Dict, Any, Optional, List

from dao.kol_wallet_dao import KOLWalletDAO
from dao.kol_wallet_activity_dao import KOLWalletActivityDAO
from models import init_db
from utils.spiders.smart_money import BasicSpider

class KOLWalletActivitySpider(BasicSpider):
    """KOL钱包活动爬虫
    
    从GMGN API获取KOL钱包的活动数据
    """
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(self.__class__.__name__)
        self.kol_wallet_dao = KOLWalletDAO()
        self.kol_wallet_activity_dao = KOLWalletActivityDAO()
        
        # API配置
        self.base_url = "https://gmgn.ai/api/v1/wallet_activity/sol"
        self.headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-TW;q=0.6',
            'cache-control': 'no-cache',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'sec-ch-ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
            'referer': 'https://gmgn.ai/sol/token/dDO6ZdYE_CniPCE4b3s8gSUPhUiyMjXnytrEqUrMfSsnbBjLCpump?tab=activity',
            'sec-ch-ua-arch': '"arm"',
            'sec-ch-ua-bitness': '"64"',
            'sec-ch-ua-full-version': '"134.0.6998.45"',
            'sec-ch-ua-full-version-list': '"Chromium";v="134.0.6998.45", "Not:A-Brand";v="********", "Google Chrome";v="134.0.6998.45"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-model': '""',
            'sec-ch-ua-platform': '"macOS"',
            'sec-ch-ua-platform-version': '"14.5.0"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
        
        # 请求参数
        self.params = {
            'type': ['buy', 'sell', 'transferIn', 'transferOut', 'add', 'remove'],
            'device_id': '65990bc3-0a13-4d99-8c66-eeea94916b0f',
            'client_id': 'gmgn_web_2025.0314.194246',
            'from_app': 'gmgn',
            'app_ver': '2025.0314.194246',
            'tz_name': 'Asia/Shanghai',
            'tz_offset': '28800',
            'app_lang': 'zh-CN',
            'limit': '100',  # 每页获取100条记录
            'cost': '10'
        }
    
    async def setup(self):
        """设置爬虫，初始化会话并更新头信息"""
        await self.init_sessions()
        # 更新会话头信息
        self.session.headers.update(self.headers)
        # 设置浏览器模拟
        self.session.impersonate = "chrome110"  # 模拟Chrome浏览器
    
    async def fetch_wallet_activities(
        self, 
        wallet_address: str, 
        next_page_token: Optional[str] = None, 
        page_interval: float = 0.1,
        until_trade_timestamp: str = None
    ):
        """获取钱包活动数据，使用异步生成器返回数据
        
        Args:
            wallet_address: 钱包地址
            next_page_token: 下一页令牌
            page_interval: 每页请求间隔时间
            until_trade_timestamp: 最后活跃交易时间戳
        Yields:
            Dict[str, Any]: 活动数据项
        """
        
        # O设置请求参数
        params = self.params.copy()
        params['wallet'] = wallet_address
        
        # 如果有下一页令牌，添加到参数中
        if next_page_token:
            params['cursor'] = next_page_token
            
        # 构建请求URL
        url = self.base_url
        
        # 使用生成器方式返回数据，避免内存堆积
        current_token = next_page_token
        
        try:
            while True:
                # 更新cursor参数
                if current_token:
                    params['cursor'] = current_token
                else:
                    params.pop('cursor', None)
                
                self.logger.info(f"请求URL: {url}, 请求参数: {params}")
                response = await self.get(url, params=params)
                
                # 记录响应状态和内容
                self.logger.debug(f"API响应状态码: {response.status_code}")
                
                # 检查响应状态
                if response.status_code != 200:
                    self.logger.error(f"获取钱包 {wallet_address} 活动失败: HTTP {response.status_code}, 错误响应: {response.text}")
                    break
                
                data = response.json()
                
                if data.get("code") != 0:
                    self.logger.error(f"获取钱包 {wallet_address} 活动失败: {data.get('message', '未知错误')}")
                    break
                
                activities = data.get("data", {}).get("activities", [])
                current_token = data.get("data", {}).get("next")
                self.logger.info(f"下一页令牌: {current_token}")
                
                # 如果存在until_trade_timestamp,则筛选时间戳大于等于它的活动
                if until_trade_timestamp:
                    for activity in activities:
                        if activity.get("timestamp") >= until_trade_timestamp:
                            yield activity
                        else:
                            # 找到早于目标时间戳的活动，停止迭代
                            return
                else:
                    # 逐个返回活动数据
                    for activity in activities:
                        yield activity
                
                # 如果没有下一页，结束循环
                if not current_token:
                    break
                
                # 请求间隔
                await asyncio.sleep(page_interval)
                
        except Exception as e:
            self.logger.error(f"获取钱包活动异常: {traceback.format_exc()}")
            return  # 遇到异常结束生成器

    async def fetch_all_wallet_activities(
        self, 
        wallet_address: str, 
        next_page_token: Optional[str] = None, 
        page_interval: float = 0.1,
        until_trade_timestamp: str = None
    ) -> List[Dict[str, Any]]:
        """获取钱包活动数据，使用列表一次性返回所有数据（用于内存对比测试）
        
        Args:
            wallet_address: 钱包地址
            next_page_token: 下一页令牌
            page_interval: 每页请求间隔时间
            until_trade_timestamp: 最后活跃交易时间戳
        Returns:
            List[Dict[str, Any]]: 活动数据列表
        """
        
        # 设置请求参数
        params = self.params.copy()
        params['wallet'] = wallet_address
        
        # 如果有下一页令牌，添加到参数中
        if next_page_token:
            params['cursor'] = next_page_token
            
        # 构建请求URL
        url = self.base_url
        
        # 使用列表方式收集所有数据
        all_activities = []
        current_token = next_page_token
        
        try:
            while True:
                # 更新cursor参数
                if current_token:
                    params['cursor'] = current_token
                else:
                    params.pop('cursor', None)
                
                self.logger.info(f"请求URL: {url}, 请求参数: {params}")
                response = await self.get(url, params=params)
                
                # 记录响应状态和内容
                self.logger.debug(f"API响应状态码: {response.status_code}")
                
                # 检查响应状态
                if response.status_code != 200:
                    self.logger.error(f"获取钱包活动失败: HTTP {response.status_code}")
                    self.logger.error(f"错误响应: {response.text}")
                    break
                
                data = response.json()
                
                if data.get("code") != 0:
                    self.logger.error(f"获取钱包活动失败: {data.get('message', '未知错误')}")
                    break
                
                activities = data.get("data", {}).get("activities", [])
                current_token = data.get("data", {}).get("next")
                self.logger.info(f"下一页令牌: {current_token}")
                
                # 如果存在until_trade_timestamp,则筛选时间戳大于等于它的活动
                if until_trade_timestamp:
                    filtered_activities = [x for x in activities if x.get("timestamp") >= until_trade_timestamp]
                    all_activities.extend(filtered_activities)
                    
                    # 如果筛选后的长度小于原始长度，说明已经找到旧数据，不再继续
                    if len(filtered_activities) < len(activities):
                        break
                else:
                    all_activities.extend(activities)
                
                # 如果没有下一页，结束循环
                if not current_token:
                    break
                
                # 请求间隔
                await asyncio.sleep(page_interval)
                
            return all_activities
            
        except Exception as e:
            self.logger.error(f"获取钱包活动异常: {traceback.format_exc()}")
            return all_activities  # 返回已获取的所有活动

async def main():
    import os
    import time

    # 获取当前进程
    wallet_address = "AHnQjWa4jxYNxzfJantHDpcUKAE9pzVSGGPLhthPxCSW"
    
    await init_db()
    print("=" * 50)
    print("对比测试：生成器方式 vs 非生成器方式")
    print("=" * 50)
    
    # ===== 测试生成器方式 =====
    print("\n[测试1] 使用生成器方式处理数据")
    
    start_time = time.time()
    
    activities = []
    async with KOLWalletActivitySpider() as spider:
        async for activity in spider.fetch_wallet_activities(wallet_address):
            activity['wallet'] = wallet_address
            activities.append(activity)
    await KOLWalletActivityDAO().upsert_activities(activities)
    generator_time = time.time() - start_time
    print(f"生成器方式完成，共处理 {len(activities)} 条活动")
    print(f"处理时间: {generator_time:.2f} 秒")


if __name__ == "__main__":
    # 使用asyncio运行异步主函数
    asyncio.run(main())
