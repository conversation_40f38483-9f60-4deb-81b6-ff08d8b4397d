import logging
from typing import Dict, List, Any, Optional
from pymongo.results import UpdateResult, DeleteResult
from pymongo import InsertOne, UpdateOne, DeleteOne
from models.gmgn_developer_traders import GmgnDeveloperTraders
from dao.base_dao import BaseDAO


class GmgnDeveloperTradersDAO(BaseDAO[GmgnDeveloperTraders]):
    """GMGN开发者交易情况DAO"""
    
    def __init__(self):
        super().__init__(GmgnDeveloperTraders)
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def find_by_address(self, chain: str, address: str) -> Optional[GmgnDeveloperTraders]:
        """根据链和地址查询开发者交易情况
        
        Args:
            chain: 链名称
            address: 钱包地址
            
        Returns:
            Optional[GmgnDeveloperTraders]: 开发者交易情况，如果不存在则返回None
        """
        query = {"chain": chain, "address": address}
        return await self.collection.find_one(query)
    
    async def find_by_token_address(self, chain: str, token_address: str, limit: int = 100) -> List[GmgnDeveloperTraders]:
        """根据代币地址查询开发者交易情况列表
        
        Args:
            chain: 链名称
            token_address: 代币地址
            limit: 返回结果数量限制，默认100
            
        Returns:
            List[GmgnDeveloperTraders]: 开发者交易情况列表
        """
        query = {"chain": chain, "token_address": token_address}
        cursor = self.collection.find(query).sort("profit", -1).limit(limit)
        return await cursor.to_list(length=limit)
    
    async def find_by_account_address(self, chain: str, account_address: str) -> Optional[GmgnDeveloperTraders]:
        """根据账户地址查询开发者交易情况
        
        Args:
            chain: 链名称
            account_address: 账户地址
            
        Returns:
            Optional[GmgnDeveloperTraders]: 开发者交易情况，如果不存在则返回None
        """
        query = {"chain": chain, "account_address": account_address}
        return await self.collection.find_one(query)
    
    async def find_by_wallet_tag(self, chain: str, wallet_tag: str) -> List[GmgnDeveloperTraders]:
        """根据钱包标签查询开发者交易情况列表
        
        Args:
            chain: 链名称
            wallet_tag: 钱包标签
            
        Returns:
            List[GmgnDeveloperTraders]: 开发者交易情况列表
        """
        query = {"chain": chain, "wallet_tag_v2": wallet_tag}
        cursor = self.collection.find(query)
        return await cursor.to_list(length=None)
    
    async def find_by_twitter_username(self, twitter_username: str) -> List[GmgnDeveloperTraders]:
        """根据Twitter用户名查询开发者交易情况列表
        
        Args:
            twitter_username: Twitter用户名
            
        Returns:
            List[GmgnDeveloperTraders]: 开发者交易情况列表
        """
        query = {"twitter_username": twitter_username}
        cursor = self.collection.find(query)
        return await cursor.to_list(length=None)
    
    async def find_by_maker_token_tags(self, tags: List[str], match_all: bool = False) -> List[GmgnDeveloperTraders]:
        """根据做市商标签查询开发者交易情况列表
        
        Args:
            tags: 做市商标签列表
            match_all: 是否匹配所有标签，默认False
            
        Returns:
            List[GmgnDeveloperTraders]: 开发者交易情况列表
        """
        if match_all:
            # 匹配所有标签
            query = {"maker_token_tags": {"$all": tags}}
        else:
            # 匹配任一标签
            query = {"maker_token_tags": {"$in": tags}}
        
        cursor = self.collection.find(query)
        return await cursor.to_list(length=None)
    
    async def find_by_profit_range(
        self,
        chain: str,
        min_profit: float = 0,
        max_profit: Optional[float] = None,
        limit: int = 100
    ) -> List[GmgnDeveloperTraders]:
        """根据利润范围查询开发者交易情况列表
        
        Args:
            chain: 链名称
            min_profit: 最小利润，默认0
            max_profit: 最大利润，默认None表示不限制
            limit: 返回结果数量限制，默认100
            
        Returns:
            List[GmgnDeveloperTraders]: 开发者交易情况列表
        """
        query = {"chain": chain, "profit": {"$gte": min_profit}}
        if max_profit is not None:
            query["profit"]["$lte"] = max_profit
        
        cursor = self.collection.find(query).sort("profit", -1).limit(limit)
        return await cursor.to_list(length=limit)
    
    async def find_by_tags(
        self,
        chain: str,
        tags: List[str],
        match_all: bool = True
    ) -> List[GmgnDeveloperTraders]:
        """根据标签查询开发者交易情况列表
        
        Args:
            chain: 链名称
            tags: 标签列表
            match_all: 是否匹配所有标签，默认True
            
        Returns:
            List[GmgnDeveloperTraders]: 开发者交易情况列表
        """
        if match_all:
            # 匹配所有标签
            query = {"chain": chain, "tags": {"$all": tags}}
        else:
            # 匹配任一标签
            query = {"chain": chain, "tags": {"$in": tags}}
        
        cursor = self.collection.find(query)
        return await cursor.to_list(length=None)
    
    async def find_active_traders(
        self,
        chain: str,
        since_timestamp: int,
        limit: int = 100
    ) -> List[GmgnDeveloperTraders]:
        """查询活跃开发者交易情况列表
        
        Args:
            chain: 链名称
            since_timestamp: 起始时间戳
            limit: 返回结果数量限制，默认100
            
        Returns:
            List[GmgnDeveloperTraders]: 开发者交易情况列表
        """
        query = {
            "chain": chain,
            "last_active_timestamp": {"$gte": since_timestamp}
        }
        
        cursor = self.collection.find(query).sort("last_active_timestamp", -1).limit(limit)
        return await cursor.to_list(length=limit)
    
    async def find_by_holding_amount(
        self,
        chain: str,
        min_amount: float = 0,
        max_amount: Optional[float] = None,
        limit: int = 100
    ) -> List[GmgnDeveloperTraders]:
        """根据持仓数量查询开发者交易情况列表
        
        Args:
            chain: 链名称
            min_amount: 最小持仓数量，默认0
            max_amount: 最大持仓数量，默认None表示不限制
            limit: 返回结果数量限制，默认100
            
        Returns:
            List[GmgnDeveloperTraders]: 开发者交易情况列表
        """
        query = {"chain": chain, "amount_cur": {"$gte": min_amount}}
        if max_amount is not None:
            query["amount_cur"]["$lte"] = max_amount
        
        cursor = self.collection.find(query).sort("amount_cur", -1).limit(limit)
        return await cursor.to_list(length=limit)
    
    async def find_creators(self, chain: str, limit: int = 100) -> List[GmgnDeveloperTraders]:
        """查询创建者列表
        
        Args:
            chain: 链名称
            limit: 返回结果数量限制，默认100
            
        Returns:
            List[GmgnDeveloperTraders]: 创建者列表
        """
        query = {
            "chain": chain,
            "maker_token_tags": "creator"
        }
        
        cursor = self.collection.find(query).limit(limit)
        return await cursor.to_list(length=limit)
    
    async def upsert_trader(self, chain: str, token_address: str, data: Dict[str, Any]) -> bool:
        """更新或插入开发者交易情况
        
        Args:
            chain: 链名称
            token_address: 代币地址
            data: API返回的数据
            
        Returns:
            bool: 是否成功
        """
        try:
            # 添加token_address到数据中
            data['token_address'] = token_address
            
            # 创建新的开发者交易情况实例
            trader = await GmgnDeveloperTraders.from_api_data(chain, data)
            
            # 更新或插入
            query = {"chain": chain, "address": trader.address, "token_address": token_address}
            result = await self.collection.replace_one(query, trader.dict(), upsert=True)
            
            self.logger.info(f"更新开发者交易情况成功: {trader.address}, matched: {result.matched_count}, modified: {result.modified_count}")
            return True
        except Exception as e:
            self.logger.error(f"更新开发者交易情况失败: {str(e)}")
            return False
    
    async def upsert_traders_many(
        self,
        chain: str,
        data_list: List[Dict[str, Any]]
    ) -> int:
        """批量更新或插入开发者交易情况
        
        Args:
            chain: 链名称
            token_address: 代币地址
            data_list: API返回的数据列表
            
        Returns:
            int: 更新或插入的记录数
        """
        if not data_list:
            return 0
            
        # 准备批量更新操作
        bulk_operations = []
        
        for data in data_list:
            # 添加token_address到数据中
            token_address = data['token_address']
            
            # 创建新的开发者交易情况实例
            trader = await GmgnDeveloperTraders.from_api_data(chain, data)
            
            # 创建更新操作
            bulk_operations.append(
                InsertOne(
                    trader.dict()
                )
            )
        
        # 执行批量更新
        try:
            result = await self.collection.bulk_write(bulk_operations)
            self.logger.info(f"批量更新完成: 修改 {result.modified_count} 条, 插入 {result.upserted_count} 条")
            return result.modified_count + result.upserted_count
        except Exception as e:
            self.logger.error(f"批量更新开发者交易情况数据失败: {str(e)}")
            return 0
    
    async def delete_by_address(self, chain: str, address: str) -> bool:
        """根据链和地址删除开发者交易情况
        
        Args:
            chain: 链名称
            address: 钱包地址
            
        Returns:
            bool: 是否成功
        """
        try:
            query = {"chain": chain, "address": address}
            result = await self.collection.delete_one(query)
            self.logger.info(f"删除开发者交易情况成功: {address}, deleted: {result.deleted_count}")
            return result.deleted_count > 0
        except Exception as e:
            self.logger.error(f"删除开发者交易情况失败: {str(e)}")
            return False
    
    async def delete_by_token_address(self, chain: str, token_address: str) -> int:
        """根据代币地址删除开发者交易情况
        
        Args:
            chain: 链名称
            token_address: 代币地址
            
        Returns:
            int: 删除的记录数
        """
        try:
            query = {"chain": chain, "token_address": token_address}
            result = await self.collection.delete_many(query)
            self.logger.info(f"删除代币相关开发者交易情况成功: {token_address}, deleted: {result.deleted_count}")
            return result.deleted_count
        except Exception as e:
            self.logger.error(f"删除代币相关开发者交易情况失败: {str(e)}")
            return 0
    
    async def delete_inactive_traders(self, chain: str, before_timestamp: int) -> int:
        """删除不活跃的开发者交易情况
        
        Args:
            chain: 链名称
            before_timestamp: 截止时间戳
            
        Returns:
            int: 删除的记录数
        """
        try:
            query = {
                "chain": chain,
                "last_active_timestamp": {"$lt": before_timestamp}
            }
            result = await self.collection.delete_many(query)
            self.logger.info(f"删除不活跃开发者交易情况成功: deleted: {result.deleted_count}")
            return result.deleted_count
        except Exception as e:
            self.logger.error(f"删除不活跃开发者交易情况失败: {str(e)}")
            return 0 