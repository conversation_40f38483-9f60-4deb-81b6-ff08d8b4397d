import unittest
from unittest.mock import Mock, AsyncMock, patch
import httpx
import json
from utils.trading.solana.jupiter_trade_service import JupiterTradeService


class TestJupiterSwapErrorHandling(unittest.TestCase):
    """测试Jupiter Swap API错误处理Bug"""

    def setUp(self):
        """测试前置设置"""
        self.service = JupiterTradeService(
            jupiter_api_host="https://quote-api.jup.ag",
            solana_rpc_url="https://api.mainnet-beta.solana.com"
        )
        
        # 模拟quote数据
        self.mock_quote_data = {
            "inputMint": "So11111111111111111111111111111111111111112",
            "inAmount": "1000000",
            "outputMint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
            "outAmount": "1000000",
            "otherAmountThreshold": "996000",
            "swapMode": "ExactIn",
            "slippageBps": 100,
            "platformFee": None,
            "priceImpactPct": "0.01",
            "routePlan": []
        }
        
        self.test_user_public_key = "11111111111111111111111111111111"

    async def test_jupiter_swap_400_error_now_fixed_with_details(self):
        """测试修复后：Jupiter swap API 400错误现在包含详细信息（Bug已修复）"""
        # 模拟400错误响应
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.text = '{"error": "Invalid slippage parameter: must be between 1 and 5000 bps"}'
        mock_response.json.return_value = {"error": "Invalid slippage parameter: must be between 1 and 5000 bps"}
        
        # 创建HTTPStatusError
        http_error = httpx.HTTPStatusError(
            message="400 Bad Request",
            request=Mock(),
            response=mock_response
        )
        
        # 模拟http_client.post抛出HTTPStatusError
        with patch.object(self.service, 'http_client') as mock_client:
            mock_client.post.side_effect = http_error
            
            # 修复后，现在应该抛出包含详细错误信息的ValueError
            with self.assertRaises(ValueError) as context:
                await self.service._get_jupiter_swap_transaction(
                    self.mock_quote_data,
                    self.test_user_public_key
                )
            
            # 修复后：能够看到详细的错误信息
            error_str = str(context.exception)
            self.assertIn("Jupiter swap API error (400)", error_str)
            self.assertIn("Invalid slippage parameter", error_str)
            print(f"修复后的详细错误信息: {error_str}")

    async def test_jupiter_swap_400_error_with_json_response_after_fix(self):
        """测试修复后：能够正确解析JSON格式的400错误响应"""
        # 这个测试在修复后应该通过
        
        # 模拟400错误响应（JSON格式）
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.text = '{"error": "Invalid slippage parameter: must be between 1 and 5000 bps"}'
        mock_response.json.return_value = {"error": "Invalid slippage parameter: must be between 1 and 5000 bps"}
        
        # 创建HTTPStatusError
        http_error = httpx.HTTPStatusError(
            message="400 Bad Request",
            request=Mock(),
            response=mock_response
        )
        
        # 模拟http_client.post抛出HTTPStatusError
        with patch.object(self.service, 'http_client') as mock_client:
            mock_client.post.side_effect = http_error
            
            # 修复后，应该抛出包含详细错误信息的ValueError
            with self.assertRaises(ValueError) as context:
                await self.service._get_jupiter_swap_transaction(
                    self.mock_quote_data,
                    self.test_user_public_key
                )
            
            # 验证错误信息包含状态码和详细错误
            error_message = str(context.exception)
            self.assertIn("Jupiter swap API error (400)", error_message)
            self.assertIn("Invalid slippage parameter", error_message)
            print(f"修复后的详细错误信息: {error_message}")

    async def test_jupiter_swap_400_error_with_text_response_after_fix(self):
        """测试修复后：能够处理纯文本格式的400错误响应"""
        # 模拟400错误响应（纯文本格式）
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.text = "Bad Request: Invalid parameters"
        mock_response.json.side_effect = json.JSONDecodeError("Expecting value", "", 0)
        
        # 创建HTTPStatusError
        http_error = httpx.HTTPStatusError(
            message="400 Bad Request",
            request=Mock(),
            response=mock_response
        )
        
        # 模拟http_client.post抛出HTTPStatusError
        with patch.object(self.service, 'http_client') as mock_client:
            mock_client.post.side_effect = http_error
            
            # 修复后，应该抛出包含文本错误信息的ValueError
            with self.assertRaises(ValueError) as context:
                await self.service._get_jupiter_swap_transaction(
                    self.mock_quote_data,
                    self.test_user_public_key
                )
            
            # 验证错误信息包含状态码和文本错误
            error_message = str(context.exception)
            self.assertIn("Jupiter swap API error (400)", error_message)
            self.assertIn("Bad Request: Invalid parameters", error_message)
            print(f"修复后的文本错误信息: {error_message}")

    async def test_jupiter_swap_success_after_fix_compatibility(self):
        """测试修复后：正常的swap请求不受影响（向后兼容性）"""
        # 模拟成功的响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "swapTransaction": "base64encodedtransaction"
        }
        
        # 模拟http_client.post返回成功响应
        with patch.object(self.service, 'http_client') as mock_client:
            # 创建AsyncMock来处理异步调用
            mock_client.post = AsyncMock(return_value=mock_response)
            
            # 应该正常返回交易数据
            result = await self.service._get_jupiter_swap_transaction(
                self.mock_quote_data,
                self.test_user_public_key
            )
            
            self.assertEqual(result, "base64encodedtransaction")
            print("✅ 向后兼容性测试通过：正常请求不受影响")


if __name__ == '__main__':
    import asyncio
    
    async def run_async_tests():
        """运行异步测试"""
        test_instance = TestJupiterSwapErrorHandling()
        test_instance.setUp()
        
        print("=== 测试Jupiter Swap API错误处理Bug ===")
        
        # 测试修复后的详细错误处理
        print("\n1. 测试修复后的详细错误处理...")
        try:
            await test_instance.test_jupiter_swap_400_error_now_fixed_with_details()
            print("✅ 详细错误处理测试完成")
        except Exception as e:
            print(f"❌ 详细错误处理测试失败: {e}")
        
        # 测试修复后的功能（这些测试在修复前会失败）
        print("\n2. 测试修复后的JSON错误处理...")
        try:
            await test_instance.test_jupiter_swap_400_error_with_json_response_after_fix()
            print("✅ JSON错误处理测试完成")
        except Exception as e:
            print(f"❌ JSON错误处理测试失败: {e}")
        
        print("\n3. 测试修复后的文本错误处理...")
        try:
            await test_instance.test_jupiter_swap_400_error_with_text_response_after_fix()
            print("✅ 文本错误处理测试完成")
        except Exception as e:
            print(f"❌ 文本错误处理测试失败: {e}")
        
        print("\n4. 测试向后兼容性...")
        try:
            await test_instance.test_jupiter_swap_success_after_fix_compatibility()
            print("✅ 向后兼容性测试完成")
        except Exception as e:
            print(f"❌ 向后兼容性测试失败: {e}")
    
    # 运行异步测试
    asyncio.run(run_async_tests()) 