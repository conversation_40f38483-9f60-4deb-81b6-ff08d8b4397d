from typing import List, Optional, Dict, Any
from datetime import datetime
from models.gmgn_token_links import GmgnTokenLinks
from .base_dao import BaseDAO
from pymongo import UpdateOne

class GmgnTokenLinksDAO(BaseDAO[GmgnTokenLinks]):
    """GMGN代币链接数据访问对象"""
    
    def __init__(self):
        super().__init__(GmgnTokenLinks)
    
    async def find_by_address(self, chain: str, address: str) -> Optional[GmgnTokenLinks]:
        """根据链和地址获取代币链接信息
        
        Args:
            chain: 链名称
            address: 代币地址
            
        Returns:
            Optional[GmgnTokenLinks]: 代币链接信息
        """
        return await self.find_one({
            "chain": chain,
            "address": address
        })
    
    async def find_by_twitter(self, twitter_username: str) -> List[GmgnTokenLinks]:
        """根据Twitter用户名获取代币链接信息
        
        Args:
            twitter_username: Twitter用户名
            
        Returns:
            List[GmgnTokenLinks]: 代币链接信息列表
        """
        return await self.find_many({
            "twitter_username": twitter_username
        })
    
    async def find_by_verify_status(self, verify_status: int) -> List[GmgnTokenLinks]:
        """获取指定验证状态的代币链接信息
        
        Args:
            verify_status: 验证状态
            
        Returns:
            List[GmgnTokenLinks]: 代币链接信息列表
        """
        return await self.find_many({
            "verify_status": verify_status
        })
    
    async def find_by_rug_ratio_range(self, min_ratio: str = '0', max_ratio: str = '100') -> List[GmgnTokenLinks]:
        """获取指定Rug比率范围内的代币链接信息
        
        Args:
            min_ratio: 最小Rug比率
            max_ratio: 最大Rug比率
            
        Returns:
            List[GmgnTokenLinks]: 代币链接信息列表
        """
        return await self.find_many({
            "rug_ratio": {
                "$gte": min_ratio,
                "$lte": max_ratio
            }
        })
    
    async def find_by_holder_count_range(
        self,
        min_count: int = 0,
        max_count: Optional[int] = None
    ) -> List[GmgnTokenLinks]:
        """获取指定持有者数量范围内的代币链接信息
        
        Args:
            min_count: 最小持有者数量
            max_count: 最大持有者数量
            
        Returns:
            List[GmgnTokenLinks]: 代币链接信息列表
        """
        query = {"holder_token_num": {"$gte": min_count}}
        if max_count is not None:
            query["holder_token_num"]["$lte"] = max_count
            
        return await self.find_many(query)
    
    async def find_most_liked(self, limit: int = 10) -> List[GmgnTokenLinks]:
        """获取点赞数最多的代币链接信息
        
        Args:
            limit: 返回数量限制
            
        Returns:
            List[GmgnTokenLinks]: 代币链接信息列表
        """
        return await self.find_many(
            {},
            limit=limit,
            sort=[("like_count", -1)]
        )
    
    async def find_most_unliked(self, limit: int = 10) -> List[GmgnTokenLinks]:
        """获取踩数最多的代币链接信息
        
        Args:
            limit: 返回数量限制
            
        Returns:
            List[GmgnTokenLinks]: 代币链接信息列表
        """
        return await self.find_many(
            {},
            limit=limit,
            sort=[("unlike_count", -1)]
        )
    
    async def find_by_social_media_presence(
        self,
        has_twitter: bool = False,
        has_telegram: bool = False,
        has_discord: bool = False,
        has_website: bool = False
    ) -> List[GmgnTokenLinks]:
        """获取具有指定社交媒体存在的代币链接信息
        
        Args:
            has_twitter: 是否需要有Twitter
            has_telegram: 是否需要有Telegram
            has_discord: 是否需要有Discord
            has_website: 是否需要有网站
            
        Returns:
            List[GmgnTokenLinks]: 代币链接信息列表
        """
        query = {"$and": []}
        
        if has_twitter:
            query["$and"].append({"twitter_username": {"$ne": None, "$ne": ""}})
        if has_telegram:
            query["$and"].append({"telegram": {"$ne": None, "$ne": ""}})
        if has_discord:
            query["$and"].append({"discord": {"$ne": None, "$ne": ""}})
        if has_website:
            query["$and"].append({"website": {"$ne": None, "$ne": ""}})
            
        if not query["$and"]:
            return []
            
        return await self.find_many(query)
    
    async def upsert_token_links(self, chain: str, data: Dict[str, Any]) -> bool:
        """更新或插入代币链接信息
        
        Args:
            chain: 链名称
            data: API返回的数据
            
        Returns:
            bool: 是否成功
        """
        address = data.get('address', '')
        if not address:
            return False
            
        # 创建新的代币链接实例
        token_links = await GmgnTokenLinks.from_api_data(chain, data)
        
        # 更新时间
        token_links.updated_at = datetime.utcnow()
        
        # 使用原子更新操作
        return await self.update_one(
            {
                "chain": chain,
                "address": address
            },
            token_links.dict(),
            upsert=True
        )
    
    async def upsert_token_links_many(self, chain: str, data_list: List[Dict[str, Any]]) -> int:
        """批量更新或插入代币链接信息
        
        Args:
            chain: 链名称
            data_list: API返回的数据列表
            
        Returns:
            int: 更新或插入的记录数
        """
        if not data_list:
            return 0
            
        # 准备批量更新操作
        bulk_operations = []
        current_time = datetime.utcnow()
        
        for data in data_list:
            address = data.get('address')
            if not address:
                continue
                
            # 创建新的代币链接实例
            token_links = await GmgnTokenLinks.from_api_data(chain, data)
            token_links.updated_at = current_time
            
            # 创建更新操作
            bulk_operations.append(
                UpdateOne(
                    {
                        "chain": chain,
                        "address": address
                    },
                    {"$set": token_links.dict()},
                    upsert=True
                )
            )
        
        # 执行批量更新
        try:
            result = await self.collection.bulk_write(bulk_operations)
            self.logger.info(f"批量更新完成: 修改 {result.modified_count} 条, 插入 {result.upserted_count} 条")
            return result.modified_count + result.upserted_count
        except Exception as e:
            self.logger.error(f"批量更新代币链接数据失败: {str(e)}")
            return 0
    
    async def delete_by_address(self, chain: str, address: str) -> bool:
        """根据链和地址删除代币链接信息
        
        Args:
            chain: 链名称
            address: 代币地址
            
        Returns:
            bool: 是否删除成功
        """
        return await self.delete_one({
            "chain": chain,
            "address": address
        })
    
    async def upsert_many(self, chain: str, data_list: List[Dict[str, Any]]) -> int:
        """批量更新或插入代币链接信息（StorageNode兼容方法）
        
        这是upsert_token_links_many方法的别名，用于兼容StorageNode类
        
        Args:
            chain: 链名称
            data_list: API返回的数据列表
            
        Returns:
            int: 更新或插入的记录数
        """
        return await self.upsert_token_links_many(chain, data_list) 