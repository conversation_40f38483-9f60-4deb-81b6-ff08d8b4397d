name: "GMGN 代币KOL活动记录"
description: "定期获取链上的KOL活动记录，并将数据存储到数据库"

nodes:
  - name: "KolActivitySchedulerNode"
    node_type: "input"
    interval: 10
    generate_data: workflows.gmgn_token_link.handler.generate_data
    flow_control:
      max_pending_messages: 10
      check_interval: 1
      enable_flow_control: true

  - name: "KolActivityMonitorNode"
    node_type: "process"
    depend_ons: ["KolActivitySchedulerNode"]
    concurrency: 1
    interval: 1
    process_item: workflows.gmgn_kol_activity.handler.process_kol_activity

  - name: "KolActivityStoreNode"
    node_type: "storage"
    depend_ons: ["KolActivityMonitorNode"]
    batch_size: 1000  # 每批处理1000条数据
    store_data: workflows.gmgn_kol_activity.handler.store_data
    validate: workflows.gmgn_kol_activity.handler.validate 
