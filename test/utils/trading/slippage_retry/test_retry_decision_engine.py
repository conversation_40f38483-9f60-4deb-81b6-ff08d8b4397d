"""重试决策引擎单元测试"""

import unittest
from unittest.mock import patch, MagicMock, PropertyMock
from typing import Dict, Any
from models.slippage_retry import SlippageRetryConfig, RetryDecision
from utils.trading.slippage_retry.retry_decision_engine import RetryDecisionEngine


class MockTradeInterface:
    """用于测试的Mock交易接口"""
    
    def __init__(self, 
                 slippage_errors: list = None, 
                 non_retryable_errors: list = None,
                 should_raise_exception: bool = False):
        self.slippage_errors = slippage_errors or []
        self.non_retryable_errors = non_retryable_errors or []
        self.should_raise_exception = should_raise_exception
        self.call_count = 0
    
    def is_slippage_related_error(self, error_message: str = None, provider_response: Dict[str, Any] = None) -> bool:
        """Mock滑点错误识别"""
        self.call_count += 1
        if self.should_raise_exception:
            raise Exception("Mock error identification failed")
        
        if error_message:
            return any(keyword in error_message.lower() for keyword in self.slippage_errors)
        
        if provider_response:
            error_code = provider_response.get('error_code')
            return error_code in ['SLIPPAGE_EXCEEDED', 'PRICE_IMPACT_HIGH']
        
        return False
    
    def is_non_retryable_error(self, error_message: str = None, provider_response: Dict[str, Any] = None) -> bool:
        """Mock不可重试错误识别"""
        self.call_count += 1
        if self.should_raise_exception:
            raise Exception("Mock error identification failed")
        
        if error_message:
            return any(keyword in error_message.lower() for keyword in self.non_retryable_errors)
        
        if provider_response:
            error_code = provider_response.get('error_code')
            return error_code in ['INSUFFICIENT_FUNDS', 'INVALID_TOKEN']
        
        return False


# 辅助函数：创建 mock RetryDecision
def create_mock_retry_decision(**kwargs) -> MagicMock:
    """创建一个配置好的 MagicMock 实例来替代 RetryDecision。"""
    mock_decision = MagicMock(spec=RetryDecision) # 使用 spec 确保接口一致性
    
    # 从 kwargs 设置 RetryDecision 的所有字段属性
    mock_decision.should_retry = kwargs.get('should_retry')
    mock_decision.should_adjust_slippage = kwargs.get('should_adjust_slippage')
    mock_decision.retry_count = kwargs.get('retry_count')
    mock_decision.max_retries = kwargs.get('max_retries')
    mock_decision.current_slippage = kwargs.get('current_slippage')
    mock_decision.max_slippage = kwargs.get('max_slippage')
    mock_decision.is_slippage_related_error = kwargs.get('is_slippage_related_error')
    mock_decision.error_message = kwargs.get('error_message')
    mock_decision.slippage_retry_enabled = kwargs.get('slippage_retry_enabled')
    mock_decision.decision_reason = kwargs.get('decision_reason')
    
    status = "继续重试" if mock_decision.should_retry else "停止重试"
    slippage_action = "并调整滑点" if mock_decision.should_adjust_slippage else "但不调整滑点"
    reason_text = mock_decision.decision_reason if mock_decision.decision_reason is not None else "N/A"
    summary_val = f"{status}{slippage_action}: {reason_text}"
    
    # 使用 PropertyMock 来模拟 @property decision_summary
    # 这确保了当测试代码访问 decision.decision_summary 时，它会得到我们构造的 summary_val
    type(mock_decision).decision_summary = PropertyMock(return_value=summary_val)
    
    return mock_decision


@patch('utils.trading.slippage_retry.retry_decision_engine.RetryDecision', side_effect=create_mock_retry_decision)
class TestRetryDecisionEngine(unittest.TestCase):
    """重试决策引擎测试类"""
    
    def setUp(self) -> None:
        """测试前准备"""
        self.engine = RetryDecisionEngine()
        self.config = SlippageRetryConfig(
            enabled=True,
            increment_percentage=0.5,
            max_slippage_percentage=5.0,
            retry_delay_seconds=0.5
        )
    
    def test_should_continue_retry_success(self, MockRetryDecision: MagicMock) -> None:
        """测试继续重试的判断 - 成功情况"""
        result = self.engine.should_continue_retry(
            retry_count=2,
            max_retries=5,
            error_message="network timeout"
        )
        self.assertTrue(result)
    
    def test_should_continue_retry_max_reached(self, MockRetryDecision: MagicMock) -> None:
        """测试继续重试的判断 - 达到最大重试次数"""
        result = self.engine.should_continue_retry(
            retry_count=5,
            max_retries=5,
            error_message="network timeout"
        )
        self.assertFalse(result)
    
    def test_should_continue_retry_non_retryable_error(self, MockRetryDecision: MagicMock) -> None:
        """测试继续重试的判断 - 不可重试错误"""
        non_retryable_errors = [
            "insufficient funds",
            "insufficient balance", 
            "token not found",
            "invalid token",
            "unauthorized",
            "forbidden",
            "account not found",
            "invalid private key",
            "signature verification failed"
        ]
        
        for error_msg in non_retryable_errors:
            with self.subTest(error_message=error_msg):
                result = self.engine.should_continue_retry(
                    retry_count=1,
                    max_retries=5,
                    error_message=error_msg
                )
                self.assertFalse(result)
    
    def test_should_increase_slippage_enabled(self, MockRetryDecision: MagicMock) -> None:
        """测试滑点增加判断 - 功能启用且为滑点错误"""
        result = self.engine.should_increase_slippage(
            error_message="slippage tolerance exceeded",
            config=self.config,
            current_slippage=2.0
        )
        self.assertTrue(result)
    
    def test_should_increase_slippage_disabled(self, MockRetryDecision: MagicMock) -> None:
        """测试滑点增加判断 - 功能禁用"""
        disabled_config = SlippageRetryConfig(
            enabled=False,
            increment_percentage=0.5,
            max_slippage_percentage=5.0,
            retry_delay_seconds=0.5
        )
        
        result = self.engine.should_increase_slippage(
            error_message="slippage tolerance exceeded",
            config=disabled_config,
            current_slippage=2.0
        )
        self.assertFalse(result)
    
    def test_should_increase_slippage_non_slippage_error(self, MockRetryDecision: MagicMock) -> None:
        """测试滑点增加判断 - 非滑点相关错误"""
        result = self.engine.should_increase_slippage(
            error_message="network timeout",
            config=self.config,
            current_slippage=2.0
        )
        self.assertFalse(result)
    
    def test_should_increase_slippage_at_limit(self, MockRetryDecision: MagicMock) -> None:
        """测试滑点增加判断 - 已达到滑点上限"""
        result = self.engine.should_increase_slippage(
            error_message="slippage tolerance exceeded",
            config=self.config,
            current_slippage=5.0  # 等于max_slippage_percentage
        )
        self.assertFalse(result)
    
    def test_should_increase_slippage_exceeds_limit(self, MockRetryDecision: MagicMock) -> None:
        """测试滑点增加判断 - 超过滑点上限"""
        result = self.engine.should_increase_slippage(
            error_message="slippage tolerance exceeded",
            config=self.config,
            current_slippage=6.0  # 超过max_slippage_percentage
        )
        self.assertFalse(result)
    
    def test_is_slippage_related_error_positive(self, MockRetryDecision: MagicMock) -> None:
        """测试滑点相关错误识别 - 正面情况"""
        slippage_errors = [
            "slippage tolerance exceeded",
            "price impact too high",
            "insufficient output amount",
            "would result in less than minimum",
            "minimum received not met",
            "exceeds maximum slippage allowed",
            "price moved too much during execution"
        ]
        
        for error_msg in slippage_errors:
            with self.subTest(error_message=error_msg):
                result = self.engine._is_slippage_related_error(error_msg)
                self.assertTrue(result)
    
    def test_is_slippage_related_error_negative(self, MockRetryDecision: MagicMock) -> None:
        """测试滑点相关错误识别 - 负面情况"""
        non_slippage_errors = [
            "network timeout",
            "server error",
            "insufficient funds",
            "token not found",
            "unauthorized access",
            "invalid signature",
            "",
            None
        ]
        
        for error_msg in non_slippage_errors:
            with self.subTest(error_message=error_msg):
                result = self.engine._is_slippage_related_error(error_msg)
                self.assertFalse(result)
    
    def test_is_slippage_related_error_case_insensitive(self, MockRetryDecision: MagicMock) -> None:
        """测试滑点相关错误识别 - 大小写不敏感"""
        test_cases = [
            "SLIPPAGE TOLERANCE EXCEEDED",
            "Price Impact Too High",
            "INSUFFICIENT OUTPUT AMOUNT",
            "Would Result In Less"
        ]
        
        for error_msg in test_cases:
            with self.subTest(error_message=error_msg):
                result = self.engine._is_slippage_related_error(error_msg)
                self.assertTrue(result)
    
    def test_is_non_retryable_error_positive(self, MockRetryDecision: MagicMock) -> None:
        """测试不可重试错误识别 - 正面情况"""
        non_retryable_errors = [
            "insufficient funds for transaction",
            "insufficient balance in wallet",
            "token not found on chain",
            "invalid token address",
            "unauthorized transaction",
            "forbidden operation",
            "account not found",
            "invalid private key format",
            "signature verification failed"
        ]
        
        for error_msg in non_retryable_errors:
            with self.subTest(error_message=error_msg):
                result = self.engine._is_non_retryable_error(error_msg)
                self.assertTrue(result)
    
    def test_is_non_retryable_error_negative(self, MockRetryDecision: MagicMock) -> None:
        """测试不可重试错误识别 - 负面情况"""
        retryable_errors = [
            "network timeout",
            "server temporarily unavailable",
            "slippage tolerance exceeded",
            "price impact too high",
            "connection refused",
            "",
            None
        ]
        
        for error_msg in retryable_errors:
            with self.subTest(error_message=error_msg):
                result = self.engine._is_non_retryable_error(error_msg)
                self.assertFalse(result)
    
    def test_make_retry_decision_continue_with_slippage_adjustment(self, MockRetryDecision: MagicMock) -> None:
        """测试综合决策 - 继续重试并调整滑点"""
        decision = self.engine.make_retry_decision(
            retry_count=2,
            max_retries=5,
            current_slippage=2.0,
            config=self.config,
            error_message="slippage tolerance exceeded"
        )
        self.assertTrue(decision.should_retry)
        self.assertTrue(decision.should_adjust_slippage)
        self.assertIn("继续重试并调整滑点", decision.decision_summary)
        self.assertIn("检测到滑点相关错误", decision.decision_reason)
        MockRetryDecision.assert_called_once()
        call_args = MockRetryDecision.call_args[0]
        kwargs_passed_to_real_constructor = MockRetryDecision.call_args[1]
        self.assertEqual(kwargs_passed_to_real_constructor.get('retry_count'), 2)
        self.assertEqual(kwargs_passed_to_real_constructor.get('current_slippage'), 2.0)
        self.assertTrue(kwargs_passed_to_real_constructor.get('is_slippage_related_error'))
    
    def test_make_retry_decision_continue_without_slippage_adjustment(self, MockRetryDecision: MagicMock) -> None:
        """测试综合决策 - 继续重试但不调整滑点"""
        decision = self.engine.make_retry_decision(
            retry_count=2,
            max_retries=5,
            current_slippage=2.0,
            config=self.config,
            error_message="network timeout"  # 非滑点错误
        )
        
        self.assertTrue(decision.should_retry)
        self.assertFalse(decision.should_adjust_slippage)
        self.assertFalse(decision.is_slippage_related_error)
        self.assertIn("继续重试", decision.decision_reason)
    
    def test_make_retry_decision_stop_max_retries(self, MockRetryDecision: MagicMock) -> None:
        """测试综合决策 - 达到最大重试次数停止"""
        decision = self.engine.make_retry_decision(
            retry_count=5,
            max_retries=5,
            current_slippage=2.0,
            config=self.config,
            error_message="slippage tolerance exceeded"
        )
        
        self.assertFalse(decision.should_retry)
        self.assertFalse(decision.should_adjust_slippage)
        self.assertIn("已达到最大重试次数", decision.decision_reason)
    
    def test_make_retry_decision_stop_non_retryable_error(self, MockRetryDecision: MagicMock) -> None:
        """测试综合决策 - 不可重试错误停止"""
        decision = self.engine.make_retry_decision(
            retry_count=1,
            max_retries=5,
            current_slippage=2.0,
            config=self.config,
            error_message="insufficient funds"
        )
        
        self.assertFalse(decision.should_retry)
        self.assertFalse(decision.should_adjust_slippage)
        self.assertIn("不可重试错误", decision.decision_reason)
    
    def test_make_retry_decision_slippage_at_limit(self, MockRetryDecision: MagicMock) -> None:
        """测试综合决策 - 滑点已达上限"""
        decision = self.engine.make_retry_decision(
            retry_count=2,
            max_retries=5,
            current_slippage=5.0,  # 等于max_slippage_percentage
            config=self.config,
            error_message="slippage tolerance exceeded"
        )
        
        self.assertTrue(decision.should_retry)
        self.assertFalse(decision.should_adjust_slippage)
        self.assertIn("继续重试", decision.decision_reason)
        self.assertIn("已达上限", decision.decision_reason)
    
    def test_make_retry_decision_disabled_config(self, MockRetryDecision: MagicMock) -> None:
        """测试综合决策 - 滑点重试功能禁用"""
        disabled_config = SlippageRetryConfig(
            enabled=False,
            increment_percentage=0.5,
            max_slippage_percentage=5.0,
            retry_delay_seconds=0.5
        )
        
        decision = self.engine.make_retry_decision(
            retry_count=2,
            max_retries=5,
            current_slippage=2.0,
            config=disabled_config,
            error_message="slippage tolerance exceeded"
        )
        
        self.assertTrue(decision.should_retry)
        self.assertFalse(decision.should_adjust_slippage)
        self.assertFalse(decision.slippage_retry_enabled)
        self.assertIn("滑点递增功能未启用", decision.decision_reason)
    
    def test_generate_decision_reason_stop_max_retries(self, MockRetryDecision: MagicMock) -> None:
        """测试决策原因生成 - 达到最大重试次数"""
        reason = self.engine._generate_decision_reason(
            should_retry=False,
            should_adjust_slippage=False,
            retry_count=5,
            max_retries=5,
            current_slippage=2.0,
            config=self.config,
            error_message="network timeout"
        )
        
        self.assertIn("已达到最大重试次数", reason)
        self.assertIn("(5)", reason)
    
    def test_generate_decision_reason_stop_non_retryable(self, MockRetryDecision: MagicMock) -> None:
        """测试决策原因生成 - 不可重试错误"""
        reason = self.engine._generate_decision_reason(
            should_retry=False,
            should_adjust_slippage=False,
            retry_count=2,
            max_retries=5,
            current_slippage=2.0,
            config=self.config,
            error_message="insufficient funds"
        )
        
        self.assertIn("不可重试错误", reason)
    
    def test_generate_decision_reason_continue_with_adjustment(self, MockRetryDecision: MagicMock) -> None:
        """测试决策原因生成 - 继续重试并调整滑点"""
        reason = self.engine._generate_decision_reason(
            should_retry=True,
            should_adjust_slippage=True,
            retry_count=2,
            max_retries=5,
            current_slippage=2.0,
            config=self.config,
            error_message="slippage tolerance exceeded"
        )
        
        self.assertIn("继续重试", reason)
        self.assertIn("(3/5)", reason)  # retry_count + 1
        self.assertIn("从 2.0% 增加滑点", reason)
    
    def test_generate_decision_reason_continue_config_disabled(self, MockRetryDecision: MagicMock) -> None:
        """测试决策原因生成 - 继续重试但配置禁用"""
        disabled_config = SlippageRetryConfig(
            enabled=False,
            increment_percentage=0.5,
            max_slippage_percentage=5.0,
            retry_delay_seconds=0.5
        )
        
        reason = self.engine._generate_decision_reason(
            should_retry=True,
            should_adjust_slippage=False,
            retry_count=2,
            max_retries=5,
            current_slippage=2.0,
            config=disabled_config,
            error_message="slippage tolerance exceeded"
        )
        
        self.assertIn("继续重试", reason)
        self.assertIn("滑点递增功能未启用", reason)
    
    def test_generate_decision_reason_continue_at_limit(self, MockRetryDecision: MagicMock) -> None:
        """测试决策原因生成 - 继续重试但滑点已达上限"""
        reason = self.engine._generate_decision_reason(
            should_retry=True,
            should_adjust_slippage=False,
            retry_count=2,
            max_retries=5,
            current_slippage=5.0,
            config=self.config,
            error_message="slippage tolerance exceeded"
        )
        
        self.assertIn("继续重试", reason)
        self.assertIn("滑点已达上限", reason)
        self.assertIn("(5.0%)", reason)
    
    def test_generate_decision_reason_continue_non_slippage_error(self, MockRetryDecision: MagicMock) -> None:
        """测试决策原因生成 - 继续重试但非滑点错误"""
        reason = self.engine._generate_decision_reason(
            should_retry=True,
            should_adjust_slippage=False,
            retry_count=2,
            max_retries=5,
            current_slippage=2.0,
            config=self.config,
            error_message="network timeout"
        )
        
        self.assertIn("继续重试", reason)
        self.assertIn("错误与滑点无关", reason)
    
    def test_edge_cases(self, MockRetryDecision: MagicMock) -> None:
        """测试边界情况"""
        # 空错误消息
        decision = self.engine.make_retry_decision(
            retry_count=1,
            max_retries=3,
            current_slippage=1.0,
            config=self.config,
            error_message=""
        )
        self.assertTrue(decision.should_retry)
        self.assertFalse(decision.should_adjust_slippage)
        
        # None错误消息
        decision = self.engine.make_retry_decision(
            retry_count=1,
            max_retries=3,
            current_slippage=1.0,
            config=self.config,
            error_message=None
        )
        self.assertTrue(decision.should_retry)
        self.assertFalse(decision.should_adjust_slippage)
        
        # 重试次数为0
        decision = self.engine.make_retry_decision(
            retry_count=0,
            max_retries=3,
            current_slippage=1.0,
            config=self.config,
            error_message="slippage tolerance exceeded"
        )
        self.assertTrue(decision.should_retry)
        self.assertTrue(decision.should_adjust_slippage)
        
        # 当前滑点为0
        decision = self.engine.make_retry_decision(
            retry_count=1,
            max_retries=3,
            current_slippage=0.0,
            config=self.config,
            error_message="slippage tolerance exceeded"
        )
        self.assertTrue(decision.should_retry)
        self.assertTrue(decision.should_adjust_slippage)
    
    def test_complex_scenarios(self, MockRetryDecision: MagicMock) -> None:
        """测试复杂场景组合"""
        # 场景1: 滑点错误 + 未达到重试上限 + 未达到滑点上限
        decision = self.engine.make_retry_decision(
            retry_count=2,
            max_retries=5,
            current_slippage=3.0,
            config=self.config,
            error_message="slippage tolerance exceeded"
        )
        
        self.assertTrue(decision.should_retry)
        self.assertTrue(decision.should_adjust_slippage)
        self.assertTrue(decision.is_slippage_related_error)
        self.assertIn("继续重试", decision.decision_reason)
        self.assertIn("检测到滑点相关错误", decision.decision_reason)
        
        # 场景2: 非滑点错误 + 未达到重试上限
        decision = self.engine.make_retry_decision(
            retry_count=2,
            max_retries=5,
            current_slippage=3.0,
            config=self.config,
            error_message="network timeout"
        )
        
        self.assertTrue(decision.should_retry)
        self.assertFalse(decision.should_adjust_slippage)
        self.assertFalse(decision.is_slippage_related_error)
        self.assertIn("继续重试", decision.decision_reason)
        self.assertIn("错误与滑点无关", decision.decision_reason)
        
        # 场景3: 滑点错误 + 已达到滑点上限
        decision = self.engine.make_retry_decision(
            retry_count=2,
            max_retries=5,
            current_slippage=5.0,  # 等于max_slippage_percentage
            config=self.config,
            error_message="slippage tolerance exceeded"
        )
        
        self.assertTrue(decision.should_retry)
        self.assertFalse(decision.should_adjust_slippage)
        self.assertTrue(decision.is_slippage_related_error)
        self.assertIn("继续重试", decision.decision_reason)
        self.assertIn("滑点已达上限", decision.decision_reason)

    # ==================== R1.5 错误识别机制增强测试 ====================
    
    def test_professional_error_identification_with_trade_interface(self, MockRetryDecision: MagicMock) -> None:
        """测试使用交易接口的专业错误识别功能"""
        # 创建Mock交易接口，定义GMGN特定的错误关键词
        mock_trade_interface = MockTradeInterface(
            slippage_errors=['gmgn_slippage_exceeded', 'jupiter_price_impact'],
            non_retryable_errors=['gmgn_insufficient_balance', 'jupiter_invalid_token']
        )
        
        # 创建带有交易接口的决策引擎
        engine_with_interface = RetryDecisionEngine(trade_interface=mock_trade_interface)
        
        # 测试滑点相关错误识别
        result = engine_with_interface._is_slippage_related_error("GMGN_slippage_exceeded error occurred")
        self.assertTrue(result)
        self.assertEqual(mock_trade_interface.call_count, 1)
        
        # 测试不可重试错误识别
        mock_trade_interface.call_count = 0  # 重置计数
        result = engine_with_interface._is_non_retryable_error("GMGN_insufficient_balance detected")
        self.assertTrue(result)
        self.assertEqual(mock_trade_interface.call_count, 1)
    
    def test_professional_error_identification_with_provider_response(self, MockRetryDecision: MagicMock) -> None:
        """测试使用provider_response的专业错误识别"""
        mock_trade_interface = MockTradeInterface()
        engine_with_interface = RetryDecisionEngine(trade_interface=mock_trade_interface)
        
        # 测试通过provider_response识别滑点错误
        provider_response = {
            'error_code': 'SLIPPAGE_EXCEEDED',
            'message': 'Transaction failed due to slippage',
            'details': {'current_slippage': 2.5, 'max_allowed': 2.0}
        }
        
        result = engine_with_interface._is_slippage_related_error(
            error_message=None, 
            provider_response=provider_response
        )
        self.assertTrue(result)
        
        # 测试通过provider_response识别不可重试错误
        provider_response = {
            'error_code': 'INSUFFICIENT_FUNDS',
            'message': 'Wallet balance too low',
            'balance': 0.001
        }
        
        result = engine_with_interface._is_non_retryable_error(
            error_message=None,
            provider_response=provider_response
        )
        self.assertTrue(result)
    
    def test_fallback_to_generic_identification_on_exception(self, MockRetryDecision: MagicMock) -> None:
        """测试交易接口异常时回退到通用识别"""
        # 创建会抛出异常的Mock交易接口
        mock_trade_interface = MockTradeInterface(should_raise_exception=True)
        engine_with_interface = RetryDecisionEngine(trade_interface=mock_trade_interface)
        
        # 测试滑点错误识别回退
        with patch('utils.trading.slippage_retry.retry_decision_engine.logger') as mock_logger:
            result = engine_with_interface._is_slippage_related_error("slippage tolerance exceeded")
            
            # 应该回退到通用识别并返回True
            self.assertTrue(result)
            # 应该记录警告日志
            mock_logger.warning.assert_called_once()
            self.assertIn("交易接口滑点错误识别失败，回退到通用识别", 
                         mock_logger.warning.call_args[0][0])
        
        # 测试不可重试错误识别回退
        with patch('utils.trading.slippage_retry.retry_decision_engine.logger') as mock_logger:
            result = engine_with_interface._is_non_retryable_error("insufficient funds")
            
            # 应该回退到通用识别并返回True
            self.assertTrue(result)
            # 应该记录警告日志
            mock_logger.warning.assert_called_once()
            self.assertIn("交易接口不可重试错误识别失败，回退到通用识别", 
                         mock_logger.warning.call_args[0][0])
    
    def test_generic_identification_when_no_trade_interface(self, MockRetryDecision: MagicMock) -> None:
        """测试没有交易接口时使用通用识别"""
        # 使用没有交易接口的决策引擎
        engine_without_interface = RetryDecisionEngine(trade_interface=None)
        
        # 测试通用滑点错误识别
        result = engine_without_interface._is_slippage_related_error("slippage tolerance exceeded")
        self.assertTrue(result)
        
        result = engine_without_interface._is_slippage_related_error("network timeout")
        self.assertFalse(result)
        
        # 测试通用不可重试错误识别
        result = engine_without_interface._is_non_retryable_error("insufficient funds")
        self.assertTrue(result)
        
        result = engine_without_interface._is_non_retryable_error("network timeout")
        self.assertFalse(result)
    
    def test_make_retry_decision_with_trade_interface_and_provider_response(self, MockRetryDecision: MagicMock) -> None:
        """测试综合决策中使用交易接口和provider_response"""
        mock_trade_interface = MockTradeInterface(
            slippage_errors=['custom_slippage_error']
        )
        engine_with_interface = RetryDecisionEngine(trade_interface=mock_trade_interface)
        
        # 测试使用交易接口识别的滑点错误
        provider_response = {
            'error_code': 'SLIPPAGE_EXCEEDED',
            'api_response': {'status': 'failed', 'reason': 'slippage'}
        }
        
        decision = engine_with_interface.make_retry_decision(
            retry_count=1,
            max_retries=3,
            current_slippage=2.0,
            config=self.config,
            error_message="custom_slippage_error detected",
            provider_response=provider_response
        )
        
        # 验证决策结果
        self.assertTrue(decision.should_retry)
        self.assertTrue(decision.should_adjust_slippage)
        self.assertTrue(decision.is_slippage_related_error)
        self.assertIn("检测到滑点相关错误", decision.decision_reason)
        
        # 验证交易接口被调用
        self.assertGreater(mock_trade_interface.call_count, 0)
    
    def test_should_continue_retry_with_provider_response(self, MockRetryDecision: MagicMock) -> None:
        """测试should_continue_retry方法使用provider_response参数"""
        mock_trade_interface = MockTradeInterface(
            non_retryable_errors=['custom_fatal_error']
        )
        engine_with_interface = RetryDecisionEngine(trade_interface=mock_trade_interface)
        
        # 测试通过provider_response识别不可重试错误
        provider_response = {
            'error_code': 'INSUFFICIENT_FUNDS',
            'wallet_balance': 0.0001,
            'required_amount': 0.01
        }
        
        result = engine_with_interface.should_continue_retry(
            retry_count=1,
            max_retries=5,
            error_message="custom_fatal_error occurred",
            provider_response=provider_response
        )
        
        # 应该停止重试
        self.assertFalse(result)
        
        # 验证交易接口被调用
        self.assertGreater(mock_trade_interface.call_count, 0)
    
    def test_should_increase_slippage_with_provider_response(self, MockRetryDecision: MagicMock) -> None:
        """测试should_increase_slippage方法使用provider_response参数"""
        mock_trade_interface = MockTradeInterface(
            slippage_errors=['custom_slippage_error']
        )
        engine_with_interface = RetryDecisionEngine(trade_interface=mock_trade_interface)
        
        # 测试通过provider_response识别滑点错误
        provider_response = {
            'error_code': 'SLIPPAGE_EXCEEDED',
            'current_price_impact': 3.5,
            'max_allowed_impact': 2.0
        }
        
        result = engine_with_interface.should_increase_slippage(
            error_message="custom_slippage_error detected",
            config=self.config,
            current_slippage=2.0,
            provider_response=provider_response
        )
        
        # 应该增加滑点
        self.assertTrue(result)
        
        # 验证交易接口被调用
        self.assertGreater(mock_trade_interface.call_count, 0)
    
    def test_debug_logging_for_professional_identification(self, MockRetryDecision: MagicMock) -> None:
        """测试专业错误识别的调试日志"""
        mock_trade_interface = MockTradeInterface(
            slippage_errors=['test_slippage']
        )
        engine_with_interface = RetryDecisionEngine(trade_interface=mock_trade_interface)
        
        with patch('utils.trading.slippage_retry.retry_decision_engine.logger') as mock_logger:
            # 测试专业识别成功的日志
            result = engine_with_interface._is_slippage_related_error("test_slippage error")
            
            self.assertTrue(result)
            mock_logger.debug.assert_called_with("使用交易接口专业识别滑点错误: True")
    
    def test_backward_compatibility_with_existing_methods(self, MockRetryDecision: MagicMock) -> None:
        """测试向后兼容性 - 确保现有方法调用仍然有效"""
        # 测试不带provider_response参数的旧式调用
        decision = self.engine.make_retry_decision(
            retry_count=1,
            max_retries=3,
            current_slippage=2.0,
            config=self.config,
            error_message="slippage tolerance exceeded"
            # 注意：没有provider_response参数
        )
        
        # 应该仍然正常工作
        self.assertTrue(decision.should_retry)
        self.assertTrue(decision.should_adjust_slippage)
        self.assertTrue(decision.is_slippage_related_error)
        
        # 测试不带provider_response参数的should_continue_retry调用
        result = self.engine.should_continue_retry(
            retry_count=1,
            max_retries=3,
            error_message="network timeout"
            # 注意：没有provider_response参数
        )
        
        self.assertTrue(result)


if __name__ == '__main__':
    unittest.main() 