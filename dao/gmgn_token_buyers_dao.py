from typing import List, Optional, Dict, Any
from datetime import datetime
from models.gmgn_token_buyers import GmgnTokenBuyers
from .base_dao import BaseDAO
from pymongo import UpdateOne

class GmgnTokenBuyersDAO(BaseDAO[GmgnTokenBuyers]):
    """GMGN代币买家数据访问对象"""
    
    def __init__(self):
        super().__init__(GmgnTokenBuyers)
    
    async def find_by_address(self, chain: str, address: str) -> Optional[GmgnTokenBuyers]:
        """根据链和地址获取代币买家信息
        
        Args:
            chain: 链名称
            address: 代币地址
            
        Returns:
            Optional[GmgnTokenBuyers]: 代币买家信息
        """
        return await self.find_one({
            "chain": chain,
            "address": address
        })
    
    async def find_by_holder_count_range(
        self,
        min_count: int = 0,
        max_count: Optional[int] = None
    ) -> List[GmgnTokenBuyers]:
        """获取指定持有者数量范围内的代币买家信息
        
        Args:
            min_count: 最小持有者数量
            max_count: 最大持有者数量
            
        Returns:
            List[GmgnTokenBuyers]: 代币买家信息列表
        """
        query = {"holder_count": {"$gte": min_count}}
        if max_count is not None:
            query["holder_count"]["$lte"] = max_count
            
        return await self.find_many(query)
    
    async def find_by_status_count(
        self,
        status: str,
        min_count: int = 0,
        max_count: Optional[int] = None
    ) -> List[GmgnTokenBuyers]:
        """获取指定状态数量范围内的代币买家信息
        
        Args:
            status: 状态类型(hold/bought_more/sold_part/sold/transfered)
            min_count: 最小数量
            max_count: 最大数量
            
        Returns:
            List[GmgnTokenBuyers]: 代币买家信息列表
        """
        query = {f"status_stats.{status}": {"$gte": min_count}}
        if max_count is not None:
            query[f"status_stats.{status}"]["$lte"] = max_count
            
        return await self.find_many(query)
    
    async def find_by_rate_range(
        self,
        rate_type: str,
        min_rate: float = 0,
        max_rate: Optional[float] = None
    ) -> List[GmgnTokenBuyers]:
        """获取指定比率范围内的代币买家信息
        
        Args:
            rate_type: 比率类型(bought_rate/holding_rate/top_10_holder_rate)
            min_rate: 最小比率
            max_rate: 最大比率
            
        Returns:
            List[GmgnTokenBuyers]: 代币买家信息列表
        """
        query = {f"status_stats.{rate_type}": {"$gte": min_rate}}
        if max_rate is not None:
            query[f"status_stats.{rate_type}"]["$lte"] = max_rate
            
        return await self.find_many(query)
    
    async def find_by_wallet_tag(self, tag: str) -> List[GmgnTokenBuyers]:
        """获取包含指定钱包标签的代币买家信息
        
        Args:
            tag: 钱包标签
            
        Returns:
            List[GmgnTokenBuyers]: 代币买家信息列表
        """
        return await self.find_many({
            "holders.tags": tag
        })
    
    async def find_by_maker_token_tag(self, tag: str) -> List[GmgnTokenBuyers]:
        """获取包含指定做市商代币标签的代币买家信息
        
        Args:
            tag: 做市商代币标签
            
        Returns:
            List[GmgnTokenBuyers]: 代币买家信息列表
        """
        return await self.find_many({
            "holders.maker_token_tags": tag
        })
    
    async def find_by_wallet_address(self, wallet_address: str) -> List[GmgnTokenBuyers]:
        """获取包含指定钱包地址的代币买家信息
        
        Args:
            wallet_address: 钱包地址
            
        Returns:
            List[GmgnTokenBuyers]: 代币买家信息列表
        """
        return await self.find_many({
            "holders.wallet_address": wallet_address
        })
    
    async def upsert_token_buyers(self, chain: str, address: str, data: Dict[str, Any]) -> bool:
        """更新或插入代币买家信息
        
        Args:
            chain: 链名称
            address: 代币地址
            data: API返回的数据
            
        Returns:
            bool: 是否成功
        """
        # 创建新的代币买家实例
        token_buyers = await GmgnTokenBuyers.from_api_data(chain, address, data)
        
        # 更新时间
        token_buyers.updated_at = datetime.utcnow()
        
        # 使用原子更新操作
        return await self.update_one(
            {
                "chain": chain,
                "address": address
            },
            token_buyers.dict(),
            upsert=True
        )
    
    async def upsert_token_buyers_many(
        self,
        chain: str,
        data_list: List[Dict[str, Any]]
    ) -> int:
        """批量更新或插入代币买家信息
        
        Args:
            chain: 链名称
            data_list: API返回的数据列表，每个元素必须包含address字段
            
        Returns:
            int: 更新或插入的记录数
        """
        if not data_list:
            return 0
            
        # 准备批量更新操作
        bulk_operations = []
        current_time = datetime.utcnow()
        
        for data in data_list:
            address = data.get('address')
            if not address:
                continue
                
            # 创建新的代币买家实例
            token_buyers = await GmgnTokenBuyers.from_api_data(chain, address, data)
            token_buyers.updated_at = current_time
            
            # 创建更新操作
            bulk_operations.append(
                UpdateOne(
                    {
                        "chain": chain,
                        "address": address
                    },
                    {"$set": token_buyers.dict()},
                    upsert=True
                )
            )
        
        # 执行批量更新
        try:
            result = await self.collection.bulk_write(bulk_operations)
            self.logger.info(f"批量更新完成: 修改 {result.modified_count} 条, 插入 {result.upserted_count} 条")
            return result.modified_count + result.upserted_count
        except Exception as e:
            self.logger.error(f"批量更新代币买家数据失败: {str(e)}")
            return 0
    
    async def delete_by_address(self, chain: str, address: str) -> bool:
        """根据链和地址删除代币买家信息
        
        Args:
            chain: 链名称
            address: 代币地址
            
        Returns:
            bool: 是否删除成功
        """
        return await self.delete_one({
            "chain": chain,
            "address": address
        }) 