import json
import pandas as pd
import sys
import logging
import os
from typing import List, Dict, Any

# Setup basic logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("ResultFilter")

def filter_and_save_by_metric(json_file_path: str,
                              output_file_path: str,
                              metric_column: str,
                              threshold: float,
                              comparison_op: str = 'gt',
                              output_format: str = 'csv') -> bool:
    """Reads backtest results from a JSON file, filters them by a specified metric
    and threshold, and saves the filtered results to a CSV or JSON file.

    Args:
        json_file_path: Path to the input JSON results file.
        output_file_path: Path to save the filtered results file (CSV or JSON).
        metric_column: The key in the 'statistics' dict to filter by (e.g., 'win_rate').
        threshold: The threshold value for filtering.
        comparison_op: Comparison operator ('gt': >, 'lt': <, 'ge': >=, 'le': <=).
        output_format: The desired output format ('csv' or 'json'). Defaults to 'csv'.

    Returns:
        bool: True if filtering and saving were successful, False otherwise.
    """
    op_map = {
        'gt': lambda x, y: x > y,
        'lt': lambda x, y: x < y,
        'ge': lambda x, y: x >= y,
        'le': lambda x, y: x <= y
    }

    if comparison_op not in op_map:
        logger.error(f"错误：无效的比较运算符 '{comparison_op}'。请使用 'gt', 'lt', 'ge', or 'le'。")
        return False

    compare_func = op_map[comparison_op]

    logger.info(f"开始处理文件: {json_file_path}，筛选 {metric_column} {comparison_op} {threshold}")

    # --- Load JSON Data ---
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except FileNotFoundError:
        logger.error(f"错误：未找到输入文件 '{json_file_path}'。")
        return False
    except json.JSONDecodeError as e:
        logger.error(f"错误：无法解析 JSON 文件 '{json_file_path}'。错误: {e}")
        return False
    except Exception as e:
        logger.error(f"错误：读取文件时发生未知错误 '{json_file_path}': {e}")
        return False

    # --- Validate Data Structure ---
    if not isinstance(data, dict) or 'results' not in data or not isinstance(data['results'], list):
        logger.error(f"错误：JSON 文件 '{json_file_path}' 结构不符合预期。")
        return False

    results_list = data['results']
    time_stats = data.get('time_stats')
    logger.info(f"从 '{json_file_path}' 加载了 {len(results_list)} 条结果。")

    # --- Filter Results ---
    filtered_results_data = []
    skipped_count = 0
    last_valid_params = {}
    last_valid_stats = {}
    for i, result in enumerate(results_list):
        if not isinstance(result, dict) or \
           'statistics' not in result or not isinstance(result['statistics'], dict) or \
           'params' not in result or not isinstance(result['params'], dict):
            logger.warning(f"警告：第 {i+1} 条结果格式不完整，已跳过。")
            skipped_count += 1
            continue

        stats = result['statistics']
        params = result['params']
        last_valid_params = params # Store for column definition later
        last_valid_stats = stats   # Store for column definition later

        if metric_column not in stats:
            logger.warning(f"警告：第 {i+1} 条结果的 'statistics' 中缺少度量列 '{metric_column}'，已跳过。")
            skipped_count += 1
            continue

        try:
            metric_value = float(stats[metric_column])
            if compare_func(metric_value, threshold):
                # Flatten the result for CSV output
                flattened = {**params, **stats}
                # Add other top-level info if needed
                flattened['param_index'] = result.get('param_index')
                flattened['result_dir'] = result.get('result_dir')
                flattened['execution_time'] = result.get('execution_time')
                # filtered_results.append(flattened) # Keep flattened for CSV if needed
                filtered_results_data.append(result) # Keep original structure for JSON
        except (ValueError, TypeError):
            logger.warning(f"警告：第 {i+1} 条结果的 '{metric_column}' ('{stats[metric_column]}') 不是有效数字，已跳过。")
            skipped_count += 1
            continue
        except Exception as e:
             logger.error(f"处理第 {i+1} 条结果时发生错误: {e}。已跳过。", exc_info=True)
             skipped_count += 1
             continue

    logger.info(f"筛选完成。找到 {len(filtered_results_data)} 条满足条件 ({metric_column} {comparison_op} {threshold}) 的结果。跳过了 {skipped_count} 条格式不完整或无效的结果。")

    # --- Save Output ---
    output_dir = os.path.dirname(output_file_path)
    if output_dir and not os.path.exists(output_dir):
        try:
            os.makedirs(output_dir)
            logger.info(f"创建输出目录: {output_dir}")
        except OSError as e:
            logger.error(f"创建目录 {output_dir} 时出错: {e}")
            return False

    if filtered_results_data:
        if output_format.lower() == 'json':
            # --- Save as JSON ---
            try:
                output_data = {"results": filtered_results_data}
                if time_stats: # Include time_stats if they existed in the input
                    output_data["time_stats"] = time_stats

                with open(output_file_path, 'w', encoding='utf-8') as f:
                    json.dump(output_data, f, indent=4, default=str)
                logger.info(f"筛选结果已保存为 JSON 到: {output_file_path}")
                print(f"\n找到 {len(filtered_results_data)} 条满足条件的结果，已保存为 JSON 到 {output_file_path}")
                return True
            except Exception as e:
                logger.error(f"错误：保存结果到 JSON 文件 '{output_file_path}' 时出错: {e}")
                return False
        elif output_format.lower() == 'csv':
            # --- Save as CSV ---
            try:
                # Need to re-flatten for CSV
                filtered_results_flat = []
                for result in filtered_results_data:
                    params = result.get('params', {})
                    stats = result.get('statistics', {})
                    flattened = {**params, **stats}
                    flattened['param_index'] = result.get('param_index')
                    flattened['result_dir'] = result.get('result_dir')
                    flattened['execution_time'] = result.get('execution_time')
                    filtered_results_flat.append(flattened)

                df_filtered = pd.DataFrame(filtered_results_flat)

                # Define preferred column order using keys from the last valid entry
                param_cols = list(last_valid_params.keys())
                stats_cols = list(last_valid_stats.keys())
                other_cols = ['param_index', 'result_dir', 'execution_time']

                # Combine and ensure order, placing the filtered metric column prominently
                preferred_order = ['param_index'] + param_cols + [metric_column] + \
                                [col for col in stats_cols if col != metric_column] + \
                                [col for col in other_cols if col != 'param_index']

                # Ensure all columns exist and add remaining ones sorted
                final_columns = [col for col in preferred_order if col in df_filtered.columns]
                final_columns += sorted([col for col in df_filtered.columns if col not in final_columns])

                df_filtered = df_filtered[final_columns]

                df_filtered.to_csv(output_file_path, index=False, float_format='%.6f', encoding='utf-8-sig')
                logger.info(f"筛选结果已保存为 CSV 到: {output_file_path}")
                print(f"\n找到 {len(filtered_results_data)} 条满足条件的结果，已保存为 CSV 到 {output_file_path}")
                return True
            except Exception as e:
                logger.error(f"错误：保存结果到 CSV 文件 '{output_file_path}' 时出错: {e}")
                return False
        else:
            logger.error(f"错误：无效的输出格式 '{output_format}'。请使用 'csv' 或 'json'。")
            return False
    else:
        print(f"\n未找到满足条件 ({metric_column} {comparison_op} {threshold}) 的结果。")
        logger.info(f"未找到满足条件的结果，未创建文件 {output_file_path}")
        # Consider creating an empty file based on format if needed
        if output_format.lower() == 'json':
             try:
                 with open(output_file_path, 'w', encoding='utf-8') as f:
                     json.dump({"results": []}, f, indent=4) # Save empty results list
                 logger.info(f"已创建空的 JSON 输出文件: {output_file_path}")
                 return True
             except Exception as e:
                 logger.error(f"尝试创建空的 JSON 输出文件时出错: {e}")
                 return False
        # No need to explicitly create empty CSV
        return True # Process completed successfully even with no matches

# --- Example Usage (if run directly) ---
if __name__ == "__main__":
    # Example: Filter by win_rate > 0.50
    input_f = 'backtest_result/ed_param_search_20250411_023816/param_search_results.json'
    output_f = 'high_win_rate_50pct_results.json'
    metric = 'win_rate'
    thresh = 0.50
    op = 'gt'

    print(f"Running filter: {metric} {op} {thresh}")
    success = filter_and_save_by_metric(input_f, output_f, metric, thresh, op)

    if success:
        print(f"\nFilter script execution successful.")
    else:
        print(f"\nFilter script execution failed.") 