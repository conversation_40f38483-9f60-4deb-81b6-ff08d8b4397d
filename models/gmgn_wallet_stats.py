"""
GMGN钱包统计数据模型

此模型专门用于存储从GMGN API获取的钱包统计数据，与kol_wallet表分离设计，
通过wallet_address字段关联，支持独立的数据管理和聚合查询。
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from beanie import Document, Indexed
from pydantic import BaseModel, Field


class GmgnRiskMetrics(BaseModel):
    """GMGN风险指标子模型"""
    
    # 代币活跃度指标 - 根据实际API数据，这些是整数类型
    token_active: Optional[int] = Field(default=0, description="活跃代币数")
    token_honeypot: Optional[int] = Field(default=0, description="蜜罐代币数")
    token_honeypot_ratio: Optional[float] = Field(default=0.0, description="蜜罐代币比率")
    
    # 持仓行为风险指标
    no_buy_hold: Optional[int] = Field(default=0, description="无买入持有数")
    no_buy_hold_ratio: Optional[float] = Field(default=0.0, description="无买入持有比率")
    sell_pass_buy: Optional[int] = Field(default=0, description="卖出超过买入数")
    sell_pass_buy_ratio: Optional[float] = Field(default=0.0, description="卖出超过买入比率")
    
    # 交易速度风险指标
    fast_tx: Optional[int] = Field(default=0, description="快速交易数")
    fast_tx_ratio: Optional[float] = Field(default=0.0, description="快速交易比率")


class GmgnWalletStats(Document):
    """GMGN钱包统计数据模型"""
    
    # === 基本标识字段 ===
    wallet_address: Indexed(str) = Field(..., description="钱包地址")
    chain: str = Field(default="sol", description="链名称")
    period: str = Field(default="all", description="数据时间窗口(all/7d/1d等)")
    
    # === 交易统计字段 ===
    buy: Optional[int] = Field(None, description="总买入次数")
    sell: Optional[int] = Field(None, description="总卖出次数")
    buy_1d: Optional[int] = Field(None, description="1天内买入次数")
    sell_1d: Optional[int] = Field(None, description="1天内卖出次数")
    buy_7d: Optional[int] = Field(None, description="7天内买入次数")
    sell_7d: Optional[int] = Field(None, description="7天内卖出次数")
    buy_30d: Optional[int] = Field(None, description="30天内买入次数")
    sell_30d: Optional[int] = Field(None, description="30天内卖出次数")
    
    # === 收益指标字段 ===
    pnl: Optional[float] = Field(None, description="总收益率")
    pnl_1d: Optional[float] = Field(None, description="1天收益率")
    pnl_7d: Optional[float] = Field(None, description="7天收益率")
    pnl_30d: Optional[float] = Field(None, description="30天收益率")
    all_pnl: Optional[float] = Field(None, description="总收益率（与pnl相同）")
    
    # 已实现收益
    realized_profit: Optional[float] = Field(None, description="总已实现收益")
    realized_profit_1d: Optional[float] = Field(None, description="1天已实现收益")
    realized_profit_7d: Optional[float] = Field(None, description="7天已实现收益")
    realized_profit_30d: Optional[float] = Field(None, description="30天已实现收益")
    
    # 未实现收益  
    unrealized_profit: Optional[float] = Field(None, description="未实现收益")
    unrealized_pnl: Optional[float] = Field(None, description="未实现收益率")
    
    # 总利润指标
    total_profit: Optional[float] = Field(None, description="总利润")
    total_profit_pnl: Optional[float] = Field(None, description="总利润收益率")
    
    # === 余额信息字段 ===
    balance: Optional[str] = Field(None, description="总余额")  # API返回字符串格式
    eth_balance: Optional[str] = Field(None, description="ETH余额")
    sol_balance: Optional[str] = Field(None, description="SOL余额")
    trx_balance: Optional[str] = Field(None, description="TRX余额")
    bnb_balance: Optional[str] = Field(None, description="BNB余额")
    total_value: Optional[float] = Field(None, description="总价值(USD)")
    
    # === 交易绩效字段 ===
    winrate: Optional[float] = Field(None, description="总胜率")
    avg_cost: Optional[float] = Field(None, description="平均买入成本")
    avg_sold: Optional[float] = Field(None, description="平均卖出价格")
    token_sold_avg_profit: Optional[float] = Field(None, description="代币平均卖出收益")
    history_bought_cost: Optional[float] = Field(None, description="历史买入成本")
    token_avg_cost: Optional[float] = Field(None, description="代币平均成本")
    token_num: Optional[int] = Field(None, description="交易代币总数")
    profit_num: Optional[int] = Field(None, description="盈利代币数量")
    avg_holding_peroid: Optional[float] = Field(None, description="平均持仓时间")
    gas_cost: Optional[float] = Field(None, description="手续费成本")
    
    # === 收益分布统计 ===
    pnl_lt_minus_dot5_num: Optional[int] = Field(None, description="收益率<-0.5的次数")
    pnl_minus_dot5_0x_num: Optional[int] = Field(None, description="-0.5<=收益率<0的次数")
    pnl_lt_2x_num: Optional[int] = Field(None, description="0<=收益率<2的次数")
    pnl_2x_5x_num: Optional[int] = Field(None, description="2<=收益率<5的次数")
    pnl_gt_5x_num: Optional[int] = Field(None, description="收益率>=5的次数")
    
    # === 用户和社交信息字段 ===
    bind: Optional[bool] = Field(None, description="是否绑定")
    avatar: Optional[str] = Field(None, description="头像URL")
    name: Optional[str] = Field(None, description="用户名")
    ens: Optional[str] = Field(None, description="ENS域名")
    tags: Optional[List[str]] = Field(default_factory=list, description="标签列表")
    tag_rank: Optional[Dict[str, Any]] = Field(None, description="标签排名")
    
    # Twitter相关信息
    twitter_name: Optional[str] = Field(None, description="Twitter名称")
    twitter_username: Optional[str] = Field(None, description="Twitter用户名")
    twitter_bind: Optional[bool] = Field(None, description="Twitter绑定状态")
    twitter_fans_num: Optional[int] = Field(None, description="Twitter粉丝数")
    followers_count: Optional[int] = Field(None, description="关注者数量")
    
    # 其他信息
    is_contract: Optional[bool] = Field(None, description="是否为合约地址")
    
    # === 风险评估字段 ===
    risk: Optional[GmgnRiskMetrics] = Field(None, description="风险指标")
    
    # === 时间戳字段 ===
    last_active_timestamp: Optional[int] = Field(None, description="最后活跃时间戳")
    updated_at_api: Optional[int] = Field(None, description="API数据更新时间戳")
    refresh_requested_at: Optional[int] = Field(None, description="刷新请求时间戳")
    
    # === 系统字段 ===
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    
    # === 数据源信息 ===
    source_api: str = Field(default="gmgn_wallet_stat", description="数据源API")
    raw_data: Optional[Dict[str, Any]] = Field(None, description="原始API响应数据")
    
    class Settings:
        name = "gmgn_wallet_stats"
        indexes = [
            "wallet_address",
            [("wallet_address", 1), ("chain", 1)],  # 复合索引
            [("wallet_address", 1), ("period", 1)],  # 钱包地址+时间窗口索引
            [("wallet_address", 1), ("chain", 1), ("period", 1)],  # 完整复合索引
            [("updated_at", -1)],  # 更新时间倒序
            [("pnl_7d", -1)],  # 7天收益倒序
            [("pnl_30d", -1)],  # 30天收益倒序
            [("total_value", -1)],  # 总价值倒序
            [("winrate", -1)],  # 胜率倒序
            [("token_num", -1)],  # 代币数量倒序
        ]
    
    def __str__(self):
        return f"GmgnWalletStats({self.wallet_address[:8]}...)"
    
    def __repr__(self):
        return f"<GmgnWalletStats wallet_address={self.wallet_address} chain={self.chain}>"
    
    @property
    def wallet_address_short(self) -> str:
        """获取钱包地址简写"""
        if self.wallet_address and len(self.wallet_address) > 8:
            return f"{self.wallet_address[:4]}...{self.wallet_address[-4:]}"
        return self.wallet_address
    
    @property
    def is_profitable_7d(self) -> bool:
        """判断7天是否盈利"""
        return self.pnl_7d is not None and self.pnl_7d > 0
    
    @property
    def is_profitable_30d(self) -> bool:
        """判断30天是否盈利"""
        return self.pnl_30d is not None and self.pnl_30d > 0
    
    @property
    def is_profitable_total(self) -> bool:
        """判断总体是否盈利"""
        return self.pnl is not None and self.pnl > 0
    
    @property
    def trade_frequency_7d(self) -> int:
        """获取7天交易频率"""
        buy_count = self.buy_7d or 0
        sell_count = self.sell_7d or 0
        return buy_count + sell_count
    
    @property
    def trade_frequency_30d(self) -> int:
        """获取30天交易频率"""
        buy_count = self.buy_30d or 0
        sell_count = self.sell_30d or 0
        return buy_count + sell_count
    
    @property
    def trade_frequency_total(self) -> int:
        """获取总交易频率"""
        buy_count = self.buy or 0
        sell_count = self.sell or 0
        return buy_count + sell_count
    
    @property
    def balance_float(self) -> Optional[float]:
        """获取余额的浮点数值"""
        if self.balance:
            try:
                return float(self.balance)
            except (ValueError, TypeError):
                return None
        return None
    
    @property
    def sol_balance_float(self) -> Optional[float]:
        """获取SOL余额的浮点数值"""
        if self.sol_balance:
            try:
                return float(self.sol_balance)
            except (ValueError, TypeError):
                return None
        return None
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取绩效摘要"""
        return {
            "wallet_address": self.wallet_address_short,
            "total_value": self.total_value,
            "pnl": self.pnl,
            "pnl_7d": self.pnl_7d,
            "pnl_30d": self.pnl_30d,
            "winrate": self.winrate,
            "token_num": self.token_num,
            "profit_num": self.profit_num,
            "trade_frequency_total": self.trade_frequency_total,
            "trade_frequency_7d": self.trade_frequency_7d,
            "trade_frequency_30d": self.trade_frequency_30d,
            "is_profitable_total": self.is_profitable_total,
            "is_profitable_7d": self.is_profitable_7d,
            "is_profitable_30d": self.is_profitable_30d,
            "updated_at": self.updated_at,
        }
    
    def update_timestamps(self):
        """更新时间戳"""
        self.updated_at = datetime.now()
    
    @classmethod
    def create_from_api_data(cls, wallet_address: str, api_data: Dict[str, Any], period: str = "all") -> "GmgnWalletStats":
        """从API数据创建实例"""
        if not wallet_address:
            raise ValueError("wallet_address is required")
        if not api_data or not isinstance(api_data, dict):
            # 如果api_data为空或不是字典，则创建一个最小化的实例或抛出错误
            # 这里选择创建一个带有基本信息的实例，多数统计数据为None
            # 或者，可以: raise ValueError("api_data is required and must be a dictionary")
            return cls(
                wallet_address=wallet_address,
                period=period,
                chain="sol", # 默认值或从其他地方获取
                raw_data=api_data # 存储原始（可能是无效的）数据
            )

        # 安全类型转换辅助函数
        def safe_int(value: Any) -> Optional[int]:
            if value is None: return None
            try: return int(value)
            except (ValueError, TypeError): return None

        def safe_float(value: Any) -> Optional[float]:
            if value is None: return None
            try: return float(value)
            except (ValueError, TypeError): return None

        def safe_str(value: Any) -> Optional[str]:
            if value is None: return None
            return str(value)
            
        def safe_bool(value: Any) -> Optional[bool]:
            if value is None: return None
            if isinstance(value, bool): return value
            if isinstance(value, str):
                if value.lower() == 'true': return True
                if value.lower() == 'false': return False
            # 可以选择根据其他类型的输入返回值，或者如果不是明确的true/false则返回None
            return None

        risk_data = api_data.get("risk", {})
        risk_metrics = None
        if isinstance(risk_data, dict) and risk_data:  # 确保risk_data不为空字典
            risk_metrics = GmgnRiskMetrics(
                token_active=safe_int(risk_data.get("token_active")),
                token_honeypot=safe_int(risk_data.get("token_honeypot")),
                token_honeypot_ratio=safe_float(risk_data.get("token_honeypot_ratio")),
                no_buy_hold=safe_int(risk_data.get("no_buy_hold")),
                no_buy_hold_ratio=safe_float(risk_data.get("no_buy_hold_ratio")),
                sell_pass_buy=safe_int(risk_data.get("sell_pass_buy")),
                sell_pass_buy_ratio=safe_float(risk_data.get("sell_pass_buy_ratio")),
                fast_tx=safe_int(risk_data.get("fast_tx")),
                fast_tx_ratio=safe_float(risk_data.get("fast_tx_ratio"))
            )
        
        stats = cls(
            wallet_address=wallet_address,
            chain=safe_str(api_data.get("chain", "sol")), # API响应中通常不含chain，默认为sol
            period=period,

            # 交易统计字段 - 直接从api_data获取
            buy=safe_int(api_data.get("buy")),
            sell=safe_int(api_data.get("sell")),
            buy_1d=safe_int(api_data.get("buy_1d")),
            sell_1d=safe_int(api_data.get("sell_1d")),
            buy_7d=safe_int(api_data.get("buy_7d")),
            sell_7d=safe_int(api_data.get("sell_7d")),
            buy_30d=safe_int(api_data.get("buy_30d")),
            sell_30d=safe_int(api_data.get("sell_30d")),
            
            # 收益指标字段
            pnl=safe_float(api_data.get("pnl")),
            pnl_1d=safe_float(api_data.get("pnl_1d")),
            pnl_7d=safe_float(api_data.get("pnl_7d")),
            pnl_30d=safe_float(api_data.get("pnl_30d")),
            all_pnl=safe_float(api_data.get("all_pnl")),
            
            realized_profit=safe_float(api_data.get("realized_profit")),
            realized_profit_1d=safe_float(api_data.get("realized_profit_1d")),
            realized_profit_7d=safe_float(api_data.get("realized_profit_7d")),
            realized_profit_30d=safe_float(api_data.get("realized_profit_30d")),
            
            unrealized_profit=safe_float(api_data.get("unrealized_profit")),
            unrealized_pnl=safe_float(api_data.get("unrealized_pnl")),
            
            total_profit=safe_float(api_data.get("total_profit")),
            total_profit_pnl=safe_float(api_data.get("total_profit_pnl")),
            
            # 余额信息字段
            balance=safe_str(api_data.get("balance")), # 保持字符串，由属性转换
            eth_balance=safe_str(api_data.get("eth_balance")),
            sol_balance=safe_str(api_data.get("sol_balance")),
            trx_balance=safe_str(api_data.get("trx_balance")),
            bnb_balance=safe_str(api_data.get("bnb_balance")),
            total_value=safe_float(api_data.get("total_value")),
            
            # 交易绩效字段
            winrate=safe_float(api_data.get("winrate")), 
            avg_cost=safe_float(api_data.get("avg_cost", api_data.get("token_avg_cost"))), # 尝试avg_cost，fallback到token_avg_cost
            avg_sold=safe_float(api_data.get("avg_sold", api_data.get("token_avg_sold"))), # 尝试avg_sold，fallback到token_avg_sold
            token_sold_avg_profit=safe_float(api_data.get("token_sold_avg_profit")),
            history_bought_cost=safe_float(api_data.get("history_bought_cost")),
            token_avg_cost=safe_float(api_data.get("token_avg_cost")), # 重复avg_cost，但保留以防万一
            token_num=safe_int(api_data.get("token_num")),
            profit_num=safe_int(api_data.get("profit_num")),
            avg_holding_peroid=safe_float(api_data.get("avg_holding_peroid")), # 注意API拼写 peroid
            gas_cost=safe_float(api_data.get("gas_cost")),
            
            # 收益分布统计
            pnl_lt_minus_dot5_num=safe_int(api_data.get("pnl_lt_minus_dot5_num")),
            pnl_minus_dot5_0x_num=safe_int(api_data.get("pnl_minus_dot5_0x_num")),
            pnl_lt_2x_num=safe_int(api_data.get("pnl_lt_2x_num")),
            pnl_2x_5x_num=safe_int(api_data.get("pnl_2x_5x_num")),
            pnl_gt_5x_num=safe_int(api_data.get("pnl_gt_5x_num")),
            
            # 用户和社交信息字段
            bind=safe_bool(api_data.get("bind")),
            avatar=safe_str(api_data.get("avatar")),
            name=safe_str(api_data.get("name")),
            ens=safe_str(api_data.get("ens")),
            tags=api_data.get("tags", []) if isinstance(api_data.get("tags"), list) else [],
            tag_rank=api_data.get("tag_rank") if isinstance(api_data.get("tag_rank"), dict) else None,
            
            twitter_name=safe_str(api_data.get("twitter_name")),
            twitter_username=safe_str(api_data.get("twitter_username")),
            twitter_bind=safe_bool(api_data.get("twitter_bind")),
            twitter_fans_num=safe_int(api_data.get("twitter_fans_num")),
            followers_count=safe_int(api_data.get("followers_count")),
            
            is_contract=safe_bool(api_data.get("is_contract")),
            
            # 风险评估字段
            risk=risk_metrics,
            
            # 时间戳字段
            last_active_timestamp=safe_int(api_data.get("last_active_timestamp")),
            updated_at_api=safe_int(api_data.get("updated_at")), # API的 'updated_at' 是数据时间戳
            refresh_requested_at=safe_int(api_data.get("refresh_requested_at")),
            
            # raw_data
            raw_data=api_data,
            
            # created_at 和 updated_at (系统时间戳) 会通过 default_factory 自动设置
        )
        return stats


# FIELD_MAPPING 应该被弃用，因为字段现在是直接从api_data中获取的
# FIELD_MAPPING = {
#     'buy': 'buy',
#     'sell': 'sell',
# ... (其他映射)
# }

# 辅助函数，用于安全地从字典中提取和转换数据
# 这些可以移到模型外部的utils模块，或者保留在模型内部作为私有方法
# 为了简洁，此处直接在 create_from_api_data 中定义了局部辅助函数

# 注意：模型中的余额字段（balance, sol_balance等）定义为Optional[str]，
# 这是因为API返回的是字符串。模型通过@property提供了_float版本用于计算。
# 如果希望在数据库中存储为float，需要在create_from_api_data中用safe_float转换，并修改模型字段类型。
# 当前保持与原模型一致，存储字符串，通过属性访问浮点数。

# 对于 period 特定字段的映射:
# 当前的实现是，如果API响应中直接包含如 pnl_7d, buy_1d 等字段，它们会被直接填充。
# 如果请求的 period 是 'all'，模型会尝试填充所有可用的字段。
# 如果请求的 period 是 '7d'，那么api_data本身就应该是7天的数据，模型也会据此填充。
# 这种方式依赖于API在不同period参数下返回数据的结构。
# 如果需要更复杂的逻辑（例如，当period='7d'时，将api_data的'pnl'字段映射到模型的'pnl_7d'字段），
# 则需要在调用create_from_api_data之前，由调用方（如爬虫的_parse_wallet_data）准备好符合模型期望的api_data结构，
# 或者在此方法中增加更复杂的条件映射逻辑。
# 目前，我们假设api_data已经包含了与模型字段名（或其直接映射）相对应的键。 