import { Wallet } from '@project-serum/anchor';
import { Connection, Keypair, VersionedTransaction, LAMPORTS_PER_SOL, PublicKey } from '@solana/web3.js';
import bs58 from 'bs58';
import fetch from 'node-fetch';
import dotenv from 'dotenv';
import minimist from 'minimist';

// --- Helper function ---
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Function to output JSON result to stdout
function outputResult(result) {
  process.stdout.write(JSON.stringify(result));
}

// Function to log to stderr for debugging
function logError(...args) {
  console.error(...args);
}

async function getOnchainTransactionDetails(txHash, rpcUrl, walletAddressStr, inputTokenMintStr, outputTokenMintStr) {
  logError(`\nFetching onchain details for tx: ${txHash} using RPC: ${rpcUrl}`);
  const connection = new Connection(rpcUrl, 'confirmed');
  let onchainDetails = {
    onchain_data_fetch_status: "not_attempted",
    onchain_actual_amount_in_lamports: null,
    onchain_input_decimals: null,
    onchain_actual_amount_out_lamports: null,
    onchain_output_decimals: null,
    onchain_error_message: null,
    parsed_tx_response: null,
  };

  try {
    const parsedTx = await connection.getParsedTransaction(txHash, { maxSupportedTransactionVersion: 0 });
    onchainDetails.parsed_tx_response = parsedTx; // Store the raw response for debugging

    if (!parsedTx || !parsedTx.meta) {
      onchainDetails.onchain_data_fetch_status = "failed_to_fetch";
      onchainDetails.onchain_error_message = "Transaction not found or meta is null.";
      logError(`Error fetching parsed transaction ${txHash}: ${onchainDetails.onchain_error_message}`);
      return onchainDetails;
    }
    if (parsedTx.meta.err) {
      onchainDetails.onchain_data_fetch_status = "failed_onchain_tx";
      onchainDetails.onchain_error_message = `Transaction ${txHash} failed onchain: ${JSON.stringify(parsedTx.meta.err)}`;
      logError(onchainDetails.onchain_error_message);
      return onchainDetails;
    }

    const { preTokenBalances, postTokenBalances, preBalances, postBalances } = parsedTx.meta;
    const ownerPublicKey = new PublicKey(walletAddressStr);

    let actualAmountInLamports = BigInt(0);
    let actualAmountOutLamports = BigInt(0);
    let inputDecimals = null;
    let outputDecimals = null;

    // Determine input token (SOL or SPL)
    if (inputTokenMintStr.toLowerCase() === 'sol' || inputTokenMintStr === 'So11111111111111111111111111111111111111112') {
      inputDecimals = 9; // SOL decimals
      const walletAccountIndex = parsedTx.transaction.message.accountKeys.findIndex(acc => acc.pubkey.equals(ownerPublicKey));
      if (walletAccountIndex !== -1) {
        const preSolBalance = BigInt(preBalances[walletAccountIndex]);
        const postSolBalance = BigInt(postBalances[walletAccountIndex]);
        actualAmountInLamports = preSolBalance - postSolBalance; // This includes all SOL spent (amount + fees)
        // To get only the amount used for the swap (excluding priority/tx fees specifically for the main instruction),
        // one might need to look at inner instructions if the swap isn't the only thing happening.
        // For simplicity, this will be the total SOL change for the wallet.
        // If GMGN API reports priority fee, Python side might subtract it if desired.
      } else {
        logError(`Could not find SOL balance changes for wallet ${walletAddressStr}`);
      }
    } else { // SPL Token Input
      const inputTokenAccount = preTokenBalances.find(tb => tb.mint === inputTokenMintStr && tb.owner === walletAddressStr) ||
                                postTokenBalances.find(tb => tb.mint === inputTokenMintStr && tb.owner === walletAddressStr); // Check post if it was a new account
      if (inputTokenAccount) {
        inputDecimals = inputTokenAccount.uiTokenAmount.decimals;
        const preBalanceObj = preTokenBalances.find(tb => tb.mint === inputTokenMintStr && tb.owner === walletAddressStr && tb.accountIndex === inputTokenAccount.accountIndex);
        const postBalanceObj = postTokenBalances.find(tb => tb.mint === inputTokenMintStr && tb.owner === walletAddressStr && tb.accountIndex === inputTokenAccount.accountIndex);
        
        const preAmount = preBalanceObj ? BigInt(preBalanceObj.uiTokenAmount.amount) : BigInt(0);
        const postAmount = postBalanceObj ? BigInt(postBalanceObj.uiTokenAmount.amount) : BigInt(0);
        actualAmountInLamports = preAmount - postAmount;
      } else {
         logError(`Could not find input SPL token (${inputTokenMintStr}) balance changes for wallet ${walletAddressStr}`);
      }
    }

    // Determine output token (SOL or SPL)
    if (outputTokenMintStr.toLowerCase() === 'sol' || outputTokenMintStr === 'So11111111111111111111111111111111111111112') {
      outputDecimals = 9; // SOL decimals
      const walletAccountIndex = parsedTx.transaction.message.accountKeys.findIndex(acc => acc.pubkey.equals(ownerPublicKey));
       if (walletAccountIndex !== -1) {
        const preSolBalance = BigInt(preBalances[walletAccountIndex]);
        const postSolBalance = BigInt(postBalances[walletAccountIndex]);
        // If input was also SOL, this calculation is tricky as it reflects net SOL change.
        // This part assumes outputting SOL means the primary transfer was *to* the wallet.
        // If input was SPL and output is SOL (sell), then postSolBalance - preSolBalance is correct.
        if (inputTokenMintStr.toLowerCase() !== 'sol' && inputTokenMintStr !== 'So11111111111111111111111111111111111111112') {
             actualAmountOutLamports = postSolBalance - preSolBalance;
        } else {
            logError("Warning: Calculating SOL out when SOL was also input is complex. AmountOut for SOL might be inaccurate if not a direct SPL->SOL sell.");
            // A more robust way for SOL output when SOL was also input (e.g. routing through SOL)
            // would be to inspect inner instructions for transfers to the user's account that are not the primary fee payment.
            // For now, if input is SOL and output is SOL (e.g. wrapping/unwrapping or complex route), this value might not be the "trade output".
        }
      } else {
        logError(`Could not find SOL balance changes for wallet ${walletAddressStr} for output.`);
      }
    } else { // SPL Token Output
      const outputTokenAccount = postTokenBalances.find(tb => tb.mint === outputTokenMintStr && tb.owner === walletAddressStr) ||
                                 preTokenBalances.find(tb => tb.mint === outputTokenMintStr && tb.owner === walletAddressStr);
      if (outputTokenAccount) {
        outputDecimals = outputTokenAccount.uiTokenAmount.decimals;
        const preBalanceObj = preTokenBalances.find(tb => tb.mint === outputTokenMintStr && tb.owner === walletAddressStr && tb.accountIndex === outputTokenAccount.accountIndex);
        const postBalanceObj = postTokenBalances.find(tb => tb.mint === outputTokenMintStr && tb.owner === walletAddressStr && tb.accountIndex === outputTokenAccount.accountIndex);
        
        const preAmount = preBalanceObj ? BigInt(preBalanceObj.uiTokenAmount.amount) : BigInt(0);
        const postAmount = postBalanceObj ? BigInt(postBalanceObj.uiTokenAmount.amount) : BigInt(0);
        actualAmountOutLamports = postAmount - preAmount;
      } else {
        logError(`Could not find output SPL token (${outputTokenMintStr}) balance changes for wallet ${walletAddressStr}`);
      }
    }
    
    onchainDetails.onchain_actual_amount_in_lamports = actualAmountInLamports.toString();
    onchainDetails.onchain_input_decimals = inputDecimals;
    onchainDetails.onchain_actual_amount_out_lamports = actualAmountOutLamports.toString();
    onchainDetails.onchain_output_decimals = outputDecimals;
    onchainDetails.onchain_data_fetch_status = "success";
    logError("Successfully fetched and parsed onchain details:", JSON.stringify(onchainDetails, null, 2));

  } catch (e) {
    logError("Error during getOnchainTransactionDetails:", e);
    onchainDetails.onchain_data_fetch_status = "failed_to_fetch";
    onchainDetails.onchain_error_message = e.message || e.toString();
  }
  return onchainDetails;
}

async function main(rawArgs) {
  const cliArgs = minimist(rawArgs);

  // Standard positional arguments (must be adapted if --txhash is used)
  // args[0]: WALLET_PRIVATE_KEY_B58
  // args[1]: INPUT_TOKEN_ADDRESS
  // args[2]: OUTPUT_TOKEN_ADDRESS
  // args[3]: AMOUNT_LAMPORTS (string)
  // args[4]: SLIPPAGE_PERCENTAGE (float, e.g., 0.5)
  // args[5]: PRIORITY_FEE_SOL (float, e.g., 0.0001)
  // args[6]: GMGN_API_HOST (string, e.g., https://gmgn.ai)
  // args[7]: WALLET_ADDRESS (string, derived if not provided, but passed for consistency)
  
  // --- Test mode for fetching onchain data directly ---
  if (cliArgs.txhash) {
    logError("Running in --txhash mode for onchain data fetching.");
    const rpcUrl = cliArgs.rpcUrl || process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com';
    const walletAddr = cliArgs.walletAddress; // Required for --txhash mode
    const inputMint = cliArgs.inputToken;   // Required for --txhash mode
    const outputMint = cliArgs.outputToken; // Required for --txhash mode

    if (!walletAddr || !inputMint || !outputMint) {
      outputResult({
        status: "error",
        stage: "onchain_test_mode",
        message: "--txhash mode requires --walletAddress, --inputToken, and --outputToken arguments.",
      });
      return;
    }

    const onchainData = await getOnchainTransactionDetails(cliArgs.txhash, rpcUrl, walletAddr, inputMint, outputMint);
    outputResult({
      status: "onchain_test_data",
      txHash: cliArgs.txhash,
      inputParameters: { walletAddress: walletAddr, inputToken: inputMint, outputToken: outputMint, rpcUrl: rpcUrl },
      ...onchainData // Spread all fields from onchainData
    });
    return;
  }
  // --- End of test mode ---


  if (rawArgs.length < 7) {
    outputResult({
      status: "error",
      stage: "initialization",
      message: "Insufficient command line arguments. Expected: privateKey, inputToken, outputToken, amountLamports, slippage, priorityFee, apiHost, [walletAddress]",
    });
    return;
  }

  const [
    WALLET_PRIVATE_KEY_B58,
    INPUT_TOKEN_ADDRESS,
    OUTPUT_TOKEN_ADDRESS,
    AMOUNT_LAMPORTS_STR,
    SLIPPAGE_PERCENTAGE_STR,
    PRIORITY_FEE_SOL_STR,
    GMGN_API_HOST,
    // WALLET_ADDRESS can be derived or passed as 8th arg
  ] = rawArgs;
  
  let WALLET_ADDRESS = rawArgs[7]; // Positional wallet address

  // Validate and parse inputs
  const amountLamports = AMOUNT_LAMPORTS_STR;
  const slippage = parseFloat(SLIPPAGE_PERCENTAGE_STR);
  const priorityFee = parseFloat(PRIORITY_FEE_SOL_STR);
  const API_HOST = GMGN_API_HOST;
  const RPC_URL_FOR_ONCHAIN = cliArgs.rpcUrl || process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com';


  if (isNaN(slippage) || isNaN(priorityFee)) {
    outputResult({
      status: "error",
      stage: "initialization",
      message: "Invalid slippage or priority fee provided.",
    });
    return;
  }
  
  if (!WALLET_PRIVATE_KEY_B58) {
    outputResult({ status: "error", stage: "initialization", message: "WALLET_PRIVATE_KEY_B58 is required." });
    return;
  }

  let wallet;
  try {
    wallet = new Wallet(Keypair.fromSecretKey(bs58.decode(WALLET_PRIVATE_KEY_B58)));
  } catch (e) {
    outputResult({ status: "error", stage: "initialization", message: "Error initializing wallet: " + e.message, details: e.toString() });
    return;
  }
  
  // If WALLET_ADDRESS (positional arg 8) is not provided, derive it. Otherwise, use the provided one.
  const fromAddress = WALLET_ADDRESS || wallet.publicKey.toString();
  // Ensure WALLET_ADDRESS (the variable used for API calls and onchain fetch) is set to the correct one for consistency.
  if (!WALLET_ADDRESS) WALLET_ADDRESS = fromAddress;


  logError(`Executing trade with parameters:`);
  logError(`  Wallet Address: ${fromAddress}`);
  logError(`  Input Token: ${INPUT_TOKEN_ADDRESS}`);
  logError(`  Output Token: ${OUTPUT_TOKEN_ADDRESS}`);
  logError(`  Amount (Lamports): ${amountLamports}`);
  logError(`  Slippage: ${slippage}%`);
  logError(`  Priority Fee: ${priorityFee} SOL`);
  logError(`  API Host: ${API_HOST}`);
  logError(`  RPC for onchain fetch: ${RPC_URL_FOR_ONCHAIN}`);


  let routeResponseData, signedTxBase64, submitResponseData, finalStatusData;
  let txHash, lastValidBlockHeightFromRoute;
  let onchainTradeDetails = {}; // To store results from getOnchainTransactionDetails

  // Helper function to analyze and log detailed response information
  function logDetailedResponse(response, responseText, stage, url) {
    logError(`\n=== Detailed ${stage} Response Analysis ===`);
    logError(`URL: ${url}`);
    logError(`Status: ${response.status} ${response.statusText}`);
    logError(`Headers: ${JSON.stringify([...response.headers.entries()])}`);
    logError(`Content-Type: ${response.headers.get('content-type')}`);
    logError(`Content-Length: ${response.headers.get('content-length')}`);
    logError(`Response Text Length: ${responseText.length}`);
    
    // Check if response looks like HTML
    const isHTML = responseText.trim().toLowerCase().startsWith('<!doctype') || 
                   responseText.trim().toLowerCase().startsWith('<html');
    if (isHTML) {
      logError(`⚠️  Response appears to be HTML instead of JSON!`);
      logError(`First 500 characters of HTML response:`);
      logError(responseText.substring(0, 500));
      if (responseText.length > 500) {
        logError(`... (truncated, total length: ${responseText.length})`);
      }
    } else {
      logError(`Response appears to be JSON/text format`);
      logError(`First 200 characters: ${responseText.substring(0, 200)}`);
    }
    logError(`=== End ${stage} Response Analysis ===\n`);
  }

  try {
    // 1. Get quote and raw transaction
    logError("\nFetching swap route...");
    const quoteUrl = `${API_HOST}/defi/router/v1/sol/tx/get_swap_route?token_in_address=${INPUT_TOKEN_ADDRESS}&token_out_address=${OUTPUT_TOKEN_ADDRESS}&in_amount=${amountLamports}&from_address=${fromAddress}&slippage=${slippage}&fee=${priorityFee}`;
    logError("Route URL:", quoteUrl);

    const routeFetch = await fetch(quoteUrl);
    const routeResponseText = await routeFetch.text();
    
    // Log detailed information about the route response
    logDetailedResponse(routeFetch, routeResponseText, 'Route', quoteUrl);
    
    try {
      routeResponseData = JSON.parse(routeResponseText);
      logError("Route Response (parsed JSON):", JSON.stringify(routeResponseData, null, 2));
    } catch (parseError) {
      logError(`❌ Failed to parse route response as JSON: ${parseError.message}`);
      throw { stage: "quote", message: `Failed to parse route response as JSON: ${parseError.message}`, details: { parseError: parseError.toString(), responseText: routeResponseText, url: quoteUrl, status: routeFetch.status } };
    }

    if (routeResponseData.code !== 0 || !routeResponseData.data || !routeResponseData.data.raw_tx || !routeResponseData.data.raw_tx.swapTransaction) {
      throw { stage: "quote", message: `Error in fetching route or route data is invalid: ${routeResponseData.msg || "No message"}`, details: routeResponseData };
    }
    lastValidBlockHeightFromRoute = routeResponseData.data.raw_tx.lastValidBlockHeight;
    if (!lastValidBlockHeightFromRoute) {
        logError("Warning: lastValidBlockHeight is missing from route response. Status check might be affected.");
    }

    // 2. Sign transaction
    logError("\nSigning transaction...");
    const swapTransactionBase64 = routeResponseData.data.raw_tx.swapTransaction;
    const swapTransactionBuf = Buffer.from(swapTransactionBase64, 'base64');
    const versionedTransaction = VersionedTransaction.deserialize(swapTransactionBuf);
    versionedTransaction.sign([wallet.payer]);
    signedTxBase64 = Buffer.from(versionedTransaction.serialize()).toString('base64');
    logError("Signed Transaction (Base64):", signedTxBase64.substring(0, 100) + "...");

    // 3. Submit transaction
    logError("\nSubmitting signed transaction...");
    const submitUrl = `${API_HOST}/defi/router/v1/sol/tx/submit_signed_transaction`;
    const submitBody = { "signed_tx": signedTxBase64 };
     if (fromAddress && rawArgs.length > 7) { // Conditionally add from_address if it was explicitly passed by Python
        submitBody.from_address = fromAddress; 
     }

    logError("Submit URL:", submitUrl);
    logError("Submit Body:", JSON.stringify(submitBody, null, 2));

    const submitFetch = await fetch(submitUrl, {
      method: 'POST',
      headers: { 'content-type': 'application/json' },
      body: JSON.stringify(submitBody)
    });
    
    const submitResponseText = await submitFetch.text();
    
    // Log detailed information about the submit response  
    logDetailedResponse(submitFetch, submitResponseText, 'Submit', submitUrl);
    
    try {
      submitResponseData = JSON.parse(submitResponseText);
      logError("Submit Response (parsed JSON):", JSON.stringify(submitResponseData, null, 2));
    } catch (parseError) {
      logError(`❌ Failed to parse submit response as JSON: ${parseError.message}`);
      throw { stage: "submit", message: `Failed to parse submit response as JSON: ${parseError.message}`, details: { parseError: parseError.toString(), responseText: submitResponseText, url: submitUrl, status: submitFetch.status } };
    }

    if (submitResponseData.code !== 0 || !submitResponseData.data || !submitResponseData.data.hash) {
      throw { stage: "submit", message: `Error in submitting transaction or submit data is invalid: ${submitResponseData.msg || "No message"}`, details: submitResponseData };
    }
    txHash = submitResponseData.data.hash;
    logError(`Transaction submitted. Hash: ${txHash}`);

    // 4. Poll transaction status
    if (!lastValidBlockHeightFromRoute) {
      logError("\nSkipping transaction status polling because lastValidBlockHeight was not available from route response.");
      onchainTradeDetails = await getOnchainTransactionDetails(txHash, RPC_URL_FOR_ONCHAIN, WALLET_ADDRESS, INPUT_TOKEN_ADDRESS, OUTPUT_TOKEN_ADDRESS);
      outputResult({
        status: "submitted_no_poll", 
        txHash: txHash,
        message: "Transaction submitted, but polling skipped due to missing lastValidBlockHeight.",
        quoteResponse: routeResponseData,
        submitResponse: submitResponseData,
        ...onchainTradeDetails // Include onchain details
      });
      return;
    }

    logError(`\nPolling transaction status for hash: ${txHash} (using lastValidBlockHeight: ${lastValidBlockHeightFromRoute})...`);
    let attempts = 0;
    const maxAttempts = 60; 

    while (attempts < maxAttempts) {
      attempts++;
      const statusUrl = `${API_HOST}/defi/router/v1/sol/tx/get_transaction_status?hash=${txHash}&last_valid_height=${lastValidBlockHeightFromRoute}`;
      
      const statusFetch = await fetch(statusUrl);
      finalStatusData = await statusFetch.json();
      logError(`Poll Attempt ${attempts} - Status:`, JSON.stringify(finalStatusData.data, null, 2));

      if (finalStatusData.data && (finalStatusData.data.success === true || finalStatusData.data.expired === true || finalStatusData.data.failed === true)) {
        if (finalStatusData.data.success === true) {
          logError(`Transaction ${txHash} confirmed as success by GMGN API. Now fetching onchain details...`);
          onchainTradeDetails = await getOnchainTransactionDetails(txHash, RPC_URL_FOR_ONCHAIN, WALLET_ADDRESS, INPUT_TOKEN_ADDRESS, OUTPUT_TOKEN_ADDRESS);
          outputResult({
            status: "success",
            txHash: txHash,
            quoteResponse: routeResponseData,
            submitResponse: submitResponseData,
            statusResponse: finalStatusData,
            ...onchainTradeDetails // Include onchain details
          });
        } else if (finalStatusData.data.expired === true) {
          throw { stage: "poll", message: "Transaction expired.", txHash: txHash, details: finalStatusData };
        } else if (finalStatusData.data.failed === true) {
          throw { stage: "poll", message: "Transaction failed on-chain.", txHash: txHash, details: finalStatusData };
        }
        return; 
      }
      
      if (attempts >= maxAttempts) {
         // Still try to get onchain data even if polling times out, as it might have succeeded on chain but API polling failed
        logError(`Max polling attempts reached for ${txHash}. Attempting to fetch onchain details directly...`);
        onchainTradeDetails = await getOnchainTransactionDetails(txHash, RPC_URL_FOR_ONCHAIN, WALLET_ADDRESS, INPUT_TOKEN_ADDRESS, OUTPUT_TOKEN_ADDRESS);
        throw { 
            stage: "poll", 
            message: "Max polling attempts reached. Transaction status via GMGN API unknown or still pending.", 
            txHash: txHash, 
            details: finalStatusData, // Last known status from GMGN
            onchainDirectFetchResult: onchainTradeDetails // Include direct onchain fetch attempt
        };
      }
      await sleep(1000);
    }

  } catch (error) {
    logError("Error during trade execution:", error);
    // Ensure onchain details are included even in the catch block if txHash is available
    let finalOutputError = {
      status: "error",
      stage: error.stage || "unknown",
      message: error.message || "An unexpected error occurred.",
      txHash: error.txHash || txHash,
      details: error.details || error.toString(),
      quoteResponse: routeResponseData, 
      submitResponse: submitResponseData,
      statusResponse: finalStatusData
    };
    if (error.onchainDirectFetchResult) { // If error came from max polling attempts section
        finalOutputError = { ...finalOutputError, ...error.onchainDirectFetchResult };
    } else if (txHash && Object.keys(onchainTradeDetails).length === 0 && error.stage !== 'quote' && error.stage !== 'initialization') {
        // If txHash exists, not an early error, and onchain details haven't been fetched yet (e.g. error during submit/poll before success)
        logError("Attempting to fetch onchain details after encountering an error post-submission...");
        const fallbackOnchainDetails = await getOnchainTransactionDetails(txHash, RPC_URL_FOR_ONCHAIN, WALLET_ADDRESS, INPUT_TOKEN_ADDRESS, OUTPUT_TOKEN_ADDRESS);
        finalOutputError = { ...finalOutputError, ...fallbackOnchainDetails };
    }
    outputResult(finalOutputError);
  }
}

// Script execution starts here
// Use process.argv.slice(2) to get command-line arguments passed to the script
const R_ARGS = process.argv.slice(2); 

if (R_ARGS.length < 7 && process.env.NODE_ENV !== 'test_called_by_python' && !minimist(R_ARGS).txhash) {
    dotenv.config(); 
    const argsForLocalTest = [
        process.env.WALLET_PRIVATE_KEY_B58,
        process.env.INPUT_TOKEN_ADDRESS_TEST || 'So11111111111111111111111111111111111111112',
        process.env.OUTPUT_TOKEN_ADDRESS_TEST || '5fG1KadrP3yD66gx4GgkFpvLE2UvzxwGKJLWsd1cyoNv', 
        process.env.AMOUNT_LAMPORTS_TEST || '1000000',
        process.env.SLIPPAGE_PERCENTAGE_TEST || '0.5',
        process.env.PRIORITY_FEE_SOL_TEST || '0.0001',
        process.env.GMGN_API_HOST || 'https://gmgn.ai',
        process.env.WALLET_ADDRESS 
    ];
    const cliArgsForLocal = minimist(R_ARGS); // Check if any CLI args provided for local test
    
    if(argsForLocalTest.slice(0,7).some(arg => arg === undefined) && R_ARGS.length < 7 && !cliArgsForLocal.txhash) {
        logError("Missing environment variables for local test run and not enough CLI args. Please set them in .env or pass all required arguments via CLI.");
        outputResult({status: "error", stage: "initialization", message: "Missing env vars for local test or not enough CLI args."});
    } else if (R_ARGS.length === 0 && !cliArgsForLocal.txhash) { 
        logError("Running with .env variables for local testing as no CLI arguments were provided.");
        main(argsForLocalTest).catch(e => {
            logError("Unhandled error in local test main:", e);
            outputResult({ status: "error", stage: "fatal", message: e.message, details: e.toString() });
        });
    } else if (R_ARGS.length > 0 && R_ARGS.length < 7 && !cliArgsForLocal.txhash) {
        logError("Insufficient CLI arguments. Expecting 7 or 8 for full trade, or --txhash mode.");
         outputResult({ status: "error", stage: "initialization", message: "Insufficient CLI arguments for full trade." });
    } else { 
        main(R_ARGS).catch(e => { // Handles both full trade CLI args and --txhash mode
            logError("Unhandled error in main:", e);
            outputResult({ status: "error", stage: "fatal", message: e.message, details: e.toString() });
        });
    }
} else { 
    main(R_ARGS).catch(e => {
        logError("Unhandled error in main (called by Python scenario or with sufficient CLI args):", e);
        outputResult({ status: "error", stage: "fatal", message: e.message, details: e.toString() });
    });
} 