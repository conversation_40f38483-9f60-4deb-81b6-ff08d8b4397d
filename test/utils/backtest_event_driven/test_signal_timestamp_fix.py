"""
测试事件驱动回测信号时间戳修复
验证买入/卖出信号使用正确的时间戳
"""
import unittest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
from typing import Dict, Any
import pytest
import asyncio

from utils.backtest_event_driven.strategy_adapter import BuyStrategyAdapter, SellStrategyAdapter
from utils.backtest_event_driven.event_queue import EventQueue
from utils.backtest_event_driven.events import SignalType, SignalEvent
from utils.strategies.kol_sell_strategy import KOLSellStrategy
from utils.strategies.kol_buy_strategy import KOLBuyStrategy


class TestSignalTimestampFix(unittest.IsolatedAsyncioTestCase):
    """测试信号时间戳修复"""

    def setUp(self):
        """设置测试环境"""
        self.config = {
            'transaction_lookback_hours': 24,
            'transaction_min_amount': 1000,
            'kol_account_min_count': 3,
            'sell_strategy_hours': 24,
            'sell_kol_ratio': 0.5,
            'processing_interval': 60
        }
        self.event_queue = EventQueue()
        self.sample_token_address = "EfgEGG9PxLhyk1wqtqgGnwgfVC7JYic3vC9BCWLvpump"
        self.current_time = **********  # 2025-01-02 21:24:01
        self.kol_buy_time = **********  # 2025-01-02 21:14:01 (10分钟前)
        self.kol_sell_time = **********  # 2025-01-02 21:19:01 (5分钟前)

    async def test_buy_signal_uses_threshold_timestamp(self):
        """测试买入信号使用threshold_timestamp而非current_time"""
        # 创建模拟的买入策略适配器
        buy_strategy = KOLBuyStrategy(config={})
        mock_event_queue = MagicMock()
        adapter = BuyStrategyAdapter({}, mock_event_queue)
        adapter.strategy = buy_strategy
        
        # 模拟策略返回的信号数据，包含threshold_timestamp
        mock_signal_data = {
            self.sample_token_address: {
                'kol_wallets': ['wallet1', 'wallet2'],
                'token_info': {'name': 'Test Token', 'symbol': 'TEST'},
                'threshold_timestamp': self.kol_buy_time  # 实际KOL买入时间
            }
        }
        
        # 模拟buy_strategy.generate_signals方法
        buy_strategy.generate_signals = AsyncMock(return_value=mock_signal_data)
        
        # 设置其他必要的适配器属性
        adapter.recent_activities = [{'timestamp': self.current_time - 300, 'data': 'test'}]
        adapter.wallets = [{'wallet_address': 'wallet1'}, {'wallet_address': 'wallet2'}]
        
        # 执行买入信号检查
        await adapter._check_buy_signals(
            window_start=self.current_time - 3600,  # 1小时前开始的时间窗口
            current_time=self.current_time  # 当前模拟时间
        )
        
        # 验证事件队列是否被调用，以及信号时间戳是否正确
        mock_event_queue.push.assert_called_once()
        signal_event = mock_event_queue.push.call_args[0][0]
        
        assert isinstance(signal_event, SignalEvent)
        assert signal_event.signal_type == SignalType.BUY
        assert signal_event.timestamp == self.current_time  # 应该使用当前检测时间，而不是历史阈值时间
        assert signal_event.token_address == self.sample_token_address
        
        # 验证历史阈值时间戳被正确保存在token_info中作为参考
        assert signal_event.token_info.get('threshold_timestamp') == self.kol_buy_time
        assert signal_event.token_info.get('original_signal_timestamp') == self.kol_buy_time

    async def test_buy_signal_fallback_to_current_time(self):
        """测试当没有threshold_timestamp时，买入信号回退到current_time"""
        buy_strategy = KOLBuyStrategy(config={})
        mock_event_queue = MagicMock()
        adapter = BuyStrategyAdapter({}, mock_event_queue)
        adapter.strategy = buy_strategy
        
        # 模拟策略返回的信号数据，不包含threshold_timestamp
        mock_signal_data = {
            self.sample_token_address: {
                'kol_wallets': ['wallet1', 'wallet2'],
                'token_info': {'name': 'Test Token', 'symbol': 'TEST'}
                # 注意：没有threshold_timestamp字段
            }
        }
        
        buy_strategy.generate_signals = AsyncMock(return_value=mock_signal_data)
        
        # 设置其他必要的适配器属性
        adapter.recent_activities = [{'timestamp': self.current_time - 300, 'data': 'test'}]
        adapter.wallets = [{'wallet_address': 'wallet1'}, {'wallet_address': 'wallet2'}]
        
        await adapter._check_buy_signals(
            window_start=self.current_time - 3600,  # 1小时前开始的时间窗口
            current_time=self.current_time
        )
        
        mock_event_queue.push.assert_called_once()
        signal_event = mock_event_queue.push.call_args[0][0]
        
        assert signal_event.timestamp == self.current_time  # 应该回退到current_time

    async def test_sell_signal_uses_kol_sell_timestamp(self):
        """测试卖出信号使用KOL卖出时间戳而非current_time"""
        config = {
            'sell_kol_ratio': 0.5,
            'sell_strategy_hours': 24,
            'transaction_lookback_hours': 12
        }
        mock_event_queue = MagicMock()
        adapter = SellStrategyAdapter(config, mock_event_queue)
        
        # 模拟持仓信息
        holding_info = {
            'buy_signal_timestamp': self.kol_buy_time,
            'kol_wallets': ['wallet1', 'wallet2'],
            'token_info': {'name': 'Test Token', 'symbol': 'TEST'}
        }
        
        # 模拟should_sell方法返回KOL卖出时间
        adapter.strategy.should_sell = AsyncMock(return_value=(
            True, 
            "卖出KOL比例达到阈值", 
            self.kol_sell_time  # 实际KOL卖出时间
        ))
        
        await adapter._process_single_sell_check(
            token_address=self.sample_token_address,
            holding_info=holding_info,
            current_time=self.current_time
        )
        
        mock_event_queue.push.assert_called_once()
        signal_event = mock_event_queue.push.call_args[0][0]
        
        assert isinstance(signal_event, SignalEvent)
        assert signal_event.signal_type == SignalType.SELL
        assert signal_event.timestamp == self.kol_sell_time  # 应该使用KOL卖出时间
        assert signal_event.token_address == self.sample_token_address

    async def test_sell_signal_timeout_uses_current_time(self):
        """测试超时卖出信号使用current_time"""
        config = {
            'sell_kol_ratio': 0.5,
            'sell_strategy_hours': 24,
            'transaction_lookback_hours': 12
        }
        mock_event_queue = MagicMock()
        adapter = SellStrategyAdapter(config, mock_event_queue)
        
        holding_info = {
            'buy_signal_timestamp': self.kol_buy_time,
            'kol_wallets': ['wallet1', 'wallet2'],
            'token_info': {'name': 'Test Token', 'symbol': 'TEST'}
        }
        
        # 模拟should_sell方法返回超时卖出（使用current_time）
        adapter.strategy.should_sell = AsyncMock(return_value=(
            True, 
            "持有时间达到上限", 
            self.current_time  # 超时情况下使用current_time是合理的
        ))
        
        await adapter._process_single_sell_check(
            token_address=self.sample_token_address,
            holding_info=holding_info,
            current_time=self.current_time
        )
        
        mock_event_queue.push.assert_called_once()
        signal_event = mock_event_queue.push.call_args[0][0]
        
        assert signal_event.timestamp == self.current_time  # 超时情况下使用current_time

    def test_calculate_sell_threshold_timestamp(self):
        """测试卖出阈值时间戳计算方法"""
        sell_strategy = KOLSellStrategy()
        
        # 模拟卖出的KOL活动数据（按时间排序）
        selling_kols_activity = [
            {'first_sell_timestamp': 1735870200},  # KOL1卖出时间
            {'first_sell_timestamp': 1735870300},  # KOL2卖出时间 
            {'first_sell_timestamp': 1735870400},  # KOL3卖出时间
        ]
        
        total_kol_count = 4  # 总共4个KOL
        sell_ratio_threshold = 0.5  # 50%阈值
        
        # 50% * 4 = 2个KOL，所以应该返回第2个KOL的卖出时间
        result = sell_strategy._calculate_sell_threshold_timestamp(
            selling_kols_activity, total_kol_count, sell_ratio_threshold
        )
        
        assert result == 1735870300  # 第2个KOL的卖出时间

    async def test_buy_strategy_filters_immediate_sell_conflicts(self):
        """测试买入策略过滤即时卖出冲突的功能"""
        config = {
            'sell_kol_ratio': 0.5,
            'transaction_lookback_hours': 12
        }
        buy_strategy = KOLBuyStrategy(config=config)
        
        # 模拟KOL活动DAO
        mock_kol_activity_dao = AsyncMock()
        
        # 测试场景1：存在即时卖出冲突的代币（应该被过滤掉）
        conflict_token = "ConflictToken123"
        # 测试场景2：没有即时卖出冲突的代币（应该保留）
        safe_token = "SafeToken456"
        
        buy_signals = {
            conflict_token: {
                "kol_wallets": ["wallet1", "wallet2"],
                "token_info": {"name": "Conflict Token", "symbol": "CONF"},
                "threshold_timestamp": 1735870600  # 买入信号时间
            },
            safe_token: {
                "kol_wallets": ["wallet3", "wallet4"],
                "token_info": {"name": "Safe Token", "symbol": "SAFE"},
                "threshold_timestamp": 1735870600  # 买入信号时间
            }
        }
        
        # 模拟聚合查询结果
        async def mock_aggregate(pipeline):
            token_address = pipeline[0]["$match"]["token.address"]
            if token_address == conflict_token:
                # 冲突代币：2个KOL中有1个已卖出，达到50%阈值
                return [{"_id": "wallet1", "first_sell_timestamp": 1735870500}]
            elif token_address == safe_token:
                # 安全代币：没有KOL卖出
                return []
            return []
        
        mock_kol_activity_dao.aggregate = mock_aggregate
        
        # 执行即时卖出冲突检查
        filtered_signals = await buy_strategy._filter_immediate_sell_conflicts(
            buy_signals, mock_kol_activity_dao
        )
        
        # 验证结果：冲突代币应该被过滤掉，安全代币应该保留
        assert conflict_token not in filtered_signals
        assert safe_token in filtered_signals
        assert len(filtered_signals) == 1

    async def test_buy_strategy_no_immediate_sell_conflicts(self):
        """测试当没有即时卖出冲突时，所有买入信号都应该保留"""
        config = {
            'sell_kol_ratio': 0.5,
            'transaction_lookback_hours': 12
        }
        buy_strategy = KOLBuyStrategy(config=config)
        
        mock_kol_activity_dao = AsyncMock()
        
        buy_signals = {
            "Token1": {
                "kol_wallets": ["wallet1", "wallet2"],
                "token_info": {"name": "Token 1", "symbol": "TOK1"},
                "threshold_timestamp": 1735870600
            },
            "Token2": {
                "kol_wallets": ["wallet3", "wallet4"],
                "token_info": {"name": "Token 2", "symbol": "TOK2"},
                "threshold_timestamp": 1735870600
            }
        }
        
        # 模拟所有代币都没有KOL卖出活动
        mock_kol_activity_dao.aggregate = AsyncMock(return_value=[])
        
        filtered_signals = await buy_strategy._filter_immediate_sell_conflicts(
            buy_signals, mock_kol_activity_dao
        )
        
        # 验证所有信号都被保留
        assert len(filtered_signals) == 2
        assert "Token1" in filtered_signals
        assert "Token2" in filtered_signals


if __name__ == '__main__':
    unittest.main() 