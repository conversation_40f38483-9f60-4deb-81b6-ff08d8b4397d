import unittest
from unittest.mock import patch, AsyncMock, MagicMock
import asyncio
from datetime import datetime
from typing import List, Dict, Any
from beanie import PydanticObjectId

# 导入被测试的模块
from api.v1.signal_api import list_signals, SignalPeriod, SignalListApiResponse
from dao.signal_dao import SignalDAO


class TestSignalApiFilter(unittest.IsolatedAsyncioTestCase):
    """测试信号API过滤功能的单元测试类"""

    def setUp(self):
        """测试前的准备工作"""
        # 准备测试数据
        self.test_data = [
            {
                "_id": PydanticObjectId(),
                "token_address": "So1****************************************",
                "token_name": "Test Token 1",
                "token_symbol": "TT1",
                "signal_type": "kol_buy",
                "trigger_conditions": {
                    "strategy_name": "测试",
                    "other_param": "value1"
                },
                "trigger_timestamp": datetime(2025, 5, 27, 8, 0, 0),
                "created_at": datetime(2025, 5, 27, 8, 0, 0),
                "updated_at": None,
                "hit_kol_wallets": ["wallet1"]
            },
            {
                "_id": PydanticObjectId(), 
                "token_address": "TokenAddress2",
                "token_name": "Normal Token",
                "token_symbol": "NT",
                "signal_type": "kol_buy",
                "trigger_conditions": {
                    "strategy_name": "正常策略",
                    "other_param": "value2"
                },
                "trigger_timestamp": datetime(2025, 5, 27, 9, 0, 0),
                "created_at": datetime(2025, 5, 27, 9, 0, 0),
                "updated_at": None,
                "hit_kol_wallets": ["wallet2"]
            },
            {
                "_id": PydanticObjectId(),
                "token_address": "TokenAddress3", 
                "token_name": "Token Without Strategy",
                "token_symbol": "TWS",
                "signal_type": "kol_sell",
                "trigger_conditions": {
                    "other_param": "value3"
                },
                "trigger_timestamp": datetime(2025, 5, 27, 10, 0, 0),
                "created_at": datetime(2025, 5, 27, 10, 0, 0),
                "updated_at": None,
                "hit_kol_wallets": ["wallet3"]
            },
            {
                "_id": PydanticObjectId(),
                "token_address": "TokenAddress4",
                "token_name": "Token Without Trigger Conditions", 
                "token_symbol": "TWTC",
                "signal_type": "kol_buy",
                "trigger_timestamp": datetime(2025, 5, 27, 11, 0, 0),
                "created_at": datetime(2025, 5, 27, 11, 0, 0),
                "updated_at": None,
                "hit_kol_wallets": ["wallet4"]
            },
            {
                "_id": PydanticObjectId(),
                "token_address": "TokenAddress5",
                "token_name": "Test Strategy Variant",
                "token_symbol": "TSV", 
                "signal_type": "kol_buy",
                "trigger_conditions": {
                    "strategy_name": "测试策略",
                    "other_param": "value5"
                },
                "trigger_timestamp": datetime(2025, 5, 27, 12, 0, 0),
                "created_at": datetime(2025, 5, 27, 12, 0, 0),
                "updated_at": None,
                "hit_kol_wallets": ["wallet5"]
            }
        ]

    @patch('api.v1.signal_api.SignalDAO')
    async def test_filter_test_strategy_signals(self, mock_signal_dao_class):
        """测试过滤测试策略信号功能"""
        # 模拟DAO返回的数据（已过滤掉测试策略）
        filtered_data = [doc for doc in self.test_data 
                        if doc.get('trigger_conditions', {}).get('strategy_name') != '测试']
        
        # 设置mock
        mock_dao_instance = MagicMock()
        mock_signal_dao_class.return_value = mock_dao_instance
        
        # 模拟collection.find()链式调用
        mock_collection = MagicMock()
        mock_dao_instance.collection = mock_collection
        
        mock_cursor = MagicMock()
        mock_collection.find.return_value = mock_cursor
        mock_cursor.sort.return_value = mock_cursor
        mock_cursor.skip.return_value = mock_cursor
        mock_cursor.limit.return_value = mock_cursor
        mock_cursor.to_list = AsyncMock(return_value=filtered_data)

        # 调用API - 传递默认参数值
        result = await list_signals(
            skip=0, 
            limit=10, 
            signal_type=None, 
            period=None, 
            tz="Asia/Shanghai"
        )

        # 验证结果
        self.assertEqual(result.code, 0)
        self.assertEqual(result.msg, "success")
        self.assertIsNotNone(result.data)
        self.assertEqual(len(result.data), 4)  # 应该返回4条记录（排除了测试策略）
        
        # 验证没有包含测试策略的记录
        # 由于我们过滤掉了测试策略，应该没有包含第一条记录（测试策略）
        signal_ids = [str(signal.id) for signal in result.data]
        self.assertNotIn(str(self.test_data[0]["_id"]), signal_ids)
        
        # 验证查询条件包含过滤逻辑
        mock_collection.find.assert_called_once()
        call_args = mock_collection.find.call_args[0][0]
        self.assertIn('$and', call_args)
        self.assertEqual(len(call_args['$and']), 2)

    @patch('api.v1.signal_api.SignalDAO')
    async def test_keep_records_without_strategy_name(self, mock_signal_dao_class):
        """测试保留没有strategy_name字段的记录"""
        # 只返回没有strategy_name或trigger_conditions的记录
        records_without_strategy = [
            self.test_data[2],  # 没有strategy_name字段
            self.test_data[3]   # 没有trigger_conditions字段
        ]
        
        # 设置mock
        mock_dao_instance = MagicMock()
        mock_signal_dao_class.return_value = mock_dao_instance
        
        mock_collection = MagicMock()
        mock_dao_instance.collection = mock_collection
        
        mock_cursor = MagicMock()
        mock_collection.find.return_value = mock_cursor
        mock_cursor.sort.return_value = mock_cursor
        mock_cursor.skip.return_value = mock_cursor
        mock_cursor.limit.return_value = mock_cursor
        mock_cursor.to_list = AsyncMock(return_value=records_without_strategy)

        # 调用API
        result = await list_signals(
            skip=0, 
            limit=10, 
            signal_type=None, 
            period=None, 
            tz="Asia/Shanghai"
        )

        # 验证结果
        self.assertEqual(result.code, 0)
        self.assertEqual(len(result.data), 2)
        
        # 验证包含了没有strategy_name的记录
        signal_ids = [str(signal.id) for signal in result.data]
        self.assertIn(str(self.test_data[2]["_id"]), signal_ids)  # test_signal_3
        self.assertIn(str(self.test_data[3]["_id"]), signal_ids)  # test_signal_4

    @patch('api.v1.signal_api.SignalDAO')
    async def test_case_sensitive_filtering(self, mock_signal_dao_class):
        """测试大小写敏感的过滤"""
        # 添加大小写变体的测试数据
        case_test_data = [
            {
                "_id": PydanticObjectId(),
                "token_address": "TokenCase1",
                "signal_type": "kol_buy",
                "trigger_conditions": {"strategy_name": "Test"},
                "trigger_timestamp": datetime(2025, 5, 27, 15, 0, 0),
                "created_at": datetime(2025, 5, 27, 15, 0, 0),
                "updated_at": None,
                "hit_kol_wallets": ["wallet_case1"]
            },
            {
                "_id": PydanticObjectId(), 
                "token_address": "TokenCase2",
                "signal_type": "kol_buy",
                "trigger_conditions": {"strategy_name": "测试"},
                "trigger_timestamp": datetime(2025, 5, 27, 16, 0, 0),
                "created_at": datetime(2025, 5, 27, 16, 0, 0),
                "updated_at": None,
                "hit_kol_wallets": ["wallet_case2"]
            }
        ]
        
        # 只返回"Test"的记录，"测试"应该被过滤掉
        filtered_case_data = [case_test_data[0]]  # 只有"Test"
        
        # 设置mock
        mock_dao_instance = MagicMock()
        mock_signal_dao_class.return_value = mock_dao_instance
        
        mock_collection = MagicMock()
        mock_dao_instance.collection = mock_collection
        
        mock_cursor = MagicMock()
        mock_collection.find.return_value = mock_cursor
        mock_cursor.sort.return_value = mock_cursor
        mock_cursor.skip.return_value = mock_cursor
        mock_cursor.limit.return_value = mock_cursor
        mock_cursor.to_list = AsyncMock(return_value=filtered_case_data)

        # 调用API
        result = await list_signals(
            skip=0, 
            limit=10, 
            signal_type=None, 
            period=None, 
            tz="Asia/Shanghai"
        )

        # 验证结果
        self.assertEqual(result.code, 0)
        self.assertEqual(len(result.data), 1)
        self.assertEqual(str(result.data[0].id), str(case_test_data[0]["_id"]))

    @patch('api.v1.signal_api.SignalDAO')
    async def test_combination_with_signal_type_filter(self, mock_signal_dao_class):
        """测试与signal_type过滤的组合"""
        # 模拟只返回kol_buy类型且非测试策略的记录
        kol_buy_filtered = [doc for doc in self.test_data 
                           if doc['signal_type'] == 'kol_buy' 
                           and doc.get('trigger_conditions', {}).get('strategy_name') != '测试']
        
        # 设置mock
        mock_dao_instance = MagicMock()
        mock_signal_dao_class.return_value = mock_dao_instance
        
        mock_collection = MagicMock()
        mock_dao_instance.collection = mock_collection
        
        mock_cursor = MagicMock()
        mock_collection.find.return_value = mock_cursor
        mock_cursor.sort.return_value = mock_cursor
        mock_cursor.skip.return_value = mock_cursor
        mock_cursor.limit.return_value = mock_cursor
        mock_cursor.to_list = AsyncMock(return_value=kol_buy_filtered)

        # 调用API，指定signal_type
        result = await list_signals(
            skip=0, 
            limit=10, 
            signal_type="kol_buy", 
            period=None, 
            tz="Asia/Shanghai"
        )

        # 验证结果
        self.assertEqual(result.code, 0)
        self.assertGreater(len(result.data), 0)
        
        # 验证查询条件同时包含signal_type和过滤条件
        mock_collection.find.assert_called_once()
        call_args = mock_collection.find.call_args[0][0]
        self.assertIn('signal_type', call_args)
        self.assertIn('$and', call_args)
        self.assertEqual(call_args['signal_type'], 'kol_buy')

    @patch('api.v1.signal_api.SignalDAO')
    async def test_combination_with_pagination(self, mock_signal_dao_class):
        """测试与分页参数的组合"""
        # 模拟分页返回的数据
        paginated_data = self.test_data[1:3]  # 跳过第一条，返回2条
        
        # 设置mock
        mock_dao_instance = MagicMock()
        mock_signal_dao_class.return_value = mock_dao_instance
        
        mock_collection = MagicMock()
        mock_dao_instance.collection = mock_collection
        
        mock_cursor = MagicMock()
        mock_collection.find.return_value = mock_cursor
        mock_cursor.sort.return_value = mock_cursor
        mock_cursor.skip.return_value = mock_cursor
        mock_cursor.limit.return_value = mock_cursor
        mock_cursor.to_list = AsyncMock(return_value=paginated_data)

        # 调用API，指定分页参数
        result = await list_signals(
            skip=1, 
            limit=2, 
            signal_type=None, 
            period=None, 
            tz="Asia/Shanghai"
        )

        # 验证结果
        self.assertEqual(result.code, 0)
        self.assertEqual(len(result.data), 2)
        
        # 验证分页参数被正确传递
        mock_cursor.skip.assert_called_once_with(1)
        mock_cursor.limit.assert_called_once_with(2)

    @patch('api.v1.signal_api.SignalDAO')
    async def test_null_strategy_name_handling(self, mock_signal_dao_class):
        """测试strategy_name为null的记录处理"""
        # 添加strategy_name为null的测试数据
        null_strategy_data = [{
            "_id": PydanticObjectId(),
            "token_address": "TokenAddressNull",
            "signal_type": "kol_buy", 
            "trigger_conditions": {
                "strategy_name": None,
                "other_param": "value"
            },
            "trigger_timestamp": datetime(2025, 5, 27, 13, 0, 0),
            "created_at": datetime(2025, 5, 27, 13, 0, 0),
            "updated_at": None,
            "hit_kol_wallets": ["wallet_null"]
        }]
        
        # 设置mock
        mock_dao_instance = MagicMock()
        mock_signal_dao_class.return_value = mock_dao_instance
        
        mock_collection = MagicMock()
        mock_dao_instance.collection = mock_collection
        
        mock_cursor = MagicMock()
        mock_collection.find.return_value = mock_cursor
        mock_cursor.sort.return_value = mock_cursor
        mock_cursor.skip.return_value = mock_cursor
        mock_cursor.limit.return_value = mock_cursor
        mock_cursor.to_list = AsyncMock(return_value=null_strategy_data)

        # 调用API
        result = await list_signals(
            skip=0, 
            limit=10, 
            signal_type=None, 
            period=None, 
            tz="Asia/Shanghai"
        )

        # 验证结果 - null值的记录应该被保留
        self.assertEqual(result.code, 0)
        self.assertEqual(len(result.data), 1)
        self.assertEqual(str(result.data[0].id), str(null_strategy_data[0]["_id"]))

    @patch('api.v1.signal_api.SignalDAO')
    async def test_empty_string_strategy_name_handling(self, mock_signal_dao_class):
        """测试strategy_name为空字符串的记录处理"""
        # 添加strategy_name为空字符串的测试数据
        empty_strategy_data = [{
            "_id": PydanticObjectId(),
            "token_address": "TokenAddressEmpty",
            "signal_type": "kol_buy",
            "trigger_conditions": {
                "strategy_name": "",
                "other_param": "value"
            },
            "trigger_timestamp": datetime(2025, 5, 27, 14, 0, 0),
            "created_at": datetime(2025, 5, 27, 14, 0, 0),
            "updated_at": None,
            "hit_kol_wallets": ["wallet_empty"]
        }]
        
        # 设置mock
        mock_dao_instance = MagicMock()
        mock_signal_dao_class.return_value = mock_dao_instance
        
        mock_collection = MagicMock()
        mock_dao_instance.collection = mock_collection
        
        mock_cursor = MagicMock()
        mock_collection.find.return_value = mock_cursor
        mock_cursor.sort.return_value = mock_cursor
        mock_cursor.skip.return_value = mock_cursor
        mock_cursor.limit.return_value = mock_cursor
        mock_cursor.to_list = AsyncMock(return_value=empty_strategy_data)

        # 调用API
        result = await list_signals(
            skip=0, 
            limit=10, 
            signal_type=None, 
            period=None, 
            tz="Asia/Shanghai"
        )

        # 验证结果 - 空字符串的记录应该被保留
        self.assertEqual(result.code, 0)
        self.assertEqual(len(result.data), 1)
        self.assertEqual(str(result.data[0].id), str(empty_strategy_data[0]["_id"]))

    @patch('api.v1.signal_api.SignalDAO')
    async def test_error_handling(self, mock_signal_dao_class):
        """测试错误处理"""
        # 设置mock抛出异常
        mock_dao_instance = MagicMock()
        mock_signal_dao_class.return_value = mock_dao_instance
        
        mock_collection = MagicMock()
        mock_dao_instance.collection = mock_collection
        mock_collection.find.side_effect = Exception("Database error")

        # 调用API
        result = await list_signals(
            skip=0, 
            limit=10, 
            signal_type=None, 
            period=None, 
            tz="Asia/Shanghai"
        )

        # 验证错误处理
        self.assertEqual(result.code, 5001)  # ERROR_CODE_DB_ERROR
        self.assertIn("获取信号数据时出错", result.msg)
        self.assertIsNone(result.data)


    # ===== 新增：original_buy_strategy_name 过滤功能测试 =====
    
    @patch('api.v1.signal_api.SignalDAO')
    async def test_filter_original_buy_strategy_signals(self, mock_signal_dao_class):
        """测试过滤original_buy_strategy_name为"测试"的记录"""
        # 准备测试数据
        test_data_with_original_buy = [
            {
                "_id": PydanticObjectId(),
                "token_address": "token1",
                "signal_type": "kol_sell",
                "trigger_conditions": {
                    "strategy_name": "正常策略",
                    "original_buy_strategy_name": "测试"  # 应被过滤
                },
                "trigger_timestamp": datetime(2025, 5, 27, 8, 0, 0),
                "created_at": datetime(2025, 5, 27, 8, 0, 0),
                "updated_at": None,
                "hit_kol_wallets": ["wallet1"]
            },
            {
                "_id": PydanticObjectId(),
                "token_address": "token2", 
                "signal_type": "kol_sell",
                "trigger_conditions": {
                    "strategy_name": "正常策略",
                    "original_buy_strategy_name": "正常策略"  # 应保留
                },
                "trigger_timestamp": datetime(2025, 5, 27, 9, 0, 0),
                "created_at": datetime(2025, 5, 27, 9, 0, 0),
                "updated_at": None,
                "hit_kol_wallets": ["wallet2"]
            }
        ]
        
        # 模拟过滤后的数据（排除original_buy_strategy_name为"测试"的记录）
        filtered_data = [test_data_with_original_buy[1]]  # 只保留第二条记录
        
        # 设置mock
        mock_dao_instance = MagicMock()
        mock_signal_dao_class.return_value = mock_dao_instance
        
        mock_collection = MagicMock()
        mock_dao_instance.collection = mock_collection
        
        mock_cursor = MagicMock()
        mock_collection.find.return_value = mock_cursor
        mock_cursor.sort.return_value = mock_cursor
        mock_cursor.skip.return_value = mock_cursor
        mock_cursor.limit.return_value = mock_cursor
        mock_cursor.to_list = AsyncMock(return_value=filtered_data)

        # 调用API
        result = await list_signals(
            skip=0, 
            limit=10, 
            signal_type=None, 
            period=None, 
            tz="Asia/Shanghai"
        )

        # 验证结果
        self.assertEqual(result.code, 0)
        self.assertEqual(result.msg, "success")
        self.assertIsNotNone(result.data)
        self.assertEqual(len(result.data), 1)  # 应该返回1条记录
        
        # 验证返回的记录是正确的（不包含original_buy_strategy_name为"测试"的记录）
        self.assertEqual(str(result.data[0].id), str(test_data_with_original_buy[1]["_id"]))
        
        # 验证查询条件包含$and逻辑
        mock_collection.find.assert_called_once()
        call_args = mock_collection.find.call_args[0][0]
        self.assertIn('$and', call_args)
        self.assertEqual(len(call_args['$and']), 2)

    @patch('api.v1.signal_api.SignalDAO')
    async def test_original_buy_strategy_field_not_exists(self, mock_signal_dao_class):
        """测试保留original_buy_strategy_name字段不存在的记录"""
        # 准备测试数据
        test_data_no_original_buy = [
            {
                "_id": PydanticObjectId(),
                "token_address": "token1",
                "signal_type": "kol_buy",
                "trigger_conditions": {
                    "strategy_name": "正常策略"
                    # 没有 original_buy_strategy_name 字段
                },
                "trigger_timestamp": datetime(2025, 5, 27, 8, 0, 0),
                "created_at": datetime(2025, 5, 27, 8, 0, 0),
                "updated_at": None,
                "hit_kol_wallets": ["wallet1"]
            },
            {
                "_id": PydanticObjectId(),
                "token_address": "token2",
                "signal_type": "kol_sell", 
                "trigger_conditions": {
                    "strategy_name": "正常策略",
                    "original_buy_strategy_name": "测试"  # 应被过滤
                },
                "trigger_timestamp": datetime(2025, 5, 27, 9, 0, 0),
                "created_at": datetime(2025, 5, 27, 9, 0, 0),
                "updated_at": None,
                "hit_kol_wallets": ["wallet2"]
            }
        ]
        
        # 模拟过滤后的数据（保留没有original_buy_strategy_name字段的记录）
        filtered_data = [test_data_no_original_buy[0]]  # 只保留第一条记录
        
        # 设置mock
        mock_dao_instance = MagicMock()
        mock_signal_dao_class.return_value = mock_dao_instance
        
        mock_collection = MagicMock()
        mock_dao_instance.collection = mock_collection
        
        mock_cursor = MagicMock()
        mock_collection.find.return_value = mock_cursor
        mock_cursor.sort.return_value = mock_cursor
        mock_cursor.skip.return_value = mock_cursor
        mock_cursor.limit.return_value = mock_cursor
        mock_cursor.to_list = AsyncMock(return_value=filtered_data)

        # 调用API
        result = await list_signals(
            skip=0, 
            limit=10, 
            signal_type=None, 
            period=None, 
            tz="Asia/Shanghai"
        )

        # 验证结果
        self.assertEqual(result.code, 0)
        self.assertEqual(len(result.data), 1)
        
        # 验证保留了没有original_buy_strategy_name字段的记录
        self.assertEqual(str(result.data[0].id), str(test_data_no_original_buy[0]["_id"]))

    @patch('api.v1.signal_api.SignalDAO')
    async def test_combined_strategy_filtering(self, mock_signal_dao_class):
        """测试同时过滤两个策略字段的组合情况"""
        # 准备测试数据
        combined_test_data = [
            {
                "_id": PydanticObjectId(),
                "token_address": "token1",
                "signal_type": "kol_sell",
                "trigger_conditions": {
                    "strategy_name": "测试",  # 应被过滤
                    "original_buy_strategy_name": "正常策略"
                },
                "trigger_timestamp": datetime(2025, 5, 27, 8, 0, 0),
                "created_at": datetime(2025, 5, 27, 8, 0, 0),
                "updated_at": None,
                "hit_kol_wallets": ["wallet1"]
            },
            {
                "_id": PydanticObjectId(),
                "token_address": "token2",
                "signal_type": "kol_sell",
                "trigger_conditions": {
                    "strategy_name": "正常策略",
                    "original_buy_strategy_name": "测试"  # 应被过滤
                },
                "trigger_timestamp": datetime(2025, 5, 27, 9, 0, 0),
                "created_at": datetime(2025, 5, 27, 9, 0, 0),
                "updated_at": None,
                "hit_kol_wallets": ["wallet2"]
            },
            {
                "_id": PydanticObjectId(),
                "token_address": "token3",
                "signal_type": "kol_sell",
                "trigger_conditions": {
                    "strategy_name": "正常策略",
                    "original_buy_strategy_name": "正常策略"  # 应保留
                },
                "trigger_timestamp": datetime(2025, 5, 27, 10, 0, 0),
                "created_at": datetime(2025, 5, 27, 10, 0, 0),
                "updated_at": None,
                "hit_kol_wallets": ["wallet3"]
            }
        ]
        
        # 模拟过滤后的数据（只保留两个字段都不为"测试"的记录）
        filtered_data = [combined_test_data[2]]  # 只保留第三条记录
        
        # 设置mock
        mock_dao_instance = MagicMock()
        mock_signal_dao_class.return_value = mock_dao_instance
        
        mock_collection = MagicMock()
        mock_dao_instance.collection = mock_collection
        
        mock_cursor = MagicMock()
        mock_collection.find.return_value = mock_cursor
        mock_cursor.sort.return_value = mock_cursor
        mock_cursor.skip.return_value = mock_cursor
        mock_cursor.limit.return_value = mock_cursor
        mock_cursor.to_list = AsyncMock(return_value=filtered_data)

        # 调用API
        result = await list_signals(
            skip=0, 
            limit=10, 
            signal_type=None, 
            period=None, 
            tz="Asia/Shanghai"
        )

        # 验证结果
        self.assertEqual(result.code, 0)
        self.assertEqual(len(result.data), 1)
        
        # 验证只保留了两个字段都不为"测试"的记录
        self.assertEqual(str(result.data[0].id), str(combined_test_data[2]["_id"]))

    @patch('api.v1.signal_api.SignalDAO')
    async def test_both_strategy_fields_test(self, mock_signal_dao_class):
        """测试两个字段都为"测试"的记录被正确过滤"""
        # 准备测试数据
        both_test_data = [
            {
                "_id": PydanticObjectId(),
                "token_address": "token1",
                "signal_type": "kol_sell",
                "trigger_conditions": {
                    "strategy_name": "测试",
                    "original_buy_strategy_name": "测试"  # 应被过滤
                },
                "trigger_timestamp": datetime(2025, 5, 27, 8, 0, 0),
                "created_at": datetime(2025, 5, 27, 8, 0, 0),
                "updated_at": None,
                "hit_kol_wallets": ["wallet1"]
            },
            {
                "_id": PydanticObjectId(),
                "token_address": "token2",
                "signal_type": "kol_sell",
                "trigger_conditions": {
                    "strategy_name": "正常策略",
                    "original_buy_strategy_name": "正常策略"  # 应保留
                },
                "trigger_timestamp": datetime(2025, 5, 27, 9, 0, 0),
                "created_at": datetime(2025, 5, 27, 9, 0, 0),
                "updated_at": None,
                "hit_kol_wallets": ["wallet2"]
            }
        ]
        
        # 模拟过滤后的数据（过滤掉两个字段都为"测试"的记录）
        filtered_data = [both_test_data[1]]  # 只保留第二条记录
        
        # 设置mock
        mock_dao_instance = MagicMock()
        mock_signal_dao_class.return_value = mock_dao_instance
        
        mock_collection = MagicMock()
        mock_dao_instance.collection = mock_collection
        
        mock_cursor = MagicMock()
        mock_collection.find.return_value = mock_cursor
        mock_cursor.sort.return_value = mock_cursor
        mock_cursor.skip.return_value = mock_cursor
        mock_cursor.limit.return_value = mock_cursor
        mock_cursor.to_list = AsyncMock(return_value=filtered_data)

        # 调用API
        result = await list_signals(
            skip=0, 
            limit=10, 
            signal_type=None, 
            period=None, 
            tz="Asia/Shanghai"
        )

        # 验证结果
        self.assertEqual(result.code, 0)
        self.assertEqual(len(result.data), 1)
        
        # 验证过滤掉了两个字段都为"测试"的记录
        self.assertEqual(str(result.data[0].id), str(both_test_data[1]["_id"]))

    @patch('api.v1.signal_api.SignalDAO')
    async def test_case_sensitive_original_buy_strategy_filtering(self, mock_signal_dao_class):
        """测试original_buy_strategy_name过滤条件区分大小写"""
        # 准备测试数据
        case_test_data = [
            {
                "_id": PydanticObjectId(),
                "token_address": "token1",
                "signal_type": "kol_sell",
                "trigger_conditions": {
                    "strategy_name": "正常策略",
                    "original_buy_strategy_name": "测试"  # 应被过滤
                },
                "trigger_timestamp": datetime(2025, 5, 27, 8, 0, 0),
                "created_at": datetime(2025, 5, 27, 8, 0, 0),
                "updated_at": None,
                "hit_kol_wallets": ["wallet1"]
            },
            {
                "_id": PydanticObjectId(),
                "token_address": "token2",
                "signal_type": "kol_sell",
                "trigger_conditions": {
                    "strategy_name": "正常策略", 
                    "original_buy_strategy_name": "Test"  # 应保留（大小写不同）
                },
                "trigger_timestamp": datetime(2025, 5, 27, 9, 0, 0),
                "created_at": datetime(2025, 5, 27, 9, 0, 0),
                "updated_at": None,
                "hit_kol_wallets": ["wallet2"]
            },
            {
                "_id": PydanticObjectId(),
                "token_address": "token3",
                "signal_type": "kol_sell",
                "trigger_conditions": {
                    "strategy_name": "正常策略",
                    "original_buy_strategy_name": "测试1"  # 应保留（不完全匹配）
                },
                "trigger_timestamp": datetime(2025, 5, 27, 10, 0, 0),
                "created_at": datetime(2025, 5, 27, 10, 0, 0),
                "updated_at": None,
                "hit_kol_wallets": ["wallet3"]
            }
        ]
        
        # 模拟过滤后的数据（只过滤完全匹配"测试"的记录）
        filtered_data = [case_test_data[1], case_test_data[2]]  # 保留"Test"和"测试1"
        
        # 设置mock
        mock_dao_instance = MagicMock()
        mock_signal_dao_class.return_value = mock_dao_instance
        
        mock_collection = MagicMock()
        mock_dao_instance.collection = mock_collection
        
        mock_cursor = MagicMock()
        mock_collection.find.return_value = mock_cursor
        mock_cursor.sort.return_value = mock_cursor
        mock_cursor.skip.return_value = mock_cursor
        mock_cursor.limit.return_value = mock_cursor
        mock_cursor.to_list = AsyncMock(return_value=filtered_data)

        # 调用API
        result = await list_signals(
            skip=0, 
            limit=10, 
            signal_type=None, 
            period=None, 
            tz="Asia/Shanghai"
        )

        # 验证结果
        self.assertEqual(result.code, 0)
        self.assertEqual(len(result.data), 2)
        
        # 验证保留了"Test"、"测试1"等记录
        signal_ids = [str(signal.id) for signal in result.data]
        self.assertIn(str(case_test_data[1]["_id"]), signal_ids)  # "Test"
        self.assertIn(str(case_test_data[2]["_id"]), signal_ids)  # "测试1"
        self.assertNotIn(str(case_test_data[0]["_id"]), signal_ids)  # "测试"

    @patch('api.v1.signal_api.SignalDAO')
    async def test_null_original_buy_strategy_name_handling(self, mock_signal_dao_class):
        """测试original_buy_strategy_name为null值和空字符串的处理"""
        # 准备测试数据
        null_test_data = [
            {
                "_id": PydanticObjectId(),
                "token_address": "token1",
                "signal_type": "kol_sell",
                "trigger_conditions": {
                    "strategy_name": "正常策略",
                    "original_buy_strategy_name": None  # 应保留
                },
                "trigger_timestamp": datetime(2025, 5, 27, 8, 0, 0),
                "created_at": datetime(2025, 5, 27, 8, 0, 0),
                "updated_at": None,
                "hit_kol_wallets": ["wallet1"]
            },
            {
                "_id": PydanticObjectId(),
                "token_address": "token2",
                "signal_type": "kol_sell",
                "trigger_conditions": {
                    "strategy_name": "正常策略",
                    "original_buy_strategy_name": ""  # 应保留
                },
                "trigger_timestamp": datetime(2025, 5, 27, 9, 0, 0),
                "created_at": datetime(2025, 5, 27, 9, 0, 0),
                "updated_at": None,
                "hit_kol_wallets": ["wallet2"]
            },
            {
                "_id": PydanticObjectId(),
                "token_address": "token3",
                "signal_type": "kol_sell",
                "trigger_conditions": {
                    "strategy_name": "正常策略",
                    "original_buy_strategy_name": "测试"  # 应被过滤
                },
                "trigger_timestamp": datetime(2025, 5, 27, 10, 0, 0),
                "created_at": datetime(2025, 5, 27, 10, 0, 0),
                "updated_at": None,
                "hit_kol_wallets": ["wallet3"]
            }
        ]
        
        # 模拟过滤后的数据（保留null值和空字符串的记录）
        filtered_data = [null_test_data[0], null_test_data[1]]  # 保留null和空字符串
        
        # 设置mock
        mock_dao_instance = MagicMock()
        mock_signal_dao_class.return_value = mock_dao_instance
        
        mock_collection = MagicMock()
        mock_dao_instance.collection = mock_collection
        
        mock_cursor = MagicMock()
        mock_collection.find.return_value = mock_cursor
        mock_cursor.sort.return_value = mock_cursor
        mock_cursor.skip.return_value = mock_cursor
        mock_cursor.limit.return_value = mock_cursor
        mock_cursor.to_list = AsyncMock(return_value=filtered_data)

        # 调用API
        result = await list_signals(
            skip=0, 
            limit=10, 
            signal_type=None, 
            period=None, 
            tz="Asia/Shanghai"
        )

        # 验证结果
        self.assertEqual(result.code, 0)
        self.assertEqual(len(result.data), 2)
        
        # 验证保留了null值和空字符串的记录
        signal_ids = [str(signal.id) for signal in result.data]
        self.assertIn(str(null_test_data[0]["_id"]), signal_ids)  # null
        self.assertIn(str(null_test_data[1]["_id"]), signal_ids)  # 空字符串
        self.assertNotIn(str(null_test_data[2]["_id"]), signal_ids)  # "测试"


if __name__ == '__main__':
    unittest.main() 