#!/usr/bin/env python3
"""
通用测试运行器

用于运行项目中所有的测试用例，包括同步和异步测试
使用分离式进程运行避免事件循环冲突
"""

import asyncio
import unittest
import sys
import os
import importlib
import traceback
import subprocess
import json
from unittest.mock import patch
import warnings

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# 忽略一些警告
warnings.filterwarnings("ignore", category=RuntimeWarning, message=".*coroutine.*was never awaited")
warnings.filterwarnings("ignore", category=DeprecationWarning)


class TestResult:
    """测试结果封装"""
    def __init__(self, name, success=False, error=None, skipped=False, skip_reason=None):
        self.name = name
        self.success = success
        self.error = error
        self.skipped = skipped
        self.skip_reason = skip_reason


class UniversalTestRunner:
    """通用测试运行器"""
    
    def __init__(self):
        self.results = []
    
    def is_async_test_class(self, test_class):
        """检查是否是异步测试类"""
        # 检查是否继承自IsolatedAsyncioTestCase
        from unittest import IsolatedAsyncioTestCase
        return issubclass(test_class, IsolatedAsyncioTestCase)
    
    def run_async_test_module_in_subprocess(self, module_name):
        """在独立进程中运行异步测试模块"""
        results = []
        
        try:
            # 构建运行单个模块的命令
            cmd = [
                sys.executable, 
                "-m", "unittest", 
                module_name, 
                "-v"
            ]
            
            # 设置环境变量
            env = os.environ.copy()
            env['PYTHONPATH'] = '/Users/<USER>/memeMonitor'
            
            # 运行测试
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd='/Users/<USER>/memeMonitor',
                env=env,
                timeout=300  # 5分钟超时
            )
            
            output_lines = result.stdout.split('\n') + result.stderr.split('\n')
            
            # 解析测试结果
            for line in output_lines:
                line = line.strip()
                if ' ... ok' in line:
                    test_name = line.split(' ... ok')[0].strip()
                    results.append(TestResult(test_name, success=True))
                elif ' ... FAIL' in line or ' ... ERROR' in line:
                    test_name = line.split(' ... ')[0].strip()
                    error_msg = "See subprocess output for details"
                    results.append(TestResult(test_name, success=False, error=error_msg))
                elif ' ... skipped' in line:
                    test_name = line.split(' ... skipped')[0].strip()
                    skip_reason = line.split(' ... skipped')[1].strip() if len(line.split(' ... skipped')) > 1 else "No reason provided"
                    results.append(TestResult(test_name, skipped=True, skip_reason=skip_reason))
            
            # 如果没有解析到具体的测试结果，但进程成功退出，尝试估算
            if not results and result.returncode == 0:
                # 尝试从输出中获取测试数量
                for line in output_lines:
                    if 'Ran ' in line and ' test' in line:
                        try:
                            import re
                            match = re.search(r'Ran (\d+) test', line)
                            if match:
                                test_count = int(match.group(1))
                                for i in range(test_count):
                                    results.append(TestResult(f"{module_name}.test_{i}", success=True))
                                break
                        except:
                            pass
            
            # 如果进程失败，记录错误
            if result.returncode != 0 and not results:
                error_msg = f"Subprocess failed with code {result.returncode}"
                if result.stderr:
                    error_msg += f": {result.stderr[:200]}"
                results.append(TestResult(module_name, success=False, error=error_msg))
                
        except subprocess.TimeoutExpired:
            results.append(TestResult(module_name, success=False, error="Test execution timeout (5 minutes)"))
        except Exception as e:
            results.append(TestResult(module_name, success=False, error=f"Subprocess execution failed: {e}"))
        
        return results
    
    def run_sync_test_class(self, test_class):
        """运行同步测试类"""
        results = []
        
        # 创建测试套件
        suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
        
        # 运行测试
        test_result = unittest.TestResult()
        suite.run(test_result)
        
        # 处理结果
        for test, error in test_result.failures:
            if test and hasattr(test, '_testMethodName'):
                test_name = f"{test.__class__.__name__}.{test._testMethodName}"
            else:
                test_name = f"{test_class.__name__}.unknown_method"
            results.append(TestResult(test_name, success=False, error=error))
        
        for test, error in test_result.errors:
            if test and hasattr(test, '_testMethodName'):
                test_name = f"{test.__class__.__name__}.{test._testMethodName}"
            else:
                test_name = f"{test_class.__name__}.unknown_method"
            results.append(TestResult(test_name, success=False, error=error))
        
        for test in test_result.skipped:
            if test and len(test) > 0 and test[0] and hasattr(test[0], '_testMethodName'):
                test_name = f"{test[0].__class__.__name__}.{test[0]._testMethodName}"
                skip_reason = test[1] if len(test) > 1 else "No reason provided"
            else:
                test_name = f"{test_class.__name__}.unknown_method"
                skip_reason = "Unknown skip reason"
            results.append(TestResult(test_name, skipped=True, skip_reason=skip_reason))
        
        # 计算成功的测试
        total_tests = test_result.testsRun
        failed_tests = len(test_result.failures) + len(test_result.errors)
        successful_tests = total_tests - failed_tests - len(test_result.skipped)
        
        # 为成功的测试添加结果
        if successful_tests > 0:
            # 获取实际成功的测试名称
            all_test_methods = []
            for test in suite:
                if test and hasattr(test, '_testMethodName'):
                    all_test_methods.append(f"{test.__class__.__name__}.{test._testMethodName}")
            
            failed_test_names = set()
            for test, error in test_result.failures + test_result.errors:
                if test and hasattr(test, '_testMethodName'):
                    failed_test_names.add(f"{test.__class__.__name__}.{test._testMethodName}")
            
            skipped_test_names = set()
            for test in test_result.skipped:
                if test and len(test) > 0 and test[0] and hasattr(test[0], '_testMethodName'):
                    skipped_test_names.add(f"{test[0].__class__.__name__}.{test[0]._testMethodName}")
            
            for test_method in all_test_methods:
                if test_method not in failed_test_names and test_method not in skipped_test_names:
                    results.append(TestResult(test_method, success=True))
        
        return results
    
    async def run_test_module(self, module_name):
        """运行测试模块"""
        try:
            print(f"正在测试模块: {module_name}")
            
            # 尝试导入模块检查类型
            try:
                module = importlib.import_module(module_name)
            except ImportError as e:
                print(f"  ⚠️  跳过 {module_name}: 导入失败 - {e}")
                return [TestResult(module_name, skipped=True, skip_reason=f"导入失败: {e}")]
            
            # 查找测试类
            test_classes = []
            has_async_test = False
            
            for attr_name in dir(module):
                attr = getattr(module, attr_name)
                if (isinstance(attr, type) and 
                    issubclass(attr, unittest.TestCase) and 
                    attr != unittest.TestCase):
                    test_classes.append(attr)
                    if self.is_async_test_class(attr):
                        has_async_test = True
            
            if not test_classes:
                print(f"  ⚠️  跳过 {module_name}: 未找到测试类")
                return [TestResult(module_name, skipped=True, skip_reason="未找到测试类")]
            
            # 如果包含异步测试，使用subprocess运行整个模块
            if has_async_test:
                print(f"  🔄 {module_name}: 检测到异步测试，使用独立进程运行")
                results = self.run_async_test_module_in_subprocess(module_name)
                
                # 输出结果
                for result in results:
                    if result.success:
                        print(f"    ✅ {result.name}")
                    elif result.skipped:
                        print(f"    ⏭️  {result.name} (跳过: {result.skip_reason})")
                    else:
                        print(f"    ❌ {result.name}: {result.error}")
                
                return results
            else:
                # 同步测试，正常运行
                all_results = []
                
                for test_class in test_classes:
                    try:
                        results = self.run_sync_test_class(test_class)
                        all_results.extend(results)
                        
                        # 输出结果
                        for result in results:
                            if result.success:
                                print(f"    ✅ {result.name}")
                            elif result.skipped:
                                print(f"    ⏭️  {result.name} (跳过: {result.skip_reason})")
                            else:
                                print(f"    ❌ {result.name}: {result.error}")
                    
                    except Exception as e:
                        error_msg = f"运行测试类 {test_class.__name__} 时出错: {e}"
                        print(f"    ❌ {test_class.__name__}: {error_msg}")
                        all_results.append(TestResult(f"{test_class.__name__}", success=False, error=error_msg))
                
                return all_results
            
        except Exception as e:
            error_msg = f"运行模块 {module_name} 时出错: {e}"
            print(f"  ❌ {module_name}: {error_msg}")
            return [TestResult(module_name, success=False, error=error_msg)]
    
    def discover_test_modules(self):
        """发现所有测试模块"""
        test_modules = []
        test_dir = os.path.dirname(__file__)
        
        for root, dirs, files in os.walk(test_dir):
            for file in files:
                if file.startswith('test_') and file.endswith('.py'):
                    rel_path = os.path.relpath(os.path.join(root, file), test_dir)
                    module_path = rel_path.replace(os.sep, '.').replace('.py', '')
                    test_modules.append(f"test.{module_path}")
        
        return sorted(test_modules)
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🧪 发现并运行所有项目测试...")
        print("=" * 80)
        
        test_modules = self.discover_test_modules()
        print(f"📋 发现 {len(test_modules)} 个测试模块\n")
        
        all_results = []
        
        for module_name in test_modules:
            results = await self.run_test_module(module_name)
            all_results.extend(results)
            print()  # 空行分隔
        
        # 统计结果
        total_tests = len(all_results)
        passed_tests = sum(1 for r in all_results if r.success)
        failed_tests = sum(1 for r in all_results if not r.success and not r.skipped)
        skipped_tests = sum(1 for r in all_results if r.skipped)
        
        print("=" * 80)
        print("📊 测试结果总结:")
        print(f"   总计: {total_tests}")
        print(f"   通过: {passed_tests}")
        print(f"   失败: {failed_tests}")
        print(f"   跳过: {skipped_tests}")
        
        if failed_tests > 0:
            print(f"\n❌ 失败的测试 ({failed_tests}):")
            for result in all_results:
                if not result.success and not result.skipped:
                    print(f"   - {result.name}: {result.error}")
        
        if skipped_tests > 0:
            print(f"\n⏭️  跳过的测试 ({skipped_tests}):")
            for result in all_results:
                if result.skipped:
                    print(f"   - {result.name}: {result.skip_reason}")
        
        success_rate = passed_tests / total_tests * 100 if total_tests > 0 else 0
        print(f"\n🎯 测试通过率: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("🎉 测试整体状况良好！")
        elif success_rate >= 60:
            print("⚠️ 测试状况尚可，但需要关注失败的测试。")
        else:
            print("🚨 测试状况需要改善，存在较多失败测试。")
        
        return failed_tests == 0


async def main():
    """主函数"""
    runner = UniversalTestRunner()
    success = await runner.run_all_tests()
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code) 