# 回测模块V2使用指南

## 概述

回测模块V2是基于直接数据分析的回测系统，采用单一MongoDB聚合查询 + 向量化计算的简化架构，提供更精确的信号时间戳和更高的性能。

## 主要特性

- **简化架构**: 移除复杂的事件队列和适配器，采用直接分析方法
- **精确时间戳**: 使用实际KOL交易时间戳，而非模拟时间
- **高性能**: 单一聚合查询 + 向量化计算
- **买入即卖出过滤**: 过滤低质量信号
- **新代币过滤**: 只分析新代币时期内的交易
- **并发处理**: 支持多token并发信号分析

## 使用方法

### 单次回测

```bash
# 使用默认配置
python run_backtest_ed.py --mode single_v2

# 使用配置文件
python run_backtest_ed.py --mode single_v2 --config config_v2_example.yaml

# 使用命令行参数
python run_backtest_ed.py --mode single_v2 \
  --start_time ********** \
  --end_time ********** \
  --transaction_min_amount 500 \
  --kol_account_min_count 3 \
  --transaction_lookback_hours 24
```

### 参数网格搜索

```bash
# 使用默认参数网格
python run_backtest_ed.py --mode grid_v2

# 使用自定义参数网格
python run_backtest_ed.py --mode grid_v2 --param_grid param_grid_v2.json

# 控制并发数量
python run_backtest_ed.py --mode grid_v2 --max_concurrency 8
```

## 配置参数

### 回测时间参数
- `backtest_start_time`: 回测开始时间戳
- `backtest_end_time`: 回测结束时间戳

### 数据筛选参数
- `transaction_min_amount`: 最小交易金额(USD)
- `kol_account_min_txs`: KOL账号最小交易数
- `kol_account_max_txs`: KOL账号最大交易数
- `kol_account_min_count`: 最小KOL账号数量
- `token_mint_lookback_hours`: 代币创建时间回溯小时数

### 策略参数
- `transaction_lookback_hours`: 交易回溯时间窗口(小时)
- `sell_strategy_hours`: 卖出策略时间窗口(小时)
- `sell_kol_ratio`: 卖出KOL比例阈值

### 回测控制参数
- `initial_capital`: 初始资金
- `commission_pct`: 手续费百分比
- `slippage_pct`: 滑点百分比
- `same_token_notification_interval_minutes`: 相同代币通知最小间隔(分钟)

## 配置文件示例

### YAML格式
```yaml
# config_v2_example.yaml
backtest_start_time: **********
backtest_end_time: **********
transaction_min_amount: 500.0
kol_account_min_count: 3
transaction_lookback_hours: 24
sell_strategy_hours: 24
sell_kol_ratio: 0.5
token_mint_lookback_hours: 48
```

### JSON格式
```json
{
  "backtest_start_time": **********,
  "backtest_end_time": **********,
  "transaction_min_amount": 500.0,
  "kol_account_min_count": 3,
  "transaction_lookback_hours": 24,
  "sell_strategy_hours": 24,
  "sell_kol_ratio": 0.5,
  "token_mint_lookback_hours": 48
}
```

## 参数网格搜索示例

```json
{
  "transaction_lookback_hours": [12, 24, 48],
  "kol_account_min_count": [3, 5, 7],
  "sell_strategy_hours": [12, 24],
  "sell_kol_ratio": [0.3, 0.5, 0.7],
  "token_mint_lookback_hours": [24, 48, 72]
}
```

## 输出结果

回测结果保存在以时间戳命名的目录中：

### 单次回测
- `v2_backtest_results_YYYYMMDD_HHMMSS/`
  - `results.json`: 完整回测结果
  - `trades.csv`: 交易记录
  - `equity_curve.csv`: 资金曲线
  - `single_run_report.html`: HTML报告

### 参数网格搜索
- `v2_param_search_YYYYMMDD_HHMMSS/`
  - `param_search_summary_results.json`: 搜索结果摘要
  - `high_win_rate_50pct_results.csv`: 高胜率结果
  - `backtest_report.html`: 聚合HTML报告
  - `run_N_report.html`: 各参数组合的详细报告
  - `v2_backtest_results_*/`: 各参数组合的详细结果

## 与V1版本的区别

| 特性 | V1 (事件驱动) | V2 (直接分析) |
|------|---------------|---------------|
| 架构 | 事件队列 + 适配器 | 直接数据分析 |
| 数据获取 | 流式处理 | 单一聚合查询 |
| 信号时间戳 | 模拟时间 | 实际交易时间 |
| 性能 | 较慢 | 更快 |
| 内存使用 | 较高 | 较低 |
| 新代币过滤 | 无 | 有 |
| 买入即卖出过滤 | 无 | 有 |

## 性能优化

- 使用MongoDB索引优化查询性能
- 并发处理多个token的信号分析
- 向量化计算提升算法性能
- 预加载卖出数据减少查询次数

## 故障排除

### 常见问题

1. **内存不足**: 减少并发数量或缩短回测时间范围
2. **查询超时**: 检查MongoDB索引，优化查询条件
3. **无信号生成**: 降低KOL数量阈值或扩大时间窗口
4. **配置错误**: 检查参数范围和类型

### 日志级别

设置环境变量控制日志详细程度：
```bash
export LOG_LEVEL=DEBUG  # 详细日志
export LOG_LEVEL=INFO   # 标准日志
export LOG_LEVEL=ERROR  # 仅错误日志
```

## 开发和扩展

### 添加新的信号过滤器

1. 在`signal_analyzer.py`中添加新的过滤方法
2. 在`_sliding_window_analysis`中调用过滤方法
3. 添加相应的配置参数

### 添加新的卖出策略

1. 在`sell_strategy.py`中添加新的策略方法
2. 在`determine_sell_signals`中集成新策略
3. 更新配置管理器支持新参数

### 运行测试

```bash
# 运行所有V2测试
python -m pytest test/utils/backtest_v2/ -v

# 运行特定测试
python -m pytest test/utils/backtest_v2/test_config_manager.py -v
```
