"""配置管理组件测试"""

import pytest
import tempfile
import json
import yaml
from utils.backtest_v2.config_manager import ConfigManagerV2, BacktestConfigV2


class TestBacktestConfigV2:
    """测试BacktestConfigV2类"""
    
    def test_default_config(self):
        """测试默认配置"""
        config = BacktestConfigV2()
        
        assert config.backtest_start_time == **********
        assert config.backtest_end_time == **********
        assert config.transaction_min_amount == 500.0
        assert config.kol_account_min_count == 6
        assert config.transaction_lookback_hours == 24
        assert config.sell_strategy_hours == 24
        assert config.sell_kol_ratio == 0.5
        assert config.initial_capital == 10000.0
    
    def test_config_validation_success(self):
        """测试配置验证成功"""
        config = BacktestConfigV2(
            backtest_start_time=**********,
            backtest_end_time=**********,
            transaction_min_amount=1000.0,
            kol_account_min_count=3,
            sell_kol_ratio=0.6
        )
        # 应该不抛出异常
        assert config.transaction_min_amount == 1000.0
    
    def test_config_validation_failure(self):
        """测试配置验证失败"""
        # 测试开始时间大于结束时间
        with pytest.raises(ValueError, match="回测开始时间必须小于结束时间"):
            BacktestConfigV2(
                backtest_start_time=**********,
                backtest_end_time=**********
            )
        
        # 测试负数交易金额
        with pytest.raises(ValueError, match="最小交易金额必须大于0"):
            BacktestConfigV2(transaction_min_amount=-100)
        
        # 测试无效的卖出比例
        with pytest.raises(ValueError, match="卖出KOL比例阈值必须在\\(0, 1\\]范围内"):
            BacktestConfigV2(sell_kol_ratio=1.5)


class TestConfigManagerV2:
    """测试ConfigManagerV2类"""
    
    def test_get_default_config(self):
        """测试获取默认配置"""
        config = ConfigManagerV2.get_default_config()
        
        assert isinstance(config, BacktestConfigV2)
        assert config.transaction_min_amount == 500.0
    
    def test_load_from_json_file(self):
        """测试从JSON文件加载配置"""
        config_data = {
            "backtest_start_time": **********,
            "backtest_end_time": **********,
            "transaction_min_amount": 1000.0,
            "kol_account_min_count": 5
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(config_data, f)
            temp_path = f.name
        
        try:
            config = ConfigManagerV2.load_from_file(temp_path)
            assert config.transaction_min_amount == 1000.0
            assert config.kol_account_min_count == 5
        finally:
            import os
            os.unlink(temp_path)
    
    def test_load_from_yaml_file(self):
        """测试从YAML文件加载配置"""
        config_data = {
            "backtest_start_time": **********,
            "backtest_end_time": **********,
            "transaction_min_amount": 1500.0,
            "kol_account_min_count": 4
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            temp_path = f.name
        
        try:
            config = ConfigManagerV2.load_from_file(temp_path)
            assert config.transaction_min_amount == 1500.0
            assert config.kol_account_min_count == 4
        finally:
            import os
            os.unlink(temp_path)
    
    def test_generate_parameter_combinations(self):
        """测试参数组合生成"""
        param_grid = {
            'transaction_lookback_hours': [12, 24],
            'kol_account_min_count': [3, 5],
            'sell_kol_ratio': [0.3, 0.5]
        }
        
        combinations = ConfigManagerV2.generate_parameter_combinations(param_grid)
        
        # 应该生成 2 * 2 * 2 = 8 个组合
        assert len(combinations) == 8
        
        # 检查第一个组合
        first_combo = combinations[0]
        assert 'transaction_lookback_hours' in first_combo
        assert 'kol_account_min_count' in first_combo
        assert 'sell_kol_ratio' in first_combo
        
        # 检查所有组合都不同
        combo_tuples = [tuple(sorted(combo.items())) for combo in combinations]
        assert len(set(combo_tuples)) == 8
    
    def test_validate_config_success(self):
        """测试配置验证成功"""
        config_dict = {
            'backtest_start_time': **********,
            'backtest_end_time': **********,
            'transaction_min_amount': 500.0,
            'kol_account_min_count': 3
        }
        
        assert ConfigManagerV2.validate_config(config_dict) == True
    
    def test_validate_config_failure(self):
        """测试配置验证失败"""
        config_dict = {
            'backtest_start_time': **********,
            'backtest_end_time': **********,  # 错误：开始时间大于结束时间
            'transaction_min_amount': 500.0,
            'kol_account_min_count': 3
        }
        
        assert ConfigManagerV2.validate_config(config_dict) == False


if __name__ == '__main__':
    pytest.main([__file__])
