# 交易统计分析功能测试用例设计

## 1. 测试概述

### 1.1 测试目标
- 验证基于链上验证金额的交易统计功能正确性
- 确保数据筛选和计算逻辑准确
- 验证多种输出格式的正确性
- 测试排行榜功能的准确性

### 1.2 测试范围
- 数据筛选和验证
- 交易配对匹配
- 统计计算准确性
- 报告生成功能
- 命令行接口
- 异常处理

## 2. 单元测试用例

### 2.1 数据模型测试

#### 2.1.1 TradePair模型测试
```python
class TestTradePairModel:
    def test_trade_pair_creation(self):
        """测试交易对模型创建"""
        trade_pair = TradePair(
            signal_id="test_signal_001",
            strategy_name="gmgn_smart_money",
            token_address="So11111111111111111111111111111111111111112",
            buy_record_id="buy_001",
            sell_record_id="sell_001",
            buy_amount_sol=1.0,
            sell_amount_sol=1.2,
            profit_rate=20.0,
            profit_amount=0.2,
            is_profitable=True,
            buy_time=datetime.now(),
            sell_time=datetime.now(),
            holding_duration=2.5
        )
        assert trade_pair.profit_rate == 20.0
        assert trade_pair.is_profitable is True
    
    def test_trade_pair_validation(self):
        """测试交易对数据验证"""
        # 测试必填字段
        with pytest.raises(ValidationError):
            TradePair()
```

#### 2.1.2 统计结果模型测试
```python
class TestStatisticsModels:
    def test_overall_stats_model(self):
        """测试总体统计模型"""
        stats = OverallStats(
            total_trades=100,
            total_profit_rate=15.5,
            total_win_rate=65.0,
            avg_profit_rate=12.3,
            max_single_profit=5.0,
            max_single_loss=-2.5
        )
        assert stats.total_trades == 100
        assert stats.avg_profit_rate == 12.3
        assert stats.max_single_profit == 5.0
        assert stats.max_single_loss == -2.5
    
    def test_ranking_models(self):
        """测试排行榜模型"""
        profit_ranking = ProfitRanking(
            signal_id="signal_001",
            strategy_name="test_strategy",
            token_address="token_001",
            profit_amount=1.5,
            profit_rate=25.0,
            buy_time=datetime.now(),
            sell_time=datetime.now()
        )
        assert profit_ranking.profit_amount == 1.5
```

### 2.2 交易配对匹配器测试

#### 2.2.1 数据筛选测试
```python
class TestTradePairMatcher:
    @pytest.fixture
    def mock_trade_records(self):
        """模拟交易记录数据"""
        return [
            # 成功且已验证的买入记录
            TradeRecord(
                id="buy_001",
                signal_id="signal_001",
                trade_type=TradeType.BUY,
                status=TradeStatus.SUCCESS,
                verification_status=VerificationStatus.VERIFIED,
                token_in_amount=1.0,
                created_at=datetime(2024, 1, 1, 10, 0)
            ),
            # 成功且已验证的卖出记录
            TradeRecord(
                id="sell_001",
                signal_id="signal_001",
                trade_type=TradeType.SELL,
                status=TradeStatus.SUCCESS,
                verification_status=VerificationStatus.VERIFIED,
                token_out_verified_amount=1.2,
                created_at=datetime(2024, 1, 1, 12, 0)
            ),
            # 未验证的记录（应被过滤）
            TradeRecord(
                id="buy_002",
                signal_id="signal_002",
                trade_type=TradeType.BUY,
                status=TradeStatus.SUCCESS,
                verification_status=VerificationStatus.PENDING,
                token_in_amount=1.0,
                created_at=datetime(2024, 1, 1, 14, 0)
            )
        ]
    
    async def test_verified_records_filtering(self, mock_trade_records):
        """测试只获取已验证的交易记录"""
        matcher = TradePairMatcher(mock_dao)
        
        # 模拟数据库查询
        with patch.object(TradeRecord, 'find') as mock_find:
            mock_find.return_value.to_list.return_value = mock_trade_records[:2]  # 只返回已验证的
            
            config = StatisticsConfig()
            records = await matcher._get_verified_trade_records(config)
            
            # 验证查询条件包含验证状态
            mock_find.assert_called_once()
            query = mock_find.call_args[0][0]
            assert query["verification_status"] == VerificationStatus.VERIFIED
            assert len(records) == 2
    
    async def test_trade_pair_matching_with_verified_amount(self, mock_trade_records):
        """测试基于验证金额的交易配对"""
        matcher = TradePairMatcher(mock_dao)
        
        buy_record = mock_trade_records[0]
        sell_record = mock_trade_records[1]
        
        trade_pair = matcher._create_trade_pair(buy_record, sell_record)
        
        assert trade_pair is not None
        assert trade_pair.buy_amount_sol == 1.0
        assert trade_pair.sell_amount_sol == 1.2
        assert trade_pair.profit_amount == 0.2
        assert trade_pair.profit_rate == 20.0
        assert trade_pair.is_profitable is True
    
    def test_skip_unverified_sell_amount(self):
        """测试跳过缺少验证金额的交易对"""
        matcher = TradePairMatcher(mock_dao)
        
        buy_record = TradeRecord(
            token_in_amount=1.0,
            created_at=datetime.now()
        )
        sell_record = TradeRecord(
            token_out_verified_amount=None,  # 缺少验证金额
            created_at=datetime.now()
        )
        
        trade_pair = matcher._create_trade_pair(buy_record, sell_record)
        assert trade_pair is None
```

### 2.3 统计计算器测试

#### 2.3.1 总体统计计算测试
```python
class TestStatisticsCalculator:
    @pytest.fixture
    def sample_trade_pairs(self):
        """样本交易对数据"""
        return [
            TradePair(
                signal_id="signal_001",
                strategy_name="strategy_a",
                token_address="token_001",
                buy_amount_sol=1.0,
                sell_amount_sol=1.5,
                profit_amount=0.5,
                profit_rate=50.0,
                is_profitable=True,
                # ... 其他字段
            ),
            TradePair(
                signal_id="signal_002",
                strategy_name="strategy_a",
                token_address="token_002",
                buy_amount_sol=2.0,
                sell_amount_sol=1.8,
                profit_amount=-0.2,
                profit_rate=-10.0,
                is_profitable=False,
                # ... 其他字段
            ),
            TradePair(
                signal_id="signal_003",
                strategy_name="strategy_b",
                token_address="token_001",
                buy_amount_sol=1.5,
                sell_amount_sol=2.25,
                profit_amount=0.75,
                profit_rate=50.0,
                is_profitable=True,
                # ... 其他字段
            )
        ]
    
    def test_overall_stats_calculation(self, sample_trade_pairs):
        """测试总体统计计算"""
        calculator = StatisticsCalculator()
        stats = calculator.calculate_overall_stats(sample_trade_pairs)
        
        assert stats.total_trades == 3
        assert stats.profitable_trades == 2
        assert stats.loss_trades == 1
        assert stats.total_profit_amount == 1.05  # 0.5 + (-0.2) + 0.75
        assert stats.total_win_rate == 66.67  # 2/3 * 100
        assert stats.avg_profit_rate == 30.0  # (50 + (-10) + 50) / 3
        assert stats.max_single_profit == 0.75
        assert stats.max_single_loss == -0.2
    
    def test_token_stats_calculation(self, sample_trade_pairs):
        """测试Token统计计算"""
        calculator = StatisticsCalculator()
        token_stats = calculator.calculate_token_stats(sample_trade_pairs)
        
        # 应该有2个不同的token
        assert len(token_stats) == 2
        
        # 检查token_001的统计
        token_001_stats = next(s for s in token_stats if s.token_address == "token_001")
        assert token_001_stats.trade_count == 2
        assert token_001_stats.profitable_trades == 2
        assert token_001_stats.win_rate == 100.0
    
    def test_strategy_stats_calculation(self, sample_trade_pairs):
        """测试策略统计计算"""
        calculator = StatisticsCalculator()
        strategy_stats = calculator.calculate_strategy_stats(sample_trade_pairs)
        
        # 应该有2个不同的策略
        assert len(strategy_stats) == 2
        
        # 检查strategy_a的统计
        strategy_a_stats = next(s for s in strategy_stats if s.strategy_name == "strategy_a")
        assert strategy_a_stats.trade_count == 2
        assert strategy_a_stats.profitable_trades == 1
        assert strategy_a_stats.loss_trades == 1
        assert strategy_a_stats.win_rate == 50.0
        assert strategy_a_stats.max_single_loss == -0.2
    
    def test_profit_rankings_calculation(self, sample_trade_pairs):
        """测试盈利排行计算"""
        calculator = StatisticsCalculator()
        rankings = calculator.calculate_profit_rankings(sample_trade_pairs)
        
        # 只有盈利的交易
        assert len(rankings) == 2
        
        # 按盈利金额降序排列
        assert rankings[0].profit_amount == 0.75
        assert rankings[1].profit_amount == 0.5
    
    def test_loss_rankings_calculation(self, sample_trade_pairs):
        """测试亏损排行计算"""
        calculator = StatisticsCalculator()
        rankings = calculator.calculate_loss_rankings(sample_trade_pairs)
        
        # 只有亏损的交易
        assert len(rankings) == 1
        assert rankings[0].loss_amount == 0.2  # 转为正数
        assert rankings[0].loss_rate == 10.0   # 转为正数
```

### 2.4 报告生成器测试

#### 2.4.1 JSON报告生成测试
```python
class TestJSONReportGenerator:
    @pytest.fixture
    def sample_stats_result(self):
        """样本统计结果"""
        return StatisticsResult(
            overall_stats=OverallStats(total_trades=10, total_profit_rate=15.5),
            token_stats=[TokenStats(token_address="token_001", trade_count=5)],
            strategy_stats=[StrategyStats(strategy_name="strategy_a", trade_count=8)],
            trade_pairs=[],
            profit_rankings=[],
            loss_rankings=[],
            generation_time=datetime(2024, 1, 1, 12, 0),
            data_range={}
        )
    
    def test_json_report_generation(self, sample_stats_result):
        """测试JSON报告生成"""
        generator = JSONReportGenerator()
        json_data = generator.generate_json_report(sample_stats_result)
        
        assert "overall_stats" in json_data
        assert "token_stats" in json_data
        assert "strategy_stats" in json_data
        assert "profit_rankings" in json_data
        assert "loss_rankings" in json_data
        assert json_data["overall_stats"]["total_trades"] == 10
        assert len(json_data["token_stats"]) == 1
    
    def test_json_serialization(self, sample_stats_result):
        """测试JSON序列化"""
        generator = JSONReportGenerator()
        json_string = generator.serialize_to_json_string(sample_stats_result)
        
        # 验证可以正确解析
        parsed_data = json.loads(json_string)
        assert parsed_data["overall_stats"]["total_trades"] == 10
```

#### 2.4.2 HTML报告生成测试
```python
class TestHTMLReportGenerator:
    def test_html_template_rendering(self, sample_stats_result):
        """测试HTML模板渲染"""
        generator = HTMLReportGenerator()
        
        # 模拟图表生成
        with patch.object(generator.chart_generator, 'generate_overall_profit_pie_chart') as mock_chart:
            mock_chart.return_value = "<div>Mock Chart</div>"
            
            template = generator._get_html_template()
            html_content = template.render(
                stats=sample_stats_result,
                charts={"overall_profit_pie": "<div>Mock Chart</div>"},
                generation_time="2024-01-01 12:00:00"
            )
            
            assert "交易统计分析报告" in html_content
            assert "总体统计" in html_content
            assert "盈利排行榜" in html_content
            assert "亏损排行榜" in html_content
    
    async def test_html_file_generation(self, sample_stats_result, tmp_path):
        """测试HTML文件生成"""
        generator = HTMLReportGenerator()
        output_path = tmp_path / "test_report.html"
        
        result_path = await generator.generate_report(sample_stats_result, str(output_path))
        
        assert result_path == str(output_path)
        assert output_path.exists()
        
        # 验证文件内容
        content = output_path.read_text(encoding='utf-8')
        assert "交易统计分析报告" in content
```

### 2.5 主分析器测试

#### 2.5.1 多格式输出测试
```python
class TestTradeStatisticsAnalyzer:
    @pytest.fixture
    def analyzer(self):
        return TradeStatisticsAnalyzer()
    
    async def test_html_report_generation(self, analyzer, tmp_path):
        """测试HTML报告生成"""
        config = StatisticsConfig(
            report_format=ReportFormat.HTML,
            output_path=str(tmp_path / "test.html")
        )
        
        with patch.object(analyzer, 'generate_statistics') as mock_stats:
            mock_stats.return_value = sample_stats_result
            
            result = await analyzer.generate_report(config)
            
            assert isinstance(result, str)
            assert result.endswith(".html")
    
    async def test_json_report_generation(self, analyzer):
        """测试JSON报告生成"""
        config = StatisticsConfig(
            report_format=ReportFormat.JSON
        )
        
        with patch.object(analyzer, 'generate_statistics') as mock_stats:
            mock_stats.return_value = sample_stats_result
            
            result = await analyzer.generate_report(config)
            
            assert isinstance(result, dict)
            assert "overall_stats" in result
    
    async def test_html_without_output_path_error(self, analyzer):
        """测试HTML格式缺少输出路径的错误处理"""
        config = StatisticsConfig(
            report_format=ReportFormat.HTML,
            output_path=None
        )
        
        with pytest.raises(ValueError, match="HTML格式需要指定输出路径"):
            await analyzer.generate_report(config)
```

## 3. 集成测试用例

### 3.1 端到端测试
```python
class TestEndToEnd:
    @pytest.mark.asyncio
    async def test_complete_statistics_workflow(self):
        """测试完整的统计工作流"""
        # 准备测试数据
        await self._setup_test_data()
        
        # 创建配置
        config = StatisticsConfig(
            start_date=datetime(2024, 1, 1),
            end_date=datetime(2024, 12, 31),
            report_format=ReportFormat.JSON
        )
        
        # 执行统计分析
        analyzer = TradeStatisticsAnalyzer()
        result = await analyzer.generate_report(config)
        
        # 验证结果
        assert isinstance(result, dict)
        assert result["overall_stats"]["total_trades"] > 0
        assert len(result["profit_rankings"]) >= 0
        assert len(result["loss_rankings"]) >= 0
    
    async def _setup_test_data(self):
        """设置测试数据"""
        # 创建测试交易记录
        test_records = [
            TradeRecord(
                signal_id="test_signal_001",
                trade_type=TradeType.BUY,
                status=TradeStatus.SUCCESS,
                verification_status=VerificationStatus.VERIFIED,
                token_in_amount=1.0,
                strategy_name="test_strategy",
                created_at=datetime(2024, 6, 1, 10, 0)
            ),
            TradeRecord(
                signal_id="test_signal_001",
                trade_type=TradeType.SELL,
                status=TradeStatus.SUCCESS,
                verification_status=VerificationStatus.VERIFIED,
                token_out_verified_amount=1.2,
                strategy_name="test_strategy",
                created_at=datetime(2024, 6, 1, 12, 0)
            )
        ]
        
        # 保存到数据库
        for record in test_records:
            await record.save()
```

### 3.2 性能测试
```python
class TestPerformance:
    @pytest.mark.asyncio
    async def test_large_dataset_performance(self):
        """测试大数据集性能"""
        # 创建大量测试数据
        await self._create_large_dataset(10000)
        
        config = StatisticsConfig()
        analyzer = TradeStatisticsAnalyzer()
        
        start_time = time.time()
        result = await analyzer.generate_statistics(config)
        end_time = time.time()
        
        # 验证性能要求（30秒内完成）
        processing_time = end_time - start_time
        assert processing_time < 30, f"处理时间 {processing_time}s 超过30秒限制"
        
        # 验证结果正确性
        assert result.overall_stats.total_trades > 0
    
    async def _create_large_dataset(self, count):
        """创建大量测试数据"""
        # 批量创建测试交易记录
        pass
```

### 3.2 TradePairMatcher测试

#### 3.2.1 正常匹配测试
```python
async def test_match_trade_pairs_success(self):
    """测试正常的交易对匹配"""
    # 准备测试数据
    mock_verification_updater = Mock()
    matcher = TradePairMatcher(mock_verification_updater)
    
    trade_records = [
        create_mock_trade_record(
            signal_id="signal_001",
            trade_type=TradeType.BUY,
            token_in_amount=100.0,
            status=TradeStatus.SUCCESS,
            verification_status="verified"
        ),
        create_mock_trade_record(
            signal_id="signal_001", 
            trade_type=TradeType.SELL,
            token_out_verified_amount=150.0,
            status=TradeStatus.SUCCESS,
            verification_status="verified"
        )
    ]
    
    # 执行测试
    trade_pairs = await matcher.match_trade_pairs(trade_records)
    
    # 验证结果
    self.assertEqual(len(trade_pairs), 1)
    self.assertEqual(trade_pairs[0].profit_rate, 50.0)  # (150-100)/100*100
    self.assertEqual(trade_pairs[0].buy_amount, 100.0)
    self.assertEqual(trade_pairs[0].sell_amount, 150.0)

#### 3.2.2 验证金额重新获取测试
```python
async def test_match_trade_pairs_with_missing_verified_amount(self):
    """测试当token_out_verified_amount为空时重新获取的逻辑"""
    # 准备mock验证更新器
    mock_verification_updater = Mock()
    mock_verification_updater.verify_single_record = AsyncMock(
        return_value={"verified_amount": 120.0}
    )
    
    matcher = TradePairMatcher(mock_verification_updater)
    
    trade_records = [
        create_mock_trade_record(
            signal_id="signal_002",
            trade_type=TradeType.BUY,
            token_in_amount=100.0,
            status=TradeStatus.SUCCESS,
            verification_status="verified"
        ),
        create_mock_trade_record(
            signal_id="signal_002",
            trade_type=TradeType.SELL,
            token_out_verified_amount=None,  # 验证金额为空
            status=TradeStatus.SUCCESS,
            verification_status="verified",
            tx_hash="test_tx_hash",
            token_out_address="test_token_address",
            wallet_address="test_wallet_address"
        )
    ]
    
    # 执行测试
    trade_pairs = await matcher.match_trade_pairs(trade_records)
    
    # 验证结果
    self.assertEqual(len(trade_pairs), 1)
    self.assertEqual(trade_pairs[0].profit_rate, 20.0)  # (120-100)/100*100
    self.assertEqual(trade_pairs[0].sell_amount, 120.0)
    
    # 验证调用了重新获取方法
    mock_verification_updater.verify_single_record.assert_called_once_with(
        tx_hash="test_tx_hash",
        token_out_address="test_token_address", 
        wallet_address="test_wallet_address"
    )

#### 3.2.3 重新获取失败测试
```python
async def test_match_trade_pairs_reget_verification_failed(self):
    """测试重新获取验证金额失败的情况"""
    # 准备mock验证更新器（返回失败）
    mock_verification_updater = Mock()
    mock_verification_updater.verify_single_record = AsyncMock(
        return_value={"verified_amount": None}
    )
    
    matcher = TradePairMatcher(mock_verification_updater)
    
    trade_records = [
        create_mock_trade_record(
            signal_id="signal_003",
            trade_type=TradeType.BUY,
            token_in_amount=100.0,
            status=TradeStatus.SUCCESS,
            verification_status="verified"
        ),
        create_mock_trade_record(
            signal_id="signal_003",
            trade_type=TradeType.SELL,
            token_out_verified_amount=None,  # 验证金额为空
            status=TradeStatus.SUCCESS,
            verification_status="verified",
            tx_hash="test_tx_hash",
            token_out_address="test_token_address",
            wallet_address="test_wallet_address"
        )
    ]
    
    # 执行测试
    trade_pairs = await matcher.match_trade_pairs(trade_records)
    
    # 验证结果 - 应该跳过该交易对
    self.assertEqual(len(trade_pairs), 0)
    
    # 验证调用了重新获取方法
    mock_verification_updater.verify_single_record.assert_called_once()

#### 3.2.4 重新获取异常测试
```python
async def test_match_trade_pairs_reget_verification_exception(self):
    """测试重新获取验证金额时发生异常的情况"""
    # 准备mock验证更新器（抛出异常）
    mock_verification_updater = Mock()
    mock_verification_updater.verify_single_record = AsyncMock(
        side_effect=Exception("Network error")
    )
    
    matcher = TradePairMatcher(mock_verification_updater)
    
    trade_records = [
        create_mock_trade_record(
            signal_id="signal_004",
            trade_type=TradeType.BUY,
            token_in_amount=100.0,
            status=TradeStatus.SUCCESS,
            verification_status="verified"
        ),
        create_mock_trade_record(
            signal_id="signal_004",
            trade_type=TradeType.SELL,
            token_out_verified_amount=None,  # 验证金额为空
            status=TradeStatus.SUCCESS,
            verification_status="verified",
            tx_hash="test_tx_hash",
            token_out_address="test_token_address",
            wallet_address="test_wallet_address"
        )
    ]
    
    # 执行测试
    trade_pairs = await matcher.match_trade_pairs(trade_records)
    
    # 验证结果 - 应该跳过该交易对
    self.assertEqual(len(trade_pairs), 0)
    
    # 验证调用了重新获取方法
    mock_verification_updater.verify_single_record.assert_called_once()
```

## 4. 命令行接口测试

### 4.1 CLI参数测试
```python
class TestCLI:
    def test_html_format_with_output(self, capsys):
        """测试HTML格式带输出路径"""
        with patch('sys.argv', ['cli.py', '--format', 'html', '--output', 'test.html']):
            with patch('asyncio.run') as mock_run:
                from utils.trading.statistics.cli import main
                # 测试参数解析
                pass
    
    def test_json_format_without_output(self, capsys):
        """测试JSON格式不需要输出路径"""
        with patch('sys.argv', ['cli.py', '--format', 'json']):
            with patch('asyncio.run') as mock_run:
                from utils.trading.statistics.cli import main
                # 测试参数解析
                pass
    
    def test_html_format_without_output_error(self, capsys):
        """测试HTML格式缺少输出路径的错误"""
        with patch('sys.argv', ['cli.py', '--format', 'html']):
            with pytest.raises(SystemExit):
                from utils.trading.statistics.cli import main
                # 应该抛出参数错误
                pass
```

## 5. 异常处理测试

### 5.1 数据异常测试
```python
class TestExceptionHandling:
    async def test_empty_dataset_handling(self):
        """测试空数据集处理"""
        analyzer = TradeStatisticsAnalyzer()
        
        with patch.object(analyzer.trade_pair_matcher, 'match_trade_pairs') as mock_match:
            mock_match.return_value = []  # 空数据集
            
            config = StatisticsConfig()
            result = await analyzer.generate_statistics(config)
            
            assert result.overall_stats.total_trades == 0
            assert len(result.token_stats) == 0
            assert len(result.strategy_stats) == 0
    
    async def test_database_connection_error(self):
        """测试数据库连接错误"""
        analyzer = TradeStatisticsAnalyzer()
        
        with patch.object(TradeRecord, 'find') as mock_find:
            mock_find.side_effect = Exception("数据库连接失败")
            
            config = StatisticsConfig()
            
            with pytest.raises(Exception, match="数据库连接失败"):
                await analyzer.generate_statistics(config)
    
    def test_invalid_date_range(self):
        """测试无效日期范围"""
        config = StatisticsConfig(
            start_date=datetime(2024, 12, 31),
            end_date=datetime(2024, 1, 1)  # 结束日期早于开始日期
        )
        
        # 应该有适当的验证逻辑
        pass
```

## 6. 数据验证测试

### 6.1 计算准确性测试
```python
class TestCalculationAccuracy:
    def test_profit_rate_calculation_precision(self):
        """测试盈利率计算精度"""
        buy_amount = 1.0
        sell_amount = 1.333333
        expected_profit_rate = 33.3333
        
        profit_rate = (sell_amount - buy_amount) / buy_amount * 100
        
        # 验证精度
        assert abs(profit_rate - expected_profit_rate) < 0.0001
    
    def test_win_rate_calculation(self):
        """测试胜率计算"""
        profitable_trades = 7
        total_trades = 10
        expected_win_rate = 70.0
        
        win_rate = (profitable_trades / total_trades) * 100
        
        assert win_rate == expected_win_rate
    
    def test_max_profit_loss_calculation(self):
        """测试最大盈利亏损计算"""
        profit_amounts = [0.5, -0.2, 1.5, -0.8, 0.3]
        
        max_profit = max(profit_amounts)
        max_loss = min(profit_amounts)
        
        assert max_profit == 1.5
        assert max_loss == -0.8
```

## 7. 测试数据准备

### 7.1 测试数据工厂
```python
class TestDataFactory:
    @staticmethod
    def create_trade_record(
        signal_id: str,
        trade_type: TradeType,
        status: TradeStatus = TradeStatus.SUCCESS,
        verification_status: VerificationStatus = VerificationStatus.VERIFIED,
        **kwargs
    ) -> TradeRecord:
        """创建测试交易记录"""
        defaults = {
            "strategy_name": "test_strategy",
            "token_out_address": "test_token",
            "created_at": datetime.now()
        }
        defaults.update(kwargs)
        
        return TradeRecord(
            signal_id=signal_id,
            trade_type=trade_type,
            status=status,
            verification_status=verification_status,
            **defaults
        )
    
    @staticmethod
    def create_trade_pair(
        signal_id: str,
        profit_amount: float,
        **kwargs
    ) -> TradePair:
        """创建测试交易对"""
        defaults = {
            "strategy_name": "test_strategy",
            "token_address": "test_token",
            "buy_record_id": "buy_001",
            "sell_record_id": "sell_001",
            "buy_amount_sol": 1.0,
            "sell_amount_sol": 1.0 + profit_amount,
            "profit_rate": profit_amount * 100,
            "is_profitable": profit_amount > 0,
            "buy_time": datetime.now(),
            "sell_time": datetime.now(),
            "holding_duration": 1.0
        }
        defaults.update(kwargs)
        
        return TradePair(
            signal_id=signal_id,
            profit_amount=profit_amount,
            **defaults
        )
```

## 8. 测试配置

### 8.1 pytest配置
```python
# conftest.py
import pytest
import asyncio
from datetime import datetime
from unittest.mock import Mock, patch

@pytest.fixture
def event_loop():
    """创建事件循环"""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
def mock_dao():
    """模拟DAO对象"""
    return Mock()

@pytest.fixture
def sample_stats_result():
    """样本统计结果"""
    return StatisticsResult(
        overall_stats=OverallStats(),
        token_stats=[],
        strategy_stats=[],
        trade_pairs=[],
        profit_rankings=[],
        loss_rankings=[],
        generation_time=datetime.now(),
        data_range={}
    )
```

## 9. 测试执行计划

### 9.1 测试优先级
1. **高优先级**：核心计算逻辑、数据筛选、交易配对
2. **中优先级**：报告生成、多格式输出、排行榜功能
3. **低优先级**：性能测试、边界条件、异常处理

### 9.2 测试覆盖率目标
- 核心业务逻辑：95%以上
- 数据模型：90%以上
- 报告生成：85%以上
- 整体覆盖率：90%以上

### 9.3 测试环境
- 单元测试：使用模拟数据
- 集成测试：使用测试数据库
- 性能测试：使用大量模拟数据

这个测试用例设计全面覆盖了更新后的功能需求，确保基于链上验证金额的统计计算、多格式输出、排行榜功能等新特性的正确性和可靠性。 