# 滑点递增重试功能需求规格

**创建日期**: 2025-05-26  
**版本**: 0.1.1  
**基于**: AutoTradeManager v0.1.0  
**目标**: 实现滑点递增重试机制，提高交易上链成功率

## 1. 背景与问题

### 1.1 现状问题
- **滑点固定导致失败**：当前重试机制使用固定的滑点配置，在网络拥堵或价格波动大时容易因滑点不足而无法上链
- **重试效果有限**：每次重试都使用相同参数，无法应对不断变化的市场条件
- **上链成功率低**：在高波动期间，固定滑点导致多次重试仍然失败

### 1.2 核心需求
1. **滑点递增重试机制**：每次重试时逐步增加滑点，提高交易成功概率
2. **可配置控制**：提供开关和参数配置，允许灵活控制功能行为
3. **多层级配置覆盖**：策略配置 > 渠道配置 > 全局配置的优先级体系
4. **向后兼容**：不影响现有功能，新功能作为可选增强

## 2. 功能需求

### 2.1 滑点递增配置

#### 2.1.1 功能开关
- **配置项**: `enable_slippage_retry`
- **类型**: bool
- **默认值**: false
- **说明**: 是否启用滑点递增重试功能

#### 2.1.2 滑点增加步长
- **配置项**: `slippage_increment_percentage`
- **类型**: float
- **默认值**: 0.5
- **说明**: 每次重试时滑点增加的百分比（例如：0.5表示每次增加0.5%）

#### 2.1.3 最大滑点限制
- **配置项**: `max_slippage_percentage`
- **类型**: float
- **默认值**: 10.0
- **说明**: 滑点的最大值，超过此值不再重试

#### 2.1.4 独立买卖配置
- **买入滑点配置**:
  - `enable_buy_slippage_retry`: 买入滑点递增开关
  - `buy_slippage_increment_percentage`: 买入滑点增加步长
  - `max_buy_slippage_percentage`: 买入最大滑点
- **卖出滑点配置**:
  - `enable_sell_slippage_retry`: 卖出滑点递增开关  
  - `sell_slippage_increment_percentage`: 卖出滑点增加步长
  - `max_sell_slippage_percentage`: 卖出最大滑点

#### 2.1.5 重试间隔配置
**考虑到meme币交易瞬息万变的特性，重试间隔配置至关重要**

- **基础重试间隔配置**:
  - `retry_delay_seconds`: 基础重试间隔（秒）
  - **类型**: float
  - **默认值**: 0.5 (500毫秒，适合快速变化的meme币市场)
  - **说明**: 每次重试前的等待时间

- **间隔策略配置**:
  - `retry_delay_strategy`: 重试间隔策略
  - **类型**: enum ["fixed", "linear", "exponential"]
  - **默认值**: "fixed"
  - **说明**: 
    - "fixed": 固定间隔
    - "linear": 线性递增 (delay * retry_count)
    - "exponential": 指数退避 (delay * (2^retry_count))

- **间隔上限配置**:
  - `max_retry_delay_seconds`: 最大重试间隔
  - **类型**: float
  - **默认值**: 5.0
  - **说明**: 防止间隔过长错失交易机会

- **错误类型差异化间隔**:
  - `slippage_error_delay_seconds`: 滑点错误专用间隔
  - **类型**: Optional[float]
  - **默认值**: None (使用基础间隔)
  - **说明**: 滑点错误可能需要更短间隔以快速适应价格变化

- **独立买卖间隔配置**:
  - `buy_retry_delay_seconds`: 买入重试间隔
  - `sell_retry_delay_seconds`: 卖出重试间隔
  - **类型**: Optional[float]
  - **默认值**: None (使用通用配置)
  - **说明**: 买卖操作可能需要不同的重试节奏

### 2.2 配置层级与覆盖规则

#### 2.2.1 配置层级结构
```
运行时交易配置覆盖 (execute_trade接口传入) - 预留最高优先级
    ↓ 覆盖所有交易参数
策略级别配置 (SingleKolStrategyConfig)
    ↓ 覆盖
渠道级别配置 (TradeChannelConfig.trading_params)
    ↓ 覆盖  
全局级别配置 (AutoTradeConfig.global_trading_params)
```

#### 2.2.2 参数合并逻辑
- 每个层级只需配置要覆盖的参数
- 未配置的参数继承上级配置
- **运行时交易配置覆盖**具有最高优先级，可覆盖任何交易相关参数
- 支持滑点、重试、优先费、金额、超时等所有交易参数的运行时调整
- 最终执行时使用合并后的完整参数集

#### 2.2.3 扩展接口预留
**设计原则**: 在执行层面预留运行时交易配置覆盖接口，支持后续模块扩展

**扩展方向预期**:
- **市场分析模块**: 根据波动率动态调整滑点、优先费、重试策略
- **网络监控模块**: 根据拥堵情况优化重试间隔、超时时间
- **风险控制模块**: 在异常情况下调整交易金额、滑点上限
- **流动性监控模块**: 根据深度调整滑点容忍度和交易金额
- **价格预测模块**: 根据预期走势调整重试激进程度
- **成本优化模块**: 动态平衡滑点容忍度和优先费设置

### 2.3 重试执行逻辑

#### 2.3.1 重试触发条件
重试机制和滑点递增机制是两个独立的机制：

**重试继续条件**（决定是否继续尝试）：
- 交易失败
- 未达到最大重试次数（`max_retries`）
- 未超过超时时间

**滑点递增条件**（决定是否增加滑点）：
- 错误信息包含滑点相关关键词：
  - "slippage"
  - "price impact"  
  - "insufficient output amount"
  - "would result in"
- 启用了滑点递增重试功能（`enable_slippage_retry=True`）
- 当前滑点未达到最大限制（`max_slippage_percentage`）

**重要**：即使滑点达到上限无法继续增加，也会继续重试直到达到重试次数上限

#### 2.3.2 重试执行流程
1. **检查重试继续条件**：验证是否达到重试次数上限
2. **检查滑点递增条件**：
   - 验证是否为滑点相关失败
   - 检查滑点递增功能是否启用
   - 检查是否达到滑点上限
3. **滑点调整（可选）**：
   - 如果满足滑点递增条件：current_slippage + increment_percentage
   - 如果不满足：保持当前滑点值不变
4. **更新交易参数**：使用调整后的滑点值（可能不变）
5. **记录重试信息**：在ChannelAttemptResult中记录重试原因和滑点调整信息
6. **执行重试**：使用当前参数重新发起交易
7. **重复流程**：直到成功或达到重试次数上限

### 2.4 监控与记录

#### 2.4.1 重试记录增强
- **滑点值记录**：每次尝试的具体滑点值
- **重试原因**：记录是否为滑点递增重试
- **参数变化**：记录每次重试的参数调整

#### 2.4.2 统计信息
- **滑点重试次数**：成功的滑点递增重试统计
- **平均滑点值**：成功交易的平均滑点统计
- **滑点效果分析**：不同滑点值的成功率统计

## 3. 技术需求

### 3.1 配置模型扩展
- **扩展TradingParams**：添加滑点递增相关配置字段
- **策略配置增强**：在SingleKolStrategyConfig中添加可选的滑点重试覆盖字段
- **配置验证**：确保滑点参数的合理性（例如increment > 0, max > min等）

### 3.2 重试逻辑增强
- **接口层错误识别**：各交易接口实现自己的滑点错误识别逻辑（因为不同API错误格式差异很大）
- **参数计算**：实现滑点递增计算逻辑
- **状态跟踪**：跟踪每次重试的滑点参数变化

### 3.3 交易接口扩展
- **抽象接口增强**：在TradeInterface中添加滑点错误识别抽象方法
- **实现类责任**：每个交易接口实现类负责识别自己API的滑点相关错误
- **格式适配**：各实现类最了解自己的错误响应格式，能提供最准确的判断

### 3.4 接口兼容性
- **向后兼容**：现有接口和配置不受影响
- **渐进启用**：可以按策略或渠道逐步启用新功能
- **平滑切换**：支持运行时动态开启/关闭功能

## 4. 接口设计

### 4.1 扩展接口设计

#### 4.1.1 运行时交易配置覆盖接口
```python
# AutoTradeManager执行接口预留扩展参数
async def execute_trade(
    self,
    trade_type: TradeType,
    token_in_address: str,
    token_out_address: str,
    amount: Optional[float] = None,
    wallet_private_key_env_var: Optional[str] = None,
    wallet_address: Optional[str] = None,
    strategy_trading_overrides: Optional[Dict[str, Any]] = None,
    runtime_trading_overrides: Optional[Dict[str, Any]] = None,  # 预留运行时交易配置覆盖
    signal_id: Optional[PydanticObjectId] = None,
    strategy_name: Optional[str] = None
) -> TradeExecutionResult:
    """
    执行交易的主入口 - 预留运行时交易配置覆盖
    
    Args:
        runtime_trading_overrides: 运行时交易配置覆盖，具有最高优先级
                                  支持外部模块动态调整任何交易相关参数：
                                  - 滑点配置 (buy_slippage_percentage, sell_slippage_percentage)
                                  - 重试配置 (retry_delay_seconds, max_retries, enable_slippage_retry)
                                  - 优先费配置 (buy_priority_fee_sol, sell_priority_fee_sol)
                                  - 交易金额 (buy_amount_sol)
                                  - 超时配置 (timeout_seconds)
                                  - 其他所有TradingParams支持的字段
                                  具体格式和字段后续根据需要扩展
    """
```

#### 4.1.2 参数合并器扩展接口
```python
class ParameterMerger:
    """参数合并器 - 支持多层级配置和运行时交易配置覆盖"""
    
    @staticmethod
    def merge_with_runtime_trading_overrides(
        base_params: TradingParams,
        runtime_trading_overrides: Optional[Dict[str, Any]] = None
    ) -> TradingParams:
        """
        支持运行时交易配置覆盖的参数合并
        
        Args:
            base_params: 经过多层级合并的基础交易参数
            runtime_trading_overrides: 运行时交易配置覆盖（最高优先级）
                                      支持覆盖所有交易相关参数：
                                      - 滑点、重试、优先费、金额、超时等
        """
        # 预留实现，支持后续扩展
        pass
    
    @staticmethod
    def extract_trading_overrides(
        runtime_overrides: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        从运行时覆盖配置中提取交易相关参数
        """
        # 预留实现，支持后续扩展
        pass
```

### 4.2 配置接口扩展

#### 4.1.1 TradingParams 扩展
```python
from enum import Enum
from typing import Optional

class RetryDelayStrategy(str, Enum):
    """重试间隔策略枚举"""
    FIXED = "fixed"          # 固定间隔
    LINEAR = "linear"        # 线性递增
    EXPONENTIAL = "exponential"  # 指数退避

class TradingParams(BaseModel):
    """交易参数配置 - 渠道级别的默认参数"""
    # 基础交易参数
    default_buy_amount_sol: float = Field(default=0.01, description="默认每笔买入交易花费的SOL数量")
    default_buy_slippage_percentage: float = Field(default=1.0, description="默认买入滑点百分比")
    default_buy_priority_fee_sol: float = Field(default=0.00005, description="默认买入优先费（SOL）")
    default_sell_slippage_percentage: float = Field(default=1.0, description="默认卖出滑点百分比")
    default_sell_priority_fee_sol: float = Field(default=0.00005, description="默认卖出优先费（SOL）")
    
    # 滑点递增重试配置 - 直接集成
    enable_slippage_retry: bool = Field(default=False, description="是否启用滑点递增重试")
    slippage_increment_percentage: float = Field(default=0.5, description="滑点增加步长（百分比）")
    max_slippage_percentage: float = Field(default=10.0, description="最大滑点限制（百分比）")
    
    # 重试间隔配置 - 新增
    retry_delay_seconds: float = Field(default=0.5, description="基础重试间隔（秒）")
    retry_delay_strategy: RetryDelayStrategy = Field(default=RetryDelayStrategy.FIXED, description="重试间隔策略")
    max_retry_delay_seconds: float = Field(default=5.0, description="最大重试间隔（秒）")
    slippage_error_delay_seconds: Optional[float] = Field(default=None, description="滑点错误专用间隔（秒）")
    
    # 买卖独立滑点重试配置
    enable_buy_slippage_retry: Optional[bool] = Field(default=None, description="买入滑点递增开关（None时使用通用开关）")
    buy_slippage_increment_percentage: Optional[float] = Field(default=None, description="买入滑点增加步长")
    max_buy_slippage_percentage: Optional[float] = Field(default=None, description="买入最大滑点")
    buy_retry_delay_seconds: Optional[float] = Field(default=None, description="买入重试间隔（秒）")
    
    enable_sell_slippage_retry: Optional[bool] = Field(default=None, description="卖出滑点递增开关")
    sell_slippage_increment_percentage: Optional[float] = Field(default=None, description="卖出滑点增加步长")
    max_sell_slippage_percentage: Optional[float] = Field(default=None, description="卖出最大滑点")
    sell_retry_delay_seconds: Optional[float] = Field(default=None, description="卖出重试间隔（秒）")
    
    @validator('slippage_increment_percentage', 'buy_slippage_increment_percentage', 'sell_slippage_increment_percentage')
    def validate_increment(cls, v):
        if v is not None and v <= 0:
            raise ValueError("滑点增加步长必须大于0")
        return v
    
    @validator('max_slippage_percentage', 'max_buy_slippage_percentage', 'max_sell_slippage_percentage')
    def validate_max_slippage(cls, v):
        if v is not None and (v <= 0 or v > 50):
            raise ValueError("最大滑点必须在0-50%之间")
        return v
    
    @validator('retry_delay_seconds', 'max_retry_delay_seconds', 'slippage_error_delay_seconds', 'buy_retry_delay_seconds', 'sell_retry_delay_seconds')
    def validate_delay_times(cls, v):
        if v is not None and v < 0:
            raise ValueError("重试间隔时间不能为负数")
        if v is not None and v > 60:
            raise ValueError("重试间隔时间不应超过60秒")
        return v
    
    def get_effective_slippage_config(self, trade_type: str) -> dict:
        """获取指定交易类型的有效滑点重试配置"""
        if trade_type.lower() == 'buy':
            return {
                'enabled': self.enable_buy_slippage_retry if self.enable_buy_slippage_retry is not None else self.enable_slippage_retry,
                'increment': self.buy_slippage_increment_percentage if self.buy_slippage_increment_percentage is not None else self.slippage_increment_percentage,
                'max_slippage': self.max_buy_slippage_percentage if self.max_buy_slippage_percentage is not None else self.max_slippage_percentage,
                'retry_delay': self.buy_retry_delay_seconds if self.buy_retry_delay_seconds is not None else self.retry_delay_seconds
            }
        else:  # sell
            return {
                'enabled': self.enable_sell_slippage_retry if self.enable_sell_slippage_retry is not None else self.enable_slippage_retry,
                'increment': self.sell_slippage_increment_percentage if self.sell_slippage_increment_percentage is not None else self.slippage_increment_percentage,
                'max_slippage': self.max_sell_slippage_percentage if self.max_sell_slippage_percentage is not None else self.max_slippage_percentage,
                'retry_delay': self.sell_retry_delay_seconds if self.sell_retry_delay_seconds is not None else self.retry_delay_seconds
            }
    
    def calculate_retry_delay(self, retry_count: int, is_slippage_error: bool = False, trade_type: str = "buy") -> float:
        """
        计算重试间隔时间
        
        Args:
            retry_count: 当前重试次数（从1开始）
            is_slippage_error: 是否为滑点相关错误
            trade_type: 交易类型 (buy/sell)
            
        Returns:
            float: 重试间隔时间（秒）
        """
        # 确定基础间隔
        if is_slippage_error and self.slippage_error_delay_seconds is not None:
            base_delay = self.slippage_error_delay_seconds
        else:
            effective_config = self.get_effective_slippage_config(trade_type)
            base_delay = effective_config['retry_delay']
        
        # 根据策略计算间隔
        if self.retry_delay_strategy == RetryDelayStrategy.FIXED:
            calculated_delay = base_delay
        elif self.retry_delay_strategy == RetryDelayStrategy.LINEAR:
            calculated_delay = base_delay * retry_count
        elif self.retry_delay_strategy == RetryDelayStrategy.EXPONENTIAL:
            calculated_delay = base_delay * (2 ** (retry_count - 1))
        else:
            calculated_delay = base_delay
        
        # 应用最大间隔限制
        return min(calculated_delay, self.max_retry_delay_seconds)
```

#### 4.1.2 策略配置扩展
```python
# SingleKolStrategyConfig 中新增滑点重试覆盖字段
class SingleKolStrategyConfig(BaseModel):
    # ... 现有字段 ...
    
    # 滑点递增重试覆盖配置（可选，直接字段覆盖）
    strategy_enable_slippage_retry: Optional[bool] = Field(default=None, description="策略级别滑点递增开关覆盖")
    strategy_slippage_increment_percentage: Optional[float] = Field(default=None, description="策略级别滑点增加步长覆盖")
    strategy_max_slippage_percentage: Optional[float] = Field(default=None, description="策略级别最大滑点覆盖")
    
    # 买卖独立覆盖配置
    strategy_enable_buy_slippage_retry: Optional[bool] = Field(default=None, description="策略级别买入滑点递增开关覆盖")
    strategy_buy_slippage_increment_percentage: Optional[float] = Field(default=None, description="策略级别买入滑点增加步长覆盖")
    strategy_max_buy_slippage_percentage: Optional[float] = Field(default=None, description="策略级别买入最大滑点覆盖")
    
    strategy_enable_sell_slippage_retry: Optional[bool] = Field(default=None, description="策略级别卖出滑点递增开关覆盖")
    strategy_sell_slippage_increment_percentage: Optional[float] = Field(default=None, description="策略级别卖出滑点增加步长覆盖")
    strategy_max_sell_slippage_percentage: Optional[float] = Field(default=None, description="策略级别卖出最大滑点覆盖")
    
    # 重试间隔覆盖配置
    strategy_retry_delay_seconds: Optional[float] = Field(default=None, description="策略级别重试间隔覆盖")
    strategy_retry_delay_strategy: Optional[str] = Field(default=None, description="策略级别重试间隔策略覆盖")
    strategy_max_retry_delay_seconds: Optional[float] = Field(default=None, description="策略级别最大重试间隔覆盖")
    strategy_slippage_error_delay_seconds: Optional[float] = Field(default=None, description="策略级别滑点错误间隔覆盖")
    strategy_buy_retry_delay_seconds: Optional[float] = Field(default=None, description="策略级别买入重试间隔覆盖")
    strategy_sell_retry_delay_seconds: Optional[float] = Field(default=None, description="策略级别卖出重试间隔覆盖")
```

### 4.2 执行接口增强

#### 4.2.1 重试上下文
```python
@dataclass
class RetryContext:
    """重试上下文，跟踪滑点调整"""
    original_buy_slippage: float
    original_sell_slippage: float
    current_buy_slippage: float
    current_sell_slippage: float
    retry_count: int
    is_slippage_retry: bool = False
    slippage_adjustments: List[Dict[str, Any]] = field(default_factory=list)
```

#### 4.2.2 增强的ChannelAttemptResult
```python
class ChannelAttemptResult(BaseModel):
    """单个渠道的尝试结果"""
    channel_type: str = Field(..., description="渠道类型")
    attempt_number: int = Field(..., description="尝试次数")
    status: TradeStatus = Field(..., description="尝试状态")
    tx_hash: Optional[str] = Field(None, description="交易哈希")
    error_message: Optional[str] = Field(None, description="错误信息")
    execution_time: float = Field(..., description="执行时间（秒）")
    started_at: datetime = Field(..., description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    
    # 新增实际交易数量字段
    actual_amount_in: Optional[float] = Field(None, description="实际输入数量")
    actual_amount_out: Optional[float] = Field(None, description="实际输出数量")
    
    # 新增：滑点重试相关字段
    initial_buy_slippage: Optional[float] = Field(None, description="初始买入滑点(%)")
    final_buy_slippage: Optional[float] = Field(None, description="最终使用的买入滑点(%)")
    initial_sell_slippage: Optional[float] = Field(None, description="初始卖出滑点(%)")
    final_sell_slippage: Optional[float] = Field(None, description="最终使用的卖出滑点(%)")
    
    slippage_retry_enabled: bool = Field(default=False, description="是否启用了滑点递增重试")
    is_slippage_retry_attempt: bool = Field(default=False, description="是否为滑点递增重试尝试")
    slippage_adjustment_reason: Optional[str] = Field(None, description="滑点调整原因")
    
    # 滑点调整历史（简化版，用于快速查看）
    slippage_adjustments_count: int = Field(default=0, description="滑点调整次数")
    total_slippage_increase: Optional[float] = Field(None, description="累计滑点增加(%)")
    total_slippage_retries: int = Field(default=0, description="总滑点重试次数")
    
    # 兼容性字段 - 用于测试和向后兼容
    slippage_adjustments: List = Field(default_factory=list, description="滑点调整历史记录列表")
    
    @property
    def final_slippage(self) -> Optional[float]:
        """获取最终滑点值 - 兼容性属性"""
        if self.final_buy_slippage is not None:
            return self.final_buy_slippage
        elif self.final_sell_slippage is not None:
            return self.final_sell_slippage
        return None
```

## 5. 边界条件与异常处理

### 5.1 配置验证
- **参数合理性**：increment > 0, max_slippage > default_slippage
- **类型检查**：确保所有百分比值为正数
- **范围检查**：最大滑点不超过合理范围（如50%）

### 5.2 重试限制
- **重试次数限制**：结合现有的max_retries限制
- **时间限制**：结合现有的超时机制
- **滑点上限**：严格遵守max_slippage限制

### 5.3 错误识别
- **滑点相关错误**：准确识别需要滑点调整的失败场景
- **非滑点错误**：对于余额不足、代币不存在等错误，不进行滑点重试
- **错误日志**：详细记录重试决策过程

## 6. 测试需求

### 6.1 单元测试
- **配置解析测试**：测试多层级配置合并逻辑
- **滑点计算测试**：测试递增计算的准确性
- **错误识别测试**：测试滑点相关错误的正确识别

### 6.2 集成测试
- **端到端重试测试**：模拟滑点失败和自动重试
- **多层级配置测试**：验证策略覆盖渠道覆盖全局的优先级
- **边界条件测试**：测试达到最大滑点时的行为

### 6.3 性能测试
- **重试延迟测试**：确保滑点计算不影响重试速度
- **内存使用测试**：验证重试上下文的内存开销
- **并发安全测试**：确保多个交易的滑点调整互不影响

## 7. 兼容性考虑

### 7.1 向后兼容
- **默认禁用**：新功能默认关闭，不影响现有行为
- **配置可选**：所有新配置字段都有合理的默认值
- **接口稳定**：现有接口签名保持不变

### 7.2 渐进迁移
- **分阶段启用**：可以按策略逐步启用新功能
- **回滚机制**：支持快速禁用新功能回到原有行为
- **配置验证**：提供工具验证新配置的正确性

## 8. 扩展接口使用预期

### 8.1 基础扩展接口使用
```python
# 后续外部模块可通过运行时交易配置覆盖接口进行动态调整
# 支持所有交易相关参数的动态覆盖

# 示例1：市场分析模块综合调整
market_override = {
    # 滑点配置调整
    "buy_slippage_percentage": 2.5,      # 高波动期间提高滑点容忍度
    "sell_slippage_percentage": 3.0,
    # 重试配置调整
    "retry_delay_seconds": 0.3,          # 快速重试
    "enable_slippage_retry": True,
    "slippage_increment_percentage": 0.8,
    # 优先费调整
    "buy_priority_fee_sol": 0.0002,      # 提高优先费确保上链
    # 元信息
    "source": "market_analyzer",
    "reason": "高波动市场综合优化"
}

# 示例2：风险控制模块保守调整  
risk_override = {
    # 降低交易金额
    "buy_amount_sol": 0.005,             # 减少单笔金额
    # 保守滑点设置
    "max_slippage_percentage": 5.0,      # 降低滑点上限
    # 增加超时保护
    "timeout_seconds": 20,               # 缩短超时时间
    "source": "risk_controller",
    "reason": "异常市场条件保守交易"
}

result = await auto_trade_manager.execute_trade(
    trade_type=TradeType.BUY,
    token_in_address="So11111111111111111111111111111111111111112",
    token_out_address="target_token_address",
    runtime_trading_overrides=market_override,  # 运行时交易配置覆盖
    signal_id=signal_id,
    strategy_name="meme_hunter"
)
```

### 8.2 预期扩展方向
```python
# 网络监控模块扩展示例
class NetworkMonitor:
    def get_trading_overrides(self) -> Optional[Dict[str, Any]]:
        """根据网络状况生成交易配置覆盖"""
        # 可能调整：重试间隔、超时时间、优先费等
        # 具体实现待后续设计
        pass

# 流动性分析模块扩展示例
class LiquidityAnalyzer:
    def get_adaptive_trading_config(self) -> Optional[Dict[str, Any]]:
        """根据流动性情况生成自适应交易配置"""
        # 可能调整：滑点容忍度、交易金额分割、重试策略等
        # 具体实现待后续设计
        pass

# 成本优化模块扩展示例
class CostOptimizer:
    def get_optimized_config(self) -> Optional[Dict[str, Any]]:
        """生成成本优化的交易配置"""
        # 可能调整：优先费动态定价、滑点成本平衡等
        # 具体实现待后续设计
        pass

# 价格预测模块扩展示例
class PricePredictor:
    def get_predictive_config(self) -> Optional[Dict[str, Any]]:
        """基于价格预测生成交易配置"""
        # 可能调整：重试激进程度、滑点预期、时机优化等
        # 具体实现待后续设计
        pass
```

## 9. 成功标准

### 9.1 功能标准
- 滑点递增重试能显著提高交易成功率（目标提升20%以上）
- 配置覆盖逻辑正确执行，运行时 > 策略 > 渠道 > 全局优先级有效
- 运行时交易配置覆盖接口预留到位，支持所有交易参数的动态调整
- 所有重试过程和配置覆盖都有完整的记录和追踪

### 9.2 性能标准
- 滑点调整计算耗时 < 10ms
- 参数合并逻辑耗时 < 5ms
- 重试延迟增加 < 5%
- 内存使用增加 < 5MB

### 9.3 稳定性标准
- 新功能不影响现有交易稳定性
- 扩展接口兼容性良好，易于后续开发
- 边界条件处理正确，无异常情况
- 所有测试用例100%通过 