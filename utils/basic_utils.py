from typing import Any
from datetime import datetime
from zoneinfo import ZoneInfo

# Final proposed version, simple and robust for empty or None:
def convert_str_to_float(value: Any) -> float:
    """Converts a value to a float. Returns 0.0 for empty strings or None or unconvertible strings."""
    if value is None or str(value).strip() == '':
        return 0.0
    try:
        return float(value)
    except (ValueError, TypeError):
        # Log a warning here if unexpected types are common but unconvertible
        # logger.warning(f"Could not convert value '{value}' of type {type(value)} to float, returning 0.0")
        return 0.0

def get_current_time_dt() -> datetime:
    """获取当前时间, 强制为东八区时间
    
    Returns:
        datetime: 当前时间
    """
    EASTERN_ZONE = ZoneInfo("Asia/Shanghai")
    return datetime.now(EASTERN_ZONE) 