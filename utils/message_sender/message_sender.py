from abc import ABC, abstractmethod
from enum import Enum
import logging
import os

import aiohttp


class MessageChannel(Enum):
    """消息频道"""
    TELEGRAM = "telegram"
    FEISHU = "feishu"


class MessageSender(ABC):
    """消息发送器"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    async def send_message_to_user(self, message: str, user_id: str, parse_mode: str = "HTML") -> bool:
        pass
    
    @abstractmethod
    async def send_message_to_channel(self, message: str, channel_id: str, parse_mode: str = "HTML") -> bool:
        pass
    
    
class TelegramMessageSender(MessageSender):
    """Telegram消息发送器"""
    
    def __init__(self):
        self.token = os.getenv("TELEGRAM_BOT_TOKEN")
        # print(f"DEBUG: TelegramMessageSender loaded token: '{self.token}'") # Debug line commented out
        if not self.token:
            raise ValueError("TELEGRAM_BOT_TOKEN is not set")
        super().__init__()
        
    async def _send_message(self, message: str, channel_id: str, parse_mode: str = "HTML") -> bool:
        """发送消息
        
        Args:
            message: 消息内容
            channel_id: 频道ID
            parse_mode: 解析模式 (HTML, MarkdownV2)
            
        Returns:
            bool: 是否发送成功
        """
        # print(f"DEBUG: Attempting to send to channel_id: '{channel_id}', parse_mode: '{parse_mode}'") # Debug line commented out
        request_url = f"https://api.telegram.org/botTOKEN_HIDDEN/sendMessage" # Log URL with token hidden
        self.logger.info(f"Preparing to send Telegram message. URL (token hidden): {request_url}, Chat ID: {channel_id}, Parse Mode: {parse_mode}")
        self.logger.debug(f"Message content: {message[:200]}...") # Log first 200 chars of message
        
        async with aiohttp.ClientSession() as session:
            url = f"https://api.telegram.org/bot{self.token}/sendMessage"
            payload = {
                "chat_id": channel_id,
                "text": message,
                "parse_mode": parse_mode
            }
            self.logger.debug(f"Payload for Telegram: {payload}")
            
            try:
                async with session.post(url, json=payload) as response:
                    response_text = await response.text()
                    self.logger.info(f"Telegram API response for chat_id {channel_id}. Status: {response.status}, Response text: {response_text}")
                    if response.status != 200:
                        self.logger.error(f"Failed to send message to {channel_id}: {response.status} {response_text}")
                        return False
                    self.logger.info(f"Message sent to {channel_id} successfully")
                    return True
            except aiohttp.ClientError as e:
                self.logger.error(f"AIOHTTP ClientError while sending message to {channel_id}: {e}", exc_info=True)
                return False
            except Exception as e:
                self.logger.error(f"Unexpected error while sending message to {channel_id}: {e}", exc_info=True)
                return False
        
    async def send_message_to_user(self, message: str, user_id: str, parse_mode: str = "HTML") -> bool:
        """
        发送消息给用户
        
        Args:
            message: 消息内容
            user_id: 用户ID
            parse_mode: 解析模式
            
        Returns: 是否发送成功
        """
        return await self._send_message(message, user_id, parse_mode)
    
    async def send_message_to_channel(self, message: str, channel_id: str, parse_mode: str = "HTML") -> bool:
        """
        发送消息给频道
        
        Args:
            message: 消息内容
            channel_id: 频道ID
            parse_mode: 解析模式
        """
        return await self._send_message(message, channel_id, parse_mode)
