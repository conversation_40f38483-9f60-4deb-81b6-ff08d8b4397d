# 交易统计分析功能技术实现方案

> ⚠️ **重要错误标记** ⚠️  
> **本文档中的交易对匹配逻辑存在重大错误！**  
> **错误位置：第2.2.1节 "交易配对匹配器" 中的按signal_id分组匹配逻辑**  
> **修复方案请参考：[BUGFIX_PLAN_TradePairMatcher_SignalIdMatchingError_20250529.md](./fixes/BUGFIX_PLAN_TradePairMatcher_SignalIdMatchingError_20250529.md)**  
> **发现日期：2025-05-29

## 1. 架构设计

### 1.1 整体架构
```
交易统计系统
├── 数据层 (Data Layer)
│   ├── TradeRecordDAO - 交易记录数据访问
│   └── MongoDB - 数据存储
├── 业务逻辑层 (Business Logic Layer)
│   ├── TradeStatisticsAnalyzer - 核心统计分析器
│   ├── TradePairMatcher - 交易配对匹配器
│   ├── StatisticsCalculator - 统计计算器
│   └── DataValidator - 数据验证器
├── 可视化层 (Visualization Layer)
│   ├── ChartGenerator - 图表生成器
│   ├── TableGenerator - 表格生成器
│   ├── HTMLReportGenerator - HTML报告生成器
│   └── JSONReportGenerator - JSON报告生成器
└── 接口层 (Interface Layer)
    ├── CLI - 命令行接口
    └── API - 编程接口
```

### 1.2 模块依赖关系
- CLI/API → TradeStatisticsAnalyzer
- TradeStatisticsAnalyzer → TradePairMatcher, StatisticsCalculator, HTMLReportGenerator, JSONReportGenerator
- TradePairMatcher → TradeRecordDAO, DataValidator
- StatisticsCalculator → 统计计算器
- HTMLReportGenerator → ChartGenerator, TableGenerator
- JSONReportGenerator → 数据序列化

## 2. 核心模块设计

### 2.1 数据模型设计

#### 2.1.1 统计结果模型
```python
# utils/trading/statistics/models.py

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

class ReportFormat(str, Enum):
    HTML = "html"
    JSON = "json"

class TradePair(BaseModel):
    """交易对数据模型"""
    signal_id: str
    strategy_name: str
    token_address: str
    buy_record_id: str
    sell_record_id: str
    buy_amount_sol: float                # 买入金额（SOL）
    sell_amount_sol: float               # 卖出金额（SOL）
    profit_rate: float                   # 盈利率(%)
    profit_amount: float                 # 盈亏金额（SOL）
    is_profitable: bool                  # 是否盈利
    buy_time: datetime
    sell_time: datetime
    holding_duration: float              # 持仓时长（小时）

class OverallStats(BaseModel):
    """总体统计模型"""
    total_trades: int = 0
    total_profit_rate: float = 0.0
    total_win_rate: float = 0.0
    avg_profit_rate: float = 0.0         # 平均盈利率
    total_profit_amount: float = 0.0
    profitable_trades: int = 0
    loss_trades: int = 0
    avg_holding_duration: float = 0.0
    total_buy_amount: float = 0.0
    total_sell_amount: float = 0.0
    max_single_profit: float = 0.0       # 最大单笔盈利
    max_single_loss: float = 0.0         # 最大单笔亏损

class TokenStats(BaseModel):
    """Token统计模型"""
    token_address: str
    token_symbol: Optional[str] = None
    trade_count: int = 0
    win_rate: float = 0.0
    avg_profit_rate: float = 0.0
    total_profit_amount: float = 0.0
    profitable_trades: int = 0
    loss_trades: int = 0
    total_buy_amount: float = 0.0
    total_sell_amount: float = 0.0

class StrategyStats(BaseModel):
    """策略统计模型"""
    strategy_name: str
    trade_count: int = 0
    win_rate: float = 0.0
    avg_profit_rate: float = 0.0
    total_profit_amount: float = 0.0
    profitable_trades: int = 0
    loss_trades: int = 0
    total_buy_amount: float = 0.0
    total_sell_amount: float = 0.0
    max_single_profit: float = 0.0       # 最大单笔盈利
    max_single_loss: float = 0.0         # 最大单笔亏损

class ProfitRanking(BaseModel):
    """盈利排行"""
    signal_id: str
    strategy_name: str
    token_address: str
    profit_amount: float
    profit_rate: float
    buy_time: datetime
    sell_time: datetime

class LossRanking(BaseModel):
    """亏损排行"""
    signal_id: str
    strategy_name: str
    token_address: str
    loss_amount: float
    loss_rate: float
    buy_time: datetime
    sell_time: datetime

class StatisticsResult(BaseModel):
    """完整统计结果模型"""
    overall_stats: OverallStats
    token_stats: List[TokenStats]
    strategy_stats: List[StrategyStats]
    trade_pairs: List[TradePair]
    profit_rankings: List[ProfitRanking]  # 盈利排行
    loss_rankings: List[LossRanking]      # 亏损排行
    generation_time: datetime
    data_range: Dict[str, Any]
```

#### 2.1.2 配置模型
```python
class StatisticsConfig(BaseModel):
    """统计配置模型"""
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    strategy_filter: Optional[List[str]] = None
    token_filter: Optional[List[str]] = None
    min_trade_amount: Optional[float] = None
    output_path: Optional[str] = None
    report_format: ReportFormat = ReportFormat.HTML
    include_charts: bool = True
    include_tables: bool = True
    chart_style: str = "plotly"  # plotly, matplotlib
```

### 2.2 核心模块设计

#### 2.2.1 交易配对匹配器（更新）
```python
# utils/trading/statistics/trade_pair_matcher.py

class TradePairMatcher:
    """交易配对匹配器"""
    
    def __init__(self, verification_updater: TradeRecordVerificationUpdater):
        self.verification_updater = verification_updater
        self.logger = logging.getLogger(__name__)
    
    async def match_trade_pairs(
        self, 
        config: StatisticsConfig
    ) -> List[TradePair]:
        """
        匹配买入卖出交易对
        
        ⚠️ **错误算法** ⚠️ 以下逻辑存在重大错误！
        算法：
        1. 获取所有成功且已验证的交易记录
        2. 按signal_id分组 ← **错误：买入和卖出记录有不同的signal_id**
        3. 在每组中匹配买入和卖出记录 ← **错误：应该通过buy_signal_ref_id关联**
        4. 基于链上验证金额计算盈利率
        
        正确的修复方案请参考：fixes/BUGFIX_PLAN_TradePairMatcher_SignalIdMatchingError_20250529.md
        """
        # 获取交易记录
        trade_records = await self._get_verified_trade_records(config)
        
        # 按signal_id分组
        signal_groups = self._group_by_signal_id(trade_records)
        
        # 匹配交易对
        trade_pairs = []
        for signal_id, records in signal_groups.items():
            pairs = self._match_pairs_in_group(signal_id, records)
            trade_pairs.extend(pairs)
        
        return trade_pairs
    
    async def _get_verified_trade_records(self, config: StatisticsConfig) -> List[TradeRecord]:
        """获取已验证的交易记录"""
        query = {
            "status": TradeStatus.SUCCESS,
            "verification_status": VerificationStatus.VERIFIED  # 新增验证状态过滤
        }
        
        # 时间范围过滤
        if config.start_date or config.end_date:
            time_filter = {}
            if config.start_date:
                time_filter["$gte"] = config.start_date
            if config.end_date:
                time_filter["$lte"] = config.end_date
            query["created_at"] = time_filter
        
        # 策略过滤
        if config.strategy_filter:
            query["strategy_name"] = {"$in": config.strategy_filter}
        
        return await TradeRecord.find(query).to_list()
    
    def _group_by_signal_id(self, records: List[TradeRecord]) -> Dict[str, List[TradeRecord]]:
        """按signal_id分组 ⚠️ **错误方法** - 买入卖出记录有不同signal_id，不应该这样分组"""
        groups = {}
        for record in records:
            signal_id = str(record.signal_id)
            if signal_id not in groups:
                groups[signal_id] = []
            groups[signal_id].append(record)
        return groups
    
    def _match_pairs_in_group(self, signal_id: str, records: List[TradeRecord]) -> List[TradePair]:
        """在同一signal_id组内匹配买入卖出对 ⚠️ **错误方法** - 应该通过buy_signal_ref_id关联"""
        buy_records = [r for r in records if r.trade_type == TradeType.BUY]
        sell_records = [r for r in records if r.trade_type == TradeType.SELL]
        
        pairs = []
        for buy_record in buy_records:
            # 找到对应的卖出记录（时间最近的）
            matching_sell = self._find_matching_sell(buy_record, sell_records)
            if matching_sell:
                pair = self._create_trade_pair(buy_record, matching_sell)
                pairs.append(pair)
                sell_records.remove(matching_sell)  # 避免重复匹配
        
        return pairs
    
    def _find_matching_sell(self, buy_record: TradeRecord, sell_records: List[TradeRecord]) -> Optional[TradeRecord]:
        """找到匹配的卖出记录"""
        # 按时间排序，找到买入后最近的卖出
        valid_sells = [
            sell for sell in sell_records 
            if sell.created_at > buy_record.created_at
        ]
        
        if not valid_sells:
            return None
        
        # 返回时间最近的卖出记录
        return min(valid_sells, key=lambda x: x.created_at)
    
    def _create_trade_pair(self, buy_record: TradeRecord, sell_record: TradeRecord) -> Optional[TradePair]:
        """创建交易对（更新盈利计算逻辑）"""
        # 获取实际交易金额
        buy_amount_sol = buy_record.token_in_amount or 0
        sell_amount_sol = await self._validate_and_get_verified_amount(sell_record)
        
        # 如果卖出验证金额为空，跳过该交易对
        if sell_amount_sol is None:
            self.logger.warning(f"跳过交易对 {buy_record.signal_id}: 缺少验证的卖出金额")
            return None
        
        # 计算盈利率和盈亏金额
        profit_amount = sell_amount_sol - buy_amount_sol
        profit_rate = (profit_amount / buy_amount_sol * 100) if buy_amount_sol > 0 else 0
        
        # 计算持仓时长
        holding_duration = (sell_record.created_at - buy_record.created_at).total_seconds() / 3600
        
        return TradePair(
            signal_id=str(buy_record.signal_id),
            strategy_name=buy_record.strategy_name,
            token_address=buy_record.token_out_address,  # 买入的token
            buy_record_id=str(buy_record.id),
            sell_record_id=str(sell_record.id),
            buy_amount_sol=buy_amount_sol,
            sell_amount_sol=sell_amount_sol,
            profit_rate=profit_rate,
            profit_amount=profit_amount,
            is_profitable=profit_amount > 0,
            buy_time=buy_record.created_at,
            sell_time=sell_record.created_at,
            holding_duration=holding_duration
        )
    
    async def _validate_and_get_verified_amount(self, trade_record: TradeRecord) -> Optional[float]:
        """验证并获取验证金额，如果为空则重新获取"""
        if trade_record.token_out_verified_amount is not None:
            return trade_record.token_out_verified_amount
        
        # 如果verification_status为verified但token_out_verified_amount为空，表示数据异常
        if trade_record.verification_status == "verified":
            self.logger.warning(f"交易记录 {trade_record.id} 状态为verified但token_out_verified_amount为空，尝试重新获取")
            
            # 调用验证更新器重新获取
            try:
                verification_result = await self.verification_updater.verify_single_record(
                    tx_hash=trade_record.tx_hash,
                    token_out_address=trade_record.token_out_address,
                    wallet_address=trade_record.wallet_address
                )
                
                if verification_result.get("verified_amount") is not None:
                    self.logger.info(f"成功重新获取交易记录 {trade_record.id} 的验证金额: {verification_result['verified_amount']}")
                    return verification_result["verified_amount"]
                else:
                    self.logger.warning(f"重新获取交易记录 {trade_record.id} 的验证金额失败")
                    return None
                    
            except Exception as e:
                self.logger.error(f"重新获取交易记录 {trade_record.id} 验证金额时发生异常: {str(e)}")
                return None
        
        return None
```

#### 2.2.2 统计计算器（更新）
```python
# utils/trading/statistics/statistics_calculator.py

class StatisticsCalculator:
    """统计计算器"""
    
    def calculate_overall_stats(self, trade_pairs: List[TradePair]) -> OverallStats:
        """计算总体统计（增加平均盈利率）"""
        if not trade_pairs:
            return OverallStats()
        
        total_trades = len(trade_pairs)
        profitable_trades = sum(1 for pair in trade_pairs if pair.is_profitable)
        loss_trades = total_trades - profitable_trades
        
        total_profit_amount = sum(pair.profit_amount for pair in trade_pairs)
        total_buy_amount = sum(pair.buy_amount_sol for pair in trade_pairs)
        total_sell_amount = sum(pair.sell_amount_sol for pair in trade_pairs)
        
        total_profit_rate = (total_profit_amount / total_buy_amount * 100) if total_buy_amount > 0 else 0
        total_win_rate = (profitable_trades / total_trades * 100) if total_trades > 0 else 0
        
        # 计算平均盈利率
        avg_profit_rate = sum(pair.profit_rate for pair in trade_pairs) / total_trades if total_trades > 0 else 0
        
        avg_holding_duration = sum(pair.holding_duration for pair in trade_pairs) / total_trades
        
        # 计算最大盈利和最大亏损
        profit_amounts = [pair.profit_amount for pair in trade_pairs]
        max_single_profit = max(profit_amounts) if profit_amounts else 0
        max_single_loss = min(profit_amounts) if profit_amounts else 0
        
        return OverallStats(
            total_trades=total_trades,
            total_profit_rate=total_profit_rate,
            total_win_rate=total_win_rate,
            avg_profit_rate=avg_profit_rate,
            total_profit_amount=total_profit_amount,
            profitable_trades=profitable_trades,
            loss_trades=loss_trades,
            avg_holding_duration=avg_holding_duration,
            total_buy_amount=total_buy_amount,
            total_sell_amount=total_sell_amount,
            max_single_profit=max_single_profit,
            max_single_loss=max_single_loss
        )
    
    def calculate_token_stats(self, trade_pairs: List[TradePair]) -> List[TokenStats]:
        """计算分Token统计"""
        token_groups = {}
        
        # 按token分组
        for pair in trade_pairs:
            token = pair.token_address
            if token not in token_groups:
                token_groups[token] = []
            token_groups[token].append(pair)
        
        # 计算每个token的统计
        token_stats = []
        for token_address, pairs in token_groups.items():
            stats = self._calculate_group_stats(pairs)
            token_stats.append(TokenStats(
                token_address=token_address,
                **stats
            ))
        
        # 按盈利率排序
        token_stats.sort(key=lambda x: x.avg_profit_rate, reverse=True)
        return token_stats
    
    def calculate_strategy_stats(self, trade_pairs: List[TradePair]) -> List[StrategyStats]:
        """计算分策略统计"""
        strategy_groups = {}
        
        # 按策略分组
        for pair in trade_pairs:
            strategy = pair.strategy_name
            if strategy not in strategy_groups:
                strategy_groups[strategy] = []
            strategy_groups[strategy].append(pair)
        
        # 计算每个策略的统计
        strategy_stats = []
        for strategy_name, pairs in strategy_groups.items():
            stats = self._calculate_group_stats(pairs)
            strategy_stats.append(StrategyStats(
                strategy_name=strategy_name,
                **stats
            ))
        
        # 按胜率排序
        strategy_stats.sort(key=lambda x: x.win_rate, reverse=True)
        return strategy_stats
    
    def _calculate_group_stats(self, pairs: List[TradePair]) -> Dict[str, Any]:
        """计算分组统计的通用方法"""
        if not pairs:
            return {
                "trade_count": 0,
                "win_rate": 0.0,
                "avg_profit_rate": 0.0,
                "total_profit_amount": 0.0,
                "profitable_trades": 0,
                "loss_trades": 0,
                "total_buy_amount": 0.0,
                "total_sell_amount": 0.0
            }
        
        trade_count = len(pairs)
        profitable_trades = sum(1 for pair in pairs if pair.is_profitable)
        loss_trades = trade_count - profitable_trades
        
        total_profit_amount = sum(pair.profit_amount for pair in pairs)
        total_buy_amount = sum(pair.buy_amount_sol for pair in pairs)
        total_sell_amount = sum(pair.sell_amount_sol for pair in pairs)
        
        win_rate = (profitable_trades / trade_count * 100) if trade_count > 0 else 0
        avg_profit_rate = sum(pair.profit_rate for pair in pairs) / trade_count if trade_count > 0 else 0
        
        return {
            "trade_count": trade_count,
            "win_rate": win_rate,
            "avg_profit_rate": avg_profit_rate,
            "total_profit_amount": total_profit_amount,
            "profitable_trades": profitable_trades,
            "loss_trades": loss_trades,
            "total_buy_amount": total_buy_amount,
            "total_sell_amount": total_sell_amount
        }
    
    def calculate_profit_rankings(self, trade_pairs: List[TradePair], limit: int = 50) -> List[ProfitRanking]:
        """计算盈利排行（按盈利金额从大到小）"""
        profitable_pairs = [pair for pair in trade_pairs if pair.is_profitable]
        profitable_pairs.sort(key=lambda x: x.profit_amount, reverse=True)
        
        rankings = []
        for pair in profitable_pairs[:limit]:
            rankings.append(ProfitRanking(
                signal_id=pair.signal_id,
                strategy_name=pair.strategy_name,
                token_address=pair.token_address,
                profit_amount=pair.profit_amount,
                profit_rate=pair.profit_rate,
                buy_time=pair.buy_time,
                sell_time=pair.sell_time
            ))
        
        return rankings
    
    def calculate_loss_rankings(self, trade_pairs: List[TradePair], limit: int = 50) -> List[LossRanking]:
        """计算亏损排行（按亏损金额从接近0到最亏）"""
        loss_pairs = [pair for pair in trade_pairs if not pair.is_profitable]
        loss_pairs.sort(key=lambda x: x.profit_amount, reverse=True)  # 从接近0到最负
        
        rankings = []
        for pair in loss_pairs[:limit]:
            rankings.append(LossRanking(
                signal_id=pair.signal_id,
                strategy_name=pair.strategy_name,
                token_address=pair.token_address,
                loss_amount=abs(pair.profit_amount),  # 转为正数显示
                loss_rate=abs(pair.profit_rate),      # 转为正数显示
                buy_time=pair.buy_time,
                sell_time=pair.sell_time
            ))
        
        return rankings
```

### 2.3 报告生成器

#### 2.3.1 JSON报告生成器（新增）
```python
# utils/trading/statistics/json_report_generator.py

import json
from datetime import datetime
from typing import Dict, Any

class JSONReportGenerator:
    """JSON报告生成器"""
    
    def generate_json_report(self, stats_result: StatisticsResult) -> Dict[str, Any]:
        """生成JSON格式的统计报告"""
        
        # 转换为可序列化的字典
        json_data = {
            "overall_stats": stats_result.overall_stats.dict(),
            "token_stats": [token.dict() for token in stats_result.token_stats],
            "strategy_stats": [strategy.dict() for strategy in stats_result.strategy_stats],
            "profit_rankings": [ranking.dict() for ranking in stats_result.profit_rankings],
            "loss_rankings": [ranking.dict() for ranking in stats_result.loss_rankings],
            "generation_time": stats_result.generation_time.isoformat(),
            "data_range": stats_result.data_range,
            "summary": {
                "total_trade_pairs": len(stats_result.trade_pairs),
                "total_tokens": len(stats_result.token_stats),
                "total_strategies": len(stats_result.strategy_stats)
            }
        }
        
        return json_data
    
    def serialize_to_json_string(self, stats_result: StatisticsResult) -> str:
        """序列化为JSON字符串"""
        json_data = self.generate_json_report(stats_result)
        return json.dumps(json_data, ensure_ascii=False, indent=2)
```

#### 2.3.2 HTML报告生成器（更新）
```python
# utils/trading/statistics/html_report_generator.py

from jinja2 import Template
from datetime import datetime
import os

class HTMLReportGenerator:
    """HTML报告生成器（更新模板以包含新表格）"""
    
    def __init__(self):
        self.chart_generator = ChartGenerator()
        self.template = self._get_html_template()
    
    async def generate_report(
        self, 
        stats_result: StatisticsResult, 
        output_path: str
    ) -> str:
        """生成完整的HTML报告"""
        
        # 生成图表
        charts = {
            'overall_profit_pie': self.chart_generator.generate_overall_profit_pie_chart(stats_result.overall_stats),
            'strategy_comparison': self.chart_generator.generate_strategy_comparison_chart(stats_result.strategy_stats),
            'token_performance': self.chart_generator.generate_token_performance_chart(stats_result.token_stats),
            'profit_trend': self.chart_generator.generate_profit_trend_chart(stats_result.trade_pairs)
        }
        
        # 渲染HTML
        html_content = self.template.render(
            stats=stats_result,
            charts=charts,
            generation_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # 写入文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return output_path
    
    def _get_html_template(self) -> Template:
        """获取更新的HTML模板"""
        template_str = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交易统计报告</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        h2 { color: #555; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .stat-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; }
        .stat-value { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .stat-label { font-size: 0.9em; opacity: 0.9; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; font-weight: bold; }
        .positive { color: #28a745; }
        .negative { color: #dc3545; }
        .chart-container { margin: 30px 0; }
        .footer { text-align: center; margin-top: 40px; color: #666; font-size: 0.9em; }
        .ranking-section { margin: 40px 0; }
        .ranking-table { max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>交易统计分析报告</h1>
        
        <!-- 总体统计 -->
        <h2>总体统计</h2>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">{{ stats.overall_stats.total_trades }}</div>
                <div class="stat-label">总交易数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ "%.2f"|format(stats.overall_stats.total_profit_rate) }}%</div>
                <div class="stat-label">总盈利率</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ "%.2f"|format(stats.overall_stats.total_win_rate) }}%</div>
                <div class="stat-label">总胜率</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ "%.2f"|format(stats.overall_stats.avg_profit_rate) }}%</div>
                <div class="stat-label">平均盈利率</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ "%.4f"|format(stats.overall_stats.total_profit_amount) }}</div>
                <div class="stat-label">总盈亏金额(SOL)</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ "%.4f"|format(stats.overall_stats.max_single_profit) }}</div>
                <div class="stat-label">最大单笔盈利(SOL)</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ "%.4f"|format(stats.overall_stats.max_single_loss) }}</div>
                <div class="stat-label">最大单笔亏损(SOL)</div>
            </div>
        </div>
        
        <!-- 图表 -->
        <div class="chart-container">
            {{ charts.overall_profit_pie|safe }}
        </div>
        
        <div class="chart-container">
            {{ charts.strategy_comparison|safe }}
        </div>
        
        <div class="chart-container">
            {{ charts.token_performance|safe }}
        </div>
        
        <div class="chart-container">
            {{ charts.profit_trend|safe }}
        </div>
        
        <!-- 盈利排行表 -->
        <div class="ranking-section">
            <h2>盈利排行榜 (前50名)</h2>
            <div class="ranking-table">
                <table>
                    <thead>
                        <tr>
                            <th>排名</th>
                            <th>信号ID</th>
                            <th>策略</th>
                            <th>Token地址</th>
                            <th>盈利金额(SOL)</th>
                            <th>盈利率(%)</th>
                            <th>买入时间</th>
                            <th>卖出时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for ranking in stats.profit_rankings %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ ranking.signal_id[:8] }}...</td>
                            <td>{{ ranking.strategy_name }}</td>
                            <td>{{ ranking.token_address[:16] }}...</td>
                            <td class="positive">{{ "%.4f"|format(ranking.profit_amount) }}</td>
                            <td class="positive">{{ "%.2f"|format(ranking.profit_rate) }}</td>
                            <td>{{ ranking.buy_time.strftime('%m-%d %H:%M') }}</td>
                            <td>{{ ranking.sell_time.strftime('%m-%d %H:%M') }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- 亏损排行表 -->
        <div class="ranking-section">
            <h2>亏损排行榜 (前50名)</h2>
            <div class="ranking-table">
                <table>
                    <thead>
                        <tr>
                            <th>排名</th>
                            <th>信号ID</th>
                            <th>策略</th>
                            <th>Token地址</th>
                            <th>亏损金额(SOL)</th>
                            <th>亏损率(%)</th>
                            <th>买入时间</th>
                            <th>卖出时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for ranking in stats.loss_rankings %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ ranking.signal_id[:8] }}...</td>
                            <td>{{ ranking.strategy_name }}</td>
                            <td>{{ ranking.token_address[:16] }}...</td>
                            <td class="negative">{{ "%.4f"|format(ranking.loss_amount) }}</td>
                            <td class="negative">{{ "%.2f"|format(ranking.loss_rate) }}</td>
                            <td>{{ ranking.buy_time.strftime('%m-%d %H:%M') }}</td>
                            <td>{{ ranking.sell_time.strftime('%m-%d %H:%M') }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- 策略统计表格（更新） -->
        <h2>策略统计</h2>
        <table>
            <thead>
                <tr>
                    <th>策略名称</th>
                    <th>交易次数</th>
                    <th>胜率(%)</th>
                    <th>平均盈利率(%)</th>
                    <th>总盈亏金额(SOL)</th>
                    <th>最大盈利(SOL)</th>
                    <th>最大亏损(SOL)</th>
                    <th>盈利交易</th>
                    <th>亏损交易</th>
                </tr>
            </thead>
            <tbody>
                {% for strategy in stats.strategy_stats %}
                <tr>
                    <td>{{ strategy.strategy_name }}</td>
                    <td>{{ strategy.trade_count }}</td>
                    <td>{{ "%.2f"|format(strategy.win_rate) }}</td>
                    <td class="{{ 'positive' if strategy.avg_profit_rate > 0 else 'negative' }}">
                        {{ "%.2f"|format(strategy.avg_profit_rate) }}
                    </td>
                    <td class="{{ 'positive' if strategy.total_profit_amount > 0 else 'negative' }}">
                        {{ "%.4f"|format(strategy.total_profit_amount) }}
                    </td>
                    <td class="positive">{{ "%.4f"|format(strategy.max_single_profit) }}</td>
                    <td class="negative">{{ "%.4f"|format(strategy.max_single_loss) }}</td>
                    <td>{{ strategy.profitable_trades }}</td>
                    <td>{{ strategy.loss_trades }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        <!-- Token统计表格（更新） -->
        <h2>Token统计 (前20名)</h2>
        <table>
            <thead>
                <tr>
                    <th>Token地址</th>
                    <th>交易次数</th>
                    <th>胜率(%)</th>
                    <th>平均盈利率(%)</th>
                    <th>总盈亏金额(SOL)</th>
                    <th>盈利交易</th>
                    <th>亏损交易</th>
                </tr>
            </thead>
            <tbody>
                {% for token in stats.token_stats[:20] %}
                <tr>
                    <td>{{ token.token_address[:16] }}...</td>
                    <td>{{ token.trade_count }}</td>
                    <td>{{ "%.2f"|format(token.win_rate) }}</td>
                    <td class="{{ 'positive' if token.avg_profit_rate > 0 else 'negative' }}">
                        {{ "%.2f"|format(token.avg_profit_rate) }}
                    </td>
                    <td class="{{ 'positive' if token.total_profit_amount > 0 else 'negative' }}">
                        {{ "%.4f"|format(token.total_profit_amount) }}
                    </td>
                    <td>{{ token.profitable_trades }}</td>
                    <td>{{ token.loss_trades }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        <div class="footer">
            <p>报告生成时间: {{ generation_time }}</p>
            <p>数据范围: {{ stats.data_range.start_date or '全部' }} 至 {{ stats.data_range.end_date or '全部' }}</p>
            <p>基于链上验证的真实交易金额计算</p>
        </div>
    </div>
</body>
</html>
        """
        return Template(template_str)
```

### 2.4 主要分析器（更新）

#### 2.4.1 交易统计分析器
```python
# utils/trading/statistics/trade_statistics_analyzer.py

class TradeStatisticsAnalyzer:
    """交易统计分析器 - 主要入口类（支持多种输出格式）"""
    
    def __init__(self, 
                 trade_record_dao: TradeRecordDAO,
                 verification_updater: TradeRecordVerificationUpdater):
        self.trade_record_dao = trade_record_dao
        self.verification_updater = verification_updater
        self.pair_matcher = TradePairMatcher(verification_updater)
        self.calculator = StatisticsCalculator()
        self.chart_generator = ChartGenerator()
        self.html_generator = HTMLReportGenerator()
        self.json_generator = JSONReportGenerator()
        self.logger = logging.getLogger(__name__)
    
    async def generate_statistics(
        self, 
        config: StatisticsConfig
    ) -> StatisticsResult:
        """生成完整的统计分析结果"""
        
        self.logger.info(f"开始生成交易统计，配置: {config}")
        
        try:
            # 1. 匹配交易对
            self.logger.info("开始匹配已验证的交易对...")
            trade_pairs = await self.pair_matcher.match_trade_pairs(config)
            self.logger.info(f"匹配到 {len(trade_pairs)} 个已验证交易对")
            
            # 2. 计算统计指标
            self.logger.info("开始计算统计指标...")
            overall_stats = self.calculator.calculate_overall_stats(trade_pairs)
            token_stats = self.calculator.calculate_token_stats(trade_pairs)
            strategy_stats = self.calculator.calculate_strategy_stats(trade_pairs)
            
            # 3. 计算排行榜
            profit_rankings = self.calculator.calculate_profit_rankings(trade_pairs)
            loss_rankings = self.calculator.calculate_loss_rankings(trade_pairs)
            
            # 4. 构建结果
            result = StatisticsResult(
                overall_stats=overall_stats,
                token_stats=token_stats,
                strategy_stats=strategy_stats,
                trade_pairs=trade_pairs,
                profit_rankings=profit_rankings,
                loss_rankings=loss_rankings,
                generation_time=datetime.now(),
                data_range={
                    "start_date": config.start_date.isoformat() if config.start_date else None,
                    "end_date": config.end_date.isoformat() if config.end_date else None,
                    "strategy_filter": config.strategy_filter,
                    "token_filter": config.token_filter
                }
            )
            
            self.logger.info("统计分析完成")
            return result
            
        except Exception as e:
            self.logger.error(f"生成统计分析失败: {e}")
            raise
    
    async def generate_html_report(
        self, 
        stats_result: StatisticsResult, 
        output_path: str
    ) -> str:
        """生成HTML报告"""
        return await self.html_generator.generate_report(stats_result, output_path)
    
    def generate_json_report(self, stats_result: StatisticsResult) -> Dict[str, Any]:
        """生成JSON报告"""
        return self.json_generator.generate_json_report(stats_result)
    
    async def generate_report(
        self, 
        config: StatisticsConfig
    ) -> Union[str, Dict[str, Any]]:
        """根据配置生成指定格式的报告"""
        # 生成统计
        stats_result = await self.generate_statistics(config)
        
        if config.report_format == ReportFormat.HTML:
            if not config.output_path:
                raise ValueError("HTML格式需要指定输出路径")
            report_path = await self.generate_html_report(stats_result, config.output_path)
            self.logger.info(f"HTML报告已生成: {report_path}")
            return report_path
        
        elif config.report_format == ReportFormat.JSON:
            json_data = self.generate_json_report(stats_result)
            self.logger.info("JSON数据已生成")
            return json_data
        
        else:
            raise ValueError(f"不支持的报告格式: {config.report_format}")
```

## 3. 命令行接口实现（更新）

```python
# utils/trading/statistics/cli.py

import asyncio
import argparse
import json
from datetime import datetime
from .trade_statistics_analyzer import TradeStatisticsAnalyzer
from .models import StatisticsConfig, ReportFormat

async def main():
    parser = argparse.ArgumentParser(description='交易统计分析工具')
    parser.add_argument('--format', '-f', choices=['html', 'json'], default='html', help='输出格式')
    parser.add_argument('--output', '-o', help='输出文件路径（仅HTML格式需要）')
    parser.add_argument('--start-date', type=str, help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=str, help='结束日期 (YYYY-MM-DD)')
    parser.add_argument('--strategy', type=str, action='append', help='策略过滤')
    parser.add_argument('--token', type=str, action='append', help='Token过滤')
    
    args = parser.parse_args()
    
    # 验证参数
    if args.format == 'html' and not args.output:
        parser.error("HTML格式需要指定 --output 参数")
    
    # 解析日期
    start_date = datetime.fromisoformat(args.start_date) if args.start_date else None
    end_date = datetime.fromisoformat(args.end_date) if args.end_date else None
    
    # 创建配置
    config = StatisticsConfig(
        start_date=start_date,
        end_date=end_date,
        strategy_filter=args.strategy,
        token_filter=args.token,
        output_path=args.output,
        report_format=ReportFormat(args.format)
    )
    
    # 生成报告
    analyzer = TradeStatisticsAnalyzer()
    result = await analyzer.generate_report(config)
    
    if args.format == 'html':
        print(f"HTML报告已生成: {result}")
    else:
        print(json.dumps(result, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    asyncio.run(main())
```

## 4. 文件结构（更新）

```
utils/trading/statistics/
├── __init__.py                     # 包初始化
├── models.py                       # 数据模型定义（更新）
├── trade_statistics_analyzer.py    # 主要分析器（更新）
├── trade_pair_matcher.py          # 交易配对匹配器（更新）
├── statistics_calculator.py       # 统计计算器（更新）
├── chart_generator.py             # 图表生成器
├── html_report_generator.py       # HTML报告生成器（更新）
├── json_report_generator.py       # JSON报告生成器（新增）
└── cli.py                         # 命令行接口（更新）
```

## 5. 主要变更总结

### 5.1 数据筛选变更
- 增加 `verification_status = "verified"` 过滤条件
- 只处理链上验证过的交易记录

### 5.2 盈利计算变更
- 买入金额：`token_in_amount`
- 卖出金额：`token_out_verified_amount`（链上验证金额）
- 跳过缺少验证金额的交易对

### 5.3 统计指标增强
- 总体统计增加 `avg_profit_rate`（平均盈利率）
- 策略统计保留 `max_single_profit` 和 `max_single_loss`
- Token统计删除最大盈利亏损字段，简化为基础统计
- 新增盈利排行榜和亏损排行榜

### 5.4 多格式支持
- 支持HTML和JSON两种输出格式
- JSON格式直接返回数据，HTML格式写入文件

### 5.5 界面增强
- HTML报告增加盈利亏损排行表
- 总体统计显示平均盈利率
- Token统计表格简化，删除最大盈利亏损列
- 优化数据显示精度（SOL金额显示4位小数）

这个更新的技术方案完全满足了您提出的6个要求，确保了基于链上验证数据的准确计算和丰富的统计展示功能。 

### 6.2 CLI接口

**文件位置**：`utils/trading/trade_statistics_cli.py`

**主要功能**：
- 解析命令行参数
- 初始化分析器
- 执行统计分析
- 输出结果

**使用示例**：
```bash
# 生成HTML报告
python -m utils.trading.trade_statistics_cli --format html --output /path/to/report.html

# 生成JSON数据
python -m utils.trading.trade_statistics_cli --format json --output /path/to/data.json

# 指定时间范围和策略
python -m utils.trading.trade_statistics_cli \
    --format html \
    --output /path/to/report.html \
    --start-date 2024-01-01 \
    --end-date 2024-12-31 \
    --strategies "gmgn_smart_money,manual_trading"
```

**CLI实现**：
```python
async def main():
    args = parse_arguments()
    
    # 初始化依赖
    trade_record_dao = TradeRecordDAO()
    verification_updater = TradeRecordVerificationUpdater()
    
    # 初始化分析器
    analyzer = TradeStatisticsAnalyzer(
        trade_record_dao=trade_record_dao,
        verification_updater=verification_updater
    )
    
    # 构建配置
    config = StatisticsConfig(
        start_date=args.start_date,
        end_date=args.end_date,
        strategies=args.strategies,
        tokens=args.tokens
    )
    
    # 执行分析
    result = await analyzer.analyze_statistics(config)
    
    # 生成报告
    if args.format == ReportFormat.HTML:
        output_path = await analyzer.generate_html_report(result, args.output)
        print(f"HTML报告已生成: {output_path}")
    elif args.format == ReportFormat.JSON:
        json_data = analyzer.generate_json_report(result)
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)
        print(f"JSON数据已生成: {args.output}")
``` 