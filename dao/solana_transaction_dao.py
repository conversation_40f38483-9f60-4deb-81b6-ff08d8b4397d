from typing import List, Optional, Dict, Any
from datetime import datetime
from dao.base_dao import BaseDAO
from models.solana_transaction import SolanaTransaction

class SolanaTransactionDAO(BaseDAO[SolanaTransaction]):
    """Solana 交易 DAO"""
    
    def __init__(self):
        super().__init__(SolanaTransaction)
    
    async def find_by_signature(self, signature: str) -> Optional[SolanaTransaction]:
        """根据交易签名查找交易记录"""
        return await self.find_one({"signature": signature})
    
    async def find_by_address(
        self,
        address: str,
        skip: int = 0,
        limit: int = 100,
        sort_by_time: bool = True
    ) -> List[SolanaTransaction]:
        """查找指定地址的所有交易记录"""
        sort = [("block_time", -1)] if sort_by_time else None
        return await self.find_many({"address": address}, skip=skip, limit=limit, sort=sort)
    
    async def find_latest_transaction(self, address: str) -> Optional[SolanaTransaction]:
        """获取指定地址的最新交易"""
        transactions = await self.find_many(
            {"address": address},
            limit=1,
            sort=[("block_time", -1)]
        )
        return transactions[0] if transactions else None
    
    async def find_transactions_by_type(
        self,
        address: str,
        transaction_type: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[SolanaTransaction]:
        """查找指定地址和类型的交易记录"""
        return await self.find_many(
            {
                "address": address,
                "transaction_type": transaction_type
            },
            skip=skip,
            limit=limit,
            sort=[("block_time", -1)]
        )
    
    async def find_token_transactions(
        self,
        address: str,
        token_mint: str = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[SolanaTransaction]:
        """查找代币交易记录"""
        filter_dict = {
            "address": address,
            "transaction_type": "token_transfer"
        }
        if token_mint:
            filter_dict["token_mint"] = token_mint
        
        return await self.find_many(
            filter_dict,
            skip=skip,
            limit=limit,
            sort=[("block_time", -1)]
        )
    
    async def find_sol_transactions(
        self,
        address: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[SolanaTransaction]:
        """查找 SOL 转账记录"""
        return await self.find_many(
            {
                "address": address,
                "is_sol_transfer": True
            },
            skip=skip,
            limit=limit,
            sort=[("block_time", -1)]
        )
    
    async def batch_save_transactions(
        self,
        transactions: List[SolanaTransaction]
    ) -> int:
        """
        批量保存交易记录
        
        Args:
            transactions: 要保存的交易记录列表
            
        Returns:
            成功保存的记录数量
        """
        # 设置创建和更新时间
        now = datetime.utcnow()
        for tx in transactions:
            tx.created_at = now
            tx.updated_at = now
        
        result = await self.insert_many(transactions)
        return len(transactions) if result else 0
    
    async def update_transaction(
        self,
        signature: str,
        update_data: Dict[str, Any]
    ) -> bool:
        """更新交易记录"""
        update_data["updated_at"] = datetime.utcnow()
        return await self.update_one(
            {"signature": signature},
            update_data
        )
    
    async def delete_by_signature(self, signature: str) -> bool:
        """根据签名删除交易记录"""
        return await self.delete_one({"signature": signature})
    
    async def delete_by_address(self, address: str) -> int:
        """删除指定地址的所有交易记录"""
        return await self.delete_many({"address": address}) 