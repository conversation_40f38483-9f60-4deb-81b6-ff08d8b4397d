# 回测模块V2技术实现方案

**生成时间**: 2025-06-05T10:26:12+08:00  
**版本**: 0.1.0  
**模块**: backtesting  
**文档类型**: 技术实现方案  
**生成方式**: AI生成  

## 总体架构设计

### 架构原则
- **简化优先**: 避免复杂的事件队列和适配器，采用直接数据分析
- **性能优化**: 单次聚合查询 + 向量化计算
- **精确时间**: 使用实际KOL交易时间戳，提升信号精度
- **兼容性**: 保持与现有回测模块的配置和输出兼容

### 模块结构设计
```
utils/backtest_v2/
├── __init__.py                 # 模块初始化
├── backtest_engine.py          # 回测执行引擎
├── data_query.py              # 数据查询组件  
├── signal_analyzer.py         # 信号分析组件
├── sell_strategy.py           # 卖出策略组件
├── result_analyzer.py         # 结果分析组件
├── config_manager.py          # 配置管理组件
└── utils.py                   # 工具函数
```

## 详细技术方案

### 1. 数据查询组件 (data_query.py)

**设计思路**: 
- 封装用户提供的MongoDB聚合管道
- 将硬编码参数替换为配置化参数
- 提供数据验证和错误处理

**核心类设计**:
```python
class DataQuery:
    def __init__(self, config):
        # 从配置初始化数据库连接和参数
        
    async def build_aggregation_pipeline(self, start_time, end_time):
        # 构建动态聚合管道，参数化用户提供的查询
        # 返回完整的聚合管道数组
        
    async def execute_query(self, pipeline):
        # 执行聚合查询并返回结果
        # 包含错误处理和查询性能监控
        
    async def filter_new_token_records(self, token_data_map, token_info_map):
        # 对每个token的交易记录进行新代币时间过滤
        # 逐条检查买入记录是否在token的"新代币"时期内
        # 过滤后统计唯一KOL数量，移除不足以产生信号的token
        # 返回过滤后的token数据
        
    def validate_query_result(self, result):
        # 验证查询结果的数据完整性
        # 检查必需字段、数据类型等
```

**MongoDB买入数据聚合查询**:
```python
async def build_buy_data_aggregation_pipeline(self, start_time, end_time):
    """构建买入数据聚合管道，参数化用户提供的查询"""
    return [
        {
            '$match': {
                'timestamp': {
                    '$gte': start_time,
                    '$lte': end_time
                },
                'event_type': "buy"
            }
        },
        {
            '$project': {
                'cost_usd': {
                    '$toDouble': "$cost_usd"
                },
                'price_usd': {
                    '$toDouble': "$price_usd"
                },
                'token_amount': {
                    '$toDouble': "$token_amount"
                },
                'quote_amount': {
                    '$toDouble': "$quote_amount"
                },
                'allFields': "$$ROOT"
            }
        },
        {
            '$replaceRoot': {
                'newRoot': {
                    '$mergeObjects': [
                        "$allFields",
                        {
                            'cost_usd': "$cost_usd",
                            'price_usd': "$price_usd",
                            'token_amount': "$token_amount",
                            'quote_amount': "$quote_amount"
                        }
                    ]
                }
            }
        },
        {
            '$match': {
                'cost_usd': {
                    '$gt': self.transaction_min_amount
                }
            }
        },
        {
            '$sort': {
                'timestamp': 1
            }
        },
        {
            '$group': {
                '_id': "$token.address",
                'records': {
                    '$push': "$$ROOT"
                },
                'unique_wallets': {
                    '$addToSet': "$wallet"
                }
            }
        },
        {
            '$lookup': {
                'from': "kol_wallets",
                'localField': "unique_wallets",
                'foreignField': "wallet_address",
                'as': "matched_kols"
            }
        },
        {
            '$addFields': {
                'kol_wallets': {
                    '$filter': {
                        'input': "$matched_kols",
                        'as': "wallet",
                        'cond': {
                            '$and': [
                                {
                                    '$in': ["kol", "$$wallet.tags"]
                                },
                                {
                                    '$gte': ["$$wallet.txs", self.kol_account_min_txs]
                                },
                                {
                                    '$lte': ["$$wallet.txs", self.kol_account_max_txs]
                                }
                            ]
                        }
                    }
                }
            }
        },
        {
            '$addFields': {
                'kol_wallets_count': {
                    '$size': "$kol_wallets"
                }
            }
        },
        {
            '$match': {
                'kol_wallets_count': {
                    '$gte': self.kol_account_min_count
                }
            }
        },
        {
            '$addFields': {
                "qualified_kol_wallet_addresses": {
                    '$map': {
                        'input': "$kol_wallets",
                        'as': "kol",
                        'in': "$$kol.wallet_address"
                    }
                }
            }
        },
        {
            '$addFields': {
                "records": {
                    '$filter': {
                        'input': "$records",
                        'as': "record",
                        'cond': {
                            '$in': ["$$record.wallet", "$qualified_kol_wallet_addresses"]
                        }
                    }
                }
            }
        },
        {
            '$project': {
                "matched_kols": 0,
                "unique_wallets": 0,
                "qualified_kol_wallet_addresses": 0
            }
        }
    ]
```

**聚合管道参数化策略**:
- 时间范围: `$gte: start_time, $lte: end_time`
- 交易金额: `cost_usd: {$gt: transaction_min_amount}`  
- KOL交易数: `$gte: kol_account_min_txs, $lte: kol_account_max_txs`
- KOL数量阈值: `kol_wallets_count: {$gte: kol_account_min_count}`

**聚合查询执行流程**:
1. **时间和事件类型过滤**: 匹配指定时间范围内的所有买入交易
2. **数据类型转换**: 将字符串类型的数值字段转换为浮点数，确保后续计算正确
3. **字段合并**: 使用$replaceRoot保持原始字段的同时添加转换后的数值字段
4. **交易金额过滤**: 过滤掉低于最小交易金额阈值的交易
5. **时间排序**: 按交易时间戳升序排列，便于后续时间窗口分析
6. **Token分组**: 按token地址分组，收集每个token的所有交易记录和参与的钱包地址
7. **KOL钱包关联**: 通过$lookup关联kol_wallets集合，获取KOL钱包的详细信息
8. **KOL条件筛选**: 过滤出符合条件的KOL钱包（包含"kol"标签且交易数在指定范围内）
9. **KOL数量统计**: 计算每个token的符合条件的KOL数量
10. **Token预筛选**: 移除KOL数量不足的token，提前优化数据集大小
11. **记录过滤**: 只保留来自符合条件KOL的交易记录
12. **字段清理**: 移除中间计算字段，减少数据传输量

**新代币过滤策略**:
- 聚合查询后，对结果中的token列表查询token信息（包含`first_mint_time`）
- 对每个token的交易记录进行逐条过滤：只保留满足 `交易时间戳 - mint_timestamp <= token_mint_lookback_hours * 3600` 的买入记录
- 过滤后立即统计剩余的唯一KOL账号数量
- 如果唯一KOL数量 < `kol_account_min_count`，直接移除该token（无法产生信号）
- 只保留有足够KOL账号的token进入信号分析阶段

### 2. 信号分析组件 (signal_analyzer.py)

**设计思路**:
- 基于滑动窗口算法检测买入信号
- 买入即卖出过滤，提高信号质量
- 使用pandas进行向量化计算提升性能
- 精确记录信号触发时间戳

**核心算法实现**:
```python
class SignalAnalyzer:
    def __init__(self, config, sell_data_cache):
        self.transaction_lookback_hours = config.get('transaction_lookback_hours', 24)
        self.kol_account_min_count = config.get('kol_account_min_count', 3)
        self.same_token_notification_interval_minutes = config.get('same_token_notification_interval_minutes', 60)
        self.sell_kol_ratio = config.get('sell_kol_ratio', 0.5)
        self.recent_signals = {}  # 记录每个代币的最近信号时间
        self.sell_data_cache = sell_data_cache  # 预加载的卖出数据
        
    def analyze_token_signals(self, token_address, token_data):
        # 对单个token的数据进行信号分析
        # 返回检测到的买入信号列表
        # 支持并发调用，每个token独立分析
        
    def _sliding_window_analysis(self, records):
        # 滑动窗口算法实现
        # 检查每个时间点向前lookback_hours的时间窗口
        # 统计窗口内唯一KOL数量
        # 应用买入即卖出过滤
        
    def _check_buy_sell_immediately(self, candidate_signal, token_address):
        # 买入即卖出判断
        # 检查买入信号时间窗口内的KOL卖出比例
        # 如果达到sell_kol_ratio则跳过信号
        
    def _calculate_signal_timestamp(self, window_records, threshold_index):
        # 计算精确的信号时间戳
        # 返回达到最小KOL数量时的最后一个交易时间
        
    def _check_signal_interval(self, token_address, signal_timestamp):
        # 检查信号间隔限制
        # 如果该token没有历史信号，直接返回True并记录
        # 如果距离上次信号时间 >= same_token_notification_interval_minutes，返回True并更新记录
        # 否则返回False，抑制此次信号
```

**滑动窗口算法细节**:
1. 遍历每个交易记录作为窗口结束点
2. 向前查找lookback_hours时间范围内的所有交易
3. 统计窗口内唯一KOL钱包数量
4. 当数量达到阈值时，生成候选信号
5. **买入即卖出过滤**: 检查候选信号时间窗口内的KOL卖出比例，如果 >= sell_kol_ratio则跳过
6. 检查信号间隔限制：如果距离该token上次信号时间 >= same_token_notification_interval_minutes，则允许产生信号
7. 如果通过所有检查，记录信号并更新该token的最近信号时间
8. 继续遍历，可能在后续时间点产生新的信号（只要满足间隔要求）

### 3. 卖出策略组件 (sell_strategy.py)

**设计思路**:
- 基于KOL卖出行为和时间限制确定最优卖出时机
- 预先获取整个回测期间的卖出数据，提高查询效率
- 支持KOL比例卖出和超时卖出两种策略

**数据获取策略**:
- **预查询卖出数据**: 查询整个回测时间段+`sell_strategy_hours`的KOL卖出记录
- **时间范围**: `[回测开始时间, 回测结束时间 + sell_strategy_hours * 3600]`
- **MongoDB聚合查询**: 使用复杂聚合管道筛选符合条件的KOL卖出记录

**策略实现架构**:
```python
class SellStrategy:
    def __init__(self, config):
        self.sell_strategy_hours = config.get('sell_strategy_hours', 24)
        self.sell_kol_ratio = config.get('sell_kol_ratio', 0.5)
        self.sell_data_cache = {}  # 缓存预查询的卖出数据
        
    async def preload_sell_data(self, backtest_start_time, backtest_end_time):
        # 预先查询整个回测期间的KOL卖出数据
        # 使用MongoDB聚合管道获取符合条件的卖出记录
        
    async def determine_sell_signals(self, buy_signals):
        # 为每个买入信号确定对应的卖出信号
        # 优先使用KOL比例卖出，超时则使用时间卖出
        
    def _timeout_sell_strategy(self, buy_signal):
        # 超时卖出：买入时间 + sell_strategy_hours
        
    def _kol_ratio_sell_strategy(self, buy_signal):
        # KOL比例卖出：基于预加载的卖出数据计算
        # 在买入信号KOL列表中查找卖出行为
        # 返回达到sell_kol_ratio比例时的时间戳
```

**MongoDB卖出数据聚合查询**:
```python
async def build_sell_data_aggregation_pipeline(self, start_time, end_time):
    return [
        {
            '$match': {
                'timestamp': {'$lte': end_time, '$gte': start_time},
                'event_type': 'sell'
            }
        },
        {'$sort': {'timestamp': 1}},
        {
            '$group': {
                '_id': "$token.address",
                'records': {'$push': "$$ROOT"},
                'unique_wallets': {'$addToSet': "$wallet"}
            }
        },
        {
            '$lookup': {
                'from': "kol_wallets",
                'localField': "unique_wallets",
                'foreignField': "wallet_address",
                'as': "matched_kols"
            }
        },
        {
            '$addFields': {
                'kol_wallets': {
                    '$filter': {
                        'input': "$matched_kols",
                        'as': "wallet",
                        'cond': {
                            '$and': [
                                {'$in': ["kol", "$$wallet.tags"]},
                                {'$gte': ["$$wallet.txs", self.kol_account_min_txs]},
                                {'$lte': ["$$wallet.txs", self.kol_account_max_txs]}
                            ]
                        }
                    }
                }
            }
        },
        {
            '$addFields': {
                "qualified_kol_wallet_addresses": {
                    '$map': {
                        'input': "$kol_wallets",
                        'as': "kol",
                        'in': "$$kol.wallet_address"
                    }
                }
            }
        },
        {
            '$addFields': {
                "records": {
                    '$filter': {
                        'input': "$records",
                        'as': "record",
                        'cond': {
                            '$in': ["$$record.wallet", "$qualified_kol_wallet_addresses"]
                        }
                    }
                }
            }
        },
        {
            '$project': {
                "matched_kols": 0,
                "unique_wallets": 0,
                "qualified_kol_wallet_addresses": 0
            }
        }
    ]
```

**KOL比例卖出计算逻辑**:
1. **时间窗口确定**: 从`买入时间 - transaction_lookback_hours`到数据结束时间
2. **KOL卖出检测**: 在买入信号的KOL列表中查找卖出交易记录
3. **比例计算**: 按时间顺序累积计算卖出KOL数量占总KOL数量的比例
4. **卖出时机**: 当比例达到`sell_kol_ratio`阈值时，返回该时间戳
5. **超时处理**: 如果持仓时间超过`sell_strategy_hours`，使用超时卖出

### 4. 回测执行引擎 (backtest_engine.py)

**设计思路**:
- 统筹整个回测流程
- 集成各个组件，处理组件间的数据流转
- 提供进度监控和错误处理

**主要流程控制**:
```python
import asyncio
from concurrent.futures import ThreadPoolExecutor

class BacktestEngine:
    def __init__(self, config):
        # 初始化所有组件
        self.config = config
        self.data_query = DataQuery(config)
        self.sell_strategy = SellStrategy(config)
        # signal_analyzer需要在预加载卖出数据后初始化
        
    async def run_backtest(self):
        # 主要回测流程（9步骤）
        logger.info("开始执行回测模块V2流程")
        
        # 1. 预加载卖出数据（扩展时间范围）
        logger.info("步骤1: 预加载卖出数据")
        extended_end_time = self.config['backtest_end_time'] + self.config.get('sell_strategy_hours', 24) * 3600
        await self.sell_strategy.preload_sell_data(self.config['backtest_start_time'], extended_end_time)
        
        # 2. 初始化信号分析器（需要卖出数据缓存）
        logger.info("步骤2: 初始化信号分析器")
        self.signal_analyzer = SignalAnalyzer(self.config, self.sell_strategy.sell_data_cache)
        self.result_analyzer = ResultAnalyzer(self.config)
        
        # 3. 获取KOL买入记录（MongoDB聚合查询）
        logger.info("步骤3: 执行买入数据聚合查询")
        buy_pipeline = self.data_query.build_buy_data_aggregation_pipeline(
            self.config['backtest_start_time'], 
            self.config['backtest_end_time']
        )
        raw_token_data = await self.data_query.execute_aggregation_query(buy_pipeline)
        logger.info(f"买入数据查询完成，获得 {len(raw_token_data)} 个token")
        
        # 4. 获取token信息（mint时间等）
        logger.info("步骤4: 获取token基础信息")
        token_addresses = list(raw_token_data.keys())
        token_info_map = await self.data_query.get_token_info(token_addresses)
        
        # 5. 新代币记录过滤 + KOL数量预筛选
        logger.info("步骤5: 新代币记录过滤和KOL数量预筛选")
        filtered_token_data = await self.data_query.filter_new_token_records(raw_token_data, token_info_map)
        logger.info(f"过滤后剩余 {len(filtered_token_data)} 个token进入信号分析")
        
        # 6. 信号检测（包含买入即卖出过滤）- 并发处理
        logger.info("步骤6: 并发执行信号检测")
        buy_signals = await self._analyze_signals_concurrent(filtered_token_data)
        logger.info(f"信号检测完成，生成 {len(buy_signals)} 个买入信号")
        
        # 7. 卖出策略（基于预加载的卖出数据）
        logger.info("步骤7: 执行卖出策略")
        sell_signals = await self.sell_strategy.determine_sell_signals(buy_signals)
        
        # 8. 收益计算
        logger.info("步骤8: 计算交易收益")
        trades = self._calculate_trades(buy_signals, sell_signals)
        
        # 9. 结果分析
        logger.info("步骤9: 生成结果分析")
        results = self.result_analyzer.analyze(trades)
        
        logger.info("回测流程执行完成")
        return results
    
    async def _analyze_signals_concurrent(self, filtered_token_data, max_concurrent_tokens=10):
        """并发分析多个token的信号，提升性能"""
        token_items = list(filtered_token_data.items())
        
        # 创建信号量控制并发数量
        semaphore = asyncio.Semaphore(max_concurrent_tokens)
        
        async def analyze_single_token(token_address, token_data):
            async with semaphore:
                logger.debug(f"分析token {token_address} 的信号")
                return self.signal_analyzer.analyze_token_signals(token_address, token_data)
        
        # 并发执行所有token的信号分析
        tasks = [
            analyze_single_token(token_address, token_data) 
            for token_address, token_data in token_items
        ]
        
        # 等待所有任务完成并收集结果
        token_signals_list = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 合并所有token的信号结果
        all_buy_signals = []
        for i, signals in enumerate(token_signals_list):
            if isinstance(signals, Exception):
                token_address = token_items[i][0]
                logger.error(f"Token {token_address} 信号分析失败: {signals}")
                continue
            if signals:  # 如果该token有信号
                all_buy_signals.extend(signals)
        
        return all_buy_signals
        
    def _calculate_trades(self, buy_signals, sell_signals):
        # 根据买入卖出信号计算具体交易
        # 包含价格计算、手续费、滑点等
        logger.info(f"计算 {len(buy_signals)} 个买入信号对应的交易")
        trades = []
        
        for buy_signal in buy_signals:
            # 查找对应的卖出信号
            sell_signal = next(
                (s for s in sell_signals if s['token_address'] == buy_signal['token_address'] 
                 and s['buy_signal_timestamp'] == buy_signal['signal_timestamp']), 
                None
            )
            
            if sell_signal:
                trade = {
                    'token_address': buy_signal['token_address'],
                    'buy_timestamp': buy_signal['signal_timestamp'],
                    'sell_timestamp': sell_signal['sell_timestamp'],
                    'buy_price': buy_signal.get('avg_price_usd', 0),
                    'sell_price': sell_signal.get('avg_price_usd', 0),
                    'quantity': buy_signal.get('total_volume_usd', 0),
                    'buy_reason': 'kol_signal',
                    'sell_reason': sell_signal.get('sell_reason', 'unknown')
                }
                
                # 计算收益率（考虑手续费和滑点）
                commission = self.config.get('commission_pct', 0.003)
                slippage = self.config.get('slippage_pct', 0.002)
                
                effective_buy_price = trade['buy_price'] * (1 + commission + slippage)
                effective_sell_price = trade['sell_price'] * (1 - commission - slippage)
                
                trade['return_rate'] = (effective_sell_price - effective_buy_price) / effective_buy_price
                trade['profit_usd'] = trade['quantity'] * trade['return_rate']
                
                trades.append(trade)
            else:
                logger.warning(f"买入信号 {buy_signal['token_address']}:{buy_signal['signal_timestamp']} 未找到对应卖出信号")
        
        return trades
```

**流程优化特性**:
1. **买入数据明确载入**: 步骤3明确执行买入数据的MongoDB聚合查询
2. **并发信号分析**: 使用asyncio和信号量控制，对多个token并发执行信号检测
3. **性能监控**: 每个步骤都有详细的日志输出和进度监控
4. **错误处理**: 并发执行中包含异常处理，单个token失败不影响整体流程
5. **内存优化**: 通过并发控制避免同时处理过多token导致内存问题

### 5. 结果分析组件 (result_analyzer.py)

**设计思路**:
- 保持与现有回测模块的输出兼容性
- 提供丰富的统计指标和可视化
- 支持结果导出和报告生成

**功能模块**:
```python
class ResultAnalyzer:
    def calculate_performance_metrics(self, trades):
        # 计算回测统计指标
        # 胜率、总收益、最大回撤、夏普比率等
        
    def generate_equity_curve(self, trades):
        # 生成资金曲线数据
        
    def export_results(self, results, output_dir):
        # 导出结果到JSON、CSV等格式
        
    def create_visualizations(self, results, output_dir):
        # 生成可视化图表（PNG格式）
```

### 6. 配置管理组件 (config_manager.py)

**设计思路**:
- 统一管理所有配置参数
- 支持多种配置来源（YAML、JSON、命令行）
- 提供参数验证和默认值处理

**配置类设计**:
```python
class ConfigManager:
    def load_config(self, config_path=None, **kwargs):
        # 加载配置，支持文件和命令行参数
        
    def validate_config(self, config):
        # 验证配置参数的有效性
        
    def get_default_config(self):
        # 返回默认配置参数
```

## 性能优化策略

### 1. 数据库查询优化
- 使用单一聚合管道查询，减少数据库往返次数
- 确保查询字段上有合适的数据库索引
- 合理设置聚合管道的内存限制

### 2. 算法性能优化
- 使用pandas DataFrame进行向量化计算
- 滑动窗口算法采用高效的时间复杂度实现
- 避免嵌套循环，尽可能使用numpy/pandas内置函数

### 3. 内存管理
- 分批处理大量token数据，避免内存溢出
- 及时释放不再使用的大对象
- 使用生成器模式处理大量数据

### 4. 并发处理
- 支持多个token的信号分析并发执行
- 异步处理数据库查询和I/O操作
- 合理控制并发数量，避免资源竞争

## 关键文件实现计划

### 1. `data_query.py` 实现要点
- MongoDB聚合管道的参数化封装
- 异步数据库操作使用motor客户端
- 查询结果的数据类型转换和验证
- 详细的查询性能日志记录

### 2. `signal_analyzer.py` 实现要点
- 高效的滑动窗口算法，时间复杂度O(n)
- 使用pandas的时间窗口功能进行优化
- 精确的信号时间戳计算逻辑
- 支持多个token的并行分析

### 3. `sell_strategy.py` 实现要点
- 异步查询卖出交易数据
- KOL比例计算的精确性
- 时间戳精度的保持
- 策略结果的缓存优化

### 4. `backtest_engine.py` 实现要点
- 完整的错误处理和回滚机制
- 详细的执行进度报告
- 组件间数据流的优化
- 支持中断和恢复机制

## 测试策略

### 1. 单元测试覆盖
- 每个组件的核心算法测试
- 边界条件和异常情况测试
- 数据验证逻辑测试

### 2. 集成测试
- 完整回测流程的端到端测试
- 不同配置参数组合的测试
- 性能基准测试

### 3. 数据验证测试
- 使用已知结果的历史数据验证
- 与现有回测模块结果对比
- 信号时间戳精度验证

## 入口文件集成方案

### 集成目标
将回测模块V2集成到现有的`run_backtest_ed.py`入口文件中，实现统一的回测接口，支持用户通过命令行参数选择不同的回测模式。

### 集成架构设计

**支持的回测模式**:
1. `single` - 单次事件驱动回测（现有功能）
2. `grid` - 参数网格搜索事件驱动回测（现有功能） 
3. `single_v2` - 单次直接分析回测（新增）
4. `grid_v2` - 参数网格搜索直接分析回测（新增）

**命令行接口扩展**:
```bash
# 现有事件驱动回测
python run_backtest_ed.py --mode single --config config.yaml
python run_backtest_ed.py --mode grid --param_grid grid.json

# 新增直接分析回测  
python run_backtest_ed.py --mode single_v2 --config config.yaml
python run_backtest_ed.py --mode grid_v2 --param_grid grid.json

# 支持混合参数（配置文件 + 命令行覆盖）
python run_backtest_ed.py --mode single_v2 --start_time ********** --end_time **********
```

### 代码集成计划

**1. 导入模块扩展**:
```python
# 在run_backtest_ed.py顶部添加
from utils.backtest_v2.backtest_engine import BacktestEngineV2
from utils.backtest_v2.config_manager import ConfigManagerV2
```

**2. 配置管理统一**:
```python
def load_config_unified(args):
    """统一的配置加载函数，支持V1和V2回测模式"""
    if args.mode in ['single_v2', 'grid_v2']:
        # 使用V2配置管理器
        config_manager = ConfigManagerV2()
        if args.config:
            config = config_manager.load_from_file(args.config)
        else:
            config = config_manager.get_default_config()
        # 应用命令行参数覆盖
        config = config_manager.apply_cli_overrides(config, args)
        return config, 'v2'
    else:
        # 使用现有的V1配置管理器
        config = ConfigManager.load_from_file(args.config) if args.config else BacktestConfig()
        # 现有的命令行参数覆盖逻辑
        return config, 'v1'
```

**3. 回测执行函数扩展**:
```python
async def run_single_backtest_v2(config):
    """运行单次直接分析回测"""
    logger.info(f"开始单次直接分析回测, 配置: {config}")
    
    # 创建结果目录
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    result_subdir_name = f"v2_backtest_results_{timestamp}"
    result_dir_path = os.path.join(RESULT_DIR, result_subdir_name)
    os.makedirs(result_dir_path, exist_ok=True)
    
    start_time = time.time()
    
    # 使用V2回测引擎
    backtest_engine = BacktestEngineV2(config)
    backtest_engine.result_dir = result_dir_path
    
    result = await backtest_engine.run_backtest()
    end_time = time.time()
    
    execution_time = end_time - start_time
    result["execution_time"] = execution_time
    result["param_index"] = 1
    result["backtest_version"] = "v2"
    
    # 保存结果（复用现有的结果保存逻辑）
    results_json_path = os.path.join(result_dir_path, "results.json")
    with open(results_json_path, 'w') as f:
        json.dump(result, f, indent=4, default=str)
    
    # 生成报告（复用现有的报告生成逻辑）
    generate_single_run_report(results_json_path, os.path.join(result_dir_path, "single_run_report.html"))
    
    logger.info(f"V2回测完成，用时: {timedelta(seconds=execution_time)}")
    return result

async def run_parameter_grid_v2(param_grid: Dict[str, List], max_concurrency: int = 4):
    """运行V2参数网格搜索"""
    logger.info(f"开始V2参数网格搜索, 参数网格: {param_grid}")
    
    # 创建主结果目录
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    search_result_dir_name = f"v2_param_search_{timestamp}"
    full_result_dir = os.path.join(RESULT_DIR, search_result_dir_name)
    os.makedirs(full_result_dir, exist_ok=True)
    
    # 生成参数组合
    parameter_combinations = ConfigManagerV2.generate_parameter_combinations(param_grid)
    logger.info(f"生成了 {len(parameter_combinations)} 个参数组合")
    
    # 并行执行回测（复用现有的并行执行逻辑，但使用V2引擎）
    semaphore = asyncio.Semaphore(max_concurrency)
    tasks = [
        run_backtest_v2_with_params(params, semaphore, i, len(parameter_combinations), full_result_dir)
        for i, params in enumerate(parameter_combinations)
    ]
    
    results_summary_list = await asyncio.gather(*tasks)
    
    # 复用现有的结果分析、凯利计算、报告生成逻辑
    # ... (与现有grid模式相同的后处理流程)
    
    return {
        "all_results_summary": results_summary_list,
        "result_dir": full_result_dir,
        "backtest_version": "v2"
    }
```

**4. 主函数修改**:
```python
async def main():
    """主函数，支持V1和V2回测模式"""
    parser = argparse.ArgumentParser(description='运行KOL交易策略回测')
    parser.add_argument('--mode', choices=['single', 'grid', 'single_v2', 'grid_v2'], 
                        default='single', help='回测模式')
    # ... 其他现有参数保持不变
    
    args = parser.parse_args()
    await init_db()
    
    # 统一配置加载
    config, backtest_version = load_config_unified(args)
    
    if args.mode == 'single':
        result = await run_single_backtest(config)
    elif args.mode == 'grid':
        result = await run_parameter_grid(param_grid, args.max_concurrency)
    elif args.mode == 'single_v2':
        result = await run_single_backtest_v2(config)
    elif args.mode == 'grid_v2':
        if args.param_grid:
            with open(args.param_grid, 'r') as f:
                param_grid = json.load(f)
        else:
            param_grid = get_default_param_grid_v2()
        result = await run_parameter_grid_v2(param_grid, args.max_concurrency)
    
    logger.info(f"回测完成，结果保存在 {result.get('result_dir', '')}")
```

### 配置兼容性处理

**参数映射策略**:
- V1和V2回测使用相同的核心参数名称（如`backtest_start_time`, `transaction_min_amount`等）
- 新增V2特有参数的默认值处理
- 提供参数验证，确保V2特有参数的有效性

**配置文件格式**:
```yaml
# 兼容V1和V2的统一配置格式
backtest_start_time: **********
backtest_end_time: **********
transaction_min_amount: 500
kol_account_min_count: 3
# ... 其他通用参数

# V2特有参数（V1回测会忽略）
token_mint_lookback_hours: 48
same_token_notification_interval_minutes: 60
```

### 结果输出兼容性

**统一结果格式**:
- 保持现有的`results.json`文件结构
- 添加`backtest_version`字段标识回测版本
- 统计指标名称保持一致，确保凯利计算和报告生成兼容
- 复用现有的HTML报告生成逻辑

**结果目录命名**:
- V1事件驱动: `ed_backtest_results_*`
- V2直接分析: `v2_backtest_results_*`
- 便于用户区分不同回测方法的结果

### 错误处理和向后兼容

**版本兼容性**:
- 现有的V1回测功能完全不受影响
- 新增模式独立实现，不影响现有代码路径
- 命令行参数保持向后兼容

**错误处理增强**:
```python
def validate_mode_config(mode, config):
    """验证回测模式和配置的兼容性"""
    if mode in ['single_v2', 'grid_v2']:
        # V2特有的配置验证
        required_v2_params = ['token_mint_lookback_hours']
        for param in required_v2_params:
            if not hasattr(config, param):
                logger.warning(f"V2回测缺少参数 {param}，使用默认值")
                setattr(config, param, get_default_value(param))
    return config
```

### 文档更新计划

**用户使用文档**:
- 更新README，添加V2回测模式的使用说明
- 提供V1和V2回测的对比说明
- 添加参数配置示例

**开发者文档**:
- 更新技术架构文档，说明双模式支持
- 添加V2模块的API文档
- 提供扩展和维护指南

## 部署和维护

### 1. 模块集成
- 在`utils/`目录下创建`backtest_v2`子目录
- 添加到项目的包导入路径
- 更新项目文档和README

### 2. 配置管理
- 提供默认配置文件模板
- 兼容现有配置格式
- 添加配置参数说明文档

### 3. 监控和日志
- 详细的执行日志，便于调试
- 性能监控指标收集
- 错误报告和诊断信息

### 4. 入口文件集成
- 在`run_backtest_ed.py`中添加V2模式支持
- 保持向后兼容性，现有功能不受影响
- 统一的命令行接口和配置管理
- 复用现有的结果分析和报告生成逻辑

---

**技术实现优先级**:
1. **高优先级**: DataQuery和SignalAnalyzer - 核心算法逻辑
2. **中优先级**: SellStrategy和BacktestEngine - 策略和流程控制
3. **低优先级**: ResultAnalyzer和ConfigManager - 结果处理和配置管理

**预估工作量**: 总计约8-12小时
- DataQuery: 2-3小时
- SignalAnalyzer: 3-4小时  
- SellStrategy: 2-3小时
- 其他组件: 2-3小时
- 测试和调试: 1-2小时 