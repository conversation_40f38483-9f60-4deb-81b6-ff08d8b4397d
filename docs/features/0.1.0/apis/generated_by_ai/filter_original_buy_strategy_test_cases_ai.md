# 过滤原始买入策略信号功能测试用例设计

**创建日期**: 2025-05-27  
**功能模块**: APIs - 信号记录列表API  
**版本**: 0.1.0  
**测试类型**: 单元测试

## 测试概述

为过滤`original_buy_strategy_name`为"测试"的记录功能设计全面的测试用例，确保过滤逻辑正确、兼容性良好、性能稳定。

## 测试环境

### 测试框架
- **框架**: unittest
- **测试文件**: `test/api/v1/test_signal_api_filter.py`（扩展现有文件）
- **数据库**: MongoDB测试数据库

### 测试数据准备
使用内存数据库或测试专用集合，确保测试隔离性。

## 测试用例设计

### 1. 基本过滤功能测试

#### TC-1: test_filter_original_buy_strategy_signals
**目的**: 验证API正确过滤掉`original_buy_strategy_name`为"测试"的记录

**前置条件**:
- 数据库中存在多条信号记录
- 部分记录的`trigger_conditions.original_buy_strategy_name`为"测试"

**测试数据**:
```python
test_signals = [
    {
        "token_address": "token1",
        "signal_type": "kol_sell",
        "trigger_conditions": {
            "strategy_name": "正常策略",
            "original_buy_strategy_name": "测试"  # 应被过滤
        }
    },
    {
        "token_address": "token2", 
        "signal_type": "kol_sell",
        "trigger_conditions": {
            "strategy_name": "正常策略",
            "original_buy_strategy_name": "正常策略"  # 应保留
        }
    }
]
```

**执行步骤**:
1. 插入测试数据
2. 调用API: `GET /api/v1/signals`
3. 验证响应结果

**预期结果**:
- 返回的记录中不包含`original_buy_strategy_name`为"测试"的记录
- 包含`original_buy_strategy_name`为"正常策略"的记录

#### TC-2: test_original_buy_strategy_field_not_exists
**目的**: 验证保留`original_buy_strategy_name`字段不存在的记录

**测试数据**:
```python
test_signals = [
    {
        "token_address": "token1",
        "signal_type": "kol_buy",
        "trigger_conditions": {
            "strategy_name": "正常策略"
            # 没有 original_buy_strategy_name 字段
        }
    },
    {
        "token_address": "token2",
        "signal_type": "kol_sell", 
        "trigger_conditions": {
            "strategy_name": "正常策略",
            "original_buy_strategy_name": "测试"  # 应被过滤
        }
    }
]
```

**预期结果**:
- 保留没有`original_buy_strategy_name`字段的记录
- 过滤掉`original_buy_strategy_name`为"测试"的记录

### 2. 组合过滤测试

#### TC-3: test_combined_strategy_filtering
**目的**: 验证同时过滤两个策略字段的组合情况

**测试数据**:
```python
test_signals = [
    {
        "token_address": "token1",
        "signal_type": "kol_sell",
        "trigger_conditions": {
            "strategy_name": "测试",  # 应被过滤
            "original_buy_strategy_name": "正常策略"
        }
    },
    {
        "token_address": "token2",
        "signal_type": "kol_sell",
        "trigger_conditions": {
            "strategy_name": "正常策略",
            "original_buy_strategy_name": "测试"  # 应被过滤
        }
    },
    {
        "token_address": "token3",
        "signal_type": "kol_sell",
        "trigger_conditions": {
            "strategy_name": "正常策略",
            "original_buy_strategy_name": "正常策略"  # 应保留
        }
    }
]
```

**预期结果**:
- 只保留两个字段都不为"测试"的记录

#### TC-4: test_both_strategy_fields_test
**目的**: 验证两个字段都为"测试"的记录被正确过滤

**测试数据**:
```python
test_signals = [
    {
        "token_address": "token1",
        "signal_type": "kol_sell",
        "trigger_conditions": {
            "strategy_name": "测试",
            "original_buy_strategy_name": "测试"  # 应被过滤
        }
    },
    {
        "token_address": "token2",
        "signal_type": "kol_sell",
        "trigger_conditions": {
            "strategy_name": "正常策略",
            "original_buy_strategy_name": "正常策略"  # 应保留
        }
    }
]
```

**预期结果**:
- 过滤掉两个字段都为"测试"的记录
- 保留正常的记录

### 3. 边界条件测试

#### TC-5: test_case_sensitive_original_buy_strategy_filtering
**目的**: 验证过滤条件区分大小写

**测试数据**:
```python
test_signals = [
    {
        "token_address": "token1",
        "signal_type": "kol_sell",
        "trigger_conditions": {
            "strategy_name": "正常策略",
            "original_buy_strategy_name": "测试"  # 应被过滤
        }
    },
    {
        "token_address": "token2",
        "signal_type": "kol_sell",
        "trigger_conditions": {
            "strategy_name": "正常策略", 
            "original_buy_strategy_name": "Test"  # 应保留（大小写不同）
        }
    },
    {
        "token_address": "token3",
        "signal_type": "kol_sell",
        "trigger_conditions": {
            "strategy_name": "正常策略",
            "original_buy_strategy_name": "测试1"  # 应保留（不完全匹配）
        }
    }
]
```

**预期结果**:
- 只过滤完全匹配"测试"的记录
- 保留"Test"、"测试1"等记录

#### TC-6: test_null_original_buy_strategy_name_handling
**目的**: 验证null值和空字符串的处理

**测试数据**:
```python
test_signals = [
    {
        "token_address": "token1",
        "signal_type": "kol_sell",
        "trigger_conditions": {
            "strategy_name": "正常策略",
            "original_buy_strategy_name": None  # 应保留
        }
    },
    {
        "token_address": "token2",
        "signal_type": "kol_sell",
        "trigger_conditions": {
            "strategy_name": "正常策略",
            "original_buy_strategy_name": ""  # 应保留
        }
    },
    {
        "token_address": "token3",
        "signal_type": "kol_sell",
        "trigger_conditions": {
            "strategy_name": "正常策略",
            "original_buy_strategy_name": "测试"  # 应被过滤
        }
    }
]
```

**预期结果**:
- 保留null值和空字符串的记录
- 过滤掉"测试"的记录

### 4. 兼容性测试

#### TC-7: test_combination_with_signal_type_filter
**目的**: 验证与signal_type过滤的组合使用

**执行步骤**:
1. 插入包含不同signal_type的测试数据
2. 调用API: `GET /api/v1/signals?signal_type=kol_sell`
3. 验证既过滤了signal_type，又过滤了测试策略

**预期结果**:
- 只返回signal_type为kol_sell的记录
- 同时过滤掉测试策略相关的记录

#### TC-8: test_combination_with_period_filter
**目的**: 验证与时间范围过滤的组合使用

**执行步骤**:
1. 插入不同时间的测试数据
2. 调用API: `GET /api/v1/signals?period=today`
3. 验证时间过滤和策略过滤同时生效

**预期结果**:
- 只返回今天的记录
- 同时过滤掉测试策略相关的记录

#### TC-9: test_combination_with_pagination
**目的**: 验证与分页参数的组合使用

**执行步骤**:
1. 插入大量测试数据
2. 调用API: `GET /api/v1/signals?skip=5&limit=10`
3. 验证分页和过滤同时正确工作

**预期结果**:
- 正确的分页行为
- 过滤后的数据进行分页

### 5. 性能测试

#### TC-10: test_large_dataset_filtering_performance
**目的**: 验证大数据量下的过滤性能

**测试数据**:
- 插入1000+条测试记录
- 包含各种组合的策略字段

**执行步骤**:
1. 记录API调用前的时间
2. 调用API
3. 记录API调用后的时间
4. 计算响应时间

**预期结果**:
- 响应时间在可接受范围内（<500ms）
- 过滤结果正确

### 6. 错误处理测试

#### TC-11: test_malformed_trigger_conditions
**目的**: 验证异常数据的处理

**测试数据**:
```python
test_signals = [
    {
        "token_address": "token1",
        "signal_type": "kol_sell",
        "trigger_conditions": "invalid_format"  # 非字典格式
    },
    {
        "token_address": "token2",
        "signal_type": "kol_sell"
        # 没有 trigger_conditions 字段
    }
]
```

**预期结果**:
- API不报错
- 正确处理异常数据格式

## 测试执行计划

### 测试顺序
1. 基本过滤功能测试（TC-1, TC-2）
2. 组合过滤测试（TC-3, TC-4）
3. 边界条件测试（TC-5, TC-6）
4. 兼容性测试（TC-7, TC-8, TC-9）
5. 性能测试（TC-10）
6. 错误处理测试（TC-11）

### 测试数据管理
- 每个测试用例使用独立的测试数据
- 测试前清理数据库
- 测试后清理测试数据

### 断言验证
- 验证返回的记录数量
- 验证每条记录的字段值
- 验证过滤逻辑的正确性
- 验证API响应格式

## 测试覆盖率目标

### 功能覆盖率
- **目标**: 100%
- **覆盖范围**: 所有过滤逻辑分支

### 代码覆盖率
- **目标**: 95%+
- **覆盖文件**: `api/v1/signal_api.py`中的相关函数

### 场景覆盖率
- **目标**: 100%
- **覆盖场景**: 所有可能的数据组合和边界条件

## 测试自动化

### 持续集成
- 集成到CI/CD流水线
- 每次代码提交自动运行测试
- 测试失败时阻止部署

### 测试报告
- 生成详细的测试报告
- 包含覆盖率统计
- 记录性能指标

## 验收标准

### 功能验收
- 所有测试用例通过
- 过滤逻辑100%正确
- 兼容性测试通过

### 性能验收
- 响应时间无显著增加
- 大数据量测试通过

### 质量验收
- 代码覆盖率达标
- 无内存泄漏
- 无异常错误 