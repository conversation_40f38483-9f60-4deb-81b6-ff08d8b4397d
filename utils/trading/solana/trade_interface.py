from abc import ABC, abstractmethod
from typing import Optional, Dict, Any
from datetime import datetime
from enum import Enum
from pydantic import BaseModel, Field
from beanie import PydanticObjectId # 虽然TradeRecord在models中，但PydanticObjectId可能在这里也需要

class TradeStatus(str, Enum):
    PENDING = "pending"
    SUCCESS = "success"
    FAILED = "failed"
    SKIPPED = "skipped"
    MANUAL_INTERVENTION_REQUIRED = "manual_intervention_required"

class TradeType(str, Enum):
    BUY = "buy"
    SELL = "sell"

class TradeResult(BaseModel):
    status: TradeStatus
    tx_hash: Optional[str] = None
    error_message: Optional[str] = None
    provider_response_raw: Optional[Dict[str, Any]] = None
    actual_amount_in: Optional[float] = None
    actual_amount_out: Optional[float] = None
    executed_at: Optional[datetime] = None

class TradeInterface(ABC):
    @abstractmethod
    async def execute_trade(
        self,
        trade_type: TradeType,
        input_token_address: str,
        output_token_address: str,
        amount_input_token: float, 
        wallet_private_key_b58: str,
        wallet_address: str,
        strategy_snapshot: Dict[str, Any],
        signal_id: PydanticObjectId,
        trade_record_id: PydanticObjectId
    ) -> TradeResult:
        """
        执行交易操作。

        Args:
            trade_type: 交易类型 (BUY 或 SELL).
            input_token_address: 输入代币的地址。
            output_token_address: 输出代币的地址。
            amount_input_token: 输入代币的数量。
            wallet_private_key_b58: 执行交易的钱包的Base58编码私钥。
            wallet_address: 执行交易的钱包公钥。
            strategy_snapshot: 当前策略配置的快照，包含如滑点、优先费等参数。
            signal_id: 关联的信号ID。
            trade_record_id: 关联的交易记录ID。

        Returns:
            TradeResult: 包含交易结果的对象。
        """
        pass 
    
    @abstractmethod
    def is_slippage_related_error(
        self, 
        error_message: Optional[str], 
        provider_response: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        判断错误是否与滑点相关 - 各实现类负责识别自己API的滑点错误
        
        Args:
            error_message: 错误信息文本
            provider_response: 原始API响应（可选，用于更精确的判断）
            
        Returns:
            bool: 是否为滑点相关错误
            
        Note:
            各交易接口实现类最了解自己的错误响应格式，能提供最准确的判断：
            - GMGN API: 检查特定的错误码和消息格式
            - Solana Direct: 检查交易失败的具体原因
            - 其他接口: 根据各自的API文档实现
        """
        pass
    
    def is_non_retryable_error(
        self, 
        error_message: Optional[str], 
        provider_response: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        判断错误是否为不可重试错误 - 提供默认实现，子类可覆盖
        
        Args:
            error_message: 错误信息文本
            provider_response: 原始API响应（可选）
            
        Returns:
            bool: 是否为不可重试错误
        """
        if not error_message:
            return False
        
        error_lower = error_message.lower()
        
        # 通用的不可重试错误关键词
        non_retryable_keywords = [
            "insufficient funds",
            "insufficient balance", 
            "token not found",
            "invalid token",
            "unauthorized",
            "forbidden",
            "account not found",
            "invalid private key",
            "signature verification failed",
            "invalid address",
            "malformed transaction"
        ]
        
        for keyword in non_retryable_keywords:
            if keyword in error_lower:
                return True
        
        return False 