"""
AutoTradeManager集成测试

测试AutoTradeManager与其他组件的集成：
1. 端到端交易流程
2. 组件协作
3. 数据库集成
4. 配置动态更新
5. 故障转移场景
"""

import unittest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import Dict, Any, List, Optional
from datetime import datetime
import os

# 测试相关导入
from models.config import Config, AutoTradeManagerConfig, AutoTradeConfig, WalletConfig, TradingParams, TradeChannelConfig, NotificationConfig
from beanie import PydanticObjectId
from models.trade_execution import TradeExecutionResult, TradeStatus, ChannelAttemptResult
from models.trade_record import TradeRecord, TradeStatus as ModelTradeStatus, TradeType as ModelTradeType
from utils.trading.auto_trade_manager import AutoTradeManager, get_auto_trade_manager
from utils.trading.solana.trade_interface import TradeResult, TradeStatus as InterfaceTradeStatus
from dao.config_dao import ConfigDAO
from dao.trade_record_dao import TradeRecordDAO


class MockGmgnTradeService:
    """用于集成测试的Mock GMGN交易服务"""
    
    def __init__(self, should_succeed: bool = True, execution_time: float = 1.0):
        self.should_succeed = should_succeed
        self.execution_time = execution_time
        self.execute_count = 0
    
    async def execute_trade(self, *args, **kwargs) -> TradeResult:
        """Mock交易执行"""
        self.execute_count += 1
        await asyncio.sleep(self.execution_time)
        
        if self.should_succeed:
            return TradeResult(
                success=True,
                tx_hash=f"gmgn_tx_hash_{self.execute_count}",
                status=InterfaceTradeStatus.SUCCESS,
                amount_in=1000000000,  # 1 SOL
                amount_out=10000000,   # 0.01 token
                error_message=None
            )
        else:
            return TradeResult(
                success=False,
                tx_hash=None,
                status=InterfaceTradeStatus.FAILED,
                amount_in=0,
                amount_out=0,
                error_message="GMGN service error"
            )
    
    async def is_available(self) -> bool:
        return True
    
    async def close(self) -> None:
        pass


class MockSolanaDirectService:
    """用于集成测试的Mock Solana直接交易服务"""
    
    def __init__(self, should_succeed: bool = True, execution_time: float = 2.0):
        self.should_succeed = should_succeed
        self.execution_time = execution_time
        self.execute_count = 0
    
    async def execute_trade(self, *args, **kwargs) -> TradeResult:
        """Mock交易执行"""
        self.execute_count += 1
        await asyncio.sleep(self.execution_time)
        
        if self.should_succeed:
            return TradeResult(
                success=True,
                tx_hash=f"solana_tx_hash_{self.execute_count}",
                status=InterfaceTradeStatus.SUCCESS,
                amount_in=1000000000,  # 1 SOL
                amount_out=10000000,   # 0.01 token
                error_message=None
            )
        else:
            return TradeResult(
                success=False,
                tx_hash=None,
                status=InterfaceTradeStatus.FAILED,
                amount_in=0,
                amount_out=0,
                error_message="Solana service error"
            )
    
    async def is_available(self) -> bool:
        return True
    
    async def close(self) -> None:
        pass


class TestAutoTradeIntegration(unittest.TestCase):
    """AutoTradeManager集成测试类"""
    
    def setUp(self) -> None:
        """测试前置设置"""
        # 重置单例实例
        AutoTradeManager._instance = None
        # 重置全局单例实例
        import utils.trading.auto_trade_manager
        utils.trading.auto_trade_manager._auto_trade_manager_instance = None
        
        # 设置测试环境变量（使用真实生成的Solana测试私钥）
        import os
        # 这是真实生成的有效Solana测试私钥（88字符Base58编码，专门用于测试）
        os.environ["TEST_PRIVATE_KEY"] = "61pBDpzbHW5HLqmaTn4MK8UpkadQDM3LppHETiQPg6GUCPKBuoNDkGmXuKVqEn2ZVZHG1VurVDmaqnqUsW9HNKkK"
        # 第二个有效的测试私钥
        os.environ["CUSTOM_PRIVATE_KEY"] = "4Y49XefEcyhxVRGd5DN4TkoJziUmdVsB73mwTq1tdYMrc5fWQjoVNpbNE2PjPLjBFBi34ig8jj9xMT1fHYDHUuGp"
        
        # 创建Mock服务
        self.mock_gmgn_service = MockGmgnTradeService(should_succeed=True)
        self.mock_solana_service = MockSolanaDirectService(should_succeed=True)
        self.mock_failing_gmgn = MockGmgnTradeService(should_succeed=False)
        
        # 创建Mock DAO（不使用spec以允许添加任何方法）
        self.mock_config_dao = Mock()
        self.mock_trade_record_dao = Mock()
        
        # 设置TradeRecordDAO的Mock方法
        mock_trade_record = Mock()
        mock_trade_record.id = PydanticObjectId()
        self.mock_trade_record_dao.create_trade_record = AsyncMock(return_value=mock_trade_record)
        self.mock_trade_record_dao.create_channel_attempt_record = AsyncMock(return_value="attempt_record_id")
        self.mock_trade_record_dao.update_trade_record = AsyncMock()
        self.mock_trade_record_dao.update_trade_record_from_execution_result = AsyncMock()
        self.mock_trade_record_dao.save_channel_attempt_records = AsyncMock()
        
        # 创建测试配置
        self.sample_config = self._create_test_config()
        # 创建Mock配置文档而不是实际的Beanie文档
        self.sample_config_doc = Mock()
        self.sample_config_doc.type = "auto_trade_manager"
        self.sample_config_doc.data = self.sample_config
        self.sample_config_doc.updated_at = datetime.now()
        
        # 设置环境变量
        os.environ["TEST_PRIVATE_KEY"] = "61pBDpzbHW5HLqmaTn4MK8UpkadQDM3LppHETiQPg6GUCPKBuoNDkGmXuKVqEn2ZVZHG1VurVDmaqnqUsW9HNKkK"
    
    def tearDown(self) -> None:
        """测试后置清理"""
        # 重置单例实例
        AutoTradeManager._instance = None
        
        # 清理测试环境变量
        import os
        for key in ["TEST_PRIVATE_KEY", "CUSTOM_PRIVATE_KEY"]:
            if key in os.environ:
                del os.environ[key]
    
    def _create_test_config(self) -> AutoTradeManagerConfig:
        """创建测试用配置"""
        wallet_config = WalletConfig(
            default_private_key_env_var="TEST_PRIVATE_KEY",
            default_wallet_address="test_wallet_address"
        )
        
        trading_params = TradingParams(
            default_buy_amount_sol=0.01,
            default_buy_slippage_percentage=10.0,
            default_buy_priority_fee_sol=0.0005,
            default_sell_slippage_percentage=10.0,
            default_sell_priority_fee_sol=0.0005
        )
        
        gmgn_channel = TradeChannelConfig(
            channel_type="gmgn",
            enabled=True,
            priority=1,
            timeout_seconds=60,
            max_retries=3,
            trading_params=trading_params,
            channel_params={"gmgn_api_host": "https://gmgn.ai"}
        )
        
        solana_channel = TradeChannelConfig(
            channel_type="jupiter",
            enabled=True,
            priority=2,
            timeout_seconds=90,
            max_retries=2,
            trading_params=trading_params,
            channel_params={
                "jupiter_api_host": "https://quote-api.jup.ag",
                "solana_rpc_url": "https://api.mainnet-beta.solana.com"
            }
        )
        
        notification_config = NotificationConfig(
            include_trade_details=True,
            notify_on_success=True,
            notify_on_failure=True
        )
        
        auto_trade_config = AutoTradeConfig(
            enabled=True,
            wallet_config=wallet_config,
            channels=[gmgn_channel, solana_channel],
            default_timeout=120,
            max_total_retries=5,
            notification_config=notification_config
        )
        
        return AutoTradeManagerConfig(
            auto_trade=auto_trade_config
        )
    
    def _get_disabled_config(self) -> AutoTradeConfig:
        """创建禁用的配置"""
        wallet_config = WalletConfig(
            default_private_key_env_var="TEST_PRIVATE_KEY",
            default_wallet_address="test_wallet_address"
        )
        
        notification_config = NotificationConfig(
            include_trade_details=True,
            notify_on_success=True,
            notify_on_failure=True
        )
        
        return AutoTradeConfig(
            enabled=False,  # 禁用
            wallet_config=wallet_config,
            channels=[],  # 无渠道
            default_timeout=120,
            max_total_retries=5,
            notification_config=notification_config
        )
    
    @patch('utils.trading.auto_trade_manager.AutoTradeManager._register_channels')
    @patch('utils.trading.config_manager.ConfigDAO')
    @patch('utils.trading.trade_record_manager.TradeRecordDAO')
    async def test_end_to_end_successful_trade(
        self,
        mock_trade_record_dao_class: Mock,
        mock_config_dao_class: Mock,
        mock_register_channels: AsyncMock
    ) -> None:
        """测试端到端成功交易流程"""
        # 设置Mock DAO
        mock_config_dao_class.return_value = self.mock_config_dao
        mock_trade_record_dao_class.return_value = self.mock_trade_record_dao
        
        # 设置配置DAO返回值，返回AutoTradeConfig而不是AutoTradeManagerConfig
        mock_auto_trade_config_doc = Mock()
        mock_auto_trade_config_doc.type = "auto_trade_manager"
        mock_auto_trade_config_doc.data = self.sample_config.auto_trade
        mock_auto_trade_config_doc.updated_at = datetime.now()
        self.mock_config_dao.get_config = AsyncMock(return_value=mock_auto_trade_config_doc)
        
        # 设置交易记录DAO返回值
        mock_trade_record = Mock()
        mock_trade_record.id = PydanticObjectId()
        
        # Mock渠道注册（不做任何实际注册）
        mock_register_channels.return_value = None
        
        # 获取AutoTradeManager实例
        auto_trade_manager = await get_auto_trade_manager()
        
        # 手动设置Mock组件以覆盖真实组件
        # 创建Mock ConfigManager
        mock_config_manager = Mock()
        mock_config_manager.get_config = AsyncMock(return_value=self.sample_config.auto_trade)
        mock_config_manager.is_enabled = AsyncMock(return_value=True)
        mock_config_manager.get_enabled_channels = AsyncMock(return_value=self.sample_config.auto_trade.channels)
        auto_trade_manager.config_manager = mock_config_manager
        
        # 手动设置Mock TradeRecordManager
        auto_trade_manager.trade_record_manager = self.mock_trade_record_dao
        
        # 重置Mock服务的计数器
        self.mock_gmgn_service.execute_count = 0
        self.mock_solana_service.execute_count = 0
        
        # 重置Mock DAO的调用历史
        self.mock_trade_record_dao.reset_mock()
        
        # 重新设置Mock DAO的方法
        mock_trade_record = Mock()
        mock_trade_record.id = PydanticObjectId()
        self.mock_trade_record_dao.create_trade_record = AsyncMock(return_value=mock_trade_record)
        self.mock_trade_record_dao.create_channel_attempt_record = AsyncMock(return_value="attempt_record_id")
        self.mock_trade_record_dao.update_trade_record = AsyncMock()
        self.mock_trade_record_dao.update_trade_record_from_execution_result = AsyncMock()
        self.mock_trade_record_dao.save_channel_attempt_records = AsyncMock()
        
        # 手动注册Mock渠道（覆盖真实渠道）
        auto_trade_manager.channel_registry.register_channel(
            "gmgn", self.mock_gmgn_service, self.sample_config.auto_trade.channels[0]
        )
        auto_trade_manager.channel_registry.register_channel(
            "jupiter", self.mock_solana_service, self.sample_config.auto_trade.channels[1]
        )
        
        # 执行交易
        result = await auto_trade_manager.execute_trade(
            trade_type="buy",
            token_in_address="So11111111111111111111111111111111111111112",
            token_out_address="test_token_address",
            amount=0.01,
            signal_id=PydanticObjectId(),
            strategy_name="test_strategy"
        )
        
        
        # 验证结果
        self.assertEqual(result.final_status, TradeStatus.SUCCESS)
        self.assertEqual(result.successful_channel, "gmgn")
        self.assertEqual(len(result.channel_attempts), 1)
        self.assertIsNotNone(result.final_trade_record_id)
        
        # 验证服务调用
        self.assertEqual(self.mock_gmgn_service.execute_count, 1)
        self.assertEqual(self.mock_solana_service.execute_count, 0)  # 第二个服务没有被调用
        
        # 验证数据库操作
        self.mock_trade_record_dao.create_trade_record.assert_called_once()
        self.mock_trade_record_dao.update_trade_record_from_execution_result.assert_called()
    
    @patch('utils.trading.auto_trade_manager.AutoTradeManager._register_channels')
    @patch('utils.trading.config_manager.ConfigDAO')
    @patch('utils.trading.trade_record_manager.TradeRecordDAO')
    async def test_end_to_end_failover_scenario(
        self,
        mock_trade_record_dao_class: Mock,
        mock_config_dao_class: Mock,
        mock_register_channels: AsyncMock
    ) -> None:
        """测试端到端故障转移场景"""
        # 设置Mock DAO
        mock_config_dao_class.return_value = self.mock_config_dao
        mock_trade_record_dao_class.return_value = self.mock_trade_record_dao
        
        # 设置配置DAO返回值，返回AutoTradeConfig而不是AutoTradeManagerConfig
        mock_auto_trade_config_doc = Mock()
        mock_auto_trade_config_doc.type = "auto_trade_manager"
        mock_auto_trade_config_doc.data = self.sample_config.auto_trade
        mock_auto_trade_config_doc.updated_at = datetime.now()
        self.mock_config_dao.get_config = AsyncMock(return_value=mock_auto_trade_config_doc)
        
        # 设置交易记录DAO返回值
        mock_trade_record = Mock()
        mock_trade_record.id = PydanticObjectId()
        
        # Mock渠道注册（不做任何实际注册）
        mock_register_channels.return_value = None
        
        # 获取AutoTradeManager实例
        auto_trade_manager = await get_auto_trade_manager()
        
        # 手动设置Mock组件以覆盖真实组件
        # 创建Mock ConfigManager
        mock_config_manager = Mock()
        mock_config_manager.get_config = AsyncMock(return_value=self.sample_config.auto_trade)
        mock_config_manager.is_enabled = AsyncMock(return_value=True)
        mock_config_manager.get_enabled_channels = AsyncMock(return_value=self.sample_config.auto_trade.channels)
        auto_trade_manager.config_manager = mock_config_manager
        
        # 手动设置Mock TradeRecordManager
        auto_trade_manager.trade_record_manager = self.mock_trade_record_dao
        
        # 重置Mock服务的计数器
        self.mock_failing_gmgn.execute_count = 0
        self.mock_solana_service.execute_count = 0
        
        # 重置Mock DAO的调用历史
        self.mock_trade_record_dao.reset_mock()
        
        # 重新设置Mock DAO的方法
        mock_trade_record = Mock()
        mock_trade_record.id = PydanticObjectId()
        self.mock_trade_record_dao.create_trade_record = AsyncMock(return_value=mock_trade_record)
        self.mock_trade_record_dao.create_channel_attempt_record = AsyncMock(return_value="attempt_record_id")
        self.mock_trade_record_dao.update_trade_record = AsyncMock()
        self.mock_trade_record_dao.update_trade_record_from_execution_result = AsyncMock()
        self.mock_trade_record_dao.save_channel_attempt_records = AsyncMock()
        
        # 手动注册Mock渠道（第一个失败，第二个成功以测试故障转移）
        auto_trade_manager.channel_registry.register_channel(
            "gmgn", self.mock_failing_gmgn, self.sample_config.auto_trade.channels[0]
        )
        auto_trade_manager.channel_registry.register_channel(
            "jupiter", self.mock_solana_service, self.sample_config.auto_trade.channels[1]
        )
        
        # 执行交易
        result = await auto_trade_manager.execute_trade(
            trade_type="buy",
            token_in_address="So11111111111111111111111111111111111111112",
            token_out_address="test_token_address",
            amount=0.01,
            signal_id=PydanticObjectId(),
            strategy_name="test_strategy"
        )
        
        
        # 验证结果
        self.assertEqual(result.final_status, TradeStatus.SUCCESS)
        self.assertEqual(result.successful_channel, "jupiter")  # 故障转移到第二个渠道
        self.assertEqual(len(result.channel_attempts), 2)  # 两次尝试
        
        # 验证故障转移
        self.assertEqual(result.channel_attempts[0].status, TradeStatus.FAILED)  # 第一次失败
        self.assertEqual(result.channel_attempts[1].status, TradeStatus.SUCCESS)  # 第二次成功
        
        # 验证服务调用
        self.assertEqual(self.mock_failing_gmgn.execute_count, 1)
        self.assertEqual(self.mock_solana_service.execute_count, 1)
    
    @patch('utils.trading.auto_trade_manager.ConfigManager')
    @patch('utils.trading.config_manager.ConfigDAO')
    @patch('utils.trading.trade_record_manager.TradeRecordDAO')
    async def test_config_not_found_default_handling(
        self,
        mock_trade_record_dao_class: Mock,
        mock_config_dao_class: Mock,
        mock_config_manager_class: Mock
    ) -> None:
        """测试配置不存在时的默认处理"""
        # 设置Mock DAO
        mock_config_dao_class.return_value = self.mock_config_dao
        mock_trade_record_dao_class.return_value = self.mock_trade_record_dao
        
        # 创建Mock ConfigManager
        mock_config_manager = Mock()
        mock_config_manager.get_config = AsyncMock(return_value=self._get_disabled_config())
        mock_config_manager.is_enabled = AsyncMock(return_value=False)  # 重要：确保is_enabled返回False
        mock_config_manager.force_reload = AsyncMock()
        mock_config_manager.get_cache_info = AsyncMock(return_value={"has_cache": True})
        mock_config_manager_class.return_value = mock_config_manager
        
        # 设置配置DAO返回None（配置不存在）
        self.mock_config_dao.get_config = AsyncMock(return_value=None)
        
        # 重置单例实例以确保使用新的Mock设置
        AutoTradeManager._instance = None
        
        # 获取AutoTradeManager实例
        auto_trade_manager = await get_auto_trade_manager()
        
        # 手动设置Mock ConfigManager
        auto_trade_manager.config_manager = mock_config_manager
        
        # 执行交易
        result = await auto_trade_manager.execute_trade(
            trade_type="buy",
            token_in_address="So11111111111111111111111111111111111111112",
            token_out_address="test_token_address",
            amount=0.01,
            signal_id=PydanticObjectId(),
            strategy_name="test_strategy"
        )
        
        # 验证结果（应该被禁用）
        self.assertEqual(result.final_status, TradeStatus.SKIPPED)
        self.assertIn("disabled", result.error_summary.lower())
    
    @patch('utils.trading.auto_trade_manager.ConfigManager')
    @patch('utils.trading.config_manager.ConfigDAO')
    @patch('utils.trading.trade_record_manager.TradeRecordDAO')
    async def test_dynamic_config_update(
        self,
        mock_trade_record_dao_class: Mock,
        mock_config_dao_class: Mock,
        mock_config_manager_class: Mock
    ) -> None:
        """测试动态配置更新"""
        # 设置Mock DAO
        mock_config_dao_class.return_value = self.mock_config_dao
        mock_trade_record_dao_class.return_value = self.mock_trade_record_dao
        
        # 创建Mock ConfigManager
        mock_config_manager = Mock()
        
        # 初始配置（禁用）
        disabled_config = self._get_disabled_config()
        mock_config_manager.get_config = AsyncMock(return_value=disabled_config)
        mock_config_manager.is_enabled = AsyncMock(return_value=False)  # 初始禁用
        mock_config_manager.force_reload = AsyncMock()
        mock_config_manager.get_cache_info = AsyncMock(return_value={"has_cache": True})
        mock_config_manager.update_config = AsyncMock(return_value=True)
        mock_config_manager_class.return_value = mock_config_manager
        
        self.mock_config_dao.get_config = AsyncMock(return_value=None)
        self.mock_config_dao.save_or_update_config = AsyncMock()
        
        # 重置单例实例以确保使用新的Mock设置
        AutoTradeManager._instance = None
        
        # 获取AutoTradeManager实例
        auto_trade_manager = await get_auto_trade_manager()
        
        # 手动设置Mock ConfigManager
        auto_trade_manager.config_manager = mock_config_manager
        
        # 第一次执行（应该被跳过）
        result1 = await auto_trade_manager.execute_trade(
            trade_type="buy",
            token_in_address="So11111111111111111111111111111111111111112",
            token_out_address="test_token_address",
            amount=0.01,
            signal_id=PydanticObjectId(),
            strategy_name="test_strategy"
        )
        
        self.assertEqual(result1.final_status, TradeStatus.SKIPPED)
        
        # 模拟配置更新（启用）
        enabled_config = self.sample_config.auto_trade
        enabled_config.enabled = True
        
        # 更新Mock ConfigManager的返回值
        mock_config_manager.get_config = AsyncMock(return_value=enabled_config)
        mock_config_manager.is_enabled = AsyncMock(return_value=True)  # 更新为启用
        
        # 通过ConfigManager更新配置
        await auto_trade_manager.config_manager.update_config(enabled_config)
        
        # 重新加载配置
        await auto_trade_manager.reload_config()
        
        # 验证配置已更新
        current_config = await auto_trade_manager.config_manager.get_config()
        self.assertTrue(current_config.enabled)
    
    @patch('utils.trading.config_manager.ConfigDAO')
    @patch('utils.trading.trade_record_manager.TradeRecordDAO')
    async def test_parameter_override_flow(
        self,
        mock_trade_record_dao_class: Mock,
        mock_config_dao_class: Mock
    ) -> None:
        """测试参数覆盖流程"""
        # 设置Mock DAO
        mock_config_dao_class.return_value = self.mock_config_dao
        mock_trade_record_dao_class.return_value = self.mock_trade_record_dao
        
        # 设置配置DAO返回值，返回AutoTradeConfig而不是AutoTradeManagerConfig
        mock_auto_trade_config_doc = Mock()
        mock_auto_trade_config_doc.type = "auto_trade_manager"
        mock_auto_trade_config_doc.data = self.sample_config.auto_trade
        mock_auto_trade_config_doc.updated_at = datetime.now()
        self.mock_config_dao.get_config = AsyncMock(return_value=mock_auto_trade_config_doc)
        
        # 设置交易记录DAO返回值
        mock_trade_record = Mock()
        mock_trade_record.id = PydanticObjectId()
        
        # 获取AutoTradeManager实例
        auto_trade_manager = await get_auto_trade_manager()
        
        # 手动设置Mock组件以覆盖真实组件
        # 创建Mock ConfigManager
        mock_config_manager = Mock()
        mock_config_manager.get_config = AsyncMock(return_value=self.sample_config.auto_trade)
        mock_config_manager.is_enabled = AsyncMock(return_value=True)
        mock_config_manager.get_enabled_channels = AsyncMock(return_value=self.sample_config.auto_trade.channels)
        auto_trade_manager.config_manager = mock_config_manager
        
        # 手动设置Mock TradeRecordManager
        auto_trade_manager.trade_record_manager = self.mock_trade_record_dao
        
        # 准备策略级别覆盖参数
        strategy_overrides = {
            "buy_amount_sol": 0.05,  # 覆盖默认的0.01
            "buy_slippage_percentage": 15.0  # 覆盖默认的10.0
        }
        
        # 执行交易（使用Mock验证参数传递）
        with patch.object(auto_trade_manager.trade_orchestrator, 'execute_trade') as mock_execute:
            mock_execute_result = TradeExecutionResult(
                final_status=TradeStatus.SUCCESS,
                successful_channel="gmgn",
                final_trade_record_id=PydanticObjectId(),
                channel_attempts=[],
                total_execution_time=5.0,
                started_at=datetime.now(),
                completed_at=datetime.now()
            )
            mock_execute.return_value = mock_execute_result
            
            result = await auto_trade_manager.execute_trade(
                trade_type="buy",
                token_in_address="So11111111111111111111111111111111111111112",
                token_out_address="test_token_address",
                wallet_private_key_env_var="CUSTOM_PRIVATE_KEY",
                wallet_address="custom_wallet_address",
                strategy_trading_overrides=strategy_overrides,
                signal_id=PydanticObjectId(),
                strategy_name="test_strategy"
            )
            
            # 验证TradeOrchestrator被调用
            mock_execute.assert_called_once()
            
            # 获取传递给TradeOrchestrator的参数
            trade_request = mock_execute.call_args[0][0]
            
            # 验证参数覆盖生效
            self.assertEqual(trade_request.amount, 0.05)  # 使用覆盖的金额
            # 注意：TradeRequest使用的是wallet_private_key_b58而不是wallet_private_key_env_var
            self.assertIsNotNone(trade_request.wallet_private_key_b58)  # 私钥应该已经从环境变量解析
            self.assertEqual(trade_request.wallet_address, "custom_wallet_address")
    
    @patch('utils.trading.auto_trade_manager.AutoTradeManager._create_channel_instance')
    async def test_concurrent_trades(self, mock_create_channel_instance: AsyncMock) -> None:
        """测试并发交易处理"""
        # 创建配置有限并发数的配置
        config = self._create_test_config()
        # 注意：max_concurrent_trades字段可能不存在于AutoTradeConfig中
        
        with patch('utils.trading.config_manager.ConfigDAO') as mock_config_dao_class:
            with patch('utils.trading.trade_record_manager.TradeRecordDAO') as mock_trade_record_dao_class:
                # 设置Mock DAO
                mock_config_dao_class.return_value = self.mock_config_dao
                mock_trade_record_dao_class.return_value = self.mock_trade_record_dao
                
                # 设置配置DAO返回值
                config_doc = Mock()
                config_doc.type = "auto_trade_manager"
                config_doc.data = config.auto_trade
                config_doc.updated_at = datetime.now()
                self.mock_config_dao.get_config = AsyncMock(return_value=config_doc)
                
                # 设置渠道实例创建
                async def mock_create_channel_side_effect(channel_config):
                    if channel_config.channel_type == "gmgn":
                        return self.mock_gmgn_service
                    elif channel_config.channel_type == "jupiter":
                        return self.mock_solana_service
                    return None
                
                mock_create_channel_instance.side_effect = mock_create_channel_side_effect
                
                # 获取AutoTradeManager实例
                auto_trade_manager = await get_auto_trade_manager()
                
                # 手动设置Mock组件以覆盖真实组件
                # 创建Mock ConfigManager
                mock_config_manager = Mock()
                mock_config_manager.get_config = AsyncMock(return_value=config.auto_trade)
                mock_config_manager.is_enabled = AsyncMock(return_value=True)
                mock_config_manager.get_enabled_channels = AsyncMock(return_value=config.auto_trade.channels)
                auto_trade_manager.config_manager = mock_config_manager
                
                # 手动设置Mock TradeRecordManager
                auto_trade_manager.trade_record_manager = self.mock_trade_record_dao
                
                # 创建慢速的Mock执行器
                async def slow_execute_trade(*args, **kwargs):
                    await asyncio.sleep(0.1)  # 模拟慢速交易
                    return TradeExecutionResult(
                        final_status=TradeStatus.SUCCESS,
                        successful_channel="gmgn",
                        final_trade_record_id=PydanticObjectId(),
                        channel_attempts=[],
                        total_execution_time=0.1,
                        started_at=datetime.now(),
                        completed_at=datetime.now()
                    )
                
                # 模拟并发交易
                with patch.object(auto_trade_manager.trade_orchestrator, 'execute_trade', side_effect=slow_execute_trade):
                    # 启动多个并发交易
                    tasks = []
                    for i in range(5):
                        task = auto_trade_manager.execute_trade(
                            trade_type="buy",
                            token_in_address="So11111111111111111111111111111111111111112",
                            token_out_address=f"test_token_address_{i}",
                            amount=0.01,
                            signal_id=PydanticObjectId(),
                            strategy_name="test_strategy"
                        )
                        tasks.append(task)
                    
                    # 等待所有交易完成
                    results = await asyncio.gather(*tasks)
                    
                    # 验证所有交易都成功完成
                    for result in results:
                        self.assertEqual(result.final_status, TradeStatus.SUCCESS)
    
    @patch('utils.trading.auto_trade_manager.AutoTradeManager._create_channel_instance')
    async def test_statistics_collection(self, mock_create_channel_instance: AsyncMock) -> None:
        """测试统计信息收集"""
        with patch('utils.trading.config_manager.ConfigDAO') as mock_config_dao_class:
            with patch('utils.trading.trade_record_manager.TradeRecordDAO') as mock_trade_record_dao_class:
                # 设置Mock DAO
                mock_config_dao_class.return_value = self.mock_config_dao
                mock_trade_record_dao_class.return_value = self.mock_trade_record_dao
                
                # 设置配置DAO返回值，返回AutoTradeConfig而不是AutoTradeManagerConfig
                mock_auto_trade_config_doc = Mock()
                mock_auto_trade_config_doc.type = "auto_trade_manager"
                mock_auto_trade_config_doc.data = self.sample_config.auto_trade
                mock_auto_trade_config_doc.updated_at = datetime.now()
                self.mock_config_dao.get_config = AsyncMock(return_value=mock_auto_trade_config_doc)
                
                # 设置渠道实例创建
                async def mock_create_channel_side_effect(channel_config):
                    if channel_config.channel_type == "gmgn":
                        return self.mock_gmgn_service
                    elif channel_config.channel_type == "jupiter":
                        return self.mock_solana_service
                    return None
                
                mock_create_channel_instance.side_effect = mock_create_channel_side_effect
                
                # 获取AutoTradeManager实例
                auto_trade_manager = await get_auto_trade_manager()
                
                # 手动设置Mock组件以覆盖真实组件
                # 创建Mock ConfigManager
                mock_config_manager = Mock()
                mock_config_manager.get_config = AsyncMock(return_value=self.sample_config.auto_trade)
                mock_config_manager.is_enabled = AsyncMock(return_value=True)
                mock_config_manager.get_enabled_channels = AsyncMock(return_value=self.sample_config.auto_trade.channels)
                auto_trade_manager.config_manager = mock_config_manager
                
                # 手动设置Mock TradeRecordManager
                auto_trade_manager.trade_record_manager = self.mock_trade_record_dao
                
                # 模拟交易执行结果
                mock_success_result = TradeExecutionResult(
                    final_status=TradeStatus.SUCCESS,
                    successful_channel="gmgn",
                    final_trade_record_id=PydanticObjectId(),
                    channel_attempts=[],
                    total_execution_time=5.0,
                    started_at=datetime.now(),
                    completed_at=datetime.now()
                )
                
                mock_failed_result = TradeExecutionResult(
                    final_status=TradeStatus.FAILED,
                    successful_channel=None,
                    final_trade_record_id=None,
                    channel_attempts=[],
                    total_execution_time=3.0,
                    error_summary="All channels failed",
                    started_at=datetime.now(),
                    completed_at=datetime.now()
                )
                
                # 模拟多次交易执行
                with patch.object(auto_trade_manager.trade_orchestrator, 'execute_trade') as mock_execute:
                    # 前两次成功，第三次失败
                    mock_execute.side_effect = [mock_success_result, mock_success_result, mock_failed_result]
                    
                    # 执行多次交易
                    for i in range(3):
                        await auto_trade_manager.execute_trade(
                            trade_type="buy",
                            token_in_address="So11111111111111111111111111111111111111112",
                            token_out_address=f"test_token_address_{i}",
                            amount=0.01,
                            signal_id=PydanticObjectId(),
                            strategy_name="test_strategy"
                        )
                    
                    # 获取状态统计
                    status = await auto_trade_manager.get_status()
                    
                    # 验证统计信息包含预期字段
                    self.assertIn("auto_trade_enabled", status)
                    self.assertIn("channel_registry", status)
                    self.assertIn("trade_execution", status)


if __name__ == '__main__':
    unittest.main() 