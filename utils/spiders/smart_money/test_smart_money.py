import time
import statistics
from datetime import datetime
from typing import Dict, List, Tuple
from .smart_money import SmartMoneySpider

class SmartMoneySpiderTester:
    def __init__(self):
        self.spider = SmartMoneySpider()
        self.results: List[Dict] = []
        
    def test_request_frequency(
        self,
        total_requests: int = 50,
        interval_seconds: float = 1.0,
        ) -> <PERSON><PERSON>[float, List[Dict]]:
        """
        测试爬虫在指定频率下的性能
        
        Args:
            total_requests: 总请求次数
            interval_seconds: 请求间隔时间（秒）
            
        Returns:
            Tuple[float, List[Dict]]: 成功率和详细测试结果
        """
        success_count = 0
        test_results = []
        
        print(f"\n开始测试 - 总请求数: {total_requests}, 间隔时间: {interval_seconds}秒")
        print("-" * 50)
        
        for i in range(total_requests):
            start_time = time.time()
            try:
                result = self.spider.get_smart_money_wallets()
                success = bool(result)  # 如果返回空字典则认为失败
                if success:
                    success_count += 1
            except Exception as e:
                success = False
                result = str(e)
            
            end_time = time.time()
            response_time = end_time - start_time
            
            test_result = {
                "request_number": i + 1,
                "timestamp": datetime.now().isoformat(),
                "success": success,
                "response_time": response_time,
                "result": result if isinstance(result, dict) else str(result)
            }
            test_results.append(test_result)
            
            print(f"请求 {i+1}/{total_requests} - {'成功' if success else '失败'} "
                  f"- 响应时间: {response_time:.2f}秒")
            
            if i < total_requests - 1:  # 最后一次请求不需要等待
                time.sleep(interval_seconds)
        
        success_rate = (success_count / total_requests) * 100
        return success_rate, test_results
    
    def analyze_results(self, test_results: List[Dict]) -> Dict:
        """分析测试结果"""
        response_times = [r["response_time"] for r in test_results]
        successful_requests = [r for r in test_results if r["success"]]
        
        return {
            "总请求数": len(test_results),
            "成功请求数": len(successful_requests),
            "成功率": f"{(len(successful_requests) / len(test_results) * 100):.2f}%",
            "平均响应时间": f"{statistics.mean(response_times):.2f}秒",
            "最短响应时间": f"{min(response_times):.2f}秒",
            "最长响应时间": f"{max(response_times):.2f}秒",
            "响应时间中位数": f"{statistics.median(response_times):.2f}秒"
        }

def main():
    tester = SmartMoneySpiderTester()
    
    # 测试不同的请求间隔
    intervals = [0.5, 1, 2]  # 单位：秒
    requests_per_test = 10
    
    for interval in intervals:
        success_rate, results = tester.test_request_frequency(
            total_requests=requests_per_test,
            interval_seconds=interval
        )
        
        print(f"\n测试结果 (间隔: {interval}秒)")
        print("=" * 50)
        analysis = tester.analyze_results(results)
        for key, value in analysis.items():
            print(f"{key}: {value}")
        print("=" * 50)

if __name__ == "__main__":
    main() 