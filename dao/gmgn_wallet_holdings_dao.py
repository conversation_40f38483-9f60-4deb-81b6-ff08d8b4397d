from datetime import datetime
import traceback
from typing import List, Optional
from models.smart_money_holdings import SmartMoneyHoldings
import logging

logger = logging.getLogger(__name__)

class GmgnWalletHoldingsDAO:
    """GMGN钱包持仓信息数据访问对象"""
    
    async def upsert_holdings(self, chain: str, wallet_address: str, holdings: list) -> bool:
        """更新或插入钱包持仓信息
        
        Args:
            chain: 链名称
            wallet_address: 钱包地址
            data: API返回的数据
            
        Returns:
            bool: 是否成功
        """
        try:
            # 从API数据创建模型实例
            holdings = await SmartMoneyHoldings.from_api_data(chain, wallet_address, holdings)
            # 查找现有记录
            existing = await SmartMoneyHoldings.find_one({
                "chain": chain,
                "wallet_address": wallet_address
            })
            
            if existing:
                # 更新现有记录
                existing.holdings = holdings.holdings
                existing.updated_at = datetime.utcnow()
                await existing.save()
                logger.info(f"更新钱包 {wallet_address} 的持仓信息")
            else:
                # 创建新记录
                await holdings.insert()
                logger.info(f"创建钱包 {wallet_address} 的持仓信息")
            
            return True
            
        except Exception as e:
            logger.error(f"更新钱包 {wallet_address} 的持仓信息失败: {traceback.format_exc()}")
            return False
    
    async def get_holdings(
        self,
        chain: str,
        wallet_address: str
    ) -> Optional[SmartMoneyHoldings]:
        """获取钱包持仓信息
        
        Args:
            chain: 链名称
            wallet_address: 钱包地址
            
        Returns:
            Optional[SmartMoneyHoldings]: 持仓信息
        """
        try:
            holdings = await SmartMoneyHoldings.find_one({
                "chain": chain,
                "wallet_address": wallet_address
            })
            return holdings
        except Exception as e:
            logger.error(f"获取钱包 {wallet_address} 的持仓信息失败: {str(e)}")
            return None
    
    async def get_holdings_by_addresses(
        self,
        chain: str,
        wallet_addresses: List[str]
    ) -> List[SmartMoneyHoldings]:
        """批量获取钱包持仓信息
        
        Args:
            chain: 链名称
            wallet_addresses: 钱包地址列表
            
        Returns:
            List[SmartMoneyHoldings]: 持仓信息列表
        """
        try:
            holdings = await SmartMoneyHoldings.find({
                "chain": chain,
                "wallet_address": {"$in": wallet_addresses}
            }).to_list()
            return holdings
        except Exception as e:
            logger.error(f"批量获取钱包持仓信息失败: {str(e)}")
            return []
    
    async def delete_holdings(self, chain: str, wallet_address: str) -> bool:
        """删除钱包持仓信息
        
        Args:
            chain: 链名称
            wallet_address: 钱包地址
            
        Returns:
            bool: 是否成功
        """
        try:
            result = await SmartMoneyHoldings.find_one({
                "chain": chain,
                "wallet_address": wallet_address
            }).delete()
            return bool(result)
        except Exception as e:
            logger.error(f"删除钱包 {wallet_address} 的持仓信息失败: {str(e)}")
            return False 