import logging
import time
import json
from typing import Dict, Any, Optional

from utils.spiders.smart_money.authenticated_spider import AuthenticatedSpider

logger = logging.getLogger(__name__)

class GmgnAuthenticatedSpider(AuthenticatedSpider):
    """GMGN平台认证爬虫
    
    为GMGN平台API提供认证功能，包括：
    - 使用refresh_token刷新access_token
    - 处理GMGN API特定的认证错误和响应格式
    - 自动检测和刷新过期的access_token
    """
    
    REFRESH_TOKEN_URL = "https://gmgn.ai/account/account/refresh_access_token"
    TOKEN_EXPIRED_CODE = ********
    SUCCESS_CODE = 0
    
    def __init__(self, refresh_token: str, device_id: str, max_retries: int = 3, retry_interval: float = 1.0):
        """初始化GMGN认证爬虫
        
        Args:
            refresh_token: 用于刷新access_token的refresh_token
            device_id: 与refresh_token对应的device_id
            max_retries: 最大重试次数
            retry_interval: 重试间隔时间(秒)
        """
        super().__init__(max_retries=max_retries, retry_interval=retry_interval)
        self.refresh_token_value = refresh_token
        self.device_id = device_id  # 保存device_id
        self.headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-TW;q=0.6',
            'cache-control': 'no-cache',
            'content-type': 'application/json',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'origin': 'https://gmgn.ai',
            'referer': 'https://gmgn.ai/monitor/dDO6ZdYE?chain=sol',
            'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin'
        }
    
    async def setup(self):
        """设置爬虫，初始化会话并更新头信息"""
        await self.init_sessions()
        # 更新会话头信息
        self.session.headers.update(self.headers)
        # 设置浏览器模拟
        self.session.impersonate = "chrome110"  # 模拟Chrome浏览器
    
    def is_token_valid(self) -> bool:
        """检查当前access_token是否有效
        
        Returns:
            bool: 如果Token存在且未过期返回True，否则返回False
        """
        current_time = int(time.time())
        # Token不存在或已过期（提前30秒判断过期以避免边界情况）
        if not self.access_token or current_time > (self.expire_at - 30):
            return False
        return True
    
    async def refresh_token(self) -> bool:
        """刷新access_token
        
        Returns:
            bool: 刷新成功返回True，否则返回False
        """
        try:
            # 检查refresh_token是否为空
            if not self.refresh_token_value or self.refresh_token_value == "":
                logger.error("刷新Token失败: refresh_token为空")
                return False
            
            # 确保会话已初始化
            if not hasattr(self, 'session') or self.session is None:
                logger.info("会话未初始化，正在初始化...")
                await self.setup()
            
            logger.info("正在刷新access_token...")
            
            # 设置请求参数
            params = {
                'req_time': int(time.time() * 1000),
                'device_id': self.device_id,  # 使用实例的device_id
                'client_id': 'gmgn_web_20250510-908-8356458',
                'from_app': 'gmgn',
                'app_ver': '20250508-860-943999d',
                'tz_name': 'Asia/Shanghai',
                'tz_offset': '28800',
                'app_lang': 'zh-CN',
                'fp_did': '1d5274192395fd1dd9a0146ae3ba4639',
                'os': 'web'
            }
            
            # 请求数据
            data = {
                "refresh_token": self.refresh_token_value
            }
            
            logger.debug(f"刷新Token请求参数: {params}")
            logger.info(f"刷新Token请求数据: {data}")
            logger.info(f"刷新Token值(前30字符): {self.refresh_token_value[:30]}...") 
            
            # 使用POST方法发送请求
            response = await self.post(
                self.REFRESH_TOKEN_URL, 
                params=params, 
                json=data
            )
            
            # 检查响应状态
            if response.status_code != 200:
                logger.error(f"刷新Token失败: HTTP {response.status_code}")
                logger.error(f"错误响应: {response.text}")
                # 输出详细的请求信息以便调试
                logger.error(f"请求URL: {self.REFRESH_TOKEN_URL}")
                logger.error(f"请求参数: {params}")
                logger.error(f"请求数据: {data}")
                logger.error(f"请求头: {self.session.headers}")
                return False
            
            # 解析响应数据
            try:
                result = response.json()
                logger.debug(f"刷新Token响应: {result}")
                
                if result.get('code') != self.SUCCESS_CODE:
                    logger.error(f"刷新Token失败: {result.get('msg', '未知错误')}")
                    return False
                
                # 提取Token数据
                token_data = result.get('data', {}).get('data', {})
                if not token_data:
                    logger.error("响应中没有Token数据")
                    return False
                
                # 更新Token信息
                self.access_token = token_data.get('token')
                self.expire_at = token_data.get('expire_at', 0)
                
                logger.info(f"刷新Token成功，过期时间: {self.expire_at}")
                return True
                
            except json.JSONDecodeError as e:
                logger.error(f"解析Token响应失败: {e}")
                logger.error(f"原始响应: {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"刷新Token时发生错误: {e}")
            return False
    
    def check_response_valid(self, data: Dict[str, Any]) -> bool:
        """检查GMGN API响应是否有效（令牌是否过期）
        
        Args:
            data: API响应数据
            
        Returns:
            bool: 如果响应有效（令牌未过期）返回True，否则返回False
        """
        # 检查是否有Token过期错误码
        return data.get('code') != self.TOKEN_EXPIRED_CODE
    
    def is_response_success(self, data: Dict[str, Any]) -> bool:
        """检查GMGN API响应是否成功
        
        Args:
            data: API响应数据
            
        Returns:
            bool: 如果响应成功返回True，否则返回False
        """
        return data.get('code') == self.SUCCESS_CODE
    
    def get_error_message(self, data: Dict[str, Any]) -> str:
        """从GMGN API响应中获取错误消息
        
        Args:
            data: API响应数据
            
        Returns:
            str: 错误消息
        """
        return f"{data.get('msg', '未知错误')} (code: {data.get('code')})" 