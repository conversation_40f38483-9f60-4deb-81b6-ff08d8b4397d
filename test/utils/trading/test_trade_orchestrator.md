# TradeOrchestrator 测试文档

## 1. 测试目的

本测试套件旨在全面验证 `utils.trading.trade_orchestrator.TradeOrchestrator` 的核心交易编排逻辑，包括通道选择、故障转移、重试机制（包括常规重试和滑点重试）、超时处理以及统计信息收集。

## 2. 测试用例详细说明

### 2.1 基础设置和辅助类

#### MockTradeInterface 类
模拟交易接口，支持多种测试场景：
- `should_succeed`: 控制是否成功
- `fail_until_attempt`: 前N次失败，之后成功
- `error_message`: 自定义错误消息
- `execute_count`: 记录执行次数

#### 辅助函数
- `create_mock_retry_decision()`: 创建模拟重试决策对象
- `create_mock_adjustment_record()`: 创建模拟滑点调整记录

### 2.2 核心测试用例

#### test_execute_trade_success_first_channel
**测试目的**: 验证第一个渠道成功的交易执行

**测试步骤**:
1. 设置渠道选择结果为 ["gmgn", "jupiter"]
2. 配置 gmgn 渠道为成功执行
3. 执行交易请求
4. 验证结果状态为 SUCCESS
5. 验证成功渠道为 "gmgn"
6. 验证只有一次渠道尝试
7. 验证渠道实例不被关闭（修复后的行为）

**关键验证点**:
- 交易成功后不再关闭渠道实例，避免HTTP客户端过早关闭的Bug
- 渠道注册表被正确调用

#### test_execute_trade_failover_to_second_channel
**测试目的**: 测试故障转移到第二个渠道

**测试步骤**:
1. 创建失败的第一渠道（gmgn）
2. 创建成功的第二渠道（jupiter）
3. 设置渠道选择结果
4. 执行交易
5. 验证最终成功渠道为 jupiter

**关键验证点**:
- gmgn 配置的 max_retries 是 3，总共执行 4 次（1 + 3）
- jupiter 成功执行 1 次
- 两个渠道尝试记录正确

#### test_execute_trade_all_channels_fail
**测试目的**: 测试所有渠道都失败的情况

**测试步骤**:
1. 创建两个都失败的渠道
2. 设置不同的错误消息
3. 执行交易
4. 验证最终状态为 FAILED

**关键验证点**:
- 所有渠道都按配置的重试次数执行
- 所有尝试记录都标记为失败

#### test_execute_trade_no_channels_available
**测试目的**: 测试无可用渠道的情况

**测试步骤**:
1. 设置空的渠道列表
2. 执行交易
3. 验证错误信息包含 "no available channels"

#### test_execute_trade_timeout_handling
**测试目的**: 测试超时处理

**测试步骤**:
1. 模拟 `asyncio.wait_for` 抛出 `TimeoutError`
2. 执行交易
3. 验证超时被正确处理
4. 验证错误信息包含超时相关内容

#### test_execute_trade_exception_handling
**测试目的**: 测试异常处理

**测试步骤**:
1. 创建会抛出异常的渠道
2. 执行交易
3. 验证异常被正确捕获和处理

### 2.3 统计功能测试

#### test_get_execution_stats
**测试目的**: 测试统计信息获取

**验证内容**:
- total_trades: 总交易数
- successful_trades: 成功交易数
- failed_trades: 失败交易数
- fallback_count: 故障转移次数

#### test_get_execution_stats_empty
**测试目的**: 测试空统计的情况

#### test_reset_stats
**测试目的**: 测试重置统计

#### test_execution_stats_recording
**测试目的**: 测试执行统计记录

### 2.4 滑点重试测试

#### test_slippage_retry_with_delay_strategies
**测试目的**: 测试不同的重试间隔策略

**测试策略**:
- `FIXED`: 固定间隔 [0.1, 0.1]
- `LINEAR`: 线性增长 [0.1, 0.2]
- `EXPONENTIAL`: 指数增长 [0.1, 0.2]

**测试步骤**:
1. 为每种策略创建配置
2. 创建前2次失败、第3次成功的渠道
3. 验证延迟策略按预期工作
4. 验证最终交易成功

#### test_execute_trade_with_slippage_retry_success
**测试目的**: 测试滑点重试成功场景

**测试步骤**:
1. 创建模拟滑点错误的接口（前2次失败，第3次成功）
2. 配置滑点重试参数
3. 执行交易
4. 验证滑点重试信息被正确记录

**关键验证点**:
- 渠道尝试了3次
- 滑点重试功能启用
- 滑点调整次数大于0

#### test_execute_trade_slippage_retry_reaches_limit
**测试目的**: 测试滑点重试达到上限后继续重试

**测试步骤**:
1. 创建总是返回滑点错误的接口
2. 执行交易
3. 验证达到最大重试次数
4. 验证最终滑点不超过配置的最大值

#### test_execute_trade_non_slippage_error_no_slippage_adjustment
**测试目的**: 测试非滑点错误不触发滑点调整

**测试步骤**:
1. 创建返回非滑点错误的接口（如"insufficient funds"）
2. 执行交易
3. 验证没有进行滑点调整
4. 验证滑点值保持不变

#### test_execute_trade_slippage_retry_disabled
**测试目的**: 测试禁用滑点重试的执行

**测试步骤**:
1. 创建禁用滑点重试的配置
2. 创建返回滑点错误的接口
3. 执行交易
4. 验证滑点重试被禁用
5. 验证仍然进行标准重试但没有滑点调整

#### test_slippage_retry_triggered_for_jupiter_custom_error_6001
**测试目的**: 测试 Jupiter 错误 0x1771 (6001) 被识别为滑点并触发重试

**测试步骤**:
1. 配置 Mock Jupiter 渠道实例
2. 构建模拟的 0x1771 RPCException
3. 第一次调用抛出错误，第二次成功
4. 配置滑点重试参数
5. 执行交易并验证结果

**关键验证点**:
- `is_slippage_related_error` 被正确调用
- 滑点从10%调整到15%
- 交易最终成功
- 重试决策引擎被正确调用

#### test_direct_mock_call
**测试目的**: 测试直接调用 AsyncMock

## 3. 如何运行测试

### 3.1 环境准备
- 确保项目依赖已安装
- 测试主要依赖 Mock 对象，通常不需要真实外部连接

### 3.2 运行方式

**运行单个测试文件**:
```bash
python -m unittest test/utils/trading/test_trade_orchestrator.py
```

**使用 Pytest 运行**:
```bash
pytest test/utils/trading/test_trade_orchestrator.py
```

**运行特定测试用例**:
```bash
python -m unittest test.utils.trading.test_trade_orchestrator.TestTradeOrchestrator.test_execute_trade_success_first_channel
```

## 4. 主要Mock对象

- **ChannelSelector**: 模拟通道选择逻辑
- **ChannelRegistry**: 模拟通道配置和实例的注册与获取
- **TradeInterface** (通过 `MockTradeInterface`): 创建多种行为的模拟交易通道实例
- **RetryDecisionEngine**: 模拟重试决策逻辑
- **SlippageAdjustmentRecord**: 模拟滑点调整记录的创建
- **logging.Logger**: 验证日志输出
- **asyncio.wait_for**: 模拟超时

## 5. 测试配置

### 5.1 渠道配置
- **gmgn**: priority=1, timeout=60s, max_retries=3
- **jupiter**: priority=2, timeout=90s, max_retries=2

### 5.2 交易参数
- 默认买入金额: 0.01 SOL
- 默认滑点: 10%
- 滑点增量: 0.5%
- 最大滑点: 15%
- 重试延迟: 0.1秒

## 6. 注意事项

- 测试大量使用 `async/await` 和 `unittest.IsolatedAsyncioTestCase`
- `asyncSetUp` 用于设置每个异步测试用例共享的 Mock 对象和基本配置
- 部分辅助函数用来简化复杂 Mock 对象的创建
- 测试验证了修复后的行为：交易成功后不再关闭渠道实例
- 特别关注滑点重试机制的测试，包括不同错误类型的处理

此文档应随测试代码的更新而维护。