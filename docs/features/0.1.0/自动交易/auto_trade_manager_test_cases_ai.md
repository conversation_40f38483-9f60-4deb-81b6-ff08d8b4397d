# 统一自动交易管理器测试用例设计

**创建日期**: 2025-05-25  
**版本**: 0.1.0  
**目标**: 全面的测试用例设计，确保系统的可靠性和正确性

## 1. 测试范围概述

### 1.1 核心组件测试
- AutoTradeManager 主管理器
- ChannelRegistry 渠道注册表
- ChannelSelector 渠道选择器  
- TradeOrchestrator 交易编排器
- TradeRecordManager 交易记录管理器
- ConfigManager 配置管理器

### 1.2 集成测试
- 端到端交易流程
- 多渠道故障转移
- 配置动态更新

### 1.3 异常场景测试
- 网络异常处理
- 渠道失败处理
- 配置错误处理

## 2. 单元测试用例

### 2.1 AutoTradeManager 测试

#### 2.1.1 正常交易流程
**测试用例**: test_execute_trade_success
**前置条件**: 
- 配置了可用的交易渠道
- 有效的交易参数
**输入**: 
- trade_type: BUY
- input_token_address: SOL地址
- output_token_address: 测试代币地址
- amount_input_token: 1.0
- 有效的钱包信息和配置
**预期输出**: 
- TradeExecutionResult.final_status = SUCCESS
- successful_channel 不为空
- final_trade_record_id 有效
- channel_attempts 包含一次成功尝试

#### 2.1.2 所有渠道失败
**测试用例**: test_execute_trade_all_channels_fail
**前置条件**: 
- 配置了多个交易渠道
- 模拟所有渠道都失败
**输入**: 
- 正常的交易参数
- 模拟的渠道失败响应
**预期输出**: 
- TradeExecutionResult.final_status = FAILED
- successful_channel 为 None
- channel_attempts 包含所有渠道的失败尝试
- error_summary 描述失败原因

#### 2.1.3 故障转移成功
**测试用例**: test_execute_trade_fallback_success
**前置条件**: 
- 配置了优先级不同的多个渠道
- 第一个渠道失败，第二个成功
**输入**: 
- 正常的交易参数
- 模拟第一个渠道失败，第二个成功
**预期输出**: 
- TradeExecutionResult.final_status = SUCCESS
- successful_channel 为第二个渠道类型
- channel_attempts 包含两次尝试记录

### 2.2 ChannelRegistry 测试

#### 2.2.1 渠道注册
**测试用例**: test_register_channel
**前置条件**: 空的渠道注册表
**输入**: 
- channel_type: "gmgn"
- 有效的渠道实例
- 有效的渠道配置
**预期输出**: 
- 渠道成功注册
- get_channel("gmgn") 返回正确实例
- list_channels() 包含新注册的渠道

#### 2.2.2 重复注册处理
**测试用例**: test_register_duplicate_channel
**前置条件**: 已注册一个渠道
**输入**: 
- 相同类型的渠道注册请求
**预期输出**: 
- 抛出 ValueError 或更新现有渠道
- 原有渠道被替换

#### 2.2.3 获取不存在的渠道
**测试用例**: test_get_nonexistent_channel
**前置条件**: 空的渠道注册表
**输入**: 
- channel_type: "invalid_channel"
**预期输出**: 
- 抛出 ChannelNotFoundError

### 2.3 ChannelSelector 测试

#### 2.3.1 按优先级排序
**测试用例**: test_select_channels_by_priority
**前置条件**: 
- 注册了多个渠道，优先级分别为 2, 1, 3
- 所有渠道都启用
**输入**: 
- 正常的交易请求
**预期输出**: 
- 返回的渠道列表按优先级排序 [1, 2, 3]

#### 2.3.2 过滤禁用的渠道
**测试用例**: test_select_channels_filter_disabled
**前置条件**: 
- 注册了多个渠道，部分禁用
**输入**: 
- 正常的交易请求
**预期输出**: 
- 返回的渠道列表只包含启用的渠道

#### 2.3.3 空渠道列表处理
**测试用例**: test_select_channels_empty_list
**前置条件**: 
- 没有可用的渠道
**输入**: 
- 正常的交易请求
**预期输出**: 
- 返回空列表或抛出 NoAvailableChannelsError

### 2.4 TradeOrchestrator 测试

#### 2.4.1 单渠道成功执行
**测试用例**: test_execute_with_fallback_single_success
**前置条件**: 
- 配置了一个可用渠道
- 模拟渠道返回成功结果
**输入**: 
- 渠道列表：[gmgn_channel]
- 正常的交易请求
**预期输出**: 
- 执行结果状态为 SUCCESS
- 只有一次渠道尝试记录

#### 2.4.2 超时处理
**测试用例**: test_execute_with_timeout
**前置条件**: 
- 配置了超时时间为 5 秒的渠道
- 模拟渠道执行时间超过 5 秒
**输入**: 
- 会超时的交易请求
**预期输出**: 
- 渠道尝试状态为 FAILED
- 错误消息包含 "超时"

#### 2.4.3 重试机制
**测试用例**: test_execute_with_retry
**前置条件**: 
- 配置了最大重试次数为 3 的渠道
- 模拟前两次失败，第三次成功
**输入**: 
- 会重试的交易请求
**预期输出**: 
- 最终状态为 SUCCESS
- 渠道尝试记录有 3 次尝试

### 2.5 ConfigManager 测试

#### 2.5.1 配置加载
**测试用例**: test_get_config_from_database
**前置条件**: 
- 数据库中存在有效的配置
**输入**: 
- 配置获取请求
**预期输出**: 
- 返回正确的 AutoTradeConfig 对象
- 所有配置字段正确加载

#### 2.5.2 配置缓存
**测试用例**: test_config_caching
**前置条件**: 
- 设置了 5 分钟的缓存间隔
- 已加载过一次配置
**输入**: 
- 在缓存间隔内的配置获取请求
**预期输出**: 
- 不访问数据库
- 返回缓存的配置

#### 2.5.3 配置刷新
**测试用例**: test_config_refresh
**前置条件**: 
- 缓存已过期
- 数据库中配置已更新
**输入**: 
- 配置获取请求
**预期输出**: 
- 重新从数据库加载配置
- 返回最新的配置

## 3. 集成测试用例

### 3.1 端到端交易测试

#### 3.1.1 完整交易流程
**测试用例**: test_end_to_end_trade_success
**前置条件**: 
- 完整的系统配置
- 模拟的交易服务
**输入**: 
- 从策略触发到交易完成的完整流程
**预期输出**: 
- 交易成功完成
- 交易记录正确保存
- 通知正确发送

#### 3.1.2 多策略并发交易
**测试用例**: test_concurrent_trades
**前置条件**: 
- 多个策略同时触发交易
- 系统配置了并发限制
**输入**: 
- 10 个并发交易请求
**预期输出**: 
- 所有交易正确处理
- 遵守并发限制
- 无资源竞争问题

### 3.2 故障转移测试

#### 3.2.1 渠道故障转移
**测试用例**: test_channel_failover
**前置条件**: 
- 配置了 GMGN（优先级1）和 Solana Direct（优先级2）
- GMGN 服务不可用
**输入**: 
- 正常的买入交易请求
**预期输出**: 
- 自动切换到 Solana Direct
- 交易成功完成
- 记录包含两个渠道的尝试历史

#### 3.2.2 部分渠道恢复
**测试用例**: test_partial_channel_recovery
**前置条件**: 
- 初始时某些渠道不可用
- 在测试过程中渠道恢复
**输入**: 
- 持续的交易请求
**预期输出**: 
- 系统能检测到渠道恢复
- 新交易使用恢复的高优先级渠道

### 3.3 配置动态更新测试

#### 3.3.1 运行时配置更新
**测试用例**: test_runtime_config_update
**前置条件**: 
- 系统正在运行
- 更新数据库中的配置
**输入**: 
- 配置更新操作
- 新的交易请求
**预期输出**: 
- 系统在缓存刷新后使用新配置
- 不影响正在进行的交易

## 4. 异常场景测试

### 4.1 网络异常测试

#### 4.1.1 网络连接中断
**测试用例**: test_network_disconnection
**前置条件**: 
- 正常的网络连接
- 在交易过程中模拟网络中断
**输入**: 
- 交易请求 + 网络中断事件
**预期输出**: 
- 交易失败，状态为 FAILED
- 错误信息描述网络问题

#### 4.1.2 API 服务不可用
**测试用例**: test_api_service_unavailable
**前置条件**: 
- 模拟 API 返回 503 错误
**输入**: 
- 正常的交易请求
**预期输出**: 
- 自动重试
- 达到重试上限后切换渠道

### 4.2 数据异常测试

#### 4.2.1 无效的代币地址
**测试用例**: test_invalid_token_address
**前置条件**: 
- 正常的系统配置
**输入**: 
- output_token_address: "invalid_address"
**预期输出**: 
- 交易状态为 FAILED
- 错误信息描述代币地址无效

#### 4.2.2 余额不足
**测试用例**: test_insufficient_balance
**前置条件**: 
- 钱包余额低于交易金额
**输入**: 
- amount_input_token: 超过余额的金额
**预期输出**: 
- 交易状态为 FAILED
- 错误信息描述余额不足
- 不进行重试

### 4.3 配置错误测试

#### 4.3.1 无效的渠道配置
**测试用例**: test_invalid_channel_config
**前置条件**: 
- 配置了不存在的渠道类型
**输入**: 
- channel_type: "nonexistent_channel"
**预期输出**: 
- 抛出配置验证错误
- 系统使用默认配置或其他可用渠道

#### 4.3.2 缺失的必要配置
**测试用例**: test_missing_required_config
**前置条件**: 
- 配置缺少必要的字段
**输入**: 
- 不完整的配置
**预期输出**: 
- 抛出配置验证错误
- 提供清晰的错误信息

## 5. 性能测试用例

### 5.1 负载测试

#### 5.1.1 高并发交易
**测试用例**: test_high_concurrency_trades
**前置条件**: 
- 系统正常运行
**输入**: 
- 100 个并发交易请求
**预期输出**: 
- 所有交易在合理时间内完成
- 系统资源使用在可接受范围内

#### 5.1.2 长时间运行稳定性
**测试用例**: test_long_running_stability
**前置条件**: 
- 系统连续运行
**输入**: 
- 持续 24 小时的交易请求
**预期输出**: 
- 无内存泄漏
- 性能不显著下降

### 5.2 响应时间测试

#### 5.2.1 交易执行时间
**测试用例**: test_trade_execution_time
**前置条件**: 
- 正常的网络环境
**输入**: 
- 标准的交易请求
**预期输出**: 
- 单次交易时间 < 30 秒
- 99% 的交易在 60 秒内完成

## 6. 边界条件测试

### 6.1 极限值测试

#### 6.1.1 极小交易金额
**测试用例**: test_minimum_trade_amount
**前置条件**: 
- 系统正常配置
**输入**: 
- amount_input_token: 0.000001
**预期输出**: 
- 根据配置决定是否接受或拒绝

#### 6.1.2 极大交易金额
**测试用例**: test_maximum_trade_amount
**前置条件**: 
- 系统正常配置
**输入**: 
- amount_input_token: 1000000
**预期输出**: 
- 系统能正确处理大额交易

### 6.2 资源限制测试

#### 6.2.1 内存限制
**测试用例**: test_memory_limits
**前置条件**: 
- 限制系统可用内存
**输入**: 
- 大量并发交易请求
**预期输出**: 
- 系统优雅降级
- 不发生内存溢出

## 7. 测试数据准备

### 7.1 Mock 数据

#### 7.1.1 成功交易响应
```python
MOCK_SUCCESS_RESPONSE = {
    "status": "success",
    "tx_hash": "5f7d8b9c...",
    "amount_in": 1000000000,  # 1 SOL in lamports
    "amount_out": 1500000,    # 1.5M tokens
    "executed_at": "2025-05-25T12:00:00Z"
}
```

#### 7.1.2 失败交易响应
```python
MOCK_FAILURE_RESPONSE = {
    "status": "failed", 
    "error": "insufficient funds",
    "error_code": "INSUFFICIENT_BALANCE"
}
```

### 7.2 测试配置

#### 7.2.1 标准配置
```python
TEST_CONFIG = {
    "enabled": True,
    "channels": [
        {
            "channel_type": "gmgn",
            "priority": 1,
            "enabled": True,
            "timeout_seconds": 30,
            "max_retries": 2
        },
        {
            "channel_type": "solana_direct", 
            "priority": 2,
            "enabled": True,
            "timeout_seconds": 45,
            "max_retries": 1
        }
    ]
}
```

## 8. 测试执行策略

### 8.1 持续集成
- 每次代码提交触发单元测试
- 每日执行集成测试
- 每周执行性能测试

### 8.2 测试环境
- 开发环境：快速单元测试
- 测试环境：完整集成测试  
- 预生产环境：性能和压力测试

## 9. 通知功能测试用例

### 9.1 交易失败通知

#### 9.1.1 交易失败时发送通知
**测试用例**: test_notification_on_trade_failure
**前置条件**: 
- 配置了通知功能（notify_on_failure=True）
- 设置了管理员Chat ID列表
- 交易执行失败
**输入**: 
- 失败的交易执行结果
- 通知配置包含管理员ID
**预期输出**: 
- 向所有配置的管理员发送失败通知
- 通知内容包含策略名称、代币地址、失败原因
- 通知格式为"🚨 Auto-Trade FAILED 🚨"

#### 9.1.2 禁用失败通知时不发送
**测试用例**: test_notification_disabled  
**前置条件**: 
- 配置了通知功能（notify_on_failure=False）
- 交易执行失败
**输入**: 
- 失败的交易执行结果
- 禁用失败通知的配置
**预期输出**: 
- 不发送任何通知
- message_sender.send_message_to_user 未被调用

### 9.2 故障转移通知

#### 9.2.1 故障转移成功时发送通知
**测试用例**: test_notification_on_fallback_success
**前置条件**: 
- 配置了故障转移通知（notify_on_fallback=True）
- 第一个渠道失败，第二个渠道成功
**输入**: 
- 成功的交易执行结果（包含多个渠道尝试）
- 通知配置启用故障转移通知
**预期输出**: 
- 发送故障转移成功通知
- 通知内容包含失败的渠道和成功的渠道
- 通知格式为"⚠️ Auto-Trade: Fallback Success ⚠️"

### 9.3 配置异常测试

#### 9.3.1 没有配置管理员Chat ID
**测试用例**: test_notification_no_admin_chat_ids
**前置条件**: 
- 通知功能已启用
- admin_chat_ids 为空列表
**输入**: 
- 任何交易执行结果
- 空的管理员ID配置
**预期输出**: 
- 不发送任何通知
- 系统正常运行，不抛出异常

#### 9.3.2 消息发送失败处理
**测试用例**: test_notification_message_sender_failure
**前置条件**: 
- 通知功能已启用且配置正确
- 模拟消息发送失败（网络错误等）
**输入**: 
- 需要发送通知的交易结果
- 消息发送器抛出异常
**预期输出**: 
- 系统不因消息发送失败而崩溃
- 交易逻辑正常完成
- 记录发送失败的错误日志

### 9.4 通知内容测试

#### 9.4.1 通知消息内容验证
**测试用例**: test_build_notification_message_content
**前置条件**: 
- 有效的通知配置
- 完整的交易执行结果
**输入**: 
- 包含详细信息的交易执行结果
- 包含策略名称、信号ID、代币地址等上下文
**预期输出**: 
- 生成正确格式的通知消息
- 包含所有必要的信息字段
- 消息格式符合Telegram HTML规范

#### 9.4.2 通知消息详情控制
**测试用例**: test_notification_detail_control
**前置条件**: 
- 配置include_trade_details=false
**输入**: 
- 交易执行结果
- 禁用详情的通知配置
**预期输出**: 
- 生成简化的通知消息
- 不包含渠道尝试详情和执行时间

### 9.5 消息发送器初始化测试

#### 9.5.1 自动初始化消息发送器
**测试用例**: test_message_sender_initialization
**前置条件**: 
- 配置了有效的管理员Chat ID
**输入**: 
- AutoTradeManager初始化
**预期输出**: 
- 自动创建TelegramMessageSender实例
- message_sender不为None

#### 9.5.2 无管理员配置时不初始化
**测试用例**: test_no_message_sender_when_no_admins
**前置条件**: 
- 管理员Chat ID列表为空
**输入**: 
- AutoTradeManager初始化
**预期输出**: 
- message_sender为None
- 系统正常运行

### 9.6 集成测试

#### 9.6.1 完整交易流程通知测试
**测试用例**: test_end_to_end_notification_flow
**前置条件**: 
- 完整的系统配置
- 真实的消息发送器（或高度仿真的Mock）
**输入**: 
- 完整的交易请求
**预期输出**: 
- 交易执行完成
- 根据结果发送相应通知
- 通知内容准确反映交易状态

### 9.7 Mock对象验证

#### 9.7.1 验证通知调用次数
**前置条件**: 
- 配置了N个管理员
- 触发通知条件
**预期输出**: 
- send_message_to_user被调用N次
- 每次调用的参数正确

#### 9.7.2 验证通知调用参数
**前置条件**: 
- 完整的测试场景
**预期输出**: 
- 消息内容参数正确
- Chat ID参数正确
- 调用顺序符合预期

## 10. 测试数据模板

### 10.1 通知配置模板

```python
# 完整通知配置
FULL_NOTIFICATION_CONFIG = NotificationConfig(
    notify_on_failure=True,
    notify_on_fallback=True,
    admin_chat_ids=["123456789", "987654321"],
    include_trade_details=True
)

# 禁用通知配置
DISABLED_NOTIFICATION_CONFIG = NotificationConfig(
    notify_on_failure=False,
    notify_on_fallback=False,
    admin_chat_ids=[],
    include_trade_details=False
)
```

### 10.2 执行结果模板

```python
# 失败执行结果
FAILED_EXECUTION_RESULT = TradeExecutionResult(
    final_status=TradeStatus.FAILED,
    successful_channel=None,
    channel_attempts=[
        ChannelAttemptResult(
            channel_type="gmgn",
            status=TradeStatus.FAILED,
            error_message="Insufficient funds"
        )
    ],
    error_summary="All channels failed"
)

# 故障转移成功结果
FALLBACK_SUCCESS_RESULT = TradeExecutionResult(
    final_status=TradeStatus.SUCCESS,
    successful_channel="solana_direct",
    channel_attempts=[
        ChannelAttemptResult(channel_type="gmgn", status=TradeStatus.FAILED),
        ChannelAttemptResult(channel_type="solana_direct", status=TradeStatus.SUCCESS)
    ]
)
``` 