#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统计计算器测试用例

测试各种统计计算功能
"""

import unittest
from unittest.mock import MagicMock, AsyncMock
from datetime import datetime, timezone
from beanie import PydanticObjectId

from utils.trading.statistics.statistics_calculator import StatisticsCalculator
from utils.trading.statistics.models import (
    TradePair, OverallStats, TokenStats, StrategyStats,
    ProfitRanking, LossRanking
)


class TestStatisticsCalculator(unittest.IsolatedAsyncioTestCase):
    """统计计算器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.calculator = StatisticsCalculator()
    
    def create_trade_pair(
        self,
        signal_id: str,
        buy_record_id: str,
        sell_record_id: str,
        profit_rate: float,
        token_address: str = "test_token",
        strategy_name: str = "test_strategy",
        holding_duration: float = 1.0,
        buy_amount_sol: float = 100.0
    ) -> TradePair:
        """创建测试交易对"""
        profit_amount = buy_amount_sol * (profit_rate / 100.0)
        sell_amount_sol = buy_amount_sol + profit_amount
        is_profitable = profit_rate > 0
        
        buy_time = datetime(2024, 1, 1, 10, 0, 0, tzinfo=timezone.utc)
        sell_time = datetime(2024, 1, 1, 10 + int(holding_duration), 0, 0, tzinfo=timezone.utc)
        
        return TradePair(
            signal_id=signal_id,
            strategy_name=strategy_name,
            token_address=token_address,
            buy_record_id=buy_record_id,
            sell_record_id=sell_record_id,
            buy_amount_sol=buy_amount_sol,
            sell_amount_sol=sell_amount_sol,
            profit_rate=profit_rate,
            profit_amount=profit_amount,
            is_profitable=is_profitable,
            buy_time=buy_time,
            sell_time=sell_time,
            holding_duration=holding_duration
        )
    
    def test_calculate_overall_stats_basic(self):
        """测试基本的总体统计计算"""
        # 创建测试数据：3个盈利，2个亏损
        trade_pairs = [
            self.create_trade_pair("signal1", "507f1f77bcf86cd799439011", "507f1f77bcf86cd799439012", 10.0),   # 盈利10%
            self.create_trade_pair("signal2", "507f1f77bcf86cd799439013", "507f1f77bcf86cd799439014", 20.0),   # 盈利20%
            self.create_trade_pair("signal3", "507f1f77bcf86cd799439015", "507f1f77bcf86cd799439016", 5.0),    # 盈利5%
            self.create_trade_pair("signal4", "507f1f77bcf86cd799439017", "507f1f77bcf86cd799439018", -10.0),  # 亏损10%
            self.create_trade_pair("signal5", "507f1f77bcf86cd799439019", "507f1f77bcf86cd799439020", -5.0),   # 亏损5%
        ]
        
        # 执行计算
        stats = self.calculator._calculate_overall_stats(trade_pairs)
        
        # 验证结果
        self.assertEqual(stats.total_trades, 5)
        self.assertEqual(stats.total_win_rate, 60.0)  # 3/5 = 60%
        self.assertEqual(stats.total_profit_rate, 20.0)  # 10+20+5-10-5 = 20%
        self.assertEqual(stats.avg_profit_rate, 4.0)  # 20/5 = 4%
    
    def test_calculate_overall_stats_empty(self):
        """测试空交易对列表的总体统计"""
        trade_pairs = []
        
        # 执行计算
        stats = self.calculator._calculate_overall_stats(trade_pairs)
        
        # 验证结果
        self.assertEqual(stats.total_trades, 0)
        self.assertEqual(stats.total_win_rate, 0.0)
        self.assertEqual(stats.total_profit_rate, 0.0)
        self.assertEqual(stats.avg_profit_rate, 0.0)
    
    def test_calculate_overall_stats_all_wins(self):
        """测试全部盈利的情况"""
        trade_pairs = [
            self.create_trade_pair("signal1", "507f1f77bcf86cd799439011", "507f1f77bcf86cd799439012", 10.0),
            self.create_trade_pair("signal2", "507f1f77bcf86cd799439013", "507f1f77bcf86cd799439014", 20.0),
            self.create_trade_pair("signal3", "507f1f77bcf86cd799439015", "507f1f77bcf86cd799439016", 5.0),
        ]
        
        # 执行计算
        stats = self.calculator._calculate_overall_stats(trade_pairs)
        
        # 验证结果
        self.assertEqual(stats.total_trades, 3)
        self.assertEqual(stats.total_win_rate, 100.0)  # 3/3 = 100%
        self.assertEqual(stats.total_profit_rate, 35.0)  # 10+20+5 = 35%
        self.assertAlmostEqual(stats.avg_profit_rate, 11.67, places=2)  # 35/3 ≈ 11.67%
    
    def test_calculate_overall_stats_all_losses(self):
        """测试全部亏损的情况"""
        trade_pairs = [
            self.create_trade_pair("signal1", "507f1f77bcf86cd799439011", "507f1f77bcf86cd799439012", -10.0),
            self.create_trade_pair("signal2", "507f1f77bcf86cd799439013", "507f1f77bcf86cd799439014", -20.0),
            self.create_trade_pair("signal3", "507f1f77bcf86cd799439015", "507f1f77bcf86cd799439016", -5.0),
        ]
        
        # 执行计算
        stats = self.calculator._calculate_overall_stats(trade_pairs)
        
        # 验证结果
        self.assertEqual(stats.total_trades, 3)
        self.assertEqual(stats.total_win_rate, 0.0)  # 0/3 = 0%
        self.assertEqual(stats.total_profit_rate, -35.0)  # -10-20-5 = -35%
        self.assertAlmostEqual(stats.avg_profit_rate, -11.67, places=2)  # -35/3 ≈ -11.67%
    
    def test_calculate_token_stats(self):
        """测试Token统计计算"""
        # 创建不同Token的交易对
        trade_pairs = [
            # Token A: 2个盈利，1个亏损
            self.create_trade_pair("signal1", "507f1f77bcf86cd799439011", "507f1f77bcf86cd799439012", 10.0, "token_a"),
            self.create_trade_pair("signal2", "507f1f77bcf86cd799439013", "507f1f77bcf86cd799439014", 20.0, "token_a"),
            self.create_trade_pair("signal3", "507f1f77bcf86cd799439015", "507f1f77bcf86cd799439016", -10.0, "token_a"),
            
            # Token B: 1个盈利，1个亏损
            self.create_trade_pair("signal4", "507f1f77bcf86cd799439017", "507f1f77bcf86cd799439018", 5.0, "token_b"),
            self.create_trade_pair("signal5", "507f1f77bcf86cd799439019", "507f1f77bcf86cd799439020", -5.0, "token_b"),
        ]
        
        # 执行计算
        token_stats = self.calculator._calculate_token_stats(trade_pairs)
        
        # 验证结果
        self.assertEqual(len(token_stats), 2)
        
        # 验证Token A统计（应该排在第一位，因为总盈利率更高）
        token_a_stats = next(stats for stats in token_stats if stats.token_address == "token_a")
        self.assertEqual(token_a_stats.trade_count, 3)
        self.assertAlmostEqual(token_a_stats.win_rate, 66.67, places=2)  # 2/3 ≈ 66.67%
        self.assertEqual(token_a_stats.total_profit_amount, 20.0)  # 10+20-10 = 20%
        
        # 验证Token B统计
        token_b_stats = next(stats for stats in token_stats if stats.token_address == "token_b")
        self.assertEqual(token_b_stats.trade_count, 2)
        self.assertEqual(token_b_stats.win_rate, 50.0)  # 1/2 = 50%
        self.assertEqual(token_b_stats.total_profit_amount, 0.0)  # 5-5 = 0%
    
    def test_calculate_token_stats_empty(self):
        """测试空交易对列表的Token统计"""
        trade_pairs = []
        
        # 执行计算
        token_stats = self.calculator._calculate_token_stats(trade_pairs)
        
        # 验证结果
        self.assertEqual(len(token_stats), 0)
    
    def test_calculate_strategy_stats(self):
        """测试策略统计计算"""
        # 创建不同策略的交易对
        trade_pairs = [
            # 策略A: 包含最大盈利和最大亏损
            self.create_trade_pair("signal1", "507f1f77bcf86cd799439011", "507f1f77bcf86cd799439012", 30.0, strategy_name="strategy_a"),
            self.create_trade_pair("signal2", "507f1f77bcf86cd799439013", "507f1f77bcf86cd799439014", 10.0, strategy_name="strategy_a"),
            self.create_trade_pair("signal3", "507f1f77bcf86cd799439015", "507f1f77bcf86cd799439016", -20.0, strategy_name="strategy_a"),
            
            # 策略B: 较小的盈亏
            self.create_trade_pair("signal4", "507f1f77bcf86cd799439017", "507f1f77bcf86cd799439018", 5.0, strategy_name="strategy_b"),
            self.create_trade_pair("signal5", "507f1f77bcf86cd799439019", "507f1f77bcf86cd799439020", -5.0, strategy_name="strategy_b"),
        ]
        
        # 执行计算
        strategy_stats = self.calculator._calculate_strategy_stats(trade_pairs)
        
        # 验证结果
        self.assertEqual(len(strategy_stats), 2)
        
        # 验证策略A统计（应该排在第一位，因为总盈利率更高）
        strategy_a_stats = next(stats for stats in strategy_stats if stats.strategy_name == "strategy_a")
        self.assertEqual(strategy_a_stats.trade_count, 3)
        self.assertAlmostEqual(strategy_a_stats.win_rate, 66.67, places=2)  # 2/3 ≈ 66.67%
        self.assertEqual(strategy_a_stats.total_profit_amount, 20.0)  # 30+10-20 = 20%
        
        # 验证策略B统计
        strategy_b_stats = next(stats for stats in strategy_stats if stats.strategy_name == "strategy_b")
        self.assertEqual(strategy_b_stats.trade_count, 2)
        self.assertAlmostEqual(strategy_b_stats.win_rate, 50.0, places=2)  # 1/2 = 50%
        self.assertEqual(strategy_b_stats.total_profit_amount, 0.0)  # 5-5 = 0%
    
    def test_calculate_strategy_stats_empty(self):
        """测试空交易对列表的策略统计"""
        trade_pairs = []
        
        # 执行计算
        strategy_stats = self.calculator._calculate_strategy_stats(trade_pairs)
        
        # 验证结果
        self.assertEqual(len(strategy_stats), 0)
    
    def test_calculate_profit_rankings(self):
        """测试盈利排行榜生成"""
        # 创建包含盈利和亏损的交易对
        trade_pairs = [
            self.create_trade_pair("signal1", "507f1f77bcf86cd799439011", "507f1f77bcf86cd799439012", 10.0, "token_a", "strategy_a"),
            self.create_trade_pair("signal2", "507f1f77bcf86cd799439013", "507f1f77bcf86cd799439014", -30.0, "token_b", "strategy_b"),
            self.create_trade_pair("signal3", "507f1f77bcf86cd799439015", "507f1f77bcf86cd799439016", -10.0, "token_c", "strategy_c"),
            self.create_trade_pair("signal4", "507f1f77bcf86cd799439017", "507f1f77bcf86cd799439018", 20.0, "token_d", "strategy_d"),
            self.create_trade_pair("signal5", "507f1f77bcf86cd799439019", "507f1f77bcf86cd799439020", -5.0, "token_e", "strategy_e"),
        ]
        
        # 执行计算
        profit_rankings = self.calculator._calculate_profit_rankings(trade_pairs)
        
        # 验证结果：只包含盈利的交易，按盈利金额降序排列
        self.assertEqual(len(profit_rankings), 2)  # 只有2个盈利交易
        
        # 验证排序：第一个应该是盈利最高的
        self.assertEqual(profit_rankings[0].signal_id, "signal4")  # 20%盈利
        self.assertEqual(profit_rankings[0].profit_rate, 20.0)
        self.assertEqual(profit_rankings[1].signal_id, "signal1")  # 10%盈利
        self.assertEqual(profit_rankings[1].profit_rate, 10.0)
    
    def test_calculate_profit_rankings_empty(self):
        """测试没有盈利交易的盈利排行榜"""
        # 创建只有亏损的交易对
        trade_pairs = [
            self.create_trade_pair("signal1", "507f1f77bcf86cd799439011", "507f1f77bcf86cd799439012", -10.0),
            self.create_trade_pair("signal2", "507f1f77bcf86cd799439013", "507f1f77bcf86cd799439014", -20.0),
        ]
        
        # 执行计算
        profit_rankings = self.calculator._calculate_profit_rankings(trade_pairs)
        
        # 验证结果：应该为空
        self.assertEqual(len(profit_rankings), 0)
    
    def test_calculate_loss_rankings(self):
        """测试亏损排行榜生成"""
        # 创建包含盈利和亏损的交易对
        trade_pairs = [
            self.create_trade_pair("signal1", "507f1f77bcf86cd799439011", "507f1f77bcf86cd799439012", 10.0, "token_a", "strategy_a"),
            self.create_trade_pair("signal2", "507f1f77bcf86cd799439013", "507f1f77bcf86cd799439014", -30.0, "token_b", "strategy_b"),
            self.create_trade_pair("signal3", "507f1f77bcf86cd799439015", "507f1f77bcf86cd799439016", -10.0, "token_c", "strategy_c"),
            self.create_trade_pair("signal4", "507f1f77bcf86cd799439017", "507f1f77bcf86cd799439018", 20.0, "token_d", "strategy_d"),
            self.create_trade_pair("signal5", "507f1f77bcf86cd799439019", "507f1f77bcf86cd799439020", -5.0, "token_e", "strategy_e"),
        ]
        
        # 执行计算
        loss_rankings = self.calculator._calculate_loss_rankings(trade_pairs)
        
        # 验证结果：只包含亏损的交易，按亏损金额升序排列（从接近0到最亏）
        self.assertEqual(len(loss_rankings), 3)  # 只有3个亏损交易
        
        # 验证排序：第一个应该是亏损最小的（接近0）
        self.assertEqual(loss_rankings[0].signal_id, "signal5")  # -5%亏损
        self.assertEqual(loss_rankings[0].loss_rate, 5.0)  # 显示为正数
        self.assertEqual(loss_rankings[1].signal_id, "signal3")  # -10%亏损
        self.assertEqual(loss_rankings[1].loss_rate, 10.0)
        self.assertEqual(loss_rankings[2].signal_id, "signal2")  # -30%亏损
        self.assertEqual(loss_rankings[2].loss_rate, 30.0)
    
    def test_calculate_loss_rankings_empty(self):
        """测试没有亏损交易的亏损排行榜"""
        # 创建只有盈利的交易对
        trade_pairs = [
            self.create_trade_pair("signal1", "507f1f77bcf86cd799439011", "507f1f77bcf86cd799439012", 10.0),
            self.create_trade_pair("signal2", "507f1f77bcf86cd799439013", "507f1f77bcf86cd799439014", 20.0),
        ]
        
        # 执行计算
        loss_rankings = self.calculator._calculate_loss_rankings(trade_pairs)
        
        # 验证结果：应该为空
        self.assertEqual(len(loss_rankings), 0)
    
    def test_calculate_statistics_complete(self):
        """测试完整的统计计算"""
        # 创建测试数据
        trade_pairs = [
            self.create_trade_pair("signal1", "507f1f77bcf86cd799439011", "507f1f77bcf86cd799439012", 10.0, "token_a", "strategy_a"),
            self.create_trade_pair("signal2", "507f1f77bcf86cd799439013", "507f1f77bcf86cd799439014", -10.0, "token_b", "strategy_b"),
        ]
        
        # 执行计算
        result = self.calculator.calculate_statistics(trade_pairs)
        
        # 验证结果结构
        self.assertIsNotNone(result.overall_stats)
        self.assertIsInstance(result.token_stats, list)
        self.assertIsInstance(result.strategy_stats, list)
        self.assertIsInstance(result.profit_rankings, list)
        self.assertIsInstance(result.loss_rankings, list)
        
        # 验证基本数据
        self.assertEqual(result.overall_stats.total_trades, 2)
        self.assertEqual(len(result.token_stats), 2)
        self.assertEqual(len(result.strategy_stats), 2)
        self.assertEqual(len(result.profit_rankings), 1)
        self.assertEqual(len(result.loss_rankings), 1)


if __name__ == '__main__':
    unittest.main() 