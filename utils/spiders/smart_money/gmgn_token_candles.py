import logging
from typing import Dict, Any, List
import json
import asyncio
from utils.spiders.smart_money import BasicSpider


logger = logging.getLogger(__name__)


class GmgnTokenCandlesSpider(BasicSpider):
    """GMGN Token蜡烛图爬虫"""
    
    BASE_URL = "https://gmgn.ai/api/v1/token_candles/sol"
    
    def __init__(self, max_retries: int = 5, retry_interval: float = 1.0):
        super().__init__(max_retries=max_retries, retry_interval=retry_interval)
        self.headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-TW;q=0.6',
            'cache-control': 'no-cache',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://gmgn.ai/sol/token/dDO6ZdYE_4TBi66vi32S7J8X1A6eWfaLHYmUXu7CStcEmsJQdpump?tag=kol&filter=buy',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
        }
        
    async def setup(self):
        """设置爬虫，初始化会话并更新头信息"""
        await self.init_sessions()
        # 更新会话头信息
        self.session.headers.update(self.headers)
        # 设置浏览器模拟
        self.session.impersonate = "chrome110"  # 模拟Chrome浏览器
        
    async def get_price_point_in_time(self, address: str, timestamp: int, resolution: str = "1m") -> Dict[str, Any]:
        """
        获取指定地址在指定时间点的价格点
        
        Args:
            address (str): 代币地址
            timestamp (int): 时间戳, 单位: 秒
            resolution (str): 分辨率, 可选值: "1s", "30s", "1m", "1h", "4h", "1d"
        Returns:
            Dict[str, Any]: 价格点数据
        """
        # 将秒转换为毫秒
        timestamp_ms = timestamp * 1000
        # 只需要一个数据点
        limit = 1
        
        # 构建URL，直接请求指定地址的价格数据
        url = f"{self.BASE_URL}/{address}"
        
        response = await self.get(url, params={
                'device_id': '65990bc3-0a13-4d99-8c66-eeea94916b0f',
                'client_id': 'gmgn_web_2025.0227.180046',
                'from_app': 'gmgn',
                'app_ver': '2025.0327.173932',
                'tz_name': 'Asia/Shanghai',
                'tz_offset': '28800',
                'app_lang': 'zh-CN',
                'fp_did': 'd36bfabad5afd78eb80f0c5a410c93fc',
                'resolution': resolution,
                'from': 0,
                'to': timestamp_ms,
                'limit': limit
            })
        if response and response.status_code == 200:
            data = response.json()
            if data.get("code") == 0 and data.get("data") and data["data"].get("list"):
                # 返回找到的第一个价格点
                return data["data"]["list"][0]
            else:
                logger.warning(f"获取价格点失败: {data.get('message', '未知错误')}")
                return {}
        else:
            logger.error(f"请求失败: {response.status if response else '无响应'}")
            return {}
            
    async def get_price_range(self, address: str, from_timestamp: int, to_timestamp: int, 
                             resolution: str = "1m", limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取指定地址在时间范围内的价格数据
        
        Args:
            address (str): 代币地址
            from_timestamp (int): 开始时间戳, 单位: 秒
            to_timestamp (int): 结束时间戳, 单位: 秒
            resolution (str): 分辨率, 可选值: "1s", "30s", "1m", "1h", "4h", "1d"
            limit (int): 返回结果数量限制
        Returns:
            List[Dict[str, Any]]: 价格数据列表
        """
        # 将秒转换为毫秒
        from_ms = from_timestamp * 1000
        to_ms = to_timestamp * 1000
        
        # 构建URL，请求时间范围内的价格数据
        url = f"{self.BASE_URL}/{address}?resolution={resolution}&from={from_ms}&to={to_ms}&limit={limit}"
        
        response = await self.get(url)
        if response and response.status == 200:
            data = await response.json()
            if data.get("code") == 0 and data.get("data") and data["data"].get("list"):
                return data["data"]["list"]
            else:
                logger.warning(f"获取价格范围失败: {data.get('message', '未知错误')}")
                return []
        else:
            logger.error(f"请求失败: {response.status if response else '无响应'}")
            return []
        

if __name__ == "__main__":
    asyncio.run(
        GmgnTokenCandlesSpider().get_price_point_in_time(
            address="4TBi66vi32S7J8X1A6eWfaLHYmUXu7CStcEmsJQdpump",
            timestamp=1743057840000,
            resolution="1m"
        )
    )
