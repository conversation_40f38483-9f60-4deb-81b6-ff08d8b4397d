"""
GMGN钱包统计数据访问对象

功能：
- 管理gmgn_wallet_stats集合的数据访问
- 实现基于钱包地址和时间窗口的查询和更新
- 支持批量操作和性能优化
- 提供与kol_wallets表的关联验证
"""

from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from beanie import PydanticObjectId
import logging

from models.gmgn_wallet_stats import GmgnWalletStats
from models.kol_wallet import KOLWallet

logger = logging.getLogger(__name__)


class GmgnWalletStatsDAO:
    """GMGN钱包统计数据访问对象"""
    
    def __init__(self):
        """初始化DAO"""
        self.collection_name = "gmgn_wallet_stats"
    
    async def find_wallets_need_stats_update(
        self, 
        limit: int = 50, 
        hours_threshold: int = 1, 
        period: str = "all"
    ) -> List[str]:
        """
        获取需要更新统计数据的钱包地址列表
        
        Args:
            limit: 返回的钱包数量限制
            hours_threshold: 更新时间阈值（小时）
            period: 时间窗口过滤器
            
        Returns:
            需要更新的钱包地址列表
        """
        try:
            # 计算时间阈值
            threshold_time = datetime.now() - timedelta(hours=hours_threshold)
            
            # 查询kol_wallets表获取KOL钱包地址
            # 查询tag为"kol"的钱包或tags列表包含"kol"的钱包
            kol_wallets = await KOLWallet.find(
                {
                    "$or": [
                        {"tag": "kol"},
                        {"tags": {"$in": ["kol"]}}
                    ]
                }
            ).to_list()
            
            if not kol_wallets:
                logger.warning("No active KOL wallets found")
                return []
            
            kol_wallet_addresses = [wallet.wallet_address for wallet in kol_wallets]
            
            # 查询已存在的统计数据，过滤掉最近已更新的钱包
            recent_updates = await GmgnWalletStats.find(
                {
                    "wallet_address": {"$in": kol_wallet_addresses},
                    "period": period,
                    "updated_at": {"$gte": threshold_time}
                }
            ).to_list()
            
            recent_updated_addresses = {stats.wallet_address for stats in recent_updates}
            
            # 返回需要更新的地址列表
            wallets_need_update = [
                addr for addr in kol_wallet_addresses 
                if addr not in recent_updated_addresses
            ]
            
            logger.info(
                f"Found {len(wallets_need_update)} wallets need stats update for period {period}, "
                f"threshold: {hours_threshold}h"
            )
            
            return wallets_need_update[:limit]
            
        except Exception as e:
            logger.error(f"Error finding wallets need stats update: {e}")
            return []
    
    async def upsert_wallet_stats(
        self, 
        wallet_address: str, 
        stats_data: Dict[str, Any], 
        period: str = "all"
    ) -> Dict[str, Any]:
        """
        新增或更新单个钱包的统计数据
        
        Args:
            wallet_address: 钱包地址
            stats_data: 统计数据字典
            period: 时间窗口
            
        Returns:
            操作结果字典
        """
        try:
            # 验证钱包地址是否在kol_wallets表中存在
            kol_wallet = await KOLWallet.find_one(
                KOLWallet.wallet_address == wallet_address
            )
            
            if not kol_wallet:
                logger.warning(f"Wallet {wallet_address} not found in kol_wallets table")
                return {
                    "success": False,
                    "error": f"Wallet {wallet_address} not found in kol_wallets table",
                    "wallet_address": wallet_address
                }
            
            # 使用模型的create_from_api_data方法创建实例
            stats_instance = GmgnWalletStats.create_from_api_data(
                wallet_address=wallet_address,
                api_data=stats_data,
                period=period
            )
            
            # 基于复合键进行upsert操作
            filter_query = {
                "wallet_address": wallet_address,
                "chain": "sol",
                "period": period
            }
            
            # 更新时间戳
            stats_instance.updated_at = datetime.now()
            
            # 执行upsert操作
            result = await GmgnWalletStats.find_one(filter_query)
            
            if result:
                # 更新现有记录
                update_data = stats_instance.model_dump(exclude={"id", "created_at"})
                await result.update({"$set": update_data})
                operation = "updated"
                stats_id = result.id
            else:
                # 创建新记录
                saved_stats = await stats_instance.save()
                operation = "created"
                stats_id = saved_stats.id
            
            logger.info(
                f"Successfully {operation} wallet stats for {wallet_address}, "
                f"period: {period}, id: {stats_id}"
            )
            
            return {
                "success": True,
                "operation": operation,
                "wallet_address": wallet_address,
                "period": period,
                "stats_id": str(stats_id)
            }
            
        except Exception as e:
            logger.error(
                f"Error upserting wallet stats for {wallet_address}, period {period}: {e}"
            )
            return {
                "success": False,
                "error": str(e),
                "wallet_address": wallet_address,
                "period": period
            }
    
    async def batch_upsert_wallet_stats(
        self, 
        stats_list: List[Dict[str, Any]], 
        period: str = "all"
    ) -> Dict[str, Any]:
        """
        批量新增或更新钱包统计数据
        
        Args:
            stats_list: 统计数据列表，每个元素包含wallet_address和stats_data
            period: 时间窗口
            
        Returns:
            批量操作结果
        """
        try:
            results = {
                "success_count": 0,
                "error_count": 0,
                "total_count": len(stats_list),
                "errors": [],
                "successes": []
            }
            
            for stats_item in stats_list:
                wallet_address = stats_item.get("wallet_address")
                stats_data = stats_item.get("stats_data", stats_item)
                
                if not wallet_address:
                    results["error_count"] += 1
                    results["errors"].append({
                        "error": "Missing wallet_address",
                        "item": stats_item
                    })
                    continue
                
                # 单个upsert操作
                result = await self.upsert_wallet_stats(
                    wallet_address=wallet_address,
                    stats_data=stats_data,
                    period=period
                )
                
                if result["success"]:
                    results["success_count"] += 1
                    results["successes"].append(result)
                else:
                    results["error_count"] += 1
                    results["errors"].append(result)
            
            logger.info(
                f"Batch upsert completed: {results['success_count']} success, "
                f"{results['error_count']} errors, period: {period}"
            )
            
            return results
            
        except Exception as e:
            logger.error(f"Error in batch upsert wallet stats: {e}")
            return {
                "success_count": 0,
                "error_count": len(stats_list),
                "total_count": len(stats_list),
                "errors": [{"error": str(e), "batch_operation": True}],
                "successes": []
            }
    
    async def get_wallet_stats(
        self, 
        wallet_address: str, 
        period: Optional[str] = None,
        chain: str = "sol"
    ) -> Optional[GmgnWalletStats]:
        """
        获取单个钱包的统计数据
        
        Args:
            wallet_address: 钱包地址
            period: 时间窗口过滤器，None表示获取最新的
            chain: 链名称
            
        Returns:
            钱包统计数据实例或None
        """
        try:
            query = {
                "wallet_address": wallet_address,
                "chain": chain
            }
            
            if period:
                query["period"] = period
            
            # 如果没有指定period，获取最新的记录
            if period:
                result = await GmgnWalletStats.find_one(query)
            else:
                results = await GmgnWalletStats.find(query).sort([("updated_at", -1)]).limit(1).to_list()
                result = results[0] if results else None
            
            if result:
                logger.debug(f"Found wallet stats for {wallet_address}, period: {result.period}")
            else:
                logger.debug(f"No wallet stats found for {wallet_address}, period: {period}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting wallet stats for {wallet_address}: {e}")
            return None
    
    async def get_stats_by_period(
        self, 
        period: str,
        limit: int = 100,
        skip: int = 0,
        sort_by: str = "updated_at",
        sort_order: int = -1
    ) -> List[GmgnWalletStats]:
        """
        按时间窗口获取统计数据
        
        Args:
            period: 时间窗口
            limit: 数量限制
            skip: 跳过数量
            sort_by: 排序字段
            sort_order: 排序方向（1: 升序, -1: 降序）
            
        Returns:
            统计数据列表
        """
        try:
            query = {"period": period}
            
            results = await GmgnWalletStats.find(query).sort([
                (sort_by, sort_order)
            ]).skip(skip).limit(limit).to_list()
            
            logger.debug(
                f"Retrieved {len(results)} wallet stats for period {period}, "
                f"skip: {skip}, limit: {limit}"
            )
            
            return results
            
        except Exception as e:
            logger.error(f"Error getting stats by period {period}: {e}")
            return []
    
    async def get_wallets_with_stats_and_kol_info(
        self, 
        period: str = "all",
        min_pnl: Optional[float] = None,
        min_winrate: Optional[float] = None,
        limit: int = 50
    ) -> List[Tuple[GmgnWalletStats, KOLWallet]]:
        """
        获取带有KOL信息的钱包统计数据（关联查询）
        
        Args:
            period: 时间窗口
            min_pnl: 最小收益率过滤
            min_winrate: 最小胜率过滤
            limit: 数量限制
            
        Returns:
            (统计数据, KOL信息)的元组列表
        """
        try:
            # 构建查询条件
            stats_query = {"period": period}
            
            if min_pnl is not None:
                stats_query["pnl"] = {"$gte": min_pnl}
            
            if min_winrate is not None:
                stats_query["winrate"] = {"$gte": min_winrate}
            
            # 获取符合条件的统计数据
            stats_results = await GmgnWalletStats.find(stats_query).sort([
                ("pnl", -1)
            ]).limit(limit).to_list()
            
            if not stats_results:
                return []
            
            # 获取对应的KOL信息
            wallet_addresses = [stats.wallet_address for stats in stats_results]
            kol_results = await KOLWallet.find(
                {"wallet_address": {"$in": wallet_addresses}}
            ).to_list()
            
            # 创建KOL信息映射
            kol_map = {kol.wallet_address: kol for kol in kol_results}
            
            # 组合结果
            combined_results = []
            for stats in stats_results:
                kol_info = kol_map.get(stats.wallet_address)
                if kol_info:  # 只返回有KOL信息的记录
                    combined_results.append((stats, kol_info))
            
            logger.info(
                f"Retrieved {len(combined_results)} wallets with stats and KOL info, "
                f"period: {period}, filters: pnl>={min_pnl}, winrate>={min_winrate}"
            )
            
            return combined_results
            
        except Exception as e:
            logger.error(f"Error getting wallets with stats and KOL info: {e}")
            return []
    
    async def delete_stats_by_period(self, period: str, wallet_address: Optional[str] = None) -> int:
        """
        删除指定时间窗口的统计数据
        
        Args:
            period: 时间窗口
            wallet_address: 可选的钱包地址过滤器
            
        Returns:
            删除的记录数量
        """
        try:
            query = {"period": period}
            
            if wallet_address:
                query["wallet_address"] = wallet_address
            
            # 获取要删除的记录数量
            count = await GmgnWalletStats.find(query).count()
            
            # 执行删除
            result = await GmgnWalletStats.find(query).delete()
            
            logger.info(
                f"Deleted {count} wallet stats records for period {period}" +
                (f", wallet: {wallet_address}" if wallet_address else "")
            )
            
            return count
            
        except Exception as e:
            logger.error(f"Error deleting stats by period {period}: {e}")
            return 0
    
    async def get_stats_summary(self) -> Dict[str, Any]:
        """
        获取统计数据概览
        
        Returns:
            统计概览字典
        """
        try:
            # 总记录数
            total_count = await GmgnWalletStats.count()
            
            # 按period分组统计
            pipeline = [
                {
                    "$group": {
                        "_id": "$period",
                        "count": {"$sum": 1},
                        "avg_pnl": {"$avg": "$pnl"},
                        "avg_winrate": {"$avg": "$winrate"},
                        "last_updated": {"$max": "$updated_at"}
                    }
                },
                {
                    "$sort": {"_id": 1}
                }
            ]
            
            period_stats = await GmgnWalletStats.aggregate(pipeline).to_list()
            
            # 获取最近更新时间
            latest_stats = await GmgnWalletStats.find().sort([
                ("updated_at", -1)
            ]).limit(1).to_list()
            
            last_update = latest_stats[0].updated_at if latest_stats else None
            
            summary = {
                "total_records": total_count,
                "period_breakdown": period_stats,
                "last_update": last_update,
                "generated_at": datetime.now()
            }
            
            logger.debug(f"Generated stats summary: {total_count} total records")
            
            return summary
            
        except Exception as e:
            logger.error(f"Error generating stats summary: {e}")
            return {
                "total_records": 0,
                "period_breakdown": [],
                "last_update": None,
                "generated_at": datetime.now(),
                "error": str(e)
            } 