name: "GMGN KOL钱包统计数据更新"
description: "定期获取和更新KOL钱包的统计数据到独立数据表，支持多时间窗口数据存储(all/7d/1d)"

nodes:
  - name: "WalletStatsSchedulerNode"
    node_type: "input"
    interval: 10
    generate_data: workflows.gmgn_kol_wallet_stats.handler.generate_wallet_addresses
    flow_control:
      max_pending_messages: 150  # 最大等待处理的消息数（增加以适应3个时间窗口）
      check_interval: 5          # 流量控制检查间隔(秒)
      enable_flow_control: true  # 启用流量控制

  - name: "WalletStatsProcessNode"
    node_type: "process"
    depend_ons: ["WalletStatsSchedulerNode"]
    concurrency: 5   # 3个并发工作者（适应多时间窗口处理）
    interval: 0.1    # 每个请求间隔0.2秒，避免API限流
    process_item: workflows.gmgn_kol_wallet_stats.handler.process_wallet_stats

  - name: "WalletStatsStoreNode"
    node_type: "storage"
    depend_ons: ["WalletStatsProcessNode"]
    batch_size: 100  # 每批处理100条数据，按期分组存储优化数据库性能
    store_data: workflows.gmgn_kol_wallet_stats.handler.store_wallet_stats
    validate: workflows.gmgn_kol_wallet_stats.handler.validate_wallet_data 