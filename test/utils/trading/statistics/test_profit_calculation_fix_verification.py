#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
盈利计算Bug修复验证测试

测试目标：验证修复后的盈利计算逻辑能够正确使用实际金额进行计算。

修复说明：
- 买入成本：优先使用 token_in_actual_amount（实际花费），回退到 token_in_amount（计划投入）
- 卖出收入：优先使用 token_out_actual_amount（实际收入），回退到 token_out_verified_amount（验证金额）
- 统一口径：使用GmgN API获取的实际金额，已扣除平台服务费等

预期行为：
- 修复前：使用不一致的金额计算，导致盈利被夸大
- 修复后：使用统一口径的实际金额，盈利计算准确
"""

import unittest
from unittest.mock import AsyncMock, MagicMock, patch, Mock
from datetime import datetime, timezone
from beanie import PydanticObjectId
from decimal import Decimal

from utils.trading.statistics.trade_pair_matcher import TradePairMatcher
from utils.trading.statistics.statistics_calculator import StatisticsCalculator
from models.trade_record import TradeRecord, TradeType, TradeStatus


class TestProfitCalculationFixVerification(unittest.IsolatedAsyncioTestCase):
    """盈利计算Bug修复验证测试"""

    def setUp(self):
        """设置测试环境"""
        # Mock验证更新器
        self.mock_verification_updater = Mock()
        self.mock_verification_updater.verify_single_record = AsyncMock()
        
        # Mock DAO操作
        self.mock_trade_record_dao_patcher = patch('dao.trade_record_dao.TradeRecordDAO')
        self.mock_trade_record_dao_class = self.mock_trade_record_dao_patcher.start()
        self.mock_trade_record_dao_instance = Mock()
        self.mock_trade_record_dao_class.return_value = self.mock_trade_record_dao_instance
        self.mock_trade_record_dao_instance.update_verification_result = AsyncMock(return_value=True)
        
        # Mock信号DAO
        self.mock_signal_dao_patcher = patch('dao.signal_dao.SignalDAO')
        self.mock_signal_dao_class = self.mock_signal_dao_patcher.start()
        self.mock_signal_dao_instance = Mock()
        self.mock_signal_dao_class.return_value = self.mock_signal_dao_instance
        
        # 创建TradePairMatcher实例（使用mock的依赖）
        self.trade_pair_matcher = TradePairMatcher(self.mock_verification_updater)
        
        # Mock StatisticsCalculator
        self.mock_statistics_calculator = Mock(spec=StatisticsCalculator)

    def tearDown(self):
        """清理测试环境"""
        self.mock_trade_record_dao_patcher.stop()
        self.mock_signal_dao_patcher.stop()

    async def test_fixed_profit_calculation_with_actual_amounts(self):
        """
        测试修复后的盈利计算逻辑：使用实际金额进行准确计算
        
        场景：
        - 买入记录：有实际金额 token_in_actual_amount = 0.00310428 SOL
        - 卖出记录：有实际金额 token_out_actual_amount = 0.001448464 SOL
        - 预期：使用实际金额计算，显示亏损 -53.34%
        """
        print("\n=== 测试修复后的盈利计算逻辑 ===")
        
        # Mock买入记录数据
        mock_buy_record = Mock(spec=TradeRecord)
        mock_buy_record.id = PydanticObjectId()
        mock_buy_record.signal_id = PydanticObjectId()
        mock_buy_record.strategy_name = "test_strategy"
        mock_buy_record.trade_type = TradeType.BUY
        mock_buy_record.status = TradeStatus.SUCCESS
        mock_buy_record.token_in_address = "So11111111111111111111111111111111111111112"
        mock_buy_record.token_in_amount = 0.001  # 计划投入（旧逻辑使用的值）
        mock_buy_record.token_in_actual_amount = 0.00310428  # 实际花费（修复后使用的值）
        mock_buy_record.token_out_address = "MUbEZ6mDVy39FrjNCQPyX3PM7CwSM3hQ754ii4wpump"
        mock_buy_record.wallet_address = "AHnQjWa4jxYNxzfJantHDpcUKAE9pzVSGGPLhthPxCSW"
        mock_buy_record.tx_hash = "BQUbMmrLMiaxkwCSD8VRbkJdUYre1or6A19Pkd6KXPGCqcUGMYU6YVKHR59XxcTfuBiMxYzyJYyxASzC6f7Ke1c"
        mock_buy_record.created_at = datetime(2025, 1, 26, 10, 0, 0, tzinfo=timezone.utc)
        mock_buy_record.verification_status = "verified"
        mock_buy_record.actual_amount_source = "gmgn_api"
        
        # Mock卖出记录数据
        mock_sell_record = Mock(spec=TradeRecord)
        mock_sell_record.id = PydanticObjectId()
        mock_sell_record.signal_id = PydanticObjectId()
        mock_sell_record.strategy_name = "test_strategy"
        mock_sell_record.trade_type = TradeType.SELL
        mock_sell_record.status = TradeStatus.SUCCESS
        mock_sell_record.token_in_address = "MUbEZ6mDVy39FrjNCQPyX3PM7CwSM3hQ754ii4wpump"
        mock_sell_record.token_out_address = "So11111111111111111111111111111111111111112"
        mock_sell_record.token_out_actual_amount = 0.001448464  # 实际收入（修复后使用的值）
        mock_sell_record.token_out_verified_amount = 0.001448464  # 验证金额（回退值）
        mock_sell_record.wallet_address = "AHnQjWa4jxYNxzfJantHDpcUKAE9pzVSGGPLhthPxCSW"
        mock_sell_record.tx_hash = "52vSZHtMT41i7A8zDnazquLM5BME5k1F7YgqnwiujQmFpzYqSBAdfs2SBC8wuxkXPLGsofyoK8M1wKboF3nNgmbU"
        mock_sell_record.created_at = datetime(2025, 1, 26, 12, 0, 0, tzinfo=timezone.utc)
        mock_sell_record.verification_status = "verified"
        mock_sell_record.actual_amount_source = "gmgn_api"
        
        # 测试修复后的 _get_sol_amount 方法
        buy_amount = await self.trade_pair_matcher._get_sol_amount(mock_buy_record)
        sell_amount = await self.trade_pair_matcher._get_sol_amount(mock_sell_record)
        
        # 验证使用了实际金额
        self.assertEqual(buy_amount, 0.00310428, "买入应该使用实际金额 token_in_actual_amount")
        self.assertEqual(sell_amount, 0.001448464, "卖出应该使用实际金额 token_out_actual_amount")
        
        # 计算修复后的盈利
        profit_amount = sell_amount - buy_amount
        profit_rate = (profit_amount / buy_amount) * 100.0 if buy_amount > 0 else 0.0
        is_profitable = sell_amount > buy_amount
        
        print(f"修复后逻辑 - 买入成本: {buy_amount} SOL (实际花费)")
        print(f"修复后逻辑 - 卖出收入: {sell_amount} SOL (实际收入)")
        print(f"修复后逻辑 - 盈利金额: {profit_amount} SOL")
        print(f"修复后逻辑 - 盈利率: {profit_rate:.2f}%")
        print(f"修复后逻辑 - 是否盈利: {is_profitable}")
        
        # 验证修复后的计算结果
        expected_profit_amount = -0.001655816  # 0.001448464 - 0.00310428
        expected_profit_rate = -53.34  # (-0.001655816 / 0.00310428) * 100
        
        self.assertAlmostEqual(profit_amount, expected_profit_amount, places=6, 
                              msg="修复后的盈利金额应该准确")
        self.assertAlmostEqual(profit_rate, expected_profit_rate, places=1, 
                              msg="修复后的盈利率应该准确")
        self.assertFalse(is_profitable, "修复后应该正确识别为亏损交易")
        
        print("\n=== 修复验证成功 ===")
        print("✅ 买入成本使用实际金额（包含所有费用）")
        print("✅ 卖出收入使用实际金额（统一口径）")
        print("✅ 盈利计算准确反映真实情况")
        print("✅ 正确识别亏损交易")

    async def test_fallback_logic_when_actual_amounts_missing(self):
        """
        测试回退逻辑：当实际金额缺失时，使用原有字段
        
        场景：
        - 买入记录：缺少实际金额，回退到计划金额
        - 卖出记录：缺少实际金额，回退到验证金额
        """
        print("\n=== 测试回退逻辑 ===")
        
        # Mock买入记录数据（缺少实际金额）
        mock_buy_record = Mock(spec=TradeRecord)
        mock_buy_record.id = PydanticObjectId()
        mock_buy_record.trade_type = TradeType.BUY
        mock_buy_record.token_in_amount = 0.001  # 只有计划金额
        mock_buy_record.token_in_actual_amount = None  # 缺少实际金额
        mock_buy_record.verification_status = "verified"
        
        # Mock卖出记录数据（缺少实际金额）
        mock_sell_record = Mock(spec=TradeRecord)
        mock_sell_record.id = PydanticObjectId()
        mock_sell_record.trade_type = TradeType.SELL
        mock_sell_record.token_out_actual_amount = None  # 缺少实际金额
        mock_sell_record.token_out_verified_amount = 0.0015  # 只有验证金额
        mock_sell_record.verification_status = "verified"
        
        # 测试回退逻辑
        buy_amount = await self.trade_pair_matcher._get_sol_amount(mock_buy_record)
        sell_amount = await self.trade_pair_matcher._get_sol_amount(mock_sell_record)
        
        # 验证回退到原有字段
        self.assertEqual(buy_amount, 0.001, "买入应该回退到计划金额 token_in_amount")
        self.assertEqual(sell_amount, 0.0015, "卖出应该回退到验证金额 token_out_verified_amount")
        
        print(f"回退逻辑 - 买入成本: {buy_amount} SOL (回退到计划金额)")
        print(f"回退逻辑 - 卖出收入: {sell_amount} SOL (回退到验证金额)")
        print("✅ 回退逻辑正常工作")

    async def test_fallback_to_verification_with_mock_updater(self):
        """
        测试当验证金额为空时调用验证更新器的回退逻辑
        """
        print("\n=== 测试验证更新器回退逻辑 ===")

        # Mock卖出记录（缺少验证金额）
        mock_sell_record = Mock(spec=TradeRecord)
        mock_sell_record.id = PydanticObjectId()
        mock_sell_record.trade_type = TradeType.SELL
        mock_sell_record.token_out_actual_amount = None
        mock_sell_record.token_out_verified_amount = None  # 验证金额也为空
        mock_sell_record.verification_status = "pending"
        mock_sell_record.tx_hash = "test_tx_hash"
        mock_sell_record.token_out_address = "test_token_address"
        mock_sell_record.wallet_address = "test_wallet_address"
        
        # 确保这不是一个Mock对象（移除_mock_name属性）
        if hasattr(mock_sell_record, '_mock_name'):
            delattr(mock_sell_record, '_mock_name')

        # Mock验证更新器返回成功结果
        self.mock_verification_updater.verify_single_record.return_value = {
            'status': 'verified',
            'verified_amount': 0.002
        }

        # Mock数据库更新操作
        self.mock_trade_record_dao_instance.update_verification_result.return_value = True

        # 测试回退到验证更新器
        result_amount = await self.trade_pair_matcher._get_verified_amount(mock_sell_record)

        # 验证返回正确的金额
        self.assertEqual(result_amount, 0.002)

        # 验证调用了验证更新器
        self.mock_verification_updater.verify_single_record.assert_called_once_with(
            tx_hash="test_tx_hash",
            token_out_address="test_token_address",
            wallet_address="test_wallet_address"
        )

        # 验证数据库更新操作被调用
        self.mock_trade_record_dao_instance.update_verification_result.assert_called_once()

    @patch('utils.trading.statistics.trade_pair_matcher.TradePairMatcher._get_verified_amount')
    async def test_error_handling_with_mock_logging(self, mock_get_verified_amount):
        """
        测试错误处理逻辑（使用mock方法）
        """
        print("\n=== 测试错误处理逻辑 ===")

        # Mock记录（缺少必要字段）
        mock_invalid_record = Mock(spec=TradeRecord)
        mock_invalid_record.id = PydanticObjectId()
        mock_invalid_record.trade_type = TradeType.SELL
        mock_invalid_record.token_out_actual_amount = None
        mock_invalid_record.token_out_verified_amount = None
        mock_invalid_record.verification_status = "pending"
        mock_invalid_record.tx_hash = None  # 缺少tx_hash
        mock_invalid_record.token_out_address = None  # 缺少token_out_address
        mock_invalid_record.wallet_address = "test_wallet"
        
        # 确保这不是一个Mock对象（移除_mock_name属性），这样会调用_get_verified_amount
        if hasattr(mock_invalid_record, '_mock_name'):
            delattr(mock_invalid_record, '_mock_name')

        # Mock _get_verified_amount 方法返回None（模拟错误处理）
        mock_get_verified_amount.return_value = None

        # 测试错误处理
        result = await self.trade_pair_matcher._get_sol_amount(mock_invalid_record)

        # 验证返回None
        self.assertIsNone(result)

        # 验证调用了 _get_verified_amount 方法
        mock_get_verified_amount.assert_called_once_with(mock_invalid_record)

    async def test_data_source_tracking(self):
        """
        测试数据来源跟踪：验证新增的字段能够正确记录数据来源
        """
        print("\n=== 测试数据来源跟踪 ===")
        
        # Mock有数据来源标记的记录
        mock_record = Mock(spec=TradeRecord)
        mock_record.actual_amount_source = "gmgn_api"
        mock_record.actual_amount_updated_at = datetime.now(timezone.utc)
        
        # 验证数据来源字段
        self.assertEqual(mock_record.actual_amount_source, "gmgn_api")
        self.assertIsNotNone(mock_record.actual_amount_updated_at)
        
        print(f"数据来源: {mock_record.actual_amount_source}")
        print(f"更新时间: {mock_record.actual_amount_updated_at}")
        print("✅ 数据来源跟踪正常工作")


if __name__ == "__main__":
    unittest.main() 