"""
工作流配置解析模块

实现从YAML配置文件创建工作流的功能
"""

import os
import yaml
import uuid
import inspect
import importlib
import logging
import traceback
from typing import Dict, Any, List, Optional, Callable, Type, Union

from .workflow import Workflow
from .nodes import Node, InputNode, ProcessNode, StorageNode
from utils.workflows.message_queue.message_queue import MessageQueue

class WorkflowConfigParser:
    """工作流配置解析器"""
    
    def __init__(self):
        """初始化工作流配置解析器"""
        self.logger = logging.getLogger("WorkflowConfigParser")
    
    def parse_yaml(self, yaml_content: str) -> Dict:
        """解析YAML内容
        
        Args:
            yaml_content: YAML内容字符串
            
        Returns:
            Dict: 解析后的配置字典
        """
        try:
            config = yaml.safe_load(yaml_content)
            return config
        except Exception as e:
            self.logger.error(f"解析YAML内容失败: {str(e)}")
            raise ValueError(f"无效的YAML内容: {str(e)}")
    
    def load_yaml_file(self, file_path: str) -> Dict:
        """从文件加载YAML配置
        
        Args:
            file_path: YAML配置文件路径
            
        Returns:
            Dict: 解析后的配置字典
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                yaml_content = f.read()
            return self.parse_yaml(yaml_content)
        except Exception as e:
            self.logger.error(f"加载YAML文件失败: {str(e)}")
            raise ValueError(f"无法加载YAML文件 {file_path}: {str(e)}")
    
    def import_class_or_function(self, import_path: str) -> Any:
        """导入类或函数
        
        Args:
            import_path: 导入路径，格式为"module.submodule.object"
            
        Returns:
            Any: 导入的类或函数
        """
        try:
            module_path, object_name = import_path.rsplit('.', 1)
            module = importlib.import_module(module_path)
            return getattr(module, object_name)
        except Exception as e:
            self.logger.error(f"导入 {import_path} 失败: {str(e)}")
            raise ImportError(f"无法导入 {import_path}: {str(e)}\n{traceback.format_exc()}")
    
    def create_node_from_config(self, node_config: Dict) -> Node:
        """从配置创建节点
        
        Args:
            node_config: 节点配置字典
            
        Returns:
            Node: 创建的节点
        """
        node_name = node_config.get('name')
        if not node_name:
            raise ValueError("节点配置缺少name字段")
        
        # 确定节点类型，优先使用显式指定的类型
        node_type = node_config.get('node_type')
        if not node_type:
            # 如果没有显式指定，则尝试自动推断
            node_type = self._determine_node_type(node_config)
        
        # 创建节点实例
        node = self._create_node_instance(node_type, node_config)
        
        # 设置节点属性
        self._set_node_attributes(node, node_config)
        
        return node
    
    def _determine_node_type(self, node_config: Dict) -> str:
        """确定节点类型
        
        Args:
            node_config: 节点配置字典
            
        Returns:
            str: 节点类型，"input", "process", "storage"
        """
        # 根据配置中的关键字判断节点类型
        if 'generate_data' in node_config:
            return "input"
        elif 'process_item' in node_config:
            return "process"
        elif 'store_data' in node_config or 'validate' in node_config:
            return "storage"
        else:
            raise ValueError(f"无法确定节点 {node_config.get('name')} 的类型，请显式指定node_type")
    
    def _create_node_instance(self, node_type: str, node_config: Dict) -> Node:
        """创建节点实例
        
        Args:
            node_type: 节点类型
            node_config: 节点配置字典
            
        Returns:
            Node: 创建的节点实例
        """
        node_name = node_config.get('name')
        
        # 创建动态节点类
        if node_type.lower() == "input":
            return DynamicInputNode(name=node_name)
        elif node_type.lower() == "process":
            return DynamicProcessNode(name=node_name)
        elif node_type.lower() == "storage":
            return DynamicStorageNode(name=node_name)
        else:
            raise ValueError(f"不支持的节点类型: {node_type}")
    
    def _set_node_attributes(self, node: Node, node_config: Dict) -> None:
        """设置节点属性
        
        Args:
            node: 需要设置属性的节点
            node_config: 节点配置
        """
        # 拷贝并清理配置，移除已处理的属性
        config = node_config.copy()
        if 'node_type' in config:
            config.pop('node_type')
        if 'name' in config:
            config.pop('name')
        if 'depend_ons' in config:
            config.pop('depend_ons')
        
        # 处理flow_control配置
        if 'flow_control' in config:
            flow_control = config.pop('flow_control')
            if isinstance(node, InputNode):
                if 'max_pending_messages' in flow_control:
                    node.max_pending_messages = flow_control.get('max_pending_messages')
                if 'check_interval' in flow_control:
                    node.check_interval = flow_control.get('check_interval')
                if 'enable_flow_control' in flow_control:
                    node.enable_flow_control = flow_control.get('enable_flow_control')
                node.setup_flow_controller()
        
        # 处理函数属性
        for func_name in ['generate_data', 'process_item', 'validate', 'store_data']:
            if func_name in config:
                func_path = config.pop(func_name)
                func = self.import_class_or_function(func_path)
                
                # 设置对应的函数属性
                if func_name == 'generate_data' and isinstance(node, DynamicInputNode):
                    node.generate_data_func = func
                    # 检查函数类型
                    node.is_async_generator_func = inspect.isasyncgenfunction(func)
                    node.is_generator_func = inspect.isgeneratorfunction(func) or node.is_async_generator_func
                    self.logger.info(f"设置{node.name}的generate_data_func，是生成器函数: {node.is_generator_func}，是异步生成器函数: {node.is_async_generator_func}")
                elif func_name == 'process_item' and isinstance(node, DynamicProcessNode):
                    node.process_item_func = func
                    # 检查函数类型
                    node.is_async_generator_func = inspect.isasyncgenfunction(func)
                    node.is_generator_func = inspect.isgeneratorfunction(func) or node.is_async_generator_func
                    self.logger.info(f"设置{node.name}的process_item_func，是生成器函数: {node.is_generator_func}，是异步生成器函数: {node.is_async_generator_func}")
                elif func_name == 'validate' and isinstance(node, DynamicStorageNode):
                    node.validate_data_func = func
                elif func_name == 'store_data' and isinstance(node, DynamicStorageNode):
                    node.store_data_func = func
        
        # 设置其他属性
        for key, value in config.items():
            if hasattr(node, key):
                setattr(node, key, value)
            else:
                self.logger.warning(f"节点{node.name}没有属性{key}，无法设置")
    
    def create_workflow_from_config(self, config: Dict) -> Workflow:
        """从配置创建工作流
        
        Args:
            config: 工作流配置字典
            
        Returns:
            Workflow: 创建的工作流
        """
        # 创建工作流
        workflow_name = config.get('name', 'Workflow')
        workflow = Workflow(name=workflow_name)
        
        # 设置工作流描述
        if 'description' in config:
            workflow.description = config['description']
        
        # 创建节点
        nodes_dict = {}  # 节点字典，键为节点名称，值为节点实例
        for node_config in config.get('nodes', []):
            node = self.create_node_from_config(node_config)
            nodes_dict[node.name] = {
                'node': node,
                'config': node_config
            }
        
        # 添加节点到工作流并根据依赖关系连接
        for node_name, node_info in nodes_dict.items():
            node = node_info['node']
            node_config = node_info['config']
            
            # 获取依赖节点
            depend_ons = node_config.get('depend_ons', [])
            
            if not depend_ons:
                # 没有依赖节点，直接添加
                workflow.add_node(node)
                self.logger.info(f"添加节点: {node_name}")
            else:
                # 有依赖节点，连接到所有依赖节点
                for depend_on in depend_ons:
                    if depend_on not in nodes_dict:
                        raise ValueError(f"节点 {node_name} 依赖的节点 {depend_on} 不存在")
                    
                    # 获取依赖节点
                    depend_node = nodes_dict[depend_on]['node']
                    
                    # 生成队列名称
                    queue_name = f"{depend_on}_to_{node_name}"
                    
                    # 添加节点并连接
                    workflow.add_node(node, connect_to=depend_node, queue_name=queue_name)
                    self.logger.info(f"添加节点: {node_name}，连接到: {depend_on}")
        
        return workflow

class DynamicInputNode(InputNode):
    """动态输入节点，可以通过配置设置处理函数"""
    
    def __init__(self, name: str = None, output_queue: MessageQueue = None):
        """初始化动态输入节点"""
        super().__init__(name, output_queue)
        self.generate_data_func = None
        self.is_generator_func = False
        self.is_async_generator_func = False
    
    async def generate_data(self):
        """生成数据
        
        调用配置中指定的函数生成数据
        现在是异步生成器方法，使用yield返回数据
        """
        if not self.generate_data_func:
            self.logger.warning("未设置generate_data_func，无法生成数据")
            return
        
        try:
            # 区分不同类型的函数
            if inspect.isasyncgenfunction(self.generate_data_func):
                # 异步生成器函数
                self.logger.debug("使用异步生成器函数生成数据")
                async for item in self.generate_data_func():
                    yield item
            elif inspect.isgeneratorfunction(self.generate_data_func):
                # 同步生成器函数
                self.logger.debug("使用同步生成器函数生成数据")
                for item in self.generate_data_func():
                    yield item
            elif inspect.iscoroutinefunction(self.generate_data_func):
                # 异步普通函数
                self.logger.debug("使用异步普通函数生成数据")
                result = await self.generate_data_func()
                if isinstance(result, list):
                    for item in result:
                        yield item
                elif result is not None:
                    yield result
            else:
                # 同步普通函数
                self.logger.debug("使用同步普通函数生成数据")
                result = self.generate_data_func()
                if isinstance(result, list):
                    for item in result:
                        yield item
                elif result is not None:
                    yield result
        except Exception as e:
            self.logger.error(f"生成数据时发生异常: {str(e)}\n{traceback.format_exc()}")
            raise

class DynamicProcessNode(ProcessNode):
    """动态处理节点，可以通过配置设置处理函数"""
    
    def __init__(self, name: str = None, input_queue: MessageQueue = None, output_queue: MessageQueue = None):
        """初始化动态处理节点"""
        super().__init__(name, input_queue, output_queue)
        self.process_item_func = None
        self.is_generator_func = False
        self.is_async_generator_func = False
    
    async def process_item(self, item: Any):
        """处理单个数据项
        
        调用配置中指定的函数处理数据项
        现在是异步生成器方法，使用yield返回处理结果
        """
        if not self.process_item_func:
            self.logger.warning("未设置process_item_func，无法处理数据项")
            return
        
        try:
            # 区分不同类型的函数
            if inspect.isasyncgenfunction(self.process_item_func):
                # 异步生成器函数
                self.logger.debug("使用异步生成器函数处理数据")
                async for result in self.process_item_func(item):
                    yield result
            elif inspect.isgeneratorfunction(self.process_item_func):
                # 同步生成器函数
                self.logger.debug("使用同步生成器函数处理数据")
                for result in self.process_item_func(item):
                    yield result
            elif inspect.iscoroutinefunction(self.process_item_func):
                # 异步普通函数
                self.logger.debug("使用异步普通函数处理数据")
                result = await self.process_item_func(item)
                if isinstance(result, list):
                    for single_result in result:
                        yield single_result
                elif result is not None:
                    yield result
            else:
                # 同步普通函数
                self.logger.debug("使用同步普通函数处理数据")
                result = self.process_item_func(item)
                if isinstance(result, list):
                    for single_result in result:
                        yield single_result
                elif result is not None:
                    yield result
        except Exception as e:
            self.logger.error(f"处理数据项时发生异常: {str(e)}\n{traceback.format_exc()}")
            raise

class DynamicStorageNode(StorageNode):
    """动态存储节点，可以通过配置设置存储函数"""
    
    def __init__(self, name: str = None, input_queue: MessageQueue = None):
        """初始化动态存储节点"""
        super().__init__(name, input_queue)
        self.validate_data_func = None
        self.store_data_func = None
    
    async def validate_data(self, data: Any) -> bool:
        """验证数据
        
        调用配置中指定的函数验证数据
        """
        if self.validate_data_func:
            if inspect.iscoroutinefunction(self.validate_data_func):
                return await self.validate_data_func(data)
            else:
                return self.validate_data_func(data)
        else:
            # 默认验证逻辑
            return super().validate_data(data)
    
    async def store_data(self, data: Any) -> int:
        """存储数据
        
        调用配置中指定的函数存储数据
        """
        if self.store_data_func:
            if inspect.iscoroutinefunction(self.store_data_func):
                return await self.store_data_func(data)
            else:
                return self.store_data_func(data)
        else:
            # 默认存储逻辑
            return await super().store_data(data)

def create_workflow_from_yaml(yaml_content: str) -> Workflow:
    """从YAML内容创建工作流
    
    Args:
        yaml_content: YAML配置内容
        
    Returns:
        Workflow: 创建的工作流
    """
    parser = WorkflowConfigParser()
    config = parser.parse_yaml(yaml_content)
    return parser.create_workflow_from_config(config)

def create_workflow_from_yaml_file(file_path: str) -> Workflow:
    """从YAML文件创建工作流
    
    Args:
        file_path: YAML配置文件路径
        
    Returns:
        Workflow: 创建的工作流
    """
    parser = WorkflowConfigParser()
    config = parser.load_yaml_file(file_path)
    return parser.create_workflow_from_config(config) 