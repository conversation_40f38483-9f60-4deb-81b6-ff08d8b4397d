from datetime import datetime
from typing import Optional, List
from beanie import Document, Link
from pydantic import Field
from .x_user import XUser

class Tweet(Document):
    """推文模型"""
    
    # 推文基本信息
    tweet_id: str = Field(unique=True, index=True)  # 推文ID
    text: str  # 推文内容
    # author: Link[XUser]  # 作者引用
    author_id: str = Field(index=True)  # 作者ID
    # 推文元数据
    created_at: datetime  # 发布时间
    language: Optional[str] = None  # 语言
    
    # 互动数据
    like_count: int = Field(default=0)  # 点赞数
    retweet_count: int = Field(default=0)  # 转发数
    reply_count: int = Field(default=0)  # 回复数
    quote_count: int = Field(default=0)  # 引用数
    
    # 推文类型
    is_retweet: bool = Field(default=False)  # 是否是转发
    is_reply: bool = Field(default=False)  # 是否是回复
    is_quote: bool = Field(default=False)  # 是否是引用
    
    # 关联推文
    replied_to_tweet_id: Optional[str] = Field(default=None, index=True)  # 回复的推文ID
    quoted_tweet_id: Optional[str] = Field(default=None, index=True)  # 引用的推文ID
    retweeted_tweet_id: Optional[str] = Field(default=None, index=True)  # 转发的推文ID
    sender_id: Optional[str] = Field(default=None, index=True)  # 发送者ID即原作者ID
    
    # 媒体内容
    media: Optional[List[dict]] = None  # 媒体列表（图片、视频等）
    urls: Optional[List[str]] = None  # URL列表
    hashtags: Optional[List[str]] = None  # 话题标签
    mentions: Optional[List[str]] = None  # 提及用户列表
    
    # 系统元数据
    fetched_at: datetime = Field(default_factory=datetime.utcnow)  # 抓取时间
    last_updated: datetime = Field(default_factory=datetime.utcnow)  # 最后更新时间
    
    # 是否置顶
    is_top: bool = Field(default=False)  # 是否置顶
    class Settings:
        name = "tweets"
        indexes = [
            "tweet_id",
            "author",
            "created_at",
            "fetched_at",
            "is_retweet",
            "is_reply",
            "is_quote"
        ] 