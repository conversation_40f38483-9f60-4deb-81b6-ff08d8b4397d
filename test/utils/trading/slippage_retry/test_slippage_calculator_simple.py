"""滑点计算器简化测试"""

import unittest
from utils.trading.slippage_retry.slippage_calculator import SlippageCalculator
from models.slippage_retry import SlippageRetryConfig


class TestSlippageCalculatorSimple(unittest.TestCase):
    """滑点计算器简化测试类"""
    
    def setUp(self) -> None:
        """测试前准备"""
        self.calculator = SlippageCalculator()
        self.test_config = SlippageRetryConfig(
            enabled=True,
            increment_percentage=0.5,
            max_slippage_percentage=5.0,
            retry_delay_seconds=0.5
        )
    
    def test_basic_calculation(self) -> None:
        """测试基础滑点计算"""
        new_slippage, exceeded = self.calculator.calculate_next_slippage(
            current_slippage=1.0,
            config=self.test_config,
            trade_type="buy"
        )
        self.assertEqual(new_slippage, 1.5)
        self.assertFalse(exceeded)
    
    def test_exceeds_limit(self) -> None:
        """测试超过限制"""
        new_slippage, exceeded = self.calculator.calculate_next_slippage(
            current_slippage=4.8,
            config=self.test_config,
            trade_type="buy"
        )
        self.assertEqual(new_slippage, 5.0)
        self.assertTrue(exceeded)
    
    def test_disabled_config(self) -> None:
        """测试禁用配置"""
        disabled_config = SlippageRetryConfig(
            enabled=False,
            increment_percentage=0.5,
            max_slippage_percentage=5.0,
            retry_delay_seconds=0.5
        )
        new_slippage, exceeded = self.calculator.calculate_next_slippage(
            current_slippage=1.0,
            config=disabled_config,
            trade_type="buy"
        )
        self.assertEqual(new_slippage, 1.0)
        self.assertFalse(exceeded)
    
    def test_at_limit_check(self) -> None:
        """测试是否达到限制"""
        self.assertFalse(self.calculator.is_slippage_at_limit(4.9, self.test_config))
        self.assertTrue(self.calculator.is_slippage_at_limit(5.0, self.test_config))
    
    def test_remaining_room(self) -> None:
        """测试剩余空间"""
        remaining = self.calculator.get_remaining_slippage_room(1.0, self.test_config)
        self.assertEqual(remaining, 4.0)
    
    def test_steps_remaining(self) -> None:
        """测试剩余步数"""
        steps = self.calculator.calculate_slippage_steps_remaining(1.0, self.test_config)
        self.assertEqual(steps, 8)
    
    def test_config_validation(self) -> None:
        """测试配置验证"""
        self.assertTrue(self.calculator.validate_slippage_config(self.test_config))
        
        invalid_config = SlippageRetryConfig(
            enabled=True,
            increment_percentage=0.0,
            max_slippage_percentage=5.0,
            retry_delay_seconds=0.5
        )
        self.assertFalse(self.calculator.validate_slippage_config(invalid_config))


if __name__ == '__main__':
    unittest.main() 