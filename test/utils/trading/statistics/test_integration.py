#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易统计系统集成测试

测试整个系统的端到端功能
"""

import unittest
from unittest.mock import AsyncMock, MagicMock, patch, Mock
from datetime import datetime, timezone, timedelta
import tempfile
import os
import json
import logging  # 添加logger导入
from pathlib import Path

from utils.trading.statistics.trade_statistics_analyzer import TradeStatisticsAnalyzer
from utils.trading.statistics.api import TradeStatisticsAPI
from utils.trading.statistics.models import StatisticsConfig, ReportFormat
from models.trade_record import TradeRecord, TradeType, TradeStatus


class TestTradeStatisticsIntegration(unittest.IsolatedAsyncioTestCase):
    """交易统计系统集成测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建模拟的依赖项
        self.mock_dao = AsyncMock()
        self.mock_verification_updater = AsyncMock()
        
        # 初始化分析器和API
        self.analyzer = TradeStatisticsAnalyzer(
            trade_record_dao=self.mock_dao,
            verification_updater=self.mock_verification_updater
        )
        self.api = TradeStatisticsAPI()
    
    def tearDown(self):
        """测试后清理"""
        # 清理临时文件
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_comprehensive_test_data(self):
        """创建综合测试数据"""
        test_data = []
        
        # 策略A：高胜率，中等盈利
        for i in range(10):
            signal_id = f"strategy_a_signal_{i}"
            profit_rate = 0.1 if i < 8 else -0.05  # 80%胜率
            
            buy_record = MagicMock(spec=TradeRecord)
            buy_record.id = f"strategy_a_buy_{i}"
            buy_record.signal_id = signal_id
            buy_record.trade_type = TradeType.BUY
            buy_record.status = TradeStatus.SUCCESS
            buy_record.verification_status = "verified"
            buy_record.token_in_amount = 100.0
            buy_record.token_in_actual_amount = 100.0  # 实际花费金额
            buy_record.token_out_verified_amount = 1000.0
            buy_record.token_out_actual_amount = None  # 买入不涉及卖出实际金额
            buy_record.created_at = datetime(2024, 1, 1, 10, i, 0, tzinfo=timezone.utc)
            buy_record.strategy_name = "strategy_a"
            buy_record.token_in_address = "USDT"
            buy_record.token_out_address = f"token_a_{i % 3}"  # 3个不同token
            # 确保这不是Mock对象（移除_mock_name属性以避免我们的Mock检测逻辑）
            if hasattr(buy_record, '_mock_name'):
                delattr(buy_record, '_mock_name')
            
            sell_record = MagicMock(spec=TradeRecord)
            sell_record.id = f"strategy_a_sell_{i}"
            sell_record.signal_id = signal_id
            sell_record.trade_type = TradeType.SELL
            sell_record.status = TradeStatus.SUCCESS
            sell_record.verification_status = "verified"
            sell_record.token_in_amount = 1000.0
            sell_record.token_in_actual_amount = None  # 卖出不涉及买入实际金额
            sell_record.token_out_verified_amount = 100.0 * (1 + profit_rate)
            sell_record.token_out_actual_amount = 100.0 * (1 + profit_rate)  # 实际收入金额
            sell_record.created_at = datetime(2024, 1, 1, 11, i, 0, tzinfo=timezone.utc)
            sell_record.strategy_name = "strategy_a"
            sell_record.token_in_address = f"token_a_{i % 3}"
            sell_record.token_out_address = "USDT"
            # 确保这不是Mock对象
            if hasattr(sell_record, '_mock_name'):
                delattr(sell_record, '_mock_name')
            
            test_data.extend([buy_record, sell_record])
        
        # 策略B：低胜率，高盈利
        for i in range(5):
            signal_id = f"strategy_b_signal_{i}"
            profit_rate = 0.5 if i < 2 else -0.2  # 40%胜率，但盈利时很高
            
            buy_record = MagicMock(spec=TradeRecord)
            buy_record.id = f"strategy_b_buy_{i}"
            buy_record.signal_id = signal_id
            buy_record.trade_type = TradeType.BUY
            buy_record.status = TradeStatus.SUCCESS
            buy_record.verification_status = "verified"
            buy_record.token_in_amount = 200.0
            buy_record.token_in_actual_amount = 200.0  # 实际花费金额
            buy_record.token_out_verified_amount = 2000.0
            buy_record.token_out_actual_amount = None
            buy_record.created_at = datetime(2024, 1, 2, 10, i, 0, tzinfo=timezone.utc)
            buy_record.strategy_name = "strategy_b"
            buy_record.token_in_address = "USDT"
            buy_record.token_out_address = f"token_b_{i % 2}"  # 2个不同token
            # 确保这不是Mock对象
            if hasattr(buy_record, '_mock_name'):
                delattr(buy_record, '_mock_name')
            
            sell_record = MagicMock(spec=TradeRecord)
            sell_record.id = f"strategy_b_sell_{i}"
            sell_record.signal_id = signal_id
            sell_record.trade_type = TradeType.SELL
            sell_record.status = TradeStatus.SUCCESS
            sell_record.verification_status = "verified"
            sell_record.token_in_amount = 2000.0
            sell_record.token_in_actual_amount = None
            sell_record.token_out_verified_amount = 200.0 * (1 + profit_rate)
            sell_record.token_out_actual_amount = 200.0 * (1 + profit_rate)  # 实际收入金额
            sell_record.created_at = datetime(2024, 1, 2, 11, i, 0, tzinfo=timezone.utc)
            sell_record.strategy_name = "strategy_b"
            sell_record.token_in_address = f"token_b_{i % 2}"
            sell_record.token_out_address = "USDT"
            # 确保这不是Mock对象
            if hasattr(sell_record, '_mock_name'):
                delattr(sell_record, '_mock_name')
            
            test_data.extend([buy_record, sell_record])
        
        return test_data
    
    @patch('utils.trading.statistics.trade_statistics_analyzer.TradeRecordDAO')
    async def test_end_to_end_analysis_workflow(self, mock_dao_class):
        """测试端到端分析工作流"""
        # 设置测试数据
        test_data = self.create_comprehensive_test_data()
        mock_dao_class.return_value = self.mock_dao
        self.mock_dao.find_verified_trade_records.return_value = test_data
        
        # 创建配置
        config = StatisticsConfig()
        config.start_date = datetime(2024, 1, 1, tzinfo=timezone.utc)
        config.end_date = datetime(2024, 1, 3, tzinfo=timezone.utc)
        
        # 执行完整分析
        result = await self.analyzer.analyze(config)
        
        # 验证总体统计
        self.assertIsNotNone(result.overall_stats)
        self.assertEqual(result.overall_stats.total_trades, 15)  # 10 + 5
        
        # 验证策略统计
        self.assertEqual(len(result.strategy_stats), 2)
        strategy_a_stats = next(s for s in result.strategy_stats if s.strategy_name == "strategy_a")
        strategy_b_stats = next(s for s in result.strategy_stats if s.strategy_name == "strategy_b")
        
        self.assertEqual(strategy_a_stats.trade_count, 10)
        self.assertAlmostEqual(strategy_a_stats.win_rate, 80.0)  # 8/10 * 100
        
        self.assertEqual(strategy_b_stats.trade_count, 5)
        self.assertAlmostEqual(strategy_b_stats.win_rate, 40.0)  # 2/5 * 100
        
        # 验证Token统计
        self.assertGreater(len(result.token_stats), 0)
        
        # 验证排行榜
        self.assertGreater(len(result.profit_rankings), 0)
        self.assertGreater(len(result.loss_rankings), 0)
    
    @patch('utils.trading.statistics.trade_statistics_analyzer.TradeRecordDAO')
    async def test_html_report_generation(self, mock_dao_class):
        """测试HTML报告生成"""
        # 设置测试数据
        test_data = self.create_comprehensive_test_data()
        mock_dao_class.return_value = self.mock_dao
        self.mock_dao.find_verified_trade_records.return_value = test_data
        
        # 生成HTML报告
        config = StatisticsConfig()
        
        # Mock生成器实例的方法
        with patch.object(self.analyzer.html_generator, 'generate_report', return_value="<html>test</html>") as mock_html_gen:
            with patch.object(self.analyzer.chart_generator, 'generate_all_charts', return_value={}) as mock_chart_gen:
                result = await self.analyzer.generate_report(
                    format=ReportFormat.HTML,
                    config=config
                )
                
                # 验证HTML生成器被正确调用
                mock_html_gen.assert_called_once()
                mock_chart_gen.assert_called_once()
                self.assertIn("html", result)
    
    @patch('utils.trading.statistics.trade_statistics_analyzer.TradeRecordDAO')
    async def test_json_report_generation(self, mock_dao_class):
        """测试JSON报告生成"""
        # 设置测试数据
        test_data = self.create_comprehensive_test_data()
        mock_dao_class.return_value = self.mock_dao
        self.mock_dao.find_verified_trade_records.return_value = test_data
        
        # 生成JSON报告
        config = StatisticsConfig()
        
        # Mock生成器实例的方法
        with patch.object(self.analyzer.json_generator, 'generate_report', return_value='{"test": "data"}') as mock_json_gen:
            result = await self.analyzer.generate_report(
                format=ReportFormat.JSON,
                config=config
            )
            
            # 验证JSON生成器被正确调用
            mock_json_gen.assert_called_once()
            self.assertIn("test", result)
    
    @patch('utils.trading.statistics.api.TradeRecordDAO')
    @patch('models.init_db')
    async def test_api_integration(self, mock_init_db, mock_dao_class):
        """测试API集成"""
        # Mock数据库初始化，避免真实数据库连接
        mock_init_db.return_value = None
        
        # 设置测试数据
        test_data = self.create_comprehensive_test_data()
        mock_dao_class.return_value = self.mock_dao
        self.mock_dao.find_verified_trade_records.return_value = test_data
        
        # 测试获取统计摘要
        summary = await self.api.get_statistics_summary(days=7)
        
        # 验证数据库初始化被调用
        mock_init_db.assert_called()
        
        # 验证摘要结构
        self.assertTrue(summary['success'])
        self.assertIn('overall_stats', summary)
        self.assertIn('token_count', summary)
        self.assertIn('strategy_count', summary)
        self.assertIn('best_profit', summary)
        self.assertIn('worst_loss', summary)
        
        # 测试获取详细统计
        detailed = await self.api.get_detailed_statistics(days=7)
        
        # 验证详细统计结构
        self.assertTrue(detailed['success'])
        self.assertIn('overall_stats', detailed)
        self.assertIn('token_stats', detailed)
        self.assertIn('strategy_stats', detailed)
        self.assertIn('profit_rankings', detailed)
        self.assertIn('loss_rankings', detailed)
    
    @patch('utils.trading.statistics.trade_statistics_analyzer.TradeRecordDAO')
    async def test_data_filtering_integration(self, mock_dao_class):
        """测试数据过滤集成"""
        # 设置测试数据
        test_data = self.create_comprehensive_test_data()
        mock_dao_class.return_value = self.mock_dao
        
        # 模拟策略过滤：只返回strategy_a的数据
        strategy_a_data = [record for record in test_data if record.strategy_name == "strategy_a"]
        self.mock_dao.find_verified_trade_records.return_value = strategy_a_data
        
        # 测试策略过滤
        config = StatisticsConfig()
        config.strategy_filter = ["strategy_a"]
        
        result = await self.analyzer.analyze(config)
        
        # 验证只有strategy_a的统计
        self.assertEqual(len(result.strategy_stats), 1)
        self.assertEqual(result.strategy_stats[0].strategy_name, "strategy_a")
        self.assertEqual(result.overall_stats.total_trades, 10)
        
        # 验证DAO调用参数
        call_args = self.mock_dao.find_verified_trade_records.call_args
        self.assertEqual(call_args.kwargs['strategies'], ["strategy_a"])
    
    @patch('utils.trading.statistics.trade_statistics_analyzer.TradeRecordDAO')
    async def test_error_handling_integration(self, mock_dao_class):
        """测试错误处理集成"""
        # 设置测试数据
        test_data = self.create_comprehensive_test_data()
        mock_dao_class.return_value = self.mock_dao
        self.mock_dao.find_verified_trade_records.side_effect = Exception("Database connection failed")
        
        config = StatisticsConfig()
        
        # 验证异常被正确抛出
        with self.assertRaises(Exception) as context:
            await self.analyzer.analyze(config)
        
        self.assertIn("Database connection failed", str(context.exception))
    
    @patch('utils.trading.statistics.trade_statistics_analyzer.TradeRecordDAO')
    async def test_performance_integration(self, mock_dao_class):
        """测试性能集成"""
        # 设置测试数据
        test_data = self.create_comprehensive_test_data()
        mock_dao_class.return_value = self.mock_dao
        self.mock_dao.find_verified_trade_records.return_value = test_data
        
        # 记录开始时间
        start_time = datetime.now()
        
        # 执行分析
        config = StatisticsConfig()
        result = await self.analyzer.analyze(config)
        
        # 记录结束时间
        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()
        
        # 验证结果
        self.assertEqual(result.overall_stats.total_trades, 15)
        self.assertEqual(len(result.strategy_stats), 2)
        self.assertEqual(len(result.token_stats), 5)
        
        # 验证性能：应该在合理时间内完成
        self.assertLess(execution_time, 5.0, f"小数据集分析耗时过长: {execution_time}秒")
    
    @patch('utils.trading.statistics.trade_statistics_analyzer.TradeRecordDAO')
    async def test_data_validation_integration(self, mock_dao_class):
        """测试数据验证集成"""
        # 设置测试数据
        test_data = self.create_comprehensive_test_data()
        mock_dao_class.return_value = self.mock_dao
        self.mock_dao.find_verified_trade_records.return_value = test_data
        
        # 执行数据验证
        config = StatisticsConfig()
        validation_result = await self.analyzer.validate_data_integrity(config)
        
        # 验证结果
        self.assertIsInstance(validation_result, dict)
        self.assertIn('total_records', validation_result)
        self.assertIn('verified_records', validation_result)
        self.assertIn('missing_verification', validation_result)
        self.assertIn('integrity_percentage', validation_result)
        
        # 验证数据完整性统计
        total_records = validation_result['total_records']
        missing_verification = validation_result['missing_verification']
        self.assertGreater(total_records, 0)
        self.assertGreaterEqual(missing_verification, 0)
        self.assertLessEqual(validation_result['integrity_percentage'], 100.0)
    
    @patch('utils.trading.statistics.api.TradeRecordDAO', new_callable=AsyncMock)
    async def test_api_error_handling(self, mock_dao_class: AsyncMock):
        """Test API error handling when DAO raises an exception."""
        # Configure the instance that will be created by TradeRecordDAO() when the API calls TradeRecordDAO()
        mock_instance = AsyncMock()  # 使用AsyncMock创建实例
        mock_dao_class.return_value = mock_instance
        # 正确设置find_verified_trade_records方法（这是实际被调用的方法）
        mock_instance.find_verified_trade_records.side_effect = Exception("DAO Error")

        summary = await self.api.get_statistics_summary(days=7)
        
        logger = logging.getLogger(__name__)  # 获取logger
        logger.info(f"API error handling test summary: {summary}")
        self.assertFalse(summary["success"])
        # 验证错误处理路径工作正常 - 不管具体错误消息是什么，重要的是错误被捕获并返回合适的响应
        self.assertIn("error", summary)
        # 验证返回了适当的错误响应结构
        self.assertIn("overall_stats", summary)
        self.assertIn("strategy_count", summary)
        self.assertIn("token_count", summary)
        
        # For a generic Exception, we might not have a status_code in the response
        # depending on how the TradeStatisticsAPI's generic catch-all formats it.
        # Based on current TradeStatisticsAPI, it will have "error": "message from e.args or class name"
        # and no "status_code".
        self.assertNotIn("status_code", summary, "status_code should not be in summary for generic exceptions")


class TestTradeStatisticsTestRunner(unittest.TestCase):
    """测试运行器"""
    
    def test_run_all_tests(self):
        """运行所有测试"""
        # 这个测试用于确保测试套件可以正常运行
        self.assertTrue(True)


if __name__ == '__main__':
    # 运行集成测试
    unittest.main(verbosity=2) 