# GmgnTradeService功能单元测试
创建日期：2025-05-14
更新日期：2025-05-20
测试方法：自动化测试
测试级别：单元测试

## 测试用例

| 用例方法 | 用例标题 | 前置条件 | 输入 | 预期输出 | 实际输出 | 状态 |
| --- | --- | --- | --- | --- | --- | --- |
| `test_execute_trade_buy_node_script_success` | 买入交易成功 | Mock `asyncio.create_subprocess_exec` | `trade_type=BUY`, 有效参数, Node脚本输出: `stdout: {"status": "success", "txHash": "mock_tx_hash_buy", "quoteResponse": {...}}`, `stderr: b''`, `returncode: 0` | `TradeResult.status == TradeStatus.SUCCESS`, `tx_hash` 正确, `actual_amount_in/out` 从 `quoteResponse` 解析 | N/A | 已实现并通过 |
| `test_execute_trade_sell_node_script_success` | 卖出交易成功 (无重试) | Mock `asyncio.create_subprocess_exec` | `trade_type=SELL`, 有效参数, Node脚本输出: `stdout: {"status": "success", "txHash": "mock_tx_hash_sell", "quoteResponse": {...}}`, `stderr: b''`, `returncode: 0` | `TradeResult.status == TradeStatus.SUCCESS`, `tx_hash` 正确, `actual_amount_in/out` 从 `quoteResponse` 解析 | N/A | 已实现并通过 |
| `test_execute_trade_node_script_submit_no_poll` | Node.js 脚本返回 "submitted_no_poll" | Mock `asyncio.create_subprocess_exec` | 有效参数, Node脚本输出: `stdout: {"status": "submitted_no_poll", "txHash": "mock_tx_hash_pending", "message": "Polling skipped"}`, `stderr: b''`, `returncode: 0` | `TradeResult.status == TradeStatus.PENDING`, `tx_hash` 正确, `error_message` 包含 "Polling skipped" | N/A | 已实现并通过 |
| `test_execute_trade_node_script_reports_error_with_message` | Node.js 脚本返回含错误信息的JSON | Mock `asyncio.create_subprocess_exec` | 有效参数, Node脚本输出: `stdout: {"status": "error", "message": "GMGN API timeout", "details": {...}}`, `stderr: b''`, `returncode: 0` | `TradeResult.status == TradeStatus.FAILED`, `error_message` 包含 "GMGN API timeout", `provider_response_raw` 包含 `details` | N/A | 已实现并通过 |
| `test_execute_trade_node_script_reports_error_no_message` | Node.js 脚本返回错误状态但无具体message | Mock `asyncio.create_subprocess_exec` | 有效参数, Node脚本输出: `stdout: {"status": "error"}`, `stderr: b''`, `returncode: 0` | `TradeResult.status == TradeStatus.FAILED`, `error_message` 为通用错误信息 | N/A | 已实现并通过 |
| `test_execute_trade_node_script_execution_fails_return_code` | Node.js 脚本执行失败 (非零返回码) | Mock `asyncio.create_subprocess_exec` | 有效参数, Node脚本输出: `stdout: b''`, `stderr: b'Error: Node script crashed'`, `returncode: 1` | `TradeResult.status == TradeStatus.FAILED`, `error_message` 包含 "Node.js script execution failed", `provider_response_raw` 包含 stderr | N/A | 已实现并通过 |
| `test_execute_trade_node_script_invalid_json_output` | Node.js 脚本stdout非有效JSON | Mock `asyncio.create_subprocess_exec` | 有效参数, Node脚本输出: `stdout: b'This is not JSON'`, `stderr: b''`, `returncode: 0` | `TradeResult.status == TradeStatus.FAILED`, `error_message` 包含 "Failed to parse JSON output", `provider_response_raw` 包含原始 stdout | N/A | 已实现并通过 |
| `test_execute_trade_node_script_file_not_found` | Node.js 脚本文件未找到 | Mock `asyncio.create_subprocess_exec` (使其抛出 `FileNotFoundError`) | 有效参数, `asyncio.create_subprocess_exec` 抛出 `FileNotFoundError` | `TradeResult.status == TradeStatus.FAILED`, `error_message` 包含 "Node.js script not found" | N/A | 已实现并通过 |
| `test_execute_trade_unexpected_exception_during_subprocess` | Node.js 脚本执行中发生意外异常 | Mock `asyncio.create_subprocess_exec` (使其抛出 `RuntimeError`) | 有效参数, `asyncio.create_subprocess_exec` 抛出 `RuntimeError` | `TradeResult.status == TradeStatus.FAILED`, `error_message` 包含异常信息 | N/A | 已实现并通过 |
| `test_correct_command_construction` | 验证Node.js脚本命令参数构建 | Patch `asyncio.create_subprocess_exec` | 特定参数组合 (买/卖, 不同滑点/费用), Node脚本成功输出: `stdout: {"status": "success", "txHash": "..."}` | 验证 `create_subprocess_exec` 的 `cmd` 参数符合预期 | N/A | 已实现并通过 |
| `test_execute_trade_extracts_actual_amounts_from_quote` | 从quote正确解析原子单位的金额 | Mock `asyncio.create_subprocess_exec` | 有效参数, Node脚本输出: `stdout: {"status": "success", ..., "quoteResponse": {"data": {"quote": {"inAmount": "1000000000", "outAmount": "200000000"}}}}` | `actual_amount_in == Decimal('1000000000')`, `actual_amount_out == Decimal('200000000')` | N/A | 已实现并通过 |
| `test_execute_trade_handles_missing_quote_for_actual_amounts` | quote数据缺失时金额处理 | Mock `asyncio.create_subprocess_exec` | 有效参数, Node脚本输出: `stdout: {"status": "success", "txHash": "..."}` (无 `quoteResponse`) | `actual_amount_in is None`, `actual_amount_out is None` | N/A | 已实现并通过 |
| `test_execute_trade_handles_malformed_actual_amount_strings` | quote中金额格式错误时处理 | Mock `asyncio.create_subprocess_exec` | 有效参数, Node脚本输出: `stdout: {"status": "success", ..., "quoteResponse": {"data": {"quote": {"inAmount": "invalid", "outAmount": "1.0.0"}}}}` | `actual_amount_in is None`, `actual_amount_out is None`, 记录警告 | N/A | 已实现并通过 |
| `test_provider_response_raw_population` | `provider_response_raw` 正确填充 | Mock `asyncio.create_subprocess_exec` | 有效参数, Node脚本输出: `stdout: {"status":"success", "quoteResponse":{...}, "submitResponse":{...}, "statusResponse":{...}}` | `provider_response_raw` 包含各部分响应 | N/A | 已实现并通过 |
| `test_close_method` | `close()` 方法测试 (空操作) |  | 调用 `service.close()` | 无异常 | N/A | 已实现并通过 |
| `test_node_script_path_is_absolute_and_correct_filename` | `NODE_SCRIPT_PATH` 路径和文件名验证 | `NODE_SCRIPT_PATH` 已定义 (测试中临时移除mock) | 从模块获取 `NODE_SCRIPT_PATH` | `NODE_SCRIPT_PATH` 是绝对路径且文件名为 "gmgn_test.mjs" | N/A | 已实现并通过 |
| `test_execute_trade_parses_onchain_data_success` | 成功解析链上成交数据 | Mock `asyncio.create_subprocess_exec` | `stdout: {"status": "success", ..., "onchain_data_fetch_status": "success", "onchain_actual_amount_in_lamports": "1050000", ...}` | `TradeResult.status == SUCCESS`, `actual_amount_in/out` 优先使用链上数据 | N/A | 已实现并通过 |
| `test_execute_trade_fallback_if_onchain_data_failed` | 链上数据获取失败时回退到报价数据 | Mock `asyncio.create_subprocess_exec` | `stdout: {"status": "success", ..., "onchain_data_fetch_status": "failed_to_fetch", "quoteResponse": {"data": {"quote": {"inAmount": "1000000", ...}}}}` | `TradeResult.status == SUCCESS`, `actual_amount_in/out` 使用报价数据, 可能有警告 | N/A | 已实现并通过 |
| `test_execute_trade_handles_missing_onchain_fields` | 链上数据字段部分缺失时回退 | Mock `asyncio.create_subprocess_exec` | `stdout: {"status": "success", ..., "quoteResponse": {"data": {"quote": {"inAmount": "1000000", ...}}}}` (无 `onchain_` 字段) | `TradeResult.status == SUCCESS`, `actual_amount_in/out` 使用报价数据 | N/A | 已实现并通过 |
| `test_sell_trade_no_retry_on_success_first_attempt` | 卖出首次尝试成功,不重试 | Mock `_execute_single_trade_attempt` 返回成功 | `trade_type=SELL` | `_execute_single_trade_attempt` 被调用1次, 返回 `TradeStatus.SUCCESS` | N/A | 已实现并通过 |
| `test_sell_trade_retries_and_succeeds_on_third_attempt` | 卖出重试并在第3次成功 | Mock `_execute_single_trade_attempt` 前2次失败,第3次成功; Mock `asyncio.sleep` | `trade_type=SELL` | `_execute_single_trade_attempt` 被调用3次, `asyncio.sleep` 被调用2次, 滑点/费用在第3次调用时调整, 返回 `TradeStatus.SUCCESS` | N/A | 已实现并通过 |
| `test_sell_trade_all_10_retries_fail` | 卖出重试10次均失败 | Mock `_execute_single_trade_attempt` 均返回失败; Mock `asyncio.sleep` | `trade_type=SELL` | `_execute_single_trade_attempt` 被调用10次, `asyncio.sleep` 被调用9次, 滑点/费用在第10次调用时调整, 返回 `TradeStatus.FAILED` | N/A | 已实现并通过 |
| `test_buy_trade_does_not_retry_on_failure` | 买入失败不重试 | Mock `_execute_single_trade_attempt` 返回失败 | `trade_type=BUY` | `_execute_single_trade_attempt` 被调用1次, 返回 `TradeStatus.FAILED` | N/A | 已实现并通过 |
| `test_calculate_next_priority_fee` | `_calculate_next_priority_fee` 辅助函数逻辑 | 无 | 多种 `Decimal` 输入值 (如 `0.00005`, `0.00009`, `0.9`, `1`) | 预期递增后的 `Decimal` 值 (如 `0.00006`, `0.00010`, `1.0`, `2`) | N/A | 已实现并通过 |
| `test_sell_spl_uses_correct_decimals_from_snapshot` | 卖出SPL时从快照正确使用`input_token_decimals` | Mock `asyncio.create_subprocess_exec`; 策略快照中提供不同`input_token_decimals`值 (有效/无效/缺失) | `trade_type=SELL`, `input_token_address`为SPL | Node.js脚本命令中的lamports数量根据`input_token_decimals`正确计算; 无效或缺失时返回`TradeStatus.FAILED` | N/A | 已实现并通过 |
| `test_execute_trade_lamport_conversion_error` | lamports转换失败处理 | Mock `Decimal` 构造函数在特定输入时抛异常; Mock `asyncio.sleep` | `trade_type=SELL` (或BUY), `amount_input_token` 会导致转换失败 | `TradeResult.status == TradeStatus.FAILED`, 记录错误日志, `asyncio.sleep` 被调用 (因重试) | N/A | 已实现并通过 |
