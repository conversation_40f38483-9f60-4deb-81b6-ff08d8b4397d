"""
GMGN钱包统计数据DAO单元测试

测试模块: dao.gmgn_wallet_stats_dao
功能: 验证GmgnWalletStatsDAO的所有方法
"""

import unittest
from unittest.mock import patch, Mock, AsyncMock
from datetime import datetime, timedelta
from typing import List, Dict, Any

from dao.gmgn_wallet_stats_dao import GmgnWalletStatsDAO
from models.gmgn_wallet_stats import GmgnWalletStats
from models.kol_wallet import KOLWallet


class TestGmgnWalletStatsDAO(unittest.TestCase):
    """测试GmgnWalletStatsDAO"""
    
    def setUp(self):
        """测试前置设置"""
        self.dao = GmgnWalletStatsDAO()
        self.test_wallet_address = "7BgBvyjrZX1YKz4oh9mjb8ZScatkkwb8DzFx4sxXEP5P"
        self.test_period = "all"
        
        # 测试数据
        self.test_stats_data = {
            "buy": "15",
            "sell": "12",
            "pnl": "0.85",
            "winrate": "0.75",
            "balance": "1000.50",
            "total_value": 1500.0
        }
        
        self.mock_kol_wallet = Mock()
        self.mock_kol_wallet.wallet_address = self.test_wallet_address
        self.mock_kol_wallet.is_active = True
        
        self.mock_stats = Mock()
        self.mock_stats.wallet_address = self.test_wallet_address
        self.mock_stats.period = self.test_period
        self.mock_stats.updated_at = datetime.now()
        self.mock_stats.id = "mock_id_123"
    
    @patch('dao.gmgn_wallet_stats_dao.KOLWallet')
    @patch('dao.gmgn_wallet_stats_dao.GmgnWalletStats')
    async def test_find_wallets_need_stats_update_success(self, mock_stats_model, mock_kol_model):
        """测试用例 2.2.1: 成功获取需要更新的钱包列表"""
        # Mock KOL wallets查询
        mock_kol_model.find.return_value.to_list = AsyncMock(return_value=[self.mock_kol_wallet])
        
        # Mock 最近更新的统计数据查询（返回空，表示都需要更新）
        mock_stats_model.find.return_value.to_list = AsyncMock(return_value=[])
        
        # 调用方法
        result = await self.dao.find_wallets_need_stats_update(
            limit=10, hours_threshold=1, period="all"
        )
        
        # 验证结果
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0], self.test_wallet_address)
        
        # 验证调用
        mock_kol_model.find.assert_called_once()
        mock_stats_model.find.assert_called_once()
    
    @patch('dao.gmgn_wallet_stats_dao.KOLWallet')
    @patch('dao.gmgn_wallet_stats_dao.GmgnWalletStats')
    async def test_find_wallets_need_stats_update_no_active_wallets(self, mock_stats_model, mock_kol_model):
        """测试用例 2.2.2: 没有活跃KOL钱包的情况"""
        # Mock KOL wallets查询返回空
        mock_kol_model.find.return_value.to_list = AsyncMock(return_value=[])
        
        # 调用方法
        result = await self.dao.find_wallets_need_stats_update()
        
        # 验证结果
        self.assertEqual(result, [])
        
        # 验证KOL查询被调用，但统计数据查询没有被调用
        mock_kol_model.find.assert_called_once()
        mock_stats_model.find.assert_not_called()
    
    @patch('dao.gmgn_wallet_stats_dao.KOLWallet')
    @patch('dao.gmgn_wallet_stats_dao.GmgnWalletStats')
    async def test_upsert_wallet_stats_create_new(self, mock_stats_model, mock_kol_model):
        """测试用例 2.1.1: 新增钱包统计数据"""
        # Mock KOL wallet存在验证
        mock_kol_model.find_one = AsyncMock(return_value=self.mock_kol_wallet)
        
        # Mock create_from_api_data
        mock_stats_instance = Mock()
        mock_stats_instance.updated_at = datetime.now()
        mock_stats_instance.model_dump.return_value = {"test": "data"}
        mock_stats_instance.save = AsyncMock()
        mock_stats_instance.save.return_value.id = "new_id_456"
        mock_stats_model.create_from_api_data.return_value = mock_stats_instance
        
        # Mock find_one返回None（不存在，需要创建）
        mock_stats_model.find_one = AsyncMock(return_value=None)
        
        # 调用方法
        result = await self.dao.upsert_wallet_stats(
            wallet_address=self.test_wallet_address,
            stats_data=self.test_stats_data,
            period=self.test_period
        )
        
        # 验证结果
        self.assertTrue(result["success"])
        self.assertEqual(result["operation"], "created")
        self.assertEqual(result["wallet_address"], self.test_wallet_address)
        self.assertEqual(result["period"], self.test_period)
        
        # 验证调用
        mock_kol_model.find_one.assert_called_once()
        mock_stats_model.create_from_api_data.assert_called_once_with(
            wallet_address=self.test_wallet_address,
            api_data=self.test_stats_data,
            period=self.test_period
        )
        mock_stats_instance.save.assert_called_once()
    
    @patch('dao.gmgn_wallet_stats_dao.KOLWallet')
    @patch('dao.gmgn_wallet_stats_dao.GmgnWalletStats')
    async def test_upsert_wallet_stats_update_existing(self, mock_stats_model, mock_kol_model):
        """测试用例 2.1.2: 更新现有钱包统计数据"""
        # Mock KOL wallet存在验证
        mock_kol_model.find_one = AsyncMock(return_value=self.mock_kol_wallet)
        
        # Mock create_from_api_data
        mock_stats_instance = Mock()
        mock_stats_instance.updated_at = datetime.now()
        mock_stats_instance.model_dump.return_value = {"updated": "data"}
        mock_stats_model.create_from_api_data.return_value = mock_stats_instance
        
        # Mock existing record
        mock_existing = Mock()
        mock_existing.id = "existing_id_789"
        mock_existing.update = AsyncMock()
        mock_stats_model.find_one = AsyncMock(return_value=mock_existing)
        
        # 调用方法
        result = await self.dao.upsert_wallet_stats(
            wallet_address=self.test_wallet_address,
            stats_data=self.test_stats_data,
            period=self.test_period
        )
        
        # 验证结果
        self.assertTrue(result["success"])
        self.assertEqual(result["operation"], "updated")
        self.assertEqual(result["stats_id"], "existing_id_789")
        
        # 验证更新被调用
        mock_existing.update.assert_called_once()
    
    @patch('dao.gmgn_wallet_stats_dao.KOLWallet')
    async def test_upsert_wallet_stats_kol_not_found(self, mock_kol_model):
        """测试用例 2.1.3: KOL钱包不存在的情况"""
        # Mock KOL wallet不存在
        mock_kol_model.find_one = AsyncMock(return_value=None)
        
        # 调用方法
        result = await self.dao.upsert_wallet_stats(
            wallet_address=self.test_wallet_address,
            stats_data=self.test_stats_data,
            period=self.test_period
        )
        
        # 验证结果
        self.assertFalse(result["success"])
        self.assertIn("not found in kol_wallets table", result["error"])
        self.assertEqual(result["wallet_address"], self.test_wallet_address)
    
    @patch('dao.gmgn_wallet_stats_dao.GmgnWalletStatsDAO.upsert_wallet_stats')
    async def test_batch_upsert_wallet_stats_success(self, mock_upsert):
        """测试用例 2.2.3: 批量更新成功"""
        # Mock单个upsert操作
        mock_upsert.side_effect = [
            {"success": True, "operation": "created", "wallet_address": "addr1"},
            {"success": True, "operation": "updated", "wallet_address": "addr2"},
            {"success": False, "error": "test error", "wallet_address": "addr3"}
        ]
        
        # 测试数据
        stats_list = [
            {"wallet_address": "addr1", "stats_data": {"pnl": "0.5"}},
            {"wallet_address": "addr2", "stats_data": {"pnl": "0.8"}},
            {"wallet_address": "addr3", "stats_data": {"pnl": "0.2"}}
        ]
        
        # 调用方法
        result = await self.dao.batch_upsert_wallet_stats(
            stats_list=stats_list,
            period="7d"
        )
        
        # 验证结果
        self.assertEqual(result["total_count"], 3)
        self.assertEqual(result["success_count"], 2)
        self.assertEqual(result["error_count"], 1)
        self.assertEqual(len(result["successes"]), 2)
        self.assertEqual(len(result["errors"]), 1)
        
        # 验证调用次数
        self.assertEqual(mock_upsert.call_count, 3)
    
    async def test_batch_upsert_wallet_stats_missing_wallet_address(self):
        """测试用例 2.2.4: 批量更新中缺少钱包地址"""
        # 测试数据（缺少wallet_address）
        stats_list = [
            {"stats_data": {"pnl": "0.5"}},  # 缺少wallet_address
            {"wallet_address": "", "stats_data": {"pnl": "0.8"}}  # 空wallet_address
        ]
        
        # 调用方法
        result = await self.dao.batch_upsert_wallet_stats(stats_list=stats_list)
        
        # 验证结果
        self.assertEqual(result["total_count"], 2)
        self.assertEqual(result["success_count"], 0)
        self.assertEqual(result["error_count"], 2)
        
        # 验证错误信息
        for error in result["errors"]:
            self.assertEqual(error["error"], "Missing wallet_address")
    
    @patch('dao.gmgn_wallet_stats_dao.GmgnWalletStats')
    async def test_get_wallet_stats_with_period(self, mock_stats_model):
        """测试用例 2.3.1: 获取指定period的钱包统计数据"""
        # Mock查询结果
        mock_stats_model.find_one = AsyncMock(return_value=self.mock_stats)
        
        # 调用方法
        result = await self.dao.get_wallet_stats(
            wallet_address=self.test_wallet_address,
            period="7d"
        )
        
        # 验证结果
        self.assertEqual(result, self.mock_stats)
        
        # 验证查询参数
        expected_query = {
            "wallet_address": self.test_wallet_address,
            "chain": "sol",
            "period": "7d"
        }
        mock_stats_model.find_one.assert_called_once_with(expected_query)
    
    @patch('dao.gmgn_wallet_stats_dao.GmgnWalletStats')
    async def test_get_wallet_stats_without_period(self, mock_stats_model):
        """测试用例 2.3.2: 获取最新的钱包统计数据（不指定period）"""
        # Mock查询结果
        mock_find_result = Mock()
        mock_find_result.sort.return_value.limit.return_value.to_list = AsyncMock(
            return_value=[self.mock_stats]
        )
        mock_stats_model.find.return_value = mock_find_result
        
        # 调用方法
        result = await self.dao.get_wallet_stats(
            wallet_address=self.test_wallet_address,
            period=None
        )
        
        # 验证结果
        self.assertEqual(result, self.mock_stats)
        
        # 验证查询被调用
        expected_query = {
            "wallet_address": self.test_wallet_address,
            "chain": "sol"
        }
        mock_stats_model.find.assert_called_once_with(expected_query)
    
    @patch('dao.gmgn_wallet_stats_dao.GmgnWalletStats')
    async def test_get_stats_by_period(self, mock_stats_model):
        """测试用例 2.4.1: 按时间窗口获取统计数据"""
        # Mock查询结果
        mock_find_result = Mock()
        mock_find_result.sort.return_value.skip.return_value.limit.return_value.to_list = AsyncMock(
            return_value=[self.mock_stats, self.mock_stats]
        )
        mock_stats_model.find.return_value = mock_find_result
        
        # 调用方法
        result = await self.dao.get_stats_by_period(
            period="7d",
            limit=50,
            skip=10,
            sort_by="pnl",
            sort_order=-1
        )
        
        # 验证结果
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0], self.mock_stats)
        
        # 验证查询参数
        mock_stats_model.find.assert_called_once_with({"period": "7d"})
    
    @patch('dao.gmgn_wallet_stats_dao.KOLWallet')
    @patch('dao.gmgn_wallet_stats_dao.GmgnWalletStats')
    async def test_get_wallets_with_stats_and_kol_info(self, mock_stats_model, mock_kol_model):
        """测试用例 2.5.1: 获取关联查询结果"""
        # Mock统计数据查询
        mock_find_result = Mock()
        mock_find_result.sort.return_value.limit.return_value.to_list = AsyncMock(
            return_value=[self.mock_stats]
        )
        mock_stats_model.find.return_value = mock_find_result
        
        # Mock KOL数据查询
        mock_kol_model.find.return_value.to_list = AsyncMock(
            return_value=[self.mock_kol_wallet]
        )
        
        # 调用方法
        result = await self.dao.get_wallets_with_stats_and_kol_info(
            period="all",
            min_pnl=0.5,
            min_winrate=0.6,
            limit=20
        )
        
        # 验证结果
        self.assertEqual(len(result), 1)
        stats, kol_info = result[0]
        self.assertEqual(stats, self.mock_stats)
        self.assertEqual(kol_info, self.mock_kol_wallet)
        
        # 验证查询条件
        expected_stats_query = {
            "period": "all",
            "pnl": {"$gte": 0.5},
            "winrate": {"$gte": 0.6}
        }
        mock_stats_model.find.assert_called_once_with(expected_stats_query)
    
    @patch('dao.gmgn_wallet_stats_dao.GmgnWalletStats')
    async def test_delete_stats_by_period(self, mock_stats_model):
        """测试用例 2.6.1: 按period删除统计数据"""
        # Mock查询和删除
        mock_find_result = Mock()
        mock_find_result.count = AsyncMock(return_value=5)
        mock_find_result.delete = AsyncMock(return_value=5)
        mock_stats_model.find.return_value = mock_find_result
        
        # 调用方法
        result = await self.dao.delete_stats_by_period(
            period="1d",
            wallet_address="specific_wallet"
        )
        
        # 验证结果
        self.assertEqual(result, 5)
        
        # 验证查询条件
        expected_query = {
            "period": "1d",
            "wallet_address": "specific_wallet"
        }
        # find被调用两次：一次count，一次delete
        self.assertEqual(mock_stats_model.find.call_count, 2)
        mock_stats_model.find.assert_called_with(expected_query)
    
    @patch('dao.gmgn_wallet_stats_dao.GmgnWalletStats')
    async def test_get_stats_summary(self, mock_stats_model):
        """测试用例 2.7.1: 获取统计概览"""
        # Mock count查询
        mock_stats_model.count = AsyncMock(return_value=100)
        
        # Mock聚合查询
        mock_aggregate_result = Mock()
        mock_aggregate_result.to_list = AsyncMock(return_value=[
            {
                "_id": "all",
                "count": 50,
                "avg_pnl": 0.8,
                "avg_winrate": 0.65,
                "last_updated": datetime.now()
            },
            {
                "_id": "7d",
                "count": 30,
                "avg_pnl": 0.6,
                "avg_winrate": 0.7,
                "last_updated": datetime.now()
            }
        ])
        mock_stats_model.aggregate.return_value = mock_aggregate_result
        
        # Mock最新记录查询
        mock_find_result = Mock()
        mock_find_result.sort.return_value.limit.return_value.to_list = AsyncMock(
            return_value=[self.mock_stats]
        )
        mock_stats_model.find.return_value = mock_find_result
        
        # 调用方法
        result = await self.dao.get_stats_summary()
        
        # 验证结果结构
        self.assertEqual(result["total_records"], 100)
        self.assertEqual(len(result["period_breakdown"]), 2)
        self.assertEqual(result["last_update"], self.mock_stats.updated_at)
        self.assertIn("generated_at", result)
        
        # 验证各种查询被调用
        mock_stats_model.count.assert_called_once()
        mock_stats_model.aggregate.assert_called_once()
        mock_stats_model.find.assert_called_once()


if __name__ == '__main__':
    unittest.main() 