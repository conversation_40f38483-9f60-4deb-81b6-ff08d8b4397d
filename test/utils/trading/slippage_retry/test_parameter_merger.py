"""参数合并器单元测试"""

import unittest
from unittest.mock import Mock
from models.config import TradingParams, RetryDelayStrategy, SingleKolStrategyConfig
from models.slippage_retry import SlippageRetryConfig
from utils.trading.slippage_retry.parameter_merger import ParameterMerger


class TestParameterMerger(unittest.TestCase):
    """参数合并器测试类"""
    
    def setUp(self) -> None:
        """测试前准备"""
        self.merger = ParameterMerger()
        
        # 全局默认参数
        self.global_params = TradingParams(
            # 基础交易参数
            default_buy_amount_sol=0.01,
            default_buy_slippage_percentage=1.0,
            default_buy_priority_fee_sol=0.00005,
            default_sell_slippage_percentage=1.5,
            default_sell_priority_fee_sol=0.00008,
            
            # 滑点重试参数
            enable_slippage_retry=False,
            slippage_increment_percentage=0.5,
            max_slippage_percentage=5.0,
            
            # 重试间隔参数
            retry_delay_seconds=1.0,
            max_retry_delay_seconds=10.0,
            retry_delay_strategy=RetryDelayStrategy.FIXED,
            slippage_error_delay_seconds=0.5
        )
        
        # 渠道级别参数
        self.channel_params = TradingParams(
            # 渠道可能有不同的默认值
            default_buy_amount_sol=0.02,
            default_buy_slippage_percentage=1.2,
            enable_slippage_retry=True,
            slippage_increment_percentage=0.3,
            max_slippage_percentage=8.0,
            retry_delay_seconds=0.5,
            retry_delay_strategy=RetryDelayStrategy.LINEAR
        )
        
        # 策略配置
        self.strategy_config = SingleKolStrategyConfig(
            strategy_name="test_strategy",
            transaction_lookback_hours=24,
            transaction_min_amount=0.001,
            kol_account_min_count=5,
            
            # 策略级别交易参数覆盖
            buy_amount_sol=0.05,
            buy_slippage_percentage=2.0,
            sell_slippage_percentage=3.0,
            
            # 策略级别滑点重试覆盖
            strategy_enable_slippage_retry=True,
            strategy_slippage_increment_percentage=0.8,
            strategy_max_slippage_percentage=10.0,
            
            # 买卖独立滑点重试
            strategy_enable_buy_slippage_retry=True,
            strategy_buy_slippage_increment_percentage=0.6,
            strategy_max_buy_slippage_percentage=12.0,
            
            strategy_enable_sell_slippage_retry=False,
            strategy_sell_slippage_increment_percentage=1.0,
            strategy_max_sell_slippage_percentage=15.0,
            
            # 重试间隔覆盖
            strategy_retry_delay_seconds=0.2,
            strategy_retry_delay_strategy="exponential",
            strategy_max_retry_delay_seconds=5.0,
            strategy_slippage_error_delay_seconds=0.1
        )
    
    def test_merge_trading_params_global_only(self) -> None:
        """测试仅使用全局参数的合并"""
        result = self.merger.merge_trading_params_with_slippage_retry(
            global_params=self.global_params,
            channel_params=None,
            strategy_config=None,
            runtime_overrides=None
        )
        
        # 应该完全等于全局参数
        self.assertEqual(result.default_buy_amount_sol, 0.01)
        self.assertEqual(result.enable_slippage_retry, False)
        self.assertEqual(result.slippage_increment_percentage, 0.5)
        self.assertEqual(result.retry_delay_strategy, RetryDelayStrategy.FIXED)
    
    def test_merge_trading_params_global_plus_channel(self) -> None:
        """测试全局 + 渠道参数合并"""
        result = self.merger.merge_trading_params_with_slippage_retry(
            global_params=self.global_params,
            channel_params=self.channel_params,
            strategy_config=None,
            runtime_overrides=None
        )
        
        # 渠道参数应该覆盖全局参数
        self.assertEqual(result.default_buy_amount_sol, 0.02)  # 渠道覆盖
        self.assertEqual(result.enable_slippage_retry, True)   # 渠道覆盖
        self.assertEqual(result.slippage_increment_percentage, 0.3)  # 渠道覆盖
        self.assertEqual(result.retry_delay_strategy, RetryDelayStrategy.LINEAR)  # 渠道覆盖
        
        # 渠道未设置的字段应保持全局默认值
        self.assertEqual(result.default_sell_priority_fee_sol, 0.00005)  # 全局默认
    
    def test_merge_trading_params_all_levels(self) -> None:
        """测试全局 + 渠道 + 策略参数合并"""
        result = self.merger.merge_trading_params_with_slippage_retry(
            global_params=self.global_params,
            channel_params=self.channel_params,
            strategy_config=self.strategy_config,
            runtime_overrides=None
        )
        
        # 验证策略级别覆盖生效（最高优先级）
        self.assertEqual(result.default_buy_amount_sol, 0.05)  # 策略覆盖
        self.assertEqual(result.default_buy_slippage_percentage, 2.0)  # 策略覆盖
        self.assertEqual(result.default_sell_slippage_percentage, 3.0)  # 策略覆盖
        
        # 验证滑点重试配置覆盖
        self.assertEqual(result.enable_slippage_retry, True)  # 策略覆盖
        self.assertEqual(result.slippage_increment_percentage, 0.8)  # 策略覆盖
        self.assertEqual(result.max_slippage_percentage, 10.0)  # 策略覆盖
        
        # 验证买卖独立滑点重试配置
        self.assertEqual(result.enable_buy_slippage_retry, True)  # 策略覆盖
        self.assertEqual(result.buy_slippage_increment_percentage, 0.6)  # 策略覆盖
        self.assertEqual(result.max_buy_slippage_percentage, 12.0)  # 策略覆盖
        
        self.assertEqual(result.enable_sell_slippage_retry, False)  # 策略覆盖
        self.assertEqual(result.sell_slippage_increment_percentage, 1.0)  # 策略覆盖
        self.assertEqual(result.max_sell_slippage_percentage, 15.0)  # 策略覆盖
        
        # 验证重试间隔配置覆盖
        self.assertEqual(result.retry_delay_seconds, 0.2)  # 策略覆盖
        self.assertEqual(result.retry_delay_strategy, RetryDelayStrategy.EXPONENTIAL)  # 策略覆盖
        self.assertEqual(result.max_retry_delay_seconds, 5.0)  # 策略覆盖
        self.assertEqual(result.slippage_error_delay_seconds, 0.1)  # 策略覆盖
    
    def test_merge_trading_params_with_runtime_overrides(self) -> None:
        """测试运行时覆盖参数合并"""
        runtime_overrides = {
            "default_buy_amount_sol": 0.1,
            "enable_slippage_retry": False,
            "retry_delay_strategy": "fixed",
            "max_retry_delay_seconds": 20.0
        }
        
        result = self.merger.merge_trading_params_with_slippage_retry(
            global_params=self.global_params,
            channel_params=self.channel_params,
            strategy_config=self.strategy_config,
            runtime_overrides=runtime_overrides
        )
        
        # 运行时覆盖应该有最高优先级
        self.assertEqual(result.default_buy_amount_sol, 0.1)  # 运行时覆盖
        self.assertEqual(result.enable_slippage_retry, False)  # 运行时覆盖
        self.assertEqual(result.retry_delay_strategy, RetryDelayStrategy.FIXED)  # 运行时覆盖
        self.assertEqual(result.max_retry_delay_seconds, 20.0)  # 运行时覆盖
        
        # 其他策略覆盖应该保持
        self.assertEqual(result.default_buy_slippage_percentage, 2.0)  # 策略覆盖保持
        self.assertEqual(result.slippage_increment_percentage, 0.8)  # 策略覆盖保持
    
    def test_extract_slippage_retry_config_buy(self) -> None:
        """测试提取买入滑点重试配置"""
        merged_params = self.merger.merge_trading_params_with_slippage_retry(
            global_params=self.global_params,
            channel_params=self.channel_params,
            strategy_config=self.strategy_config
        )
        
        config = self.merger.extract_slippage_retry_config(
            trading_params=merged_params,
            trade_type="buy"
        )
        
        self.assertIsInstance(config, SlippageRetryConfig)
        self.assertEqual(config.enabled, True)  # 买入滑点重试启用
        self.assertEqual(config.increment_percentage, 0.6)  # 买入特定增量
        self.assertEqual(config.max_slippage_percentage, 12.0)  # 买入特定上限
        self.assertEqual(config.config_source, "merged_trading_params")
    
    def test_extract_slippage_retry_config_sell(self) -> None:
        """测试提取卖出滑点重试配置"""
        merged_params = self.merger.merge_trading_params_with_slippage_retry(
            global_params=self.global_params,
            channel_params=self.channel_params,
            strategy_config=self.strategy_config
        )
        
        config = self.merger.extract_slippage_retry_config(
            trading_params=merged_params,
            trade_type="sell"
        )
        
        self.assertIsInstance(config, SlippageRetryConfig)
        self.assertEqual(config.enabled, False)  # 卖出滑点重试禁用
        self.assertEqual(config.increment_percentage, 1.0)  # 卖出特定增量
        self.assertEqual(config.max_slippage_percentage, 15.0)  # 卖出特定上限
        self.assertEqual(config.config_source, "merged_trading_params")
    
    def test_apply_channel_overrides(self) -> None:
        """测试渠道级别覆盖应用"""
        result = self.merger._apply_channel_overrides(
            base_params=self.global_params,
            channel_params=self.channel_params
        )
        
        # 渠道参数应该完全替换基础参数
        self.assertEqual(result.default_buy_amount_sol, 0.02)
        self.assertEqual(result.enable_slippage_retry, True)
        self.assertEqual(result.slippage_increment_percentage, 0.3)
        self.assertEqual(result.retry_delay_strategy, RetryDelayStrategy.LINEAR)
    
    def test_apply_strategy_overrides(self) -> None:
        """测试策略级别覆盖应用"""
        result = self.merger._apply_strategy_overrides(
            base_params=self.channel_params,
            strategy_config=self.strategy_config
        )
        
        # 策略字段应该正确映射到交易参数字段
        self.assertEqual(result.default_buy_amount_sol, 0.05)
        self.assertEqual(result.enable_slippage_retry, True)  # strategy_enable_slippage_retry
        self.assertEqual(result.slippage_increment_percentage, 0.8)  # strategy_slippage_increment_percentage
        self.assertEqual(result.retry_delay_seconds, 0.2)  # strategy_retry_delay_seconds
        self.assertEqual(result.retry_delay_strategy, RetryDelayStrategy.EXPONENTIAL)  # strategy_retry_delay_strategy
    
    def test_apply_strategy_overrides_partial(self) -> None:
        """测试部分策略覆盖"""
        # 创建只有部分覆盖字段的策略配置
        partial_strategy = SingleKolStrategyConfig(
            strategy_name="partial_strategy",
            transaction_lookback_hours=24,
            transaction_min_amount=0.001,
            kol_account_min_count=5,
            
            # 只覆盖部分字段
            buy_amount_sol=0.03,
            strategy_enable_slippage_retry=True,
            strategy_retry_delay_strategy="linear"
        )
        
        result = self.merger._apply_strategy_overrides(
            base_params=self.channel_params,
            strategy_config=partial_strategy
        )
        
        # 被覆盖的字段
        self.assertEqual(result.default_buy_amount_sol, 0.03)
        self.assertEqual(result.enable_slippage_retry, True)
        self.assertEqual(result.retry_delay_strategy, RetryDelayStrategy.LINEAR)
        
        # 未覆盖的字段应保持原值
        self.assertEqual(result.default_buy_slippage_percentage, 1.2)  # 保持渠道值
        self.assertEqual(result.slippage_increment_percentage, 0.3)  # 保持渠道值
    
    def test_apply_runtime_overrides(self) -> None:
        """测试运行时覆盖应用"""
        runtime_overrides = {
            "default_buy_amount_sol": 0.08,
            "enable_slippage_retry": False,
            "retry_delay_strategy": "exponential",
            "max_retry_delay_seconds": 25.0,
            "invalid_field": "should_be_ignored"  # 无效字段应被忽略
        }
        
        result = self.merger._apply_runtime_overrides(
            base_params=self.global_params,
            runtime_overrides=runtime_overrides
        )
        
        # 有效字段应该被覆盖
        self.assertEqual(result.default_buy_amount_sol, 0.08)
        self.assertEqual(result.enable_slippage_retry, False)
        self.assertEqual(result.retry_delay_strategy, RetryDelayStrategy.EXPONENTIAL)
        self.assertEqual(result.max_retry_delay_seconds, 25.0)
        
        # 未覆盖的字段应保持原值
        self.assertEqual(result.default_sell_slippage_percentage, 1.5)
        self.assertEqual(result.slippage_increment_percentage, 0.5)
    
    def test_apply_runtime_overrides_invalid_enum(self) -> None:
        """测试运行时覆盖中的无效枚举值"""
        runtime_overrides = {
            "retry_delay_strategy": "invalid_strategy",  # 无效的策略值
            "default_buy_amount_sol": 0.05
        }
        
        result = self.merger._apply_runtime_overrides(
            base_params=self.global_params,
            runtime_overrides=runtime_overrides
        )
        
        # 无效枚举值应被跳过，字段保持原值
        self.assertEqual(result.retry_delay_strategy, RetryDelayStrategy.FIXED)  # 保持原值
        
        # 有效字段应该被覆盖
        self.assertEqual(result.default_buy_amount_sol, 0.05)
    
    def test_get_merge_summary(self) -> None:
        """测试合并摘要生成"""
        final_params = self.merger.merge_trading_params_with_slippage_retry(
            global_params=self.global_params,
            channel_params=self.channel_params,
            strategy_config=self.strategy_config
        )
        
        summary = self.merger.get_merge_summary(
            global_params=self.global_params,
            channel_params=self.channel_params,
            strategy_config=self.strategy_config,
            runtime_overrides=None,
            final_params=final_params
        )
        
        # 摘要应该包含主要变化
        self.assertIn("滑点重试: False → True", summary)
        self.assertIn("买入滑点: 1.0% → 2.0%", summary)
        self.assertIn("卖出滑点: 1.5% → 3.0%", summary)
        self.assertIn("重试间隔: 1.0s → 0.2s", summary)
    
    def test_get_merge_summary_no_changes(self) -> None:
        """测试无变化时的合并摘要"""
        # 使用相同参数，应该无变化
        summary = self.merger.get_merge_summary(
            global_params=self.global_params,
            channel_params=None,
            strategy_config=None,
            runtime_overrides=None,
            final_params=self.global_params
        )
        
        self.assertEqual(summary, "无参数覆盖，使用全局默认配置")
    
    def test_merge_with_none_values(self) -> None:
        """测试包含None值的合并"""
        # 策略配置中包含None值
        strategy_with_none = SingleKolStrategyConfig(
            strategy_name="test_strategy",
            transaction_lookback_hours=24,
            transaction_min_amount=0.001,
            kol_account_min_count=5,
            
            buy_amount_sol=None,  # None值应被忽略
            strategy_enable_slippage_retry=True,
            strategy_slippage_increment_percentage=None  # None值应被忽略
        )
        
        result = self.merger.merge_trading_params_with_slippage_retry(
            global_params=self.global_params,
            channel_params=self.channel_params,
            strategy_config=strategy_with_none
        )
        
        # None值的字段应该保持前一级别的值
        self.assertEqual(result.default_buy_amount_sol, 0.02)  # 保持渠道值
        self.assertEqual(result.slippage_increment_percentage, 0.3)  # 保持渠道值
        
        # 非None值应该被应用
        self.assertEqual(result.enable_slippage_retry, True)
    
    def test_invalid_strategy_retry_delay_strategy(self) -> None:
        """测试策略中无效的重试延迟策略"""
        invalid_strategy = SingleKolStrategyConfig(
            strategy_name="invalid_strategy",
            transaction_lookback_hours=24,
            transaction_min_amount=0.001,
            kol_account_min_count=5,
            
            strategy_retry_delay_strategy="invalid_strategy_value"
        )
        
        result = self.merger._apply_strategy_overrides(
            base_params=self.global_params,
            strategy_config=invalid_strategy
        )
        
        # 无效值应被跳过，保持原值
        self.assertEqual(result.retry_delay_strategy, RetryDelayStrategy.FIXED)
    
    def test_complex_override_priority(self) -> None:
        """测试复杂的覆盖优先级场景"""
        # 测试所有层级都有不同值的情况
        runtime_overrides = {
            "default_buy_amount_sol": 0.2,  # 运行时覆盖
            "enable_slippage_retry": False,  # 运行时覆盖
            "retry_delay_seconds": 3.0  # 运行时覆盖
        }
        
        result = self.merger.merge_trading_params_with_slippage_retry(
            global_params=self.global_params,  # buy_amount=0.01, enable=False, retry_delay=1.0
            channel_params=self.channel_params,  # buy_amount=0.02, enable=True, retry_delay=0.5
            strategy_config=self.strategy_config,  # buy_amount=0.05, enable=True, retry_delay=0.2
            runtime_overrides=runtime_overrides  # buy_amount=0.2, enable=False, retry_delay=3.0
        )
        
        # 验证优先级：运行时 > 策略 > 渠道 > 全局
        self.assertEqual(result.default_buy_amount_sol, 0.2)  # 运行时最高优先级
        self.assertEqual(result.enable_slippage_retry, False)  # 运行时最高优先级
        self.assertEqual(result.retry_delay_seconds, 3.0)  # 运行时最高优先级
        
        # 未被运行时覆盖的字段应该使用策略级别的值
        self.assertEqual(result.slippage_increment_percentage, 0.8)  # 策略值
        self.assertEqual(result.max_slippage_percentage, 10.0)  # 策略值
    
    def test_edge_cases(self) -> None:
        """测试边界情况"""
        # 全部参数为None
        result = self.merger.merge_trading_params_with_slippage_retry(
            global_params=self.global_params,
            channel_params=None,
            strategy_config=None,
            runtime_overrides=None
        )
        # 应该等于全局参数
        self.assertEqual(result, self.global_params)
        
        # 空的运行时覆盖
        result = self.merger.merge_trading_params_with_slippage_retry(
            global_params=self.global_params,
            channel_params=None,
            strategy_config=None,
            runtime_overrides={}
        )
        # 应该等于全局参数
        self.assertEqual(result, self.global_params)
        
        # 只有运行时覆盖
        runtime_only = {"default_buy_amount_sol": 0.15}
        result = self.merger.merge_trading_params_with_slippage_retry(
            global_params=self.global_params,
            channel_params=None,
            strategy_config=None,
            runtime_overrides=runtime_only
        )
        # 只有指定字段被覆盖
        self.assertEqual(result.default_buy_amount_sol, 0.15)
        self.assertEqual(result.enable_slippage_retry, False)  # 保持全局值
    
    def test_case_sensitivity(self) -> None:
        """测试交易类型大小写敏感性"""
        merged_params = self.merger.merge_trading_params_with_slippage_retry(
            global_params=self.global_params,
            channel_params=self.channel_params,
            strategy_config=self.strategy_config
        )
        
        # 测试不同大小写的交易类型
        buy_config = self.merger.extract_slippage_retry_config(merged_params, "buy")
        BUY_config = self.merger.extract_slippage_retry_config(merged_params, "BUY")
        Buy_config = self.merger.extract_slippage_retry_config(merged_params, "Buy")
        
        # 都应该得到相同的买入配置
        self.assertEqual(buy_config.enabled, BUY_config.enabled)
        self.assertEqual(buy_config.enabled, Buy_config.enabled)
        self.assertEqual(buy_config.increment_percentage, BUY_config.increment_percentage)
        self.assertEqual(buy_config.increment_percentage, Buy_config.increment_percentage)
        
        sell_config = self.merger.extract_slippage_retry_config(merged_params, "sell")
        SELL_config = self.merger.extract_slippage_retry_config(merged_params, "SELL")
        Sell_config = self.merger.extract_slippage_retry_config(merged_params, "Sell")
        
        # 都应该得到相同的卖出配置
        self.assertEqual(sell_config.enabled, SELL_config.enabled)
        self.assertEqual(sell_config.enabled, Sell_config.enabled)
        self.assertEqual(sell_config.increment_percentage, SELL_config.increment_percentage)
        self.assertEqual(sell_config.increment_percentage, Sell_config.increment_percentage)


if __name__ == '__main__':
    unittest.main() 