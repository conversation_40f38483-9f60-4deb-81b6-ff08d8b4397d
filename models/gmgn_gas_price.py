from datetime import datetime
from typing import Optional
from beanie import Document, Indexed
from pydantic import BaseModel, Field

class GmgnGasPrice(Document):
    """GMGN Gas价格信息模型
    
    用于存储链上的gas价格信息，包括：
    - 基本信息（链名称、最新区块）
    - gas价格（高、中、低）
    - 优先费用
    - 代币价格
    - 预估时间
    """
    
    # 基本信息
    chain: str = Field(description="链名称")
    last_block: int = Field(description="最新区块")
    
    # Gas价格
    high: str = Field(description="高gas价格")
    average: str = Field(description="平均gas价格")
    low: str = Field(description="低gas价格")
    suggest_base_fee: str = Field(description="建议基础费用")
    
    # 优先费用
    high_prio_fee: str = Field(description="高优先费用")
    average_prio_fee: str = Field(description="平均优先费用")
    low_prio_fee: str = Field(description="低优先费用")
    high_prio_fee_mixed: str = Field(description="混合高优先费用")
    average_prio_fee_mixed: str = Field(description="混合平均优先费用")
    low_prio_fee_mixed: str = Field(description="混合低优先费用")
    
    # 代币价格
    native_token_usd_price: float = Field(description="原生代币USD价格")
    eth_usd_price: float = Field(description="ETH USD价格")
    
    # 预估时间
    high_estimate_time: int = Field(description="高gas预估时间(秒)")
    average_estimate_time: int = Field(description="平均gas预估时间(秒)")
    low_estimate_time: int = Field(description="低gas预估时间(秒)")
    
    # 原始价格
    high_orign: str = Field(description="原始高gas价格")
    average_orign: str = Field(description="原始平均gas价格")
    low_orign: str = Field(description="原始低gas价格")
    
    # 时间信息
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")
    
    class Settings:
        name = "gmgn_gas_price"
        use_state_management = True
    
    @classmethod
    async def from_api_data(cls, chain: str, data: dict) -> 'GmgnGasPrice':
        """从API数据创建模型实例
        
        Args:
            chain: 链名称
            data: API返回的数据
            
        Returns:
            GmgnGasPrice: 模型实例
        """
        return cls(
            chain=chain,
            last_block=data.get('last_block', 0),
            high=data.get('high', '0'),
            average=data.get('average', '0'),
            low=data.get('low', '0'),
            suggest_base_fee=data.get('suggest_base_fee', '0'),
            high_prio_fee=data.get('high_prio_fee', '0'),
            average_prio_fee=data.get('average_prio_fee', '0'),
            low_prio_fee=data.get('low_prio_fee', '0'),
            high_prio_fee_mixed=data.get('high_prio_fee_mixed', '0'),
            average_prio_fee_mixed=data.get('average_prio_fee_mixed', '0'),
            low_prio_fee_mixed=data.get('low_prio_fee_mixed', '0'),
            native_token_usd_price=float(data.get('native_token_usd_price', 0)),
            eth_usd_price=float(data.get('eth_usd_price', 0)),
            high_estimate_time=data.get('high_estimate_time', 0),
            average_estimate_time=data.get('average_estimate_time', 0),
            low_estimate_time=data.get('low_estimate_time', 0),
            high_orign=data.get('high_orign', '0'),
            average_orign=data.get('average_orign', '0'),
            low_orign=data.get('low_orign', '0')
        )
    
    class Config:
        json_schema_extra = {
            "example": {
                "chain": "sol",
                "last_block": 1,
                "high": "0.01",
                "average": "0.005",
                "low": "0.001",
                "suggest_base_fee": "1",
                "high_prio_fee": "1",
                "average_prio_fee": "1",
                "low_prio_fee": "1",
                "high_prio_fee_mixed": "0.01",
                "average_prio_fee_mixed": "0.005",
                "low_prio_fee_mixed": "0.001",
                "native_token_usd_price": 145.09,
                "eth_usd_price": 145.09,
                "high_estimate_time": 2,
                "average_estimate_time": 4,
                "low_estimate_time": 10,
                "high_orign": "0.01",
                "average_orign": "0.005",
                "low_orign": "0.001",
                "created_at": "2024-03-02T00:00:00",
                "updated_at": "2024-03-02T00:00:00"
            }
        } 