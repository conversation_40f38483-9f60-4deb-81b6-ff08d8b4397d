"""滑点重试配置迁移工具 - 辅助从旧配置迁移到新的滑点重试配置"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from dao.config_dao import ConfigDAO
from models.config import (
    Config, AutoTradeManagerConfig, AutoTradeConfig, 
    TradeChannelConfig, TradingParams, WalletConfig, NotificationConfig,
    SingleKolStrategyConfig
)

logger = logging.getLogger(__name__)


class SlippageConfigMigrator:
    """滑点重试配置迁移器"""
    
    def __init__(self):
        self.config_dao = ConfigDAO()
    
    async def migrate_from_legacy_config(
        self, 
        legacy_strategy_config: SingleKolStrategyConfig,
        dry_run: bool = True
    ) -> Dict[str, Any]:
        """
        从旧的策略配置迁移到新的滑点重试配置
        
        Args:
            legacy_strategy_config: 旧的策略配置
            dry_run: 是否为试运行模式（不实际保存）
            
        Returns:
            Dict[str, Any]: 迁移结果和建议配置
        """
        migration_result = {
            "success": False,
            "changes": [],
            "warnings": [],
            "suggested_auto_trade_config": None,
            "updated_strategy_config": None
        }
        
        try:
            # 分析旧配置并生成新配置
            auto_trade_config = self._generate_auto_trade_config(legacy_strategy_config, migration_result)
            updated_strategy_config = self._clean_strategy_config(legacy_strategy_config, migration_result)
            
            migration_result["suggested_auto_trade_config"] = auto_trade_config
            migration_result["updated_strategy_config"] = updated_strategy_config
            
            if not dry_run:
                # 实际保存新配置
                await self._save_auto_trade_config(auto_trade_config)
                await self._update_strategy_config(updated_strategy_config)
                migration_result["changes"].append("配置已成功保存到数据库")
            else:
                migration_result["changes"].append("试运行模式：配置未保存，仅生成建议")
            
            migration_result["success"] = True
            logger.info(f"配置迁移{'完成' if not dry_run else '试运行完成'}，变更数: {len(migration_result['changes'])}")
            
        except Exception as e:
            logger.error(f"配置迁移失败: {e}", exc_info=True)
            migration_result["warnings"].append(f"迁移失败: {str(e)}")
        
        return migration_result
    
    def _generate_auto_trade_config(
        self, 
        legacy_config: SingleKolStrategyConfig,
        migration_result: Dict[str, Any]
    ) -> AutoTradeConfig:
        """从旧配置生成自动交易配置"""
        
        # 提取钱包配置
        wallet_config = WalletConfig(
            default_private_key_env_var=getattr(legacy_config, 'gmgn_private_key_env_var', 'DEFAULT_WALLET_PRIVATE_KEY'),
            default_wallet_address=getattr(legacy_config, 'gmgn_sol_wallet_address', '')
        )
        
        if wallet_config.default_private_key_env_var != 'DEFAULT_WALLET_PRIVATE_KEY':
            migration_result["changes"].append(f"钱包私钥环境变量: {wallet_config.default_private_key_env_var}")
        if wallet_config.default_wallet_address:
            migration_result["changes"].append(f"默认钱包地址: {wallet_config.default_wallet_address}")
        
        # 生成渠道配置
        channels = []
        
        # GMGN渠道配置
        if hasattr(legacy_config, 'gmgn_api_host'):
            gmgn_trading_params = TradingParams(
                default_buy_amount_sol=getattr(legacy_config, 'gmgn_auto_trade_buy_amount_sol', 0.01),
                default_buy_slippage_percentage=getattr(legacy_config, 'gmgn_buy_slippage_percentage', 1.0),
                default_buy_priority_fee_sol=getattr(legacy_config, 'gmgn_buy_priority_fee', 0.00005),
                default_sell_slippage_percentage=getattr(legacy_config, 'gmgn_sell_slippage_percentage', 1.0),
                default_sell_priority_fee_sol=getattr(legacy_config, 'gmgn_sell_priority_fee', 0.00005),
                
                # 新增滑点重试配置（默认启用）
                enable_slippage_retry=True,
                slippage_increment_percentage=0.5,  # meme币推荐值
                max_slippage_percentage=10.0,
                
                # 重试间隔配置（针对meme币优化）
                retry_delay_seconds=0.5,
                max_retry_delay_seconds=5.0,
                retry_delay_strategy="fixed",
                slippage_error_delay_seconds=0.3
            )
            
            gmgn_channel = TradeChannelConfig(
                channel_type="gmgn",
                priority=1,
                enabled=True,
                timeout_seconds=getattr(legacy_config, 'gmgn_max_retries', 30),
                max_retries=getattr(legacy_config, 'gmgn_max_retries', 3),
                trading_params=gmgn_trading_params,
                channel_params={
                    "api_host": getattr(legacy_config, 'gmgn_api_host', ''),
                    "retry_delay_seconds": getattr(legacy_config, 'gmgn_retry_delay_seconds', 1.0)
                }
            )
            channels.append(gmgn_channel)
            migration_result["changes"].append("已配置GMGN渠道，启用滑点重试")
        
        # Solana Direct渠道配置
        if hasattr(legacy_config, 'use_direct_solana_trading') and getattr(legacy_config, 'use_direct_solana_trading', False):
            solana_trading_params = TradingParams(
                default_buy_amount_sol=getattr(legacy_config, 'gmgn_auto_trade_buy_amount_sol', 0.01),
                default_buy_slippage_percentage=getattr(legacy_config, 'max_slippage_bps', 1000) / 100.0,  # 转换BPS到百分比
                default_buy_priority_fee_sol=getattr(legacy_config, 'priority_fee_lamports', 50000) / 1e9,  # 转换lamports到SOL
                default_sell_slippage_percentage=getattr(legacy_config, 'sell_max_slippage_bps', 1500) / 100.0,
                default_sell_priority_fee_sol=getattr(legacy_config, 'sell_priority_fee_lamports', 75000) / 1e9,
                
                # 新增滑点重试配置（默认启用）
                enable_slippage_retry=True,
                slippage_increment_percentage=0.5,
                max_slippage_percentage=15.0,  # Solana Direct允许更高滑点
                
                # 重试间隔配置
                retry_delay_seconds=0.8,  # Solana Direct稍微保守一些
                max_retry_delay_seconds=8.0,
                retry_delay_strategy="linear",
                slippage_error_delay_seconds=0.5
            )
            
            solana_channel = TradeChannelConfig(
                channel_type="solana_direct", 
                priority=2,  # 作为备用渠道
                enabled=True,
                timeout_seconds=getattr(legacy_config, 'solana_trade_timeout_seconds', 45),
                max_retries=2,
                trading_params=solana_trading_params,
                channel_params={
                    "rpc_endpoint": getattr(legacy_config, 'solana_rpc_url', ''),
                    "commitment": "confirmed",
                    "jupiter_api_host": getattr(legacy_config, 'jupiter_api_host', ''),
                    "http_timeout_seconds": getattr(legacy_config, 'http_timeout_seconds', 30)
                }
            )
            channels.append(solana_channel)
            migration_result["changes"].append("已配置Solana Direct渠道，启用滑点重试")
        
        # 通知配置
        notification_config = NotificationConfig(
            notify_on_failure=True,
            notify_on_fallback=True,
            admin_chat_ids=[],  # 需要手动配置
            include_trade_details=getattr(legacy_config, 'include_trade_details_in_notification', True)
        )
        
        # 生成完整的自动交易配置
        auto_trade_config = AutoTradeConfig(
            enabled=getattr(legacy_config, 'auto_trade_enabled', True),
            wallet_config=wallet_config,
            channels=channels,
            default_timeout=60,
            max_total_retries=5,
            notification_config=notification_config
        )
        
        # 记录主要变更
        if channels:
            migration_result["changes"].append(f"配置了 {len(channels)} 个交易渠道")
        migration_result["changes"].append("启用了meme币优化的滑点重试功能")
        migration_result["changes"].append("支持动态重试配置接口")
        
        return auto_trade_config
    
    def _clean_strategy_config(
        self, 
        legacy_config: SingleKolStrategyConfig,
        migration_result: Dict[str, Any]
    ) -> SingleKolStrategyConfig:
        """清理策略配置，移除已迁移的字段"""
        
        # 需要保留的字段（策略业务逻辑相关）
        preserved_fields = {
            'strategy_name', 'transaction_lookback_hours', 'transaction_min_amount',
            'kol_account_min_count', 'token_mint_lookback_hours', 'kol_account_min_txs',
            'kol_account_max_txs', 'sell_strategy_hours', 'sell_kol_ratio',
            'same_token_notification_interval', 'is_active'
        }
        
        # 可选保留的字段（策略级别覆盖）
        optional_override_fields = {
            'wallet_private_key_env_var': 'gmgn_private_key_env_var',
            'wallet_address': 'gmgn_sol_wallet_address',
            'buy_amount_sol': 'gmgn_auto_trade_buy_amount_sol',
            'buy_slippage_percentage': 'gmgn_buy_slippage_percentage',
            'buy_priority_fee_sol': 'gmgn_buy_priority_fee',
            'sell_slippage_percentage': 'gmgn_sell_slippage_percentage',
            'sell_priority_fee_sol': 'gmgn_sell_priority_fee'
        }
        
        # 构建清理后的配置
        cleaned_config_dict = {}
        
        # 复制保留字段
        for field in preserved_fields:
            if hasattr(legacy_config, field):
                cleaned_config_dict[field] = getattr(legacy_config, field)
        
        # 处理可选覆盖字段（重命名）
        for new_field, old_field in optional_override_fields.items():
            if hasattr(legacy_config, old_field):
                old_value = getattr(legacy_config, old_field)
                cleaned_config_dict[new_field] = old_value
                migration_result["changes"].append(f"策略字段重命名: {old_field} -> {new_field}")
        
        # 记录被移除的字段
        removed_fields = []
        for attr in dir(legacy_config):
            if not attr.startswith('_') and hasattr(legacy_config, attr):
                attr_value = getattr(legacy_config, attr)
                if not callable(attr_value) and attr not in preserved_fields:
                    old_field_found = False
                    for new_field, old_field in optional_override_fields.items():
                        if attr == old_field:
                            old_field_found = True
                            break
                    if not old_field_found:
                        removed_fields.append(attr)
        
        if removed_fields:
            migration_result["changes"].append(f"移除的旧配置字段: {', '.join(removed_fields)}")
            migration_result["warnings"].append("请确认移除的字段不再需要")
        
        # 创建新的配置对象
        try:
            cleaned_config = SingleKolStrategyConfig(**cleaned_config_dict)
            return cleaned_config
        except Exception as e:
            migration_result["warnings"].append(f"创建清理后的策略配置失败: {e}")
            return legacy_config
    
    async def _save_auto_trade_config(self, auto_trade_config: AutoTradeConfig) -> None:
        """保存自动交易配置到数据库"""
        config_data = AutoTradeManagerConfig(auto_trade=auto_trade_config)
        
        config_doc = Config(
            type="auto_trade_manager",
            data=config_data,
            description="滑点重试增强自动交易配置 - 由迁移工具生成"
        )
        
        await config_doc.save()
        logger.info("自动交易配置已保存到数据库")
    
    async def _update_strategy_config(self, strategy_config: SingleKolStrategyConfig) -> None:
        """更新策略配置到数据库"""
        # 这里需要根据实际的策略配置存储方式来实现
        # 暂时记录日志
        logger.info("策略配置更新逻辑需要根据实际存储方式实现")
    
    async def analyze_existing_configs(self) -> Dict[str, Any]:
        """分析现有配置，给出迁移建议"""
        analysis_result = {
            "has_auto_trade_config": False,
            "legacy_strategy_configs_found": 0,
            "migration_needed": False,
            "recommendations": []
        }
        
        try:
            # 检查是否已有自动交易配置
            auto_trade_config = await self.config_dao.get_config("auto_trade_manager")
            if auto_trade_config:
                analysis_result["has_auto_trade_config"] = True
                analysis_result["recommendations"].append("已存在自动交易配置，可能不需要迁移")
            else:
                analysis_result["recommendations"].append("未找到自动交易配置，建议执行迁移")
                analysis_result["migration_needed"] = True
            
            # 检查策略配置（这里需要根据实际情况实现）
            # 暂时模拟
            analysis_result["legacy_strategy_configs_found"] = 0
            analysis_result["recommendations"].append("请检查现有策略配置是否包含旧的交易参数字段")
            
            if analysis_result["migration_needed"]:
                analysis_result["recommendations"].extend([
                    "建议首先执行dry_run模式测试迁移",
                    "迁移前请备份现有配置",
                    "迁移后请验证交易功能是否正常"
                ])
            
        except Exception as e:
            logger.error(f"配置分析失败: {e}")
            analysis_result["recommendations"].append(f"配置分析失败: {e}")
        
        return analysis_result


async def main():
    """迁移工具主函数 - 示例用法"""
    print("滑点重试配置迁移工具")
    print("=" * 50)
    
    migrator = SlippageConfigMigrator()
    
    # 分析现有配置
    print("\n1. 分析现有配置...")
    analysis = await migrator.analyze_existing_configs()
    print(f"自动交易配置存在: {analysis['has_auto_trade_config']}")
    print(f"发现旧策略配置: {analysis['legacy_strategy_configs_found']} 个")
    print(f"需要迁移: {analysis['migration_needed']}")
    print("\n建议:")
    for rec in analysis['recommendations']:
        print(f"  - {rec}")
    
    # 如果需要迁移，可以在这里添加交互式迁移逻辑
    if analysis['migration_needed']:
        print("\n要执行迁移，请创建 SingleKolStrategyConfig 实例并调用 migrate_from_legacy_config 方法")


if __name__ == "__main__":
    import asyncio
    asyncio.run(main()) 