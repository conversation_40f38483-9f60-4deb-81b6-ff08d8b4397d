"""
GMGN钱包统计数据爬虫单元测试

本模块测试GmgnWalletStatsSpider的各项功能，包括：
- 爬虫初始化和设置
- 请求参数生成
- API响应解析
- 错误处理机制
"""

import pytest
import json
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from utils.spiders.smart_money.gmgn_wallet_stats_spider import GmgnWalletStatsSpider
from models.gmgn_wallet_stats import GmgnWalletStats


class TestGmgnWalletStatsSpider:
    """GmgnWalletStatsSpider测试类"""
    
    @pytest.fixture
    def spider(self):
        """创建测试用的爬虫实例"""
        return GmgnWalletStatsSpider(max_retries=3, retry_interval=0.1)
    
    @pytest.fixture
    def mock_response_data(self):
        """模拟的API响应数据"""
        return {
            "code": 0,
            "msg": "success",
            "data": {
                "wallet_address": "test_wallet_123",
                "buy": 10,
                "sell": 8,
                "total_count": 18,
                "win_rate": "0.6",
                "realized_pnl": "1500.50",
                "realized_pnl_7d": "300.25",
                "unrealized_pnl": "200.75",
                "total_profit": "1701.25",
                "avg_buy_price": "50.5",
                "avg_sell_price": "75.8",
                "last_active": 1704067200,
                "risk": {
                    "avg_cost": "25.5",
                    "avg_sold": "45.8",
                    "total_cost": "255.0",
                    "avg_holding_period": "2.5"
                }
            }
        }
    
    async def test_spider_initialization(self, spider):
        """测试用例 1.1: 爬虫初始化"""
        assert spider.max_retries == 3
        assert spider.retry_interval == 0.1
        assert spider.base_url == "https://gmgn.ai/api/v1/wallet_stat/sol/{}/all"
        assert spider.device_id is None
        assert spider.client_id is None
    
    @patch('utils.spiders.smart_money.gmgn_wallet_stats_spider.GmgnWalletStatsSpider.init_sessions')
    async def test_setup(self, mock_init_sessions, spider):
        """测试用例 1.2: 爬虫设置功能"""
        # Mock初始化会话
        mock_init_sessions.return_value = None
        
        # 执行设置
        await spider.setup()
        
        # 验证设备ID和客户端ID已生成
        assert spider.device_id is not None
        assert spider.client_id is not None
        assert spider.client_id.startswith("gmgn-client-")
        
        # 验证初始化方法被调用
        mock_init_sessions.assert_called_once()
    
    def test_validate_response_missing_fields(self, spider):
        """测试用例 3.2: 缺少必需字段的响应"""
        invalid_response = {"code": 0, "msg": "success"}  # 缺少data字段
        
        result = spider._validate_response(invalid_response)
        assert result is False
    
    def test_validate_response_error_code(self, spider):
        """测试用例 3.3: 错误状态码响应"""
        error_response = {
            "code": -1,
            "msg": "wallet not found",
            "data": {}
        }
        
        result = spider._validate_response(error_response)
        assert result is False
    
    def test_validate_response_invalid_format(self, spider):
        """测试用例 3.4: 无效格式响应"""
        # 非字典格式
        result = spider._validate_response("invalid_format")
        assert result is False
        
        # data字段非字典
        invalid_data_response = {
            "code": 0,
            "msg": "success",
            "data": "invalid_data_format"
        }
        result = spider._validate_response(invalid_data_response)
        assert result is False
    
    @patch('models.gmgn_wallet_stats.GmgnWalletStats.create_from_api_data')
    def test_parse_wallet_data_exception(self, mock_create_from_api_data, spider, mock_response_data):
        """测试用例 4.2: 解析数据时异常处理"""
        # Mock模型创建方法抛出异常
        mock_create_from_api_data.side_effect = ValueError("Invalid data")
        
        result = spider._parse_wallet_data("test_wallet_123", mock_response_data, "all")
        
        # 验证返回None
        assert result is None
    
    @patch('utils.spiders.smart_money.gmgn_wallet_stats_spider.GmgnWalletStatsSpider.request_with_retry')
    async def test_get_wallet_stats_success(self, mock_request, spider, mock_response_data):
        """测试用例 5.1: 成功获取钱包统计数据"""
        # 设置必需的属性
        spider.device_id = "test-device-123"
        spider.client_id = "test-client-123"
        
        # Mock响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = mock_response_data
        mock_request.return_value = mock_response
        
        # Mock数据解析
        with patch.object(spider, '_parse_wallet_data') as mock_parse:
            mock_stats = MagicMock(spec=GmgnWalletStats)
            mock_parse.return_value = mock_stats
            
            result = await spider.get_wallet_stats("test_wallet_123", "all")
            
            # 验证结果
            assert result == mock_stats
            
            # 验证请求参数
            mock_request.assert_called_once()
            call_args = mock_request.call_args
            assert call_args[0][0] == "GET"  # method
            assert "test_wallet_123" in call_args[0][1]  # URL
            assert "params" in call_args[1]
            assert call_args[1]["timeout"] == 10
    
    @patch('utils.spiders.smart_money.gmgn_wallet_stats_spider.GmgnWalletStatsSpider.request_with_retry')
    async def test_get_wallet_stats_http_error(self, mock_request, spider):
        """测试用例 5.2: HTTP错误处理"""
        # 设置必需的属性
        spider.device_id = "test-device-123"
        
        # Mock错误响应
        mock_response = MagicMock()
        mock_response.status_code = 404
        mock_response.text = "Not Found"
        mock_request.return_value = mock_response
        
        result = await spider.get_wallet_stats("test_wallet_123")
        
        # 验证返回None
        assert result is None
    
    @patch('utils.spiders.smart_money.gmgn_wallet_stats_spider.GmgnWalletStatsSpider.setup')
    @patch('utils.spiders.smart_money.gmgn_wallet_stats_spider.GmgnWalletStatsSpider.request_with_retry')
    async def test_get_wallet_stats_auto_setup(self, mock_request, mock_setup, spider, mock_response_data):
        """测试用例 5.3: 自动初始化设置"""
        # 未设置device_id
        assert spider.device_id is None
        
        # Mock响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = mock_response_data
        mock_request.return_value = mock_response
        
        # Mock数据解析
        with patch.object(spider, '_parse_wallet_data') as mock_parse:
            mock_stats = MagicMock(spec=GmgnWalletStats)
            mock_parse.return_value = mock_stats
            
            await spider.get_wallet_stats("test_wallet_123")
            
            # 验证setup方法被调用
            mock_setup.assert_called_once()
    
    async def test_get_wallet_stats_empty_address(self, spider):
        """测试用例 5.4: 空钱包地址处理"""
        result = await spider.get_wallet_stats("")
        assert result is None
        
        result = await spider.get_wallet_stats(None)
        assert result is None
    
    @patch('utils.spiders.smart_money.gmgn_wallet_stats_spider.GmgnWalletStatsSpider.get_wallet_stats')
    @patch('asyncio.sleep')
    async def test_get_multiple_periods_stats_success(self, mock_sleep, mock_get_stats, spider):
        """测试用例 6.1: 获取多时间窗口数据成功"""
        # Mock单个获取方法
        mock_stats_all = MagicMock(spec=GmgnWalletStats)
        mock_stats_7d = MagicMock(spec=GmgnWalletStats)
        mock_stats_1d = MagicMock(spec=GmgnWalletStats)
        
        mock_get_stats.side_effect = [mock_stats_all, mock_stats_7d, mock_stats_1d]
        
        result = await spider.get_multiple_periods_stats("test_wallet_123")
        
        # 验证结果
        assert len(result) == 3
        assert result["all"] == mock_stats_all
        assert result["7d"] == mock_stats_7d
        assert result["1d"] == mock_stats_1d
        
        # 验证调用次数
        assert mock_get_stats.call_count == 3
        assert mock_sleep.call_count == 2  # 间隔调用
    
    @patch('utils.spiders.smart_money.gmgn_wallet_stats_spider.GmgnWalletStatsSpider.get_wallet_stats')
    async def test_get_multiple_periods_stats_partial_failure(self, mock_get_stats, spider):
        """测试用例 6.2: 部分时间窗口获取失败"""
        # Mock部分成功，部分失败
        mock_stats_all = MagicMock(spec=GmgnWalletStats)
        mock_get_stats.side_effect = [mock_stats_all, None, Exception("Network error")]
        
        result = await spider.get_multiple_periods_stats("test_wallet_123")
        
        # 验证结果
        assert len(result) == 3
        assert result["all"] == mock_stats_all
        assert result["7d"] is None
        assert result["1d"] is None
    
    @patch('utils.spiders.smart_money.gmgn_wallet_stats_spider.GmgnWalletStatsSpider.get_wallet_stats')
    async def test_get_multiple_periods_stats_custom_periods(self, mock_get_stats, spider):
        """测试用例 6.3: 自定义时间窗口列表"""
        mock_stats = MagicMock(spec=GmgnWalletStats)
        mock_get_stats.return_value = mock_stats
        
        custom_periods = ["all", "1d"]
        result = await spider.get_multiple_periods_stats("test_wallet_123", custom_periods)
        
        # 验证结果
        assert len(result) == 2
        assert "all" in result
        assert "1d" in result
        assert "7d" not in result
        
        # 验证调用参数
        assert mock_get_stats.call_count == 2
        expected_calls = [
            (("test_wallet_123", "all"), {}),
            (("test_wallet_123", "1d"), {})
        ]
        actual_calls = [call for call in mock_get_stats.call_args_list]
        assert len(actual_calls) == len(expected_calls) 