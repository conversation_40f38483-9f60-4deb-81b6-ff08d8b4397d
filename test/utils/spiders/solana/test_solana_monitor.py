# test/utils/spiders/solana/test_solana_monitor.py
import unittest
from unittest.mock import patch, AsyncMock, MagicMock, call
from datetime import datetime, timezone
from decimal import Decimal
import json
import logging
from typing import Optional, Dict, Any, List

# Import the module to test
from utils.spiders.solana.solana_monitor import SolanaMonitor

# Mock imports for dependencies
from solana.rpc.api import Client
from solders.pubkey import Pubkey
from solders.signature import Signature
from solana.rpc.types import TokenAccountOpts

# Disable logging during tests unless specifically needed
logging.disable(logging.CRITICAL)

class TestSolanaMonitor(unittest.IsolatedAsyncioTestCase):
    """SolanaMonitor功能单元测试"""

    def setUp(self):
        """测试前置设置"""
        # Mock Solana RPC Client
        self.mock_client = MagicMock(spec=Client)
        
        # Create SolanaMonitor instance with mocked client
        with patch('utils.spiders.solana.solana_monitor.Client', return_value=self.mock_client):
            self.monitor = SolanaMonitor(rpc_url="https://test-rpc.solana.com")
        
        # Replace the client with our mock
        self.monitor.client = self.mock_client
        
        # Mock logger to avoid log output during tests
        self.monitor.logger = MagicMock()
        
        # Test data constants
        self.test_wallet_address = "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM"
        self.test_token_mint = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"  # USDC
        self.test_sol_mint = "So11111111111111111111111111111111111111112"
        self.test_tx_hash = "5VfYmGC52L1BS4oCNy8VtTGGiGD8KQz9Gm1uGmWkKkqGKqGm1uGmWkKkqGKqGm1uGmWkKkqG"

    def tearDown(self):
        """测试后清理"""
        # Re-enable logging
        logging.disable(logging.NOTSET)

    # === get_token_balance 测试用例 ===

    async def test_get_token_balance_success_with_balance(self):
        """测试成功获取代币余额（有余额）"""
        # Mock token account response
        mock_account = MagicMock()
        mock_account.account.to_json.return_value = json.dumps({
            "data": {
                "parsed": {
                    "info": {
                        "tokenAmount": {
                            "uiAmountString": "123.456789"
                        }
                    }
                }
            }
        })
        
        mock_response = MagicMock()
        mock_response.value = [mock_account]
        self.mock_client.get_token_accounts_by_owner.return_value = mock_response
        
        # Mock Pubkey.from_string
        with patch('utils.spiders.solana.solana_monitor.Pubkey') as mock_pubkey:
            mock_pubkey.from_string.return_value = MagicMock()
            
            # Execute test
            balance = await self.monitor.get_token_balance(
                owner_address=self.test_wallet_address,
                token_mint=self.test_token_mint
            )
            
            # Assertions
            self.assertEqual(balance, 123.456789)
            self.mock_client.get_token_accounts_by_owner.assert_called_once()

    async def test_get_token_balance_success_zero_balance(self):
        """测试成功获取代币余额（余额为0）"""
        # Mock empty token account response
        mock_response = MagicMock()
        mock_response.value = []
        self.mock_client.get_token_accounts_by_owner.return_value = mock_response
        
        # Mock Pubkey.from_string
        with patch('utils.spiders.solana.solana_monitor.Pubkey') as mock_pubkey:
            mock_pubkey.from_string.return_value = MagicMock()
            
            # Execute test
            balance = await self.monitor.get_token_balance(
                owner_address=self.test_wallet_address,
                token_mint=self.test_token_mint
            )
            
            # Assertions
            self.assertEqual(balance, 0.0)

    async def test_get_token_balance_rpc_exception(self):
        """测试RPC调用异常时的处理"""
        # Mock RPC exception
        self.mock_client.get_token_accounts_by_owner.side_effect = Exception("RPC connection failed")
        
        # Mock Pubkey.from_string
        with patch('utils.spiders.solana.solana_monitor.Pubkey') as mock_pubkey:
            mock_pubkey.from_string.return_value = MagicMock()
            
            # Execute test
            balance = await self.monitor.get_token_balance(
                owner_address=self.test_wallet_address,
                token_mint=self.test_token_mint
            )
            
            # Assertions
            self.assertEqual(balance, 0.0)
            self.monitor.logger.error.assert_called()

    async def test_get_token_balance_invalid_account_data(self):
        """测试账户数据格式无效时的处理"""
        # Mock account with invalid data structure
        mock_account = MagicMock()
        mock_account.account.to_json.return_value = json.dumps({
            "data": {
                "invalid_structure": True
            }
        })
        
        mock_response = MagicMock()
        mock_response.value = [mock_account]
        self.mock_client.get_token_accounts_by_owner.return_value = mock_response
        
        # Mock Pubkey.from_string
        with patch('utils.spiders.solana.solana_monitor.Pubkey') as mock_pubkey:
            mock_pubkey.from_string.return_value = MagicMock()
            
            # Execute test
            balance = await self.monitor.get_token_balance(
                owner_address=self.test_wallet_address,
                token_mint=self.test_token_mint
            )
            
            # Assertions
            self.assertEqual(balance, 0.0)

    # === get_transaction_details 测试用例 ===

    async def test_get_transaction_details_success(self):
        """测试成功获取交易详情"""
        # Mock transaction response
        mock_tx_data = {
            "meta": {
                "err": None,
                "preTokenBalances": [],
                "postTokenBalances": [],
                "preBalances": [**********, **********],
                "postBalances": [*********, **********]
            },
            "transaction": {
                "message": {
                    "accountKeys": [self.test_wallet_address, "AnotherAddress123"]
                }
            }
        }
        
        mock_response = MagicMock()
        mock_response.value = MagicMock()
        mock_response.value.to_json.return_value = json.dumps(mock_tx_data)
        self.mock_client.get_transaction.return_value = mock_response
        
        # Mock Signature.from_string
        with patch('utils.spiders.solana.solana_monitor.Signature') as mock_signature:
            mock_signature.from_string.return_value = MagicMock()
            
            # Execute test
            tx_details = await self.monitor.get_transaction_details(self.test_tx_hash)
            
            # Assertions
            self.assertIsNotNone(tx_details)
            self.assertEqual(tx_details["meta"]["err"], None)
            self.mock_client.get_transaction.assert_called_once()
            mock_signature.from_string.assert_called_once_with(self.test_tx_hash)

    async def test_get_transaction_details_not_found(self):
        """测试交易不存在时的处理"""
        # Mock empty response
        mock_response = MagicMock()
        mock_response.value = None
        self.mock_client.get_transaction.return_value = mock_response
        
        # Mock Signature.from_string
        with patch('utils.spiders.solana.solana_monitor.Signature') as mock_signature:
            mock_signature.from_string.return_value = MagicMock()
            
            # Execute test
            tx_details = await self.monitor.get_transaction_details(self.test_tx_hash)
            
            # Assertions
            self.assertIsNone(tx_details)

    async def test_get_transaction_details_rpc_exception(self):
        """测试RPC异常时的处理"""
        # Mock RPC exception
        self.mock_client.get_transaction.side_effect = Exception("RPC error")
        
        # Mock Signature.from_string
        with patch('utils.spiders.solana.solana_monitor.Signature') as mock_signature:
            mock_signature.from_string.return_value = MagicMock()
            
            # Execute test
            tx_details = await self.monitor.get_transaction_details(self.test_tx_hash)
            
            # Assertions
            self.assertIsNone(tx_details)
            self.monitor.logger.error.assert_called()

    # === get_confirmed_token_output_from_tx 测试用例 ===

    async def test_get_confirmed_token_output_from_tx_spl_token_success(self):
        """测试从交易中成功获取SPL代币输出数量"""
        # Mock transaction details with SPL token transfer
        mock_tx_data = {
            "meta": {
                "err": None,
                "preTokenBalances": [
                    {
                        "accountIndex": 1,
                        "mint": self.test_token_mint,
                        "owner": self.test_wallet_address,
                        "uiTokenAmount": {
                            "uiAmountString": "100.0"
                        }
                    }
                ],
                "postTokenBalances": [
                    {
                        "accountIndex": 1,
                        "mint": self.test_token_mint,
                        "owner": self.test_wallet_address,
                        "uiTokenAmount": {
                            "uiAmountString": "150.0"
                        }
                    }
                ]
            }
        }
        
        # Mock get_transaction_details
        with patch.object(self.monitor, 'get_transaction_details', return_value=mock_tx_data):
            # Execute test
            output_amount = await self.monitor.get_confirmed_token_output_from_tx(
                tx_hash=self.test_tx_hash,
                expected_output_token_mint=self.test_token_mint,
                wallet_address=self.test_wallet_address
            )
        
        # Assertions
        self.assertEqual(output_amount, 50.0)  # 150.0 - 100.0 = 50.0

    async def test_get_confirmed_token_output_from_tx_sol_success(self):
        """测试从交易中成功获取SOL输出数量"""
        # Mock transaction details with SOL transfer
        mock_tx_data = {
            "meta": {
                "err": None,
                "preBalances": [**********, **********],  # 1 SOL, 2 SOL
                "postBalances": [**********, **********],  # 1.5 SOL, 1.5 SOL
                "preTokenBalances": [],
                "postTokenBalances": []
            },
            "transaction": {
                "message": {
                    "accountKeys": [self.test_wallet_address, "AnotherAddress123"]
                }
            }
        }
        
        # Mock get_transaction_details
        with patch.object(self.monitor, 'get_transaction_details', return_value=mock_tx_data):
            # Execute test
            output_amount = await self.monitor.get_confirmed_token_output_from_tx(
                tx_hash=self.test_tx_hash,
                expected_output_token_mint="SOL",
                wallet_address=self.test_wallet_address
            )
        
        # Assertions
        self.assertEqual(output_amount, 0.5)  # (1.5 - 1.0) SOL = 0.5 SOL

    async def test_get_confirmed_token_output_from_tx_failed_transaction(self):
        """测试处理失败的交易"""
        # Mock failed transaction
        mock_tx_data = {
            "meta": {
                "err": {"InstructionError": [0, "Custom error"]},
                "preTokenBalances": [],
                "postTokenBalances": []
            }
        }
        
        # Mock get_transaction_details
        with patch.object(self.monitor, 'get_transaction_details', return_value=mock_tx_data):
            # Execute test
            output_amount = await self.monitor.get_confirmed_token_output_from_tx(
                tx_hash=self.test_tx_hash,
                expected_output_token_mint=self.test_token_mint,
                wallet_address=self.test_wallet_address
            )
        
        # Assertions
        self.assertIsNone(output_amount)

    async def test_get_confirmed_token_output_from_tx_no_token_change(self):
        """测试没有代币变化的交易"""
        # Mock transaction with no token balance changes
        mock_tx_data = {
            "meta": {
                "err": None,
                "preTokenBalances": [
                    {
                        "accountIndex": 1,
                        "mint": self.test_token_mint,
                        "owner": self.test_wallet_address,
                        "uiTokenAmount": {
                            "uiAmountString": "100.0"
                        }
                    }
                ],
                "postTokenBalances": [
                    {
                        "accountIndex": 1,
                        "mint": self.test_token_mint,
                        "owner": self.test_wallet_address,
                        "uiTokenAmount": {
                            "uiAmountString": "100.0"  # No change
                        }
                    }
                ]
            }
        }
        
        # Mock get_transaction_details
        with patch.object(self.monitor, 'get_transaction_details', return_value=mock_tx_data):
            # Execute test
            output_amount = await self.monitor.get_confirmed_token_output_from_tx(
                tx_hash=self.test_tx_hash,
                expected_output_token_mint=self.test_token_mint,
                wallet_address=self.test_wallet_address
            )
        
        # Assertions
        self.assertIsNone(output_amount)

    async def test_get_confirmed_token_output_from_tx_transaction_not_found(self):
        """测试交易不存在时的处理"""
        # Mock get_transaction_details returning None
        with patch.object(self.monitor, 'get_transaction_details', return_value=None):
            # Execute test
            output_amount = await self.monitor.get_confirmed_token_output_from_tx(
                tx_hash=self.test_tx_hash,
                expected_output_token_mint=self.test_token_mint,
                wallet_address=self.test_wallet_address
            )
        
        # Assertions
        self.assertIsNone(output_amount)

    async def test_get_confirmed_token_output_from_tx_exception(self):
        """测试处理异常情况"""
        # Mock get_transaction_details raising exception
        with patch.object(self.monitor, 'get_transaction_details', side_effect=Exception("Network error")):
            # Execute test
            output_amount = await self.monitor.get_confirmed_token_output_from_tx(
                tx_hash=self.test_tx_hash,
                expected_output_token_mint=self.test_token_mint,
                wallet_address=self.test_wallet_address
            )
        
        # Assertions
        self.assertIsNone(output_amount)
        self.monitor.logger.error.assert_called()

    # === analyze_transaction 测试用例 ===

    async def test_analyze_transaction_token_transfer(self):
        """测试分析代币转账交易"""
        # Mock transaction data with token transfers
        tx_data = {
            "meta": {
                "preTokenBalances": [
                    {
                        "accountIndex": 1,
                        "mint": self.test_token_mint,
                        "owner": self.test_wallet_address,
                        "uiTokenAmount": {
                            "uiAmountString": "100.0"
                        }
                    }
                ],
                "postTokenBalances": [
                    {
                        "accountIndex": 1,
                        "mint": self.test_token_mint,
                        "owner": self.test_wallet_address,
                        "uiTokenAmount": {
                            "uiAmountString": "150.0"
                        }
                    }
                ],
                "preBalances": [**********],
                "postBalances": [**********]
            },
            "transaction": {
                "message": {
                    "accountKeys": [self.test_wallet_address]
                }
            }
        }
        
        # Mock get_token_info
        mock_token_info = {
            "name": "USD Coin",
            "symbol": "USDC"
        }
        
        with patch.object(self.monitor, 'get_token_info', return_value=mock_token_info):
            # Execute test
            analysis = await self.monitor.analyze_transaction(tx_data)
        
        # Assertions
        self.assertEqual(analysis["type"], "token_transfer")
        self.assertEqual(len(analysis["token_transfers"]), 1)
        
        transfer = analysis["token_transfers"][0]
        self.assertEqual(transfer["token_mint"], self.test_token_mint)
        self.assertEqual(transfer["token_name"], "USD Coin")
        self.assertEqual(transfer["token_symbol"], "USDC")
        self.assertEqual(transfer["pre_amount"], 100.0)
        self.assertEqual(transfer["post_amount"], 150.0)
        self.assertEqual(transfer["change"], 50.0)
        self.assertEqual(transfer["owner"], self.test_wallet_address)

    async def test_analyze_transaction_sol_transfer(self):
        """测试分析SOL转账交易"""
        # Mock transaction data with SOL transfers only
        tx_data = {
            "meta": {
                "preTokenBalances": [],
                "postTokenBalances": [],
                "preBalances": [**********, **********],  # 1 SOL, 2 SOL
                "postBalances": [*********, **********]   # 0.5 SOL, 2.5 SOL
            },
            "transaction": {
                "message": {
                    "accountKeys": [self.test_wallet_address, "AnotherAddress123"]
                }
            }
        }
        
        # Execute test
        analysis = await self.monitor.analyze_transaction(tx_data)
        
        # Assertions
        self.assertEqual(analysis["type"], "sol_transfer")
        self.assertEqual(len(analysis["sol_transfers"]), 2)
        
        # Check first transfer (wallet lost 0.5 SOL)
        transfer1 = analysis["sol_transfers"][0]
        self.assertEqual(transfer1["address"], self.test_wallet_address)
        self.assertEqual(transfer1["pre_balance"], 1.0)
        self.assertEqual(transfer1["post_balance"], 0.5)
        self.assertEqual(transfer1["change"], -0.5)
        
        # Check second transfer (other address gained 0.5 SOL)
        transfer2 = analysis["sol_transfers"][1]
        self.assertEqual(transfer2["address"], "AnotherAddress123")
        self.assertEqual(transfer2["pre_balance"], 2.0)
        self.assertEqual(transfer2["post_balance"], 2.5)
        self.assertEqual(transfer2["change"], 0.5)

    async def test_analyze_transaction_no_changes(self):
        """测试分析没有余额变化的交易"""
        # Mock transaction data with no balance changes
        tx_data = {
            "meta": {
                "preTokenBalances": [],
                "postTokenBalances": [],
                "preBalances": [**********],
                "postBalances": [**********]  # No change
            },
            "transaction": {
                "message": {
                    "accountKeys": [self.test_wallet_address]
                }
            }
        }
        
        # Execute test
        analysis = await self.monitor.analyze_transaction(tx_data)
        
        # Assertions
        self.assertEqual(analysis["type"], "unknown")
        self.assertEqual(len(analysis["token_transfers"]), 0)
        self.assertEqual(len(analysis["sol_transfers"]), 0)

    # === Integration tests for Bug Fix scenarios ===

    async def test_bug_fix_scenario_balance_check_integration(self):
        """测试Bug修复场景：余额查询集成测试"""
        # This test simulates the bug fix scenario where we need to check
        # actual wallet balance before attempting to sell
        
        # Mock successful balance query
        mock_account = MagicMock()
        mock_account.account.to_json.return_value = json.dumps({
            "data": {
                "parsed": {
                    "info": {
                        "tokenAmount": {
                            "uiAmountString": "75.5"  # Current balance
                        }
                    }
                }
            }
        })
        
        mock_response = MagicMock()
        mock_response.value = [mock_account]
        self.mock_client.get_token_accounts_by_owner.return_value = mock_response
        
        # Mock transaction details for confirmed amount
        mock_tx_data = {
            "meta": {
                "err": None,
                "preTokenBalances": [
                    {
                        "accountIndex": 1,
                        "mint": self.test_token_mint,
                        "owner": self.test_wallet_address,
                        "uiTokenAmount": {
                            "uiAmountString": "0.0"
                        }
                    }
                ],
                "postTokenBalances": [
                    {
                        "accountIndex": 1,
                        "mint": self.test_token_mint,
                        "owner": self.test_wallet_address,
                        "uiTokenAmount": {
                            "uiAmountString": "100.0"  # Original buy amount
                        }
                    }
                ]
            }
        }
        
        with patch.object(self.monitor, 'get_transaction_details', return_value=mock_tx_data):
            with patch('utils.spiders.solana.solana_monitor.Pubkey') as mock_pubkey:
                mock_pubkey.from_string.return_value = MagicMock()
                
                # Execute both methods that the bug fix depends on
                current_balance = await self.monitor.get_token_balance(
                    owner_address=self.test_wallet_address,
                    token_mint=self.test_token_mint
                )
                
                confirmed_amount = await self.monitor.get_confirmed_token_output_from_tx(
                    tx_hash=self.test_tx_hash,
                    expected_output_token_mint=self.test_token_mint,
                    wallet_address=self.test_wallet_address
                )
        
        # Assertions for bug fix logic
        self.assertEqual(current_balance, 75.5)  # Current wallet balance
        self.assertEqual(confirmed_amount, 100.0)  # Original buy amount
        
        # The bug fix should use the minimum of these two values
        amount_to_sell = min(current_balance, confirmed_amount)
        self.assertEqual(amount_to_sell, 75.5)  # Should use current balance (conservative)

    async def test_bug_fix_scenario_insufficient_balance(self):
        """测试Bug修复场景：余额不足的情况"""
        # Mock zero balance
        mock_response = MagicMock()
        mock_response.value = []  # No token accounts = zero balance
        self.mock_client.get_token_accounts_by_owner.return_value = mock_response
        
        # Mock Pubkey.from_string
        with patch('utils.spiders.solana.solana_monitor.Pubkey') as mock_pubkey:
            mock_pubkey.from_string.return_value = MagicMock()
            
            # Execute test
            balance = await self.monitor.get_token_balance(
                owner_address=self.test_wallet_address,
                token_mint=self.test_token_mint
            )
            
            # Assertions
            self.assertEqual(balance, 0.0)
            
            # This should trigger the bug fix logic to skip the trade
            # (tested in the sell_signal_handler tests)

    async def test_bug_fix_scenario_rpc_failure_handling(self):
        """测试Bug修复场景：RPC失败时的处理"""
        # Mock RPC failure
        self.mock_client.get_token_accounts_by_owner.side_effect = Exception("RPC node unavailable")
        
        # Mock Pubkey.from_string
        with patch('utils.spiders.solana.solana_monitor.Pubkey') as mock_pubkey:
            mock_pubkey.from_string.return_value = MagicMock()
            
            # Execute test
            balance = await self.monitor.get_token_balance(
                owner_address=self.test_wallet_address,
                token_mint=self.test_token_mint
            )
            
            # Assertions
            self.assertEqual(balance, 0.0)  # Should return 0.0 on error
            self.monitor.logger.error.assert_called()
            
            # This should trigger the bug fix logic to skip the trade and send error notification
            # (tested in the sell_signal_handler tests)

if __name__ == '__main__':
    unittest.main() 