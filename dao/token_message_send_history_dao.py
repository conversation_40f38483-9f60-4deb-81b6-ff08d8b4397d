from typing import List, Optional
from datetime import datetime, timedelta

from models.token_message_send_history import TokenMessageSendHistory
from dao.base_dao import BaseDAO


class TokenMessageSendHistoryDAO(BaseDAO[TokenMessageSendHistory]):
    """Token消息发送历史数据访问对象"""
    
    def __init__(self):
        super().__init__(TokenMessageSendHistory)
        
    async def insert_one(self, data: dict):
        """插入一条数据"""
        if isinstance(data, TokenMessageSendHistory):
            data = data.model_dump()
        return await self.collection.insert_one(data)
    
    async def find_by_token_address(self, token_address: str) -> List[TokenMessageSendHistory]:
        """根据代币地址查找数据"""
        return await self.collection.find({"token_address": token_address}).to_list()
    
    async def find_by_chat_id(self, chat_id: str) -> List[TokenMessageSendHistory]:
        """根据用户ID查找数据"""
        return await self.collection.find({"chat_id": chat_id}).to_list()
    
    async def exists(self, token_address: str, chat_id: str) -> bool:
        """判断是否存在"""
        return await self.collection.find_one({"token_address": token_address, "chat_id": chat_id}) is not None
    
    async def recent_history(self, token_address: str, chat_id: str, minutes: int = 10, strategy_name: Optional[str] = None) -> List[TokenMessageSendHistory]:
        """
        获取某个代币某个用户最近x分钟的已发送成功的消息发送历史
        
        参数:
            token_address: 代币地址
            chat_id: 用户ID
            minutes: 查找的时间范围（分钟）
            strategy_name: 策略名称，如果提供则只查找该策略的记录
        """
        start_time = datetime.utcnow() - timedelta(minutes=minutes)
        query = {
            "token_address": token_address, 
            "chat_id": chat_id, 
            "status": "sent", # 只检查已成功发送的记录
            "created_at": {"$gte": start_time}
        }
        
        # 如果提供了策略名称，则添加到查询条件中
        if strategy_name:
            query["strategy_name"] = strategy_name
            
        return await self.collection.find(query).to_list()
