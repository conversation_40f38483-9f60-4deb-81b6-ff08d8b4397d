# 交易记录验证更新器测试用例

**版本**: 2.0.0  
**创建日期**: 2024-01-15  
**最后更新**: 2024-01-15  
**负责人**: AI Assistant

## 1. 测试概述

### 1.1 测试目标
本文档定义了交易记录验证更新器功能的完整测试用例，确保系统能够：
- 每10秒扫描需要验证的交易记录
- 准确解析链上交易数据
- 正确更新验证字段（verified_amount、verification_status等）
- 保持原有字段不变
- 处理各种异常情况
- 满足高频扫描的性能要求

### 1.2 测试范围
- 工作流调度功能（10秒间隔）
- 交易记录验证查询逻辑
- 链上数据解析功能
- 批量验证更新操作
- 验证状态管理
- 错误处理机制
- 性能和稳定性

### 1.3 测试环境
- **开发环境**: 本地开发测试（30秒间隔）
- **集成环境**: 完整功能集成测试
- **性能环境**: 高频扫描性能测试
- **生产环境**: 生产验证测试（10秒间隔）

## 2. 单元测试用例

### 2.1 验证工作流处理器测试

#### TC-001: 验证工作流正常执行
- **测试目的**: 验证验证工作流能够正常启动和执行
- **前置条件**: 工作流配置正确，10秒间隔设置
- **测试步骤**:
  1. 启动验证工作流
  2. 等待一个执行周期（10秒）
  3. 检查执行日志
- **预期结果**: 工作流正常执行，无错误日志
- **优先级**: P0

#### TC-002: 验证工作流配置验证
- **测试目的**: 验证工作流配置参数的有效性
- **前置条件**: 提供各种配置参数
- **测试步骤**:
  1. 使用有效配置启动工作流（10秒间隔，50条批处理）
  2. 使用无效配置启动工作流
  3. 检查配置验证结果
- **预期结果**: 有效配置通过，无效配置被拒绝
- **优先级**: P1

### 2.2 TradeRecordVerificationUpdater类测试

#### TC-003: 初始化测试
**测试目的**: 验证验证更新器类正确初始化

**前置条件**: 无

**测试步骤**:
1. 创建TradeRecordVerificationUpdater实例
2. 验证默认参数设置
3. 验证自定义参数设置

**预期结果**:
- 实例创建成功
- 默认参数: scan_hours=24, batch_size=50
- 自定义参数正确设置

**测试数据**:
```python
# 默认初始化
updater = TradeRecordVerificationUpdater()
assert updater.scan_hours == 24
assert updater.batch_size == 50

# 自定义初始化
updater = TradeRecordVerificationUpdater(scan_hours=12, batch_size=30)
assert updater.scan_hours == 12
assert updater.batch_size == 30
```

#### TC-002: 获取需要验证的记录测试
**测试目的**: 验证正确查询需要验证的交易记录

**前置条件**: 
- 数据库中存在测试交易记录
- 包含不同验证状态和时间的记录

**测试步骤**:
1. 准备测试数据（不同状态、时间、验证状态的记录）
2. 调用_get_records_to_verify方法
3. 验证返回结果

**预期结果**:
- 只返回最近24小时内的记录
- 只返回状态为'completed'的记录
- 优先返回verification_status为NULL或'PENDING'的记录
- 记录按优先级排序

**测试数据**:
```python
# 创建测试记录
records = [
    # 应该被选中的记录
    TradeRecord(id=1, created_at=datetime.now() - timedelta(hours=1), 
                status='completed', verification_status=None),      # 老记录
    TradeRecord(id=2, created_at=datetime.now() - timedelta(hours=2), 
                status='completed', verification_status='PENDING'), # 新记录
    
    # 不应该被选中的记录
    TradeRecord(id=3, created_at=datetime.now() - timedelta(hours=25), 
                status='completed', verification_status='PENDING'),  # 超时
    TradeRecord(id=4, created_at=datetime.now() - timedelta(hours=1), 
                status='failed', verification_status='PENDING'),     # 状态错误
]
```

#### TC-003: 交易验证数据解析测试
**测试目的**: 验证从Solana交易中正确解析验证数据

**前置条件**: 
- 有效的交易签名
- Solana网络连接正常

**测试步骤**:
1. 准备有效的交易记录
2. 调用_get_verification_data_from_tx方法
3. 验证返回的验证数据

**预期结果**:
- 成功解析有效交易的验证数据
- 无效交易返回None
- 网络错误时正确处理异常

**测试数据**:
```python
# 有效交易记录
valid_record = TradeRecord(
    transaction_signature="valid_signature_123",
    wallet_address="wallet_address_123",
    token_out_mint="token_mint_123"
)

# 无效交易记录
invalid_record = TradeRecord(
    transaction_signature="invalid_signature",
    wallet_address="wallet_address_123",
    token_out_mint="token_mint_123"
)
```

#### TC-004: 批量验证处理测试
**测试目的**: 验证批量处理交易记录验证的正确性

**前置条件**: 
- 准备多条测试记录
- 数据库连接正常

**测试步骤**:
1. 准备包含成功和失败案例的记录批次
2. 调用_process_verification_batch方法
3. 验证处理结果和数据库更新

**预期结果**:
- 成功记录的验证字段被正确更新
- 失败记录不影响其他记录
- 返回正确的统计信息
- 数据库事务正确处理

### 2.2 Handler测试

#### TC-005: Handler执行测试
**测试目的**: 验证工作流Handler正确执行

**前置条件**: 
- 工作流环境配置正确
- 数据库连接正常

**测试步骤**:
1. 调用perform_trade_record_verification_update_task
2. 验证执行结果
3. 检查日志输出

**预期结果**:
- 任务成功执行
- 返回正确的状态信息
- 日志记录完整

### 2.3 DAO扩展测试

#### TC-006: 查询方法测试
**测试目的**: 验证新增的查询方法正确性

**前置条件**: 
- 数据库中有测试数据

**测试步骤**:
1. 调用get_records_for_verification_update方法
2. 验证查询条件和排序
3. 验证返回结果

**预期结果**:
- 查询条件正确应用
- 结果按优先级排序
- 数量限制生效

## 3. 集成测试用例

### 3.1 工作流集成测试

#### TC-101: 端到端验证工作流测试
**测试目的**: 验证完整的验证工作流执行流程

**前置条件**: 
- 验证工作流配置文件正确（10秒间隔）
- 所有依赖服务运行正常
- 存在待验证的交易记录

**测试步骤**:
1. 启动验证工作流调度器
2. 等待一个执行周期（10秒）
3. 验证执行结果
4. 检查数据库验证字段更新
5. 确认原有字段未被修改

**预期结果**:
- 工作流按10秒间隔执行
- 交易记录验证字段被正确更新
- 原有字段保持不变
- 执行统计信息准确

#### TC-102: 配置参数测试
**测试目的**: 验证不同配置参数的影响

**测试步骤**:
1. 测试不同的interval设置
2. 测试不同的batch_size设置
3. 测试不同的scan_hours设置

**预期结果**:
- 配置参数正确生效
- 性能表现符合预期

### 3.2 数据库集成测试

#### TC-103: 数据库事务测试
**测试目的**: 验证数据库事务的正确性

**测试步骤**:
1. 模拟批量更新中的异常
2. 验证事务回滚
3. 检查数据一致性

**预期结果**:
- 异常时事务正确回滚
- 数据保持一致性
- 不会出现部分更新

#### TC-104: 并发访问测试
**测试目的**: 验证并发访问时的数据安全

**测试步骤**:
1. 同时启动多个更新任务
2. 验证数据库锁机制
3. 检查最终数据状态

**预期结果**:
- 不会出现数据竞争
- 记录不会被重复更新
- 性能在可接受范围内

### 3.3 外部服务集成测试

#### TC-105: Solana网络集成测试
**测试目的**: 验证与Solana网络的集成

**测试步骤**:
1. 测试正常网络条件下的交易解析
2. 模拟网络异常情况
3. 验证重试机制

**预期结果**:
- 正常情况下解析成功
- 网络异常时正确处理
- 重试机制有效

## 4. 性能测试用例

### 4.1 高频扫描性能测试

#### TC-201: 10秒间隔批量处理测试
**测试目的**: 验证系统在10秒间隔下的处理能力

**测试数据**: 50条交易记录（单批次大小）

**测试步骤**:
1. 准备50条测试记录
2. 执行验证更新任务
3. 监控执行时间和资源使用
4. 重复多个周期测试稳定性

**性能指标**:
- 单次处理时间 < 8秒
- 内存使用 < 200MB
- CPU使用率 < 60%
- 连续执行稳定无内存泄漏

#### TC-202: 并发处理测试
**测试目的**: 验证并发处理能力

**测试步骤**:
1. 同时启动多个处理任务
2. 监控系统资源使用
3. 验证处理结果正确性

**性能指标**:
- 支持至少3个并发任务
- 总体处理时间不超过单任务的150%
- 错误率 < 1%

### 4.2 压力测试

#### TC-203: 极限数据量测试
**测试目的**: 验证系统在极限条件下的表现

**测试数据**: 10000条交易记录

**测试步骤**:
1. 准备大量测试数据
2. 执行更新任务
3. 监控系统稳定性

**预期结果**:
- 系统不崩溃
- 内存使用稳定
- 处理完成率 > 95%

## 5. 异常测试用例

### 5.1 网络异常测试

#### TC-301: Solana网络不可用测试
**测试目的**: 验证Solana网络不可用时的处理

**测试步骤**:
1. 断开Solana网络连接
2. 执行更新任务
3. 验证错误处理

**预期结果**:
- 任务不会崩溃
- 记录详细错误日志
- 支持后续重试

#### TC-302: 网络超时测试
**测试目的**: 验证网络超时的处理

**测试步骤**:
1. 模拟网络延迟
2. 执行更新任务
3. 验证超时处理

**预期结果**:
- 超时后正确终止请求
- 不影响其他记录处理
- 记录超时错误

### 5.2 数据库异常测试

#### TC-303: 数据库连接失败测试
**测试目的**: 验证数据库连接失败的处理

**测试步骤**:
1. 断开数据库连接
2. 执行更新任务
3. 验证错误处理

**预期结果**:
- 任务优雅失败
- 记录连接错误
- 支持自动重连

#### TC-304: 数据库事务冲突测试
**测试目的**: 验证数据库事务冲突的处理

**测试步骤**:
1. 模拟数据库锁冲突
2. 执行更新任务
3. 验证冲突处理

**预期结果**:
- 正确处理锁冲突
- 实施重试机制
- 最终数据一致

### 5.3 数据异常测试

#### TC-305: 无效交易签名测试
**测试目的**: 验证无效交易签名的处理

**测试数据**: 包含无效签名的交易记录

**测试步骤**:
1. 准备无效签名的记录
2. 执行更新任务
3. 验证处理结果

**预期结果**:
- 跳过无效记录
- 不影响其他记录
- 记录处理失败原因

#### TC-306: 数据格式错误测试
**测试目的**: 验证数据格式错误的处理

**测试步骤**:
1. 准备格式错误的数据
2. 执行更新任务
3. 验证错误处理

**预期结果**:
- 正确识别格式错误
- 记录详细错误信息
- 继续处理其他记录

## 6. 安全测试用例

### 6.1 权限测试

#### TC-401: 数据库权限测试
**测试目的**: 验证数据库访问权限控制

**测试步骤**:
1. 使用受限权限账户
2. 执行更新任务
3. 验证权限控制

**预期结果**:
- 权限不足时正确失败
- 记录权限错误
- 不会泄露敏感信息

### 6.2 数据安全测试

#### TC-402: 敏感数据保护测试
**测试目的**: 验证敏感数据不会泄露

**测试步骤**:
1. 检查日志输出
2. 验证错误信息
3. 检查临时文件

**预期结果**:
- 日志不包含敏感信息
- 错误信息不泄露内部细节
- 不产生包含敏感数据的临时文件

## 7. 用户验收测试用例

### 7.1 功能验收

#### TC-501: 基本功能验收
**测试目的**: 验证基本功能满足需求

**验收标准**:
- [ ] 工作流能够正常启动和运行
- [ ] 能够正确识别需要更新的交易记录
- [ ] 能够准确解析链上交易数据
- [ ] 能够正确更新数据库中的金额字段

#### TC-502: 性能验收
**测试目的**: 验证性能满足需求

**验收标准**:
- [ ] 处理100条记录的时间不超过30秒
- [ ] 内存使用稳定在100MB以内
- [ ] 错误率低于1%

#### TC-503: 监控验收
**测试目的**: 验证监控功能满足需求

**验收标准**:
- [ ] 提供完整的运行日志
- [ ] 统计信息准确
- [ ] 异常告警及时

## 8. 测试执行计划

### 8.1 测试阶段

#### 阶段1: 单元测试 (2天)
- 执行TC-001 到 TC-006
- 修复发现的问题
- 代码覆盖率 > 90%

#### 阶段2: 集成测试 (3天)
- 执行TC-101 到 TC-105
- 验证组件间集成
- 修复集成问题

#### 阶段3: 性能测试 (2天)
- 执行TC-201 到 TC-203
- 性能调优
- 验证性能指标

#### 阶段4: 异常测试 (2天)
- 执行TC-301 到 TC-306
- 完善错误处理
- 验证系统稳定性

#### 阶段5: 安全测试 (1天)
- 执行TC-401 到 TC-402
- 安全加固
- 验证安全措施

#### 阶段6: 用户验收测试 (2天)
- 执行TC-501 到 TC-503
- 用户验收
- 最终确认

### 8.2 测试环境准备

#### 开发环境
- 本地数据库实例
- 模拟Solana网络
- 测试数据准备

#### 测试环境
- 独立数据库实例
- Solana测试网络
- 生产级配置

#### 生产环境
- 生产数据库
- Solana主网
- 监控和告警配置

### 8.3 测试数据管理

#### 测试数据准备
- 创建各种状态的交易记录
- 准备有效和无效的交易签名
- 设置不同时间范围的数据

#### 测试数据清理
- 每次测试后清理临时数据
- 保留测试结果和日志
- 定期备份测试数据

## 9. 测试报告模板

### 9.1 测试执行报告
```
测试用例ID: TC-XXX
测试名称: [测试名称]
执行时间: [执行时间]
执行结果: [通过/失败]
实际结果: [实际结果描述]
问题描述: [如果失败，描述问题]
修复建议: [修复建议]
```

### 9.2 测试总结报告
```
测试阶段: [阶段名称]
测试用例总数: [总数]
通过用例数: [通过数]
失败用例数: [失败数]
通过率: [通过率]
主要问题: [问题列表]
风险评估: [风险评估]
发布建议: [是否建议发布]
```