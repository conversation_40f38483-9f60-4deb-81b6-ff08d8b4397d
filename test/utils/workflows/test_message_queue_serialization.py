#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
消息队列序列化测试用例

测试覆盖：
1. PydanticObjectId序列化问题
2. JSON序列化器正确性
3. 消息传递完整性
"""

import unittest
import json
from datetime import datetime
from decimal import Decimal
from unittest.mock import patch, MagicMock

from utils.workflows.message_queue.message_queue import KafkaQueue, RedisStreamQueue


class TestMessageQueueSerialization(unittest.TestCase):
    """消息队列序列化测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.kafka_queue = KafkaQueue("test_topic")
        self.redis_queue = RedisStreamQueue("test_stream")
    
    def test_pydantic_objectid_serialization(self):
        """测试PydanticObjectId序列化"""
        # 模拟PydanticObjectId
        class MockPydanticObjectId:
            def __init__(self, value):
                self.value = value
            
            def __str__(self):
                return self.value
        
        # 测试数据
        test_objectid = MockPydanticObjectId("507f1f77bcf86cd799439011")
        
        # 直接测试序列化器方法
        # 模拟导入PydanticObjectId成功
        with patch('builtins.__import__') as mock_import:
            # 模拟成功导入beanie模块
            mock_beanie = MagicMock()
            mock_beanie.PydanticObjectId = MockPydanticObjectId
            
            def import_side_effect(name, *args, **kwargs):
                if name == 'beanie':
                    return mock_beanie
                return __import__(name, *args, **kwargs)
            
            mock_import.side_effect = import_side_effect
            
            # 测试Kafka序列化器
            result = self.kafka_queue._json_serializer(test_objectid)
            self.assertEqual(result, "507f1f77bcf86cd799439011")
            
            # 测试Redis序列化器
            result = self.redis_queue._json_serializer(test_objectid)
            self.assertEqual(result, "507f1f77bcf86cd799439011")
    
    def test_datetime_serialization(self):
        """测试datetime序列化"""
        test_datetime = datetime(2024, 1, 1, 12, 0, 0)
        
        # 测试Kafka序列化器
        result = self.kafka_queue._json_serializer(test_datetime)
        self.assertEqual(result, "2024-01-01T12:00:00")
        
        # 测试Redis序列化器
        result = self.redis_queue._json_serializer(test_datetime)
        self.assertEqual(result, "2024-01-01T12:00:00")
    
    def test_decimal_serialization(self):
        """测试Decimal序列化"""
        test_decimal = Decimal("123.456")
        
        # 由于当前序列化器没有处理Decimal，应该抛出TypeError
        with self.assertRaises(TypeError):
            self.kafka_queue._json_serializer(test_decimal)
        
        with self.assertRaises(TypeError):
            self.redis_queue._json_serializer(test_decimal)
    
    def test_complex_message_serialization(self):
        """测试复杂消息序列化"""
        # 模拟PydanticObjectId
        class MockPydanticObjectId:
            def __init__(self, value):
                self.value = value
            
            def __str__(self):
                return self.value
        
        # 复杂消息数据
        complex_message = {
            'id': MockPydanticObjectId("507f1f77bcf86cd799439011"),
            'signal_id': MockPydanticObjectId("507f1f77bcf86cd799439012"),
            'timestamp': datetime(2024, 1, 1, 12, 0, 0),
            'nested': {
                'object_id': MockPydanticObjectId("507f1f77bcf86cd799439013"),
                'value': 42
            }
        }
        
        # 测试序列化
        with patch('builtins.__import__') as mock_import:
            # 模拟成功导入beanie模块
            mock_beanie = MagicMock()
            mock_beanie.PydanticObjectId = MockPydanticObjectId
            
            def import_side_effect(name, *args, **kwargs):
                if name == 'beanie':
                    return mock_beanie
                return __import__(name, *args, **kwargs)
            
            mock_import.side_effect = import_side_effect
            
            serialized = json.dumps(complex_message, default=self.kafka_queue._json_serializer)
            
            # 验证序列化结果
            deserialized = json.loads(serialized)
            
            self.assertEqual(deserialized['id'], "507f1f77bcf86cd799439011")
            self.assertEqual(deserialized['signal_id'], "507f1f77bcf86cd799439012")
            self.assertEqual(deserialized['timestamp'], "2024-01-01T12:00:00")
            self.assertEqual(deserialized['nested']['object_id'], "507f1f77bcf86cd799439013")
    
    def test_empty_objectid_handling(self):
        """测试空ObjectId处理"""
        # 测试None值 - 应该抛出TypeError因为序列化器不处理None
        with self.assertRaises(TypeError):
            self.kafka_queue._json_serializer(None)
        
        # 测试空字符串 - 应该抛出TypeError因为序列化器不处理字符串
        with self.assertRaises(TypeError):
            self.kafka_queue._json_serializer("")
    
    def test_redis_serialization_consistency(self):
        """测试Redis序列化一致性"""
        # 模拟PydanticObjectId
        class MockPydanticObjectId:
            def __init__(self, value):
                self.value = value
            
            def __str__(self):
                return self.value
        
        test_data = {
            'id': MockPydanticObjectId("507f1f77bcf86cd799439011"),
            'timestamp': datetime(2024, 1, 1, 12, 0, 0)
        }
        
        # 测试序列化器一致性
        with patch('builtins.__import__') as mock_import:
            # 模拟成功导入beanie模块
            mock_beanie = MagicMock()
            mock_beanie.PydanticObjectId = MockPydanticObjectId
            
            def import_side_effect(name, *args, **kwargs):
                if name == 'beanie':
                    return mock_beanie
                return __import__(name, *args, **kwargs)
            
            mock_import.side_effect = import_side_effect
            
            kafka_result = json.dumps(test_data, default=self.kafka_queue._json_serializer)
            redis_result = json.dumps(test_data, default=self.redis_queue._json_serializer)
            
            # 两个序列化器应该产生相同的结果
            self.assertEqual(kafka_result, redis_result)
    
    def test_serialization_error_handling(self):
        """测试序列化错误处理"""
        # 创建一个不可序列化的对象
        class UnserializableObject:
            pass
        
        unserializable = UnserializableObject()
        
        # 测试错误处理 - 序列化器会尝试使用__dict__属性
        # 对于没有dict方法和__dict__属性的对象，会抛出TypeError
        try:
            result = self.kafka_queue._json_serializer(unserializable)
            # 如果成功序列化，应该返回对象的__dict__
            self.assertIsInstance(result, dict)
        except TypeError:
            # 如果抛出TypeError，这也是可接受的
            pass
        
        try:
            result = self.redis_queue._json_serializer(unserializable)
            # 如果成功序列化，应该返回对象的__dict__
            self.assertIsInstance(result, dict)
        except TypeError:
            # 如果抛出TypeError，这也是可接受的
            pass
    
    def test_object_with_dict_method(self):
        """测试具有dict方法的对象序列化"""
        class ObjectWithDict:
            def dict(self):
                return {'key': 'value', 'number': 42}
        
        obj = ObjectWithDict()
        
        # 测试序列化
        result = self.kafka_queue._json_serializer(obj)
        expected = {'key': 'value', 'number': 42}
        self.assertEqual(result, expected)
        
        result = self.redis_queue._json_serializer(obj)
        self.assertEqual(result, expected)
    
    def test_object_with_dict_attribute(self):
        """测试具有__dict__属性的对象序列化"""
        class ObjectWithDictAttr:
            def __init__(self):
                self.key = 'value'
                self.number = 42
        
        obj = ObjectWithDictAttr()
        
        # 测试序列化
        result = self.kafka_queue._json_serializer(obj)
        expected = {'key': 'value', 'number': 42}
        self.assertEqual(result, expected)
        
        result = self.redis_queue._json_serializer(obj)
        self.assertEqual(result, expected)
    
    def test_import_error_handling(self):
        """测试导入错误处理"""
        # 模拟PydanticObjectId
        class MockPydanticObjectId:
            def __init__(self, value):
                self.value = value
            
            def __str__(self):
                return self.value
        
        test_objectid = MockPydanticObjectId("507f1f77bcf86cd799439011")
        
        # 模拟导入失败
        with patch('builtins.__import__') as mock_import:
            def import_side_effect(name, *args, **kwargs):
                if name == 'beanie':
                    raise ImportError("No module named 'beanie'")
                return __import__(name, *args, **kwargs)
            
            mock_import.side_effect = import_side_effect
            
            # 当导入失败时，序列化器会继续检查其他条件
            # 由于MockPydanticObjectId有__dict__属性，会返回其__dict__
            result = self.kafka_queue._json_serializer(test_objectid)
            self.assertIsInstance(result, dict)
            self.assertEqual(result, {'value': "507f1f77bcf86cd799439011"})
    
    def test_string_serialization(self):
        """测试字符串序列化"""
        test_string = "test_string"
        
        # 字符串不应该被序列化器处理，应该抛出TypeError
        with self.assertRaises(TypeError):
            self.kafka_queue._json_serializer(test_string)
        
        with self.assertRaises(TypeError):
            self.redis_queue._json_serializer(test_string)
    
    def test_number_serialization(self):
        """测试数字序列化"""
        test_int = 42
        test_float = 3.14
        
        # 数字不应该被序列化器处理，应该抛出TypeError
        with self.assertRaises(TypeError):
            self.kafka_queue._json_serializer(test_int)
        
        with self.assertRaises(TypeError):
            self.kafka_queue._json_serializer(test_float)


if __name__ == '__main__':
    unittest.main() 