from typing import TypeVar, Generic, List, Optional, Type, Dict, Any
from beanie import Document
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

T = TypeVar("T", bound=Document)

class BaseDAO(Generic[T]):
    """基础 DAO 类，提供通用的数据库操作方法"""
    
    def __init__(self, model: Type[T]):
        self.model = model
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @property
    def collection(self):
        """获取数据库集合对象"""
        return self.model.get_motor_collection()
    
    async def find_by_id(self, id: str) -> Optional[T]:
        """根据 ID 查找文档"""
        return await self.model.get(id)
    
    async def find_one(self, filter_dict: Dict[str, Any]) -> Optional[T]:
        """根据条件查找单个文档"""
        return await self.model.find_one(filter_dict)
    
    async def find_many(
        self,
        filter_dict: Dict[str, Any],
        skip: int = 0,
        limit: int = 100,
        sort: List[tuple] = None
    ) -> List[T]:
        """根据条件查找多个文档"""
        query = self.model.find(filter_dict).skip(skip).limit(limit)
        if sort:
            query = query.sort(sort)
        return await query.to_list()
    
    async def insert_one(self, document: T) -> T:
        """插入单个文档"""
        return await document.insert()
    
    async def insert_many(self, documents: List[T]) -> List[T]:
        """批量插入文档"""
        return await self.collection.insert_many(documents)
    
    async def update_one(
        self,
        filter_dict: Dict[str, Any],
        update_dict: Dict[str, Any],
        upsert: bool = False
    ) -> bool:
        """更新单个文档"""
        result = await self.collection.find_one(filter_dict).update(
            {"$set": {**update_dict, "last_updated_at": datetime.utcnow()}},
            upsert=upsert
        )
        return bool(result.modified_count)
    
    async def update_many(
        self,
        filter_dict: Dict[str, Any],
        update_dict: Dict[str, Any]
    ) -> int:
        """批量更新文档"""
        result = await self.collection.find(filter_dict).update(
            {"$set": {**update_dict, "last_updated_at": datetime.utcnow()}}
        )
        return result.modified_count
    
    async def delete_one(self, filter_dict: Dict[str, Any]) -> bool:
        """删除单个文档"""
        result = await self.collection.find_one(filter_dict).delete()
        return bool(result.deleted_count)
    
    async def delete_many(self, filter_dict: Dict[str, Any]) -> int:
        """批量删除文档"""
        result = await self.collection.find(filter_dict).delete()
        return result.deleted_count
    
    async def count(self, filter_dict: Dict[str, Any]) -> int:
        """计数"""
        return await self.collection.find(filter_dict).count() 