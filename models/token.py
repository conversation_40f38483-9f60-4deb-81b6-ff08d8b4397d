from beanie import Document, Indexed
from datetime import datetime
from typing import Optional, Dict

class Token(Document):
    """代币信息模型"""
    
    # 代币地址（作为主键）
    address: Indexed(str, unique=True)
    
    # 代币基本信息
    name: Optional[str] = None
    symbol: Optional[str] = None
    decimals: Optional[int] = None
    icon: Optional[str] = None
    supply: Optional[str | int] = None
    
    # 创建信息
    creator: Optional[str] = None
    created_tx: Optional[str] = None
    created_time: Optional[datetime] = None
    
    # 首次铸造信息
    first_mint_tx: Optional[str] = None
    first_mint_time: Optional[datetime] = None
    first_mint_amount: Optional[float] = None
    
    # 权限信息
    token_authority: Optional[str] = None
    freeze_authority: Optional[str] = None
    
    # 扩展信息
    coingecko_id: Optional[str] = None
    coinmarketcap_id: Optional[str] = None
    website: Optional[str] = None
    twitter: Optional[str] = None
    telegram: Optional[str] = None
    discord: Optional[str] = None
    medium: Optional[str] = None
    description: Optional[str] = None
    
    # 元数据
    metadata_uri: Optional[str] = None
    metadata_update_authority: Optional[str] = None
    
    # 价格信息
    GmgnToken1m: Optional[float] = None  # 1分钟价格
    GmgnToken5m: Optional[float] = None  # 5分钟价格
    GmgnToken1h: Optional[float] = None  # 1小时价格
    GmgnToken6h: Optional[float] = None  # 6小时价格
    GmgnToken24h: Optional[float] = None  # 24小时价格
    
    # 更新时间
    updated_at: datetime = datetime.utcnow()
    
    class Settings:
        name = "tokens"
        
    @classmethod
    async def get_or_create(cls, mint_address: str) -> 'Token':
        """获取或创建代币记录"""
        token = await cls.find_one({"address": mint_address})
        if not token:
            token = cls(address=mint_address)
            await token.save()
        return token 