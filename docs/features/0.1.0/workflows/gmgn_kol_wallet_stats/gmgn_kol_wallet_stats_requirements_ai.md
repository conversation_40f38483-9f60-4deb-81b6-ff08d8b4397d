# GMGN KOL钱包统计数据爬取功能详细需求规格

**创建日期**: 2025-06-03  
**功能模块**: workflows/gmgn_kol_wallet_stats  
**版本**: 0.1.0

## 1. 功能概述

实现GMGN平台钱包统计数据的自动化爬取功能，定期从KOL钱包列表中获取需要更新的钱包地址，调用GMGN API获取详细的钱包统计数据，并将爬取到的数据存储到独立的`gmgn_wallet_stats`数据表中。通过钱包地址关联，支持后续的聚合查询和数据分析。

## 2. 接口分析

### 2.1 目标API接口
- **URL模式**: `https://gmgn.ai/api/v1/wallet_stat/sol/{wallet_address}/all`
- **请求方法**: GET
- **必需参数**:
  - `device_id`: 设备标识符
  - `client_id`: 客户端标识符 
  - `from_app`: 来源应用 (gmgn)
  - `app_ver`: 应用版本
  - `tz_name`: 时区名称 (Asia/Shanghai)
  - `tz_offset`: 时区偏移 (28800)
  - `app_lang`: 应用语言 (zh-CN)
  - `fp_did`: 指纹设备标识
  - `os`: 操作系统 (web)
  - `period`: 时间周期 (all/7d/1d等) - **关键参数，影响所有统计数据的时间窗口**

**重要说明**: `period`参数决定返回数据的时间窗口语义：
- `period=all`: 返回总体统计数据，如`winrate`表示总胜率
- `period=7d`: 返回7天统计数据，如`winrate`表示7天胜率  
- `period=1d`: 返回1天统计数据，如`winrate`表示1天胜率

### 2.2 返回数据结构
根据实际API响应，返回包含以下关键统计数据：

#### 2.2.1 交易统计
- `buy`, `sell`: 总买入/卖出次数
- `buy_1d`, `sell_1d`: 1天内买入/卖出次数
- `buy_7d`, `sell_7d`: 7天内买入/卖出次数  
- `buy_30d`, `sell_30d`: 30天内买入/卖出次数

#### 2.2.2 收益指标
- `pnl`, `pnl_1d`, `pnl_7d`, `pnl_30d`: 不同时间窗口的收益率
- `all_pnl`: 总收益率（与pnl相同）
- `realized_profit`, `realized_profit_1d`, `realized_profit_7d`, `realized_profit_30d`: 已实现收益
- `unrealized_profit`, `unrealized_pnl`: 未实现收益
- `total_profit`, `total_profit_pnl`: 总利润和总收益率

#### 2.2.3 账户余额信息
- `balance`: 总余额
- `eth_balance`, `sol_balance`, `trx_balance`, `bnb_balance`: 各币种余额
- `total_value`: 总价值(USD)

#### 2.2.4 交易绩效指标
- `winrate`: 总胜率（注意：只有总胜率，无按时间窗口分割）
- `token_sold_avg_profit`: 代币平均卖出收益
- `history_bought_cost`: 历史买入成本
- `token_avg_cost`: 代币平均成本
- `token_num`: 交易代币总数
- `profit_num`: 盈利代币数量
- `avg_holding_peroid`: 平均持仓时间

#### 2.2.5 收益分布统计
- `pnl_lt_minus_dot5_num`: 收益率<-0.5的次数
- `pnl_minus_dot5_0x_num`: -0.5<=收益率<0的次数
- `pnl_lt_2x_num`: 0<=收益率<2的次数
- `pnl_2x_5x_num`: 2<=收益率<5的次数
- `pnl_gt_5x_num`: 收益率>=5的次数

#### 2.2.6 其他信息
- `gas_cost`: 手续费成本
- `bind`: 是否绑定
- `avatar`, `name`, `ens`: 用户基本信息
- `tags`, `tag_rank`: 标签和排名
- `twitter_*`: Twitter相关信息
- `followers_count`: 关注者数量
- `is_contract`: 是否为合约地址

#### 2.2.7 风险指标对象
- `risk.token_active`: 活跃代币数
- `risk.token_honeypot`: 蜜罐代币数
- `risk.token_honeypot_ratio`: 蜜罐代币比率
- `risk.no_buy_hold`: 无买入持有数
- `risk.no_buy_hold_ratio`: 无买入持有比率
- `risk.sell_pass_buy`: 卖出超过买入数
- `risk.sell_pass_buy_ratio`: 卖出超过买入比率
- `risk.fast_tx`: 快速交易数
- `risk.fast_tx_ratio`: 快速交易比率

#### 2.2.8 时间信息
- `last_active_timestamp`: 最后活跃时间戳
- `updated_at`: 更新时间戳
- `refresh_requested_at`: 刷新请求时间戳（可为null）

## 3. 数据架构设计

### 3.1 数据表设计策略
采用分离存储策略，将GMGN钱包统计数据存储到独立的`gmgn_wallet_stats`表中，与现有的`kol_wallets`表通过`wallet_address`字段关联：

- **kol_wallets表**: 存储KOL基本信息、用户档案、社交媒体信息等
- **gmgn_wallet_stats表**: 专门存储GMGN平台的钱包统计数据
- **关联方式**: 通过`wallet_address`字段进行表间关联

### 3.2 数据表结构
新建`GmgnWalletStats`数据模型，包含以下字段组：

#### 3.2.1 基本标识字段
- `wallet_address`: 钱包地址（主键，与kol_wallets表关联）
- `chain`: 区块链名称（固定为"solana"）
- `period`: 数据时间窗口（all/7d/1d等），用于区分同一钱包不同时间窗口的统计数据

#### 3.2.2 交易统计字段
- `buy`: 总买入次数
- `sell`: 总卖出次数  
- `buy_1d/7d/30d`: 不同时间窗口的买入次数
- `sell_1d/7d/30d`: 不同时间窗口的卖出次数

#### 3.2.3 收益和财务字段
- `pnl`: 总收益率
- `pnl_1d/7d/30d`: 不同时间窗口的收益率
- `all_pnl`: 总收益率（与pnl相同）
- `realized_profit`: 总实现收益
- `realized_profit_1d/7d/30d`: 不同时间窗口的实现收益
- `unrealized_profit`: 未实现收益
- `unrealized_pnl`: 未实现收益率
- `total_profit`: 总利润
- `total_profit_pnl`: 总利润收益率
- `balance`: 总余额
- `sol_balance/eth_balance/trx_balance/bnb_balance`: 不同币种余额
- `total_value`: 总价值(USD)

#### 3.2.4 交易绩效字段
- `winrate`: 总胜率（注意：API返回总胜率，非按时间窗口）
- `token_sold_avg_profit`: 代币平均卖出收益
- `history_bought_cost`: 历史买入成本
- `token_avg_cost`: 代币平均成本
- `token_num`: 交易代币总数
- `profit_num`: 盈利代币数量
- `avg_holding_peroid`: 平均持仓时间
- `gas_cost`: 手续费成本

#### 3.2.5 收益分布统计
- `pnl_lt_minus_dot5_num`: 收益率<-0.5的次数
- `pnl_minus_dot5_0x_num`: -0.5<=收益率<0的次数
- `pnl_lt_2x_num`: 0<=收益率<2的次数
- `pnl_2x_5x_num`: 2<=收益率<5的次数
- `pnl_gt_5x_num`: 收益率>=5的次数

#### 3.2.6 用户和社交信息
- `bind`: 是否绑定
- `avatar`: 头像URL
- `name`: 用户名
- `ens`: ENS域名
- `tags`: 标签列表
- `tag_rank`: 标签排名
- `twitter_name`: Twitter名称
- `twitter_username`: Twitter用户名
- `twitter_bind`: Twitter绑定状态
- `twitter_fans_num`: Twitter粉丝数
- `followers_count`: 关注者数量
- `is_contract`: 是否为合约地址

#### 3.2.7 风险指标
- `risk`: 风险评估对象，包含：
  - `token_active`: 活跃代币数
  - `token_honeypot`: 蜜罐代币数
  - `token_honeypot_ratio`: 蜜罐代币比率
  - `no_buy_hold`: 无买入持有数
  - `no_buy_hold_ratio`: 无买入持有比率
  - `sell_pass_buy`: 卖出超过买入数
  - `sell_pass_buy_ratio`: 卖出超过买入比率
  - `fast_tx`: 快速交易数
  - `fast_tx_ratio`: 快速交易比率

#### 3.2.8 时间信息
- `last_active_timestamp`: 最后活跃时间戳
- `updated_at`: API数据更新时间戳
- `refresh_requested_at`: 刷新请求时间戳（可为null）
- `created_at`: 记录创建时间（系统字段）
- `updated_at_system`: 记录更新时间（系统字段）

### 3.3 多时间窗口数据存储策略

#### 3.3.1 数据存储模式
- **主要模式**: 优先存储`period=all`的总体统计数据
- **扩展模式**: 支持同一钱包存储多个时间窗口的数据记录
- **唯一标识**: 使用`(wallet_address, chain, period)`三元组作为逻辑主键

#### 3.3.2 数据更新策略
- 默认获取并存储`period=all`的总体数据
- 可配置支持获取特定时间窗口数据（如7d、1d）
- 每个时间窗口的数据独立更新，互不影响
- 保持数据版本的时间戳记录

#### 3.3.3 查询优化
- 建立`(wallet_address, period)`复合索引
- 支持按时间窗口过滤的高效查询
- 默认查询返回`period=all`数据，除非特别指定

## 4. 数据流程需求

### 4.1 数据获取流程
1. 从`kol_wallets`表中获取需要更新统计数据的钱包地址列表
2. 按批次对钱包地址进行爬取，控制并发数避免触发反爬
3. 对每个钱包地址调用GMGN API获取统计数据
4. 数据预处理和格式转换
5. 将数据存储或更新到`gmgn_wallet_stats`表

### 4.2 数据筛选条件
- 优先更新7天内有活动的KOL钱包
- 排除最近1小时内已更新统计数据的钱包
- 支持按KOL标签过滤（如只更新kol标签的钱包）

### 4.3 数据同步策略
- **新增模式**: 钱包地址在统计表中不存在时，创建新记录
- **更新模式**: 钱包地址已存在时，更新统计数据
- **关联验证**: 确保钱包地址在kol_wallets表中存在
- **数据完整性**: 保持与原始KOL数据的关联性

## 5. 工作流设计需求

### 5.1 节点结构
1. **调度节点** (SchedulerNode): 定期触发工作流执行
2. **数据获取节点** (DataFetchNode): 从kol_wallets表获取待更新的钱包地址列表
3. **爬取处理节点** (SpiderProcessNode): 并发调用GMGN API获取钱包统计数据
4. **数据存储节点** (StorageNode): 批量存储到gmgn_wallet_stats表

### 5.2 执行频率和并发控制
- **执行间隔**: 每30分钟触发一次
- **批次大小**: 每批处理50个钱包地址
- **并发数**: 最大5个并发请求，避免触发反爬
- **重试机制**: 失败的请求最多重试3次

### 5.3 容错和监控需求
- 单个钱包爬取失败不影响其他钱包的处理
- 记录爬取成功率和失败原因
- 支持手动触发特定钱包的数据更新
- 记录每次更新的数据变化量用于监控

## 6. 数据聚合查询需求

### 6.1 关联查询支持
支持通过wallet_address字段进行表间关联查询：

```sql
-- 获取KOL基本信息和统计数据
SELECT k.*, g.* 
FROM kol_wallets k 
LEFT JOIN gmgn_wallet_stats g ON k.wallet_address = g.wallet_address
WHERE k.tags CONTAINS 'kol'
```

### 6.2 数据分析场景
- KOL交易绩效分析：结合用户档案和交易统计
- 收益表现排名：按不同时间窗口进行收益排序  
- 风险偏好分析：结合社交影响力和风险指标
- 趋势追踪：监控KOL群体的整体交易表现

## 7. 性能和安全需求

### 7.1 性能要求
- 每小时能处理至少200个钱包的数据更新
- 单个API请求超时时间不超过30秒
- 数据库批量操作延迟控制在10秒内
- 表关联查询响应时间控制在5秒内

### 7.2 反爬虫对策
- 使用项目现有的代理IP池和User-Agent轮换机制
- 请求间隔随机化（1-3秒）
- 请求头随机化，模拟真实浏览器行为
- 实现请求失败时的代理切换机制

## 8. 验收标准

### 8.1 功能验收
- [ ] 能够成功从kol_wallets表获取待更新的钱包地址列表
- [ ] 能够成功调用GMGN API并解析返回的钱包统计数据
- [ ] 数据能够正确存储到独立的gmgn_wallet_stats表
- [ ] 支持新增和更新两种数据同步模式
- [ ] 工作流能够稳定运行，错误处理机制正常工作
- [ ] 支持批量处理和并发控制

### 8.2 性能验收  
- [ ] 完整处理100个钱包的时间不超过10分钟
- [ ] API请求成功率达到95%以上
- [ ] 内存使用稳定，不出现内存泄漏
- [ ] 表关联查询性能满足要求

### 8.3 数据质量验收
- [ ] 存储的数据与API返回数据一致
- [ ] 时间戳字段正确转换为datetime格式
- [ ] 数值字段的精度保持一致
- [ ] 与kol_wallets表的关联关系正确

### 8.4 数据架构验收
- [ ] 独立数据表设计符合需求
- [ ] 支持通过wallet_address进行表关联
- [ ] 数据模型字段覆盖完整
- [ ] 索引设计优化查询性能 