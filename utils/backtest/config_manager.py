import argparse
from typing import Dict, Any, Optional, List
import json
import yaml
from dataclasses import dataclass, field, fields
import time
import itertools

@dataclass
class BacktestConfig:
    """回测配置类"""
    transaction_lookback_hours: int = 24  # 买入信号时间窗口
    transaction_min_amount: float = 1000.0  # 最小交易价格
    kol_account_min_count: int = 3  # 最小kol个数
    token_mint_lookback_hours: int = 48  # 铸造时间离当前时间的小时数
    kol_account_min_txs: int = 10  # kol最小交易个数
    kol_account_max_txs: int = 100  # kol最大交易个数
    backtest_start_time: Optional[int] = None  # 回测开始时间戳
    backtest_end_time: Optional[int] = None    # 回测结束时间戳
    sell_strategy_hours: int = 24              # 卖出信号最长时间窗口，如果没有监听到信号则最长在多少时间后卖出
    sell_kol_ratio: float = 0.5                # 卖出信号KOL比例
    buy_step_size: Optional[int] = None        # 买入信号时间窗口的步长，默认等于买入窗口大小
    sell_step_size: Optional[int] = None       # 卖出信号时间窗口的步长，默认等于卖出窗口大小
    skip_token_api_query: bool = False         # 是否跳过代币API查询
    use_real_price: bool = False               # 是否使用真实价格数据
    skip_price_api_query: bool = True          # 是否跳过价格API查询
    # 新增字段：控制策略信号检查的频率（秒）
    processing_interval: Optional[int] = None
    # 新增字段：模拟真实流程中相同代币通知的最小间隔（分钟）
    same_token_notification_interval_minutes: int = 60
    kol_min_winrate_7d: Optional[float] = None # 新增：KOL最低7日胜率阈值

    def __post_init__(self):
        """后处理初始化，设置默认的步长值"""
        # 如果买入步长未设置，默认使用买入窗口大小
        if self.buy_step_size is None:
            self.buy_step_size = self.transaction_lookback_hours
            
        # 如果卖出步长未设置，默认使用卖出窗口大小
        if self.sell_step_size is None:
            self.sell_step_size = self.sell_strategy_hours

class ConfigManager:
    """配置管理器"""
    
    @staticmethod
    def load_from_file(file_path: str) -> BacktestConfig:
        """从文件加载配置
        
        Args:
            file_path: 配置文件路径，支持JSON和YAML
            
        Returns:
            BacktestConfig: 配置对象
        """
        if file_path.endswith('.json'):
            with open(file_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
        elif file_path.endswith(('.yaml', '.yml')):
            with open(file_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
        else:
            raise ValueError("不支持的配置文件格式，请使用JSON或YAML")
        
        return BacktestConfig(**config_data)
    
    @staticmethod
    def load_from_args() -> BacktestConfig:
        """从命令行参数加载配置
        
        Returns:
            BacktestConfig: 配置对象
        """
        parser = argparse.ArgumentParser(description='KOL交易策略回测系统')
        parser.add_argument('--config', type=str, help='配置文件路径')
        parser.add_argument('--transaction_lookback_hours', type=int, help='交易记录回溯时间(小时)')
        parser.add_argument('--transaction_min_amount', type=float, help='最小交易金额(USD)')
        parser.add_argument('--kol_account_min_count', type=int, help='最小KOL账号数量')
        parser.add_argument('--token_mint_lookback_hours', type=int, help='代币铸造回溯时间(小时)')
        parser.add_argument('--kol_account_min_txs', type=int, help='KOL账号最少交易次数')
        parser.add_argument('--kol_account_max_txs', type=int, help='KOL账号最多交易次数')
        parser.add_argument('--backtest_start_time', type=int, help='回测开始时间戳')
        parser.add_argument('--backtest_end_time', type=int, help='回测结束时间戳')
        parser.add_argument('--sell_strategy_hours', type=int, help='卖出策略时间窗口(小时)')
        parser.add_argument('--sell_kol_ratio', type=float, help='卖出KOL比例阈值')
        parser.add_argument('--buy_step_size', type=int, help='买入信号时间窗口的步长(小时)')
        parser.add_argument('--sell_step_size', type=int, help='卖出信号时间窗口的步长(小时)')
        parser.add_argument('--skip_token_api_query', action='store_true', help='是否跳过代币API查询')
        parser.add_argument('--use_real_price', action='store_true', help='是否使用真实价格数据')
        parser.add_argument('--skip_price_api_query', action='store_true', help='是否跳过价格API查询')
        
        args = parser.parse_args()
        
        # 如果指定了配置文件，先从文件加载
        if args.config:
            config = ConfigManager.load_from_file(args.config)
        else:
            config = BacktestConfig()
            
        # 然后用命令行参数覆盖
        for key, value in vars(args).items():
            if value is not None and key != 'config':
                setattr(config, key, value)
        
        return config
    
    @staticmethod
    def generate_parameter_combinations(param_grid: Dict[str, List]) -> List[Dict[str, Any]]:
        """生成参数网格的所有组合
        
        Args:
            param_grid: 参数网格，如 {'param1': [1, 2], 'param2': [3, 4]}
            
        Returns:
            List[Dict[str, Any]]: 参数组合列表
        """
        import itertools
        
        # 获取所有参数名和可能的值
        param_names = list(param_grid.keys())
        param_values = list(param_grid.values())
        
        # 生成笛卡尔积
        combinations = list(itertools.product(*param_values))
        
        # 转换为字典列表
        result = []
        for combination in combinations:
            config_dict = {name: value for name, value in zip(param_names, combination)}
            result.append(config_dict)
        
        return result 