from datetime import datetime
from typing import List, Optional, Dict, Any
from beanie import Document, PydanticObjectId
from pydantic import Field
from utils.basic_utils import get_current_time_dt

class Signal(Document):
    """
    存储监控系统生成的信号信息
    """
    token_address: str = Field(..., index=True)
    token_name: Optional[str] = None
    token_symbol: Optional[str] = None
    signal_type: str = Field(..., index=True) # 例如: 'kol_buy', 'price_alert'
    trigger_timestamp: datetime = Field(default_factory=get_current_time_dt, index=True)
    trigger_conditions: Dict[str, Any] = Field(...) # 触发信号的具体条件
    hit_kol_wallets: Optional[List[str]] = None # 触发信号的具体 KOL 钱包地址 (如果适用)
    status: str = Field(default='open', index=True) # 信号状态: 'open', 'sold'
    buy_signal_ref_id: Optional[PydanticObjectId] = Field(default=None, index=True) # 关联的买入信号 ID (仅用于卖出信号)
    
    trade_record_ids: List[PydanticObjectId] = Field(default_factory=list) # 关联的交易记录ID
    
    created_at: datetime = Field(default_factory=get_current_time_dt)
    updated_at: Optional[datetime] = None

    class Settings:
        name = "signals" # MongoDB collection name 