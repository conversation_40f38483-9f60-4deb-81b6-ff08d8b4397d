from typing import Dict, List, Optional, Any
from datetime import datetime
# from utils.workflows.workflow import WorkflowData # Removed unused import
from utils.kol_scoring_service import KOLScoringService
from dao.trade_record_dao import TradeRecordDAO
from dao.kol_wallet_dao import KOLWalletDAO
from dao.config_dao import ConfigDA<PERSON>
from models.kol_wallet import KOLWallet
from models.config import KOLScoringConfig
import logging

logger = logging.getLogger(__name__)

class KOLScoringWorkflowHandler:
    """KOL打分工作流处理器 - 已更新以使用聚合查询和细粒度打分服务"""
    
    def __init__(self):
        self.scoring_service = KOLScoringService()
        self.trade_dao = TradeRecordDAO()
        self.kol_wallet_dao = KOLWalletDAO()
        self.config_dao = ConfigDAO()

    async def process_pending_scores_from_aggregation(self, limit: int = 50, max_lookback_days: int = 90) -> Dict[str, Any]:
        """
        主处理函数：获取待打分组合并为它们打分。
        将由工作流节点直接调用。
        """
        logger.info(f"开始处理待打分的KOL交易组合 (聚合查询)，limit={limit}, lookback_days={max_lookback_days}")
        
        results_summary = {
            "total_combinations_fetched": 0,
            "successfully_processed_count": 0,
            "failed_to_process_count": 0,
            "errors": [],
            "detailed_results": []
        }

        try:
            # 1. 获取打分配置 (一次性)
            scoring_config = await self.scoring_service.get_scoring_config()
            if not scoring_config: # get_scoring_config 总是返回一个默认配置
                logger.error("无法获取KOL打分配置，终止处理。")
                results_summary["errors"].append("Failed to retrieve scoring configuration.")
                return results_summary

            # 2. 使用聚合查询获取待打分的KOL、交易对、策略组合
            # 返回: List[{"buy_trade": TradeRecord, "sell_trade": TradeRecord, "kol_wallet_address": str, "strategy_name": str}]
            pending_combinations = await self.trade_dao.get_pending_kol_trade_combinations_for_scoring(
                limit=limit, 
                max_lookback_days=max_lookback_days
            )
            results_summary["total_combinations_fetched"] = len(pending_combinations)
            logger.info(f"从聚合查询获取了 {len(pending_combinations)} 个待打分组合。")

            if not pending_combinations:
                logger.info("没有找到待打分的组合。")
                return results_summary

            for combo in pending_combinations:
                buy_trade = combo["buy_trade"]
                sell_trade = combo["sell_trade"]
                kol_wallet_address = combo["kol_wallet_address"]
                strategy_name = combo["strategy_name"]
                
                single_combo_result = {
                    "buy_trade_id": str(buy_trade.id),
                    "sell_trade_id": str(sell_trade.id),
                    "kol_wallet_address": kol_wallet_address,
                    "strategy_name": strategy_name,
                    "service_call_successful": False,
                    "service_response": None
                }

                try:
                    # 3. 获取KOLWallet对象
                    kol_wallet_obj = await self.kol_wallet_dao.find_by_wallet_address(kol_wallet_address)
                    if not kol_wallet_obj:
                        logger.warning(f"未找到KOL钱包对象: {kol_wallet_address}，跳过此组合。")
                        single_combo_result["service_response"] = {"error": "KOLWallet object not found"}
                        results_summary["detailed_results"].append(single_combo_result)
                        results_summary["failed_to_process_count"] += 1
                        continue
                    
                    # 4. 计算 PnL (移至服务调用前)
                    pnl = self.scoring_service.calculate_pnl(buy_trade, sell_trade)

                    # 5. 调用细粒度的打分服务方法
                    # score_individual_kol_combination(buy_trade, sell_trade, kol_wallet_address, strategy_name, pnl, config)
                    service_response = await self.scoring_service.score_individual_kol_combination(
                        buy_trade=buy_trade,
                        sell_trade=sell_trade,
                        kol_wallet_address=kol_wallet_address,
                        strategy_name=strategy_name,
                        pnl=pnl, # 传入计算好的 PnL
                        config=scoring_config # 传入获取的配置
                        # force_rescore 默认为 False，因为聚合查询已处理了日志检查
                    )
                    
                    single_combo_result["service_response"] = service_response
                    if service_response.get("success"):
                        single_combo_result["service_call_successful"] = True
                        results_summary["successfully_processed_count"] += 1
                        logger.info(f"成功处理组合: KOL={kol_wallet_address}, Strategy={strategy_name}, Buy={buy_trade.id}, Sell={sell_trade.id}")
                    else:
                        results_summary["failed_to_process_count"] += 1
                        logger.warning(f"处理组合失败: KOL={kol_wallet_address}, Strategy={strategy_name}, Error: {service_response.get('error')}")
                
                except Exception as e_inner:
                    logger.error(f"处理单个组合时发生内部错误: KOL={kol_wallet_address}, Strategy={strategy_name}, Error: {str(e_inner)}")
                    single_combo_result["service_response"] = {"error": f"Inner processing exception: {str(e_inner)}"}
                    results_summary["failed_to_process_count"] += 1
                
                results_summary["detailed_results"].append(single_combo_result)

            logger.info(f"KOL打分工作流批处理完成。获取组合: {results_summary['total_combinations_fetched']}, "
                        f"成功处理: {results_summary['successfully_processed_count']}, "
                        f"失败处理: {results_summary['failed_to_process_count']}")
            return results_summary

        except Exception as e_outer:
            logger.error(f"KOL打分工作流处理器发生严重错误: {str(e_outer)}")
            results_summary["errors"].append(f"Outer processing exception: {str(e_outer)}")
            return results_summary

# 工作流YAML将调用的函数接口
async def process_kol_scores() -> Dict[str, Any]:
    """
    工作流入口点：查找并处理待打分的KOL交易组合。
    现在直接调用更新后的处理器方法。
    参数从配置系统中读取。
    """
    handler = KOLScoringWorkflowHandler()
    
    # 从配置系统中获取参数
    try:
        scoring_config = await handler.scoring_service.get_scoring_config()
        limit = scoring_config.workflow_limit
        max_lookback_days = scoring_config.workflow_max_lookback_days
        
        logger.info(f"从配置中读取工作流参数: limit={limit}, max_lookback_days={max_lookback_days}")
        
        return await handler.process_pending_scores_from_aggregation(
            limit=limit, 
            max_lookback_days=max_lookback_days
        )
    except Exception as e:
        logger.error(f"处理KOL打分时发生错误: {str(e)}")
        return {
            "total_combinations_fetched": 0,
            "successfully_processed_count": 0,
            "failed_to_process_count": 0,
            "errors": [f"Configuration or handler error: {str(e)}"],
            "detailed_results": []
        }

# 清理旧的、不再使用的导出函数 (可选，或者在YAML中更新调用)
# async def find_pending_scoring_trades() -> List[WorkflowData]: ...
# async def score_trade_pairs(workflow_data: WorkflowData) -> Dict[str, Any]: ...
# async def validate_scoring_result(result: Dict[str, Any]) -> bool: ... 