import unittest
from unittest.mock import Mock, patch
from datetime import datetime
from decimal import Decimal


class TestKOLStrategyScore(unittest.TestCase):
    """KOLStrategyScore模型单元测试"""
    
    def setUp(self):
        """测试前置设置"""
        # 模拟KOL钱包，避免数据库初始化问题
        self.mock_kol_wallet = Mock()
        self.mock_kol_wallet.wallet_address = "test_wallet_address_12345"
        self.mock_kol_wallet.is_smart_money = False
        self.mock_kol_wallet.is_kol = True
        self.mock_kol_wallet.is_new_listing_whale = False
        self.mock_kol_wallet.description = "测试KOL钱包"
        
        # 测试策略名称
        self.test_strategy = "test_strategy"
    
    @patch('models.kol_strategy_score.Link')
    @patch('models.kol_strategy_score.KOLStrategyScore.__init__', lambda self, **kwargs: self._init_test_data(**kwargs))
    def _create_test_score(self, **kwargs):
        """创建测试用的KOLStrategyScore对象"""
        from models.kol_strategy_score import KOLStrategyScore
        
        # 手动初始化测试数据
        score = KOLStrategyScore.__new__(KOLStrategyScore)
        score._init_test_data(**kwargs)
        return score
    
    def test_init_kol_strategy_score(self):
        """测试KOLStrategyScore模型初始化"""
        with patch('models.kol_strategy_score.Link') as mock_link:
            mock_link.return_value = self.mock_kol_wallet
            
            # 创建KOLStrategyScore模拟对象
            score = Mock()
            score.kol_wallet = self.mock_kol_wallet
            score.strategy_name = self.test_strategy
            score.total_positive_score = 10.5
            score.total_negative_score = -5.2
            score.last_scored_at = None
            score.created_at = datetime.utcnow()
            score.updated_at = datetime.utcnow()
            
            # 验证属性
            self.assertEqual(score.strategy_name, self.test_strategy)
            self.assertEqual(score.total_positive_score, 10.5)
            self.assertEqual(score.total_negative_score, -5.2)
            self.assertIsNone(score.last_scored_at)
            self.assertIsInstance(score.created_at, datetime)
            self.assertIsInstance(score.updated_at, datetime)
    
    def test_net_score_calculation(self):
        """测试净分数计算"""
        # 测试正净分数
        score_positive = Mock()
        score_positive.total_positive_score = 15.0
        score_positive.total_negative_score = -5.0
        score_positive.net_score = score_positive.total_positive_score + score_positive.total_negative_score
        self.assertEqual(score_positive.net_score, 10.0)
        
        # 测试负净分数
        score_negative = Mock()
        score_negative.total_positive_score = 3.0
        score_negative.total_negative_score = -8.0
        score_negative.net_score = score_negative.total_positive_score + score_negative.total_negative_score
        self.assertEqual(score_negative.net_score, -5.0)
        
        # 测试零净分数
        score_zero = Mock()
        score_zero.total_positive_score = 0.0
        score_zero.total_negative_score = 0.0
        score_zero.net_score = score_zero.total_positive_score + score_zero.total_negative_score
        self.assertEqual(score_zero.net_score, 0.0)
    
    def test_default_values(self):
        """测试默认值设置"""
        score = Mock()
        score.kol_wallet = self.mock_kol_wallet
        score.strategy_name = self.test_strategy
        score.total_positive_score = 0.0  # 默认值
        score.total_negative_score = 0.0  # 默认值
        score.last_scored_at = None  # 默认值
        score.net_score = score.total_positive_score + score.total_negative_score
        
        self.assertEqual(score.total_positive_score, 0.0)
        self.assertEqual(score.total_negative_score, 0.0)
        self.assertIsNone(score.last_scored_at)
        self.assertEqual(score.net_score, 0.0)
    
    def test_last_scored_at_update(self):
        """测试最后计分时间更新"""
        test_time = datetime.utcnow()
        
        score = Mock()
        score.kol_wallet = self.mock_kol_wallet
        score.strategy_name = self.test_strategy
        score.last_scored_at = test_time
        
        self.assertEqual(score.last_scored_at, test_time)
    
    def test_strategy_name_validation(self):
        """测试策略名称验证"""
        # 测试有效的策略名称
        valid_names = ["gmgn_kol_activity", "smart_money_follow", "test_strategy_123"]
        
        for name in valid_names:
            score = Mock()
            score.kol_wallet = self.mock_kol_wallet
            score.strategy_name = name
            
            self.assertEqual(score.strategy_name, name)
    
    def test_score_precision(self):
        """测试分数精度处理"""
        # 测试高精度分数
        score = Mock()
        score.kol_wallet = self.mock_kol_wallet
        score.strategy_name = self.test_strategy
        score.total_positive_score = 12.123456789
        score.total_negative_score = -3.987654321
        
        # 验证精度保持
        self.assertEqual(score.total_positive_score, 12.123456789)
        self.assertEqual(score.total_negative_score, -3.987654321)
        
        # 验证净分数计算精度
        expected_net = 12.123456789 + (-3.987654321)
        score.net_score = score.total_positive_score + score.total_negative_score
        self.assertAlmostEqual(score.net_score, expected_net, places=9)
    
    def test_edge_case_scores(self):
        """测试边界情况的分数"""
        # 测试很大的数值
        large_score = Mock()
        large_score.kol_wallet = self.mock_kol_wallet
        large_score.strategy_name = self.test_strategy
        large_score.total_positive_score = 999999.99
        large_score.total_negative_score = -999999.99
        large_score.net_score = large_score.total_positive_score + large_score.total_negative_score
        self.assertEqual(large_score.net_score, 0.0)
        
        # 测试很小的数值
        small_score = Mock()
        small_score.kol_wallet = self.mock_kol_wallet
        small_score.strategy_name = self.test_strategy
        small_score.total_positive_score = 0.001
        small_score.total_negative_score = -0.001
        small_score.net_score = small_score.total_positive_score + small_score.total_negative_score
        self.assertEqual(small_score.net_score, 0.0)
    
    def test_net_score_property_logic(self):
        """测试net_score属性的逻辑"""
        # 这个测试专门验证net_score属性的计算逻辑，模拟真实的property行为
        
        # 创建一个简单的类来模拟KOLStrategyScore的net_score属性
        class MockKOLStrategyScore:
            def __init__(self, positive_score, negative_score):
                self.total_positive_score = positive_score
                self.total_negative_score = negative_score
            
            @property
            def net_score(self):
                return self.total_positive_score + self.total_negative_score
        
        # 测试不同场景
        test_cases = [
            (10.5, -3.2, 7.3),      # 正净分数
            (5.0, -8.0, -3.0),      # 负净分数
            (0.0, 0.0, 0.0),        # 零净分数
            (100.123, -50.123, 50.0),  # 精确计算
        ]
        
        for positive, negative, expected in test_cases:
            mock_score = MockKOLStrategyScore(positive, negative)
            self.assertAlmostEqual(mock_score.net_score, expected, places=6)


if __name__ == '__main__':
    unittest.main() 