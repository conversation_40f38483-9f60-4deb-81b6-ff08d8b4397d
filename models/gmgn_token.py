from beanie import Document, Indexed
from datetime import datetime
from typing import Optional, Union, Any

class GmgnTokenBase(Document):
    """GMGN平台代币基础模型"""
    
    # 基本信息
    token_id: Indexed(int, unique=True)  # GMGN平台ID
    chain: Optional[str] = "sol"  # 链名称，默认为 sol
    address: Indexed(str)  # 代币地址
    symbol: Optional[str] = ""  # 代币符号，可以为空
    logo: Optional[str] = None  # 代币logo
    
    # 价格和市场数据
    price: Optional[float] = 0.0  # 当前价格
    price_change_percent: Optional[float] = 0.0  # 5分钟价格变化百分比
    price_change_percent1m: Optional[float] = 0.0  # 1分钟价格变化百分比
    price_change_percent5m: Optional[float] = 0.0  # 5分钟价格变化百分比
    price_change_percent1h: Optional[float] = 0.0  # 1小时价格变化百分比
    
    # 交易数据
    swaps: Optional[int] = 0  # 交易次数
    volume: Optional[float] = 0.0  # 交易量
    liquidity: Optional[float] = 0.0  # 流动性
    market_cap: Optional[float] = 0.0  # 市值
    buys: Optional[int] = 0  # 买入次数
    sells: Optional[int] = 0  # 卖出次数
    
    # 代币信息
    hot_level: Optional[int] = 0  # 热度等级
    pool_creation_timestamp: Optional[int] = 0  # 池子创建时间
    holder_count: Optional[int] = 0  # 持有人数量
    total_supply: Optional[float] = 0.0  # 总供应量
    open_timestamp: Optional[int] = 0  # 开盘时间
    initial_liquidity: Optional[float] = 0.0  # 初始流动性
    
    # 社交媒体信息
    twitter_username: Optional[str] = None  # Twitter用户名
    website: Optional[str] = None  # 网站
    telegram: Optional[str] = None  # Telegram链接
    
    # 安全相关
    is_show_alert: Optional[bool] = False  # 是否显示警告
    top_10_holder_rate: Optional[float] = 0.0  # 前10持有人比例
    renounced_mint: Optional[int] = 0  # 是否放弃铸币权
    renounced_freeze_account: Optional[int] = 0  # 是否放弃冻结账户权
    burn_ratio: Optional[Union[str, int]] = None  # 销毁比例，可以是字符串或整数
    burn_status: Optional[str] = None  # 销毁状态，可以为空
    dev_token_burn_amount: Optional[float] = None  # 开发者代币销毁数量
    dev_token_burn_ratio: Optional[float] = None  # 开发者代币销毁比例
    
    # 其他标记
    dexscr_ad: Optional[int] = 0  # DEX广告标记
    dexscr_update_link: Optional[int] = 0  # DEX更新链接标记
    cto_flag: Optional[int] = 0  # CTO标记
    twitter_change_flag: Optional[int] = 0  # Twitter变更标记
    creator_token_status: Optional[str] = None  # 创建者代币状态，可以为空
    creator_close: Optional[bool] = False  # 创建者是否关闭
    launchpad_status: Optional[int] = 0  # 发射台状态
    
    # 交易者相关
    rat_trader_amount_rate: Optional[float] = 0.0  # 套利交易者数量比例
    bluechip_owner_percentage: Optional[float] = 0.0  # 蓝筹持有者百分比
    smart_degen_count: Optional[int] = 0  # 智能投机者数量
    renowned_count: Optional[int] = 0  # 知名投资者数量
    is_wash_trading: Optional[bool] = False  # 是否存在洗盘交易
    
    # 更新时间
    updated_at: datetime = datetime.utcnow()
    
    class Settings:
        use_state_management = True

class GmgnToken1m(GmgnTokenBase):
    """1分钟时间段的GMGN代币数据"""
    class Settings:
        name = "gmgn_tokens_1m"

class GmgnToken5m(GmgnTokenBase):
    """5分钟时间段的GMGN代币数据"""
    class Settings:
        name = "gmgn_tokens_5m"

class GmgnToken1h(GmgnTokenBase):
    """1小时时间段的GMGN代币数据"""
    class Settings:
        name = "gmgn_tokens_1h"

class GmgnToken6h(GmgnTokenBase):
    """6小时时间段的GMGN代币数据"""
    class Settings:
        name = "gmgn_tokens_6h"

class GmgnToken24h(GmgnTokenBase):
    """24小时时间段的GMGN代币数据"""
    class Settings:
        name = "gmgn_tokens_24h"

    @classmethod
    async def get_or_create(cls, gmgn_id: int) -> 'GmgnToken':
        """获取或创建GMGN代币记录"""
        token = await cls.find_one({"token_id": gmgn_id})
        if not token:
            token = cls(token_id=gmgn_id)
            await token.save()
        return token 