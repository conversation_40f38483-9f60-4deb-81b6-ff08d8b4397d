from typing import Optional
from dao.base_dao import BaseDAO
from models.config import Config, ApplicationConfig

class ConfigDAO(BaseDAO[Config]):
    """配置数据访问对象"""
    
    def __init__(self):
        super().__init__(Config)
        
    async def get_config(self, type: str) -> Optional[Config]:
        return await self.find_one({"type": type})
    
    async def update_config(self, type: str, value: dict) -> bool:
        return await self.update_one({"type": type}, {"$set": {"value": value}})
    
    async def create_config(self, type: str, value: dict) -> bool:
        return await self.insert_one(Config(type=type, value=value))
    
    async def get_same_token_notification_interval(self) -> Optional[int]:
        config = await self.get_config("kol_activity")
        return config.data.same_token_notification_interval
    
    async def get_application_config(self) -> Optional[ApplicationConfig]:
        """Retrieve the application configuration."""
        config_doc = await self.find_one({"type": "application_config"})
        if config_doc and isinstance(config_doc.data, ApplicationConfig):
            return config_doc.data
        return None
    