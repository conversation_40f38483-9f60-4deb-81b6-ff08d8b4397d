from datetime import datetime
from typing import Optional, Dict, Any
from beanie import Document, Indexed
from pydantic import Field

class GmgnTokenStats(Document):
    """GMGN代币统计信息模型
    
    用于存储代币的统计信息，包括：
    - 基本信息（链、地址）
    - 持有者统计
    - 蓝筹持有者统计
    - 信号和调用统计
    """
    
    # 基本信息
    chain: str = Field(description="链名称")
    address: Indexed(str) = Field(description="代币地址")
    
    # 持有者统计
    holder_count: int = Field(default=0, description="持有者总数")
    
    # 蓝筹持有者统计
    bluechip_owner_count: int = Field(default=0, description="蓝筹持有者数量")
    bluechip_owner_percentage: float = Field(default=0.0, description="蓝筹持有者比例")
    
    # 信号和调用统计
    signal_count: int = Field(default=0, description="信号数量")
    degen_call_count: int = Field(default=0, description="Degen调用数量")
    top_rat_trader_percentage: float = Field(default=0.0, description="Top Rat交易者比例")
    
    # 时间信息
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")
    
    class Settings:
        name = "gmgn_token_stats"
        use_state_management = True
    
    @classmethod
    async def from_api_data(cls, chain: str, address: str, data: Dict[str, Any]) -> 'GmgnTokenStats':
        """从API数据创建模型实例
        
        Args:
            chain: 链名称
            address: 代币地址
            data: API返回的数据
            
        Returns:
            GmgnTokenStats: 模型实例
        """
        return cls(
            chain=chain,
            address=address,
            holder_count=data.get('holder_count', 0),
            bluechip_owner_count=data.get('bluechip_owner_count', 0),
            bluechip_owner_percentage=float(data.get('bluechip_owner_percentage', '0')),
            signal_count=data.get('signal_count', 0),
            degen_call_count=data.get('degen_call_count', 0),
            top_rat_trader_percentage=float(data.get('top_rat_trader_percentage', '0'))
        )
    
    class Config:
        json_schema_extra = {
            "example": {
                "chain": "sol",
                "address": "6p6xgHyF7AeE6TZkSmFsko444wqoP15icUSqi2jfGiPN",
                "holder_count": 629052,
                "bluechip_owner_count": 13230,
                "bluechip_owner_percentage": 0.0210316476221362,
                "signal_count": 0,
                "degen_call_count": 0,
                "top_rat_trader_percentage": 0.0,
                "created_at": "2024-03-02T00:00:00",
                "updated_at": "2024-03-02T00:00:00"
            }
        } 