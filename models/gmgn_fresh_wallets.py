from typing import Dict, List, Any, Optional
from beanie import Document, Indexed
from pydantic import Field


class GmgnFreshWallets(Document):
    """GMGN 新钱包模型
    
    用于存储代币的新钱包信息，包括：
    - 基本信息（链、地址、账户地址）
    - 交易统计（买入卖出金额、数量等）
    - 钱包标签
    - 用户信息
    - 利润统计
    """
    
    # 基本信息
    chain: str = Field(description="链名称")
    address: Indexed(str) = Field(description="钱包地址")
    token_address: Indexed(str) = Field(description="代币地址")
    account_address: str = Field(description="账户地址")
    addr_type: int = Field(default=0, description="地址类型")
    
    # 当前持仓
    amount_cur: float = Field(default=0, description="当前持仓数量")
    usd_value: float = Field(default=0, description="当前持仓USD价值")
    cost_cur: float = Field(default=0, description="当前持仓成本")
    
    # 交易统计
    sell_amount_cur: float = Field(default=0, description="卖出数量")
    sell_amount_percentage: float = Field(default=0, description="卖出比例")
    sell_volume_cur: float = Field(default=0, description="卖出成交额")
    buy_volume_cur: float = Field(default=0, description="买入成交额")
    buy_amount_cur: float = Field(default=0, description="买入数量")
    netflow_usd: float = Field(default=0, description="净流入USD")
    netflow_amount: float = Field(default=0, description="净流入数量")
    buy_tx_count_cur: int = Field(default=0, description="买入交易次数")
    sell_tx_count_cur: int = Field(default=0, description="卖出交易次数")
    
    # 钱包标签
    wallet_tag_v2: str = Field(default="", description="钱包标签")
    eth_balance: str = Field(default="0", description="ETH余额")
    sol_balance: str = Field(default="0", description="SOL余额")
    trx_balance: str = Field(default="0", description="TRX余额")
    balance: str = Field(default="0", description="总余额")
    
    # 利润统计
    profit: float = Field(default=0, description="总利润")
    realized_profit: float = Field(default=0, description="已实现利润")
    profit_change: float = Field(default=0, description="利润变化")
    amount_percentage: float = Field(default=0, description="持仓比例")
    unrealized_profit: float = Field(default=0, description="未实现利润")
    unrealized_pnl: Optional[float] = Field(default=None, description="未实现盈亏")
    avg_cost: float = Field(default=0, description="平均成本")
    avg_sold: float = Field(default=0, description="平均卖出价格")
    
    # 标签信息
    tags: List[str] = Field(default_factory=list, description="标签列表")
    maker_token_tags: List[str] = Field(default_factory=list, description="做市商标签")
    tag_rank: Dict[str, Optional[int]] = Field(default_factory=dict, description="标签排名")
    
    # 用户信息
    name: Optional[str] = Field(default=None, description="用户名")
    avatar: Optional[str] = Field(default=None, description="头像URL")
    twitter_username: Optional[str] = Field(default=None, description="Twitter用户名")
    twitter_name: Optional[str] = Field(default=None, description="Twitter显示名")
    
    # 活动信息
    last_active_timestamp: int = Field(default=0, description="最后活动时间戳")
    created_at: int = Field(default=0, description="创建时间戳")
    
    # 累计统计
    accu_amount: float = Field(default=0, description="累计数量")
    accu_cost: float = Field(default=0, description="累计成本")
    cost: float = Field(default=0, description="成本")
    total_cost: float = Field(default=0, description="总成本")
    
    # 转账信息
    transfer_in: bool = Field(default=False, description="是否转入")
    is_new: bool = Field(default=False, description="是否新用户")
    is_suspicious: bool = Field(default=False, description="是否可疑")
    
    # 持仓时间
    start_holding_at: Optional[int] = Field(default=None, description="开始持仓时间")
    end_holding_at: Optional[int] = Field(default=None, description="结束持仓时间")
    
    # 原生转账信息
    native_transfer: Dict[str, Any] = Field(default_factory=dict, description="原生转账信息")
    
    # 数据库配置
    class Settings:
        name = "gmgn_fresh_wallets"
        use_state_management = True
    
    @classmethod
    async def from_api_data(cls, chain: str, data: Dict[str, Any]) -> 'GmgnFreshWallets':
        """从API数据创建模型实例
        
        Args:
            chain: 链名称
            data: API返回的数据
            
        Returns:
            GmgnFreshWallets: 模型实例
        """
        return cls(
            chain=chain,
            address=data.get('address', ''),
            token_address=data.get('token_address', ''),
            account_address=data.get('account_address', ''),
            addr_type=data.get('addr_type', 0),
            amount_cur=float(data.get('amount_cur', 0)),
            usd_value=float(data.get('usd_value', 0)),
            cost_cur=float(data.get('cost_cur', 0)),
            sell_amount_cur=float(data.get('sell_amount_cur', 0)),
            sell_amount_percentage=float(data.get('sell_amount_percentage', 0)),
            sell_volume_cur=float(data.get('sell_volume_cur', 0)),
            buy_volume_cur=float(data.get('buy_volume_cur', 0)),
            buy_amount_cur=float(data.get('buy_amount_cur', 0)),
            netflow_usd=float(data.get('netflow_usd', 0)),
            netflow_amount=float(data.get('netflow_amount', 0)),
            buy_tx_count_cur=int(data.get('buy_tx_count_cur', 0)),
            sell_tx_count_cur=int(data.get('sell_tx_count_cur', 0)),
            wallet_tag_v2=data.get('wallet_tag_v2', ''),
            eth_balance=str(data.get('eth_balance', '0')),
            sol_balance=str(data.get('sol_balance', '0')),
            trx_balance=str(data.get('trx_balance', '0')),
            balance=str(data.get('balance', '0')),
            profit=float(data.get('profit', 0)),
            realized_profit=float(data.get('realized_profit', 0)),
            profit_change=float(data.get('profit_change', 0)),
            amount_percentage=float(data.get('amount_percentage', 0)),
            unrealized_profit=float(data.get('unrealized_profit', 0)),
            unrealized_pnl=data.get('unrealized_pnl'),
            avg_cost=float(data.get('avg_cost', 0)),
            avg_sold=float(data.get('avg_sold', 0)),
            tags=data.get('tags', []),
            maker_token_tags=data.get('maker_token_tags', []),
            tag_rank=data.get('tag_rank', {}),
            name=data.get('name'),
            avatar=data.get('avatar'),
            twitter_username=data.get('twitter_username'),
            twitter_name=data.get('twitter_name'),
            last_active_timestamp=int(data.get('last_active_timestamp', 0)),
            created_at=int(data.get('created_at', 0)),
            accu_amount=float(data.get('accu_amount', 0)),
            accu_cost=float(data.get('accu_cost', 0)),
            cost=float(data.get('cost', 0)),
            total_cost=float(data.get('total_cost', 0)),
            transfer_in=bool(data.get('transfer_in', False)),
            is_new=bool(data.get('is_new', False)),
            is_suspicious=bool(data.get('is_suspicious', False)),
            start_holding_at=data.get('start_holding_at'),
            end_holding_at=data.get('end_holding_at'),
            native_transfer=data.get('native_transfer', {})
        )
    
    class Config:
        json_schema_extra = {
            "example": {
                "chain": "sol",
                "address": "8P3RQAWUv78wQVWt8kKWMgN6MLBp1Xu1oy8SsJGGW3J8",
                "token_address": "7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU",
                "account_address": "8jDcTunwBvAWdzmvxLJ6p1cWtKbQYmWH9WMafktMY9mu",
                "addr_type": 0,
                "amount_cur": 0,
                "usd_value": 0,
                "cost_cur": 0,
                "sell_amount_cur": *********.127715,
                "sell_amount_percentage": 1,
                "sell_volume_cur": 101844.**********,
                "buy_volume_cur": 22568.***********,
                "buy_amount_cur": *********.578362,
                "netflow_usd": -79276.***********,
                "netflow_amount": 1545929.**********,
                "buy_tx_count_cur": 5,
                "sell_tx_count_cur": 336,
                "wallet_tag_v2": "TOP62",
                "eth_balance": "***********",
                "sol_balance": "***********",
                "trx_balance": "0",
                "balance": "***********",
                "profit": 81044.***********,
                "realized_profit": 81044.***********,
                "profit_change": 3.****************,
                "amount_percentage": 0,
                "unrealized_profit": 0,
                "unrealized_pnl": None,
                "avg_cost": 0.00016430736165016767,
                "avg_sold": 0.0007499086815350033,
                "tags": ["sandwich_bot", "fresh_wallet"],
                "maker_token_tags": ["whale"],
                "tag_rank": {"sandwich_bot": None, "fresh_wallet": None},
                "name": None,
                "avatar": None,
                "twitter_username": None,
                "twitter_name": None,
                "last_active_timestamp": 1741705708,
                "created_at": 1741279610,
                "accu_amount": 0,
                "accu_cost": 0,
                "cost": 0,
                "total_cost": 20799.90182058067,
                "transfer_in": False,
                "is_new": False,
                "is_suspicious": False,
                "start_holding_at": 1741688043,
                "end_holding_at": 1741705708,
                "native_transfer": {
                    "name": None,
                    "from_address": "EE9neSAA66B1H9coKZxkUSFQS18RtEVgksaqabX3hQ6r",
                    "timestamp": 1741370416
                }
            }
        } 