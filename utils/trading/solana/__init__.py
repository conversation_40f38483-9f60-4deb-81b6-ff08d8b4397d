"""
Solana trading utilities and services

This package contains Solana-specific trading interfaces and implementations.
"""

from .trade_interface import TradeInterface, TradeResult, TradeStatus, TradeType
from .gmgn_trade_service import GmgnTradeService
from .gmgn_trade_service_v2 import GmgnTradeServiceV2

# 添加条件导入，如果 jupiter_trade_service 存在的话
try:
    from .jupiter_trade_service import JupiterTradeService, create_jupiter_trade_service
    _has_jupiter = True
except ImportError:
    _has_jupiter = False
    JupiterTradeService = None
    create_jupiter_trade_service = None

__all__ = [
    'TradeInterface',
    'TradeResult', 
    'TradeStatus',
    'TradeType',
    'GmgnTradeService',
    'GmgnTradeServiceV2',
]

# 只有在模块存在时才添加到 __all__
if _has_jupiter:
    __all__.extend([
        'JupiterTradeService',
        'create_jupiter_trade_service'
    ]) 