import asyncio
import logging
import os
import json
from typing import Dict, Any, List, Optional
from datetime import datetime
import argparse
import pandas as pd
import matplotlib.pyplot as plt
from dotenv import load_dotenv

from utils.backtest.config_manager import Config<PERSON>anager, BacktestConfig
from utils.backtest.data_loader import DataLoader
from utils.backtest.signal_generator import SignalGenerator
from utils.backtest.price_calculator import PriceCalculator
from utils.backtest.result_analyzer import ResultAnalyzer
from utils.strategies.kol_buy_strategy import KOLBuyStrategy
from utils.strategies.kol_sell_strategy import KOLSellStrategy
from models import init_db

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('backtest.log')
    ]
)

logger = logging.getLogger("BacktestMain")


class Backtest:
    """回测系统主类"""
    
    def __init__(self, config: BacktestConfig):
        """初始化回测
        
        Args:
            config: 回测配置
        """
        self.config = config
        self.result_dir = f"backtest_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.data_loader = None
        self.signal_generator = None
        self.price_calculator = None
        self.result_analyzer = None
        self.wallets = []
        self.buy_signals = []
        self.sell_signals = []
        self.trade_results = []
        self.statistics = {}
        
    async def setup(self):
        """初始化回测环境"""
        logger.info("创建结果目录...")
        os.makedirs(self.result_dir, exist_ok=True)
        
        logger.info("初始化数据库连接...")
        await init_db()
        
        logger.info("初始化组件...")
        self.data_loader = DataLoader()
        
        buy_strategy = KOLBuyStrategy(self.config.__dict__)
        sell_strategy = KOLSellStrategy(self.config.__dict__)
        self.signal_generator = SignalGenerator(buy_strategy, sell_strategy)
        
        self.price_calculator = PriceCalculator()
        await self.price_calculator.setup()
        
        self.result_analyzer = ResultAnalyzer(self.config.__dict__)
        
    async def prepare_time_range(self):
        """准备回测时间范围"""
        self.start_time = self.config.backtest_start_time
        self.end_time = self.config.backtest_end_time
        
        if not self.start_time or not self.end_time:
            # 如果未指定时间范围，使用最近30天
            current_time = int(datetime.now().timestamp())
            if not self.end_time:
                self.end_time = current_time
            if not self.start_time:
                self.start_time = self.end_time - 30 * 24 * 3600  # 30天前
        
        logger.info(f"回测时间范围: {datetime.fromtimestamp(self.start_time)} 到 {datetime.fromtimestamp(self.end_time)}")
    
    async def load_global_data(self):
        """加载全局数据"""
        logger.info("加载KOL钱包数据...")
        self.wallets = await self.data_loader.load_kol_wallets()
        logger.info(f"加载了 {len(self.wallets)} 个KOL钱包")
        
    async def collect_activities_for_time_slice(self, time_slice, lookback_hours):
        """收集当前时间片的活动数据
        
        Args:
            time_slice: 时间片
            lookback_hours: 回溯小时数
            
        Returns:
            Tuple[List, Set]: 活动列表和代币地址集合
        """
        all_activities = []
        token_addresses = set()
        
        # 使用流式API加载活动数据
        async for activities_batch in self.data_loader.stream_activities_by_window(
            time_slice, 
            lookback_hours=lookback_hours,
            batch_size=100
        ):
            # 收集本批次活动数据
            all_activities.extend(activities_batch)
            
            # 收集代币地址
            for activity in activities_batch:
                if activity.get('token', {}).get('address', ''):
                    token_addresses.add(activity.get('token', {}).get('address', ''))
                    
        logger.info(f"共收集到 {len(all_activities)} 条活动记录, {len(token_addresses)} 个代币地址")
        return all_activities, token_addresses
        
    async def generate_buy_signals(self):
        """生成买入信号"""
        logger.info("=== 生成买入信号 ===")
        buy_step_size = self.config.buy_step_size
        logger.info(f"使用买入信号滑动窗口步长: {buy_step_size} 小时")
        
        time_slice_count = 0
        async for time_slice in self.data_loader.stream_time_slices(self.start_time, self.end_time, buy_step_size):
            time_slice_count += 1
            current_time = time_slice['current_time']
            logger.info(f"处理买入信号时间点: {datetime.fromtimestamp(current_time)}")
            
            # 确定回溯窗口大小
            lookback_hours = self.config.transaction_lookback_hours
            
            # 如果有token_mint_lookback_hours，也需要考虑这个窗口
            if hasattr(self.config, 'token_mint_lookback_hours') and self.config.token_mint_lookback_hours > lookback_hours:
                lookback_hours = self.config.token_mint_lookback_hours
            
            # 添加缓冲区，确保数据足够
            lookback_hours = int(lookback_hours * 1.5)
            logger.info(f"使用买入数据回溯窗口: {lookback_hours} 小时")
            
            # 收集当前时间片的所有活动和代币地址
            all_activities, token_addresses = await self.collect_activities_for_time_slice(time_slice, lookback_hours)
            
            if not all_activities:
                logger.warning(f"时间点 {datetime.fromtimestamp(current_time)} 没有活动数据，跳过买入信号生成")
                continue
            
            # 加载本时间片需要的代币信息
            tokens = await self.data_loader.load_tokens(list(token_addresses))
            
            if not tokens:
                logger.warning(f"时间点 {datetime.fromtimestamp(current_time)} 没有代币数据，跳过买入信号生成")
                continue
            
            # 生成买入信号
            buy_signals = await self.signal_generator.generate_buy_signals(
                current_time, all_activities, tokens, self.wallets
            )
            self.buy_signals.extend(buy_signals)
            
            # 每处理5个时间片计算一次中间结果并输出进度
            if len(self.buy_signals) > 0 and time_slice_count % 5 == 0:
                logger.info(f"买入信号生成进度: 已处理 {time_slice_count} 个时间片, 当前买入信号: {len(self.buy_signals)}")
                
                # 定期执行垃圾回收，释放内存
                if time_slice_count % 20 == 0:
                    import gc
                    gc.collect()
                    logger.info("执行垃圾回收，释放内存")
        
        logger.info(f"买入信号生成完成，共 {len(self.buy_signals)} 个信号")
    
    async def generate_sell_signals(self):
        """生成卖出信号"""
        logger.info("=== 生成卖出信号 ===")
        sell_step_size = self.config.sell_step_size
        logger.info(f"使用卖出信号滑动窗口步长: {sell_step_size} 小时")
        
        # 确保有可用的买入信号
        if not self.buy_signals:
            logger.warning("没有买入信号可用，跳过卖出信号生成")
            return
        
        time_slice_count = 0
        async for time_slice in self.data_loader.stream_time_slices(self.start_time, self.end_time, sell_step_size):
            time_slice_count += 1
            current_time = time_slice['current_time']
            logger.info(f"处理卖出信号时间点: {datetime.fromtimestamp(current_time)}")
            
            # 卖出策略窗口作为回溯时间
            lookback_hours = self.config.sell_strategy_hours
            
            # 添加缓冲区，确保数据足够
            lookback_hours = int(lookback_hours * 1.5)
            logger.info(f"使用卖出数据回溯窗口: {lookback_hours} 小时")
            
            # 收集当前时间片的所有活动和代币地址
            all_activities, token_addresses = await self.collect_activities_for_time_slice(time_slice, lookback_hours)
            
            if not all_activities:
                logger.warning(f"时间点 {datetime.fromtimestamp(current_time)} 没有活动数据，跳过卖出信号生成")
                continue
            
            # 加载本时间片需要的代币信息
            tokens = await self.data_loader.load_tokens(list(token_addresses))
            
            if not tokens:
                logger.warning(f"时间点 {datetime.fromtimestamp(current_time)} 没有代币数据，跳过卖出信号生成")
                continue
            
            # 生成卖出信号
            sell_signals = await self.signal_generator.generate_sell_signals(
                current_time, all_activities, tokens, self.wallets
            )
            self.sell_signals.extend(sell_signals)
            
            # 每处理5个时间片计算一次中间结果并输出进度
            if time_slice_count % 5 == 0:
                logger.info(f"卖出信号生成进度: 已处理 {time_slice_count} 个时间片, 当前卖出信号: {len(self.sell_signals)}")
                
                # 定期执行垃圾回收，释放内存
                if time_slice_count % 20 == 0:
                    import gc
                    gc.collect()
                    logger.info("执行垃圾回收，释放内存")
        
        logger.info(f"卖出信号生成完成，共 {len(self.sell_signals)} 个信号")
    
    async def calculate_trade_results(self):
        """计算交易结果"""
        logger.info("开始计算交易结果...")
        
        batch_count = 0
        
        # 流式计算回报率
        async for trade_batch in self.price_calculator.stream_calculate_returns(
            self.buy_signals, self.sell_signals, batch_size=50
        ):
            batch_count += 1
            self.trade_results.extend(trade_batch)
            logger.info(f"处理了第 {batch_count} 批交易结果，当前共有 {len(self.trade_results)} 个结果")
            
            # 每处理10批计算一次中间统计数据
            if batch_count % 10 == 0:
                interim_stats = self.result_analyzer.calculate_statistics(self.trade_results)
                logger.info(f"中间统计结果 - 当前胜率: {interim_stats['win_rate']:.2%}, 平均收益率: {interim_stats['average_return']:.2%}")
        
        logger.info(f"交易结果计算完成，共 {len(self.trade_results)} 个交易结果")
    
    async def analyze_results(self):
        """分析交易结果"""
        logger.info("分析交易结果...")
        self.statistics = self.result_analyzer.calculate_statistics(self.trade_results)
        
        # 导出结果
        self.result_analyzer.export_to_json(
            self.trade_results, self.statistics, f"{self.result_dir}/results.json"
        )
        self.result_analyzer.export_to_csv(
            self.trade_results, f"{self.result_dir}/trades.csv"
        )
        self.result_analyzer.plot_returns(
            self.trade_results, f"{self.result_dir}/returns.png"
        )
        
        # 输出统计数据
        logger.info(f"回测完成，统计数据：")
        logger.info(f"总交易数: {self.statistics['total_trades']}")
        logger.info(f"盈利交易数: {self.statistics['winning_trades']}")
        logger.info(f"亏损交易数: {self.statistics['losing_trades']}")
        logger.info(f"胜率: {self.statistics['win_rate']:.2%}")
        logger.info(f"平均收益率: {self.statistics['average_return']:.2%}")
        logger.info(f"累积收益率: {self.statistics['cumulative_return']:.2%}")
        logger.info(f"最大回撤: {self.statistics['max_drawdown']:.2%}")
    
    async def run(self):
        """运行回测流程"""
        logger.info(f"开始回测，配置：{self.config}")
        
        # 初始化环境
        await self.setup()
        
        # 准备时间范围
        await self.prepare_time_range()
        
        # 加载全局数据
        await self.load_global_data()
        
        # 生成买入信号
        await self.generate_buy_signals()
        
        # 生成卖出信号
        await self.generate_sell_signals()
        
        # 计算交易结果
        await self.calculate_trade_results()
        
        # 分析结果
        await self.analyze_results()
        
        return {
            "config": self.config.__dict__,
            "statistics": self.statistics,
            "buy_signals": len(self.buy_signals),
            "sell_signals": len(self.sell_signals),
            "trade_results": len(self.trade_results)
        }


class ParameterGridSearch:
    """参数网格搜索类"""
    
    def __init__(self, param_grid: Dict[str, List]):
        """初始化参数网格搜索
        
        Args:
            param_grid: 参数网格
        """
        self.param_grid = param_grid
        self.results = []
        self.parameter_combinations = []
        self.result_dir = f"param_search_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
    async def generate_parameter_combinations(self):
        """生成参数组合"""
        logger.info(f"开始参数网格搜索，参数网格: {self.param_grid}")
        self.parameter_combinations = ConfigManager.generate_parameter_combinations(self.param_grid)
        logger.info(f"生成了 {len(self.parameter_combinations)} 个参数组合")
        
    async def run_all_combinations(self):
        """运行所有参数组合"""
        # 确保结果目录存在
        os.makedirs(self.result_dir, exist_ok=True)
        
        # 逐一运行每组参数
        for i, params in enumerate(self.parameter_combinations):
            logger.info(f"运行参数组合 {i+1}/{len(self.parameter_combinations)}: {params}")
            
            # 创建配置
            config = BacktestConfig(**params)
            
            # 运行回测
            backtest = Backtest(config)
            result = await backtest.run()
            
            # 存储结果
            self.results.append({
                "params": params,
                "win_rate": result["statistics"]["win_rate"],
                "average_return": result["statistics"]["average_return"],
                "cumulative_return": result["statistics"]["cumulative_return"],
                "max_drawdown": result["statistics"]["max_drawdown"],
                "total_trades": result["statistics"]["total_trades"]
            })
            
    async def analyze_results(self):
        """分析参数搜索结果"""
        # 转换为DataFrame进行分析
        df = pd.DataFrame(self.results)
        
        # 保存参数搜索结果
        df.to_csv(f"{self.result_dir}/param_search_results.csv", index=False)
        
        # 找出最佳参数组合
        best_by_win_rate = df.loc[df['win_rate'].idxmax()]
        best_by_return = df.loc[df['cumulative_return'].idxmax()]
        
        logger.info("参数搜索完成")
        logger.info(f"最佳胜率参数组合: {best_by_win_rate['params']}, 胜率: {best_by_win_rate['win_rate']:.2%}")
        logger.info(f"最佳收益参数组合: {best_by_return['params']}, 累积收益: {best_by_return['cumulative_return']:.2%}")
        
        # 绘制参数影响图表
        if len(self.parameter_combinations) > 1:
            for param_name in self.param_grid.keys():
                if len(self.param_grid[param_name]) > 1:
                    self.plot_parameter_effect(df, param_name)
        
        return {
            "best_by_win_rate": best_by_win_rate['params'],
            "best_by_return": best_by_return['params'],
            "all_results": df.to_dict('records')
        }
    
    def plot_parameter_effect(self, df, param_name):
        """绘制参数效果图
        
        Args:
            df: 结果DataFrame
            param_name: 参数名称
        """
        plt.figure(figsize=(10, 6))
        
        # 绘制参数与胜率的关系
        plt.subplot(2, 1, 1)
        param_win_rate = df.groupby(lambda x: df.iloc[x]['params'][param_name])['win_rate'].mean()
        param_win_rate.plot(marker='o')
        plt.title(f'{param_name} vs Win Rate')
        plt.ylabel('Win Rate')
        plt.grid(True, alpha=0.3)
        
        # 绘制参数与累积收益的关系
        plt.subplot(2, 1, 2)
        param_return = df.groupby(lambda x: df.iloc[x]['params'][param_name])['cumulative_return'].mean()
        param_return.plot(marker='o')
        plt.title(f'{param_name} vs Cumulative Return')
        plt.ylabel('Cumulative Return')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f"{self.result_dir}/{param_name}_analysis.png")
        plt.close()
        
    async def run(self):
        """运行参数网格搜索"""
        await self.generate_parameter_combinations()
        await self.run_all_combinations()
        return await self.analyze_results()


async def run_backtest(config: BacktestConfig):
    """运行回测
    
    Args:
        config: 回测配置
    
    Returns:
        Dict[str, Any]: 回测结果
    """
    backtest = Backtest(config)
    return await backtest.run()


async def run_parameter_grid(param_grid: Dict[str, List]):
    """运行参数网格搜索
    
    Args:
        param_grid: 参数网格
    """
    grid_search = ParameterGridSearch(param_grid)
    return await grid_search.run()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='运行KOL交易策略回测')
    parser.add_argument('--mode', choices=['single', 'grid'], default='single',
                        help='回测模式: single-单次回测, grid-参数网格搜索')
    parser.add_argument('--config', type=str, help='配置文件路径')
    parser.add_argument('--param_grid', type=str, help='参数网格文件路径(JSON)')
    
    args = parser.parse_args()
    
    if args.mode == 'single':
        # 单次回测模式
        if args.config:
            config = ConfigManager.load_from_file(args.config)
        else:
            config = ConfigManager.load_from_args()
            
        asyncio.run(run_backtest(config))
    
    elif args.mode == 'grid':
        # 参数网格搜索模式
        if args.param_grid:
            with open(args.param_grid, 'r') as f:
                param_grid = json.load(f)
        else:
            # 默认参数网格
            param_grid = {
                'transaction_lookback_hours': [12, 24, 48],
                'transaction_min_amount': [500, 1000, 2000],
                'kol_account_min_count': [2, 3, 5],
                'token_mint_lookback_hours': [24, 48, 72],
                'sell_strategy_hours': [12, 24, 48],
                'sell_kol_ratio': [0.3, 0.5, 0.7],
                'buy_step_size': [6, 12, 24],
                'sell_step_size': [6, 12, 24]
            }
            
        asyncio.run(run_parameter_grid(param_grid)) 