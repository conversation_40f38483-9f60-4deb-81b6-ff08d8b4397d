# 整体收益率指标增强功能技术实现方案

## 1. 方案概述

### 1.1 实现目标
在交易统计系统中新增"整体收益率"指标，计算公式为：总盈利金额 / 总投入金额 × 100%

### 1.2 实现策略
- **最小侵入式设计**: 在现有架构基础上新增字段和计算逻辑
- **向后兼容**: 确保现有功能不受影响
- **完整覆盖**: 在所有相关输出中都包含新指标

### 1.3 影响范围
1. 数据模型层：`utils/trading/statistics/models.py`
2. 计算逻辑层：`utils/trading/statistics/statistics_calculator.py`
3. 显示层：HTML模板、API输出、飞书消息
4. 测试层：新增单元测试和集成测试

## 2. 详细实现方案

### 2.1 数据模型修改

#### 2.1.1 OverallStats模型增强
**文件**: `utils/trading/statistics/models.py`

```python
class OverallStats(BaseModel):
    """总体统计模型"""
    total_trades: int = Field(default=0, description="总交易数")
    total_profit_rate: float = Field(default=0.0, description="总盈利率(%)")
    total_win_rate: float = Field(default=0.0, description="总胜率(%)")
    avg_profit_rate: float = Field(default=0.0, description="平均盈利率(%)")
    overall_return_rate: float = Field(default=0.0, description="整体收益率(%)")  # 新增字段
    total_profit_amount: float = Field(default=0.0, description="总盈亏金额（SOL）")
    profitable_trades: int = Field(default=0, description="盈利交易数")
    loss_trades: int = Field(default=0, description="亏损交易数")
    avg_holding_duration: float = Field(default=0.0, description="平均持仓时长（小时）")
    total_buy_amount: float = Field(default=0.0, description="总买入金额（SOL）")
    total_sell_amount: float = Field(default=0.0, description="总卖出金额（SOL）")
    max_single_profit: float = Field(default=0.0, description="最大单笔盈利（SOL）")
    max_single_loss: float = Field(default=0.0, description="最大单笔亏损（SOL）")
```

### 2.2 计算逻辑实现

#### 2.2.1 StatisticsCalculator修改
**文件**: `utils/trading/statistics/statistics_calculator.py`

在`_calculate_overall_stats`方法中新增计算逻辑：

```python
def _calculate_overall_stats(self, trade_pairs: List[TradePair]) -> OverallStats:
    """
    计算总体统计（增加整体收益率）
    """
    try:
        # ... 现有计算逻辑 ...
        
        # 计算总买入和卖出金额
        total_buy_amount = sum(pair.buy_amount_sol for pair in trade_pairs)
        total_sell_amount = sum(pair.sell_amount_sol for pair in trade_pairs)
        
        # 新增：计算整体收益率
        overall_return_rate = (total_profit_amount / total_buy_amount * 100.0) if total_buy_amount > 0 else 0.0
        
        # ... 现有其他计算 ...
        
        return OverallStats(
            total_trades=total_trades,
            total_win_rate=win_rate,
            total_profit_rate=total_profit_rate,
            avg_profit_rate=avg_profit_rate,
            overall_return_rate=overall_return_rate,  # 新增字段
            total_profit_amount=total_profit_amount,
            profitable_trades=profitable_count,
            loss_trades=loss_count,
            avg_holding_duration=avg_holding_duration,
            total_buy_amount=total_buy_amount,
            total_sell_amount=total_sell_amount,
            max_single_profit=max_single_profit,
            max_single_loss=max_single_loss
        )
```

### 2.3 HTML报告显示

#### 2.3.1 HTML模板修改
需要查找并修改HTML模板文件，在总体统计卡片区域新增整体收益率显示：

```html
<!-- 总体统计卡片区域新增 -->
<div class="stat-card">
    <div class="stat-value">{{ "%.2f"|format(stats.overall_stats.overall_return_rate) }}%</div>
    <div class="stat-label">整体收益率</div>
</div>
```

### 2.4 图表功能增强

#### 2.4.1 ChartGenerator修改
**文件**: `utils/trading/statistics/chart_generator.py`

在`generate_overall_bar_chart`方法中新增整体收益率：

```python
def generate_overall_bar_chart(self, overall_stats: OverallStats) -> str:
    """生成总体统计柱状图（增加整体收益率）"""
    metrics = ['胜率 (%)', '总盈利率 (%)', '平均盈利率 (%)', '整体收益率 (%)']  # 新增
    values = [
        overall_stats.total_win_rate,
        overall_stats.total_profit_rate,
        overall_stats.avg_profit_rate,
        overall_stats.overall_return_rate  # 新增
    ]
    # ... 其余图表生成逻辑保持不变
```

### 2.5 API接口修改

#### 2.5.1 Trade Statistics Analyzer修改
**文件**: `utils/trading/statistics/trade_statistics_analyzer.py`

在`get_statistics_summary`方法中新增字段：

```python
summary = {
    "total_trades": statistics.overall_stats.total_trades if statistics.overall_stats else 0,
    "total_win_rate": round(statistics.overall_stats.total_win_rate, 2) if statistics.overall_stats else 0,
    "total_profit_rate": round(statistics.overall_stats.total_profit_rate, 2) if statistics.overall_stats else 0,
    "avg_profit_rate": round(statistics.overall_stats.avg_profit_rate, 2) if statistics.overall_stats else 0,
    "overall_return_rate": round(statistics.overall_stats.overall_return_rate, 2) if statistics.overall_stats else 0,  # 新增
    "total_profit_amount": round(statistics.overall_stats.total_profit_amount, 4) if statistics.overall_stats else 0,
    # ... 其余字段
}
```

#### 2.5.2 API模块修改
**文件**: `utils/trading/statistics/api.py`

在`get_statistics_summary`方法的返回结果中新增字段：

```python
"overall_stats": {
    "total_trades": result.overall_stats.total_trades if result.overall_stats else 0,
    "win_rate": round(result.overall_stats.total_win_rate, 2) if result.overall_stats else 0,
    "total_profit_rate": round(result.overall_stats.total_profit_rate, 2) if result.overall_stats else 0,
    "avg_profit_rate": round(result.overall_stats.avg_profit_rate, 2) if result.overall_stats else 0,
    "overall_return_rate": round(result.overall_stats.overall_return_rate, 2) if result.overall_stats else 0,  # 新增
    "total_profit_amount": round(result.overall_stats.total_profit_amount, 4) if result.overall_stats else 0,
    # ... 其余字段
}
```

### 2.6 飞书消息增强

#### 2.6.1 Summary Generator修改
**文件**: `utils/trading/statistics/summary_generator.py`

在`_extract_overall_stats`方法中新增字段：

```python
def _extract_overall_stats(self, overall_stats: Dict[str, Any]) -> Dict[str, Any]:
    """提取总体统计数据"""
    return {
        "total_trades": overall_stats.get("total_trades", 0),
        "win_rate": round(overall_stats.get("total_win_rate", 0), 2),
        "total_pnl_rate": round(overall_stats.get("total_profit_rate", 0), 2),
        "avg_pnl_rate": round(overall_stats.get("avg_profit_rate", 0), 2),
        "overall_return_rate": round(overall_stats.get("overall_return_rate", 0), 2),  # 新增
        "total_pnl_amount": round(overall_stats.get("total_profit_amount", 0), 4),
        "profitable_trades": overall_stats.get("profitable_trades", 0),
        "loss_trades": overall_stats.get("loss_trades", 0)
    }
```

#### 2.6.2 飞书消息格式增强
在`SummaryFormatter.format_as_text`方法中新增显示：

```python
lines.append("📊 总体统计:")
lines.append(f"  • 总交易次数: {overall.get('total_trades', 0)}")
lines.append(f"  • 总胜率: {overall.get('win_rate', 0):.2f}%")
lines.append(f"  • 总盈利率: {overall.get('total_pnl_rate', 0):.2f}%")
lines.append(f"  • 平均盈利率: {overall.get('avg_pnl_rate', 0):.2f}%")
lines.append(f"  • 整体收益率: {overall.get('overall_return_rate', 0):.2f}%")  # 新增
```

### 2.7 测试用例实现

#### 2.7.1 单元测试
**文件**: `test/test_overall_return_rate.py`

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
整体收益率指标测试用例
"""

import unittest
from datetime import datetime

from utils.trading.statistics.models import TradePair, OverallStats
from utils.trading.statistics.statistics_calculator import StatisticsCalculator


class TestOverallReturnRate(unittest.TestCase):
    """整体收益率指标测试"""
    
    def setUp(self):
        """测试准备"""
        self.calculator = StatisticsCalculator()
    
    def test_normal_case(self):
        """测试正常情况"""
        trade_pairs = [
            TradePair(
                signal_id="test1",
                strategy_name="test_strategy",
                token_address="token1",
                buy_record_id="buy1",
                sell_record_id="sell1",
                buy_amount_sol=10.0,
                sell_amount_sol=12.0,
                profit_rate=20.0,
                profit_amount=2.0,
                is_profitable=True,
                buy_time=datetime.now(),
                sell_time=datetime.now(),
                holding_duration=1.0
            ),
            TradePair(
                signal_id="test2",
                strategy_name="test_strategy",
                token_address="token2",
                buy_record_id="buy2",
                sell_record_id="sell2",
                buy_amount_sol=5.0,
                sell_amount_sol=4.0,
                profit_rate=-20.0,
                profit_amount=-1.0,
                is_profitable=False,
                buy_time=datetime.now(),
                sell_time=datetime.now(),
                holding_duration=2.0
            )
        ]
        
        result = self.calculator._calculate_overall_stats(trade_pairs)
        
        # 验证计算结果
        self.assertEqual(result.total_buy_amount, 15.0)
        self.assertEqual(result.total_profit_amount, 1.0)
        self.assertAlmostEqual(result.overall_return_rate, 6.67, places=2)
    
    def test_zero_investment(self):
        """测试零投入情况"""
        trade_pairs = []
        result = self.calculator._calculate_overall_stats(trade_pairs)
        self.assertEqual(result.overall_return_rate, 0.0)
    
    def test_loss_case(self):
        """测试亏损情况"""
        trade_pairs = [
            TradePair(
                signal_id="test1",
                strategy_name="test_strategy",
                token_address="token1",
                buy_record_id="buy1",
                sell_record_id="sell1",
                buy_amount_sol=10.0,
                sell_amount_sol=8.0,
                profit_rate=-20.0,
                profit_amount=-2.0,
                is_profitable=False,
                buy_time=datetime.now(),
                sell_time=datetime.now(),
                holding_duration=1.0
            )
        ]
        
        result = self.calculator._calculate_overall_stats(trade_pairs)
        self.assertEqual(result.overall_return_rate, -20.0)


if __name__ == '__main__':
    unittest.main()
```

## 3. 实施步骤

### 第一阶段：核心功能实现
1. 修改OverallStats模型，新增`overall_return_rate`字段
2. 修改StatisticsCalculator，实现计算逻辑
3. 编写单元测试验证计算正确性

### 第二阶段：显示功能实现
1. 修改HTML模板，新增整体收益率显示
2. 修改图表生成器，在图表中包含新指标
3. 修改API接口，返回新字段

### 第三阶段：集成功能完善
1. 修改飞书消息，包含整体收益率
2. 完善所有相关API接口
3. 进行完整的集成测试

### 第四阶段：测试与验证
1. 执行全面的单元测试
2. 进行功能测试和集成测试
3. 验证所有输出格式正确

## 4. 风险评估与缓解

### 4.1 技术风险
- **风险**: 计算精度问题
- **缓解**: 使用适当的浮点精度，测试边界情况

### 4.2 兼容性风险
- **风险**: 破坏现有功能
- **缓解**: 向后兼容设计，充分测试

### 4.3 数据风险
- **风险**: 除数为零的异常
- **缓解**: 边界情况处理，返回0.0

## 5. 验证标准

### 5.1 功能正确性
- [ ] 计算公式实现正确
- [ ] 边界情况处理正确
- [ ] 所有显示位置都包含新指标

### 5.2 性能要求
- [ ] 计算性能无明显影响
- [ ] 渲染性能无明显影响

### 5.3 兼容性要求
- [ ] 现有功能完全正常
- [ ] API接口向后兼容
- [ ] 数据格式向后兼容

## 6. 预期收益

### 6.1 用户价值
- 提供更直观的投资回报指标
- 便于进行资金效率评估
- 支持更精准的策略分析

### 6.2 技术价值
- 增强统计分析功能的完整性
- 提高数据展示的价值
- 为后续功能扩展奠定基础 