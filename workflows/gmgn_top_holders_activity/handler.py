import logging
import traceback
from typing import Any, Dict, List, Optional

from dao.gmgn_top_holders_activity_dao import GmgnTopHoldersActivityDAO
from models.gmgn_top_holders_activity import GmgnTopHoldersActivity
from utils.spiders.smart_money.gmgn_token_trades_spider import GmgnTokenTradesSpider


logger = logging.getLogger("GmgnTopHoldersActivityHandler")
spider = GmgnTokenTradesSpider()
gmgn_top_holders_activity_dao = GmgnTopHoldersActivityDAO()


async def process_top_holders_activity(token_dict: Dict) -> Optional[List[Dict]]:
    """处理代币活动数据
    
    Args:
        token: 代币数据
        
    Returns:
        Optional[Dict]: 处理后的代币数据，如果处理失败则返回None
    """
    if not token_dict:
        logger.warning("收到空的代币数据")
        return None
    
    # 获取代币地址
    token_address = token_dict.get('address')
    if not token_address:
        logger.warning(f"代币数据缺少地址字段: {token_dict}")
        return None
    
    chain = "sol"
    
    last_activity = await gmgn_top_holders_activity_dao.get_last_activity(token_address)
    if isinstance(last_activity, GmgnTopHoldersActivity):
        last_timestamp = last_activity.timestamp
    elif isinstance(last_activity, dict):
        last_timestamp = last_activity["timestamp"]
    else:
        last_timestamp = None
        
    if last_timestamp:
        logger.info(f"获取最后一个活动记录: {last_timestamp}")
        activities = await spider.get_token_trades(chain, token_address, tag="top_holder", until_trade_timestamp=last_timestamp)
    else:
        activities = await spider.get_token_trades(chain, token_address, tag="top_holder")
    if not activities:
        logger.warning(f"没有活动记录: {token_address}")
        return None
    
    return activities

async def validate(data: Any) -> bool:
    """验证代币活动数据是否有效
    
    Args:
        data: 代币活动数据
        
    Returns:
        bool: 如果数据有效则返回True，否则返回False
    """
    return True


async def store_data(data: List[Dict]) -> int:
    """存储代币活动数据到数据库
    
    Args:
        data: 代币活动数据列表
        
    Returns:
        int: 成功存储的记录数
    """
    if not data:
        return 0
    
    activities = data if isinstance(data, list) else [data]
        
    try:
        update_count = await gmgn_top_holders_activity_dao.upsert_activity_many(activities)
        logger.info(f"批量更新完成: 更新了 {update_count} 条记录")
        return update_count
    except Exception as e:
        error_msg = f"存储代币活动数据时发生错误: {str(e)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        return False