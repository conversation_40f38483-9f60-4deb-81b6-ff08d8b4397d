# 卖出信号处理器功能单元测试

创建日期：2025-05-25
更新日期：2025-05-26
测试方法：自动化测试
测试级别：单元测试

## 测试概述
本测试套件验证卖出信号处理器的核心功能，包括信号生成、处理和新的AutoTradeManager架构集成。

## 测试架构说明
- **新架构集成**: 测试已迁移到使用AutoTradeManager进行统一的交易管理
- **配置管理**: 使用AutoTradeManager的config_manager进行自动交易启用状态检查
- **Mock策略**: 使用mock对象模拟所有外部依赖，包括DAO、AutoTradeManager和消息发送器
- **异步测试**: 所有测试都是异步的，使用unittest.IsolatedAsyncioTestCase作为基础类

## 测试用例

| 用例方法 | 用例概述 | 前置条件 | 输入 | 预期输出 | 实际输出 | 状态 |
| --- | --- | --- | --- | --- | --- | --- |
| test_generate_sell_signals_timeout_trigger | 测试信号生成逻辑（超时触发） | 配置正确加载，存在超过超时时间的开放买入信号 | 模拟的配置和信号数据 | 生成包含超时原因的卖出信号 | 生成正确的卖出信号数据 | 通过 |
| test_process_sell_signal_auto_sell_success_with_auto_trade_manager | 测试使用AutoTradeManager成功执行自动卖出 | 启用自动卖出，存在有效的买入记录 | 卖出信号数据，AutoTradeManager返回成功结果 | AutoTradeManager被调用，信号状态更新，发送成功通知 | 按预期执行，所有断言通过 | 通过 |
| test_process_sell_signal_auto_sell_disabled_skips_trade | 测试自动卖出禁用时跳过交易 | 自动卖出被禁用 | 卖出信号数据，自动卖出配置为false | 跳过AutoTradeManager调用，仍创建信号和发送通知 | 按预期跳过交易，其他流程正常 | 通过 |
| test_process_sell_signal_no_buy_trade_record_skips | 测试没有买入记录时跳过交易 | 启用自动卖出，但没有有效的买入交易记录 | 卖出信号数据，空的交易记录列表 | 跳过AutoTradeManager调用，仍完成信号处理 | 按预期跳过交易，信号处理完成 | 通过 |
| test_process_sell_signal_auto_trade_manager_exception | 测试AutoTradeManager抛出异常时的处理 | 启用自动卖出，AutoTradeManager抛出异常 | 卖出信号数据，模拟的异常 | 捕获异常，创建失败结果，继续完成信号处理 | 异常被正确处理，信号处理继续 | 通过 |
| test_process_sell_signal_with_balance_check_success | 测试使用钱包余额查询的新修复逻辑 - 成功场景 | 启用自动卖出，钱包有足够余额 | 卖出信号数据，模拟钱包余额查询成功 | 使用实际余额执行卖出，AutoTradeManager被调用 | 按预期使用余额查询结果，交易成功执行 | 通过 |
| test_process_sell_signal_insufficient_balance_skips | 测试钱包余额不足时跳过交易 | 启用自动卖出，钱包余额为0 | 卖出信号数据，模拟钱包余额为0 | 跳过交易，发送余额不足通知 | 按预期跳过交易，发送正确通知 | 通过 |
| test_process_sell_signal_balance_query_error | 测试余额查询失败时的错误处理 | 启用自动卖出，余额查询抛出异常 | 卖出信号数据，模拟RPC错误 | 跳过交易，发送错误通知 | 异常被正确捕获，发送错误通知 | 通过 |
| test_validate_success | 测试有效数据的验证逻辑 | 无 | 包含所有必需字段的有效数据字典 | 返回True | 返回True | 通过 |
| test_validate_fail_missing_key | 测试无效数据（缺少关键字段）的验证逻辑 | 无 | 缺少必需字段的数据字典 | 返回False | 返回False | 通过 |

## 新架构特性测试覆盖

### AutoTradeManager集成
- ✅ 成功调用AutoTradeManager执行卖出交易
- ✅ 正确传递钱包配置（私钥环境变量、钱包地址）
- ✅ 正确传递交易参数覆盖（滑点、优先费等）
- ✅ 正确计算UI层面的卖出数量（从lamports转换）
- ✅ 处理不同代币的小数位数（SOL=9位，SPL=6位默认）

### 异常处理
- ✅ AutoTradeManager执行异常的恢复处理
- ✅ 无买入记录时的优雅跳过
- ✅ 自动卖出禁用时的行为

### 通知系统
- ✅ 根据交易结果选择正确的消息模板
- ✅ 成功交易的详细信息展示（交易哈希、数量等）
- ✅ 失败交易的错误信息展示
- ✅ 跳过交易的状态通知

## Bug修复相关测试覆盖

### 余额查询修复 (2025-05-26)
针对"insufficient funds"错误的修复，新增了以下测试用例：
- ✅ **余额查询成功场景**: 验证使用实际钱包余额而非历史记录进行卖出
- ✅ **余额不足处理**: 验证当钱包余额为0时正确跳过交易并发送通知
- ✅ **余额查询异常处理**: 验证当RPC查询失败时的错误处理和通知机制

### 修复前后对比
- **修复前**: 依赖历史买入记录的`token_out_actual_amount`字段，可能导致数量不匹配
- **修复后**: 使用`SolanaMonitor.get_token_balance()`查询实际钱包余额，确保数据准确性

## 测试运行结果
- **总测试数**: 10
- **通过数**: 10  
- **失败数**: 0
- **跳过数**: 0
- **运行时间**: ~0.12秒

## 架构迁移成功确认
✅ 从旧的直接GMGN服务架构成功迁移到AutoTradeManager统一架构
✅ 保留了所有核心功能（信号生成、处理、通知）
✅ 增强了错误处理和配置灵活性  
✅ 测试覆盖率保持完整

## 注意事项
1. 测试使用大量Mock对象，确保单元测试的隔离性
2. 异步测试需要正确的事件循环管理
3. 新架构下的参数传递更加复杂，测试需要准确模拟各种配置场景
4. 所有外部依赖（DAO、消息发送器、AutoTradeManager）都被Mock，确保测试稳定性 