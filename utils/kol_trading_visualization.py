#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
KOL钱包交易活动可视化分析
生成胜率和收益率的可视化图表
"""

import json
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import numpy as np
from collections import defaultdict

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

def load_data(file_path):
    """加载JSON数据"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data

def analyze_token_performance(data):
    """分析每个token的交易表现（简化版）"""
    
    # 按token分组整理数据
    token_trades = defaultdict(list)
    
    for trade in data:
        token_address = trade['token']['address']
        token_symbol = trade['token']['symbol']
        
        trade_info = {
            'symbol': token_symbol,
            'address': token_address,
            'event_type': trade['event_type'],
            'cost_usd': float(trade['cost_usd']),
            'buy_cost_usd': float(trade['buy_cost_usd']) if trade['buy_cost_usd'] else None,
            'timestamp': trade['timestamp'],
            'date': datetime.fromtimestamp(trade['timestamp']),
            'is_open_or_close': trade['is_open_or_close'],
            'price_usd': float(trade['price_usd']),
            'token_amount': float(trade['token_amount'])
        }
        
        token_trades[token_address].append(trade_info)
    
    # 分析每个token的表现
    results = []
    
    for token_address, trades in token_trades.items():
        # 按时间排序
        trades.sort(key=lambda x: x['timestamp'])
        
        token_symbol = trades[0]['symbol']
        
        # 计算完成的交易对
        completed_trades = []
        
        for trade in trades:
            if trade['event_type'] == 'sell' and trade['buy_cost_usd'] is not None:
                profit_loss = trade['cost_usd'] - trade['buy_cost_usd']
                roi = (profit_loss / trade['buy_cost_usd']) * 100 if trade['buy_cost_usd'] > 0 else 0
                
                completed_trades.append({
                    'symbol': token_symbol,
                    'buy_cost': trade['buy_cost_usd'],
                    'sell_value': trade['cost_usd'],
                    'profit_loss': profit_loss,
                    'roi': roi,
                    'is_profitable': profit_loss > 0,
                    'sell_date': trade['date']
                })
        
        # 计算统计数据
        if completed_trades:
            total_trades = len(completed_trades)
            profitable_trades = sum(1 for t in completed_trades if t['is_profitable'])
            win_rate = (profitable_trades / total_trades) * 100
            
            total_profit_loss = sum(t['profit_loss'] for t in completed_trades)
            total_buy_cost = sum(t['buy_cost'] for t in completed_trades)
            average_roi = (total_profit_loss / total_buy_cost) * 100 if total_buy_cost > 0 else 0
            
            # 计算总收益率（总盈亏/总投资成本）
            total_return_rate = (total_profit_loss / total_buy_cost) * 100 if total_buy_cost > 0 else 0
            
            results.append({
                'token_symbol': token_symbol,
                'token_address': token_address,
                'total_trades': total_trades,
                'win_rate': win_rate,
                'total_profit_loss': total_profit_loss,
                'total_return_rate': total_return_rate,
                'average_roi': average_roi,
                'completed_trades': completed_trades
            })
    
    return results

def create_visualizations(results):
    """创建可视化图表"""
    
    # 准备数据
    symbols = []
    win_rates = []
    profit_losses = []
    total_return_rates = []
    average_rois = []
    trade_counts = []
    
    for result in results:
        if result['total_trades'] > 0:  # 只包含有完成交易的token
            symbols.append(result['token_symbol'])
            win_rates.append(result['win_rate'])
            profit_losses.append(result['total_profit_loss'])
            total_return_rates.append(result['total_return_rate'])
            average_rois.append(result['average_roi'])
            trade_counts.append(result['total_trades'])
    
    # 创建图表
    fig = plt.figure(figsize=(20, 15))
    fig.suptitle('KOL钱包交易表现分析', fontsize=16, fontweight='bold')
    
    # 创建2x3的子图布局
    ax1 = plt.subplot(2, 3, 1)
    ax2 = plt.subplot(2, 3, 2)
    ax3 = plt.subplot(2, 3, 3)
    ax4 = plt.subplot(2, 3, 4)
    ax5 = plt.subplot(2, 3, 5)
    ax6 = plt.subplot(2, 3, 6)
    
    # 1. 胜率分析（柱状图）
    colors = ['green' if wr >= 50 else 'red' for wr in win_rates]
    bars1 = ax1.bar(symbols, win_rates, color=colors, alpha=0.7)
    ax1.set_title('各Token胜率分析', fontweight='bold')
    ax1.set_ylabel('胜率 (%)')
    ax1.set_xlabel('Token Symbol')
    ax1.axhline(y=50, color='gray', linestyle='--', alpha=0.5, label='50%基准线')
    ax1.legend()
    ax1.tick_params(axis='x', rotation=45)
    
    # 在柱子上添加数值标签
    for bar, wr in zip(bars1, win_rates):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, 
                f'{wr:.1f}%', ha='center', va='bottom', fontsize=9)
    
    # 2. 收益分析（柱状图）
    colors2 = ['green' if pl > 0 else 'red' for pl in profit_losses]
    bars2 = ax2.bar(symbols, profit_losses, color=colors2, alpha=0.7)
    ax2.set_title('各Token总盈亏分析', fontweight='bold')
    ax2.set_ylabel('总盈亏 (USD)')
    ax2.set_xlabel('Token Symbol')
    ax2.axhline(y=0, color='gray', linestyle='--', alpha=0.5, label='盈亏平衡线')
    ax2.legend()
    ax2.tick_params(axis='x', rotation=45)
    
    # 在柱子上添加数值标签
    for bar, pl in zip(bars2, profit_losses):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2, 
                height + (0.01 if height >= 0 else -0.01), 
                f'${pl:.3f}', ha='center', 
                va='bottom' if height >= 0 else 'top', fontsize=9)
    
    # 3. 总收益率分析（柱状图）
    colors3 = ['green' if trr > 0 else 'red' for trr in total_return_rates]
    bars3 = ax3.bar(symbols, total_return_rates, color=colors3, alpha=0.7)
    ax3.set_title('各Token总收益率分析', fontweight='bold')
    ax3.set_ylabel('总收益率 (%)')
    ax3.set_xlabel('Token Symbol')
    ax3.axhline(y=0, color='gray', linestyle='--', alpha=0.5, label='盈亏平衡线')
    ax3.legend()
    ax3.tick_params(axis='x', rotation=45)
    
    # 在柱子上添加数值标签
    for bar, trr in zip(bars3, total_return_rates):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2, 
                height + (1 if height >= 0 else -1), 
                f'{trr:.1f}%', ha='center', 
                va='bottom' if height >= 0 else 'top', fontsize=9)
    
    # 4. 平均收益率分析（柱状图）
    colors4 = ['green' if roi > 0 else 'red' for roi in average_rois]
    bars4 = ax4.bar(symbols, average_rois, color=colors4, alpha=0.7)
    ax4.set_title('各Token平均收益率分析', fontweight='bold')
    ax4.set_ylabel('平均收益率 (%)')
    ax4.set_xlabel('Token Symbol')
    ax4.axhline(y=0, color='gray', linestyle='--', alpha=0.5, label='盈亏平衡线')
    ax4.legend()
    ax4.tick_params(axis='x', rotation=45)
    
    # 在柱子上添加数值标签
    for bar, roi in zip(bars4, average_rois):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2, 
                height + (1 if height >= 0 else -1), 
                f'{roi:.1f}%', ha='center', 
                va='bottom' if height >= 0 else 'top', fontsize=9)
    
    # 5. 胜率vs总收益率散点图
    ax5.scatter(win_rates, total_return_rates, s=[tc*30 for tc in trade_counts], 
               c=profit_losses, cmap='RdYlGn', alpha=0.7)
    ax5.set_title('胜率 vs 总收益率分析', fontweight='bold')
    ax5.set_xlabel('胜率 (%)')
    ax5.set_ylabel('总收益率 (%)')
    ax5.axhline(y=0, color='gray', linestyle='--', alpha=0.5)
    ax5.axvline(x=50, color='gray', linestyle='--', alpha=0.5)
    
    # 添加象限标签
    ax5.text(75, max(total_return_rates)*0.8, '高胜率\n高收益', ha='center', fontsize=10, 
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen", alpha=0.5))
    ax5.text(25, max(total_return_rates)*0.8, '低胜率\n高收益', ha='center', fontsize=10,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.5))
    ax5.text(75, min(total_return_rates)*0.8, '高胜率\n低收益', ha='center', fontsize=10,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.5))
    ax5.text(25, min(total_return_rates)*0.8, '低胜率\n低收益', ha='center', fontsize=10,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightcoral", alpha=0.5))
    
    # 为每个点添加标签
    for i, symbol in enumerate(symbols):
        ax5.annotate(symbol, (win_rates[i], total_return_rates[i]), 
                    xytext=(5, 5), textcoords='offset points', fontsize=8)
    
    # 6. 总收益率vs平均收益率对比
    ax6.scatter(total_return_rates, average_rois, s=[tc*30 for tc in trade_counts], 
               c=profit_losses, cmap='RdYlGn', alpha=0.7)
    ax6.set_title('总收益率 vs 平均收益率对比', fontweight='bold')
    ax6.set_xlabel('总收益率 (%)')
    ax6.set_ylabel('平均收益率 (%)')
    ax6.axhline(y=0, color='gray', linestyle='--', alpha=0.5)
    ax6.axvline(x=0, color='gray', linestyle='--', alpha=0.5)
    
    # 为每个点添加标签
    for i, symbol in enumerate(symbols):
        ax6.annotate(symbol, (total_return_rates[i], average_rois[i]), 
                    xytext=(5, 5), textcoords='offset points', fontsize=8)
    
    plt.tight_layout()
    plt.savefig('kol_trading_analysis_charts.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_summary_table(results):
    """创建汇总表格"""
    
    # 过滤有完成交易的token
    completed_results = [r for r in results if r['total_trades'] > 0]
    
    if not completed_results:
        print("没有找到完成的交易对")
        return
    
    # 创建详细表格
    table_data = []
    for result in completed_results:
        table_data.append({
            'Token': result['token_symbol'],
            '完成交易数': result['total_trades'],
            '胜率(%)': f"{result['win_rate']:.1f}%",
            '总盈亏(USD)': f"${result['total_profit_loss']:.4f}",
            '总收益率(%)': f"{result['total_return_rate']:.1f}%",
            '平均收益率(%)': f"{result['average_roi']:.1f}%",
            '交易状态': '盈利' if result['total_profit_loss'] > 0 else '亏损'
        })
    
    df = pd.DataFrame(table_data)
    df = df.sort_values('总盈亏(USD)', key=lambda x: x.str.replace('$', '').astype(float), ascending=False)
    
    print("=== 详细交易表现汇总表 ===")
    print(df.to_string(index=False))
    
    # 计算整体统计
    total_trades = sum(r['total_trades'] for r in completed_results)
    total_profit_loss = sum(r['total_profit_loss'] for r in completed_results)
    total_winning_trades = sum(r['total_trades'] * r['win_rate'] / 100 for r in completed_results)
    overall_win_rate = (total_winning_trades / total_trades) * 100 if total_trades > 0 else 0
    
    # 计算整体总收益率（加权平均）
    total_investment = sum([r['total_profit_loss'] / (r['total_return_rate'] / 100) if r['total_return_rate'] != 0 else 0 for r in completed_results])
    overall_return_rate = (total_profit_loss / total_investment) * 100 if total_investment > 0 else 0
    
    print(f"\n=== 整体表现汇总 ===")
    print(f"总Token数量: {len(completed_results)}")
    print(f"总完成交易数: {total_trades}")
    print(f"整体胜率: {overall_win_rate:.2f}%")
    print(f"总盈亏: ${total_profit_loss:.4f}")
    print(f"整体总收益率: {overall_return_rate:.2f}%")
    print(f"平均每Token盈亏: ${total_profit_loss/len(completed_results):.4f}")
    
    return df

def main():
    """主函数"""
    print("正在加载交易数据...")
    data = load_data('meme_monitor_backup.kol_wallet_activities.json')
    print(f"加载了 {len(data)} 条交易记录")
    
    print("正在分析交易表现...")
    results = analyze_token_performance(data)
    
    # 创建汇总表格
    summary_df = create_summary_table(results)
    
    # 创建可视化图表
    print("\n正在生成可视化图表...")
    create_visualizations(results)
    
    print("\n分析完成！图表已保存为 'kol_trading_analysis_charts.png'")
    
    return results, summary_df

if __name__ == "__main__":
    results, summary_df = main() 