# 过滤原始买入策略信号功能技术实现方案

**创建日期**: 2025-05-27  
**功能模块**: APIs - 信号记录列表API  
**版本**: 0.1.0  
**方案类型**: 功能扩展

## 实施概述

在现有的过滤测试策略信号功能基础上，扩展MongoDB查询条件，增加对`trigger_conditions.original_buy_strategy_name`字段的过滤。

## 技术方案

### 1. 修改范围

#### 1.1 主要文件
- **文件**: `api/v1/signal_api.py`
- **修改位置**: 第108-114行的查询过滤逻辑
- **修改类型**: 扩展现有的`$or`查询条件

#### 1.2 影响评估
- **代码变更**: 最小化，仅修改查询条件
- **API接口**: 无变更
- **数据库**: 无结构变更
- **性能**: 微乎其微的影响

### 2. 实现方案

#### 2.1 当前实现
```python
# 现有的过滤逻辑
query_filter['$or'] = [
    {'trigger_conditions.strategy_name': {'$ne': '测试'}},
    {'trigger_conditions.strategy_name': {'$exists': False}},
    {'trigger_conditions': {'$exists': False}}
]
```

#### 2.2 目标实现
```python
# 扩展后的过滤逻辑
query_filter['$and'] = [
    {
        '$or': [
            {'trigger_conditions.strategy_name': {'$ne': '测试'}},
            {'trigger_conditions.strategy_name': {'$exists': False}},
            {'trigger_conditions': {'$exists': False}}
        ]
    },
    {
        '$or': [
            {'trigger_conditions.original_buy_strategy_name': {'$ne': '测试'}},
            {'trigger_conditions.original_buy_strategy_name': {'$exists': False}},
            {'trigger_conditions': {'$exists': False}}
        ]
    }
]
```

#### 2.3 查询逻辑说明

**AND逻辑**: 两个过滤条件必须同时满足
1. **第一个OR条件**: 过滤`strategy_name`
   - 不等于"测试" OR
   - 字段不存在 OR  
   - trigger_conditions不存在

2. **第二个OR条件**: 过滤`original_buy_strategy_name`
   - 不等于"测试" OR
   - 字段不存在 OR
   - trigger_conditions不存在

### 3. 代码实现细节

#### 3.1 修改位置
- **文件**: `api/v1/signal_api.py`
- **行号**: 108-114行
- **函数**: `list_signals`

#### 3.2 具体修改
```python
# 修改前
query_filter['$or'] = [
    {'trigger_conditions.strategy_name': {'$ne': '测试'}},
    {'trigger_conditions.strategy_name': {'$exists': False}},
    {'trigger_conditions': {'$exists': False}}
]

# 修改后
# 过滤掉测试策略信号
# 同时排除 trigger_conditions.strategy_name 和 trigger_conditions.original_buy_strategy_name 为 "测试" 的记录
# 保留 trigger_conditions 或相关字段不存在的记录
query_filter['$and'] = [
    {
        '$or': [
            {'trigger_conditions.strategy_name': {'$ne': '测试'}},
            {'trigger_conditions.strategy_name': {'$exists': False}},
            {'trigger_conditions': {'$exists': False}}
        ]
    },
    {
        '$or': [
            {'trigger_conditions.original_buy_strategy_name': {'$ne': '测试'}},
            {'trigger_conditions.original_buy_strategy_name': {'$exists': False}},
            {'trigger_conditions': {'$exists': False}}
        ]
    }
]
```

#### 3.3 注释更新
更新代码注释，说明新增的过滤逻辑：
```python
# 过滤掉测试策略信号
# 同时排除 trigger_conditions.strategy_name 和 trigger_conditions.original_buy_strategy_name 为 "测试" 的记录
# 保留 trigger_conditions 或相关字段不存在的记录
```

### 4. 测试方案

#### 4.1 单元测试扩展
在现有测试文件`test/api/v1/test_signal_api_filter.py`基础上，增加以下测试用例：

1. **test_filter_original_buy_strategy_signals**: 测试过滤original_buy_strategy_name为"测试"的记录
2. **test_combined_strategy_filtering**: 测试同时过滤两个字段的组合情况
3. **test_original_buy_strategy_field_not_exists**: 测试original_buy_strategy_name字段不存在的情况
4. **test_both_strategy_fields_test**: 测试两个字段都为"测试"的情况

#### 4.2 测试数据准备
```python
# 测试数据示例
test_signals = [
    {
        "signal_type": "kol_sell",
        "trigger_conditions": {
            "strategy_name": "正常策略",
            "original_buy_strategy_name": "测试"  # 应被过滤
        }
    },
    {
        "signal_type": "kol_sell", 
        "trigger_conditions": {
            "strategy_name": "测试",  # 应被过滤
            "original_buy_strategy_name": "正常策略"
        }
    },
    {
        "signal_type": "kol_sell",
        "trigger_conditions": {
            "strategy_name": "正常策略",
            "original_buy_strategy_name": "正常策略"  # 应保留
        }
    }
]
```

### 5. 性能考虑

#### 5.1 查询复杂度
- **当前**: 单个`$or`查询
- **修改后**: `$and`包含两个`$or`查询
- **影响**: 查询复杂度略微增加，但仍在可接受范围内

#### 5.2 索引建议
考虑为以下字段创建复合索引（可选）：
```javascript
// MongoDB索引建议
db.signals.createIndex({
    "trigger_conditions.strategy_name": 1,
    "trigger_conditions.original_buy_strategy_name": 1,
    "created_at": -1
})
```

#### 5.3 性能预期
- **响应时间**: 增幅预计<2%
- **内存使用**: 无显著变化
- **CPU使用**: 微量增加

### 6. 兼容性保证

#### 6.1 向后兼容
- API接口定义不变
- 查询参数不变
- 响应格式不变
- 错误处理不变

#### 6.2 数据兼容
- 支持字段不存在的历史数据
- 支持null值和空字符串
- 不影响现有数据结构

### 7. 部署方案

#### 7.1 部署步骤
1. 代码修改和测试
2. 单元测试验证
3. 代码审查
4. 部署到测试环境
5. 功能验证
6. 部署到生产环境

#### 7.2 回滚方案
如果出现问题，可以快速回滚到原始的`$or`查询：
```python
# 回滚代码
query_filter['$or'] = [
    {'trigger_conditions.strategy_name': {'$ne': '测试'}},
    {'trigger_conditions.strategy_name': {'$exists': False}},
    {'trigger_conditions': {'$exists': False}}
]
```

### 8. 监控和验证

#### 8.1 功能验证
- 验证过滤逻辑正确性
- 验证API响应时间
- 验证数据完整性

#### 8.2 监控指标
- API响应时间
- 查询错误率
- 返回数据量变化

### 9. 风险缓解

#### 9.1 技术风险
- **风险**: MongoDB查询语法错误
- **缓解**: 充分的单元测试和代码审查

#### 9.2 业务风险
- **风险**: 过度过滤有效数据
- **缓解**: 详细的测试用例覆盖各种场景

### 10. 文档更新

#### 10.1 API文档
更新`docs/features/0.1.0/apis/获取信号记录列表（分页）.md`，说明新的过滤行为。

#### 10.2 代码注释
更新代码中的注释，说明过滤逻辑的变更。

## 总结

这是一个低风险、高价值的功能扩展，通过最小化的代码修改实现双重过滤功能，确保API返回的数据不包含任何测试策略相关的记录。实施方案简单直接，兼容性好，性能影响微乎其微。 