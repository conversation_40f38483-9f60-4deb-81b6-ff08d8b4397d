name: '智能钱包地址同步工作流'
description: '定期获取智能钱包数据，并将地址同步到监控表'

nodes:
  - name: "SmartMoneyMonitorNode2"
    node_type: "input"
    interval: 10  # 每10s运行一次
    generate_data: workflows.smart_money_kol.handler.fetch_smart_money_wallets
    flow_control:
      max_pending_messages: 100
      check_interval: 1
      enable_flow_control: true

  - name: "SmartMoneyAddressSyncNode2"
    node_type: "storage"
    depend_ons: ["SmartMoneyMonitorNode2"]
    batch_size: 1000  # 每批处理100条数据
    store_data: workflows.smart_money_kol.handler.store_smart_money_data
    validate: workflows.smart_money_kol.handler.validate_smart_money_data 
