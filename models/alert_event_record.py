from datetime import datetime
from typing import Optional, Dict, Any
from beanie import Document, Indexed
from pydantic import Field

class AlertEventRecord(Document):
    """记录每一次触发的告警事件"""
    monitor_type: Indexed(str) = Field(description="监控类型标识符, e.g., kol_activity_timestamp_discrepancy")
    category: Indexed(str) = Field(description="监控的类别, e.g., imported, non_imported")
    trigger_time: Indexed(datetime) = Field(default_factory=datetime.utcnow, description="告警触发时间 (UTC)")
    
    wallet_address: Optional[str] = Field(default=None, description="相关的KOL钱包地址")
    activity_tx_hash: Optional[str] = Field(default=None, description="相关活动的交易哈希")
    
    discrepancy_seconds: Optional[float] = Field(default=None, description="计算出的时间差异秒数")
    message: str = Field(description="告警的简要描述或发送给用户的核心信息")
    details: Optional[Dict[str, Any]] = Field(default=None, description="其他与告警相关的详细信息，如updated_at, timestamp等")

    class Settings:
        name = "alert_event_records"
        indexes = [
            "monitor_type",
            "category",
            "trigger_time",
            "wallet_address",
        ] 