# 工作流系统

这是一个基于节点和工作流概念的数据处理系统，用于替代原有的分散式流处理任务。

## 系统架构

系统由以下几个核心组件组成：

1. **工作流（Workflow）**：工作流是一系列节点的集合，负责协调节点之间的数据流转。
2. **节点（Node）**：节点是工作流中的基本处理单元，负责特定的数据处理任务。
3. **工作流数据（WorkflowData）**：在节点之间传递的数据包装类，包含数据本身和元数据。
4. **工作流管理器（WorkflowManager）**：负责管理和运行多个工作流。

## 核心概念

### 工作流（Workflow）

工作流是一个有向无环图（DAG），由多个节点组成。工作流负责：

- 管理节点之间的连接关系
- 协调数据在节点之间的流转
- 处理工作流的启动和停止

### 节点（Node）

节点是工作流中的基本处理单元，每个节点负责特定的数据处理任务。节点分为以下几种类型：

- **起始节点**：工作流的入口点，负责生成初始数据
- **处理节点**：接收上游节点的数据，处理后传递给下游节点
- **终止节点**：工作流的出口点，处理最终数据

### 工作流数据（WorkflowData）

工作流数据是在节点之间传递的数据包装类，包含：

- **数据本身**：实际的业务数据
- **状态**：数据的处理状态（有效、无效、错误等）
- **消息**：状态说明或错误消息
- **元数据**：其他相关信息

## 示例：GMGN代币链接工作流

GMGN代币链接工作流是一个完整的工作流示例，用于爬取和存储代币的链接信息。

### 工作流节点

1. **TokenFetchNode**：从数据库获取代币列表
2. **TokenLinksSpiderNode**：爬取代币的链接信息
3. **TokenLinksStorageNode**：将链接信息存储到数据库

### 工作流流程

1. TokenFetchNode 从数据库获取代币列表
2. TokenLinksSpiderNode 接收代币列表，爬取每个代币的链接信息
3. TokenLinksStorageNode 接收链接信息，将其存储到数据库

## 使用方法

### 创建自定义节点

```python
from utils.workflows.workflow import WorkflowNode, WorkflowData, DataStatus

class MyCustomNode(WorkflowNode):
    """自定义节点"""
    
    def __init__(self, name: str = "MyCustomNode"):
        super().__init__(name)
        # 初始化节点特定的属性
        
    async def _process(self, data: WorkflowData) -> WorkflowData:
        """处理数据"""
        # 处理数据的逻辑
        result = process_data(data.data)
        
        # 返回处理结果
        return WorkflowData(
            data=result,
            status=DataStatus.VALID,
            message="数据处理成功"
        )
```

### 创建自定义工作流

```python
from utils.workflows.workflow import Workflow
from utils.workflows.my_custom_nodes import NodeA, NodeB, NodeC

class MyCustomWorkflow(Workflow):
    """自定义工作流"""
    
    def __init__(self, name: str = "MyCustomWorkflow"):
        super().__init__(name)
        
        # 创建节点
        self.node_a = NodeA()
        self.node_b = NodeB()
        self.node_c = NodeC()
        
        # 添加节点到工作流
        self.add_start_node(self.node_a)
        self.add_node(self.node_b)
        self.add_node(self.node_c)
        
        # 连接节点
        self.connect(self.node_a, self.node_b)
        self.connect(self.node_b, self.node_c)
```

### 运行工作流

```python
import asyncio
from utils.workflows.my_custom_workflow import MyCustomWorkflow

async def main():
    # 创建工作流
    workflow = MyCustomWorkflow()
    
    # 运行工作流
    await workflow.execute()

if __name__ == "__main__":
    asyncio.run(main())
```

### 使用工作流管理器

```python
import asyncio
from utils.workflows.workflow_manager import workflow_manager
from utils.workflows.my_custom_workflow import MyCustomWorkflow

async def main():
    # 创建工作流
    workflow = MyCustomWorkflow()
    
    # 注册工作流
    workflow_manager.register_workflow(workflow)
    
    # 运行工作流管理器
    await workflow_manager.run()

if __name__ == "__main__":
    asyncio.run(main())
```

## 优势

1. **模块化设计**：每个节点负责特定的任务，便于维护和扩展
2. **灵活的工作流**：可以灵活组合节点，构建复杂的数据处理流程
3. **统一的数据格式**：使用统一的数据格式在节点之间传递数据
4. **错误处理**：内置错误处理机制，提高系统稳定性
5. **可视化潜力**：基于节点和工作流的设计，便于未来实现可视化编排 