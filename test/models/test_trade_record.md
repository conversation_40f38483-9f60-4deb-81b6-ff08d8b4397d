# TradeRecord 模型单元测试
创建日期：{{TODAY_DATE}}
更新日期：{{TODAY_DATE}}
测试方法：自动化测试
测试级别：单元测试

## 测试用例
| 用例方法 | 用例概述 | 前置条件 | 输入 | 预期输出 | 实际输出 | 状态 |
| --- | --- | --- | --- | --- | --- | --- |
| `test_trade_record_instantiation_valid` | 测试使用所有必需和部分可选的有效数据成功实例化 `TradeRecord` | 无 | 有效的 `signal_id`, `strategy_name`, `trade_provider`, `trade_type` (BUY), `status` (PENDING), `token_in_address`, `token_in_amount`, `token_out_address`, `wallet_address` | 成功创建 `TradeRecord` 实例，字段值与输入匹配，时间戳自动生成，可选字段为默认值 (如 `tx_hash` is None) | - | 已编码 |
| `test_trade_record_instantiation_minimal` | 测试使用核心字段和一些可选字段实例化，并检查其他字段的默认值 | 无 | 有效的 `signal_id`, `strategy_name`, `trade_provider`, `trade_type` (SELL), `status` (SUCCESS), `token_in_address`, `token_in_amount`, `token_out_address`, `wallet_address`, `tx_hash`, `executed_at` | 成功创建 `TradeRecord` 实例，特定默认值 (如 `error_message` is None, `provider_response_raw` is {}) 被正确设置 | - | 已编码 |
| `test_trade_record_invalid_trade_type_enum` | 测试当 `trade_type` 字段提供一个无效的枚举字符串时，Pydantic 引发 `ValidationError` | 无 | `trade_type` = "INVALID_TYPE" (无效字符串) | 引发 `ValidationError` | - | 已编码 |
| `test_trade_record_invalid_status_enum` | 测试当 `status` 字段提供一个无效的枚举字符串时，Pydantic 引发 `ValidationError` | 无 | `status` = "INVALID_STATUS" (无效字符串) | 引发 `ValidationError` | - | 已编码 |
| `test_trade_record_timestamps_auto_creation` | 测试 `created_at` 和 `updated_at` 字段在实例化时是否自动设置为当前的 `datetime` | 无 | 基本的有效 `TradeRecord` 数据 | `created_at` 和 `updated_at` 是 `datetime` 对象，并且它们的值非常接近 | - | 已编码 |
| `test_trade_record_optional_fields_default_to_none_or_empty` | 测试在未提供可选字段时，它们是否正确地默认为 `None` 或其指定的空值 (如 `dict`) | 无 | 基本的有效 `TradeRecord` 数据，不包含可选字段 | `tx_hash`, `error_message`, `token_in_actual_amount`, `token_out_actual_amount`, `executed_at`, `linked_buy_trade_record_id` 均为 `None`；`provider_response_raw` 为 `{}` | - | 已编码 |
| `test_trade_record_with_all_fields` | 测试当所有字段 (包括所有可选字段) 都被显式提供时，模型是否能正确实例化 | 无 | 包含所有有效值的完整 `TradeRecord` 数据字典，包括覆盖默认时间戳和提供所有可选字段 | 成功创建 `TradeRecord` 实例，所有字段值与输入匹配 | - | 已编码 | 