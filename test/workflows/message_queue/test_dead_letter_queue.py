"""
测试死信队列模块
"""

import unittest
from unittest import mock
import asyncio
import traceback

from utils.workflows.message_queue.message_queue import Message
from utils.workflows.message_queue.dead_letter_queue import (
    DeadLetterType, 
    DeadLetterMessage, 
    DeadLetterQueueManager
)


class TestDeadLetterQueue(unittest.TestCase):
    """测试死信队列功能"""
    
    def setUp(self):
        """设置测试环境"""
        # 设置事件循环
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        
    def tearDown(self):
        """清理测试环境"""
        self.loop.close()
    
    def test_dead_letter_message_creation(self):
        """测试死信队列消息创建"""
        dlm = DeadLetterMessage(
            original_topic="test_topic",
            original_partition=0,
            original_offset=123,
            error_message="测试错误",
            stack_trace="测试堆栈跟踪",
            node_name="test_node",
            original_data={"test": "data"}
        )
        
        # 验证属性值
        self.assertEqual(dlm.original_topic, "test_topic")
        self.assertEqual(dlm.original_partition, 0)
        self.assertEqual(dlm.original_offset, 123)
        self.assertEqual(dlm.error_message, "测试错误")
        self.assertEqual(dlm.stack_trace, "测试堆栈跟踪")
        self.assertEqual(dlm.node_name, "test_node")
        self.assertEqual(dlm.original_data, {"test": "data"})
        
        # 测试转换为字典
        dlm_dict = dlm.to_dict()
        self.assertEqual(dlm_dict["original_topic"], "test_topic")
        self.assertEqual(dlm_dict["original_partition"], 0)
        self.assertEqual(dlm_dict["original_offset"], 123)
        self.assertEqual(dlm_dict["error_message"], "测试错误")
        self.assertEqual(dlm_dict["stack_trace"], "测试堆栈跟踪")
        self.assertEqual(dlm_dict["node_name"], "test_node")
        self.assertEqual(dlm_dict["original_data"], {"test": "data"})
        
        # 测试从字典创建
        dlm2 = DeadLetterMessage.from_dict(dlm_dict)
        self.assertEqual(dlm2.original_topic, "test_topic")
        self.assertEqual(dlm2.original_partition, 0)
        self.assertEqual(dlm2.original_offset, 123)
        self.assertEqual(dlm2.error_message, "测试错误")
        self.assertEqual(dlm2.stack_trace, "测试堆栈跟踪")
        self.assertEqual(dlm2.node_name, "test_node")
        self.assertEqual(dlm2.original_data, {"test": "data"})
    
    def test_from_original_message_with_attributes(self):
        """测试从有完整属性的原始消息创建死信队列消息"""
        # 创建带有topic、partition和offset属性的Message对象
        message = Message(data={"test": "data"})
        message.topic = "test_topic"
        message.partition = 1
        message.offset = 100
        
        error_obj = None
        try:
            raise Exception("测试异常")
        except Exception as e:
            error_obj = e
            
        node_name = "test_node"
        
        # 创建死信队列消息
        dlm = DeadLetterMessage.from_original_message(message, error_obj, node_name)
        
        # 验证属性值
        self.assertEqual(dlm.original_topic, "test_topic")
        self.assertEqual(dlm.original_partition, 1)
        self.assertEqual(dlm.original_offset, 100)
        self.assertEqual(dlm.error_message, "测试异常")
        self.assertTrue("Traceback" in dlm.stack_trace, f"Expected 'Traceback' in '{dlm.stack_trace}'")
        self.assertEqual(dlm.node_name, "test_node")
        self.assertEqual(dlm.original_data, {"test": "data"})
    
    def test_from_original_message_without_attributes(self):
        """测试从没有必要属性的原始消息创建死信队列消息"""
        # 创建普通Message对象，没有topic、partition和offset属性
        message = Message(data={"test": "data"})
        
        error_obj = None
        try:
            raise Exception("测试异常")
        except Exception as e:
            error_obj = e

        node_name = "test_node"
        
        # 创建死信队列消息
        dlm = DeadLetterMessage.from_original_message(message, error_obj, node_name)
        
        # 验证属性值
        self.assertEqual(dlm.original_topic, "unknown_topic")
        self.assertEqual(dlm.original_partition, 0)
        self.assertEqual(dlm.original_offset, -1)
        self.assertEqual(dlm.error_message, "测试异常")
        self.assertTrue("Traceback" in dlm.stack_trace, f"Expected 'Traceback' in '{dlm.stack_trace}'")
        self.assertEqual(dlm.node_name, "test_node")
        self.assertEqual(dlm.original_data, {"test": "data"})
    
    def test_from_original_message_with_metadata(self):
        """测试从有metadata的原始消息创建死信队列消息"""
        # 创建带有metadata的Message对象
        message = Message(data={"test": "data"})
        message.metadata = {
            "topic": "metadata_topic",
            "partition": 2,
            "offset": 200,
            "message_id": "12345"
        }
        
        error_obj = None
        try:
            raise Exception("测试异常")
        except Exception as e:
            error_obj = e
            
        node_name = "test_node"
        
        # 创建死信队列消息
        dlm = DeadLetterMessage.from_original_message(message, error_obj, node_name)
        
        # 验证属性值
        self.assertEqual(dlm.original_topic, "metadata_topic")
        self.assertEqual(dlm.original_partition, 2)
        self.assertEqual(dlm.original_offset, 200)
        self.assertEqual(dlm.error_message, "测试异常")
        self.assertTrue("Traceback" in dlm.stack_trace, f"Expected 'Traceback' in '{dlm.stack_trace}'")
        self.assertEqual(dlm.node_name, "test_node")
        self.assertEqual(dlm.original_data, {"test": "data"})
    
    def test_from_original_message_with_message_id(self):
        """测试从只有message_id的原始消息创建死信队列消息"""
        # 创建只有message_id的Message对象
        message = Message(data={"test": "data"})
        message.metadata = {
            "message_id": "12345"
        }
        
        error_obj = None
        try:
            raise Exception("测试异常")
        except Exception as e:
            error_obj = e
            
        node_name = "test_node"
        
        # 创建死信队列消息
        dlm = DeadLetterMessage.from_original_message(message, error_obj, node_name)
        
        # 验证属性值
        self.assertEqual(dlm.original_topic, "unknown_topic")
        self.assertEqual(dlm.original_partition, 0)
        self.assertEqual(dlm.original_offset, 12345)  # 应该使用message_id作为offset
        self.assertEqual(dlm.error_message, "测试异常")
        self.assertTrue("Traceback" in dlm.stack_trace, f"Expected 'Traceback' in '{dlm.stack_trace}'")
        self.assertEqual(dlm.node_name, "test_node")
        self.assertEqual(dlm.original_data, {"test": "data"})
    
    def test_from_original_message_with_non_numeric_message_id(self):
        """测试从有非数字message_id的原始消息创建死信队列消息"""
        # 创建有非数字message_id的Message对象
        message = Message(data={"test": "data"})
        message.metadata = {
            "message_id": "abc123"  # 非数字格式
        }
        
        error_obj = None
        try:
            raise Exception("测试异常")
        except Exception as e:
            error_obj = e
            
        node_name = "test_node"
        
        # 创建死信队列消息
        dlm = DeadLetterMessage.from_original_message(message, error_obj, node_name)
        
        # 验证属性值
        self.assertEqual(dlm.original_topic, "unknown_topic")
        self.assertEqual(dlm.original_partition, 0)
        self.assertEqual(dlm.original_offset, -1)  # 应该保持为-1，因为message_id不是数字
        self.assertEqual(dlm.error_message, "测试异常")
        self.assertTrue("Traceback" in dlm.stack_trace, f"Expected 'Traceback' in '{dlm.stack_trace}'")
        self.assertEqual(dlm.node_name, "test_node")
        self.assertEqual(dlm.original_data, {"test": "data"})
    
    def test_from_original_message_with_string_error(self):
        """测试使用字符串错误而不是异常创建死信队列消息"""
        # 创建Message对象
        message = Message(data={"test": "data"})
        
        error = "字符串错误消息"  # 使用字符串而不是异常
        node_name = "test_node"
        
        # 创建死信队列消息
        dlm = DeadLetterMessage.from_original_message(message, error, node_name)
        
        # 验证属性值
        self.assertEqual(dlm.original_topic, "unknown_topic")
        self.assertEqual(dlm.original_partition, 0)
        self.assertEqual(dlm.original_offset, -1)
        self.assertEqual(dlm.error_message, "字符串错误消息")
        self.assertEqual(dlm.stack_trace, "")  # 应该是空字符串，因为没有提供异常
        self.assertEqual(dlm.node_name, "test_node")
        self.assertEqual(dlm.original_data, {"test": "data"})
    
    def test_dlq_manager_initialization(self):
        """测试死信队列管理器初始化"""
        dlq_manager = DeadLetterQueueManager()
        self.assertEqual(len(dlq_manager.queues), 0)
    
    async def _test_dlq_manager_registration(self):
        """测试死信队列管理器注册队列"""
        dlq_manager = DeadLetterQueueManager()
        
        # 创建mock死信队列
        mock_dlq1 = mock.AsyncMock()
        mock_dlq2 = mock.MagicMock()
        
        # 注册队列
        dlq_manager.register_queue(DeadLetterType.PROCESS_FAILURE, mock_dlq1)
        dlq_manager.register_queue(DeadLetterType.STORAGE_FAILURE, mock_dlq2)
        
        # 验证注册结果
        self.assertEqual(len(dlq_manager.queues), 2)
        self.assertEqual(dlq_manager.queues[DeadLetterType.PROCESS_FAILURE], mock_dlq1)
        self.assertEqual(dlq_manager.queues[DeadLetterType.STORAGE_FAILURE], mock_dlq2)
        
        # 测试发送消息到队列
        mock_message = mock.MagicMock()
        mock_dlq1.send.return_value = True
        
        # 发送消息
        result = await dlq_manager.send_to_queue(DeadLetterType.PROCESS_FAILURE, mock_message)
        
        # 验证结果
        self.assertTrue(result)
        mock_dlq1.send.assert_called_once_with(mock_message)
    
    def test_dlq_manager_registration(self):
        """测试死信队列管理器注册队列包装器"""
        self.loop.run_until_complete(self._test_dlq_manager_registration())


if __name__ == "__main__":
    unittest.main() 