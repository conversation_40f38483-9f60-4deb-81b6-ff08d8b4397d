from beanie import Document, PydanticObjectId
from typing import Optional, Dict, Any, List
from datetime import datetime, timezone
from pydantic import BaseModel, Field
from enum import Enum
import pymongo # Added for pymongo.ASCENDING

class TradeStatus(str, Enum):
    PENDING = "pending"
    SUCCESS = "success"
    FAILED = "failed"
    SKIPPED = "skipped" # Skipped due to config, pre-check, etc.
    MANUAL_INTERVENTION_REQUIRED = "manual_intervention_required"

class TradeType(str, Enum):
    BUY = "buy"
    SELL = "sell"

class TradeRecord(Document):
    signal_id: PydanticObjectId             # Link to the Signal that triggered this trade
    strategy_name: str                      # Name of the strategy that triggered this trade
    trade_provider: str                     # e.g., "gmgn", "jupiter", "manual"
    trade_type: TradeType                   # "buy" or "sell"
    status: TradeStatus = Field(default=TradeStatus.PENDING)
    
    token_in_address: str
    token_in_amount: Optional[float] = Field(default=None, description="Amount intended to spend/sell")
    token_in_actual_amount: Optional[float] = Field(default=None, description="Amount actually spent/sold (if available)")
    
    token_out_address: str
    token_out_amount_expected: Optional[float] = Field(default=None, description="Amount expected to receive")
    token_out_actual_amount: Optional[float] = Field(default=None, description="Amount actually received")
    token_out_decimals: Optional[int] = Field(default=None, description="Decimals of the output token")
    token_in_decimals: Optional[int] = Field(default=None, description="Decimals of the input token")

    wallet_address: str                     # The wallet executing the trade
    tx_hash: Optional[str] = None
    
    error_message: Optional[str] = None
    provider_response_raw: Optional[Dict[str, Any]] = Field(default=None, description="Store raw response from provider for debugging")

    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    executed_at: Optional[datetime] = None  # Timestamp of successful execution
    
    # === 新增：AutoTradeManager 相关字段 ===
    channel_attempt_record_ids: List[PydanticObjectId] = Field(default_factory=list, description="关联的渠道尝试记录ID列表")
    total_execution_time: Optional[float] = Field(default=None, description="总执行时间（秒）")
    auto_trade_manager_version: str = Field(default="1.0", description="使用的AutoTradeManager版本")
    final_successful_channel: Optional[str] = Field(default=None, description="最终成功的渠道类型")
    total_channels_attempted: int = Field(default=0, description="尝试的渠道总数")
    
    # === 新增：交易记录验证相关字段 ===
    token_out_verified_amount: Optional[float] = Field(default=None, description="通过区块链验证的实际输出金额")
    verification_status: Optional[str] = Field(default=None, description="验证状态: pending/verified/failed/skipped")
    verification_timestamp: Optional[datetime] = Field(default=None, description="验证完成时间戳")
    verification_error: Optional[str] = Field(default=None, description="验证失败时的错误信息")
    verification_attempts: int = Field(default=0, description="验证尝试次数")
    last_verification_attempt: Optional[datetime] = Field(default=None, description="最后一次验证尝试时间")
    
    # === 新增：实际金额更新相关字段（用于修复盈利计算Bug）===
    actual_amount_updated_at: Optional[datetime] = Field(default=None, description="实际金额更新时间戳")
    actual_amount_source: Optional[str] = Field(default=None, description="实际金额数据来源: gmgn_api/blockchain/manual")
    
    # === 向后兼容：保留原有字段的别名 ===
    # trade_provider 现在主要用于向后兼容，新版本会使用 final_successful_channel

    class Settings:
        name = "trade_records"
        indexes = [
            "signal_id",
            "tx_hash",
            "status",
            "trade_provider",
            "final_successful_channel",  # 新增索引
            "wallet_address",
            "token_in_address",
            "token_out_address",
            "created_at",
            "updated_at",
            "auto_trade_manager_version",  # 新增索引
            "verification_status",  # 验证状态索引
            "verification_timestamp",  # 验证时间戳索引
            "last_verification_attempt",  # 最后验证尝试时间索引
            "actual_amount_updated_at",  # 实际金额更新时间索引
            "actual_amount_source"  # 实际金额数据来源索引
        ]

    async def save(self, *args, **kwargs):
        self.updated_at = datetime.now(timezone.utc)
        await super().save(*args, **kwargs)
    
    def add_channel_attempt_record(self, attempt_record_id: PydanticObjectId) -> None:
        """添加渠道尝试记录ID"""
        if attempt_record_id not in self.channel_attempt_record_ids:
            self.channel_attempt_record_ids.append(attempt_record_id)
            self.total_channels_attempted = len(self.channel_attempt_record_ids)
    
    def mark_successful(self, channel_type: str, tx_hash: str, executed_at: Optional[datetime] = None) -> None:
        """标记交易成功"""
        self.status = TradeStatus.SUCCESS
        self.final_successful_channel = channel_type
        self.trade_provider = channel_type  # 保持向后兼容
        self.tx_hash = tx_hash
        self.executed_at = executed_at or datetime.now(timezone.utc)
    
    def mark_failed(self, error_message: str) -> None:
        """标记交易失败"""
        self.status = TradeStatus.FAILED
        self.error_message = error_message
    
    def is_multi_channel_trade(self) -> bool:
        """判断是否为多渠道交易（通过AutoTradeManager执行）"""
        return len(self.channel_attempt_record_ids) > 0