[program:gmgn_others_combined]
command=/home/<USER>/.local/bin/poetry run python run_workflow.py --dir /home/<USER>/meme_monitor/workflows/gmgn_gas_price /home/<USER>/meme_monitor/workflows/gmgn_token_link /home/<USER>/meme_monitor/workflows/smart_money /home/<USER>/meme_monitor/workflows/spider_x_user_info /home/<USER>/meme_monitor/workflows/solana_monitor /home/<USER>/meme_monitor/workflows/gmgn_token_monitor /home/<USER>/meme_monitor/workflows/gmgn_token_stats /home/<USER>/meme_monitor/workflows/gmgn_token_window /home/<USER>/meme_monitor/workflows/gmgn_wallet_holdings /home/<USER>/meme_monitor/workflows/gmgn_token_sync
directory=/home/<USER>/meme_monitor
user=gaojunbin
autostart=true
autorestart=true
stderr_logfile=/home/<USER>/meme_monitor/logs/gmgn_others_combined.err.log
stdout_logfile=/home/<USER>/meme_monitor/logs/gmgn_others_combined.out.log
environment=PYTHONUNBUFFERED=1,http_proxy=http://127.0.0.1:7897,https_proxy=http://127.0.0.1:7897,HTTP_PROXY=http://127.0.0.1:7897,HTTPS_PROXY=http://127.0.0.1:7897,all_proxy=http://127.0.0.1:7897,ALL_PROXY=http://127.0.0.1:7897,SOCKS_PROXY=socks5://127.0.0.1:7897,socks_proxy=socks5://127.0.0.1:7897

[program:gmgn_traders_combined]
command=/home/<USER>/.local/bin/poetry run python run_workflow.py --dir /home/<USER>/meme_monitor/workflows/gmgn_trade_history
directory=/home/<USER>/meme_monitor
user=gaojunbin
autostart=true
autorestart=true
stderr_logfile=/home/<USER>/meme_monitor/logs/gmgn_traders_combined.err.log
stdout_logfile=/home/<USER>/meme_monitor/logs/gmgn_traders_combined.out.log
environment=PYTHONUNBUFFERED=1,http_proxy=http://127.0.0.1:7897,https_proxy=http://127.0.0.1:7897,HTTP_PROXY=http://127.0.0.1:7897,HTTPS_PROXY=http://127.0.0.1:7897,all_proxy=http://127.0.0.1:7897,ALL_PROXY=http://127.0.0.1:7897,SOCKS_PROXY=socks5://127.0.0.1:7897,socks_proxy=socks5://127.0.0.1:7897

[program:gmgn_holders_combined]
command=/home/<USER>/.local/bin/poetry run python run_workflow.py --dir /home/<USER>/meme_monitor/workflows/gmgn_top_holders /home/<USER>/meme_monitor/workflows/gmgn_kol_holdings /home/<USER>/meme_monitor/workflows/gmgn_blue_chip_holders /home/<USER>/meme_monitor/workflows/gmgn_fresh_wallet_holders /home/<USER>/meme_monitor/workflows/gmgn_developer_holders /home/<USER>/meme_monitor/workflows/gmgn_rat_trader_holdings /home/<USER>/meme_monitor/workflows/gmgn_phishing_holdings /home/<USER>/meme_monitor/workflows/gmgn_bot_degen_holders
directory=/home/<USER>/meme_monitor
user=gaojunbin
autostart=true
autorestart=true
stderr_logfile=/home/<USER>/meme_monitor/logs/gmgn_holders_combined.err.log
stdout_logfile=/home/<USER>/meme_monitor/logs/gmgn_holders_combined.out.log
environment=PYTHONUNBUFFERED=1,http_proxy=http://127.0.0.1:7897,https_proxy=http://127.0.0.1:7897,HTTP_PROXY=http://127.0.0.1:7897,HTTPS_PROXY=http://127.0.0.1:7897,all_proxy=http://127.0.0.1:7897,ALL_PROXY=http://127.0.0.1:7897,SOCKS_PROXY=socks5://127.0.0.1:7897,socks_proxy=socks5://127.0.0.1:7897

[program:gmgn_activity_combined]
command=/home/<USER>/.local/bin/poetry run python run_workflow.py --dir /home/<USER>/meme_monitor/workflows/gmgn_kol_activity /home/<USER>/meme_monitor/workflows/gmgn_dev_activity /home/<USER>/meme_monitor/workflows/gmgn_fresh_activity /home/<USER>/meme_monitor/workflows/gmgn_rat_trader_activity /home/<USER>/meme_monitor/workflows/gmgn_sniper_activity /home/<USER>/meme_monitor/workflows/gmgn_whale_activity /home/<USER>/meme_monitor/workflows/gmgn_top_holders_activity /home/<USER>/meme_monitor/workflows/gmgn_smart_activity
directory=/home/<USER>/meme_monitor
user=gaojunbin
autostart=true
autorestart=true
stderr_logfile=/home/<USER>/meme_monitor/logs/gmgn_activity_combined.err.log
stdout_logfile=/home/<USER>/meme_monitor/logs/gmgn_activity_combined.out.log
environment=PYTHONUNBUFFERED=1,http_proxy=http://127.0.0.1:7897,https_proxy=http://127.0.0.1:7897,HTTP_PROXY=http://127.0.0.1:7897,HTTPS_PROXY=http://127.0.0.1:7897,all_proxy=http://127.0.0.1:7897,ALL_PROXY=http://127.0.0.1:7897,SOCKS_PROXY=socks5://127.0.0.1:7897,socks_proxy=socks5://127.0.0.1:7897
