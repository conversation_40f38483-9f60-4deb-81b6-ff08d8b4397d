# 过滤测试策略信号技术实现方案

**创建日期**: 2025-05-27  
**方案设计者**: AI助手  
**功能模块**: APIs - 信号记录列表API  
**版本**: 0.1.0  

## 1. 实现概述

本方案在现有的`/api/v1/signals` API中添加MongoDB查询过滤条件，自动排除`trigger_conditions.strategy_name`为"测试"的信号记录。实现方式为在查询构建阶段添加过滤条件，确保向后兼容性。

## 2. 技术实现方案

### 2.1 修改文件清单

**主要修改文件**:
- `api/v1/signal_api.py`: 添加过滤逻辑

**可能需要更新的文档**:
- `docs/features/0.1.0/apis/获取信号记录列表（分页）.md`: 更新API文档说明过滤行为

### 2.2 核心实现逻辑

#### 2.2.1 MongoDB查询过滤条件设计

**位置**: `api/v1/signal_api.py` 第88-108行的查询构建逻辑

**当前代码结构**:
```python
# Build base query filter
query_filter: Dict[str, Any] = {}

# Add signal_type filter if provided
if signal_type:
    query_filter['signal_type'] = signal_type

# Apply time period filtering if specified
if period:
    # ... 时间过滤逻辑
```

**新增过滤逻辑**:
```python
# 在现有过滤条件构建后添加
# 过滤掉测试策略信号 (trigger_conditions.strategy_name = "测试")
query_filter['trigger_conditions.strategy_name'] = {'$ne': '测试'}
```

#### 2.2.2 字段存在性处理

为了正确处理`trigger_conditions`或`strategy_name`字段不存在的情况，使用MongoDB的`$or`操作符：

```python
# 更完善的过滤条件，处理字段不存在的情况
query_filter['$or'] = [
    {'trigger_conditions.strategy_name': {'$ne': '测试'}},  # strategy_name不等于"测试"
    {'trigger_conditions.strategy_name': {'$exists': False}},  # strategy_name字段不存在
    {'trigger_conditions': {'$exists': False}}  # trigger_conditions字段不存在
]
```

### 2.3 实现步骤

#### 步骤1: 修改查询构建逻辑
在`api/v1/signal_api.py`的`list_signals`函数中，在现有的查询过滤条件构建完成后，添加测试策略过滤条件。

#### 步骤2: 添加代码注释
为新增的过滤逻辑添加清晰的注释，说明过滤目的和逻辑。

#### 步骤3: 保持现有错误处理
确保新增的过滤逻辑不影响现有的异常处理机制。

### 2.4 代码变更详情

**文件**: `api/v1/signal_api.py`
**函数**: `list_signals`
**变更位置**: 约第108行，在`signal_dao.collection.find(query_filter)`调用之前

**变更内容**:
```python
# 现有代码...
        # Apply time period filtering if specified
        if period:
            # ... 现有时间过滤逻辑

        # 新增: 过滤掉测试策略信号
        # 排除 trigger_conditions.strategy_name 为 "测试" 的记录
        # 同时保留 trigger_conditions 或 strategy_name 字段不存在的记录
        query_filter['$or'] = [
            {'trigger_conditions.strategy_name': {'$ne': '测试'}},
            {'trigger_conditions.strategy_name': {'$exists': False}},
            {'trigger_conditions': {'$exists': False}}
        ]

        signal_dao = SignalDAO()
        # 现有查询代码...
```

### 2.5 数据结构影响分析

#### 2.5.1 MongoDB查询性能
- **影响**: 添加嵌套字段查询条件可能轻微影响查询性能
- **缓解措施**: 如果性能成为问题，可考虑在`trigger_conditions.strategy_name`字段上创建索引

#### 2.5.2 现有索引利用
- 当前Signal模型已有的索引（signal_type, created_at等）仍然可以正常使用
- 新增的过滤条件不会破坏现有索引的效果

### 2.6 向后兼容性保证

#### 2.6.1 API接口兼容性
- 不修改任何API参数定义
- 不修改响应数据结构
- 所有现有的查询参数继续正常工作

#### 2.6.2 数据兼容性
- 正确处理历史数据中可能缺失的字段
- 不影响现有数据的读取和显示

### 2.7 错误处理策略

#### 2.7.1 查询错误处理
- 复用现有的异常处理机制
- 如果MongoDB查询失败，返回标准的错误响应

#### 2.7.2 日志记录
- 不需要额外的日志记录，因为过滤是静默的
- 现有的错误日志机制继续有效

## 3. 实现风险评估

### 3.1 技术风险

**风险**: MongoDB查询语法错误
- **概率**: 低
- **影响**: 中等
- **缓解**: 充分测试查询语法，使用MongoDB官方文档验证

**风险**: 性能影响
- **概率**: 低
- **影响**: 低
- **缓解**: 监控API响应时间，必要时添加索引

### 3.2 业务风险

**风险**: 过度过滤导致有效数据丢失
- **概率**: 低
- **影响**: 中等
- **缓解**: 严格按照需求规格实现，只过滤strategy_name严格等于"测试"的记录

## 4. 测试策略

### 4.1 单元测试
- 测试不同查询条件组合下的过滤效果
- 测试边界情况（字段不存在、null值等）

### 4.2 集成测试
- 测试API端到端功能
- 验证与现有查询参数的兼容性

### 4.3 性能测试
- 对比添加过滤前后的API响应时间
- 测试大数据量下的查询性能

## 5. 部署考虑

### 5.1 部署步骤
1. 代码修改和本地测试
2. 单元测试和集成测试
3. 部署到测试环境验证
4. 生产环境部署

### 5.2 回滚计划
- 如果出现问题，可以快速回滚到之前版本
- 修改是增量的，回滚风险较低

## 6. 监控和维护

### 6.1 监控指标
- API响应时间
- API错误率
- 查询结果数量变化

### 6.2 维护考虑
- 定期检查是否有新的测试策略名称需要过滤
- 监控过滤效果和业务影响 