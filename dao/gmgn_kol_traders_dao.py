from typing import List, Optional, Dict, Any
from datetime import datetime
from models.gmgn_kol_traders import GmgnKolTraders
from .base_dao import BaseDAO
from pymongo import InsertOne, UpdateOne, DESCENDING

class GmgnKolTradersDAO(BaseDAO[GmgnKolTraders]):
    """GMGN KOL交易者数据访问对象"""
    
    def __init__(self):
        super().__init__(GmgnKolTraders)
    
    async def find_by_address(self, chain: str, address: str) -> Optional[GmgnKolTraders]:
        """根据链和地址获取KOL交易者信息
        
        Args:
            chain: 链名称
            address: 钱包地址
            
        Returns:
            Optional[GmgnKolTraders]: KOL交易者信息
        """
        return await self.find_one({
            "chain": chain,
            "address": address
        })
    
    async def find_by_token_address(self, chain: str, token_address: str, limit: int = 100) -> List[GmgnKolTraders]:
        """根据链和代币地址获取KOL交易者列表
        
        Args:
            chain: 链名称
            token_address: 代币地址
            limit: 返回数量限制
            
        Returns:
            List[GmgnKolTraders]: KOL交易者列表
        """
        return await self.find_many(
            {
                "chain": chain,
                "token_address": token_address
            },
            sort=[("profit", DESCENDING)],
            limit=limit
        )
    
    async def find_by_account_address(self, chain: str, account_address: str) -> Optional[GmgnKolTraders]:
        """根据链和账户地址获取KOL交易者信息
        
        Args:
            chain: 链名称
            account_address: 账户地址
            
        Returns:
            Optional[GmgnKolTraders]: KOL交易者信息
        """
        return await self.find_one({
            "chain": chain,
            "account_address": account_address
        })
    
    async def find_by_wallet_tag(self, chain: str, wallet_tag: str) -> List[GmgnKolTraders]:
        """根据钱包标签获取KOL交易者列表
        
        Args:
            chain: 链名称
            wallet_tag: 钱包标签
            
        Returns:
            List[GmgnKolTraders]: KOL交易者列表
        """
        return await self.find_many({
            "chain": chain,
            "wallet_tag_v2": wallet_tag
        })
    
    async def find_by_twitter_username(self, twitter_username: str) -> List[GmgnKolTraders]:
        """根据Twitter用户名获取KOL交易者列表
        
        Args:
            twitter_username: Twitter用户名
            
        Returns:
            List[GmgnKolTraders]: KOL交易者列表
        """
        return await self.find_many({
            "twitter_username": twitter_username
        })
    
    async def find_by_profit_range(
        self,
        chain: str,
        min_profit: float = 0,
        max_profit: Optional[float] = None,
        limit: int = 100
    ) -> List[GmgnKolTraders]:
        """获取指定利润范围内的KOL交易者列表
        
        Args:
            chain: 链名称
            min_profit: 最小利润
            max_profit: 最大利润
            limit: 返回数量限制
            
        Returns:
            List[GmgnKolTraders]: KOL交易者列表
        """
        query = {
            "chain": chain,
            "profit": {"$gte": min_profit}
        }
        if max_profit is not None:
            query["profit"]["$lte"] = max_profit
            
        return await self.find_many(
            query,
            sort=[("profit", DESCENDING)],
            limit=limit
        )
    
    async def find_by_tags(
        self,
        chain: str,
        tags: List[str],
        match_all: bool = True
    ) -> List[GmgnKolTraders]:
        """根据标签获取KOL交易者列表
        
        Args:
            chain: 链名称
            tags: 标签列表
            match_all: 是否需要匹配所有标签
            
        Returns:
            List[GmgnKolTraders]: KOL交易者列表
        """
        if match_all:
            query = {
                "chain": chain,
                "tags": {"$all": tags}
            }
        else:
            query = {
                "chain": chain,
                "tags": {"$in": tags}
            }
            
        return await self.find_many(query)
    
    async def find_active_traders(
        self,
        chain: str,
        since_timestamp: int,
        limit: int = 100
    ) -> List[GmgnKolTraders]:
        """获取活跃KOL交易者列表
        
        Args:
            chain: 链名称
            since_timestamp: 起始时间戳
            limit: 返回数量限制
            
        Returns:
            List[GmgnKolTraders]: KOL交易者列表
        """
        return await self.find_many(
            {
                "chain": chain,
                "last_active_timestamp": {"$gte": since_timestamp}
            },
            sort=[("last_active_timestamp", DESCENDING)],
            limit=limit
        )
    
    async def find_by_holding_amount(
        self,
        chain: str,
        min_amount: float = 0,
        max_amount: Optional[float] = None,
        limit: int = 100
    ) -> List[GmgnKolTraders]:
        """获取指定持仓数量范围内的KOL交易者列表
        
        Args:
            chain: 链名称
            min_amount: 最小持仓数量
            max_amount: 最大持仓数量
            limit: 返回数量限制
            
        Returns:
            List[GmgnKolTraders]: KOL交易者列表
        """
        query = {
            "chain": chain,
            "amount_cur": {"$gte": min_amount}
        }
        if max_amount is not None:
            query["amount_cur"]["$lte"] = max_amount
            
        return await self.find_many(
            query,
            sort=[("amount_cur", DESCENDING)],
            limit=limit
        )
    
    async def upsert_trader(self, chain: str, token_address: str, data: Dict[str, Any]) -> bool:
        """更新或插入KOL交易者信息
        
        Args:
            chain: 链名称
            token_address: 代币地址
            data: API返回的数据
            
        Returns:
            bool: 是否成功
        """
        # 创建新的KOL交易者实例
        trader = await GmgnKolTraders.from_api_data(chain, token_address, data)
        
        # 使用原子更新操作
        return await self.update_one(
            {
                "chain": chain,
                "token_address": token_address,
                "address": trader.address
            },
            trader.dict(),
            upsert=True
        )
    
    async def upsert_traders_many(
        self,
        chain: str,
        data_list: List[Dict[str, Any]]
    ) -> int:
        """批量更新或插入KOL交易者信息
        
        Args:
            chain: 链名称
            token_address: 代币地址
            data_list: API返回的数据列表
            
        Returns:
            int: 更新或插入的记录数
        """
        if not data_list:
            return 0
            
        # 准备批量更新操作
        bulk_operations = []
        
        for data in data_list:
            # 创建新的KOL交易者实例
            trader = await GmgnKolTraders.from_api_data(chain, data)
            
            # 创建更新操作
            bulk_operations.append(
                InsertOne(
                    trader.dict()
                )
            )
        
        # 执行批量更新
        try:
            result = await self.collection.bulk_write(bulk_operations)
            self.logger.info(f"批量更新完成: 修改 {result.modified_count} 条, 插入 {result.upserted_count} 条")
            return result.modified_count + result.upserted_count
        except Exception as e:
            self.logger.error(f"批量更新KOL交易者数据失败: {str(e)}")
            return 0
    
    async def delete_by_address(self, chain: str, address: str) -> bool:
        """根据链和地址删除KOL交易者信息
        
        Args:
            chain: 链名称
            address: 钱包地址
            
        Returns:
            bool: 是否删除成功
        """
        return await self.delete_one({
            "chain": chain,
            "address": address
        })
    
    async def delete_by_token_address(self, chain: str, token_address: str) -> int:
        """根据链和代币地址删除KOL交易者信息
        
        Args:
            chain: 链名称
            token_address: 代币地址
            
        Returns:
            int: 删除的记录数
        """
        result = await self.delete_many({
            "chain": chain,
            "token_address": token_address
        })
        return result.deleted_count if result else 0
    
    async def delete_inactive_traders(self, chain: str, before_timestamp: int) -> int:
        """删除不活跃的KOL交易者
        
        Args:
            chain: 链名称
            before_timestamp: 截止时间戳
            
        Returns:
            int: 删除的记录数
        """
        result = await self.delete_many({
            "chain": chain,
            "last_active_timestamp": {"$lt": before_timestamp}
        })
        return result.deleted_count if result else 0 