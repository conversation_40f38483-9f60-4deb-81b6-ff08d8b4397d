"""飞书消息发送器测试

测试飞书消息发送器的各项功能，包括：
- 文本消息发送
- 卡片消息发送
- 交易摘要卡片生成
- 重试机制
- 错误处理
"""

import pytest
import asyncio
import json
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
from typing import Dict, Any

from utils.message_sender.feishu_sender import FeishuMessageSender


class TestFeishuMessageSender:
    """飞书消息发送器测试类"""
    
    @pytest.fixture
    def webhook_url(self) -> str:
        """测试用的Webhook URL"""
        return "https://open.feishu.cn/open-apis/bot/v2/hook/test-webhook"
    
    @pytest.fixture
    def feishu_sender(self, webhook_url: str) -> FeishuMessageSender:
        """创建飞书消息发送器实例"""
        return FeishuMessageSender(
            webhook_url=webhook_url,
            max_retries=2,
            retry_delay=0.1  # 测试时使用较短的延迟
        )
    
    @pytest.fixture
    def sample_summary_data(self) -> Dict[str, Any]:
        """示例交易摘要数据"""
        return {
            "period_type": "日",
            "start_date": "2024-01-01",
            "end_date": "2024-01-01",
            "overall_stats": {
                "total_trades": 10,
                "win_rate": 70.0,
                "total_pnl_rate": 15.5,
                "avg_pnl_rate": 8.2,
                "total_pnl_amount": 2.5,
                "profitable_trades": 7,
                "loss_trades": 3
            },
            "top_strategies": [
                {
                    "strategy": "gmgn_smart_money",
                    "total_trades": 5,
                    "win_rate": 80.0,
                    "total_pnl_rate": 20.0,
                    "avg_pnl_rate": 12.0
                },
                {
                    "strategy": "manual_trading",
                    "total_trades": 3,
                    "win_rate": 66.7,
                    "total_pnl_rate": 10.0,
                    "avg_pnl_rate": 5.0
                }
            ],
            "extremes": {
                "max_profit": {
                    "pnl_rate": 45.2,
                    "pnl_amount": 1.2,
                    "token": "SOL/USDC"
                },
                "max_loss": {
                    "pnl_rate": 15.8,
                    "pnl_amount": 0.3,
                    "token": "BONK/SOL"
                }
            }
        }
    
    def test_init_with_valid_webhook(self, webhook_url: str):
        """测试使用有效Webhook URL初始化"""
        sender = FeishuMessageSender(webhook_url)
        assert sender.webhook_url == webhook_url
        assert sender.max_retries == 3  # 默认值
        assert sender.retry_delay == 1.0  # 默认值
    
    def test_init_with_empty_webhook(self):
        """测试使用空Webhook URL初始化应该抛出异常"""
        with pytest.raises(ValueError, match="飞书Webhook URL不能为空"):
            FeishuMessageSender("")
    
    def test_init_with_custom_retry_settings(self, webhook_url: str):
        """测试使用自定义重试设置初始化"""
        sender = FeishuMessageSender(
            webhook_url=webhook_url,
            max_retries=5,
            retry_delay=2.0
        )
        assert sender.max_retries == 5
        assert sender.retry_delay == 2.0
    
    @pytest.mark.asyncio
    async def test_send_text_message_success(self, feishu_sender: FeishuMessageSender):
        """测试成功发送文本消息"""
        test_message = "这是一条测试消息"
        
        # Mock HTTP响应
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json = AsyncMock(return_value={"code": 0, "msg": "success"})
        mock_response.text = AsyncMock(return_value='{"code": 0, "msg": "success"}')
        
        with patch('aiohttp.ClientSession.post') as mock_post:
            mock_post.return_value.__aenter__.return_value = mock_response
            
            result = await feishu_sender.send_text_message(test_message)
            
            assert result is True
            mock_post.assert_called_once()
            
            # 验证请求参数
            call_args = mock_post.call_args
            assert call_args[0][0] == feishu_sender.webhook_url
            
            expected_payload = {
                "msg_type": "text",
                "content": {
                    "text": test_message
                }
            }
            assert call_args[1]["json"] == expected_payload
    
    @pytest.mark.asyncio
    async def test_send_text_message_api_error(self, feishu_sender: FeishuMessageSender):
        """测试API返回错误时的处理"""
        test_message = "测试消息"
        
        # Mock HTTP响应 - API错误
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json = AsyncMock(return_value={"code": 1001, "msg": "invalid webhook"})
        mock_response.text = AsyncMock(return_value='{"code": 1001, "msg": "invalid webhook"}')
        
        with patch('aiohttp.ClientSession.post') as mock_post:
            mock_post.return_value.__aenter__.return_value = mock_response
            
            result = await feishu_sender.send_text_message(test_message)
            
            assert result is False
    
    @pytest.mark.asyncio
    async def test_send_text_message_http_error(self, feishu_sender: FeishuMessageSender):
        """测试HTTP错误时的处理"""
        test_message = "测试消息"
        
        # Mock HTTP响应 - HTTP错误
        mock_response = AsyncMock()
        mock_response.status = 404
        mock_response.text = AsyncMock(return_value="Not Found")
        
        with patch('aiohttp.ClientSession.post') as mock_post:
            mock_post.return_value.__aenter__.return_value = mock_response
            
            result = await feishu_sender.send_text_message(test_message)
            
            assert result is False
    
    @pytest.mark.asyncio
    async def test_send_card_message_success(self, feishu_sender: FeishuMessageSender):
        """测试成功发送卡片消息"""
        card_data = {
            "config": {"wide_screen_mode": True},
            "header": {
                "template": "blue",
                "title": {"content": "测试卡片", "tag": "plain_text"}
            },
            "elements": [
                {
                    "tag": "div",
                    "text": {"content": "测试内容", "tag": "lark_md"}
                }
            ]
        }
        
        # Mock HTTP响应
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json = AsyncMock(return_value={"code": 0, "msg": "success"})
        mock_response.text = AsyncMock(return_value='{"code": 0, "msg": "success"}')
        
        with patch('aiohttp.ClientSession.post') as mock_post:
            mock_post.return_value.__aenter__.return_value = mock_response
            
            result = await feishu_sender.send_card_message(card_data)
            
            assert result is True
            
            # 验证请求参数
            call_args = mock_post.call_args
            expected_payload = {
                "msg_type": "interactive",
                "card": card_data
            }
            assert call_args[1]["json"] == expected_payload
    
    @pytest.mark.asyncio
    async def test_send_trading_summary_card_success(
        self, 
        feishu_sender: FeishuMessageSender,
        sample_summary_data: Dict[str, Any]
    ):
        """测试成功发送交易摘要卡片"""
        report_url = "https://example.com/report.html"
        
        # Mock HTTP响应
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json = AsyncMock(return_value={"code": 0, "msg": "success"})
        mock_response.text = AsyncMock(return_value='{"code": 0, "msg": "success"}')
        
        with patch('aiohttp.ClientSession.post') as mock_post:
            mock_post.return_value.__aenter__.return_value = mock_response
            
            result = await feishu_sender.send_trading_summary_card(
                sample_summary_data, 
                report_url
            )
            
            assert result is True
            
            # 验证请求参数
            call_args = mock_post.call_args
            payload = call_args[1]["json"]
            
            assert payload["msg_type"] == "interactive"
            assert "card" in payload
            
            card = payload["card"]
            assert "header" in card
            assert "elements" in card
            
            # 验证卡片标题
            assert "交易日报" in card["header"]["title"]["content"]
            
            # 验证是否包含报告链接按钮
            has_button = any(
                element.get("tag") == "action" 
                for element in card["elements"]
            )
            assert has_button
    
    @pytest.mark.asyncio
    async def test_send_trading_summary_card_without_report_url(
        self, 
        feishu_sender: FeishuMessageSender,
        sample_summary_data: Dict[str, Any]
    ):
        """测试发送交易摘要卡片（不包含报告链接）"""
        # Mock HTTP响应
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json = AsyncMock(return_value={"code": 0, "msg": "success"})
        mock_response.text = AsyncMock(return_value='{"code": 0, "msg": "success"}')
        
        with patch('aiohttp.ClientSession.post') as mock_post:
            mock_post.return_value.__aenter__.return_value = mock_response
            
            result = await feishu_sender.send_trading_summary_card(
                sample_summary_data, 
                None  # 无报告链接
            )
            
            assert result is True
            
            # 验证请求参数
            call_args = mock_post.call_args
            payload = call_args[1]["json"]
            card = payload["card"]
            
            # 验证不包含报告链接按钮
            has_button = any(
                element.get("tag") == "action" 
                for element in card["elements"]
            )
            assert not has_button
    
    def test_build_trading_summary_card_structure(
        self, 
        feishu_sender: FeishuMessageSender,
        sample_summary_data: Dict[str, Any]
    ):
        """测试交易摘要卡片结构"""
        report_url = "https://example.com/report.html"
        
        card = feishu_sender._build_trading_summary_card(
            sample_summary_data, 
            report_url
        )
        
        # 验证基本结构
        assert "config" in card
        assert "header" in card
        assert "elements" in card
        
        # 验证配置
        assert card["config"]["wide_screen_mode"] is True
        
        # 验证头部
        header = card["header"]
        assert header["template"] == "blue"
        assert "交易日报" in header["title"]["content"]
        assert "2024-01-01" in header["subtitle"]["content"]
        
        # 验证元素
        elements = card["elements"]
        assert len(elements) > 0
        
        # 验证包含总体统计
        has_overall_stats = any(
            element.get("tag") == "div" and 
            any(field.get("text", {}).get("content", "").startswith("**总交易次数**") 
                for field in element.get("fields", []))
            for element in elements
        )
        assert has_overall_stats
        
        # 验证包含策略排行
        has_strategy_ranking = any(
            "策略排行榜" in element.get("text", {}).get("content", "")
            for element in elements
        )
        assert has_strategy_ranking
        
        # 验证包含报告链接按钮
        has_button = any(
            element.get("tag") == "action"
            for element in elements
        )
        assert has_button
    
    @pytest.mark.asyncio
    async def test_retry_mechanism(self, feishu_sender: FeishuMessageSender):
        """测试重试机制"""
        test_message = "测试重试"
        
        # Mock网络错误，然后成功
        mock_responses = [
            # 第一次请求失败
            Exception("Network error"),
            # 第二次请求成功
            AsyncMock(
                status=200,
                json=AsyncMock(return_value={"code": 0, "msg": "success"}),
                text=AsyncMock(return_value='{"code": 0, "msg": "success"}')
            )
        ]
        
        call_count = 0
        async def mock_post_side_effect(*args, **kwargs):
            nonlocal call_count
            response = mock_responses[call_count]
            call_count += 1
            if isinstance(response, Exception):
                raise response
            return response
        
        with patch('aiohttp.ClientSession.post') as mock_post:
            mock_post.return_value.__aenter__.side_effect = mock_post_side_effect
            
            result = await feishu_sender.send_text_message(test_message)
            
            assert result is True
            assert call_count == 2  # 第一次失败，第二次成功
    
    @pytest.mark.asyncio
    async def test_max_retries_exceeded(self, feishu_sender: FeishuMessageSender):
        """测试超过最大重试次数"""
        test_message = "测试最大重试"
        
        # Mock所有请求都失败
        with patch('aiohttp.ClientSession.post') as mock_post:
            mock_post.return_value.__aenter__.side_effect = Exception("Network error")
            
            result = await feishu_sender.send_text_message(test_message)
            
            assert result is False
            # 验证调用次数：初始调用 + 重试次数
            assert mock_post.call_count == feishu_sender.max_retries + 1
    
    @pytest.mark.asyncio
    async def test_send_message_to_user_warning(self, feishu_sender: FeishuMessageSender):
        """测试发送消息给用户的警告处理"""
        with patch.object(feishu_sender, 'send_text_message', return_value=True) as mock_send:
            result = await feishu_sender.send_message_to_user("测试消息", "user123")
            
            assert result is True
            mock_send.assert_called_once_with("测试消息")
    
    @pytest.mark.asyncio
    async def test_send_message_to_channel_text_mode(self, feishu_sender: FeishuMessageSender):
        """测试发送消息到频道（文本模式）"""
        with patch.object(feishu_sender, 'send_text_message', return_value=True) as mock_send:
            result = await feishu_sender.send_message_to_channel(
                "测试消息", 
                "channel123", 
                "text"
            )
            
            assert result is True
            mock_send.assert_called_once_with("测试消息")
    
    @pytest.mark.asyncio
    async def test_send_message_to_channel_card_mode(self, feishu_sender: FeishuMessageSender):
        """测试发送消息到频道（卡片模式）"""
        card_json = json.dumps({
            "header": {"title": {"content": "测试卡片"}},
            "elements": []
        })
        
        with patch.object(feishu_sender, 'send_card_message', return_value=True) as mock_send:
            result = await feishu_sender.send_message_to_channel(
                card_json, 
                "channel123", 
                "card"
            )
            
            assert result is True
            mock_send.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_send_message_to_channel_invalid_card_json(self, feishu_sender: FeishuMessageSender):
        """测试发送无效JSON卡片消息的降级处理"""
        invalid_json = "这不是有效的JSON"
        
        with patch.object(feishu_sender, 'send_text_message', return_value=True) as mock_send:
            result = await feishu_sender.send_message_to_channel(
                invalid_json, 
                "channel123", 
                "card"
            )
            
            assert result is True
            mock_send.assert_called_once_with(invalid_json) 