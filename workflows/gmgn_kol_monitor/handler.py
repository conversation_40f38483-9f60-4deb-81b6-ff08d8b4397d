import logging
import traceback
from typing import List, Dict, Any, Optional, AsyncGenerator
from datetime import datetime

from models.config import Config, GmgnApiConfig, GmgnAccountConfig # 确保导入GmgnAccountConfig
from utils.spiders.smart_money.gmgn_following_kols_spider import GmgnFollowingKolsSpider
from dao.gmgn_following_kol_dao import GmgnFollowingKolDAO
from dao.kol_wallet_dao import KOLWalletDAO

logger = logging.getLogger("GmgnKolMonitorHandler")

async def generate_refresh_tokens() -> AsyncGenerator[Optional[Dict[str, str]], None]:
    """
    从数据库配置中获取GMGN API的账户配置列表（包含refresh_token和device_id）。
    每个账户配置包装成一个字典，方便后续节点处理。
    """
    try:
        logger.info("开始从数据库获取GMGN账户配置列表")
        gmgn_config_doc = await Config.find_one(Config.type == "gmgn_api")

        if not gmgn_config_doc:
            logger.warning("数据库中未找到类型为 'gmgn_api' 的配置")
            yield None
            return

        if not isinstance(gmgn_config_doc.data, GmgnApiConfig):
            logger.error(f"配置 'gmgn_api' 的data字段类型不是 GmgnApiConfig，而是 {type(gmgn_config_doc.data)}")
            yield None
            return

        accounts = gmgn_config_doc.data.accounts # accounts 是 List[GmgnAccountConfig]
        if not accounts:
            logger.warning("类型为 'gmgn_api' 的配置中未包含账户信息 (accounts)")
            yield None
            return
        
        refreshed_account_count = 0
        for acc_config in accounts:
            if acc_config.refresh_token and acc_config.device_id:
                yield {
                    "refresh_token": acc_config.refresh_token,
                    "device_id": acc_config.device_id
                }
                refreshed_account_count += 1
            else:
                logger.warning(f"配置中存在无效的账户信息（缺少refresh_token或device_id）：{acc_config}")
        
        if refreshed_account_count == 0 and accounts:
             logger.warning("所有账户配置均无效或不完整，未生成任何refresh token数据")
             yield None
        elif refreshed_account_count > 0:
            logger.info(f"成功生成 {refreshed_account_count} 个有效的GMGN账户配置供处理")

    except Exception as e:
        logger.error(f"获取GMGN账户配置列表时发生错误: {str(e)}\n{traceback.format_exc()}")
        yield None

async def fetch_kols(account_data: Dict[str, str]) -> AsyncGenerator[Optional[Dict[str, List[Dict[str, Any]]]], None]:
    """
    使用给定的账户配置（包含refresh_token和device_id）从GMGN API获取关注的KOL列表。
    
    Args:
        account_data: 包含 'refresh_token' 和 'device_id' 的字典
        
    Returns:
        AsyncGenerator yielding Optional[Dict[str, List[Dict[str, Any]]]]: 获取的KOL列表包装在字典中，如果失败则为None
    """
    refresh_token = account_data.get("refresh_token")
    device_id = account_data.get("device_id")

    if not refresh_token or not device_id:
        logger.error(f"获取KOL列表时未提供完整的账户信息（refresh_token或device_id）。收到：{account_data}")
        yield None
        return

    logger.info(f"开始使用refresh_token (前10位: {refresh_token[:10]}...) 和 device_id ({device_id}) 获取KOL列表")
    try:
        current_account_config = GmgnAccountConfig(refresh_token=refresh_token, device_id=device_id)
        
        async with GmgnFollowingKolsSpider(account_config=current_account_config, max_retries=3, retry_interval=2.0) as spider:
            raw_kols_data = await spider.get_following_kols()
            if not raw_kols_data:
                logger.warning(f"使用账户 (token {refresh_token[:10]}..., device_id {device_id}) 未获取到KOL数据")
                yield None
                return
            
            formatted_kols_data = spider.format_following_kols(raw_kols_data)
            if not formatted_kols_data:
                logger.warning(f"使用账户 (token {refresh_token[:10]}..., device_id {device_id}) 获取到的KOL数据格式化后为空")
                yield None
                return
                
            logger.info(f"成功获取并格式化了 {len(formatted_kols_data)} 个KOL数据 (使用账户 (token {refresh_token[:10]}..., device_id {device_id}))")
            
            formatted_kols_data.sort(
                key=lambda kol: kol.get('last_active_timestamp', 0) or 0, 
                reverse=True
            )
            
            yield {"kols": formatted_kols_data}
            
    except Exception as e:
        logger.error(f"使用账户 (token {refresh_token[:10]}..., device_id {device_id}) 获取KOL列表时发生错误: {str(e)}\n{traceback.format_exc()}")
        yield None

async def filter_new_active_kols(kol_data_list: Dict[str, Any]) -> AsyncGenerator[Optional[List[Dict[str, Any]]], None]:
    """
    根据last_active_timestamp比较API返回数据与数据库中的数据，
    筛选出有新活动的KOL数据，更新到数据库，然后返回这些数据。
    
    Args:
        kol_data_list: KOL数据字典列表
        
    Returns:
        AsyncGenerator yielding Optional[List[Dict[str, Any]]]: 更新到数据库的有新活动的KOL列表，如果失败则返回None
    """
    kol_data_list = kol_data_list.get("kols")
    if not kol_data_list:
        logger.warning("筛选KOL活动时接收到空数据列表")
        yield None
        
    try:
        # 从数据库中获取现有的KOL数据，按照last_active_timestamp降序排序
        try:
            
            # 提取kol_data_list中的所有地址
            addresses = [kol.get('address') for kol in kol_data_list if kol.get('address')]
            if not addresses:
                logger.warning("kol_data_list中没有有效的地址")
                yield kol_data_list  # 如果没有地址，则直接返回原始数据
                
            # 使用$in查询只获取kol_data_list中包含的地址
            filter_dict = {"address": {"$in": addresses}}
            logger.info(f"构建查询参数: 查询{len(addresses)}个特定地址的KOL数据")
            
            # 使用字典形式传递参数，确保与DAO函数定义匹配
            db_kols = await GmgnFollowingKolDAO.find_kols(
                filter_dict=filter_dict,
                sort_by=[("last_active_timestamp", -1)],  # 确保这是一个列表，包含元组
                limit=None  # 不限制数量，获取所有匹配的记录
            )
            
            # 如果db_kols为None，表示查询出错，但上面的测试查询成功了，这种情况很奇怪
            if db_kols is None:
                logger.error("获取数据库KOL列表返回None，可能是类型错误或数据库问题")
                # 返回原始数据的一部分，避免全量更新
                yield kol_data_list[:10]
            
            logger.info(f"从数据库获取了 {len(db_kols)} 条符合条件的现有KOL数据用于比较")
            
            # 创建数据库KOL的地址 -> last_active_timestamp 映射
            db_kol_timestamps = {
                kol.address: kol.last_active_timestamp 
                for kol in db_kols 
                if kol.last_active_timestamp is not None
            }
            
            # 筛选出需要更新的KOL（活动时间较新的）
            kols_to_update = []
            for kol in kol_data_list:
                address = kol.get('address')
                if not address:
                    continue
                    
                new_timestamp = kol.get('last_active_timestamp')
                if new_timestamp is None:
                    # 如果新数据没有时间戳，也添加（可能是首次获取）
                    kols_to_update.append(kol)
                    continue
                    
                # 如果是新地址或时间戳更新，则添加到更新列表
                db_timestamp = db_kol_timestamps.get(address)
                if db_timestamp is None or new_timestamp > db_timestamp:
                    kols_to_update.append(kol)
            
            logger.info(f"筛选出 {len(kols_to_update)} 个有新活动的KOL数据需要更新")
            
            # 直接在此处将筛选出的KOL数据保存到数据库
            if kols_to_update:
                try:
                    # 确保kols_to_update是列表
                    save_data = kols_to_update if isinstance(kols_to_update, list) else [kols_to_update]
                    
                    # 批量保存有效的KOL数据
                    saved_count = await GmgnFollowingKolDAO.batch_upsert_kols(save_data)
                    logger.info(f"已成功将 {saved_count} 个有新活动的KOL数据更新到数据库")
                    
                except Exception as e:
                    logger.error(f"更新KOL数据到数据库时发生错误: {str(e)}\n{traceback.format_exc()}")
                    # 即使更新失败，仍然返回筛选出的数据，让下游节点可以尝试保存
            
            # 返回筛选出的数据供下游节点使用
            yield kols_to_update
            
        except Exception as e:
            logger.error(f"获取并比较数据库KOL数据时出错: {str(e)}\n{traceback.format_exc()}")
            # 返回一部分数据，避免不必要的全量更新
            # 如果必须返回数据，只返回前10条，减少不必要的处理
            sample_data = kol_data_list[:10] if len(kol_data_list) > 10 else kol_data_list
            logger.info(f"由于数据库比较错误，只返回样本数据 ({len(sample_data)} 条)")
            yield sample_data
            
    except Exception as e:
        logger.error(f"筛选KOL活动时发生错误: {str(e)}\n{traceback.format_exc()}")
        yield None

async def convert_to_kol_wallet(kol_data_list: Dict[str, Any]) -> AsyncGenerator[Optional[List[Dict[str, Any]]], None]:
    """
    将GmgnFollowingKol数据转换为KOLWallet格式。
    
    Args:
        kol_data_list: GmgnFollowingKol格式的KOL数据字典列表
        
    Returns:
        AsyncGenerator yielding Optional[List[Dict[str, Any]]]: 转换后的KOLWallet格式数据列表，如果失败则返回None
    """
    kol_data_list = kol_data_list.get("kols")
    if not kol_data_list:
        logger.warning("转换KOL数据时接收到空数据列表")
        yield None
        
    try:
        logger.info(f"开始将 {len(kol_data_list)} 个GmgnFollowingKol数据转换为KOLWallet格式")
        
        current_time = datetime.utcnow()
        
        for kol in kol_data_list:
            # 提取基本数据
            address = kol.get('address')
            if not address:
                logger.warning(f"跳过缺少地址的KOL数据: {kol}")
                continue
                
            # 转换为KOLWallet格式
            kol_wallet = {
                "wallet_address": address,  # 主键
                "address": address,         # 冗余字段
                "chain": "sol",        # 假设GMGN关注的KOL主要在以太坊链上
                
                # 用户信息
                "twitter_username": kol.get('twitter_username'),
                "twitter_name": kol.get('twitter_name'),
                "name": kol.get('name'),
                "is_blue_verified": kol.get('is_blue_verified'),
                "avatar": kol.get('avatar'),
                "ens": kol.get('ens'),
                
                # 标签信息
                "tags": ["kol"] + (kol.get('tags', []) or []),  # 确保包含kol标签
                "tag_rank": kol.get('tag_rank'),
                
                # 余额信息
                "balance": kol.get('balance', 0.0),
                "eth_balance": kol.get('eth_balance', 0.0),
                "sol_balance": kol.get('sol_balance', 0.0),
                "trx_balance": kol.get('trx_balance', 0.0),
                
                # 收益指标
                "realized_profit": kol.get('total_profit', 0.0),
                "realized_profit_1d": kol.get('realized_profit_1d', 0.0),
                "realized_profit_7d": kol.get('realized_profit_7d', 0.0),
                "realized_profit_30d": kol.get('realized_profit_30d', 0.0),
                
                # 活动数据
                "txs_1d": kol.get('swaps_1d', 0),
                "txs_7d": kol.get('swaps_7d', 0),
                "txs": kol.get('swaps_7d', 0),
                "txs_30d": kol.get('swaps_30d', 0),
                
                # 时间信息
                "followers_count": kol.get('followers_count', 0),
                
                # 标记来源，这是从关注账号列表导入的
                "imported_from_following": True,
                "last_active": kol.get('last_active_timestamp', 0),
                "last_updated_at": current_time
            }
            
            yield kol_wallet
            
    except Exception as e:
        logger.error(f"转换KOL数据时发生错误: {str(e)}\n{traceback.format_exc()}")
        yield None

async def store_kol_wallet_data(kol_wallet_list: List[Dict[str, Any]]) -> int:
    """
    将KOLWallet数据列表保存到数据库。
    
    Args:
        kol_wallet_list: KOLWallet格式的数据字典列表
        
    Returns:
        int: 成功存储的数量
    """
    if not kol_wallet_list:
        logger.warning("接收到空的KOLWallet数据列表，不执行存储操作")
        return 0
        
    # 确保输入是列表类型
    if not isinstance(kol_wallet_list, list):
        logger.error(f"KOLWallet数据应为列表，实际类型为: {type(kol_wallet_list)}")
        # 如果是字典，尝试转换为列表
        if isinstance(kol_wallet_list, dict):
            kol_wallet_list = [kol_wallet_list]
        else:
            return 0
        
    try:
        logger.info(f"准备将 {len(kol_wallet_list)} 个KOLWallet数据保存到数据库")
        
        # 执行数据保存
        dao = KOLWalletDAO()
        saved_count = await dao.upsert_wallets(kol_wallet_list)
        logger.info(f"成功将 {saved_count} 个KOLWallet数据保存到数据库")
        
        return saved_count
    except Exception as e:
        logger.error(f"保存KOLWallet数据时出错: {str(e)}\n{traceback.format_exc()}")
        return 0

async def validate_kol_wallet_data(data: Dict[str, Any]) -> bool:
    """
    验证单个KOLWallet数据是否有效。
    
    Args:
        data: 待验证的KOLWallet数据字典
        
    Returns:
        bool: 数据有效返回True，否则返回False
    """
    if not data:
        logger.warning("KOLWallet数据验证：输入数据为空")
        return False
        
    if not isinstance(data, dict):
        logger.warning(f"KOLWallet数据验证：输入数据不是字典，而是 {type(data)}")
        return False
    
    # 验证必要字段是否存在
    if 'wallet_address' not in data:
        logger.warning(f"KOLWallet数据验证：字典缺少必要字段'wallet_address'")
        return False
    
    return True

async def store_kol_data(kol_data_list: List[Dict[str, Any]]) -> int:
    """
    将KOL数据列表保存到数据库。
    
    Args:
        kol_data_list: KOL数据字典列表
        
    Returns:
        int: 成功存储的KOL数量
    """
    if not kol_data_list:
        logger.warning("接收到空的KOL数据列表，不执行存储操作")
        return 0
        
    # 确保输入是列表类型
    if not isinstance(kol_data_list, list):
        logger.error(f"KOL数据应为列表，实际类型为: {type(kol_data_list)}")
        # 如果是字典，尝试转换为列表
        if isinstance(kol_data_list, dict):
            kol_data_list = [kol_data_list]
        else:
            return 0
        
    try:
        logger.info(f"准备将 {len(kol_data_list)} 个KOL数据保存到数据库")
        
        # 遍历列表中的每个KOL数据进行验证
        valid_kols = []
        for kol in kol_data_list:
            if await validate_kol_data(kol):
                valid_kols.append(kol)
            else:
                logger.warning(f"KOL数据验证失败，跳过: {kol.get('address', '未知地址')}")
        
        if not valid_kols:
            logger.error("所有KOL数据验证失败，放弃保存")
            return 0
            
        # 执行数据保存
        saved_count = await GmgnFollowingKolDAO.batch_upsert_kols(valid_kols)
        logger.info(f"成功将 {saved_count} 个KOL数据保存到数据库")
        
        return saved_count
    except Exception as e:
        logger.error(f"保存KOL数据时出错: {str(e)}\n{traceback.format_exc()}")
        return 0

async def validate_kol_data(data: Dict[str, Any]) -> bool:
    """
    验证单个KOL数据是否有效。
    
    Args:
        data: 待验证的KOL数据字典
        
    Returns:
        bool: 数据有效返回True，否则返回False
    """
    if not data:
        logger.warning("KOL数据验证：输入数据为空")
        return False
        
    if not isinstance(data, dict):
        logger.warning(f"KOL数据验证：输入数据不是字典，而是 {type(data)}")
        return False
    
    # 验证必要字段是否存在
    if 'address' not in data:
        logger.warning(f"KOL数据验证：字典缺少必要字段'address'")
        return False
    
    return True 