from typing import Dict, Any, List
from beanie import Document, Indexed
from pydantic import Field
from utils.basic_utils import convert_str_to_float

class GmgnSmartActivity(Document):
    """GMGN KOL活动模型
    
    用于存储代币的聪明钱活动信息，包括：
    - 交易者地址
    - 交易金额和数量
    - 交易事件（买入/卖出）
    - 利润和收益率
    - 交易者标签
    - 活跃时间
    """
    
    # 基本信息
    chain: str = Field(description="链名称")
    token_address: Indexed(str) = Field(description="代币地址")
    maker: str = Field(description="交易者地址")
    base_amount: float = Field(default=0, description="基础金额")
    quote_amount: float = Field(default=0, description="报价金额")
    quote_symbol: str = Field(default="", description="报价符号")
    quote_address: str = Field(default="", description="报价地址")
    amount_usd: float = Field(default=0, description="交易金额（USD）")
    event: str = Field(description="交易事件")
    timestamp: int = Field(description="交易时间戳")
    tx_hash: str = Field(description="交易哈希")
    price_usd: float = Field(default=0, description="价格（USD）")
    total_trade: int = Field(default=0, description="总交易次数")
    id: str = Field(default="", description="交易ID")
    is_following: int = Field(default=0, description="是否关注")
    is_open_or_close: int = Field(default=0, description="是否开仓或平仓")
    buy_cost_usd: str = Field(default="", description="买入成本（USD）")
    balance: str = Field(default="0", description="余额")
    cost: str = Field(default="0", description="成本")
    history_bought_amount: float = Field(default=0, description="历史买入数量")
    history_sold_income: float = Field(default=0, description="历史卖出收入")
    history_sold_amount: float = Field(default=0, description="历史卖出数量")
    realized_profit: float = Field(default=0, description="已实现利润")
    unrealized_profit: float = Field(default=0, description="未实现利润")
    maker_tags: List[str] = Field(default_factory=list, description="交易者标签")
    maker_token_tags: List[str] = Field(default_factory=list, description="交易者代币标签")
    maker_name: str = Field(default="", description="交易者名称")
    maker_twitter_username: str = Field(default="", description="交易者Twitter用户名")
    maker_twitter_name: str = Field(default="", description="交易者Twitter显示名")
    maker_avatar: str = Field(default="", description="交易者头像URL")
    maker_ens: str = Field(default="", description="交易者ENS")
    
    # 数据库配置
    class Settings:
        name = "gmgn_smart_activity"
        use_state_management = True
    
    @classmethod
    async def from_api_data(cls, data: Dict[str, Any]) -> 'GmgnSmartActivity':
        """从API数据创建模型实例
        
        Args:
            data: API返回的数据
            
        Returns:
            GmgnSmartActivity: 模型实例
        """
        return cls(
            chain=data.get('chain', ''),
            token_address=data.get('token_address', ''),
            maker=data.get('maker', ''),
            base_amount=convert_str_to_float(data.get('base_amount', 0)),
            quote_amount=convert_str_to_float(data.get('quote_amount', 0)),
            quote_symbol=data.get('quote_symbol', ''),
            quote_address=data.get('quote_address', ''),
            amount_usd=convert_str_to_float(data.get('amount_usd', 0)),
            event=data.get('event', ''),
            timestamp=int(data.get('timestamp', 0)),
            tx_hash=data.get('tx_hash', ''),
            price_usd=convert_str_to_float(data.get('price_usd', 0)),
            total_trade=int(data.get('total_trade', 0)),
            id=data.get('id', ''),
            is_following=int(data.get('is_following', 0)),
            is_open_or_close=int(data.get('is_open_or_close', 0)),
            buy_cost_usd=data.get('buy_cost_usd', ''),
            balance=data.get('balance', '0'),
            cost=data.get('cost', '0'),
            history_bought_amount=convert_str_to_float(data.get('history_bought_amount', 0)),
            history_sold_income=convert_str_to_float(data.get('history_sold_income', 0)),
            history_sold_amount=convert_str_to_float(data.get('history_sold_amount', 0)),
            realized_profit=convert_str_to_float(data.get('realized_profit', 0)),
            unrealized_profit=convert_str_to_float(data.get('unrealized_profit', 0)),
            maker_tags=data.get('maker_tags', []),
            maker_token_tags=data.get('maker_token_tags', []),
            maker_name=data.get('maker_name', ''),
            maker_twitter_username=data.get('maker_twitter_username', ''),
            maker_twitter_name=data.get('maker_twitter_name', ''),
            maker_avatar=data.get('maker_avatar', ''),
            maker_ens=data.get('maker_ens', '')
        )
    
    class Config:
        json_schema_extra = {
            "example": {
                "chain": "sol",
                "token_address": "CniPCE4b3s8gSUPhUiyMjXnytrEqUrMfSsnbBjLCpump",
                "maker": "DBcgDzbycdUdSchW4rx2ekfF5GqfwytfF9DrWhGNi4yS",
                "base_amount": 88.055873,
                "quote_amount": 0.027264263,
                "quote_symbol": "",
                "quote_address": "So11111111111111111111111111111111111111112",
                "amount_usd": 3.50291251024,
                "event": "sell",
                "timestamp": 1741786411,
                "tx_hash": "2q8eJAQ4naeRfJLRAnfe3y6SsBhnChb5p236NSsjkTbywYyn9HseE7bLRFnPsip7FJVqJUUNSJfVgjkbFjhnMNS2",
                "price_usd": 0.039780566484649584,
                "total_trade": 16,
                "id": "MDAzMjY0MjE0MTExNDE3MDAwMA==",
                "is_following": 0,
                "is_open_or_close": 1,
                "buy_cost_usd": "",
                "balance": "0",
                "cost": "0",
                "history_bought_amount": 908444.448631,
                "history_sold_income": 75392.491973825,
                "history_sold_amount": 2146824.921138,
                "realized_profit": 13279.42800224894642304296,
                "unrealized_profit": 0,
                "maker_tags": ["kol", "gmgn", "pepeboost"],
                "maker_token_tags": [],
                "maker_name": "Weijun",
                "maker_twitter_username": "zhuangweijun",
                "maker_twitter_name": "Weijun",
                "maker_avatar": "https://pbs.twimg.com/profile_images/1899393941176471552/LmXFp51n.jpg",
                "maker_ens": ""
            }
        } 