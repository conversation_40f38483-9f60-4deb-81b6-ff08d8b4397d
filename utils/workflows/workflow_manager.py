import asyncio
import logging
from typing import Dict, List, Optional, Any
import signal
import traceback
from datetime import datetime

# 修改导入语句，使用相对导入
from .workflow import Workflow

class WorkflowManager:
    """工作流管理器
    
    负责管理和运行多个工作流
    """
    
    def __init__(self):
        self.logger = logging.getLogger("WorkflowManager")
        self.workflows: Dict[str, Workflow] = {}
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self._stop = False
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, sig, frame):
        """处理信号"""
        self.logger.info(f"收到信号 {sig}，准备停止所有工作流")
        self.stop_all()
    
    def register_workflow(self, workflow: Workflow) -> None:
        """注册工作流"""
        if workflow.name in self.workflows:
            raise ValueError(f"工作流 {workflow.name} 已存在")
        
        self.workflows[workflow.name] = workflow
        self.logger.info(f"工作流 {workflow.name} 已注册")
    
    def unregister_workflow(self, workflow_name: str) -> None:
        """注销工作流"""
        if workflow_name not in self.workflows:
            self.logger.warning(f"工作流 {workflow_name} 不存在")
            return
        
        # 如果工作流正在运行，先停止它
        if workflow_name in self.running_tasks:
            self.stop_workflow(workflow_name)
        
        del self.workflows[workflow_name]
        self.logger.info(f"工作流 {workflow_name} 已注销")
    
    async def start_workflow(self, workflow_name: str) -> None:
        """启动工作流"""
        if workflow_name not in self.workflows:
            raise ValueError(f"工作流 {workflow_name} 不存在")
        
        if workflow_name in self.running_tasks:
            self.logger.warning(f"工作流 {workflow_name} 已经在运行")
            return
        
        workflow = self.workflows[workflow_name]
        
        # 创建运行任务
        task = asyncio.create_task(workflow.run())
        self.running_tasks[workflow_name] = task
        
        self.logger.info(f"工作流 {workflow_name} 已启动")
    
    def stop_workflow(self, workflow_name: str) -> None:
        """停止工作流"""
        if workflow_name not in self.workflows:
            self.logger.warning(f"工作流 {workflow_name} 不存在")
            return
        
        if workflow_name not in self.running_tasks:
            self.logger.warning(f"工作流 {workflow_name} 未运行")
            return
        
        workflow = self.workflows[workflow_name]
        task = self.running_tasks[workflow_name]
        
        # 停止工作流
        workflow.stop()
        
        # 取消任务
        if not task.done():
            task.cancel()
        
        # 从运行任务列表中移除
        del self.running_tasks[workflow_name]
        
        self.logger.info(f"工作流 {workflow_name} 已停止")
    
    async def start_all(self) -> None:
        """启动所有工作流"""
        self.logger.info("开始启动所有工作流")
        
        for workflow_name in self.workflows:
            await self.start_workflow(workflow_name)
    
    def stop_all(self) -> None:
        """停止所有工作流"""
        self._stop = True
        self.logger.info("开始停止所有工作流")
        
        for workflow_name in list(self.running_tasks.keys()):
            self.stop_workflow(workflow_name)
    
    async def run(self) -> None:
        """运行工作流管理器"""
        self.logger.info("工作流管理器开始运行")
        
        try:
            # 启动所有工作流
            await self.start_all()
            
            # 等待所有工作流完成或被停止
            while not self._stop and self.running_tasks:
                # 检查是否有任务完成
                done_tasks = []
                for workflow_name, task in self.running_tasks.items():
                    if task.done():
                        done_tasks.append(workflow_name)
                        
                        # 检查任务是否有异常
                        try:
                            task.result()
                        except asyncio.CancelledError:
                            self.logger.info(f"工作流 {workflow_name} 被取消")
                        except Exception as e:
                            self.logger.error(f"工作流 {workflow_name} 运行出错: {str(e)}\n{traceback.format_exc()}")
                
                # 从运行任务列表中移除已完成的任务
                for workflow_name in done_tasks:
                    del self.running_tasks[workflow_name]
                
                # 等待一段时间
                await asyncio.sleep(1)
                
        except Exception as e:
            self.logger.error(f"工作流管理器运行出错: {str(e)}\n{traceback.format_exc()}")
        finally:
            # 确保所有工作流都被停止
            self.stop_all()
            self.logger.info("工作流管理器已停止")

# 创建工作流管理器实例
workflow_manager = WorkflowManager()

# 命令行入口
if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 导入工作流
    from .gmgn_token_links_workflow import create_gmgn_token_links_workflow
    
    async def main():
        try:
            # 创建工作流
            gmgn_workflow = await create_gmgn_token_links_workflow()
            
            # 注册工作流
            workflow_manager.register_workflow(gmgn_workflow)
            
            # 运行工作流管理器
            await workflow_manager.run()
            
        except Exception as e:
            logging.error(f"程序运行出错: {str(e)}\n{traceback.format_exc()}")
            raise
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logging.info("程序已停止")
    except Exception as e:
        logging.error(f"程序运行出错: {str(e)}")
        raise 