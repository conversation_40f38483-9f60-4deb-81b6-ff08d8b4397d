from typing import Optional, Dict, Any
from datetime import datetime
from models.monitor_alert_state import Monitor<PERSON>lertState
from .base_dao import BaseDAO

class MonitorAlertStateDAO(BaseDAO[MonitorAlertState]):
    """DAO for MonitorAlertState model."""

    def __init__(self):
        super().__init__(MonitorAlertState)

    async def get_alert_state(self, monitor_type: str, category: str) -> Optional[MonitorAlertState]:
        """Retrieve a specific alert state."""
        return await self.find_one({
            "monitor_type": monitor_type,
            "category": category
        })

    async def upsert_alert_state(
        self,
        monitor_type: str,
        category: str,
        consecutive_alerts: int,
        last_notification_time: Optional[datetime]
    ) -> MonitorAlertState:
        """Update or insert an alert state."""
        state = await self.get_alert_state(monitor_type, category)
        if state:
            state.consecutive_alerts = consecutive_alerts
            state.last_notification_time = last_notification_time
            await state.save()
        else:
            state = MonitorAlertState(
                monitor_type=monitor_type,
                category=category,
                consecutive_alerts=consecutive_alerts,
                last_notification_time=last_notification_time
            )
            await self.insert_one(state)
        return state

    async def reset_consecutive_alerts(self, monitor_type: str, category: str) -> Optional[MonitorAlertState]:
        """Reset consecutive alerts for a specific monitor and category."""
        state = await self.get_alert_state(monitor_type, category)
        if state:
            state.consecutive_alerts = 0
            await state.save()
            return state
        return None 