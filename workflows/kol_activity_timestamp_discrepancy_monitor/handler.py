import logging
from datetime import datetime, timezone, timedelta
from typing import Optional, Dict, Any, List, Tuple

from models.config import Config, ApplicationConfig, KolActivityMonitorConfig
from models.kol_wallet import KOLWallet
from models.kol_wallet_activity import KOLWalletActivity
from models.monitor_alert_state import Monitor<PERSON>lertState
from models.alert_event_record import AlertEventRecord
from models.notification_log_record import NotificationLogRecord
from utils.message_sender.message_sender import TelegramMessageSender
from dao.kol_wallet_dao import KOLWalletDAO
from dao.kol_wallet_activity_dao import KOLWalletActivityDAO
from dao.monitor_alert_state_dao import MonitorAlertStateDAO
from dao.alert_event_record_dao import AlertEventRecordDAO
from dao.notification_log_record_dao import NotificationLogRecordDAO
from dao.config_dao import ConfigDAO

logger = logging.getLogger(__name__)
kol_wallet_dao = KOLWalletDAO()
kol_wallet_activity_dao = KOLWalletActivityDAO()
monitor_alert_state_dao = MonitorAlertStateDAO()
alert_event_record_dao = AlertEventRecordDAO()
notification_log_record_dao = NotificationLogRecordDAO()
config_dao = ConfigDAO()

class KolActivityTimestampDiscrepancyChecker:
    """
    Checks for timestamp discrepancies in KOL wallet activities and sends alerts.
    """

    def __init__(self):
        """
        Initializes the KolActivityTimestampDiscrepancyChecker.
        """
        self.admin_chat_ids: List[str] = []
        try:
            self.message_sender = TelegramMessageSender()
        except Exception as e:
            logger.error(f"Failed to initialize TelegramMessageSender: {e}")
            self.message_sender = None # Handle cases where sender cannot be initialized

        # In-memory cache for alert states, loaded from DB
        self._alert_state: Dict[bool, Dict[str, Any]] = {
            True: {  # imported_from_following=True
                "consecutive_alerts": 0,
                "last_notification_time": None,
            },
            False: {  # imported_from_following=False
                "consecutive_alerts": 0,
                "last_notification_time": None,
            }
        }

        # Default configuration values, loaded from DB
        self._config: Dict[str, Any] = {
            "alert_threshold_seconds": 60,
            "consecutive_alerts_required": 3,
            "alert_suppression_minutes": 30,
        }
        logger.info("KolActivityTimestampDiscrepancyChecker initialized.")

    async def _load_config(self) -> None:
        """Loads configuration from the database using ConfigDAO."""
        try:
            app_config: Optional[ApplicationConfig] = await config_dao.get_application_config()
            if app_config:
                self.admin_chat_ids = app_config.admin_telegram_chat_ids if app_config.admin_telegram_chat_ids is not None else []
                if not isinstance(self.admin_chat_ids, list):
                    logger.warning(f"'admin_telegram_chat_ids' in config is not a list: {self.admin_chat_ids}. Using empty list.")
                    self.admin_chat_ids = []

                if app_config.kol_activity_monitor:
                    kol_monitor_config: KolActivityMonitorConfig = app_config.kol_activity_monitor
                    self._config["alert_threshold_seconds"] = kol_monitor_config.alert_threshold_seconds
                    self._config["consecutive_alerts_required"] = kol_monitor_config.consecutive_alerts_required
                    self._config["alert_suppression_minutes"] = kol_monitor_config.alert_suppression_minutes
                else:
                    logger.warning("'kol_activity_monitor' config not found in ApplicationConfig. Using default monitor values.")
                
                logger.info("Successfully loaded configuration from database.")
                if not self.admin_chat_ids:
                    logger.warning("Admin Telegram chat IDs are not configured in the database or are empty.")
            else:
                logger.warning("Application config not found. Using default values.")
                self.admin_chat_ids = [] 
        except Exception as e:
            logger.error(f"Error loading configuration from database: {e}. Using default values.")
            self.admin_chat_ids = []

    async def _load_alert_state(self) -> None:
        """Loads alert states from the database using MonitorAlertStateDAO."""
        logger.debug("Attempting to load alert states from database.")
        self._alert_state = {
            True: {"consecutive_alerts": 0, "last_notification_time": None},
            False: {"consecutive_alerts": 0, "last_notification_time": None}
        }
        monitor_type_val = "kol_activity_timestamp_discrepancy"
        try:
            imported_state_doc = await monitor_alert_state_dao.get_alert_state(
                monitor_type=monitor_type_val,
                category="imported"
            )
            if imported_state_doc:
                last_time_imported = imported_state_doc.last_notification_time
                if last_time_imported and last_time_imported.tzinfo is None:
                    last_time_imported = last_time_imported.replace(tzinfo=timezone.utc)

                self._alert_state[True] = {
                    "consecutive_alerts": imported_state_doc.consecutive_alerts,
                    "last_notification_time": last_time_imported
                }
                logger.info(f"Loaded imported KOL alert state: {self._alert_state[True]}")

            non_imported_state_doc = await monitor_alert_state_dao.get_alert_state(
                monitor_type=monitor_type_val,
                category="non_imported"
            )
            if non_imported_state_doc:
                last_time_non_imported = non_imported_state_doc.last_notification_time
                if last_time_non_imported and last_time_non_imported.tzinfo is None:
                    last_time_non_imported = last_time_non_imported.replace(tzinfo=timezone.utc)
                
                self._alert_state[False] = {
                    "consecutive_alerts": non_imported_state_doc.consecutive_alerts,
                    "last_notification_time": last_time_non_imported
                }
                logger.info(f"Loaded non-imported KOL alert state: {self._alert_state[False]}")
            logger.debug("Finished loading alert states.")
        except Exception as e:
            logger.error(f"Error loading alert states from database: {e}. Using default in-memory states.")

    async def _save_alert_state(self, imported_from_following: bool) -> None:
        """Saves the current alert state for a category to the database using MonitorAlertStateDAO."""
        category = "imported" if imported_from_following else "non_imported"
        state_to_save = self._alert_state[imported_from_following]
        logger.debug(f"Attempting to save alert state for category '{category}': {state_to_save}")
        
        monitor_type_val = "kol_activity_timestamp_discrepancy"

        try:
            await monitor_alert_state_dao.upsert_alert_state(
                monitor_type=monitor_type_val,
                category=category,
                consecutive_alerts=state_to_save["consecutive_alerts"],
                last_notification_time=state_to_save["last_notification_time"]
            )
            logger.info(f"Successfully saved alert state for category '{category}'.")
        except Exception as e:
            logger.error(f"Error saving alert state for category '{category}' to database. State data: {state_to_save}. Error: {e}", exc_info=True)

    async def _get_latest_kol_wallet(self, imported_from_following: bool) -> Optional[KOLWallet]:
        """
        Gets the latest active KOL wallet for a given category.
        KOLs with last_active as None are excluded.
        """
        logger.debug(f"Getting latest KOL wallet for imported_from_following={imported_from_following}")
        
        try:
            latest_kol = await kol_wallet_dao._get_kol_wallet_with_latest_activity(
                imported_from_following=imported_from_following
            )
            if latest_kol:
                logger.info(f"Latest KOL wallet for category imported={imported_from_following}: {latest_kol.wallet_address}, last_active: {latest_kol.last_active}")
            else:
                logger.info(f"No KOL wallet found for category imported={imported_from_following} with a non-null last_active time.")
            return latest_kol
        except Exception as e:
            logger.error(f"Error fetching latest KOL wallet for imported={imported_from_following}: {e}", exc_info=True)
            return None

    async def _get_latest_activity(self, wallet_address: str) -> Optional[KOLWalletActivity]:
        """
        Gets the latest activity for a given wallet, sorted by created_at descending.
        """
        logger.debug(f"Getting latest activity for wallet: {wallet_address}")
        try:
            # Use find_many with limit 1 and sort order
            activities = await kol_wallet_activity_dao.find_many(
                filter_dict={"wallet": wallet_address},
                sort=[("created_at", -1)], # Sort by created_at descending
                limit=1
            )
            latest_activity = activities[0] if activities else None
            
            if latest_activity:
                logger.info(f"Latest activity for wallet {wallet_address}: tx_hash={latest_activity.tx_hash}, created_at={latest_activity.created_at}, timestamp={latest_activity.timestamp}")
            else:
                logger.info(f"No activity found for wallet {wallet_address}.")
            return latest_activity
        except Exception as e:
            logger.error(f"Error fetching latest activity for wallet {wallet_address}: {e}", exc_info=True)
            return None

    async def _record_alert_event(
        self, 
        imported_from_following: bool, 
        wallet_address: str, 
        activity_tx_hash: str, 
        discrepancy_seconds: float, 
        message: str, 
        details: Dict[str, Any]
    ) -> Optional[str]:
        """Records an alert event to the database using AlertEventRecordDAO."""
        category = "imported" if imported_from_following else "non_imported"
        logger.debug(f"Recording alert event for category '{category}', wallet '{wallet_address}', tx '{activity_tx_hash}'")
        monitor_type_val = "kol_activity_timestamp_discrepancy"
        try:
            alert_event = await alert_event_record_dao.create_alert_event(
                monitor_type=monitor_type_val,
                category=category,
                wallet_address=wallet_address,
                activity_tx_hash=activity_tx_hash,
                discrepancy_seconds=discrepancy_seconds,
                message=message,
                details=details,
                trigger_time=datetime.now(timezone.utc)
            )
            logger.info(f"Successfully recorded alert event ID: {alert_event.id} for category '{category}', wallet '{wallet_address}'")
            return str(alert_event.id)
        except Exception as e:
            logger.error(f"Error recording alert event for category '{category}', wallet '{wallet_address}': {e}")
            return None

    async def _record_notification_log(
        self, 
        alert_event_id: Optional[str],
        recipient_chat_id: str, 
        status: str, 
        message_preview: Optional[str] = None, 
        error_message: Optional[str] = None
    ) -> None:
        """Records a notification log to the database using NotificationLogRecordDAO."""
        logger.debug(f"Recording notification log for alert_event_id '{alert_event_id}', recipient '{recipient_chat_id}', status '{status}'")
        try:
            await notification_log_record_dao.create_notification_log(
                alert_event_raw_id=alert_event_id,
                recipient_chat_id=recipient_chat_id,
                status=status,
                message_content_preview=message_preview,
                error_message=error_message,
                send_time=datetime.now(timezone.utc)
            )
            logger.info(f"Successfully recorded notification log for alert_event_id '{alert_event_id}', recipient '{recipient_chat_id}'")
        except Exception as e:
            logger.error(f"Error recording notification log for alert_event_id '{alert_event_id}', recipient '{recipient_chat_id}': {e}")

    async def _should_send_notification(
        self,
        imported_from_following: bool,
        discrepancy_seconds: float,
        wallet_address: str,
        activity: KOLWalletActivity # Ensure type hint
    ) -> Tuple[bool, Optional[str]]:
        """
        Determines if a notification should be sent based on the current state and discrepancy.
        Returns (should_send, alert_event_id)
        """
        now = datetime.now(timezone.utc)
        state = self._alert_state[imported_from_following]
        threshold = self._config["alert_threshold_seconds"]
        required = self._config["consecutive_alerts_required"]
        suppression_minutes = self._config["alert_suppression_minutes"]

        if discrepancy_seconds > threshold:
            state["consecutive_alerts"] += 1
            logger.info(f"Category {'imported' if imported_from_following else 'non-imported'}: Consecutive alerts: {state['consecutive_alerts']}/{required}, Discrepancy: {discrepancy_seconds:.2f}s")
            await self._save_alert_state(imported_from_following) 

            if state["consecutive_alerts"] >= required:
                if state["last_notification_time"] is None or \
                   (now - state["last_notification_time"]).total_seconds() > suppression_minutes * 60:
                    
                    state["last_notification_time"] = now
                    state["consecutive_alerts"] = 0 
                    await self._save_alert_state(imported_from_following)

                    category_display_name = "关注列表" if imported_from_following else "Gmgn默认KOL列表"
                    alert_message_core = (
                        f"KOL活动时间戳差异告警 ({category_display_name}): "
                        f"地址 {wallet_address}, 哈希 {activity.tx_hash}, 差异 {discrepancy_seconds:.2f}s"
                    )
                    alert_details = {
                        "created_at_utc": activity.created_at.isoformat() if activity.created_at else None, # Use created_at
                        "timestamp_raw": activity.timestamp,
                        "threshold_seconds": threshold,
                        "consecutive_alerts_before_trigger": state["consecutive_alerts"] # This will be 0 now
                    }
                    alert_event_id = await self._record_alert_event(
                        imported_from_following=imported_from_following,
                        wallet_address=wallet_address,
                        activity_tx_hash=activity.tx_hash,
                        discrepancy_seconds=discrepancy_seconds,
                        message=alert_message_core,
                        details=alert_details
                    )
                    logger.warning(f"ALERT TRIGGERED for category {'imported' if imported_from_following else 'non-imported'}: {alert_message_core}")
                    return True, alert_event_id
                else:
                    suppression_end_time = state["last_notification_time"] + timedelta(minutes=suppression_minutes)
                    logger.info(
                        f"Alert suppressed for category {'imported' if imported_from_following else 'non-imported'}. "
                        f"Consecutive alerts: {state['consecutive_alerts']}. "
                        f"Suppression ends at {suppression_end_time.isoformat() if state['last_notification_time'] else 'N/A'}."
                    )
                    return False, None
            else: # consecutive_alerts < required
                return False, None
        else: # discrepancy_seconds <= threshold
            if state["consecutive_alerts"] > 0:
                logger.info(f"Category {'imported' if imported_from_following else 'non-imported'}: Discrepancy {discrepancy_seconds:.2f}s is within threshold. Resetting consecutive alerts from {state['consecutive_alerts']} to 0.")
                state["consecutive_alerts"] = 0
                await self._save_alert_state(imported_from_following)
            return False, None
        
    async def check_and_notify_for_category(self, imported_from_following: bool) -> None:
        """
        Checks for discrepancies for a specific KOL category and sends notifications if needed.
        """
        category_display_name = "关注列表" if imported_from_following else "Gmgn默认KOL列表"
        logger.info(f"Starting check for KOL category: {category_display_name}")

        if not self.admin_chat_ids and self.message_sender: # Check if sender exists
            logger.warning(f"No admin chat IDs configured. Cannot send notifications for category {category_display_name}.")
            # No need to return if message_sender is None, as send_message will handle it
        
        latest_kol = await self._get_latest_kol_wallet(imported_from_following)
        if not latest_kol:
            logger.info(f"No suitable KOL wallet found for category: {category_display_name}. Skipping check.")
            return

        latest_activity = await self._get_latest_activity(latest_kol.wallet_address)
        if not latest_activity:
            logger.info(f"No activity found for KOL wallet {latest_kol.wallet_address} in category {category_display_name}. Skipping check.")
            return

        # Ensure created_at is a datetime object and is timezone-aware (UTC)
        created_at_utc = latest_activity.created_at
        if not isinstance(created_at_utc, datetime):
            logger.error(f"Activity {latest_activity.tx_hash} for wallet {latest_kol.wallet_address} has invalid created_at type: {type(created_at_utc)}. Skipping.")
            return
        if created_at_utc.tzinfo is None:
            created_at_utc = created_at_utc.replace(tzinfo=timezone.utc)
        else:
            created_at_utc = created_at_utc.astimezone(timezone.utc)
            
        raw_timestamp = latest_activity.timestamp
        if not isinstance(raw_timestamp, int):
            logger.error(f"Activity {latest_activity.tx_hash} for wallet {latest_kol.wallet_address} has invalid timestamp type: {type(raw_timestamp)}. Skipping.")
            return

        # Convert Unix timestamp (assumed to be for East Eight District, UTC+8) to UTC datetime
        # First, create a naive datetime from timestamp, then localize to UTC+8, then convert to UTC
        try:
            # Assuming timestamp is seconds since epoch
            dt_naive_from_timestamp = datetime.fromtimestamp(raw_timestamp)
            # Create UTC+8 timezone object
            east_eight_tz = timezone(timedelta(hours=8))
            # Localize naive datetime to UTC+8
            dt_east_eight = dt_naive_from_timestamp.replace(tzinfo=east_eight_tz) # More robust than localize
            # Convert to UTC
            timestamp_as_utc_dt = dt_east_eight.astimezone(timezone.utc)
        except Exception as e:
            logger.error(f"Error converting raw timestamp {raw_timestamp} to UTC datetime for activity {latest_activity.tx_hash}: {e}. Skipping.")
            return

        discrepancy_seconds = abs((created_at_utc - timestamp_as_utc_dt).total_seconds())

        logger.info(
            f"Check for wallet: {latest_kol.wallet_address} (Category: {category_display_name}), Activity TX: {latest_activity.tx_hash}, "
            f"DB created_at (UTC): {created_at_utc.isoformat()}, DB timestamp (raw): {raw_timestamp}, "
            f"DB timestamp (as UTC): {timestamp_as_utc_dt.isoformat()}, Discrepancy: {discrepancy_seconds:.2f}s"
        )

        should_send, alert_event_id = await self._should_send_notification(
            imported_from_following=imported_from_following,
            discrepancy_seconds=discrepancy_seconds,
            wallet_address=latest_kol.wallet_address,
            activity=latest_activity 
        )

        if should_send and alert_event_id:
            if not self.message_sender:
                logger.error("TelegramMessageSender is not initialized. Cannot send notification.")
                await self._record_notification_log(
                    alert_event_id=alert_event_id,
                    recipient_chat_id="N/A - Sender not initialized",
                    status="failure",
                    message_preview="Sender not initialized",
                    error_message="TelegramMessageSender not initialized"
                )
                return

            if not self.admin_chat_ids:
                logger.warning(f"Notification triggered for {category_display_name}, but no admin_chat_ids are configured. Alert event ID: {alert_event_id}")
                # Log that a notification would have been sent
                await self._record_notification_log(
                    alert_event_id=alert_event_id,
                    recipient_chat_id="N/A - No admins configured",
                    status="skipped",
                    message_preview=f"KOL Activity Alert (Wallet: {latest_kol.wallet_address}) - No admins to send to.",
                    error_message="No admin_telegram_chat_ids configured"
                )
                return # Still return as no actual send attempt will be made to users


            notification_message = (
                f"⚠️ KOL Activity Timestamp Discrepancy Alert ⚠️\n\n"
                f"Category: {category_display_name}\n"
                f"Wallet: {latest_kol.wallet_address}\n"
                f"Activity TX Hash: {latest_activity.tx_hash}\n"
                f"-------------------------------------\n"
                f"Activity created_at (UTC): {created_at_utc.isoformat()}\n"
                f"Activity timestamp (raw): {raw_timestamp}\n"
                f"Timestamp (as UTC): {timestamp_as_utc_dt.isoformat()}\n"
                f"Timestamp (as UTC+8): {timestamp_as_utc_dt.astimezone(timezone(timedelta(hours=8))).isoformat()}\n"
                f"-------------------------------------\n"
                f"Calculated Discrepancy: {discrepancy_seconds:.2f} seconds\n"
                f"(Threshold: {self._config['alert_threshold_seconds']:.0f}s, Consecutive: {self._config['consecutive_alerts_required']} times)"
            )
            
            message_preview = notification_message[:250] # Limit preview length

            for chat_id in self.admin_chat_ids:
                try:
                    logger.info(f"Attempting to send notification to admin chat_id: {chat_id} for event ID: {alert_event_id}")
                    logger.debug(f"Notification message for chat_id {chat_id}: {notification_message[:200]}...")
                    send_status = await self.message_sender.send_message_to_user(notification_message, chat_id)
                    if send_status:
                        logger.info(f"Successfully sent alert to admin {chat_id} for category {category_display_name}, wallet {latest_kol.wallet_address}, event ID {alert_event_id}.")
                        await self._record_notification_log(
                            alert_event_id=alert_event_id,
                            recipient_chat_id=chat_id,
                            status="success",
                            message_preview=message_preview
                        )
                    else:
                        # Error is already logged by TelegramMessageSender, here we just log the higher-level failure for this specific chat_id
                        logger.error(f"Failed to send alert to admin {chat_id} (send_message_to_user returned False) for event ID {alert_event_id}. Check TelegramMessageSender logs for details.")
                        await self._record_notification_log(
                            alert_event_id=alert_event_id,
                            recipient_chat_id=chat_id,
                            status="failure_sender_returned_false", # More specific status
                            message_preview=message_preview,
                            error_message="send_message_to_user returned False, check sender logs"
                        )

                except Exception as e:
                    logger.error(f"Exception while trying to send alert to admin {chat_id} for event ID {alert_event_id}. Error: {e}", exc_info=True)
                    await self._record_notification_log(
                        alert_event_id=alert_event_id,
                        recipient_chat_id=chat_id,
                        status="failure_exception_in_handler", # More specific status
                        message_preview=message_preview,
                        error_message=str(e)
                    )
        elif should_send and not alert_event_id:
             logger.error(f"_should_send_notification returned True but no alert_event_id for category {category_display_name}, wallet {latest_kol.wallet_address}. This should not happen.")


    async def run_check_cycle(self) -> None:
        """
        Runs a full check cycle: loads config, loads state, checks both categories.
        """
        logger.info("Starting KOL activity timestamp discrepancy check cycle.")
        await self._load_config()
        await self._load_alert_state() # Load persisted state at the beginning of each cycle

        if not self.message_sender and self.admin_chat_ids: # Only warn if admins are configured but sender failed
             logger.warning("TelegramMessageSender is not available, notifications will not be sent for this cycle.")
        
        try:
            await self.check_and_notify_for_category(imported_from_following=True)
        except Exception as e:
            logger.error(f"Error during check for 'imported' category: {e}", exc_info=True)
            
        try:
            await self.check_and_notify_for_category(imported_from_following=False)
        except Exception as e:
            logger.error(f"Error during check for 'non-imported' category: {e}", exc_info=True)
            
        logger.info("KOL activity timestamp discrepancy check cycle finished.")

async def perform_kol_activity_timestamp_discrepancy_check_task():
    """
    Entry point for the workflow task.
    """
    task_name = "perform_kol_activity_timestamp_discrepancy_check_task"
    logger.info(f"Task '{task_name}' triggered.")
    current_utc_time_iso = datetime.now(timezone.utc).isoformat()
    checker = KolActivityTimestampDiscrepancyChecker()
    try:
        await checker.run_check_cycle()
        logger.info(f"Task '{task_name}' completed successfully.")
        return {"status": "success", "timestamp": current_utc_time_iso}
    except Exception as e:
        logger.error(f"Task '{task_name}' failed: {e}", exc_info=True)
        return {"status": "error", "error_message": str(e), "timestamp": current_utc_time_iso} 