import asyncio
import logging
from typing import Dict, Any, List, Optional
import json
from utils.spiders.smart_money import BasicSpider

logger = logging.getLogger(__name__)

class GmgnWalletTokenActivitySpider(BasicSpider):
    """GMGN钱包代币交易活动爬虫
    
    用于获取钱包的特定代币交易活动信息，包括：
    - 买入/卖出交易记录
    - 实际的SOL金额变化（已扣除平台服务费等）
    - 交易时间戳
    - 交易哈希
    
    此爬虫用于修复盈利计算不准确的Bug，通过获取统一口径的交易金额数据。
    """
    
    BASE_URL = "https://gmgn.ai/defi/quotation/v1/wallet_token_activity"
    
    def __init__(self, max_retries: int = 3, retry_interval: float = 1.0):
        super().__init__(max_retries=max_retries, retry_interval=retry_interval)
        self.headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-TW;q=0.6',
            'baggage': 'sentry-environment=production,sentry-release=20250528-1646-f0a534f,sentry-public_key=93c25bab7246077dc3eb85b59d6e7d40,sentry-trace_id=6622a1b17cab462cb479740a45f418e7,sentry-sample_rate=0.01,sentry-sampled=false',
            'cache-control': 'no-cache',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://gmgn.ai/sol/address/dDO6ZdYE_AHnQjWa4jxYNxzfJantHDpcUKAE9pzVSGGPLhthPxCSW',
            'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-arch': '"arm"',
            'sec-ch-ua-bitness': '"64"',
            'sec-ch-ua-full-version': '"136.0.7103.114"',
            'sec-ch-ua-full-version-list': '"Chromium";v="136.0.7103.114", "Google Chrome";v="136.0.7103.114", "Not.A/Brand";v="********"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-model': '""',
            'sec-ch-ua-platform': '"macOS"',
            'sec-ch-ua-platform-version': '"15.4.1"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'sentry-trace': '13518a18ee8e44a6a9ca10a3db56de1e-b51028e0716e8c2e-0',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
        
        # 模拟从用户提供的curl命令中提取的cookie
        self.cookies = {
            '_ga': 'GA1.1.2106207204.1741705941',
            'GMGN_LOCALE': 'zh-CN',
            'GMGN_CHAIN': 'sol',
            'GMGN_THEME': 'dark',
            'cf_clearance': 'MeSa8gJrxnx.yULmTIb4bXpixYfdtu0lJRQ4SIKwUDQ-1748487598-*******-oFsPthLB_6mI6e3gJilqhXZ.Y2jihwb0kytj5SwvmOSZ.e3Yp0xgdQLlZ9d2kJfP56gZxE1KPiEAF28rFGaIn_R0Em9UtvxOjLU5gDaSSbpQAmieSqUyDFTlzJBkU2iQLRlb5uputSZO47TVDvi.44nPh4acVyvWb5lT16gHBvtIEh7bBjQkmlqBqd17AelFi3bb7bV8.njV8C9EAnpsQYqpzq1taBAn8SBYydyb7siC1A2zyySN6nBwnKdh2pqUbiMQqOz8e5tQ4VzOyvApm2ciW1.2tg3qLMicaYMRYAhQDQpgP_GiBs3E7sSzb8NOIpda1m8hhjwkm1Vg_eGqyf0r1KPdVy.cZd744j5EI2c',
            '_ga_0XM0LYXGC8': 'GS2.1.s1748482207$o925$g1$t1748490153$j60$l0$h0',
            'sid': 'gmgn%7C760e53213b42966cbf57ae9fe225a13d',
            '_ga_UGLVBMV4Z0': 'GS1.2.1748490153964565.a8647af0c2c57c50aa4d0f6bb92237b6.shcCfXqVhruFYFROL%2BfGKA%3D%3D.Oduy7RH0c2LPrJYULlCh%2BA%3D%3D.9vK88aLl%2BMZFNjjqgmZ8Pw%3D%3D.bWY2XSXz8OYVQR9mIh9brg%3D%3D',
            '__cf_bm': 'sh5xZqaalH65eRD5QtoftDs9W4A1SNomxM4cPiHxcJI-1748490549-*******-D94GjAC0rArLwcpqyEA0LuU22jrrJ9U5cDixk0kk1krbCoPFlhDCHrPrJLqRj44xXkMy511YkPkCQxI5ax_1_PxJd5iwTM.QHEV8GMAWc9A'
        }
    
    async def setup(self):
        """设置爬虫，初始化会话并更新头信息"""
        await self.init_sessions()
        # 更新会话头信息
        self.session.headers.update(self.headers)
        # 设置cookies
        self.session.cookies.update(self.cookies)
        # 设置浏览器模拟
        self.session.impersonate = "chrome110"  # 模拟Chrome浏览器
    
    async def get_wallet_token_activity(
        self,
        chain: str,
        wallet_address: str,
        token_mint: str,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        获取钱包对特定代币的交易活动记录
        
        Args:
            chain: 链名称 (通常为 "sol")
            wallet_address: 钱包地址
            token_mint: 代币合约地址
            limit: 返回的活动记录数量限制
            
        Returns:
            List[Dict[str, Any]]: 交易活动列表，每个活动包含：
                - signature: 交易哈希
                - block_time: 区块时间
                - type: 交易类型 ("buy" 或 "sell")
                - amount: SOL金额（买入为负数，卖出为正数）
                - price: 代币价格
                - token_amount: 代币数量
                
        Raises:
            Exception: 当API请求失败或返回错误数据时
        """
        try:
            # 确保会话已设置
            if not hasattr(self, 'session') or self.session is None:
                await self.setup()
            
            url = f"{self.BASE_URL}/{chain}"
            logger.info(f"正在获取钱包 {wallet_address} 对代币 {token_mint} 的交易活动...")
            
            # 构建请求参数（基于用户提供的curl命令）
            params = {
                'device_id': '65990bc3-0a13-4d99-8c66-eeea94916b0f',
                'client_id': 'gmgn_web_20250528-1646-f0a534f',
                'from_app': 'gmgn',
                'app_ver': '20250528-1646-f0a534f',
                'tz_name': 'Asia/Shanghai',
                'tz_offset': '28800',
                'app_lang': 'zh-CN',
                'fp_did': '40adcc5a28fdc45901855725abbf74db',
                'os': 'web',
                'wallet': wallet_address,
                'token': token_mint,
                'limit': limit
            }
            
            logger.debug(f"请求URL: {url}")
            logger.debug(f"请求参数: {params}")
            
            # 使用带有重试机制的get方法
            response = await self.get(url, params=params)
            
            # 记录响应状态和内容
            logger.debug(f"API响应状态码: {response.status_code}")
            logger.debug(f"API响应内容: {response.text[:500]}...")  # 只记录前500个字符
            
            # 检查响应状态
            if response.status_code != 200:
                logger.error(f"获取钱包代币交易活动失败: HTTP {response.status_code}")
                logger.error(f"错误响应: {response.text}")
                return []
                
            # 解析响应JSON（只调用一次）
            try:
                response_data = response.json()
                logger.debug(f"解析后的API响应: {response_data}")
            except Exception as e:
                logger.error(f"解析API响应JSON失败: {str(e)}")
                logger.error(f"原始响应: {response.text}")
                return []
                
            # 解析响应
            if response_data.get('code') == 0 and 'data' in response_data:
                data = response_data['data']
                
                # 处理不同的数据格式
                if isinstance(data, list):
                    # 直接是列表格式
                    activities = data
                elif isinstance(data, dict):
                    # 字典格式，可能包含 'activities' 或 'list' 字段
                    if 'activities' in data:
                        activities = data['activities']
                    elif 'list' in data:
                        activities = data['list']
                    elif 'data' in data:
                        activities = data['data']
                    else:
                        # 如果是字典但没有预期字段，记录并返回空列表
                        logger.warning(f"API返回的data字段为字典但缺少预期的子字段，data keys: {list(data.keys())}")
                        activities = []
                else:
                    logger.error(f"API返回的data字段格式错误，期望list或dict类型，实际为: {type(data)}")
                    activities = []
            else:
                logger.error(f"API请求失败，响应: {response_data}")
                activities = []
            
            logger.info(f"成功获取钱包 {wallet_address} 对代币 {token_mint} 的交易活动，共 {len(activities)} 条记录")
            
            # 验证并规范化数据
            normalized_activities = []
            for activity in activities:
                try:
                    # API返回的字段映射
                    # tx_hash -> signature
                    # event_type -> type  
                    # timestamp -> block_time
                    # quote_amount -> amount
                    
                    # 确保必需字段存在（使用API实际返回的字段名）
                    required_fields = ['tx_hash', 'timestamp', 'event_type', 'quote_amount']
                    if not all(field in activity for field in required_fields):
                        logger.warning(f"跳过缺少必需字段的活动记录: {activity}")
                        continue
                    
                    # 规范化数据（映射字段名）
                    normalized_activity = {
                        'signature': activity['tx_hash'],  # 映射 tx_hash -> signature
                        'block_time': activity['timestamp'],  # 映射 timestamp -> block_time
                        'type': activity['event_type'],  # 映射 event_type -> type ("buy" 或 "sell")
                        'amount': float(activity['quote_amount']),  # 映射 quote_amount -> amount (SOL 金额)
                        'price': float(activity.get('price', 0.0)),  # 代币价格
                        'token_amount': float(activity.get('token_amount', 0.0)),  # 代币数量
                        'cost_usd': float(activity.get('cost_usd', 0.0)),  # USD成本
                        'price_usd': float(activity.get('price_usd', 0.0)),  # USD价格
                        'raw_data': activity  # 保留原始数据用于调试
                    }
                    
                    normalized_activities.append(normalized_activity)
                    
                except (ValueError, TypeError, KeyError) as e:
                    logger.warning(f"跳过格式错误的活动记录: {activity}, 错误: {e}")
                    continue
            
            logger.info(f"规范化后的有效交易活动记录: {len(normalized_activities)} 条")
            return normalized_activities
            
        except Exception as e:
            logger.error(f"获取钱包代币交易活动时发生错误: {str(e)}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            return []
    
    async def find_activity_by_signature(
        self,
        chain: str,
        wallet_address: str,
        token_mint: str,
        tx_signature: str,
        limit: int = 50
    ) -> Optional[Dict[str, Any]]:
        """
        根据交易签名查找特定的交易活动记录
        
        Args:
            chain: 链名称
            wallet_address: 钱包地址
            token_mint: 代币合约地址
            tx_signature: 交易签名/哈希
            limit: 搜索的活动记录数量限制
            
        Returns:
            Optional[Dict[str, Any]]: 匹配的交易活动记录，如果没找到则返回None
        """
        try:
            logger.info(f"正在查找交易签名 {tx_signature} 的活动记录...")
            
            activities = await self.get_wallet_token_activity(
                chain, wallet_address, token_mint, limit
            )
            
            # 查找匹配的交易
            for activity in activities:
                if activity.get('signature') == tx_signature:
                    logger.info(f"找到匹配的交易活动: {activity}")
                    return activity
            
            logger.warning(f"未找到交易签名 {tx_signature} 对应的活动记录")
            return None
            
        except Exception as e:
            logger.error(f"查找交易活动记录时发生错误: {str(e)}")
            return None


async def main():
    """测试函数"""
    spider = GmgnWalletTokenActivitySpider()
    
    # 测试参数（基于用户提供的curl命令）
    test_wallet = "AHnQjWa4jxYNxzfJantHDpcUKAE9pzVSGGPLhthPxCSW"
    test_token = "MUbEZ6mDVy39FrjNCQPyX3PM7CwSM3hQ754ii4wpump"
    test_signature = "3wRoGXxKj8Ka4owNAbQeyZ8t5BER896HKAHCV1UX7Sq5z2qzmg8LG43MSKGQKmNMDRtx51r5kwzHXp21DidvW54j"
    
    try:
        # 获取交易活动
        activities = await spider.get_wallet_token_activity("sol", test_wallet, test_token, limit=10)
        print(f"获取到 {len(activities)} 条交易活动:")
        for activity in activities[:3]:  # 只显示前3条
            print(json.dumps(activity, indent=2))
        
        # 查找特定交易
        specific_activity = await spider.find_activity_by_signature(
            "sol", test_wallet, test_token, test_signature
        )
        if specific_activity:
            print(f"\n找到特定交易:")
            print(json.dumps(specific_activity, indent=2))
        else:
            print(f"\n未找到交易签名 {test_signature}")
            
    finally:
        # 关闭爬虫会话连接
        await spider.close()

if __name__ == "__main__":
    # 使用asyncio运行异步主函数
    asyncio.run(main()) 