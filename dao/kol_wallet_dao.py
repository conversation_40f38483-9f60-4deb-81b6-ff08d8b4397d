from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from models.kol_wallet import KOLWallet
from .base_dao import BaseDAO
from pymongo import UpdateOne

class KOLWalletDAO(BaseDAO[KOLWallet]):
    """KOL钱包 DAO"""
    
    def __init__(self):
        super().__init__(KOLWallet)
    
    async def find_all_wallets(self) -> List[KOLWallet]:
        """获取所有KOL钱包"""
        return await self.model.find({"tags": "kol"}).to_list()
    
    async def find_by_address(self, address: str) -> Optional[KOLWallet]:
        """根据地址查找钱包"""
        return await self.find_one({"address": address})
    
    async def add_wallet(
        self,
        address: str,
        name: str = None,
        description: str = None,
        tags: List[str] = None
    ) -> KOLWallet:
        """添加KOL钱包"""
        wallet = KOLWallet(
            address=address,
            name=name,
            description=description,
            tags=tags or []
        )
        return await self.insert_one(wallet)
    
    async def update_wallet(
        self,
        address: str,
        name: str = None,
        description: str = None,
        tags: List[str] = None
    ) -> bool:
        """更新钱包信息"""
        update_data = {}
        if name is not None:
            update_data["name"] = name
        if description is not None:
            update_data["description"] = description
        if tags is not None:
            update_data["tags"] = tags
            
        return await self.update_one(
            {"address": address},
            update_data
        )
    
    async def delete_wallet(self, address: str) -> bool:
        """删除钱包"""
        return await self.delete_one({"address": address})
    
    async def find_by_wallet_address(self, wallet_address: str) -> Optional[KOLWallet]:
        """根据钱包地址查找钱包"""
        return await self.find_one({"wallet_address": wallet_address})
    
    async def find_by_wallet_addresses(self, wallet_addresses: List[str]) -> List[KOLWallet]:
        """根据钱包地址列表批量查找钱包"""
        return await self.find_many({"wallet_address": {"$in": wallet_addresses}})
    
    async def upsert_wallet(self, wallet_data: Dict[str, Any]) -> bool:
        """更新或插入钱包数据"""
        wallet_address = wallet_data["wallet_address"]
        existing_wallet = await self.find_by_wallet_address(wallet_address)
        
        if existing_wallet:
            # 如果存在，保留first_seen_at
            wallet_data["first_seen_at"] = existing_wallet.first_seen_at
        else:
            # 如果不存在，设置first_seen_at为当前时间
            wallet_data["first_seen_at"] = datetime.utcnow()
        
        # 更新last_updated_at
        wallet_data["last_updated_at"] = datetime.utcnow()
        
        # 使用原子更新操作
        return await self.update_one(
            {"wallet_address": wallet_address},
            wallet_data,
            upsert=True
        )
    
    async def upsert_wallets(self, wallet_data_list: List[Dict[str, Any]]) -> int:
        """批量更新或插入钱包数据"""
        if not wallet_data_list:
            return 0
            
        # 获取所有钱包地址
        wallet_addresses = [data["wallet_address"] for data in wallet_data_list]
        
        # 批量查询现有钱包
        existing_wallets = await self.find_by_wallet_addresses(wallet_addresses)
        existing_addresses = {w.wallet_address: w for w in existing_wallets}
        
        # 准备批量更新操作
        bulk_operations = []
        current_time = datetime.utcnow()
        
        for wallet_data in wallet_data_list:
            wallet_address = wallet_data["wallet_address"]
            
            # 设置时间字段
            if wallet_address in existing_addresses:
                wallet_data["first_seen_at"] = existing_addresses[wallet_address].first_seen_at
            else:
                wallet_data["first_seen_at"] = current_time
            
            wallet_data["last_updated_at"] = current_time
            
            # 创建更新操作
            bulk_operations.append(
                UpdateOne(
                    {"wallet_address": wallet_address},
                    {"$set": wallet_data},
                    upsert=True
                )
            )
        
        # 执行批量更新
        try:
            result = await self.collection.bulk_write(bulk_operations)
            self.logger.info(f"批量更新完成: 修改 {result.modified_count} 条, 插入 {result.upserted_count} 条")
            return result.modified_count + result.upserted_count
        except Exception as e:
            self.logger.error(f"批量更新钱包数据失败: {str(e)}")
            return 0
    
    async def find_active_wallets(
        self,
        days: int = 7,
        skip: int = 0,
        limit: int = 100
    ) -> List[KOLWallet]:
        """查找活跃钱包"""
        cutoff_time = datetime.utcnow() - timedelta(days=days)
        return await self.find_many(
            {"last_active": {"$gte": cutoff_time}},
            skip=skip,
            limit=limit,
            sort=[("realized_profit_7d", -1)]
        )
    
    async def find_top_wallets(
        self,
        metric: str = "realized_profit_7d",
        skip: int = 0,
        limit: int = 100
    ) -> List[KOLWallet]:
        """查找排名靠前的钱包"""
        return await self.find_many(
            {metric: {"$exists": True}},
            skip=skip,
            limit=limit,
            sort=[(metric, -1)]
        )
    
    async def find_by_twitter_username(self, twitter_username: str) -> Optional[KOLWallet]:
        """根据Twitter用户名查找钱包"""
        return await self.find_one({"twitter_username": twitter_username})
    
    async def find_by_followers_count(
        self,
        min_followers: int = 10000,
        skip: int = 0,
        limit: int = 100
    ) -> List[KOLWallet]:
        """根据关注者数量查找钱包"""
        return await self.find_many(
            {"followers_count": {"$gte": min_followers}},
            skip=skip,
            limit=limit,
            sort=[("followers_count", -1)]
        )
    
    async def find_by_tag(
        self,
        tag: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[KOLWallet]:
        """根据标签查找钱包"""
        return await self.find_many(
            {"tags": tag},
            skip=skip,
            limit=limit,
            sort=[("realized_profit_7d", -1)]
        )
    
    async def find_verified_wallets(
        self,
        skip: int = 0,
        limit: int = 100
    ) -> List[KOLWallet]:
        """查找已验证的钱包"""
        return await self.find_many(
            {"is_blue_verified": True},
            skip=skip,
            limit=limit,
            sort=[("followers_count", -1)]
        ) 
        
    async def filter_no_robot_kol_wallet(self, txs_30d_gte: int = 10, txs_30d_lte: int = 100, skip: int = 0, limit: int = 100) -> List[KOLWallet]:
        return await self.find_many(
            {"tags": "kol", "txs_30d": {"$gte": txs_30d_gte, "$lte": txs_30d_lte}},
            skip=skip,
            limit=limit
        )
        
    async def _get_kol_wallet_with_latest_activity(self, imported_from_following: bool) -> Optional[KOLWallet]:
        """
        获取指定类别KOL钱包中，last_active时间最新的那个钱包对象。
        内部辅助方法。

        Args:
            imported_from_following: True表示查找从关注列表导入的KOL，False表示查找非导入的。
            
        Returns:
            Optional[KOLWallet]: 最新活动的KOL钱包对象，如果找不到则返回None。
        """
        if imported_from_following:
            query_conditions = {
                "tags": "kol", # 确保是KOL钱包
                "imported_from_following": imported_from_following,
                "last_active": {"$ne": None} # 确保 last_active 字段存在且不为 null
            }
        else:
            query_conditions = {
                "tags": "kol", # 确保是KOL钱包
                "$or": [
                    {"imported_from_following": {"$exists": False}},
                    {"imported_from_following": False}
                ],
                "last_active": {"$ne": None} # 确保 last_active 字段存在且不为 null
            }
        
        wallets = await self.find_many(
            query_conditions,
            sort=[("last_active", -1)], # 按 last_active 降序排序
            limit=1
        )
        
        if wallets:
            return wallets[0]
        else:
            return None

    async def get_kol_last_active(self, imported_from_following: bool | None = None) -> float:
        """
        获取KOL钱包的最新活动时间戳。
        为了兼容性，此方法保留。新的调用应考虑使用 _get_kol_wallet_with_latest_activity。
        如果 imported_from_following 为 None，则行为与旧版本类似，查询非导入或字段不存在的。
        
        Args:
            imported_from_following: True表示查找从关注列表导入的KOL，False表示查找非导入的。
                                     None 表示查找 imported_from_following 为 False 或不存在的钱包。
            
        Returns:
            float: 最新活动的时间戳，如果没有数据则返回0.0
        """
        # 适配 imported_from_following 为 None 的情况以保持旧行为
        effective_imported_from_following: bool
        if imported_from_following is None: 
            # 这是旧逻辑中 imported_from_following 为 None 时实际查询的类别
            # 它会查找 imported_from_following 为 false 或不存在的
            # 为了调用新的、只接受 bool 的方法，我们这里将其明确为 False。
            # 注意：这与旧的 $and 查询略有不同，旧的 $and 会匹配字段不存在的情况，
            # 而直接传递 False 给新方法只会匹配字段值为 False 的情况。
            # 如果需要精确匹配旧的 None 行为（字段不存在 OR 字段为False），这里的适配会更复杂。
            # 目前为了简化，我们假设 None 意图接近于 False。
            # 如果严格兼容性是必须的，get_kol_last_active 需要保留其旧的 $and 查询逻辑，
            # 而不是调用 _get_kol_wallet_with_latest_activity。
            # 鉴于用户要求新建方法以兼容，我们先尝试这种调用方式。
            # 如果发现问题，再决定是否让 get_kol_last_active 完全保留旧查询。
            # 更好的兼容方式是让 _get_kol_wallet_with_latest_activity 也接受 Optional[bool]
            # 但既然新handler用的是bool，暂时这样。
            # *** 为了最安全地保持兼容性，当 imported_from_following is None 时，应该执行原始的查询逻辑 ***
            # *** 而不是试图适配它到新的 _get_kol_wallet_with_latest_activity 方法 ***
            # *** 因此，下面的逻辑将恢复原始查询，仅当 imported_from_following is not None 时才调用新方法 ***
            
            # 保留旧的查询逻辑以确保完全兼容 imported_from_following 为 None 的情况
            query_conditions = {
                "tags": "kol",
                "$or": [
                    {"imported_from_following": {"$exists": False}},
                    {"imported_from_following": False}
                ],
                "last_active": {"$ne": None}
            }
            wallets = await self.find_many(
                query_conditions,
                sort=[("last_active", -1)],
                limit=1
            )
            if wallets and wallets[0].last_active:
                return wallets[0].last_active.timestamp()
            return 0.0

        # 如果 imported_from_following 是 True 或 False，则调用新的内部方法
        wallet = await self._get_kol_wallet_with_latest_activity(imported_from_following)
        if wallet and wallet.last_active:
            return wallet.last_active.timestamp()
        else:
            return 0.0
    