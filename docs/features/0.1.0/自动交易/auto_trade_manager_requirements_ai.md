# 统一自动交易管理器需求规格

**创建日期**: 2025-05-25  
**版本**: 0.1.0  
**目标**: 实现统一的自动交易管理器，支持多渠道容错和故障转移

## 1. 背景与问题

### 1.1 现状问题
- 交易方式配置与策略强耦合（通过 `use_direct_solana_trading` 配置）
- 单一交易渠道失败会导致整个交易失败，无容错机制
- 策略代码需要关心具体的交易实现细节
- 交易渠道的选择逻辑分散在各个策略处理器中

### 1.2 核心需求
1. **解耦策略与交易渠道**：策略只需要发起交易请求，不关心具体实现
2. **多渠道容错**：支持配置多个交易渠道，失败时自动切换
3. **统一交易管理**：提供统一的交易接口和状态管理
4. **灵活配置**：支持动态配置交易渠道优先级和参数

## 2. 功能需求

### 2.1 核心功能

#### 2.1.1 统一交易接口
- **功能**: 提供统一的交易请求接口
- **输入**: 交易类型、代币信息、金额、钱包信息、信号ID等
- **输出**: 交易结果，包含状态、交易哈希、错误信息等
- **验收标准**: 
  - 策略代码只需调用一个方法即可发起交易
  - 支持买入和卖出操作
  - 返回标准化的交易结果

#### 2.1.2 多渠道管理
- **功能**: 管理多个交易渠道，支持优先级配置
- **输入**: 渠道配置列表，包含类型、优先级、参数
- **输出**: 可用渠道列表和状态
- **验收标准**:
  - 支持动态添加/移除交易渠道
  - 支持设置渠道优先级
  - 支持渠道健康状态检测

#### 2.1.3 故障转移机制
- **功能**: 当高优先级渠道失败时，自动切换到备用渠道
- **输入**: 交易请求和渠道失败信息
- **输出**: 使用备用渠道的交易结果
- **验收标准**:
  - 按优先级顺序尝试不同渠道
  - 记录每次尝试的结果
  - 所有渠道失败时返回综合错误信息

#### 2.1.4 交易记录管理
- **功能**: 统一管理所有交易记录，包含多渠道尝试历史
- **输入**: 交易结果和渠道信息
- **输出**: 完整的交易记录
- **验收标准**:
  - 记录每个渠道的尝试结果
  - 记录最终成功的渠道和交易详情
  - 记录失败原因和错误信息

### 2.2 配置管理

#### 2.2.1 渠道配置
- **功能**: 支持灵活配置交易渠道参数
- **配置项**:
  - 渠道类型（gmgn, solana_direct等）
  - 优先级（数字，越小越优先）
  - 是否启用
  - 渠道特定参数（API Host、滑点等）
  - 超时时间
  - 重试次数

#### 2.2.2 全局配置
- **功能**: 支持全局交易参数配置
- **配置项**:
  - 默认超时时间
  - 最大重试次数
  - 失败通知设置
  - 管理员通知Chat ID列表

## 3. 技术需求

### 3.1 性能要求
- 单次交易总时间不超过60秒（包含所有渠道尝试）
- 渠道切换时间不超过5秒
- 支持并发交易请求

### 3.2 可靠性要求
- 交易状态必须准确记录，不能丢失
- 异常情况下能正确回滚和清理
- 所有操作必须有详细日志

### 3.3 扩展性要求
- 支持添加新的交易渠道类型
- 支持不同区块链的交易服务
- 配置可以动态更新无需重启

## 4. 接口设计

### 4.1 主要接口

#### 4.1.1 AutoTradeManager.execute_trade()
```python
async def execute_trade(
    self,
    trade_type: TradeType,
    input_token_address: str,
    output_token_address: str,
    amount_input_token: float,
    wallet_private_key_b58: str,
    wallet_address: str,
    signal_id: PydanticObjectId,
    strategy_config_snapshot: Dict[str, Any]
) -> TradeExecutionResult:
    """
    执行统一交易接口
    """
```

#### 4.1.2 AutoTradeManager.get_available_channels()
```python
async def get_available_channels(self) -> List[TradeChannelInfo]:
    """
    获取可用交易渠道列表
    """
```

### 4.2 数据模型

#### 4.2.1 TradeExecutionResult
```python
class TradeExecutionResult(BaseModel):
    final_status: TradeStatus
    successful_channel: Optional[str]
    final_trade_record_id: PydanticObjectId
    channel_attempts: List[ChannelAttemptResult]
    total_execution_time: float
    error_summary: Optional[str]
```

#### 4.2.2 TradeChannelConfig
```python
class TradeChannelConfig(BaseModel):
    channel_type: str  # "gmgn", "solana_direct"
    priority: int      # 优先级，越小越优先
    enabled: bool
    timeout_seconds: int
    max_retries: int
    channel_params: Dict[str, Any]
```

## 5. 边界条件

### 5.1 异常处理
- 所有渠道都失败时的处理
- 网络超时的处理
- 钱包余额不足的处理
- 代币不存在或已暂停交易的处理

### 5.2 资源管理
- HTTP连接池的管理
- 内存使用的控制
- 并发请求数的限制

## 6. 兼容性考虑

### 6.1 向后兼容
- 现有策略代码应能平滑迁移
- 现有配置格式应继续支持
- 现有交易记录格式保持不变

### 6.2 配置迁移
- 提供配置迁移工具
- 支持旧配置格式的自动转换
- 提供配置验证工具

## 7. 测试需求

### 7.1 单元测试
- 每个交易渠道的独立测试
- 故障转移逻辑的测试
- 配置加载和验证的测试

### 7.2 集成测试
- 多渠道协作的端到端测试
- 异常情况下的容错测试
- 性能压力测试

### 7.3 模拟测试
- 模拟交易渠道API
- 模拟网络异常情况
- 模拟各种错误场景 