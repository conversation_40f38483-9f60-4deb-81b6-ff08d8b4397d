# Bug修复方案：KOL监控通知中购买Token数量显示不正确

**Bug 标识**: KOL监控模块在发送Telegram购买通知时，`Amount Bought` 和 `Amount Spent` 字段显示的数值未经过Token的`decimals`正确转换，导致数量显示错误（通常是过大的整数）。

**报告日期/发现日期**: 2024-05-19

**根源分析概要**:
1.  Telegram通知消息模板中的 `actual_amount_out_ui` (对应 "Amount Bought") 直接使用了交易服务返回的 `actual_amount_out` 字段，该字段存储的是Token的最小单位数量（例如 lamports），没有除以 `10 ** decimals` 进行转换。
2.  类似地，`actual_amount_in_ui` (对应 "Amount Spent") 也可能存在此问题，或者其来源逻辑需要调整以符合用户期望。
3.  `trade_records` 数据库中的 `provider_response_raw` 字段包含了 `onchain_actual_amount_out_lamports`、`onchain_actual_amount_in_lamports`、`inDecimals` 和 `outDecimals`，这些信息确认了原始值是最小单位，并且 `decimals` 信息是可获取的。

**详细的、已获批准的修复方案**:

在 `workflows/monitor_kol_activity/handler.py` 文件的 `send_message_to_channel` 函数内部，当构造 `template_render_data['trade_result']` 字典时，进行以下修改：

1.  **处理购买到的Token数量 (`actual_amount_out_ui`)**:
    *   从传递给函数的 `token_info` 字典中获取购买Token的 `decimals` (键名为 `decimals`)。
    *   如果 `trade_executed_result.actual_amount_out` (表示实际获得的Token最小单位数量) 和 `decimals`均有效：
        *   计算调整后的数量：`adjusted_amount_out = float(trade_executed_result.actual_amount_out) / (10 ** int(output_token_decimals))`。
        *   格式化显示：`amount_out_ui = f"{adjusted_amount_out:.{int(output_token_decimals)}f}"`，小数位数根据Token的 `decimals` 动态确定。
    *   增加错误处理和日志：如果 `decimals` 缺失或数值转换失败，记录警告/错误，并回退显示原始值加 `(raw)` 标识。

2.  **处理花费的SOL数量 (`actual_amount_in_ui`)**:
    *   **固定逻辑**：此字段的值**始终**来源于策略配置快照中的 `gmgn_auto_trade_buy_amount_sol`。
    *   获取SOL的 `decimals` (默认为9)。可以尝试从 `trade_executed_result.provider_response_raw.quoteResponse.data.quote.inDecimals` 中获取更精确的 `decimals` 值，如果API提供了，用于更准确地格式化。
    *   从 `strategy_snapshot.get('gmgn_auto_trade_buy_amount_sol')` 获取计划花费的SOL金额。
    *   格式化显示：`amount_in_ui = f"{float(planned_sol_spent):.{input_token_decimals}f}"`，小数位数根据SOL的 `decimals` 动态确定。
    *   增加错误处理和日志：如果配置值不存在或转换失败，记录警告/错误，并回退显示原始配置值加 `(raw)` 标识或 "N/A"。

**示例代码片段修改思路**:
```python
# 在 send_message_to_channel 函数内, template_render_data 构造部分

            if trade_executed_result:
                # --- 处理 Amount Bought (购买到的Token数量) ---
                output_token_decimals = token_info.get('decimals')
                amount_out_ui = "N/A"
                if trade_executed_result.actual_amount_out is not None:
                    if output_token_decimals is not None:
                        try:
                            raw_amount_out = float(trade_executed_result.actual_amount_out)
                            divisor_out = 10 ** int(output_token_decimals) if int(output_token_decimals) > 0 else 1
                            adjusted_amount_out = raw_amount_out / divisor_out
                            amount_out_ui = f"{adjusted_amount_out:.{int(output_token_decimals)}f}"
                        except (ValueError, TypeError) as e:
                            logger.error(f"[Signal:{str_signal_id}] 无法转换 output token amount 或 decimals: amount='{trade_executed_result.actual_amount_out}', decimals='{output_token_decimals}'. Error: {e}")
                            amount_out_ui = f"{trade_executed_result.actual_amount_out} (raw)"
                    else:
                        logger.warning(f"[Signal:{str_signal_id}] Token {token_info.get('address')} 缺少 decimals 信息，无法调整数量显示。")
                        amount_out_ui = f"{trade_executed_result.actual_amount_out} (raw)"

                # --- 处理 Amount Spent (花费的SOL数量) ---
                input_token_decimals = 9 # SOL的decimals默认为9
                if hasattr(trade_executed_result, 'provider_response_raw') and \
                   isinstance(trade_executed_result.provider_response_raw, dict) and \
                   trade_executed_result.provider_response_raw.get('quoteResponse', {}).get('data', {}).get('quote', {}).get('inDecimals') is not None:
                    try:
                        fetched_input_decimals = int(trade_executed_result.provider_response_raw['quoteResponse']['data']['quote']['inDecimals'])
                        if fetched_input_decimals != input_token_decimals:
                             logger.info(f"[Signal:{str_signal_id}] GMGN API提供的SOL decimals为 {fetched_input_decimals}，将用于格式化。原默认: {input_token_decimals}.")
                        input_token_decimals = fetched_input_decimals
                    except (ValueError, TypeError):
                        logger.warning(f"[Signal:{str_signal_id}] 无法从GMGN API解析SOL decimals，将使用默认值 {input_token_decimals}.")


                amount_in_ui = "N/A"
                planned_sol_spent = strategy_snapshot.get('gmgn_auto_trade_buy_amount_sol')
                if planned_sol_spent is not None:
                    try:
                        amount_in_ui = f"{float(planned_sol_spent):.{input_token_decimals}f}"
                    except (ValueError, TypeError) as e:
                        logger.error(f"[Signal:{str_signal_id}] 无法格式化配置的SOL花费金额: '{planned_sol_spent}'. Error: {e}")
                        amount_in_ui = f"{planned_sol_spent} (raw)"
                else:
                    logger.warning(f"[Signal:{str_signal_id}] 策略快照中未找到 'gmgn_auto_trade_buy_amount_sol'，无法显示计划花费的SOL。")
                
                template_render_data['trade_result'] = {
                    'tx_hash': trade_executed_result.tx_hash,
                    'tx_hash_short': trade_executed_result.tx_hash[:4] + "..." + trade_executed_result.tx_hash[-4:] if trade_executed_result.tx_hash else "N/A",
                    'error_message': trade_executed_result.error_message,
                    'actual_amount_out_ui': amount_out_ui,
                    'actual_amount_in_ui': amount_in_ui,
                }
```

**方案提出者/执行者**: AI Assistant (Gemini 2.5 Pro)
**方案审阅者/批准者**: User (gaojerry)
**方案批准日期**: 2024-05-20

**预期的验证方法**:
1.  代码修改后，重新触发一次导致问题的KOL购买场景。
2.  检查发送到Telegram的通知消息。
3.  确认 "Amount Bought" 字段显示的是经过 `decimals` 调整后的正确Token数量（例如，对于decimals为9的PODCAST，`760722241082` 应显示为 `760.722241082`）。
4.  确认 "Amount Spent" 字段显示的是策略配置中 `gmgn_auto_trade_buy_amount_sol` 的值，并经过SOL的 `decimals` (默认为9) 正确格式化（例如，配置为 `0.001`，应显示为 `0.001000000`）。
