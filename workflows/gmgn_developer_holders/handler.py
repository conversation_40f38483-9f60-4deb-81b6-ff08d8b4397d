import logging
import traceback
from typing import Any, Dict, List, Optional

from dao.gmgn_developer_holders_dao import GmgnDeveloperHoldersDAO
from utils.spiders.smart_money.gmgn_top_holders_spider import GmgnTopHoldersSpider


logger = logging.getLogger("GmgnDeveloperHoldersHandler")
spider = GmgnTopHoldersSpider()
gmgn_developer_holders_dao = GmgnDeveloperHoldersDAO()


async def process_developer_holders(token_dict: Dict) -> Optional[List[Dict]]:
    """处理代币数据
    
    Args:
        token_dict: 代币数据
        
    Returns:
        Optional[List[Dict]]: 处理后的代币数据，如果处理失败则返回None
    """
    if not token_dict:
        logger.warning("收到空的代币数据")
        return None
    
    # 获取代币地址
    token_address = token_dict.get('address')
    if not token_address:
        logger.warning(f"代币数据缺少地址字段: {token_dict}")
        return None
    
    chain = "sol"
    
    # 使用dev标签获取开发者持有者数据
    holders = await spider.get_top_holders(
        chain=chain, 
        address=token_address, 
        tag="dev",
        orderby="amount_percentage",
        direction="desc",
        limit=100,
        cost=20
    )
    
    if not holders:
        logger.warning(f"没有开发者持有这个币: {token_address}")
        return None
    
    return holders


async def validate(data: Any) -> bool:
    """验证代币链接数据是否有效
    
    Args:
        data: 代币链接数据
        
    Returns:
        bool: 如果数据有效则返回True，否则返回False
    """
    return True


async def store_data(data: List[Dict]) -> int:
    """存储代币数据
    
    Args:
        data: 代币数据列表
        
    Returns:
        int: 存储的记录数
    """
    if not data:
        return 0
    
    holders = data if isinstance(data, list) else [data]
        
    try:
        # 批量更新开发者持有者数据
        count = await gmgn_developer_holders_dao.upsert_holders_many("sol", holders)
        logger.info(f"更新了 {count} 条开发者持有者记录")
        return count
    except Exception as e:
        logger.error(f"存储开发者持有者数据时发生错误: {str(e)}\n{traceback.format_exc()}")
        return False