name: "KOL打分工作流"
description: "定期获取并处理待打分的KOL交易组合 (使用聚合查询)"

nodes:
  - name: "ProcessKOLScoringNode"  # 节点名可以自定义，保持描述性
    node_type: "input"             # 类型为 input
    interval: 300                 # 每5分钟运行一次
    generate_data: workflows.kol_scoring_workflow.handler.process_kol_scores
    # 参数现在从配置系统中读取，无需在YAML中指定
    # flow_control: 可以根据需要保留或调整
    #   max_pending_messages: 1 
    #   check_interval: 30
    #   enable_flow_control: true # 对于InputNode，如果启用，需要output_queues

# 旧的节点定义已移除/合并
#  - name: "FindPendingScoringTradeNode"
#    node_type: "input"
#    interval: 300
#    generate_data: workflows.kol_scoring_workflow.handler.find_pending_scoring_trades
#    flow_control:
#      max_pending_messages: 50
#      check_interval: 30
#      enable_flow_control: true
#
#  - name: "ScoreTradeNode"
#    node_type: "storage"
#    depend_ons: ["FindPendingScoringTradeNode"]
#    store_data: workflows.kol_scoring_workflow.handler.score_trade_pairs
#    validate: workflows.kol_scoring_workflow.handler.validate_scoring_result 