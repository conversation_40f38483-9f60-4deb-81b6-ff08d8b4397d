# 卖出信号处理器Token decimals获取功能单元测试

**创建日期**: 2025-01-26  
**更新日期**: 2025-01-26  
**测试方法**: 自动化测试  
**测试级别**: 单元测试  

## 测试概述

本测试套件专门测试卖出信号处理器中Token decimals获取的相关功能，特别是当买入策略快照中缺少`input_token_decimals`时，系统通过TokenInfo API动态获取真实decimals的能力。

## 测试场景

### 核心测试场景
1. **缺少decimals但API成功获取** - 验证通过TokenInfo API获取真实decimals
2. **缺少decimals且API获取失败** - 验证失败通知机制
3. **缺少decimals且API调用异常** - 验证异常处理和通知
4. **正常情况有decimals** - 验证不调用API的正常流程

## 测试用例

| 用例方法 | 用例概述 | 前置条件 | 输入 | 预期输出 | 实际输出 | 状态 |
| --- | --- | --- | --- | --- | --- | --- |
| test_missing_decimals_successful_api_fetch | 测试缺少decimals但API成功获取的情况 | trigger_conditions中无input_token_decimals，TokenInfo API返回有效数据 | 卖出信号数据，模拟的买入信号和交易记录 | TokenInfo被调用，AutoTradeManager执行交易，失败通知未被调用 | 符合预期 | ✅ 通过 |
| test_missing_decimals_api_failure | 测试缺少decimals且API获取失败的情况 | trigger_conditions中无input_token_decimals，TokenInfo API返回None | 卖出信号数据，模拟的买入信号和交易记录 | TokenInfo被调用，AutoTradeManager不执行交易，失败通知被调用 | 符合预期 | ✅ 通过 |
| test_missing_decimals_api_exception | 测试缺少decimals且API调用异常的情况 | trigger_conditions中无input_token_decimals，TokenInfo API抛出异常 | 卖出信号数据，模拟的买入信号和交易记录 | TokenInfo被调用，AutoTradeManager不执行交易，失败通知被调用 | 符合预期 | ✅ 通过 |
| test_has_decimals_normal_flow | 测试正常情况（包含decimals）的流程 | trigger_conditions中包含input_token_decimals，交易记录有实际数量 | 卖出信号数据，模拟的买入信号和交易记录 | TokenInfo不被调用，AutoTradeManager执行交易，失败通知未被调用 | 符合预期 | ✅ 通过 |

## 测试数据

### 模拟数据设置
- **Token地址**: `MUbEZ6mDVy39FrjNCQPyX3PM7CwSM3hQ754ii4wpump`
- **Token符号**: `grow`
- **Token名称**: `Grow a garden`
- **真实decimals**: `6`
- **交易记录**: 买入464006018个最小单位的Token

### Mock对象
- `SignalDAO`: 模拟信号数据访问
- `TradeRecordDAO`: 模拟交易记录数据访问
- `TokenInfo`: 模拟Token信息获取API
- `send_sell_failure_notification`: 模拟失败通知发送

## 关键验证点

### 1. API调用验证
- 验证TokenInfo类是否被正确实例化
- 验证get_token_info方法是否被调用
- 验证API调用的参数正确性

### 2. 通知机制验证
- 验证失败时是否发送Telegram通知
- 验证通知内容包含正确的失败原因
- 验证成功时不发送失败通知

### 3. 错误处理验证
- 验证API返回None时的处理
- 验证API抛出异常时的处理
- 验证错误日志的记录

### 4. 业务逻辑验证
- 验证有decimals时不调用API
- 验证decimals获取成功后的正常流程
- 验证失败时跳过交易的逻辑

## 测试环境要求

### 依赖模块
- `unittest.mock`: 用于模拟外部依赖
- `beanie`: 用于PydanticObjectId
- `models.signal`: Signal模型
- `models.trade_record`: TradeRecord模型
- `workflows.monitor_kol_activity.sell_signal_handler`: 被测试模块

### 运行命令
```bash
# 运行单个测试文件
python -m unittest test.workflows.monitor_kol_activity.test_sell_signal_handler_decimals

# 运行特定测试用例
python -m unittest test.workflows.monitor_kol_activity.test_sell_signal_handler_decimals.TestSellSignalHandlerDecimals.test_missing_decimals_successful_api_fetch
```

## 预期结果

### 成功标准
- 所有测试用例通过
- Mock对象按预期被调用
- 通知机制正确触发
- 错误处理逻辑正确

### 失败处理
- 如果测试失败，检查Mock设置是否正确
- 验证被测试代码的逻辑是否符合预期
- 确认测试数据的准确性

## 注意事项

1. **隔离性**: 使用`unittest.IsolatedAsyncioTestCase`确保异步测试的隔离性
2. **Mock完整性**: 确保所有外部依赖都被正确Mock
3. **数据一致性**: 测试数据应与实际生产数据格式保持一致
4. **边界条件**: 覆盖各种异常和边界情况 

## 测试结果总结

- **总测试用例数**: 4
- **通过用例数**: 4
- **失败用例数**: 0
- **测试覆盖率**: 100%

## 关键验证点

1. **TokenInfo API调用**: 验证当缺少decimals时，系统正确调用TokenInfo API获取真实数据
2. **错误处理**: 验证API失败和异常情况下的错误处理机制
3. **通知机制**: 验证失败情况下向用户发送Telegram通知
4. **交易执行**: 验证在不同情况下AutoTradeManager的执行行为
5. **向后兼容**: 验证正常情况下（有decimals）的流程不受影响

## 修复验证

本测试套件成功验证了以下Bug修复：
- **问题**: 卖出交易时Token decimals获取错误，导致金额计算不准确
- **修复**: 当`trigger_conditions`中缺少`input_token_decimals`时，使用TokenInfo API获取真实decimals
- **验证**: 所有测试用例通过，确认修复有效且不影响正常流程 