from typing import Optional, List, Dict, Any
from datetime import datetime
from beanie import Link
from models.trade_score_log import TradeScoreLog
from models.kol_wallet import KOLWallet
from dao.kol_wallet_dao import KOLWalletDAO
import logging

logger = logging.getLogger(__name__)

class TradeScoreLogDAO:
    """交易打分日志数据访问对象"""
    
    def __init__(self):
        self.kol_wallet_dao = KOLWalletDAO()
    
    async def create_log_entry(self, log_data: TradeScoreLog) -> Optional[TradeScoreLog]:
        """
        创建交易打分日志记录
        
        Args:
            log_data: 要创建的TradeScoreLog实例
            
        Returns:
            创建的TradeScoreLog对象或None
        """
        try:
            await log_data.save()
            logger.info(f"创建交易打分日志成功: buy={log_data.buy_trade_record_id}, "
                       f"sell={log_data.sell_trade_record_id}, kol={log_data.kol_wallet}, "
                       f"strategy={log_data.strategy_name}")
            return log_data
            
        except Exception as e:
            logger.error(f"创建交易打分日志失败: error={str(e)}")
            return None
    
    async def has_log_entry_existed(
        self, 
        buy_trade_record_id: str, 
        sell_trade_record_id: str, 
        kol_wallet_address: str, 
        strategy_name: str
    ) -> bool:
        """
        检查指定交易对、KOL和策略的打分日志是否已存在
        
        Args:
            buy_trade_record_id: 买入交易记录ID
            sell_trade_record_id: 卖出交易记录ID
            kol_wallet_address: KOL钱包地址
            strategy_name: 策略名称
            
        Returns:
            日志是否已存在
        """
        try:
            # 获取KOL钱包对象
            kol_wallet = await self.kol_wallet_dao.find_by_wallet_address(kol_wallet_address)
            if not kol_wallet:
                logger.warning(f"未找到KOL钱包: {kol_wallet_address}")
                return False
            
            # 查询是否存在匹配记录
            existing_log = await TradeScoreLog.find_one(
                TradeScoreLog.buy_trade_record_id == buy_trade_record_id,
                TradeScoreLog.sell_trade_record_id == sell_trade_record_id,
                TradeScoreLog.kol_wallet._id == kol_wallet.id,
                TradeScoreLog.strategy_name == strategy_name
            )
            
            return existing_log is not None
            
        except Exception as e:
            logger.error(f"检查交易打分日志存在性失败: buy={buy_trade_record_id}, "
                        f"sell={sell_trade_record_id}, kol={kol_wallet_address}, "
                        f"strategy={strategy_name}, error={str(e)}")
            return False
    
    async def create_scoring_log(
        self,
        buy_trade_record_id: str,
        sell_trade_record_id: str,
        kol_wallet_address: str,
        strategy_name: str,
        pnl_at_scoring: float,
        positive_score_applied: float = 0.0,
        negative_score_applied: float = 0.0,
        scoring_params_snapshot: Optional[Dict[str, Any]] = None
    ) -> Optional[TradeScoreLog]:
        """
        创建交易打分日志的便捷方法
        
        Args:
            buy_trade_record_id: 买入交易记录ID
            sell_trade_record_id: 卖出交易记录ID
            kol_wallet_address: KOL钱包地址
            strategy_name: 策略名称
            pnl_at_scoring: 计算的PnL
            positive_score_applied: 应用的加分值
            negative_score_applied: 应用的扣分值
            scoring_params_snapshot: 打分参数快照
            
        Returns:
            创建的TradeScoreLog对象或None
        """
        try:
            # 获取KOL钱包对象
            kol_wallet = await self.kol_wallet_dao.find_by_wallet_address(kol_wallet_address)
            if not kol_wallet:
                logger.error(f"无法创建交易打分日志：KOL钱包不存在: {kol_wallet_address}")
                return None
            
            # 创建日志记录
            log_entry = TradeScoreLog(
                buy_trade_record_id=buy_trade_record_id,
                sell_trade_record_id=sell_trade_record_id,
                kol_wallet=Link(kol_wallet, KOLWallet),
                kol_wallet_address=kol_wallet_address,
                strategy_name=strategy_name,
                pnl_at_scoring=pnl_at_scoring,
                positive_score_applied=positive_score_applied,
                negative_score_applied=negative_score_applied,
                scoring_params_snapshot=scoring_params_snapshot or {}
            )
            
            return await self.create_log_entry(log_entry)
            
        except Exception as e:
            logger.error(f"创建交易打分日志失败: buy={buy_trade_record_id}, "
                        f"sell={sell_trade_record_id}, kol={kol_wallet_address}, "
                        f"strategy={strategy_name}, error={str(e)}")
            return None
    
    async def get_logs_by_kol_and_strategy(
        self, 
        kol_wallet_address: str, 
        strategy_name: str, 
        limit: int = 50
    ) -> List[TradeScoreLog]:
        """
        获取指定KOL和策略的打分日志列表
        
        Args:
            kol_wallet_address: KOL钱包地址
            strategy_name: 策略名称
            limit: 返回记录数量限制
            
        Returns:
            打分日志列表
        """
        try:
            kol_wallet = await self.kol_wallet_dao.find_by_wallet_address(kol_wallet_address)
            if not kol_wallet:
                logger.warning(f"未找到KOL钱包: {kol_wallet_address}")
                return []
            
            logs = await TradeScoreLog.find(
                TradeScoreLog.kol_wallet._id == kol_wallet.id,
                TradeScoreLog.strategy_name == strategy_name
            ).sort("-scored_at").limit(limit).to_list()
            
            return logs
            
        except Exception as e:
            logger.error(f"获取KOL策略打分日志失败: kol={kol_wallet_address}, "
                        f"strategy={strategy_name}, error={str(e)}")
            return []
    
    async def get_logs_by_trade_pair(
        self, 
        buy_trade_record_id: str, 
        sell_trade_record_id: str
    ) -> List[TradeScoreLog]:
        """
        获取指定交易对的所有打分日志
        
        Args:
            buy_trade_record_id: 买入交易记录ID
            sell_trade_record_id: 卖出交易记录ID
            
        Returns:
            该交易对的所有打分日志列表
        """
        try:
            logs = await TradeScoreLog.find(
                TradeScoreLog.buy_trade_record_id == buy_trade_record_id,
                TradeScoreLog.sell_trade_record_id == sell_trade_record_id
            ).sort("-scored_at").to_list()
            
            return logs
            
        except Exception as e:
            logger.error(f"获取交易对打分日志失败: buy={buy_trade_record_id}, "
                        f"sell={sell_trade_record_id}, error={str(e)}")
            return []
    
    async def get_recent_logs(self, limit: int = 100) -> List[TradeScoreLog]:
        """
        获取最近的打分日志
        
        Args:
            limit: 返回记录数量限制
            
        Returns:
            最近的打分日志列表
        """
        try:
            logs = await TradeScoreLog.find().sort("-scored_at").limit(limit).to_list()
            return logs
            
        except Exception as e:
            logger.error(f"获取最近打分日志失败: error={str(e)}")
            return []
    
    async def count_logs_by_strategy(self, strategy_name: str) -> int:
        """
        统计指定策略的打分日志数量
        
        Args:
            strategy_name: 策略名称
            
        Returns:
            日志数量
        """
        try:
            count = await TradeScoreLog.find(
                TradeScoreLog.strategy_name == strategy_name
            ).count()
            
            return count
            
        except Exception as e:
            logger.error(f"统计策略打分日志数量失败: strategy={strategy_name}, error={str(e)}")
            return 0 