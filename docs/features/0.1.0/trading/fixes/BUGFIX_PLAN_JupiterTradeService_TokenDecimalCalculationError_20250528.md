# Jupiter交易服务代币精度计算错误修复方案

## Bug 标识
**Bug描述**: Jupiter交易服务在执行卖出交易时，代币数量计算错误，导致实际交易数量比预期少1000倍

**Bug ID**: TokenDecimalCalculationError_JupiterTradeService

## 报告日期/发现日期
**发现日期**: 2025-05-28T08:49:27+08:00

## 根源分析概要
通过分析生产日志和源代码，发现Bug的根本原因在于 `utils/trading/solana/jupiter_trade_service.py` 文件的 `_execute_single_trade_attempt` 方法（第382-386行）中的代币精度计算逻辑存在问题：

1. **问题代码位置**: 第382-386行
```python
# 2. 计算输入数量（转换为最小单位）
if input_token_address == SOL_MINT_ADDRESS:
    input_amount_native = self._calculate_token_amount(amount_input_token, 9)  # SOL有9位精度
else:
    # 对于SPL代币，从strategy_snapshot获取精度
    input_decimals = strategy_snapshot.get('input_token_decimals', 6)  # 默认6位精度
    input_amount_native = self._calculate_token_amount(amount_input_token, input_decimals)
```

2. **根本原因**: 
   - 代码依赖策略配置中的 `input_token_decimals` 来获取SPL代币精度
   - 该配置项可能不存在或不正确，导致使用默认的6位精度
   - 实际代币可能有9位精度，造成1000倍的计算错误

3. **具体错误**:
   - 期望: 624.290605456 × 10^9 = 624,290,605,456 native units
   - 实际: 624.290605456 × 10^6 = 624,290,605 native units
   - 差异: 1000倍 (10^9 / 10^6 = 1000)

## 详细的、已获批准的修复方案

### 1. 新增代币精度查询方法
在 `JupiterTradeService` 类中新增 `_get_token_decimals` 方法，使用现有的 `TokenInfo` 类：

```python
async def _get_token_decimals(self, token_address: str) -> int:
    """获取代币的精度信息
    
    Args:
        token_address: 代币地址
        
    Returns:
        int: 代币精度
    """
    # SOL的精度固定为9
    if token_address == SOL_MINT_ADDRESS:
        return 9
    
    # 检查缓存
    if hasattr(self, '_token_decimals_cache') and token_address in self._token_decimals_cache:
        logger.debug(f"Using cached decimals for token {token_address}: {self._token_decimals_cache[token_address]}")
        return self._token_decimals_cache[token_address]
    
    # 使用TokenInfo类获取代币信息（按照数据库 -> GMGN -> Solscan的顺序）
    try:
        from utils.spiders.solana.token_info import TokenInfo
        
        token_info_fetcher = TokenInfo(token_address, chain="sol")
        token_info = await token_info_fetcher.get_token_info()
        
        if token_info and 'decimals' in token_info:
            decimals = int(token_info['decimals'])
            
            # 缓存结果
            if not hasattr(self, '_token_decimals_cache'):
                self._token_decimals_cache = {}
            self._token_decimals_cache[token_address] = decimals
            
            logger.debug(f"Retrieved decimals for token {token_address}: {decimals}")
            return decimals
        else:
            logger.warning(f"Token info not found or missing decimals for {token_address}")
            
    except Exception as e:
        logger.warning(f"Failed to get token decimals for {token_address}: {e}")
    
    # 如果查询失败，返回常见的默认值
    logger.warning(f"Using default decimals (6) for token {token_address}")
    return 6
```

### 2. 修改代币数量计算逻辑
修改 `_execute_single_trade_attempt` 方法中的代币精度获取逻辑：

```python
# 2. 计算输入数量（转换为最小单位）
input_decimals = await self._get_token_decimals(input_token_address)
input_amount_native = self._calculate_token_amount(amount_input_token, input_decimals)

logger.info(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] Input amount: {amount_input_token} -> {input_amount_native} (native units, decimals: {input_decimals})")
```

### 3. 增强错误处理和日志记录
- 在代币精度查询失败时记录警告日志
- 在计算代币数量时记录使用的精度
- 添加缓存命中/未命中的调试日志

## 测试用例设计

### 测试用例1: SOL精度计算
- **输入**: SOL地址, 数量1.0
- **预期输出**: 1,000,000,000 native units (9位精度)
- **测试步骤**: 调用_get_token_decimals和_calculate_token_amount

### 测试用例2: SPL代币精度查询
- **输入**: 真实SPL代币地址
- **预期输出**: 正确的精度值
- **测试步骤**: 模拟TokenInfo类的响应，验证精度解析

### 测试用例3: 缓存机制测试
- **输入**: 相同代币地址的多次查询
- **预期输出**: 第二次查询使用缓存，不发起TokenInfo查询请求
- **测试步骤**: 验证TokenInfo调用次数

### 测试用例4: 错误处理测试
- **输入**: 无效代币地址
- **预期输出**: 返回默认精度6，记录警告日志
- **测试步骤**: 模拟TokenInfo查询错误，验证降级处理

### 测试用例5: 生产场景复现测试
- **输入**: 代币地址 `2fdtCHuvyLcD2q86XZGFmYbDux9ZbbUgMmFhzChqmoon`, 数量 624.290605456
- **预期输出**: 正确的native units（基于实际精度）
- **测试步骤**: 端到端测试修复后的计算逻辑

## 方案提出者/执行者
AI Assistant (Claude Sonnet 4)

## 方案审阅者/批准者
用户 (gaojerry)

## 方案批准日期
待用户确认

## 预期的验证方法
1. **单元测试验证**: 运行新增的测试用例，确保所有测试通过
2. **集成测试验证**: 使用真实的代币地址进行端到端测试
3. **生产环境验证**: 在测试环境中重现原始Bug场景，验证修复效果
4. **性能验证**: 确保新增的RPC查询不会显著影响交易执行时间

## 影响范围评估
- **直接影响**: `utils/trading/solana/jupiter_trade_service.py`
- **依赖模块**: `utils/spiders/solana/token_info.py` (现有模块，无需修改)
- **测试文件**: `test/utils/trading/solana/test_jupiter_trade_service.py`
- **向后兼容性**: 完全兼容，现有配置仍然有效
- **性能影响**: 首次查询代币精度时通过TokenInfo获取（数据库 -> GMGN -> Solscan），后续查询使用缓存

## 风险评估
- **低风险**: 修复方案向后兼容，不会破坏现有功能
- **网络风险**: 使用现有的TokenInfo类，已有完善的错误处理和重试机制
- **缓存风险**: 内存中的缓存在服务重启后会清空，但不影响功能正确性
- **依赖风险**: 依赖现有的TokenInfo类，该类已在生产环境中稳定运行

## 修复完成记录

### 修复执行日期
**完成日期**: 2025-05-28T09:00:14+08:00

### 修复实施概要
1. **新增方法**: 在 `JupiterTradeService` 类中添加了 `_get_token_decimals` 方法
2. **修改逻辑**: 更新了 `_execute_single_trade_attempt` 方法中的代币精度获取逻辑
3. **测试覆盖**: 添加了6个新的测试用例，覆盖所有场景

### 受影响的文件
- **主要文件**: `utils/trading/solana/jupiter_trade_service.py`
  - 新增 `_get_token_decimals` 方法（45行代码）
  - 修改 `_execute_single_trade_attempt` 方法（3行代码）
- **测试文件**: `test/utils/trading/solana/test_jupiter_trade_service.py`
  - 新增6个测试用例（约150行代码）

### 测试验证结果
- **所有测试通过**: 37个测试用例全部通过
- **Bug复现测试**: 确认原Bug存在（1000倍差异）
- **修复验证测试**: 确认Bug已修复
- **回归测试**: 所有现有功能正常

### 修复效果验证
**生产场景测试结果**:
- 代币地址: `2fdtCHuvyLcD2q86XZGFmYbDux9ZbbUgMmFhzChqmoon`
- 输入数量: 624.290605456
- 修复前: 624,290,605 native units (6位精度)
- 修复后: 624,290,605,456 native units (9位精度)
- 修复倍数: 1000倍
- **✅ Bug已完全修复**

### 简要提交信息
```
fix(trading): 修复Jupiter交易服务代币精度计算错误

- 问题: SPL代币使用错误精度导致交易数量计算错误(1000倍差异)
- 根因: 依赖策略配置中的input_token_decimals，该值可能不存在或不正确
- 修复: 使用TokenInfo类动态获取代币精度(数据库->GMGN->Solscan)
- 影响: utils/trading/solana/jupiter_trade_service.py
- 测试: 新增6个测试用例，所有37个测试通过
- 验证: 生产场景测试确认Bug已修复(624M->624B native units)
```

### 性能影响评估
- **首次查询**: 增加TokenInfo查询时间（约100-500ms）
- **后续查询**: 使用内存缓存，无额外开销
- **整体影响**: 微小，相比交易执行时间可忽略

### 向后兼容性
- **完全兼容**: 现有配置和代码无需修改
- **降级处理**: TokenInfo查询失败时使用默认精度6
- **缓存机制**: 减少重复查询，提高性能

### 后续建议
1. **监控**: 关注生产环境中代币精度查询的成功率和性能
2. **优化**: 考虑将常用代币精度预缓存到数据库
3. **文档**: 更新交易服务使用文档，说明新的精度获取机制 