from typing import Dict, Any, Optional
from datetime import datetime
from models.alert_event_record import Alert<PERSON>ventRecord
from .base_dao import BaseDAO

class AlertEventRecordDAO(BaseDAO[AlertEventRecord]):
    """DAO for AlertEventRecord model."""

    def __init__(self):
        super().__init__(AlertEventRecord)

    async def create_alert_event(
        self,
        monitor_type: str,
        category: str,
        wallet_address: Optional[str],
        activity_tx_hash: Optional[str],
        discrepancy_seconds: Optional[float],
        message: str,
        details: Optional[Dict[str, Any]] = None,
        trigger_time: Optional[datetime] = None
    ) -> AlertEventRecord:
        """Create and save a new alert event record."""
        if trigger_time is None:
            trigger_time = datetime.utcnow()
        
        alert_event = AlertEventRecord(
            monitor_type=monitor_type,
            category=category,
            wallet_address=wallet_address,
            activity_tx_hash=activity_tx_hash,
            discrepancy_seconds=discrepancy_seconds,
            message=message,
            details=details or {},
            trigger_time=trigger_time
        )
        await self.insert_one(alert_event)
        return alert_event 