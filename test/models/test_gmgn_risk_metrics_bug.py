"""
GmgnRiskMetrics Bug复现和修复验证测试

测试模块: models.gmgn_wallet_stats.GmgnRiskMetrics
Bug: 当API数据中风险指标字段缺失时，字段接收None值但类型定义不允许None，导致Pydantic验证失败
修复前应该失败，修复后应该通过
"""

import unittest
from typing import Dict, Any
from pydantic import ValidationError

from models.gmgn_wallet_stats import GmgnWalletStats, GmgnRiskMetrics


class TestGmgnRiskMetricsBugFix(unittest.TestCase):
    """测试GmgnRiskMetrics Bug修复"""
    
    def setUp(self):
        """测试前置设置"""
        self.test_wallet_address = "********************************************"
        
    def test_bug_reproduction_incomplete_risk_data(self):
        """
        Bug复现测试：API数据中风险指标部分字段缺失
        
        这个测试应该能复现当前的Bug：
        - API数据中risk字段存在，但缺少某些子字段
        - safe_int()和safe_float()返回None
        - GmgnRiskMetrics验证失败（修复前）
        - 修复后应该能正常通过
        """
        # 模拟safe_int()和safe_float()返回None的情况
        # 这模拟了API数据中字段缺失的场景
        incomplete_risk_data = {
            "token_active": 2,                # 存在
            "token_honeypot": 1,              # 存在
            "token_honeypot_ratio": None,     # 缺失 - safe_float()返回None
            "no_buy_hold": None,              # 缺失 - safe_int()返回None
            "no_buy_hold_ratio": None,        # 缺失 - safe_float()返回None
            "sell_pass_buy": None,            # 缺失 - safe_int()返回None
            "sell_pass_buy_ratio": None,      # 缺失 - safe_float()返回None
            "fast_tx": 3,                     # 存在
            "fast_tx_ratio": None             # 缺失 - safe_float()返回None
        }
        
        try:
            # 尝试直接创建GmgnRiskMetrics实例
            risk = GmgnRiskMetrics(**incomplete_risk_data)
            
            # 如果能成功创建，说明Bug已修复
            self.assertEqual(risk.token_active, 2)
            self.assertEqual(risk.token_honeypot, 1)
            self.assertEqual(risk.fast_tx, 3)
            
            # 这些字段在API中缺失，但模型应该能处理
            # 修复后它们可能是None或默认值，但不应该导致验证错误
            print(f"token_honeypot_ratio: {risk.token_honeypot_ratio}")
            print(f"no_buy_hold: {risk.no_buy_hold}")
            print(f"no_buy_hold_ratio: {risk.no_buy_hold_ratio}")
            print(f"sell_pass_buy: {risk.sell_pass_buy}")
            print(f"sell_pass_buy_ratio: {risk.sell_pass_buy_ratio}")
            print(f"fast_tx_ratio: {risk.fast_tx_ratio}")
            
        except ValidationError as e:
            # 如果出现ValidationError，说明Bug还没修复
            self.fail(f"Bug still exists: ValidationError when creating GmgnRiskMetrics with incomplete risk data: {e}")
        except Exception as e:
            # 其他异常
            self.fail(f"Unexpected error when creating GmgnRiskMetrics: {e}")
            
    def test_bug_reproduction_direct_risk_creation(self):
        """
        直接测试GmgnRiskMetrics创建时的Bug
        
        直接用None值创建GmgnRiskMetrics实例
        修复前应该失败，修复后应该成功
        """
        try:
            # 尝试直接用None值创建GmgnRiskMetrics
            risk = GmgnRiskMetrics(
                token_active=2,
                token_honeypot=1,
                token_honeypot_ratio=None,  # 这个None值应该导致验证失败（修复前）
                no_buy_hold=0,
                no_buy_hold_ratio=None,     # 这个None值应该导致验证失败（修复前）
                sell_pass_buy=None,         # 这个None值应该导致验证失败（修复前）
                sell_pass_buy_ratio=None,   # 这个None值应该导致验证失败（修复前）
                fast_tx=3,
                fast_tx_ratio=None          # 这个None值应该导致验证失败（修复前）
            )
            
            # 如果能成功创建，说明Bug已修复
            self.assertEqual(risk.token_active, 2)
            self.assertEqual(risk.token_honeypot, 1)
            self.assertEqual(risk.fast_tx, 3)
            
            print(f"Direct creation successful:")
            print(f"  token_honeypot_ratio: {risk.token_honeypot_ratio}")
            print(f"  no_buy_hold_ratio: {risk.no_buy_hold_ratio}")
            print(f"  sell_pass_buy: {risk.sell_pass_buy}")
            print(f"  sell_pass_buy_ratio: {risk.sell_pass_buy_ratio}")
            print(f"  fast_tx_ratio: {risk.fast_tx_ratio}")
            
        except ValidationError as e:
            # 如果出现ValidationError，说明Bug还没修复
            self.fail(f"Bug still exists: ValidationError when creating GmgnRiskMetrics with None values: {e}")
        except Exception as e:
            # 其他异常
            self.fail(f"Unexpected error when creating GmgnRiskMetrics: {e}")
            
    def test_normal_case_with_complete_data(self):
        """
        正常情况测试：API数据完整
        
        这个测试在修复前后都应该通过
        """
        complete_risk_data = {
            "token_active": 2,
            "token_honeypot": 1,
            "token_honeypot_ratio": 0.125,
            "no_buy_hold": 0,
            "no_buy_hold_ratio": 0.0,
            "sell_pass_buy": 1,
            "sell_pass_buy_ratio": 0.083,
            "fast_tx": 3,
            "fast_tx_ratio": 0.2
        }
        
        try:
            # 直接测试GmgnRiskMetrics创建，避免数据库相关问题
            risk = GmgnRiskMetrics(**complete_risk_data)
            
            # 验证所有字段都正确设置
            self.assertEqual(risk.token_active, 2)
            self.assertEqual(risk.token_honeypot, 1)
            self.assertEqual(risk.token_honeypot_ratio, 0.125)
            self.assertEqual(risk.no_buy_hold, 0)
            self.assertEqual(risk.no_buy_hold_ratio, 0.0)
            self.assertEqual(risk.sell_pass_buy, 1)
            self.assertEqual(risk.sell_pass_buy_ratio, 0.083)
            self.assertEqual(risk.fast_tx, 3)
            self.assertEqual(risk.fast_tx_ratio, 0.2)
            
        except Exception as e:
            self.fail(f"Normal case should always work: {e}")
            
    def test_no_risk_field_case(self):
        """
        测试完全没有risk字段的情况
        
        这种情况在修复前后都应该能正常处理，risk字段应该是None
        """
        no_risk_api_data = {
            "buy": 10,
            "sell": 5,
            "pnl": 100.0,
            # 没有risk字段
        }
        
        # 由于这个测试涉及到完整的GmgnWalletStats创建，跳过数据库相关的验证错误
        # 只测试GmgnRiskMetrics的部分
        risk_data = no_risk_api_data.get("risk", {})
        if not risk_data:  # 没有risk字段时
            # 预期行为：不创建GmgnRiskMetrics实例
            print("No risk field case: risk data is empty, should skip GmgnRiskMetrics creation")
            self.assertEqual(len(risk_data), 0)
        else:
            self.fail("Should not reach here in no risk field case")


if __name__ == '__main__':
    unittest.main() 