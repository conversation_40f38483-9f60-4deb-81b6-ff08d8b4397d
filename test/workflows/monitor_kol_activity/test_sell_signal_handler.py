# test/workflows/monitor_kol_activity/test_sell_signal_handler.py
import unittest
from unittest.mock import patch, AsyncMock, MagicMock, call, create_autospec, ANY
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from beanie import PydanticObjectId
import logging
from zoneinfo import ZoneInfo
import os
from jinja2 import Template
from typing import Optional

# Import the module itself to access its members
import workflows.monitor_kol_activity.sell_signal_handler as sell_signal_handler_module 

# Functions/Classes to test
from workflows.monitor_kol_activity.sell_signal_handler import (
    generate_sell_signals,
    process_sell_signal,
    validate,
    SOL_MINT_ADDRESS
)
from models.signal import Signal
from models.config import SingleKolStrategyConfig, KolActivityConfig, Config
from models.token_message_send_history import TokenMessageSendHistory
from models.trade_record import TradeRecord, TradeStatus as ModelTradeStatus, TradeType as ModelTradeType
from utils.trading.auto_trade_manager import AutoTradeManager
from models.trade_execution import TradeExecutionResult, TradeStatus as InterfaceTradeStatus
from utils.trading.solana.trade_interface import TradeType as InterfaceTradeType

# DAO imports for spec_set
from dao.signal_dao import SignalDAO
from dao.trade_record_dao import TradeRecordDAO
from dao.config_dao import ConfigDAO
from dao.kol_wallet_activity_dao import KOLWalletActivityDAO
from dao.token_message_send_history_dao import TokenMessageSendHistoryDAO
from dao.telegram_users_dao import TelegramUserDAO
from utils.message_sender.message_sender import TelegramMessageSender

# Disable logging during tests unless specifically needed
# logging.disable(logging.CRITICAL)

# Base class for async tests
class TestSellSignalHandler(unittest.IsolatedAsyncioTestCase):

    def setUp(self):
        # Common mocks for DAOs
        self.mock_signal_dao_instance = MagicMock(spec_set=SignalDAO)
        self.mock_trade_record_dao_instance = MagicMock(spec_set=TradeRecordDAO)
        self.mock_config_dao_instance = MagicMock(spec_set=ConfigDAO)
        self.mock_kol_activity_dao_instance = MagicMock(spec_set=KOLWalletActivityDAO)
        self.mock_token_message_send_history_dao_instance = MagicMock(spec_set=TokenMessageSendHistoryDAO)
        self.mock_telegram_user_dao_instance = MagicMock(spec_set=TelegramUserDAO)

        # Mock for AutoTradeManager
        self.mock_auto_trade_manager_instance = MagicMock(spec=AutoTradeManager)  # Use spec instead of spec_set
        self.mock_auto_trade_manager_instance.execute_trade = AsyncMock()
        
        # Mock config_manager for the new architecture
        self.mock_config_manager = AsyncMock()
        self.mock_auto_trade_manager_instance.config_manager = self.mock_config_manager
        
        self.mock_auto_trade_manager_constructor = MagicMock(return_value=self.mock_auto_trade_manager_instance)

        # Mock for Signal constructor
        def signal_constructor_side_effect(*args, **kwargs):
            instance = MagicMock(spec=Signal) 
            instance.id = PydanticObjectId() 
            instance.trade_record_ids = []  
            for key, value in kwargs.items():
                setattr(instance, key, value)
            instance.save = AsyncMock(return_value=instance) 
            return instance
        self.mock_signal_constructor = MagicMock(side_effect=signal_constructor_side_effect)
        
        # Mock for TelegramMessageSender
        self.mock_telegram_sender_instance = MagicMock(spec_set=TelegramMessageSender) 
        self.mock_telegram_sender_instance.send_message_to_user = AsyncMock(return_value=True)
        self.mock_telegram_sender_constructor = MagicMock(return_value=self.mock_telegram_sender_instance)

        # Setup patches
        self.patcher_signal_dao = patch('workflows.monitor_kol_activity.sell_signal_handler.SignalDAO', return_value=self.mock_signal_dao_instance)
        self.patcher_trade_record_dao = patch('workflows.monitor_kol_activity.sell_signal_handler.TradeRecordDAO', return_value=self.mock_trade_record_dao_instance)
        self.patcher_config_dao = patch('workflows.monitor_kol_activity.sell_signal_handler.ConfigDAO', return_value=self.mock_config_dao_instance)
        self.patcher_kol_activity_dao = patch('workflows.monitor_kol_activity.sell_signal_handler.KOLWalletActivityDAO', return_value=self.mock_kol_activity_dao_instance)
        self.patcher_token_message_send_history_dao = patch('workflows.monitor_kol_activity.sell_signal_handler.TokenMessageSendHistoryDAO', return_value=self.mock_token_message_send_history_dao_instance)
        self.patcher_telegram_user_dao = patch('workflows.monitor_kol_activity.sell_signal_handler.TelegramUserDAO', return_value=self.mock_telegram_user_dao_instance)
        
        self.patcher_auto_trade_manager = patch('workflows.monitor_kol_activity.sell_signal_handler.AutoTradeManager', self.mock_auto_trade_manager_constructor)
        self.patcher_signal_constructor = patch('workflows.monitor_kol_activity.sell_signal_handler.Signal', self.mock_signal_constructor)
        self.patcher_telegram_sender = patch('workflows.monitor_kol_activity.sell_signal_handler.TelegramMessageSender', self.mock_telegram_sender_constructor)

        # Configure mock for insert_signals
        mock_returned_by_insert_await = MagicMock(name="MockedInsertResult_v3")
        mock_returned_by_insert_await.inserted_ids = ["definitive_mock_id_v3"]
        mock_returned_by_insert_await.__bool__ = lambda s: True
        self.mock_signal_dao_instance.insert_signals = AsyncMock(return_value=mock_returned_by_insert_await)

        # Start patches
        self.mock_signal_dao_patched = self.patcher_signal_dao.start()
        self.mock_trade_record_dao_patched = self.patcher_trade_record_dao.start()
        self.mock_config_dao_patched = self.patcher_config_dao.start()
        self.mock_kol_activity_dao_patched = self.patcher_kol_activity_dao.start()
        self.mock_token_message_send_history_dao_patched = self.patcher_token_message_send_history_dao.start()
        self.mock_telegram_user_dao_patched = self.patcher_telegram_user_dao.start()

        self.mock_auto_trade_manager_constructor_patched = self.patcher_auto_trade_manager.start()
        self.mock_signal_constructor_patched = self.patcher_signal_constructor.start()
        self.mock_telegram_sender_constructor_patched = self.patcher_telegram_sender.start()
        
        self.addCleanup(self.patcher_signal_dao.stop)
        self.addCleanup(self.patcher_trade_record_dao.stop)
        self.addCleanup(self.patcher_config_dao.stop)
        self.addCleanup(self.patcher_kol_activity_dao.stop)
        self.addCleanup(self.patcher_token_message_send_history_dao.stop)
        self.addCleanup(self.patcher_telegram_user_dao.stop)
        self.addCleanup(self.patcher_auto_trade_manager.stop)
        self.addCleanup(self.patcher_signal_constructor.stop)
        self.addCleanup(self.patcher_telegram_sender.stop)

        # Mock get_all_users for notifications
        self.mock_telegram_user_dao_instance.get_all_users = AsyncMock(return_value=[{'chat_id': 'user123'}])
        self.mock_trade_record_dao_instance.save = AsyncMock()

    def _create_mock_buy_signal(
        self,
        signal_id: PydanticObjectId,
        token_address: str,
        token_symbol: str = "MOCK",
        trade_record_ids: Optional[list[PydanticObjectId]] = None,
        strategy_name: str = "test_buy_strat",
        gmgn_enable_auto_sell: bool = True,
        gmgn_api_host: str = "mock_host",
        gmgn_private_key_env_var: str = "MOCK_PVT_KEY_ENV",
        gmgn_sol_wallet_address: str = "mock_wallet_addr",
        **extra_trigger_conditions
    ) -> Signal:
        mock_signal = MagicMock(spec=Signal) 
        mock_signal.id = signal_id
        mock_signal.token_address = token_address 
        mock_signal.token_symbol = token_symbol
        mock_signal.token_name = f"{token_symbol} Token"
        mock_signal.signal_type = "kol_buy"
        mock_signal.status = "open"
        mock_signal.trade_record_ids = trade_record_ids or []
        
        actual_trigger_conditions_dict = {
            "strategy_name": strategy_name,
            "gmgn_enable_auto_sell": gmgn_enable_auto_sell,
            "gmgn_api_host": gmgn_api_host,
            "gmgn_private_key_env_var": gmgn_private_key_env_var,
            "gmgn_sol_wallet_address": gmgn_sol_wallet_address,
            **extra_trigger_conditions 
        }
        
        # Set default decimals for SOL or SPL tokens
        if token_address == SOL_MINT_ADDRESS:
            actual_trigger_conditions_dict["input_token_decimals"] = 9
        else:
            if "input_token_decimals" not in actual_trigger_conditions_dict:
                actual_trigger_conditions_dict["input_token_decimals"] = 6  # Default SPL decimals

        mock_signal.trigger_conditions = actual_trigger_conditions_dict.copy()
        mock_signal.created_at = datetime.now(timezone.utc)
        mock_signal.updated_at = datetime.now(timezone.utc)
        mock_signal.save = AsyncMock()
        return mock_signal

    def _create_mock_buy_trade_record(
        self,
        record_id: PydanticObjectId,
        signal_id: PydanticObjectId,
        token_out_address: str,
        token_out_actual_amount_lamports: Optional[int]
    ) -> TradeRecord:
        mock_record = MagicMock(spec=TradeRecord) 
        mock_record.id = record_id
        mock_record.signal_id = signal_id
        mock_record.trade_type = ModelTradeType.BUY
        mock_record.status = ModelTradeStatus.SUCCESS
        mock_record.token_in_address = SOL_MINT_ADDRESS
        mock_record.token_in_amount = 0.1
        mock_record.token_out_address = token_out_address
        mock_record.token_out_actual_amount = token_out_actual_amount_lamports
        mock_record.token_out_decimals = None  # 新增：明确设置 token_out_decimals 为 None
        mock_record.executed_at = datetime.now(timezone.utc) - timedelta(minutes=5)
        mock_record.tx_hash = None  # 添加 tx_hash 属性
        mock_record.wallet_address = None  # 添加 wallet_address 属性
        return mock_record

    # === Tests for generate_sell_signals (keep existing tests) ===

    @patch('workflows.monitor_kol_activity.sell_signal_handler.ConfigDAO')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.SignalDAO')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.KOLWalletActivityDAO')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.datetime')
    @patch('workflows.monitor_kol_activity.sell_signal_handler.get_current_time_dt')
    async def test_generate_sell_signals_timeout_trigger(
        self, mock_get_current_time_dt, mock_datetime, mock_kol_dao_cls, mock_signal_dao_cls, mock_config_dao_cls
    ):
        # Mock setup
        mock_config_dao = mock_config_dao_cls.return_value
        mock_strategy_config = SingleKolStrategyConfig(
            strategy_name="test_strategy",
            sell_strategy_hours=24, 
            sell_kol_ratio=0.5, 
            transaction_lookback_hours=1
        )
        mock_config_data = KolActivityConfig(buy_strategies=[mock_strategy_config])
        mock_config_dao.get_config = AsyncMock(return_value=MagicMock(data=mock_config_data))

        mock_signal_dao = mock_signal_dao_cls.return_value
        eastern_zone = ZoneInfo("Asia/Shanghai")
        mock_now_utc = datetime(2024, 8, 2, 12, 0, 0, tzinfo=timezone.utc)
        mock_now_eastern = datetime(2024, 8, 2, 20, 0, 0, tzinfo=eastern_zone)
        mock_datetime.utcnow.return_value = mock_now_utc
        mock_get_current_time_dt.return_value = mock_now_eastern
        
        # Signal created more than 24 hours ago
        mock_buy_signal_id = PydanticObjectId()
        mock_open_buy_signal = MagicMock(spec=Signal)
        mock_open_buy_signal.id = mock_buy_signal_id
        mock_open_buy_signal.token_address = "token_addr_timeout"
        mock_open_buy_signal.token_name = "Timeout Token"
        mock_open_buy_signal.token_symbol = "TMT"
        mock_open_buy_signal.signal_type = "kol_buy"
        mock_open_buy_signal.status = "open"
        mock_open_buy_signal.trigger_conditions = {}
        mock_open_buy_signal.trigger_timestamp = mock_now_eastern - timedelta(hours=25)
        mock_open_buy_signal.created_at = mock_now_eastern - timedelta(hours=25)
        mock_open_buy_signal.hit_kol_wallets = ["kol1", "kol2"]
        
        mock_signal_dao.find_signals = AsyncMock(return_value=[mock_open_buy_signal])
        mock_kol_dao = mock_kol_dao_cls.return_value
        mock_kol_dao.aggregate = AsyncMock()

        # Execute
        generated_data = [item async for item in generate_sell_signals()]

        # Assertions
        self.assertEqual(len(generated_data), 1)
        expected_yield = {
            "token_address": "token_addr_timeout",
            "token_name": "Timeout Token",
            "token_symbol": "TMT",
            "buy_signal_ref_id": str(mock_buy_signal_id),
            "hit_kol_wallets": ["kol1", "kol2"],
            "sell_reason": "timeout",
            "sell_trigger_conditions": {'type': 'timeout', 'hours_threshold_used': 24},
            "sell_time": mock_now_eastern
        }
        self.assertDictEqual(generated_data[0], expected_yield)

    # === Tests for process_sell_signal (updated for AutoTradeManager) ===

    @patch('workflows.monitor_kol_activity.sell_signal_handler.SolanaMonitor')
    async def test_process_sell_signal_auto_sell_success_with_auto_trade_manager(self, mock_solana_monitor_cls):
        """测试使用 AutoTradeManager 成功执行自动卖出"""
        # --- Mock SolanaMonitor ---
        mock_solana_monitor = mock_solana_monitor_cls.return_value
        mock_solana_monitor.get_token_balance = AsyncMock(return_value=123.456)  # 钱包当前余额
        mock_solana_monitor.get_confirmed_token_output_from_tx = AsyncMock(return_value=None)  # 无交易哈希确认
        
        buy_signal_id = PydanticObjectId()
        buy_trade_record_id = PydanticObjectId()
        
        mock_spl_token_addr = "TestSPLTokenAddress123"
        mock_spl_lamports_bought = 123456000  # 123.456 with 6 decimals

        # Mock original buy TradeRecord
        mock_buy_trade_rec = self._create_mock_buy_trade_record(
            record_id=buy_trade_record_id,
            signal_id=buy_signal_id,
            token_out_address=mock_spl_token_addr,
            token_out_actual_amount_lamports=mock_spl_lamports_bought
        )
        mock_buy_trade_rec.wallet_address = "mock_wallet_addr"  # 添加钱包地址
        
        # Setup the get_by_id to return this specific record when called with the correct ID
        def get_by_id_side_effect(record_id):
            if record_id == buy_trade_record_id:
                return mock_buy_trade_rec
            elif record_id == mock_trade_execution_result.final_trade_record_id:
                return mock_final_trade_record  # This will be defined later
            return None
        
        # Mock original buy Signal
        mock_buy_signal = self._create_mock_buy_signal(
            signal_id=buy_signal_id,
            token_address=mock_spl_token_addr,
            token_symbol="SPLS",
            trade_record_ids=[buy_trade_record_id],
            gmgn_enable_auto_sell=True,
            input_token_decimals=6
        )
        
        sell_signal_input_data = [{
            "token_address": mock_spl_token_addr,
            "token_name": "SPL Test Token", 
            "token_symbol": "SPLS",
            "buy_signal_ref_id": str(buy_signal_id),
            "sell_reason": "test_auto_trade_manager", 
            "sell_time": datetime.now(timezone.utc)
        }]

                # Mock AutoTradeManager config to return enabled=True
        mock_auto_trade_config = MagicMock()
        mock_auto_trade_config.enabled = True
        self.mock_config_manager.get_config.return_value = mock_auto_trade_config

        # Mock AutoTradeManager to return success
        mock_trade_execution_result = TradeExecutionResult(
            final_status=InterfaceTradeStatus.SUCCESS,
            successful_channel="gmgn",
            final_trade_record_id=PydanticObjectId(),
            channel_attempts=[],
            total_execution_time=1.5,
            error_summary=None,
            started_at=datetime.now(timezone.utc),
            completed_at=datetime.now(timezone.utc)
        )
        self.mock_auto_trade_manager_instance.execute_trade.return_value = mock_trade_execution_result
        
        # Mock final trade record for notification formatting
        mock_final_trade_record = MagicMock(spec=TradeRecord)
        mock_final_trade_record.tx_hash = "0x1234567890abcdef"
        mock_final_trade_record.token_in_actual_amount = 123.456
        mock_final_trade_record.token_out_actual_amount = 987654321  # SOL lamports
        
        # Setup all the mock DAOs
        self.mock_signal_dao_instance.get_signal.return_value = mock_buy_signal
        self.mock_signal_dao_instance.find_one.return_value = None
        self.mock_trade_record_dao_instance.get_by_id.side_effect = get_by_id_side_effect

        # Execute
        await process_sell_signal(sell_signal_input_data)

        # Assertions
        # 1. AutoTradeManager.execute_trade was called
        self.mock_auto_trade_manager_instance.execute_trade.assert_called_once()
        
        # 2. Check arguments passed to AutoTradeManager
        call_args = self.mock_auto_trade_manager_instance.execute_trade.call_args
        call_kwargs = call_args.kwargs
        
        self.assertEqual(call_kwargs['trade_type'], InterfaceTradeType.SELL)
        self.assertEqual(call_kwargs['token_in_address'], mock_spl_token_addr)
        self.assertEqual(call_kwargs['token_out_address'], SOL_MINT_ADDRESS)
        self.assertEqual(call_kwargs['wallet_private_key_env_var'], "MOCK_PVT_KEY_ENV")
        self.assertEqual(call_kwargs['wallet_address'], "mock_wallet_addr")
        self.assertEqual(call_kwargs['strategy_name'], "test_buy_strat")
        
        # 3. UI amount calculation should be correct (123456000 / 10^6 = 123.456)
        expected_ui_amount = 123.456
        self.assertAlmostEqual(call_kwargs['amount'], expected_ui_amount, places=6)
        
        # 4. Signal and status updates
        self.mock_signal_dao_instance.insert_signals.assert_called_once()
        self.mock_signal_dao_instance.update_signal_status.assert_called_once_with(buy_signal_id, "sold")
        
        # 5. Notification sent
        self.mock_telegram_sender_instance.send_message_to_user.assert_called_once()

    async def test_process_sell_signal_auto_sell_disabled_skips_trade(self):
        """测试自动卖出禁用时跳过交易"""
        buy_signal_id = PydanticObjectId()
        mock_spl_token_addr = "SPLAutoSellOff"

        mock_buy_signal = self._create_mock_buy_signal(
            signal_id=buy_signal_id, 
            token_address=mock_spl_token_addr,
            strategy_name="test_strat_auto_sell_is_false",
            gmgn_enable_auto_sell=False  # 禁用自动卖出
        )

        self.mock_signal_dao_instance.get_signal.return_value = mock_buy_signal
        self.mock_signal_dao_instance.find_one.return_value = None

        sell_signal_input_data = [{
            "token_address": mock_spl_token_addr, 
            "buy_signal_ref_id": str(buy_signal_id), 
            "sell_reason": "test_auto_off", 
            "sell_time": datetime.now(timezone.utc)
        }]

        await process_sell_signal(sell_signal_input_data)

        # AutoTradeManager should not be called
        self.mock_auto_trade_manager_instance.execute_trade.assert_not_called()
        
        # Signal should still be created and buy signal updated
        self.mock_signal_dao_instance.insert_signals.assert_called_once()
        self.mock_signal_dao_instance.update_signal_status.assert_called_once_with(buy_signal_id, "sold")
        
        # Notification should be sent
        self.mock_telegram_sender_instance.send_message_to_user.assert_called_once()

    async def test_process_sell_signal_no_buy_trade_record_skips(self):
        """测试没有买入记录时跳过交易"""
        buy_signal_id = PydanticObjectId()
        mock_spl_token_addr = "NoBuyRecord"
        
        mock_buy_signal = self._create_mock_buy_signal(
            signal_id=buy_signal_id,
            token_address=mock_spl_token_addr,
            trade_record_ids=[],  # 没有交易记录
            gmgn_enable_auto_sell=True 
        )

        self.mock_signal_dao_instance.get_signal.return_value = mock_buy_signal
        self.mock_signal_dao_instance.find_one.return_value = None
        
        sell_signal_input_data = [{
            "token_address": mock_spl_token_addr, 
            "buy_signal_ref_id": str(buy_signal_id),
            "sell_reason": "test_no_record",
            "sell_time": datetime.now(timezone.utc)
        }]

        await process_sell_signal(sell_signal_input_data)

        # AutoTradeManager should not be called
        self.mock_auto_trade_manager_instance.execute_trade.assert_not_called()
        
        # Signal and status updates should still happen
        self.mock_signal_dao_instance.insert_signals.assert_called_once()
        self.mock_signal_dao_instance.update_signal_status.assert_called_once()

    @patch('workflows.monitor_kol_activity.sell_signal_handler.SolanaMonitor')
    async def test_process_sell_signal_auto_trade_manager_exception(self, mock_solana_monitor_cls):
        """测试 AutoTradeManager 抛出异常时的处理"""
        # --- Mock SolanaMonitor ---
        mock_solana_monitor = mock_solana_monitor_cls.return_value
        mock_solana_monitor.get_token_balance = AsyncMock(return_value=1.0)  # 钱包当前余额
        mock_solana_monitor.get_confirmed_token_output_from_tx = AsyncMock(return_value=None)  # 无交易哈希确认
        
        buy_signal_id = PydanticObjectId()
        buy_trade_record_id = PydanticObjectId()
        
        mock_spl_token_addr = "ExceptionTest"
        mock_spl_lamports_bought = 1000000  # 1.0 with 6 decimals

        mock_buy_trade_rec = self._create_mock_buy_trade_record(
            record_id=buy_trade_record_id,
            signal_id=buy_signal_id,
            token_out_address=mock_spl_token_addr,
            token_out_actual_amount_lamports=mock_spl_lamports_bought
        )
        mock_buy_trade_rec.wallet_address = "mock_wallet_addr"  # 添加钱包地址
        
        mock_buy_signal = self._create_mock_buy_signal(
            signal_id=buy_signal_id,
            token_address=mock_spl_token_addr,
            trade_record_ids=[buy_trade_record_id],
            gmgn_enable_auto_sell=True
        )

        # Setup mocks
        self.mock_signal_dao_instance.get_signal.return_value = mock_buy_signal
        self.mock_signal_dao_instance.find_one.return_value = None
        self.mock_trade_record_dao_instance.get_by_id.return_value = mock_buy_trade_rec
        
        # Mock AutoTradeManager config to return enabled=True
        mock_auto_trade_config = MagicMock()
        mock_auto_trade_config.enabled = True
        self.mock_config_manager.get_config.return_value = mock_auto_trade_config
        
        # Mock AutoTradeManager to raise exception
        self.mock_auto_trade_manager_instance.execute_trade.side_effect = Exception("AutoTradeManager test exception")

        sell_signal_input_data = [{
            "token_address": mock_spl_token_addr,
            "buy_signal_ref_id": str(buy_signal_id),
            "sell_reason": "test_exception",
            "sell_time": datetime.now(timezone.utc)
        }]

        await process_sell_signal(sell_signal_input_data)

        # AutoTradeManager should have been called
        self.mock_auto_trade_manager_instance.execute_trade.assert_called_once()
        
        # Signal and status updates should still happen
        self.mock_signal_dao_instance.insert_signals.assert_called_once()
        self.mock_signal_dao_instance.update_signal_status.assert_called_once()
        
        # Notification should be sent (with error template)
        self.mock_telegram_sender_instance.send_message_to_user.assert_called_once()
        
    # === Validation tests (keep existing) ===

    async def test_validate_success(self):
        """测试有效数据的验证逻辑"""
        valid_data = {
            "token_address": "valid_address",
            "buy_signal_ref_id": str(PydanticObjectId()),
            "sell_reason": "timeout",
            "sell_time": datetime.now(timezone.utc)
        }
        result = await validate(valid_data)
        self.assertTrue(result)

    async def test_validate_fail_missing_key(self):
        """测试无效数据（缺少关键字段）的验证逻辑"""
        invalid_data = {
            "token_address": "valid_address",
            # 缺少必需字段
        }
        result = await validate(invalid_data)
        self.assertFalse(result)

    @patch('workflows.monitor_kol_activity.sell_signal_handler.SolanaMonitor')
    async def test_process_sell_signal_with_balance_check_success(self, mock_solana_monitor_cls):
        """测试：使用钱包余额查询的新修复逻辑 - 成功场景"""
        # --- Mock SolanaMonitor ---
        mock_solana_monitor = mock_solana_monitor_cls.return_value
        mock_solana_monitor.get_token_balance = AsyncMock(return_value=150.0)  # 钱包当前余额150
        mock_solana_monitor.get_confirmed_token_output_from_tx = AsyncMock(return_value=100.0)  # 从交易确认的数量100

        # --- 准备 Buy Signal 与 TradeRecord ---
        buy_signal_id = PydanticObjectId()
        trade_record_id = PydanticObjectId()
        token_address = "MockTokenMintAddress"
        wallet_address = "MockWalletAddress"

        # 创建 buy_signal，包含 trade_record_ids
        buy_signal = self._create_mock_buy_signal(
            signal_id=buy_signal_id,
            token_address=token_address,
            trade_record_ids=[trade_record_id],  # 包含交易记录ID
            gmgn_enable_auto_sell=True
        )

        # 创建 TradeRecord 对象
        buy_trade_record = self._create_mock_buy_trade_record(
            record_id=trade_record_id,
            signal_id=buy_signal_id,
            token_out_address=token_address,
            token_out_actual_amount_lamports=100 * (10 ** 6)  # 100 UI 假设6位精度
        )
        buy_trade_record.wallet_address = wallet_address
        buy_trade_record.tx_hash = "MockBuyTxHash"

        # Mock DAO 返回值
        self.mock_signal_dao_instance.get_signal.return_value = buy_signal
        self.mock_signal_dao_instance.find_one.return_value = None
        self.mock_trade_record_dao_instance.get_by_id.return_value = buy_trade_record

        # Mock AutoTradeManager config
        mock_auto_trade_config = MagicMock()
        mock_auto_trade_config.enabled = True
        self.mock_config_manager.get_config.return_value = mock_auto_trade_config

        # AutoTradeManager.execute_trade 结果
        self.mock_auto_trade_manager_instance.execute_trade = AsyncMock(return_value=TradeExecutionResult(
            final_status=InterfaceTradeStatus.SUCCESS,
            channel_attempts=[],
            final_trade_record_id=None,
            successful_channel="jupiter",
            total_execution_time=1.5,
            started_at=datetime.now(timezone.utc),
            completed_at=datetime.now(timezone.utc)
        ))

        # --- 构造 sell_signal 数据并调用 ---
        sell_signal_dict = {
            'token_address': token_address,
            'token_name': 'MOCK',
            'token_symbol': 'MCK',
            'buy_signal_ref_id': str(buy_signal_id),
            'sell_reason': 'ratio',
            'hit_kol_wallets': [],
            'sell_trigger_conditions': {},
            'sell_time': datetime.now(timezone.utc)
        }

        await process_sell_signal([sell_signal_dict])

        # --- 断言 ---
        # 验证 SolanaMonitor 方法被调用
        mock_solana_monitor.get_token_balance.assert_awaited_once_with(
            owner_address=wallet_address,
            token_mint=token_address
        )
        mock_solana_monitor.get_confirmed_token_output_from_tx.assert_awaited_once_with(
            tx_hash="MockBuyTxHash",
            expected_output_token_mint=token_address,
            wallet_address=wallet_address
        )

        # 验证 AutoTradeManager 被调用，使用确认的数量（更保守的选择）
        self.mock_auto_trade_manager_instance.execute_trade.assert_awaited_once()
        args, kwargs = self.mock_auto_trade_manager_instance.execute_trade.call_args
        self.assertAlmostEqual(kwargs.get('amount'), 100.0)  # 使用确认数量而不是当前余额

    @patch('workflows.monitor_kol_activity.sell_signal_handler.SolanaMonitor')
    async def test_process_sell_signal_insufficient_balance_skips(self, mock_solana_monitor_cls):
        """测试：钱包余额不足时跳过交易"""
        # --- Mock SolanaMonitor ---
        mock_solana_monitor = mock_solana_monitor_cls.return_value
        mock_solana_monitor.get_token_balance = AsyncMock(return_value=0.0)  # 钱包余额为0

        # --- 准备 Buy Signal 与 TradeRecord ---
        buy_signal_id = PydanticObjectId()
        trade_record_id = PydanticObjectId()
        token_address = "MockTokenMintAddress"
        wallet_address = "MockWalletAddress"

        buy_signal = self._create_mock_buy_signal(
            signal_id=buy_signal_id,
            token_address=token_address,
            trade_record_ids=[trade_record_id],
            gmgn_enable_auto_sell=True
        )

        buy_trade_record = self._create_mock_buy_trade_record(
            record_id=trade_record_id,
            signal_id=buy_signal_id,
            token_out_address=token_address,
            token_out_actual_amount_lamports=100 * (10 ** 6)
        )
        buy_trade_record.wallet_address = wallet_address

        # Mock DAO 返回值
        self.mock_signal_dao_instance.get_signal.return_value = buy_signal
        self.mock_signal_dao_instance.find_one.return_value = None
        self.mock_trade_record_dao_instance.get_by_id.return_value = buy_trade_record

        # Mock AutoTradeManager config
        mock_auto_trade_config = MagicMock()
        mock_auto_trade_config.enabled = True
        self.mock_config_manager.get_config.return_value = mock_auto_trade_config

        # --- 构造 sell_signal 数据并调用 ---
        sell_signal_dict = {
            'token_address': token_address,
            'token_name': 'MOCK',
            'token_symbol': 'MCK',
            'buy_signal_ref_id': str(buy_signal_id),
            'sell_reason': 'ratio',
            'hit_kol_wallets': [],
            'sell_trigger_conditions': {},
            'sell_time': datetime.now(timezone.utc)
        }

        await process_sell_signal([sell_signal_dict])

        # --- 断言 ---
        # 验证余额查询被调用
        mock_solana_monitor.get_token_balance.assert_awaited_once()
        
        # 验证 AutoTradeManager 不被调用（因为余额不足）
        self.mock_auto_trade_manager_instance.execute_trade.assert_not_called()
        
        # 验证卖出信号被创建
        self.mock_signal_dao_instance.insert_signals.assert_called_once()
        
        # 验证买入信号状态不被更新（因为余额不足，跳过了后续处理）
        self.mock_signal_dao_instance.update_signal_status.assert_not_called()
        
        # 验证发送了余额不足的通知
        self.mock_telegram_sender_instance.send_message_to_user.assert_called_once()

    @patch('workflows.monitor_kol_activity.sell_signal_handler.SolanaMonitor')
    async def test_process_sell_signal_balance_query_error(self, mock_solana_monitor_cls):
        """测试：余额查询失败时的错误处理"""
        # --- Mock SolanaMonitor ---
        mock_solana_monitor = mock_solana_monitor_cls.return_value
        mock_solana_monitor.get_token_balance = AsyncMock(side_effect=Exception("RPC error"))

        # --- 准备 Buy Signal 与 TradeRecord ---
        buy_signal_id = PydanticObjectId()
        trade_record_id = PydanticObjectId()
        token_address = "MockTokenMintAddress"
        wallet_address = "MockWalletAddress"

        buy_signal = self._create_mock_buy_signal(
            signal_id=buy_signal_id,
            token_address=token_address,
            trade_record_ids=[trade_record_id],
            gmgn_enable_auto_sell=True
        )

        buy_trade_record = self._create_mock_buy_trade_record(
            record_id=trade_record_id,
            signal_id=buy_signal_id,
            token_out_address=token_address,
            token_out_actual_amount_lamports=100 * (10 ** 6)
        )
        buy_trade_record.wallet_address = wallet_address

        # Mock DAO 返回值
        self.mock_signal_dao_instance.get_signal.return_value = buy_signal
        self.mock_signal_dao_instance.find_one.return_value = None
        self.mock_trade_record_dao_instance.get_by_id.return_value = buy_trade_record

        # Mock AutoTradeManager config
        mock_auto_trade_config = MagicMock()
        mock_auto_trade_config.enabled = True
        self.mock_config_manager.get_config.return_value = mock_auto_trade_config

        # --- 构造 sell_signal 数据并调用 ---
        sell_signal_dict = {
            'token_address': token_address,
            'token_name': 'MOCK',
            'token_symbol': 'MCK',
            'buy_signal_ref_id': str(buy_signal_id),
            'sell_reason': 'ratio',
            'hit_kol_wallets': [],
            'sell_trigger_conditions': {},
            'sell_time': datetime.now(timezone.utc)
        }

        await process_sell_signal([sell_signal_dict])

        # --- 断言 ---
        # 验证余额查询被调用
        mock_solana_monitor.get_token_balance.assert_awaited_once()
        
        # 验证 AutoTradeManager 不被调用（因为余额查询失败）
        self.mock_auto_trade_manager_instance.execute_trade.assert_not_called()
        
        # 验证卖出信号被创建
        self.mock_signal_dao_instance.insert_signals.assert_called_once()
        
        # 验证买入信号状态不被更新（因为余额查询失败，跳过了后续处理）
        self.mock_signal_dao_instance.update_signal_status.assert_not_called()
        
        # 验证发送了余额查询失败的通知
        self.mock_telegram_sender_instance.send_message_to_user.assert_called_once()

if __name__ == '__main__':
    unittest.main() 