# 整体收益率指标功能单元测试

创建日期：2025-05-30
更新日期：2025-05-30
测试方法：自动化测试
测试级别：单元测试

## 测试概述

本测试文档针对交易统计系统中新增的"整体收益率"指标功能进行全面的单元测试。整体收益率计算公式为：(总盈利金额 / 总投入金额) × 100%，用于反映实际的资金利用效率。

## 测试目标

1. 验证OverallStats模型中新增字段的正确性
2. 验证StatisticsCalculator中整体收益率计算逻辑的准确性
3. 验证边界情况的处理（零投入、全部亏损、全部盈利等）
4. 验证计算精度和数据类型的正确性
5. 确保向后兼容性，不破坏现有功能

## 测试用例

| 用例方法 | 用例概述 | 前置条件 | 输入 | 预期输出 | 实际输出 | 状态 |
| --- | --- | --- | --- | --- | --- | --- |
| `test_normal_mixed_case` | 测试正常混合盈亏情况 | StatisticsCalculator已初始化 | 交易1：买入10 SOL，盈利2 SOL；交易2：买入5 SOL，亏损1 SOL | overall_return_rate = 6.67% | 待执行 | 待测试 |
| `test_zero_investment` | 测试零投入情况 | StatisticsCalculator已初始化 | 空交易列表 | overall_return_rate = 0.0% | 待执行 | 待测试 |
| `test_all_profit_case` | 测试全部盈利情况 | StatisticsCalculator已初始化 | 买入10 SOL，盈利5 SOL | overall_return_rate = 50.0% | 待执行 | 待测试 |
| `test_all_loss_case` | 测试全部亏损情况 | StatisticsCalculator已初始化 | 买入10 SOL，亏损2 SOL | overall_return_rate = -20.0% | 待执行 | 待测试 |
| `test_single_trade_profit` | 测试单笔盈利交易 | StatisticsCalculator已初始化 | 买入8 SOL，盈利1.6 SOL | overall_return_rate = 20.0% | 待执行 | 待测试 |
| `test_single_trade_loss` | 测试单笔亏损交易 | StatisticsCalculator已初始化 | 买入10 SOL，亏损1 SOL | overall_return_rate = -10.0% | 待执行 | 待测试 |
| `test_precision_accuracy` | 测试计算精度 | StatisticsCalculator已初始化 | 买入3 SOL，盈利1 SOL | overall_return_rate = 33.33% (精确到2位小数) | 待执行 | 待测试 |
| `test_large_numbers` | 测试大数值计算 | StatisticsCalculator已初始化 | 买入1000 SOL，盈利123.456 SOL | overall_return_rate = 12.35% | 待执行 | 待测试 |
| `test_small_numbers` | 测试小数值计算 | StatisticsCalculator已初始化 | 买入0.001 SOL，盈利0.0005 SOL | overall_return_rate = 50.0% | 待执行 | 待测试 |
| `test_zero_profit_zero_loss` | 测试盈亏平衡情况 | StatisticsCalculator已初始化 | 买入10 SOL，盈亏为0 | overall_return_rate = 0.0% | 待执行 | 待测试 |
| `test_model_field_exists` | 测试模型字段存在性 | OverallStats模型 | 检查overall_return_rate字段 | 字段存在且类型为float，默认值为0.0 | 待执行 | 待测试 |
| `test_model_field_validation` | 测试模型字段验证 | OverallStats模型 | 设置various值类型 | 接受float值，拒绝无效类型 | 待执行 | 待测试 |

## 测试数据设计

### 测试数据集1：混合盈亏
```python
trade_pairs = [
    TradePair(
        signal_id="test1", strategy_name="test_strategy", token_address="token1",
        buy_amount_sol=10.0, profit_amount=2.0, is_profitable=True, ...
    ),
    TradePair(
        signal_id="test2", strategy_name="test_strategy", token_address="token2",
        buy_amount_sol=5.0, profit_amount=-1.0, is_profitable=False, ...
    )
]
# 预期：total_buy_amount=15.0, total_profit_amount=1.0, overall_return_rate=6.67%
```

### 测试数据集2：边界情况
```python
# 空列表
trade_pairs = []
# 预期：overall_return_rate=0.0%

# 全部亏损
trade_pairs = [TradePair(buy_amount_sol=10.0, profit_amount=-2.0, ...)]
# 预期：overall_return_rate=-20.0%

# 全部盈利
trade_pairs = [TradePair(buy_amount_sol=10.0, profit_amount=5.0, ...)]
# 预期：overall_return_rate=50.0%
```

### 测试数据集3：精度测试
```python
# 小数精度
trade_pairs = [TradePair(buy_amount_sol=3.0, profit_amount=1.0, ...)]
# 预期：overall_return_rate=33.33% (四舍五入到2位小数)

# 大数值
trade_pairs = [TradePair(buy_amount_sol=1000.0, profit_amount=123.456, ...)]
# 预期：overall_return_rate=12.35%
```

## 断言验证

### 数值精度断言
```python
self.assertAlmostEqual(result.overall_return_rate, expected_rate, places=2)
```

### 边界条件断言
```python
self.assertEqual(result.overall_return_rate, 0.0)  # 零投入情况
self.assertLess(result.overall_return_rate, 0.0)   # 亏损情况
self.assertGreater(result.overall_return_rate, 0.0)  # 盈利情况
```

### 数据类型断言
```python
self.assertIsInstance(result.overall_return_rate, float)
```

## 性能要求

- 单个测试用例执行时间 < 100ms
- 所有测试用例总执行时间 < 1秒
- 内存使用量保持在合理范围内

## 错误处理测试

### 异常情况测试
- 输入无效数据类型
- None值处理
- 极端数值（接近浮点数限制）

### 边界值测试
- buy_amount_sol = 0时的除零保护
- 非常小的数值（接近0）
- 非常大的数值

## 集成验证

### 与现有功能兼容性
- 确保现有统计指标计算不受影响
- 验证OverallStats对象的完整性
- 确保JSON序列化正常工作

### 数据一致性
- 验证total_profit_amount与overall_return_rate的一致性
- 确保buy_amount_sol与计算结果的逻辑正确性

## 测试环境

- Python 3.11+
- pytest测试框架
- 需要模拟TradePair数据
- 独立的测试数据库（如果需要）

## 预期测试覆盖率

- 代码行覆盖率：100%
- 分支覆盖率：100%
- 函数覆盖率：100%

## 测试执行计划

1. **第一阶段**：基础功能测试（模型字段、基本计算）
2. **第二阶段**：边界情况测试（零值、极值）
3. **第三阶段**：精度和性能测试
4. **第四阶段**：集成和兼容性测试

## 测试依赖

```python
# 测试依赖模块
from utils.trading.statistics.models import TradePair, OverallStats
from utils.trading.statistics.statistics_calculator import StatisticsCalculator
from datetime import datetime
import unittest
```

## 回归测试

确保新增功能不影响现有测试：
- 运行所有现有的统计相关测试
- 验证API接口的向后兼容性
- 确保HTML报告生成正常 