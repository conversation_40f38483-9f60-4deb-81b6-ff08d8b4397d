import logging
from typing import Dict, List, Any, Optional
from collections import defaultdict
import time

from utils.backtest_event_driven.events import (
    Event, EventType, SignalEvent, OrderEvent, SignalType, FillEvent
)
from utils.backtest_event_driven.event_queue import EventQueue

logger = logging.getLogger("Portfolio")


class Position:
    """持仓类，记录单个代币的持仓信息"""
    
    def __init__(self, token_address: str, token_info: Optional[Dict[str, Any]] = None):
        """初始化持仓
        
        Args:
            token_address: 代币地址
            token_info: 代币信息
        """
        self.token_address = token_address
        self.token_info = token_info or {}
        self.amount = 0  # 持有数量
        self.cost = 0  # 总成本 (USD)
        self.current_price = 0  # 当前价格 (USD)
        self.current_value = 0  # 当前价值 (USD)
        self.unrealized_pnl = 0  # 未实现盈亏 (USD)
        self.realized_pnl = 0  # 已实现盈亏 (USD)
        self.trades = []  # 交易记录
        
    def update_price(self, price: float) -> None:
        """更新当前价格
        
        Args:
            price: 新价格
        """
        self.current_price = price
        self.current_value = self.amount * price
        self.unrealized_pnl = self.current_value - self.cost if self.amount > 0 else 0
        
    def add_trade(self, timestamp: int, is_buy: bool, amount: float, price: float, cost: float) -> None:
        """添加交易记录
        
        Args:
            timestamp: 交易时间戳
            is_buy: 是否为买入
            amount: 交易数量
            price: 交易价格
            cost: 交易成本 (包含手续费)
        """
        self.trades.append({
            'timestamp': timestamp,
            'type': 'BUY' if is_buy else 'SELL',
            'amount': amount,
            'price': price,
            'cost': cost
        })
        
        # 更新持仓信息
        if is_buy:
            # 买入增加持仓
            self.cost += cost
            self.amount += amount
        else:
            # 卖出减少持仓
            # 计算已实现盈亏
            realized_pnl_this_trade = amount * price - (self.cost / self.amount) * amount
            self.realized_pnl += realized_pnl_this_trade
            
            # 更新剩余持仓成本
            remaining_amount = self.amount - amount
            if remaining_amount > 0:
                # 按比例减少成本
                self.cost = (self.cost / self.amount) * remaining_amount
            else:
                # 全部卖出
                self.cost = 0
                
            # 更新持仓数量
            self.amount = max(0, remaining_amount)
            
        # 更新当前价值和未实现盈亏
        self.current_value = self.amount * price
        self.unrealized_pnl = self.current_value - self.cost if self.amount > 0 else 0
        
    def is_empty(self) -> bool:
        """检查持仓是否为空
        
        Returns:
            bool: 持仓是否为空
        """
        return self.amount <= 0
        
    def __str__(self) -> str:
        """返回持仓信息的字符串表示
        
        Returns:
            str: 持仓信息字符串
        """
        symbol = self.token_info.get('symbol', 'UNKNOWN')
        return (f"Position({symbol}, address={self.token_address}, "
                f"amount={self.amount}, cost=${self.cost:.2f}, "
                f"current_price=${self.current_price:.4f}, "
                f"current_value=${self.current_value:.2f}, "
                f"unrealized_pnl=${self.unrealized_pnl:.2f}, "
                f"realized_pnl=${self.realized_pnl:.2f})")


class Portfolio:
    """投资组合类，管理资金和持仓"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """初始化投资组合
        
        Args:
            config: 配置参数
        """
        self.positions = {}  # token_address -> Position
        self.total_invested = 0  # 总投资额
        self.total_value = 0  # 当前总价值
        self.total_realized_pnl = 0  # 总已实现盈亏
        self.total_unrealized_pnl = 0  # 总未实现盈亏
        self.trade_history = []
        self.config = config or {}
        
        # 记录每个代币的最近买入时间戳，用于实现same_token_notification_interval_minutes功能
        self.recent_buy_timestamps = {}  # token_address -> last_buy_timestamp
        
    def add_position(self, token_address: str, token_info: Optional[Dict[str, Any]] = None) -> None:
        """添加新持仓
        
        Args:
            token_address: 代币地址
            token_info: 代币信息
        """
        if token_address not in self.positions:
            self.positions[token_address] = Position(token_address, token_info)
            
    def update_position(self, token_address: str, price: float) -> None:
        """更新持仓价格
        
        Args:
            token_address: 代币地址
            price: 新价格
        """
        if token_address in self.positions:
            self.positions[token_address].update_price(price)
            self._update_portfolio_value()
            
    def _update_portfolio_value(self) -> None:
        """更新投资组合总价值"""
        # 计算所有持仓的价值
        positions_value = sum(pos.current_value for pos in self.positions.values())
        
        # 计算总价值 = 持仓价值（不再包含现金概念）
        self.total_value = positions_value
        
        # 计算总未实现盈亏
        self.total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
        
    def get_position_value(self, token_address: str) -> float:
        """获取持仓价值
        
        Args:
            token_address: 代币地址
            
        Returns:
            float: 持仓价值
        """
        if token_address in self.positions:
            return self.positions[token_address].current_value
        return 0
        
    def can_buy(self, token_address: str, dollar_amount: float) -> bool:
        """检查是否可以买入
        
        Args:
            token_address: 代币地址
            dollar_amount: 买入金额
            
        Returns:
            bool: 是否可以买入
        """
        # 检查是否超过最大持仓品种数
        max_positions = self.config.get('max_positions', 1000)
        if len(self.positions) >= max_positions and token_address not in self.positions:
            return False
            
        # 检查是否在相同代币通知时间间隔内
        same_token_interval_minutes = self.config.get('same_token_notification_interval_minutes', 0)
        if same_token_interval_minutes > 0 and token_address in self.recent_buy_timestamps:
            last_buy_timestamp = self.recent_buy_timestamps[token_address]
            current_time = int(time.time())  # 获取当前时间戳
            # 检查时间间隔
            interval_seconds = same_token_interval_minutes * 60
            if (current_time - last_buy_timestamp) < interval_seconds:
                logger.info(f"代币 {token_address} 在 {same_token_interval_minutes} 分钟内已经买入过，跳过此次买入")
                return False
            
        return True
        
    def execute_buy(self, token_address: str, amount: float, price: float, 
                   timestamp: int, commission: float = 0,
                   token_info: Optional[Dict[str, Any]] = None) -> bool:
        """执行买入操作
        
        Args:
            token_address: 代币地址
            amount: 买入数量
            price: 买入价格
            timestamp: 交易时间戳
            commission: 手续费
            token_info: 代币信息
            
        Returns:
            bool: 买入是否成功
        """
        # 检查是否可以买入
        same_token_interval_minutes = self.config.get('same_token_notification_interval_minutes', 0)
        if same_token_interval_minutes > 0:
            # 检查是否在相同代币通知时间间隔内
            if token_address in self.recent_buy_timestamps:
                last_buy_timestamp = self.recent_buy_timestamps[token_address]
                # 检查时间间隔
                interval_seconds = same_token_interval_minutes * 60
                if (timestamp - last_buy_timestamp) < interval_seconds:
                    logger.info(f"代币 {token_address} 在 {same_token_interval_minutes} 分钟内已经买入过，跳过此次买入")
                    return False
        
        # 更新最近买入时间戳
        self.recent_buy_timestamps[token_address] = timestamp
            
        # 添加持仓或更新现有持仓
        if token_address not in self.positions:
            self.add_position(token_address, token_info)
        
        position = self.positions[token_address]
        
        # 计算买入成本(包含手续费)
        cost = amount * price + commission
        
        # 更新持仓信息
        position.add_trade(timestamp, True, amount, price, cost)
        
        # 更新总投入资金
        self.total_invested += cost
        
        # 更新投资组合价值
        self._update_portfolio_value()
        
        # 记录交易
        trade = {
            'timestamp': timestamp,
            'token_address': token_address,
            'token_info': token_info,
            'type': 'BUY',
            'amount': amount,
            'price': price,
            'cost': cost,
            'commission': commission,
            'total_invested': self.total_invested,
            'total_value': self.total_value
        }
        self.trade_history.append(trade)
        
        logger.info(f"买入成功: {token_address}, 数量: {amount}, 价格: ${price:.4f}, 成本: ${cost:.2f}")
        return True
        
    def can_sell(self, token_address: str) -> bool:
        """检查是否可以卖出
        
        Args:
            token_address: 代币地址
            
        Returns:
            bool: 是否可以卖出
        """
        # 检查是否有持仓
        if token_address not in self.positions:
            return False
            
        # 检查持仓是否为空
        position = self.positions[token_address]
        if position.is_empty():
            return False
            
        return True
        
    def execute_sell(self, token_address: str, price: float, timestamp: int, 
                    commission: float = 0, sell_all: bool = True) -> bool:
        """执行卖出操作
        
        Args:
            token_address: 代币地址
            price: 卖出价格
            timestamp: 交易时间戳
            commission: 手续费
            sell_all: 是否全部卖出
            
        Returns:
            bool: 卖出是否成功
        """
        if not self.can_sell(token_address):
            logger.warning(f"无法卖出 {token_address}，无持仓或持仓为空")
            return False
            
        position = self.positions[token_address]
        
        # 卖出数量，默认全部卖出
        amount_to_sell = position.amount if sell_all else position.amount * 0.5
        
        # 计算卖出收入
        proceeds = amount_to_sell * price - commission
        
        # 更新持仓信息
        position.add_trade(timestamp, False, amount_to_sell, price, commission)
        
        # 更新已实现盈亏
        realized_pnl = position.realized_pnl
        self.total_realized_pnl += realized_pnl
        
        # 更新投资组合价值
        self._update_portfolio_value()
        
        # 记录交易
        trade = {
            'timestamp': timestamp,
            'token_address': token_address,
            'token_info': position.token_info,
            'type': 'SELL',
            'amount': amount_to_sell,
            'price': price,
            'proceeds': proceeds,
            'commission': commission,
            'realized_pnl': realized_pnl,
            'total_invested': self.total_invested,
            'total_value': self.total_value
        }
        self.trade_history.append(trade)
        
        logger.info(f"卖出成功: {token_address}, 数量: {amount_to_sell}, 价格: ${price:.4f}, "
                   f"收入: ${proceeds:.2f}, 盈亏: ${realized_pnl:.2f}")
        
        # 如果全部卖出且持仓为空，删除持仓
        if sell_all and position.is_empty():
            del self.positions[token_address]
            
        return True
        
    def calculate_buy_amount(self, token_address: str, price: float) -> float:
        """计算买入数量
        
        Args:
            token_address: 代币地址
            price: 买入价格
            
        Returns:
            float: 买入数量
        """
        if price <= 0:
            return 0
            
        # 使用固定买入金额10美元
        target_position_value = 10.0  # 固定使用10美元
        
        # 计算买入数量
        amount = target_position_value / price
        
        return amount
        
    def get_portfolio_summary(self) -> Dict[str, Any]:
        """获取投资组合摘要
        
        Returns:
            Dict[str, Any]: 投资组合摘要
        """
        return {
            'total_invested': self.total_invested,
            'total_value': self.total_value,
            'total_return_pct': ((self.total_value / self.total_invested) - 1) * 100 if self.total_invested > 0 else 0,
            'positions_count': len(self.positions),
            'total_realized_pnl': self.total_realized_pnl,
            'total_unrealized_pnl': self.total_unrealized_pnl,
            'trade_count': len(self.trade_history)
        }


class PortfolioManager:
    """投资组合管理器，负责处理信号事件并生成订单事件"""
    
    def __init__(self, event_queue: EventQueue, config: Dict[str, Any] = None):
        """初始化投资组合管理器
        
        Args:
            event_queue: 事件队列
            config: 配置参数
        """
        self.event_queue = event_queue
        self.portfolio = Portfolio(config)
        self.config = config or {}
        
        # 用于跟踪处理过的买入信号，避免重复处理
        # 格式：{token_address: last_buy_signal_timestamp}
        self.processed_buy_signals = {}
        
        # 注册为信号事件的处理器
        event_queue.register_handler(EventType.SIGNAL, self.handle_signal)
        # 注册为成交事件的处理器
        event_queue.register_handler(EventType.FILL, self.handle_fill)
        
    async def handle_signal(self, event: SignalEvent) -> None:
        """处理信号事件，生成订单事件
        
        Args:
            event: 信号事件
        """
        if event.signal_type == SignalType.BUY:
            await self._handle_buy_signal(event)
        elif event.signal_type == SignalType.SELL:
            await self._handle_sell_signal(event)
            
    async def _handle_buy_signal(self, event: SignalEvent) -> None:
        """处理买入信号
        
        Args:
            event: 买入信号事件
        """
        token_address = event.token_address
        token_info = event.token_info
        timestamp = event.timestamp
        
        # 检查是否在相同代币通知时间间隔内
        same_token_interval_minutes = self.config.get('same_token_notification_interval_minutes', 0)
        if same_token_interval_minutes > 0:
            if token_address in self.processed_buy_signals:
                last_signal_timestamp = self.processed_buy_signals[token_address]
                interval_seconds = same_token_interval_minutes * 60
                if (timestamp - last_signal_timestamp) < interval_seconds:
                    logger.info(f"代币 {token_address} 在 {same_token_interval_minutes} 分钟内已经处理过买入信号，跳过此次信号")
                    return
        
        # 检查投资组合是否可以买入该代币
        dollar_amount = 10.0  # 固定使用10美元作为买入金额
        if not self.portfolio.can_buy(token_address, dollar_amount):
            logger.info(f"投资组合无法买入代币 {token_address}，跳过此次买入信号")
            return
            
        # 记录已处理的买入信号
        self.processed_buy_signals[token_address] = timestamp
            
        # 创建订单事件，amount表示美元金额
        order_event = OrderEvent(
            SignalType.BUY,
            timestamp,
            token_address,
            dollar_amount,
            token_info
        )
        
        # 将订单事件推入队列
        self.event_queue.push(order_event)
        logger.info(f"生成买入订单: {order_event}")
        
    async def _handle_sell_signal(self, event: SignalEvent) -> None:
        """处理卖出信号
        
        Args:
            event: 卖出信号事件
        """
        token_address = event.token_address
        
        # 检查是否可以卖出
        if not self.portfolio.can_sell(token_address):
            logger.warning(f"无法卖出 {token_address}，无持仓或持仓为空，跳过卖出信号")
            return
            
        # 获取持仓
        position = self.portfolio.positions[token_address]

        # 获取代币信息，但不包含价格
        token_info = event.token_info
        if isinstance(token_info, dict) and 'price' in token_info:
             # 创建一个新的字典，不包含价格信息，避免修改原始事件数据
            token_info_for_order = token_info.copy()
            del token_info_for_order['price']
        else:
            token_info_for_order = token_info

        # 创建订单事件，卖出全部持仓
        # OrderEvent的amount对于卖出信号是代币数量
        order_event = OrderEvent(
            SignalType.SELL,
            event.timestamp,
            token_address,
            position.amount, # 传递要卖出的代币数量
            token_info_for_order # 传递不含价格的代币信息
        )
        
        # 将订单事件推入队列
        self.event_queue.push(order_event)
        logger.info(f"生成卖出订单: {order_event}")
        
    async def handle_fill(self, event) -> None:
        """处理成交事件
        
        Args:
            event: 成交事件
        """
        if not isinstance(event, FillEvent):
            return
            
        token_address = event.token_address
        token_info = event.token_info
        timestamp = event.timestamp
        price = event.price
        amount = event.amount
        commission = event.commission
        
        if event.signal_type == SignalType.BUY:
            # 处理买入成交
            success = self.portfolio.execute_buy(
                token_address, amount, price, timestamp, commission, token_info
            )
            if success:
                logger.info(f"买入成交: {token_address}, 数量: {amount}, 价格: ${price:.4f}")
        else:
            # 处理卖出成交
            success = self.portfolio.execute_sell(
                token_address, price, timestamp, commission
            )
            if success:
                logger.info(f"卖出成交: {token_address}, 价格: ${price:.4f}") 