"""回测模块V2 - 基于直接数据分析的回测系统

采用单一MongoDB聚合查询 + 向量化计算的简化架构，
提供更精确的信号时间戳和更高的性能。
"""

from utils.backtest_v2.backtest_engine import BacktestEngineV2
from utils.backtest_v2.config_manager import ConfigManagerV2
from utils.backtest_v2.data_query import DataQuery
from utils.backtest_v2.signal_analyzer import SignalAnalyzer
from utils.backtest_v2.sell_strategy import SellStrategy
from utils.backtest_v2.result_analyzer import ResultAnalyzer

__all__ = [
    'BacktestEngineV2',
    'ConfigManagerV2', 
    'DataQuery',
    'SignalAnalyzer',
    'SellStrategy',
    'ResultAnalyzer'
]

__version__ = '0.1.0'
