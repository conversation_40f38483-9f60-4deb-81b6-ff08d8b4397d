name: "GMGN 代币KOL活动记录"
description: "定期获取链上的KOL活动记录，并将数据存储到数据库"

nodes:
  - name: "DevActivitySchedulerNode"
    node_type: "input"
    interval: 10
    generate_data: workflows.gmgn_token_link.handler.generate_data
    flow_control:
      max_pending_messages: 10
      check_interval: 1
      enable_flow_control: true

  - name: "DevActivityMonitorNode"
    node_type: "process"
    depend_ons: ["DevActivitySchedulerNode"]
    concurrency: 1
    interval: 1
    process_item: workflows.gmgn_dev_activity.handler.process_dev_activity

  - name: "DevActivityStoreNode"
    node_type: "storage"
    depend_ons: ["DevActivityMonitorNode"]
    batch_size: 1000  # 每批处理1000条数据
    store_data: workflows.gmgn_dev_activity.handler.store_data
    validate: workflows.gmgn_dev_activity.handler.validate 
