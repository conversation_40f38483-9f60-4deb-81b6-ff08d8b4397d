from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field
from beanie import Document, PydanticObjectId
from models.trade_execution import TradeStatus


class ChannelAttemptRecord(Document):
    """渠道尝试记录 - 存储到数据库的详细记录"""
    
    # 关联信息
    trade_record_id: PydanticObjectId = Field(..., description="关联的交易记录ID")
    signal_id: Optional[PydanticObjectId] = Field(None, description="关联的信号ID")
    
    # 渠道信息
    channel_type: str = Field(..., description="渠道类型")
    channel_config_snapshot: Dict[str, Any] = Field(default_factory=dict, description="使用时的渠道配置快照")
    
    # 尝试信息
    attempt_number: int = Field(..., description="尝试次数")
    status: TradeStatus = Field(..., description="尝试状态")
    
    # 交易参数
    trade_type: str = Field(..., description="交易类型：buy/sell")
    token_in_address: str = Field(..., description="输入代币地址")
    token_out_address: str = Field(..., description="输出代币地址")
    amount_in: float = Field(..., description="输入金额")
    
    # 执行结果
    tx_hash: Optional[str] = Field(None, description="交易哈希")
    actual_amount_in: Optional[float] = Field(None, description="实际输入金额")
    actual_amount_out: Optional[float] = Field(None, description="实际输出金额")
    error_message: Optional[str] = Field(None, description="错误信息")
    provider_response_raw: Optional[Dict[str, Any]] = Field(None, description="渠道原始响应")
    
    # 时间记录
    started_at: datetime = Field(..., description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    execution_time: Optional[float] = Field(None, description="执行时间（秒）")
    
    # 重试信息
    retry_count: int = Field(default=0, description="重试次数")
    retry_delay: float = Field(default=0.0, description="重试延迟（秒）")
    
    # 元数据
    created_at: datetime = Field(default_factory=datetime.now, description="记录创建时间")
    
    class Settings:
        name = "channel_attempt_records"
        indexes = [
            "trade_record_id",
            "signal_id", 
            "channel_type",
            "status",
            "started_at",
            ["trade_record_id", "attempt_number"],
        ]
    
    def calculate_execution_time(self) -> float:
        """计算执行时间"""
        if self.completed_at and self.started_at:
            return (self.completed_at - self.started_at).total_seconds()
        return 0.0
    
    def mark_completed(self, status: TradeStatus, completed_at: Optional[datetime] = None) -> None:
        """标记完成"""
        self.status = status
        self.completed_at = completed_at or datetime.now()
        self.execution_time = self.calculate_execution_time() 