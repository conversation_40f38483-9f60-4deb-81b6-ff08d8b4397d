import logging
from typing import Dict, List, Any, Set, Optional
from collections import defaultdict, deque
import time
from datetime import datetime
import asyncio

from utils.backtest_event_driven.events import (
    TransactionEvent, SignalEvent, SignalType, Event, EventType
)
from utils.backtest_event_driven.event_queue import EventQueue
from utils.strategies.kol_buy_strategy import KOLBuyStrategy
from utils.strategies.kol_sell_strategy import KOLSellStrategy

logger = logging.getLogger("StrategyAdapter")


class BuyStrategyAdapter:
    """买入策略适配器，将KOLBuyStrategy适配到事件驱动框架"""
    
    def __init__(self, config: Dict[str, Any], event_queue: EventQueue):
        """初始化买入策略适配器
        
        Args:
            config: 策略配置
            event_queue: 事件队列
        """
        self.config = config
        self.event_queue = event_queue
        self.strategy = KOLBuyStrategy(config)
        
        # 用于缓存最近的活动数据
        self.recent_activities = deque()
        self.activity_window = config.get('transaction_lookback_hours', 24) * 3600
        # self.processed_tokens = set() # 移除旧的机制
        self.last_signal_time_per_token: Dict[str, int] = {} # 记录每个代币上次发送信号的时间
        self.last_processed_time = 0
        self.wallets = []  # KOL钱包列表
        
        # 配置的阈值
        self.min_amount = config.get('transaction_min_amount', 1000)
        self.min_kol_count = config.get('kol_account_min_count', 3)
        self.kol_account_min_txs = config.get('kol_account_min_txs', 1)
        self.kol_account_max_txs = config.get('kol_account_max_txs', 10)
        self.token_mint_lookback_hours = config.get('token_mint_lookback_hours', 48)
        
        # processing_interval 控制策略检查信号的频率（模拟时间，单位秒）
        # 较小的值能提高模拟精度（更接近实时检查），但显著增加回测时间
        # 较大的值会降低模拟精度（信号检测有延迟），但回测速度更快
        self.processing_interval = int(config.get('processing_interval', 60)) # 默认改为 60 秒
        logger.info(f"BuyStrategyAdapter: 信号检查频率 (processing_interval) 设置为: {self.processing_interval} 秒")
        
        # 读取并转换通知间隔为秒
        notification_interval_minutes = int(config.get('same_token_notification_interval_minutes', 60))
        self.same_token_notification_interval_seconds = notification_interval_minutes * 60
        logger.info(f"BuyStrategyAdapter: 相同代币通知间隔设置为: {notification_interval_minutes} 分钟 ({self.same_token_notification_interval_seconds} 秒)")
        
        # 注册为事件处理器
        event_queue.register_handler(EventType.TRANSACTION, self.handle_transaction)
        
    def set_wallets(self, wallets: List[Dict[str, Any]]) -> None:
        """设置KOL钱包列表
        
        Args:
            wallets: KOL钱包列表
        """
        self.wallets = wallets
        
    async def handle_transaction(self, event: TransactionEvent) -> None:
        """处理交易事件
        
        处理逻辑:
        1. 添加交易到活动窗口
        2. 移除窗口外的交易
        3. 每当时间推进一定间隔，检查是否有满足买入条件的代币
        
        Args:
            event: 交易事件
        """
        current_time = event.timestamp
        
        # 添加新活动
        self.recent_activities.append(event.transaction_data)
        
        # 清理窗口外的活动
        window_start_for_deque = current_time - self.activity_window
        while self.recent_activities and self.recent_activities[0].get('timestamp', 0) < window_start_for_deque:
            self.recent_activities.popleft()
        
        # 如果当前时间比上次处理时间推进了 processing_interval，检查是否有满足条件的代币
        # 这是模拟真实工作流中 InputNode 的 interval 检查频率
        if current_time - self.last_processed_time >= self.processing_interval:
            logger.debug(f"模拟时间 {datetime.fromtimestamp(current_time)} 触发买入信号检查 (上次检查: {datetime.fromtimestamp(self.last_processed_time)})")
            # 传递给 _check_buy_signals 的时间窗口是基于配置的 activity_window (transaction_lookback_hours)
            check_window_start = current_time - self.activity_window
            await self._check_buy_signals(check_window_start, current_time)
            self.last_processed_time = current_time # 更新上次检查时间
            
    async def _check_buy_signals(self, window_start: int, current_time: int) -> None:
        """检查是否有满足买入条件的代币
        
        Args:
            window_start: 检查的时间窗口开始时间戳
            current_time: 检查的时间窗口结束时间戳 (当前模拟时间)
        """
        if not self.recent_activities or not self.wallets:
            return
            
        # 计算时间窗口起点
        # window_start = current_time - self.activity_window # 这个计算移到调用处
        
        # 注意：这里的 window_start 是聚合查询的回溯起点，与 processing_interval 不同
        logger.info(f"[{datetime.fromtimestamp(current_time)}] 检查买入信号，时间窗口: {datetime.fromtimestamp(window_start)} - {datetime.fromtimestamp(current_time)}")
        
        # 调用KOLBuyStrategy的generate_signals方法获取所有潜在信号
        signals_result = await self.strategy.generate_signals(
            window_start, current_time,
            self.min_amount, self.kol_account_min_txs, self.kol_account_max_txs,
            self.min_kol_count, self.token_mint_lookback_hours
        )

        if not signals_result:
            logger.info(f"[{datetime.fromtimestamp(current_time)}] 没有生成买入信号")
            return

        # --- 修改：先预处理所有信号，确保每个代币只处理一次 --- #
        signals_to_process = {}  # 保存最终要处理的信号，每个代币只保留一个
        signals_suppressed_count = 0
        original_signal_count = len(signals_result)
        
        # 第一步：过滤掉不符合时间间隔的代币
        for token_address, signal_data in signals_result.items():
            last_signal_time = self.last_signal_time_per_token.get(token_address, 0)
            time_since_last_signal = current_time - last_signal_time

            # 检查是否满足时间间隔要求
            if time_since_last_signal >= self.same_token_notification_interval_seconds:
                # 该代币通过了时间间隔检查，可以生成信号
                signals_to_process[token_address] = signal_data
            else:
                # 因时间间隔限制而抑制信号
                signals_suppressed_count += 1
                logger.debug(f"[{datetime.fromtimestamp(current_time)}] 抑制买入信号 for {token_address} (间隔 {time_since_last_signal}s < {self.same_token_notification_interval_seconds}s)")
        
        # 第二步：生成信号并更新时间戳
        signals_generated_count = 0
        for token_address, signal_data in signals_to_process.items():
            try:
                token_info = signal_data.get('token_info', {})
                if 'kol_wallets' not in token_info and 'kol_wallets' in signal_data:
                    token_info['kol_wallets'] = signal_data['kol_wallets']

                # 历史阈值时间戳（保存为参考信息）
                original_timestamp = signal_data.get('threshold_timestamp', 0)
                token_info['original_signal_timestamp'] = original_timestamp
                token_info['threshold_timestamp'] = original_timestamp
                
                # 正确的设计：使用当前检测时间作为买入信号时间戳
                # 因为我们是在当前时间发现机会并执行买入的
                signal_timestamp = current_time
                token_info['buy_signal_timestamp'] = signal_timestamp

                signal_event = SignalEvent(
                    SignalType.BUY,
                    signal_timestamp,  # 使用当前检测时间
                    token_address,
                    token_info
                )
                self.event_queue.push(signal_event)

                # 更新该代币的上次信号时间戳
                self.last_signal_time_per_token[token_address] = current_time
                signals_generated_count += 1
                logger.debug(f"[{datetime.fromtimestamp(current_time)}] 生成买入信号: {signal_event}")
            except Exception as e:
                logger.error(f"处理买入信号 {token_address} 时出错: {e}", exc_info=True)
                signals_suppressed_count += 1 # 算作抑制

        if signals_generated_count > 0 or signals_suppressed_count > 0:
             logger.info(f"[{datetime.fromtimestamp(current_time)}] 买入信号检查完成。原始潜在信号: {original_signal_count}, "
                        f"成功生成: {signals_generated_count}, 被抑制/失败: {signals_suppressed_count}")


class SellStrategyAdapter:
    """卖出策略适配器，将KOLSellStrategy适配到事件驱动框架"""
    
    def __init__(self, config: Dict[str, Any], event_queue: EventQueue):
        """初始化卖出策略适配器
        
        Args:
            config: 策略配置
            event_queue: 事件队列
        """
        self.config = config
        self.event_queue = event_queue
        
        # 初始化KOL卖出策略
        self.strategy = KOLSellStrategy(config)
        
        # 设置策略参数
        self.sell_kol_ratio = float(config.get("sell_kol_ratio", 0.5))
        self.activity_window = int(config.get("sell_strategy_hours", 24)) * 3600  # 转换为秒
        self.last_processed_time = 0  # 上次处理的时间戳
        
        # processing_interval 控制策略检查信号的频率（模拟时间，单位秒）
        self.processing_interval = int(config.get('processing_interval', 60)) # 默认改为 60 秒
        logger.info(f"SellStrategyAdapter: 信号检查频率 (processing_interval) 设置为: {self.processing_interval} 秒")
        
        # 持仓管理
        self.holdings = {}  # 代币地址 -> 持仓信息
        
        # 活动缓冲
        self.recent_activities = deque()
        self.wallets = []
        
        # 注册为事件处理器
        event_queue.register_handler(EventType.TRANSACTION, self.handle_transaction)
        event_queue.register_handler(EventType.FILL, self.handle_fill)
        
    def set_wallets(self, wallets: List[Dict[str, Any]]) -> None:
        """设置KOL钱包列表
        
        Args:
            wallets: KOL钱包列表
        """
        self.wallets = wallets
        
    async def handle_transaction(self, event: TransactionEvent) -> None:
        """处理交易事件
        
        Args:
            event: 交易事件
        """
        current_time = event.timestamp
        
        # 添加新活动 (所有活动都需要添加，用于后续可能的检查)
        self.recent_activities.append(event.transaction_data)
        
        # 清理窗口外的活动 (基于 sell_strategy_hours 定义的窗口)
        window_start_for_deque = current_time - self.activity_window
        while self.recent_activities and self.recent_activities[0].get('timestamp', 0) < window_start_for_deque:
            self.recent_activities.popleft()
        
        # 如果当前时间比上次处理时间推进了 processing_interval，检查是否有满足卖出条件的代币
        if current_time - self.last_processed_time >= self.processing_interval:
            logger.debug(f"模拟时间 {datetime.fromtimestamp(current_time)} 触发卖出信号检查 (上次检查: {datetime.fromtimestamp(self.last_processed_time)})")
            # 传递给 _check_sell_signals 的时间窗口是基于配置的 activity_window (sell_strategy_hours)
            check_window_start = current_time - self.activity_window
            await self._check_sell_signals(check_window_start, current_time)
            self.last_processed_time = current_time # 更新上次检查时间
            
    async def handle_fill(self, event: Event) -> None:
        """处理成交事件
        
        Args:
            event: 成交事件
        """
        if event.event_type != EventType.FILL:
            return
            
        # 如果是买入成交，将代币添加到持仓中
        if event.signal_type == SignalType.BUY:
            token_address = event.token_address
            token_info = event.token_info
            
            # 确保买入信号时间戳存在
            buy_signal_timestamp = token_info.get('buy_signal_timestamp', event.timestamp)
            
            # 更新持仓信息
            self.holdings[token_address] = {
                'token_info': token_info,
                'bought_at': event.timestamp,
                'buy_signal_timestamp': buy_signal_timestamp,
                'amount': event.amount,
                'price': event.price
            }
            
            logger.info(f"添加持仓: {token_address}, 买入时间: {event.timestamp}, 买入信号时间: {buy_signal_timestamp}")
        
        # 如果是卖出成交，从持仓中移除代币
        elif event.signal_type == SignalType.SELL:
            token_address = event.token_address
            
            # 从持仓中移除代币
            if token_address in self.holdings:
                self.holdings.pop(token_address)
                logger.info(f"移除持仓: {token_address}")
                
    async def _check_sell_signals(self, window_start: int, current_time: int) -> None:
        """检查当前持有的代币是否满足卖出条件

        Args:
            window_start: 检查时间窗口开始 (可能不再直接需要，因为should_sell自己查库)
            current_time: 当前模拟时间戳
        """
        if not self.holdings:
            return

        logger.debug(f"[{datetime.fromtimestamp(current_time)}] 检查 {len(self.holdings)} 个持仓的卖出信号")

        # 创建任务列表以并发检查卖出信号
        tasks = []
        for token_address, holding_info in list(self.holdings.items()): # 使用 list() 避免在迭代时修改字典
            # 注意：传递给 should_sell 的是持仓信息字典
            tasks.append(asyncio.create_task(
                self._process_single_sell_check(token_address, holding_info, current_time)
            ))

        # 并发执行所有检查任务
        await asyncio.gather(*tasks)

    async def _process_single_sell_check(self, token_address: str, holding_info: Dict[str, Any], current_time: int):
        """处理单个代币的卖出检查和信号生成"""
        try:
            # 调用 KOLSellStrategy 的 should_sell 方法
            # 不再需要传递 token_activities, window_start, wallets
            should_sell, reason, sell_timestamp = await self.strategy.should_sell(
                token_address,
                holding_info, # 传递整个持仓信息字典
                current_time
            )

            if should_sell:
                logger.info(f"[{datetime.fromtimestamp(current_time)}] 代币 {token_address} 满足卖出条件: {reason}，计划在 {datetime.fromtimestamp(sell_timestamp)} 卖出")
                # 创建卖出信号事件
                signal_event = SignalEvent(
                    SignalType.SELL,
                    sell_timestamp,  # 使用策略返回的时间戳
                    token_address,
                    holding_info # 可以传递持仓信息给下游
                )
                # 推入事件队列
                self.event_queue.push(signal_event)

                # 注意：这里不需要立即从 self.holdings 移除，
                # 移除操作应该在 PortfolioManager 收到 FillEvent 后同步
                # （通过 EventDrivenBacktest.setup 中注册的 sync_holdings 回调）
            # else:
                 # logger.debug(f"[{datetime.fromtimestamp(current_time)}] 代币 {token_address} 未满足卖出条件: {reason}")

        except Exception as e:
            logger.error(f"检查代币 {token_address} 卖出信号时出错: {e}", exc_info=True) 