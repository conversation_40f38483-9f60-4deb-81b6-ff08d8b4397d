"""
ChannelSelector单元测试

测试ChannelSelector的核心功能：
1. 渠道选择和过滤
2. 优先级排序
3. 健康检查集成
4. 约束条件检查
"""

import unittest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any, List, Optional
from datetime import datetime

from models.config import TradeChannelConfig, TradingParams
from utils.trading.channel_selector import ChannelSelector
from utils.trading.channel_registry import ChannelRegistry
from utils.trading.solana.trade_interface import TradeInterface


class MockTradeInterface(TradeInterface):
    """用于测试的Mock交易接口"""
    
    def __init__(self, channel_type: str, is_healthy: bool = True, is_available: bool = True):
        self.channel_type = channel_type
        self.is_healthy = is_healthy
        self.is_available_status = is_available
        self.health_check_count = 0
    
    async def execute_trade(self, *args, **kwargs):
        """Mock交易执行"""
        pass
    
    async def is_available(self) -> bool:
        """Mock可用性检查"""
        self.health_check_count += 1
        return self.is_healthy and self.is_available_status
    
    async def close(self) -> None:
        """Mock关闭方法"""
        pass
    
    def is_slippage_related_error(self, error_message: str = None, provider_response: Dict[str, Any] = None) -> bool:
        """Mock滑点错误识别"""
        return "slippage" in (error_message or "").lower()
    
    def is_non_retryable_error(self, error_message: str = None, provider_response: Dict[str, Any] = None) -> bool:
        """Mock不可重试错误识别"""
        return "insufficient funds" in (error_message or "").lower()


class TestChannelSelector(unittest.TestCase):
    """ChannelSelector单元测试类"""
    
    def setUp(self) -> None:
        """测试前置设置"""
        self.mock_registry = Mock(spec=ChannelRegistry)
        self.selector = ChannelSelector(self.mock_registry)
        
        # 创建测试用的渠道配置
        self.gmgn_config = self._create_channel_config(
            channel_type="gmgn",
            enabled=True,
            priority=1,
            timeout_seconds=60,
            max_retries=3,
            channel_params={
                "gmgn_api_host": "https://gmgn.ai",
                "min_amount": 0.001,
                "max_amount": 10.0
            }
        )
        
        self.solana_config = self._create_channel_config(
            channel_type="jupiter",
            enabled=True,
            priority=2,
            timeout_seconds=90,
            max_retries=2,
            channel_params={
                "jupiter_api_host": "https://quote-api.jup.ag",
                "solana_rpc_url": "https://api.mainnet-beta.solana.com",
                "min_amount": 0.001,
                "max_amount": 5.0
            }
        )
        
        self.disabled_config = self._create_channel_config(
            channel_type="disabled_channel",
            enabled=False,
            priority=3,
            timeout_seconds=60,
            max_retries=2
        )
        
        # 创建测试用的Mock渠道实例
        self.mock_gmgn_channel = MockTradeInterface("gmgn", is_healthy=True)
        self.mock_solana_channel = MockTradeInterface("jupiter", is_healthy=True)
        self.mock_unhealthy_channel = MockTradeInterface("unhealthy", is_healthy=False)
        self.mock_unavailable_channel = MockTradeInterface("unavailable", is_healthy=True, is_available=False)
    
    def _create_channel_config(
        self,
        channel_type: str,
        enabled: bool,
        priority: int,
        timeout_seconds: int,
        max_retries: int,
        channel_params: Optional[Dict[str, Any]] = None
    ) -> TradeChannelConfig:
        """创建渠道配置"""
        trading_params = TradingParams(
            default_buy_amount_sol=0.01,
            default_buy_slippage_percentage=10.0,
            default_buy_priority_fee_sol=0.0005,
            default_sell_slippage_percentage=10.0,
            default_sell_priority_fee_sol=0.0005
        )
        
        return TradeChannelConfig(
            channel_type=channel_type,
            enabled=enabled,
            priority=priority,
            timeout_seconds=timeout_seconds,
            max_retries=max_retries,
            trading_params=trading_params,
            channel_params=channel_params or {}
        )
    
    async def test_select_channels_basic(self) -> None:
        """测试基本渠道选择"""
        # 设置可用渠道配置
        available_configs = [self.gmgn_config, self.solana_config]
        
        # 设置Mock返回值
        self.mock_registry.get_channel.side_effect = lambda name: {
            "gmgn": self.mock_gmgn_channel,
            "jupiter": self.mock_solana_channel
        }.get(name)
        
        # 执行渠道选择
        selected_channels = await self.selector.select_channels(
            available_configs=available_configs,
            trade_type="buy",
            amount=0.01
        )
        
        # 验证选择结果
        self.assertEqual(len(selected_channels), 2)
        
        # 验证优先级排序（priority越小优先级越高）
        self.assertEqual(selected_channels[0][0], self.gmgn_config)  # priority=1
        self.assertEqual(selected_channels[1][0], self.solana_config)  # priority=2
        
        # 验证健康检查被调用
        self.assertEqual(self.mock_gmgn_channel.health_check_count, 1)
        self.assertEqual(self.mock_solana_channel.health_check_count, 1)
    
    async def test_select_channels_filter_disabled(self) -> None:
        """测试过滤禁用的渠道"""
        # 包含禁用渠道的配置
        available_configs = [self.gmgn_config, self.disabled_config]
        
        # 设置Mock返回值
        self.mock_registry.get_channel.side_effect = lambda name: {
            "gmgn": self.mock_gmgn_channel
        }.get(name)
        
        # 执行渠道选择
        selected_channels = await self.selector.select_channels(
            available_configs=available_configs,
            trade_type="buy",
            amount=0.01
        )
        
        # 验证禁用渠道被过滤
        self.assertEqual(len(selected_channels), 1)
        self.assertEqual(selected_channels[0][0].channel_type, "gmgn")
    
    async def test_select_channels_filter_unhealthy(self) -> None:
        """测试过滤不健康的渠道"""
        # 创建不健康渠道的配置
        unhealthy_config = self._create_channel_config(
            channel_type="unhealthy",
            enabled=True,
            priority=1,
            timeout_seconds=60,
            max_retries=3
        )
        
        available_configs = [unhealthy_config, self.gmgn_config]
        
        # 设置Mock返回值
        self.mock_registry.get_channel.side_effect = lambda name: {
            "unhealthy": self.mock_unhealthy_channel,
            "gmgn": self.mock_gmgn_channel
        }.get(name)
        
        # 执行渠道选择
        selected_channels = await self.selector.select_channels(
            available_configs=available_configs,
            trade_type="buy",
            amount=0.01
        )
        
        # 验证不健康渠道被过滤
        self.assertEqual(len(selected_channels), 1)
        self.assertEqual(selected_channels[0][0].channel_type, "gmgn")
    
    async def test_select_channels_filter_unavailable(self) -> None:
        """测试过滤不可用的渠道"""
        # 创建不可用渠道的配置
        unavailable_config = self._create_channel_config(
            channel_type="unavailable",
            enabled=True,
            priority=1,
            timeout_seconds=60,
            max_retries=3
        )
        
        available_configs = [unavailable_config, self.gmgn_config]
        
        # 设置Mock返回值
        self.mock_registry.get_channel.side_effect = lambda name: {
            "unavailable": self.mock_unavailable_channel,
            "gmgn": self.mock_gmgn_channel
        }.get(name)
        
        # 执行渠道选择
        selected_channels = await self.selector.select_channels(
            available_configs=available_configs,
            trade_type="buy",
            amount=0.01
        )
        
        # 验证不可用渠道被过滤
        self.assertEqual(len(selected_channels), 1)
        self.assertEqual(selected_channels[0][0].channel_type, "gmgn")
    
    async def test_select_channels_amount_constraints(self) -> None:
        """测试金额约束过滤"""
        # 创建有严格金额限制的配置
        strict_config = self._create_channel_config(
            channel_type="strict",
            enabled=True,
            priority=1,
            timeout_seconds=60,
            max_retries=3,
            channel_params={
                "min_amount": 0.1,  # 最小金额0.1 SOL
                "max_amount": 1.0   # 最大金额1.0 SOL
            }
        )
        
        mock_strict_channel = MockTradeInterface("strict", is_healthy=True)
        
        available_configs = [strict_config, self.gmgn_config]
        
        # 设置Mock返回值
        self.mock_registry.get_channel.side_effect = lambda name: {
            "strict": mock_strict_channel,
            "gmgn": self.mock_gmgn_channel
        }.get(name)
        
        # 测试金额过小的情况
        selected_channels = await self.selector.select_channels(
            available_configs=available_configs,
            trade_type="buy",
            amount=0.01  # 小于strict渠道的最小金额
        )
        
        # 验证strict渠道被过滤，只剩gmgn
        channel_types = [config.channel_type for config, _ in selected_channels]
        self.assertIn("gmgn", channel_types)
        self.assertNotIn("strict", channel_types)
        
        # 测试金额在范围内的情况
        selected_channels = await self.selector.select_channels(
            available_configs=available_configs,
            trade_type="buy",
            amount=0.5  # 在strict渠道的范围内
        )
        
        # 验证两个渠道都可用
        self.assertEqual(len(selected_channels), 2)
    
    async def test_select_channels_priority_sorting(self) -> None:
        """测试优先级排序"""
        # 创建不同优先级的配置
        high_priority = self._create_channel_config("high", True, 1, 60, 3)
        medium_priority = self._create_channel_config("medium", True, 5, 60, 3)
        low_priority = self._create_channel_config("low", True, 10, 60, 3)
        
        # 故意以非排序顺序提供配置
        available_configs = [medium_priority, low_priority, high_priority]
        
        # 创建Mock渠道
        mock_channels = {
            "high": MockTradeInterface("high", is_healthy=True),
            "medium": MockTradeInterface("medium", is_healthy=True),
            "low": MockTradeInterface("low", is_healthy=True)
        }
        
        self.mock_registry.get_channel.side_effect = lambda name: mock_channels.get(name)
        
        # 执行渠道选择
        selected_channels = await self.selector.select_channels(
            available_configs=available_configs,
            trade_type="buy",
            amount=0.01
        )
        
        # 验证按优先级排序（数字越小优先级越高）
        self.assertEqual(len(selected_channels), 3)
        self.assertEqual(selected_channels[0][0].channel_type, "high")    # priority=1
        self.assertEqual(selected_channels[1][0].channel_type, "medium")  # priority=5
        self.assertEqual(selected_channels[2][0].channel_type, "low")     # priority=10
    
    async def test_select_channels_exclude_list(self) -> None:
        """测试排除列表功能"""
        available_configs = [self.gmgn_config, self.solana_config]
        
        # 设置Mock返回值
        self.mock_registry.get_channel.side_effect = lambda name: {
            "gmgn": self.mock_gmgn_channel,
            "jupiter": self.mock_solana_channel
        }.get(name)
        
        # 排除gmgn渠道
        selected_channels = await self.selector.select_channels(
            available_configs=available_configs,
            trade_type="buy",
            amount=0.01,
            exclude_channels=["gmgn"]
        )
        
        # 验证gmgn被排除
        self.assertEqual(len(selected_channels), 1)
        self.assertEqual(selected_channels[0][0].channel_type, "jupiter")
    
    async def test_select_channels_empty_result(self) -> None:
        """测试无可用渠道的情况"""
        # 所有渠道都被禁用或不健康
        available_configs = [self.disabled_config]
        
        # 执行渠道选择
        selected_channels = await self.selector.select_channels(
            available_configs=available_configs,
            trade_type="buy",
            amount=0.01
        )
        
        # 验证返回空列表
        self.assertEqual(len(selected_channels), 0)
    
    async def test_select_channels_health_check_exception(self) -> None:
        """测试健康检查异常处理"""
        # 创建会抛出异常的Mock渠道
        mock_error_channel = Mock(spec=TradeInterface)
        mock_error_channel.is_available = AsyncMock(side_effect=Exception("Health check error"))
        
        error_config = self._create_channel_config("error", True, 1, 60, 3)
        available_configs = [error_config, self.gmgn_config]
        
        # 设置Mock返回值
        self.mock_registry.get_channel.side_effect = lambda name: {
            "error": mock_error_channel,
            "gmgn": self.mock_gmgn_channel
        }.get(name)
        
        # 执行渠道选择
        selected_channels = await self.selector.select_channels(
            available_configs=available_configs,
            trade_type="buy",
            amount=0.01
        )
        
        # 验证异常渠道被过滤，只保留健康渠道
        self.assertEqual(len(selected_channels), 1)
        self.assertEqual(selected_channels[0][0].channel_type, "gmgn")
    
    @patch('utils.trading.channel_selector.time.time')
    async def test_health_check_caching(self, mock_time: Mock) -> None:
        """测试健康检查缓存机制"""
        # 设置固定时间
        mock_time.return_value = 1000.0
        
        available_configs = [self.gmgn_config]
        
        self.mock_registry.get_channel.side_effect = lambda name: {
            "gmgn": self.mock_gmgn_channel
        }.get(name)
        
        # 第一次选择
        await self.selector.select_channels(
            available_configs=available_configs,
            trade_type="buy",
            amount=0.01
        )
        
        first_check_count = self.mock_gmgn_channel.health_check_count
        
        # 第二次选择（在缓存期内）
        await self.selector.select_channels(
            available_configs=available_configs,
            trade_type="buy",
            amount=0.01
        )
        
        # 验证缓存效果（根据实现可能会有不同的行为）
        second_check_count = self.mock_gmgn_channel.health_check_count
        
        # 如果有缓存，第二次不应该再次检查
        # 如果没有缓存，每次都会检查
        self.assertGreaterEqual(second_check_count, first_check_count)
    
    def test_check_amount_constraints_valid(self) -> None:
        """测试有效金额约束检查"""
        # 测试在约束范围内的金额
        result = self.selector._check_amount_constraints(
            channel_config=self.gmgn_config,
            amount=0.01
        )
        
        self.assertTrue(result)
    
    def test_check_amount_constraints_too_small(self) -> None:
        """测试金额过小的约束检查"""
        # 创建有最小金额限制的配置
        config = self._create_channel_config(
            channel_type="test",
            enabled=True,
            priority=1,
            timeout_seconds=60,
            max_retries=3,
            channel_params={"min_amount": 0.1}
        )
        
        # 测试小于最小金额的情况
        result = self.selector._check_amount_constraints(
            channel_config=config,
            amount=0.01
        )
        
        self.assertFalse(result)
    
    def test_check_amount_constraints_too_large(self) -> None:
        """测试金额过大的约束检查"""
        # 创建有最大金额限制的配置
        config = self._create_channel_config(
            channel_type="test",
            enabled=True,
            priority=1,
            timeout_seconds=60,
            max_retries=3,
            channel_params={"max_amount": 1.0}
        )
        
        # 测试大于最大金额的情况
        result = self.selector._check_amount_constraints(
            channel_config=config,
            amount=2.0
        )
        
        self.assertFalse(result)


if __name__ == '__main__':
    unittest.main() 