### 获取信号记录列表 (分页)

获取 `Signal` 记录的分页列表，可按信号类型和时间范围筛选。

**注意**: 此API会自动过滤掉测试策略信号，以确保返回的都是生产环境的有效信号。具体过滤规则如下：
- 过滤掉 `trigger_conditions.strategy_name` 为 "测试" 的记录
- 过滤掉 `trigger_conditions.original_buy_strategy_name` 为 "测试" 的记录  
- 保留 `trigger_conditions` 字段不存在的记录
- 保留相关策略字段不存在或为其他值的记录
- 过滤条件区分大小写（只过滤完全匹配 "测试" 的记录）

*   **路径**: `/api/v1/signals`
*   **方法**: `GET`
*   **查询参数**:
    *   `skip` (integer, optional, default: 0): 跳过的记录数 (>= 0)。
    *   `limit` (integer, optional, default: 10): 每页返回的最大记录数 (1-100)。
    *   `signal_type` (string, optional): 按信号类型精确匹配过滤 (例如 'kol_buy', 'kol_sell')。
    *   `period` (string, optional, enum: 'today', 'past'): 筛选时间范围。
    *   `tz` (string, optional, default: 'Asia/Shanghai'): 用于计算 `period` 的时区 (IANA 时区名称)。
*   **示例请求 (curl)**:
    *   获取所有信号:
        ```bash
        curl -X GET "http://localhost:8000/api/v1/signals?skip=0&limit=20" -H "accept: application/json"
        ```
    *   获取今天的 `kol_buy` 信号 (使用默认上海时区):
        ```bash
        curl -X GET "http://localhost:8000/api/v1/signals?signal_type=kol_buy&period=today" -H "accept: application/json"
        ```
    *   获取过去所有的 `kol_sell` 信号 (使用UTC时区):
        ```bash
        curl -X GET "http://localhost:8000/api/v1/signals?signal_type=kol_sell&period=past&tz=UTC" -H "accept: application/json"
        ```
*   **响应格式**: `SignalListApiResponse` (包装在标准结构中)
    *   `code` (integer): 状态码，0 表示成功，非 0 表示失败 (例如 5001 代表数据库错误, 4001 代表无效时区)。
    *   `msg` (string): 响应消息。
    *   `data` (array | null): 成功时为 `SignalResponse` 对象数组，失败时为 `null`。

*   **成功响应示例**: (HTTP Status Code: 200)
    *   **内容**: `application/json`
    *   **Body**:
        ```json
        {
          "code": 0,
          "msg": "success",
          "data": [
            {
              "id": "66acdf1b8c8c4a2a9c0e1a2b",
              "token_address": "So11111111111111111111111111111111111111112",
              "token_name": "Wrapped SOL",
              "token_symbol": "SOL",
              "signal_type": "kol_buy",
              "trigger_timestamp": "2024-08-02T10:00:00+00:00",
              "hit_kol_wallets": ["WalletA_Address", "WalletB_Address"],
              "created_at": "2024-08-02T10:00:05+00:00",
              "updated_at": null
            }
            // ... more items
          ]
        }
        ```
*   **错误响应示例 (无效时区)**: (HTTP Status Code: 200)
    *   **内容**: `application/json`
    *   **Body**:
        ```json
        {
          "code": 4001,
          "msg": "无效的时区名称: 'Invalid/Timezone'",
          "data": null
        }
        ```
*   **错误响应示例 (数据库错误)**: (HTTP Status Code: 200)
    *   **内容**: `application/json`
    *   **Body**:
        ```json
        {
          "code": 5001,
          "msg": "获取信号数据时出错: [具体错误信息]",
          "data": null
        }
        ```
*   **错误响应 (参数验证错误)**: (HTTP Status Code: 422)
    *   FastAPI 默认的参数验证错误响应。
        ```json
        {
          "detail": [
            { "loc": ["query", "limit"], "msg": "Input should be less than or equal to 100", "type": "less_than_equal" }
          ]
        }
        ```

**`SignalResponse` 模型字段 (data 数组中的对象结构)**:

| 字段                 | 类型                  | 描述                         | 示例                                     |
| :------------------- | :-------------------- | :--------------------------- | :--------------------------------------- |
| `id`                 | string                | 信号的唯一ID (MongoDB ObjectId) | `"66acdf1b8c8c4a2a9c0e1a2b"`           |
| `token_address`      | string                | 代币地址                     | `"So1111...11112"`                     |
| `token_name`         | string \| null          | 代币名称                     | `"Wrapped SOL"`                          |
| `token_symbol`       | string \| null          | 代币符号                     | `"SOL"`                                |
| `signal_type`        | string                | 信号类型 (例如 'kol_buy')    | `"kol_buy"`                            |
| `trigger_timestamp`  | string                | 信号触发时间 (ISO 8601)      | `"2024-08-02T10:00:00+00:00"`          |
| `hit_kol_wallets`    | array[string] \| null | 命中该信号的KOL钱包地址列表  | `["WalletA_Address", ...]`             |
| `created_at`         | string                | 记录创建时间 (ISO 8601 格式) | `"2024-08-02T10:00:05+00:00"`          |
| `updated_at`         | string \| null          | 记录更新时间 (ISO 8601 格式) | `"2024-08-02T11:30:00+00:00"` / `null` |

*注意*: 原始模型中的 `chat_id` 等敏感字段已从API响应中移除。

## 测试策略过滤逻辑详解

### 过滤规则

此API实现了双重过滤机制，同时过滤两个策略字段：

1. **strategy_name 过滤**: 过滤掉 `trigger_conditions.strategy_name` 为 "测试" 的记录
2. **original_buy_strategy_name 过滤**: 过滤掉 `trigger_conditions.original_buy_strategy_name` 为 "测试" 的记录

### MongoDB 查询逻辑

实际的查询条件使用 `$and` 操作符组合两个 `$or` 条件：

```javascript
{
  "$and": [
    {
      "$or": [
        {"trigger_conditions.strategy_name": {"$ne": "测试"}},
        {"trigger_conditions.strategy_name": {"$exists": false}},
        {"trigger_conditions": {"$exists": false}}
      ]
    },
    {
      "$or": [
        {"trigger_conditions.original_buy_strategy_name": {"$ne": "测试"}},
        {"trigger_conditions.original_buy_strategy_name": {"$exists": false}},
        {"trigger_conditions": {"$exists": false}}
      ]
    }
  ]
}
```

### 过滤行为示例

| trigger_conditions 内容 | 是否被过滤 | 说明 |
|:----------------------|:----------|:-----|
| `{"strategy_name": "测试"}` | ✅ 被过滤 | strategy_name 为 "测试" |
| `{"original_buy_strategy_name": "测试"}` | ✅ 被过滤 | original_buy_strategy_name 为 "测试" |
| `{"strategy_name": "测试", "original_buy_strategy_name": "测试"}` | ✅ 被过滤 | 两个字段都为 "测试" |
| `{"strategy_name": "正常策略", "original_buy_strategy_name": "正常策略"}` | ❌ 保留 | 两个字段都不为 "测试" |
| `{"strategy_name": "Test"}` | ❌ 保留 | 大小写不同 |
| `{"strategy_name": "测试1"}` | ❌ 保留 | 不完全匹配 |
| `{"other_field": "value"}` | ❌ 保留 | 不包含策略字段 |
| `{}` | ❌ 保留 | trigger_conditions 为空对象 |
| 不存在 trigger_conditions | ❌ 保留 | 字段不存在 |
| `{"strategy_name": null}` | ❌ 保留 | 字段值为 null |
| `{"strategy_name": ""}` | ❌ 保留 | 字段值为空字符串 |

### 业务场景

- **买入信号**: 通常只有 `strategy_name` 字段，用于标识触发买入的策略
- **卖出信号**: 可能同时包含 `strategy_name`（卖出策略）和 `original_buy_strategy_name`（原始买入策略）
- **测试环境**: 测试策略产生的信号会被自动过滤，确保生产环境数据的纯净性

### 性能考虑

- 过滤条件已优化，对查询性能影响微乎其微（<2%）
- 支持字段不存在的情况，保证向后兼容性
- 使用索引友好的查询结构
