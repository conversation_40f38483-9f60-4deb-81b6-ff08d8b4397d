# Bug修复方案：TradeRecord 模型索引定义错误导致的 IndexError

## 1. Bug 标识
- **Bug描述**: 在运行单元测试时（尤其是在 `test.dao.test_trade_record_dao_verification` 中），大量测试用例因 `IndexError: Replacement index 1 out of range for positional args tuple` 而失败。
- **影响范围**: 主要影响 `async_setUp` 中 `init_db()` 对 `TradeRecord` 模型进行 Beanie 初始化的过程。

## 2. 报告日期/发现日期
- 2025-05-30

## 3. 根源分析概要
该 `IndexError` 发生在 `pymongo/helpers_shared.py` 的 `_gen_index_name` 函数内部，具体代码行：`return "_".join(["{}_{}".format(*item) for item in keys])`。
当 Beanie ODM 在初始化 `TradeRecord` 文档并尝试创建其在 MongoDB 中的索引时，如果 `TradeRecord.Settings.indexes` 列表中的条目是单个字符串（例如 `"signal_id"`），而不是一个包含字段名和方向的元组（例如 `("signal_id", pymongo.ASCENDING)`），那么 `*item` 会将该字符串解包成单个字符。这导致传递给 `.format()` 的参数数量与格式字符串 `"{}_{}"`（期望两个参数）不匹配，从而引发 `IndexError`。

问题代码位于 `models/trade_record.py` 的 `TradeRecord.Settings.indexes` 定义。

## 4. 详细的、已获批准的修复方案
1.  **修改 `models/trade_record.py` 文件。**
2.  **导入 `pymongo` 模块**: 在文件顶部添加 `import pymongo`。
3.  **修正 `TradeRecord.Settings.indexes` 定义**: 将 `indexes` 列表中的所有字符串条目更改为正确的元组格式 `(field_name, direction)`。对于所有现有索引，默认使用 `pymongo.ASCENDING` 作为方向。

    例如，将：
    ```python
    indexes = [
        "signal_id",
        "tx_hash",
        # ...
    ]
    ```
    修改为：
    ```python
    indexes = [
        [("signal_id", pymongo.ASCENDING)],
        [("tx_hash", pymongo.ASCENDING)],
        # ...
    ]
    ```
    确保列表中的每个索引定义都是一个包含单个元组 `(field, direction)` 的列表，或者是一个包含多个此类元组的列表（用于复合索引）。但根据现有定义，它们都是单字段索引。

## 5. 测试用例设计
此修复主要针对模型初始化阶段的错误，因此现有的 DAO 层测试（如 `test_trade_record_dao_verification.py`）在 `async_setUp` 成功执行 `init_db()` 后，应能正常运行并验证 DAO 逻辑。
- **核心验证**: 修复后，之前因 `IndexError` 失败的 `TestTradeRecordDAOVerification` 中的所有测试用例应该不再因该 `IndexError` 而失败。它们现在应该能够成功完成 `async_setUp`。

## 6. 方案提出者/执行者
- AI Assistant (Gemini 2.5 Pro)

## 7. 方案审阅者/批准者
- User (通过后续操作隐式批准)

## 8. 方案批准日期
- 2025-05-30

## 9. 预期的验证方法
- 重新运行所有单元测试 (`poetry run python -m unittest`)。
- 观察 `test.dao.test_trade_record_dao_verification.TestTradeRecordDAOVerification` 中的测试是否不再出现由 `_gen_index_name` 引发的 `IndexError`。
- 关注整体测试通过率是否提高。 