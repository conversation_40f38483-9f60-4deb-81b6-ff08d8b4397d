#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Trade Record Verification Updater
交易记录验证更新器

该模块负责验证交易记录的实际输出金额，通过解析Solana区块链交易数据来确认实际的代币输出数量。
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from decimal import Decimal

from dao.trade_record_dao import TradeRecordDAO
from models.trade_record import TradeRecord
from utils.spiders.solana.solana_monitor import SolanaMonitor

# 配置日志
logger = logging.getLogger(__name__)


class TradeRecordVerificationUpdater:
    """
    交易记录验证更新器
    
    负责定期扫描待验证的交易记录，通过Solana区块链数据验证实际的代币输出金额，
    并更新相应的验证字段。
    """
    
    def __init__(self):
        """
        初始化验证更新器
        """
        self.trade_record_dao = TradeRecordDAO()
        self.solana_monitor = SolanaMonitor()
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 配置参数
        self.max_retry_attempts = 3
        self.retry_delay_seconds = 2
        self.verification_timeout_seconds = 8
        
    async def run_verification_cycle(self, time_window_hours: int = 24, batch_size: int = 50) -> Dict[str, Any]:
        """
        执行一轮验证周期
        
        Args:
            time_window_hours: 时间窗口（小时），默认24小时
            batch_size: 批处理大小，默认50条
            
        Returns:
            执行结果统计
        """
        try:
            start_time = datetime.now()
            self.logger.info(f"开始执行验证周期，时间窗口: {time_window_hours}小时，批大小: {batch_size}")
            
            # 计算时间窗口
            cutoff_time = datetime.utcnow() - timedelta(hours=time_window_hours)
            
            # 查询待验证的交易记录
            pending_records = await self.trade_record_dao.find_pending_verification_records(
                cutoff_time=cutoff_time,
                limit=batch_size
            )
            
            if not pending_records:
                self.logger.info("没有待验证的交易记录")
                return {
                    'status': 'completed',
                    'processed_count': 0,
                    'verified_count': 0,
                    'failed_count': 0,
                    'skipped_count': 0,
                    'execution_time': (datetime.now() - start_time).total_seconds()
                }
            
            self.logger.info(f"找到 {len(pending_records)} 条待验证记录")
            
            # 处理每条记录
            verified_count = 0
            failed_count = 0
            skipped_count = 0
            
            for record in pending_records:
                try:
                    result = await self.verify_and_update_record(record)
                    
                    if result['status'] == 'verified':
                        verified_count += 1
                    elif result['status'] == 'failed':
                        failed_count += 1
                    elif result['status'] == 'skipped':
                        skipped_count += 1
                        
                except Exception as e:
                    failed_count += 1
                    self.logger.error(f"处理记录 {record.id} 失败: {str(e)}")
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            result = {
                'status': 'completed',
                'processed_count': len(pending_records),
                'verified_count': verified_count,
                'failed_count': failed_count,
                'skipped_count': skipped_count,
                'execution_time': execution_time
            }
            
            self.logger.info(f"验证周期完成: {result}")
            return result
            
        except Exception as e:
            self.logger.error(f"执行验证周期失败: {str(e)}")
            import traceback
            self.logger.error(f"错误堆栈: {traceback.format_exc()}")
            
            return {
                'status': 'failed',
                'error': str(e),
                'execution_time': (datetime.now() - start_time).total_seconds()
            }
    
    async def verify_and_update_record(self, record: TradeRecord) -> Dict[str, Any]:
        """
        验证并更新单条交易记录
        
        Args:
            record: 交易记录对象
            
        Returns:
            验证结果
        """
        try:
            self.logger.debug(f"开始验证记录 {record.id}")
            
            # 检查必要字段
            if not record.tx_hash:
                self.logger.warning(f"记录 {record.id} 缺少交易哈希，跳过验证")
                await self._update_verification_status(record.id, 'skipped', error_message='Missing tx_hash')
                return {'status': 'skipped', 'reason': 'Missing tx_hash'}
            
            if not record.token_out_address:
                self.logger.warning(f"记录 {record.id} 缺少输出代币地址，跳过验证")
                await self._update_verification_status(record.id, 'skipped', error_message='Missing token_out_address')
                return {'status': 'skipped', 'reason': 'Missing token_out_address'}
            
            # 执行验证
            verification_result = await self.verify_single_record(
                tx_hash=record.tx_hash,
                token_out_address=record.token_out_address,
                wallet_address=record.wallet_address
            )
            
            # 更新验证结果
            if verification_result['status'] == 'verified':
                await self._update_verification_status(
                    record_id=record.id,
                    status='verified',
                    verified_amount=verification_result['verified_amount']
                )
                self.logger.info(f"记录 {record.id} 验证成功，实际金额: {verification_result['verified_amount']}")
                
            elif verification_result['status'] == 'failed':
                await self._update_verification_status(
                    record_id=record.id,
                    status='failed',
                    error_message=verification_result.get('error_message', 'Verification failed')
                )
                self.logger.warning(f"记录 {record.id} 验证失败: {verification_result.get('error_message')}")
            
            return verification_result
            
        except Exception as e:
            self.logger.error(f"验证记录 {record.id} 失败: {str(e)}")
            await self._update_verification_status(
                record_id=record.id,
                status='failed',
                error_message=str(e)
            )
            return {'status': 'failed', 'error_message': str(e)}
    
    async def verify_single_record(self, tx_hash: str, token_out_address: str, wallet_address: str = None) -> Dict[str, Any]:
        """
        验证单条交易记录的实际输出金额
        
        Args:
            tx_hash: 交易哈希
            token_out_address: 输出代币地址
            wallet_address: 钱包地址（可选）
            
        Returns:
            验证结果
        """
        try:
            self.logger.debug(f"开始验证交易 {tx_hash}")
            
            # 使用重试机制调用Solana监控器
            for attempt in range(self.max_retry_attempts):
                try:
                    # 设置超时
                    verified_amount = await asyncio.wait_for(
                        self.solana_monitor.get_confirmed_token_output_from_tx(
                            tx_hash=tx_hash,
                            expected_output_token_mint=token_out_address,
                            wallet_address=wallet_address
                        ),
                        timeout=self.verification_timeout_seconds
                    )
                    
                    if verified_amount is not None:
                        self.logger.debug(f"交易 {tx_hash} 验证成功，金额: {verified_amount}")
                        return {
                            'status': 'verified',
                            'verified_amount': float(verified_amount),
                            'attempts': attempt + 1
                        }
                    else:
                        self.logger.warning(f"交易 {tx_hash} 未找到输出金额（尝试 {attempt + 1}/{self.max_retry_attempts}）")
                        
                        # 如果是最后一次尝试，返回失败
                        if attempt == self.max_retry_attempts - 1:
                            return {
                                'status': 'failed',
                                'error_message': 'No token output found in transaction',
                                'attempts': attempt + 1
                            }
                        
                        # 等待后重试
                        await asyncio.sleep(self.retry_delay_seconds)
                        
                except asyncio.TimeoutError:
                    self.logger.warning(f"验证交易 {tx_hash} 超时（尝试 {attempt + 1}/{self.max_retry_attempts}）")
                    
                    if attempt == self.max_retry_attempts - 1:
                        return {
                            'status': 'failed',
                            'error_message': 'Verification timeout',
                            'attempts': attempt + 1
                        }
                    
                    await asyncio.sleep(self.retry_delay_seconds)
                    
                except Exception as e:
                    self.logger.error(f"验证交易 {tx_hash} 异常（尝试 {attempt + 1}/{self.max_retry_attempts}）: {str(e)}")
                    
                    if attempt == self.max_retry_attempts - 1:
                        return {
                            'status': 'failed',
                            'error_message': str(e),
                            'attempts': attempt + 1
                        }
                    
                    await asyncio.sleep(self.retry_delay_seconds)
            
            # 理论上不会到达这里
            return {
                'status': 'failed',
                'error_message': 'Max retry attempts exceeded',
                'attempts': self.max_retry_attempts
            }
            
        except Exception as e:
            self.logger.error(f"验证交易 {tx_hash} 失败: {str(e)}")
            return {
                'status': 'failed',
                'error_message': str(e),
                'attempts': 0
            }
    
    async def _update_verification_status(self, record_id: str, status: str, 
                                        verified_amount: float = None, error_message: str = None) -> bool:
        """
        更新记录的验证状态
        
        Args:
            record_id: 记录ID
            status: 验证状态 ('verified', 'failed', 'skipped')
            verified_amount: 验证的金额（可选）
            error_message: 错误信息（可选）
            
        Returns:
            更新是否成功
        """
        try:
            update_data = {
                'verification_status': status,
                'verification_timestamp': datetime.utcnow()
            }
            
            if verified_amount is not None:
                update_data['token_out_verified_amount'] = Decimal(str(verified_amount))
            
            if error_message:
                update_data['verification_error'] = error_message
            
            success = await self.trade_record_dao.update_verification_result(
                record_id=record_id,
                update_data=update_data
            )
            
            if not success:
                self.logger.error(f"更新记录 {record_id} 的验证状态失败")
            
            return success
            
        except Exception as e:
            self.logger.error(f"更新记录 {record_id} 验证状态失败: {str(e)}")
            return False
    
    async def get_verification_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """
        获取验证统计信息
        
        Args:
            hours: 统计时间范围（小时）
            
        Returns:
            统计信息
        """
        try:
            cutoff_time = datetime.utcnow() - timedelta(hours=hours)
            
            # 获取统计数据
            stats = await self.trade_record_dao.get_verification_statistics(cutoff_time)
            
            return {
                'time_range_hours': hours,
                'total_records': stats.get('total', 0),
                'pending_records': stats.get('pending', 0),
                'verified_records': stats.get('verified', 0),
                'failed_records': stats.get('failed', 0),
                'skipped_records': stats.get('skipped', 0),
                'verification_rate': stats.get('verification_rate', 0.0),
                'generated_at': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"获取验证统计信息失败: {str(e)}")
            return {
                'error': str(e),
                'generated_at': datetime.utcnow().isoformat()
            }
    
    async def cleanup_old_verification_data(self, days: int = 30) -> Dict[str, Any]:
        """
        清理旧的验证数据
        
        Args:
            days: 保留天数
            
        Returns:
            清理结果
        """
        try:
            cutoff_time = datetime.utcnow() - timedelta(days=days)
            
            # 执行清理
            cleanup_result = await self.trade_record_dao.cleanup_old_verification_data(cutoff_time)
            
            self.logger.info(f"清理完成，删除 {cleanup_result.get('deleted_count', 0)} 条旧记录")
            
            return {
                'status': 'completed',
                'deleted_count': cleanup_result.get('deleted_count', 0),
                'cutoff_date': cutoff_time.isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"清理旧验证数据失败: {str(e)}")
            return {
                'status': 'failed',
                'error': str(e)
            }


if __name__ == "__main__":
    # 测试执行
    async def main():
        updater = TradeRecordVerificationUpdater()
        
        # 执行一轮验证
        result = await updater.run_verification_cycle()
        print(f"验证结果: {result}")
        
        # 获取统计信息
        stats = await updater.get_verification_statistics()
        print(f"统计信息: {stats}")
    
    asyncio.run(main())