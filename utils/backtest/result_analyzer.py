import logging
import json
import csv
from typing import Dict, Any, List
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime

logger = logging.getLogger("ResultAnalyzer")

class ResultAnalyzer:
    """结果分析器，负责分析和输出回测结果"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化结果分析器
        
        Args:
            config: 回测配置
        """
        self.config = config
        
    def calculate_statistics(self, trade_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算交易统计数据
        
        Args:
            trade_results: 交易结果列表
            
        Returns:
            Dict[str, Any]: 统计数据
        """
        if not trade_results:
            return {
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'win_rate': 0,
                'average_return': 0,
                'max_return': 0,
                'min_return': 0,
                'cumulative_return': 0,
                'max_drawdown': 0  # 确保即使没有交易结果也返回max_drawdown键
            }
        
        # 计算基本统计数据
        total_trades = len(trade_results)
        winning_trades = sum(1 for t in trade_results if t['profit'])
        losing_trades = total_trades - winning_trades
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # 计算收益统计
        returns = [t['return_rate'] for t in trade_results]
        average_return = sum(returns) / len(returns) if returns else 0
        max_return = max(returns) if returns else 0
        min_return = min(returns) if returns else 0
        
        # 计算累积收益
        cumulative_return = (1 + np.array(returns)).prod() - 1 if returns else 0
        
        # 计算最大回撤
        if returns:
            # 假设初始资金为1
            equity_curve = np.cumprod(1 + np.array(returns))
            running_max = np.maximum.accumulate(equity_curve)
            drawdown = (running_max - equity_curve) / running_max
            max_drawdown = drawdown.max()
        else:
            max_drawdown = 0
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'average_return': average_return,
            'max_return': max_return,
            'min_return': min_return,
            'cumulative_return': cumulative_return,
            'max_drawdown': max_drawdown
        }
    
    def export_to_json(self, 
                      trade_results: List[Dict[str, Any]], 
                      statistics: Dict[str, Any], 
                      file_path: str) -> None:
        """将结果导出为JSON文件
        
        Args:
            trade_results: 交易结果列表
            statistics: 统计数据
            file_path: 文件路径
        """
        data = {
            'config': self.config,
            'statistics': statistics,
            'trade_results': trade_results
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"结果已导出至JSON文件: {file_path}")
    
    def export_to_csv(self, trade_results: List[Dict[str, Any]], file_path: str) -> None:
        """将交易结果导出为CSV文件
        
        Args:
            trade_results: 交易结果列表
            file_path: 文件路径
        """
        if not trade_results:
            logger.warning("无交易结果可导出")
            return
        
        # 获取字段名
        fieldnames = ['token_address', 'token_name', 'token_symbol', 
                      'buy_time', 'sell_time', 'buy_price', 'sell_price', 
                      'return_rate', 'profit']
        
        with open(file_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            
            for result in trade_results:
                # 创建一个只包含要导出字段的字典
                row = {field: result.get(field, '') for field in fieldnames}
                
                # 转换时间戳为可读格式
                if 'buy_time' in row:
                    row['buy_time'] = datetime.fromtimestamp(row['buy_time']).isoformat()
                if 'sell_time' in row:
                    row['sell_time'] = datetime.fromtimestamp(row['sell_time']).isoformat()
                
                writer.writerow(row)
        
        logger.info(f"交易结果已导出至CSV文件: {file_path}")
    
    def plot_returns(self, trade_results: List[Dict[str, Any]], file_path: str) -> None:
        """绘制收益图表
        
        Args:
            trade_results: 交易结果列表
            file_path: 图表保存路径
        """
        if not trade_results:
            logger.warning("无交易结果可绘图")
            return
        
        # 按时间排序交易结果
        sorted_results = sorted(trade_results, key=lambda x: x['sell_time'])
        
        # 提取时间和收益率
        times = [datetime.fromtimestamp(r['sell_time']) for r in sorted_results]
        returns = [r['return_rate'] for r in sorted_results]
        
        # 计算累积收益
        cumulative_returns = np.cumprod(1 + np.array(returns)) - 1
        
        # 创建图表
        plt.figure(figsize=(12, 8))
        
        # 绘制单次收益
        plt.subplot(2, 1, 1)
        plt.bar(times, returns, color=['green' if r > 0 else 'red' for r in returns])
        plt.title('单次交易收益')
        plt.ylabel('收益率')
        plt.grid(True, alpha=0.3)
        
        # 绘制累积收益
        plt.subplot(2, 1, 2)
        plt.plot(times, cumulative_returns, 'b-')
        plt.title('累积收益')
        plt.ylabel('累积收益率')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(file_path)
        plt.close()
        
        logger.info(f"收益图表已保存至: {file_path}") 