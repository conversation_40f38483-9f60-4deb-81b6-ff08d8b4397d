server {
    listen 80;
    listen [::]:80;
    server_name meme-monitor.api.gaojbindev.xyz;
    
    # 将 HTTP 请求重定向到 HTTPS
    location / {
        return 301 https://$server_name$request_uri;
    }
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name meme-monitor.api.gaojbindev.xyz;

    # SSL 配置
    ssl_certificate /etc/nginx/ssl/meme-monitor.api.gaojbindev.xyz_ecc/fullchain.pem;
    ssl_certificate_key /etc/nginx/ssl/meme-monitor.api.gaojbindev.xyz_ecc/key.pem;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;

    # 现代化 SSL 配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # HSTS (建议开启，但要确保证书配置正确)
    add_header Strict-Transport-Security "max-age=63072000" always;

    # API 反向代理
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 日志配置
    access_log /var/log/nginx/meme_monitor_api_access.log;
    error_log /var/log/nginx/meme_monitor_api_error.log;
} 