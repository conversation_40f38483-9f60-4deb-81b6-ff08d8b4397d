"""
ChannelRegistry单元测试

测试渠道注册表的核心功能：
1. 渠道注册和管理
2. 渠道获取
3. 健康状态检查
4. 统计信息收集
5. 清理机制
"""

import unittest
import asyncio
from unittest.mock import Mock, AsyncMock
from typing import Dict, Any

from utils.trading.channel_registry import ChannelRegistry, ChannelNotFoundError
from utils.trading.solana.trade_interface import TradeInterface, TradeResult, TradeStatus
from models.config import TradeChannelConfig


class MockTradeInterface(TradeInterface):
    """用于测试的Mock交易接口"""
    
    def __init__(self, channel_type: str, is_healthy: bool = True):
        self.channel_type = channel_type
        self.is_healthy = is_healthy
        self.health_check_count = 0
        self.close_called = False
    
    async def execute_trade(self, *args, **kwargs):
        """Mock执行交易"""
        return TradeResult(status=TradeStatus.SUCCESS, message="Mock trade success")
    
    async def is_available(self) -> bool:
        """Mock健康检查"""
        self.health_check_count += 1
        return self.is_healthy
    
    async def close(self) -> None:
        """Mock关闭"""
        self.close_called = True
    
    def is_slippage_related_error(self, error_message: str = None, provider_response: Dict[str, Any] = None) -> bool:
        """Mock滑点错误识别"""
        return "slippage" in (error_message or "").lower()
    
    def is_non_retryable_error(self, error_message: str = None, provider_response: Dict[str, Any] = None) -> bool:
        """Mock不可重试错误识别"""
        return "insufficient funds" in (error_message or "").lower()


class TestChannelRegistry(unittest.IsolatedAsyncioTestCase):
    """ChannelRegistry单元测试类"""
    
    def setUp(self) -> None:
        """测试前置设置"""
        self.registry = ChannelRegistry()
        
        # 创建测试用的mock渠道
        self.mock_gmgn_channel = MockTradeInterface("gmgn", is_healthy=True)
        self.mock_solana_channel = MockTradeInterface("jupiter", is_healthy=True)
        self.mock_unhealthy_channel = MockTradeInterface("unhealthy", is_healthy=False)
        
        # 创建测试用的渠道配置
        self.gmgn_config = TradeChannelConfig(
            channel_type="gmgn",
            enabled=True,
            priority=1,
            max_concurrent_trades=5,
            timeout_seconds=30,
            retry_times=3,
            health_check_interval_minutes=5
        )
        
        self.solana_config = TradeChannelConfig(
            channel_type="jupiter",
            enabled=True,
            priority=2,
            max_concurrent_trades=3,
            timeout_seconds=60,
            retry_times=2,
            health_check_interval_minutes=10
        )
    
    async def asyncTearDown(self) -> None:
        """异步测试后置清理"""
        # 清理注册表
        await self.registry.close_all_channels()
    
    def test_register_channel(self) -> None:
        """测试渠道注册"""
        # 注册渠道 - 使用正确的参数
        self.registry.register_channel("gmgn", self.mock_gmgn_channel, self.gmgn_config)
        
        # 验证注册成功
        self.assertIn("gmgn", self.registry.list_channels())
        self.assertEqual(len(self.registry.list_channels()), 1)
        self.assertTrue(self.registry.is_channel_registered("gmgn"))
    
    def test_register_duplicate_channel(self) -> None:
        """测试重复注册渠道"""
        # 首次注册
        self.registry.register_channel("gmgn", self.mock_gmgn_channel, self.gmgn_config)
        
        # 重复注册
        new_mock_channel = MockTradeInterface("gmgn")
        self.registry.register_channel("gmgn", new_mock_channel, self.gmgn_config)
        
        # 验证被覆盖
        registered_channel = self.registry.get_channel("gmgn")
        self.assertIs(registered_channel, new_mock_channel)
    
    def test_get_channel_success(self) -> None:
        """测试成功获取渠道"""
        # 注册渠道
        self.registry.register_channel("gmgn", self.mock_gmgn_channel, self.gmgn_config)
        
        # 获取渠道
        channel = self.registry.get_channel("gmgn")
        
        # 验证获取成功
        self.assertIs(channel, self.mock_gmgn_channel)
    
    def test_get_channel_not_found(self) -> None:
        """测试获取不存在的渠道"""
        # 获取不存在的渠道应该抛出异常
        with self.assertRaises(ChannelNotFoundError):
            self.registry.get_channel("nonexistent")
    
    def test_get_channel_config(self) -> None:
        """测试获取渠道配置"""
        # 注册渠道
        self.registry.register_channel("gmgn", self.mock_gmgn_channel, self.gmgn_config)
        
        # 获取配置
        config = self.registry.get_channel_config("gmgn")
        
        # 验证配置正确
        self.assertIs(config, self.gmgn_config)
    
    def test_list_channels(self) -> None:
        """测试获取已注册渠道列表"""
        # 注册多个渠道
        self.registry.register_channel("gmgn", self.mock_gmgn_channel, self.gmgn_config)
        self.registry.register_channel("jupiter", self.mock_solana_channel, self.solana_config)
        
        # 获取渠道列表
        channels = self.registry.list_channels()
        
        # 验证渠道列表
        self.assertEqual(set(channels), {"gmgn", "jupiter"})
        self.assertEqual(len(channels), 2)
    
    def test_list_enabled_channels(self) -> None:
        """测试获取已启用渠道列表"""
        # 创建禁用的配置
        disabled_config = TradeChannelConfig(
            channel_type="disabled",
            enabled=False,
            priority=1,
            max_concurrent_trades=1,
            timeout_seconds=30,
            retry_times=1,
            health_check_interval_minutes=5
        )
        
        # 注册渠道
        self.registry.register_channel("gmgn", self.mock_gmgn_channel, self.gmgn_config)
        self.registry.register_channel("disabled", self.mock_unhealthy_channel, disabled_config)
        
        # 获取启用的渠道列表
        enabled_channels = self.registry.list_enabled_channels()
        
        # 验证只返回启用的渠道
        self.assertEqual(enabled_channels, ["gmgn"])
    
    def test_is_channel_registered(self) -> None:
        """测试检查渠道是否已注册"""
        # 注册渠道
        self.registry.register_channel("gmgn", self.mock_gmgn_channel, self.gmgn_config)
        
        # 验证注册状态
        self.assertTrue(self.registry.is_channel_registered("gmgn"))
        self.assertFalse(self.registry.is_channel_registered("nonexistent"))
    
    def test_is_channel_enabled(self) -> None:
        """测试检查渠道是否启用"""
        # 注册渠道
        self.registry.register_channel("gmgn", self.mock_gmgn_channel, self.gmgn_config)
        
        # 验证启用状态
        self.assertTrue(self.registry.is_channel_enabled("gmgn"))
        self.assertFalse(self.registry.is_channel_enabled("nonexistent"))
    
    def test_set_and_get_channel_health(self) -> None:
        """测试设置和获取渠道健康状态"""
        # 注册渠道
        self.registry.register_channel("gmgn", self.mock_gmgn_channel, self.gmgn_config)
        
        # 设置健康状态
        self.registry.set_channel_health("gmgn", False)
        
        # 验证健康状态
        self.assertFalse(self.registry.is_channel_healthy("gmgn"))
        
        # 恢复健康状态
        self.registry.set_channel_health("gmgn", True)
        self.assertTrue(self.registry.is_channel_healthy("gmgn"))
    
    def test_get_registry_stats(self) -> None:
        """测试获取注册表统计信息"""
        # 注册多个渠道
        self.registry.register_channel("gmgn", self.mock_gmgn_channel, self.gmgn_config)
        self.registry.register_channel("jupiter", self.mock_solana_channel, self.solana_config)
        
        # 获取统计信息
        stats = self.registry.get_registry_stats()
        
        # 验证统计信息
        self.assertEqual(stats["total_channels"], 2)
        self.assertEqual(stats["enabled_channels"], 2)
        self.assertEqual(stats["healthy_channels"], 2)
        self.assertIn("channels", stats)
        self.assertEqual(len(stats["channels"]), 2)
    
    def test_remove_channel(self) -> None:
        """测试移除渠道"""
        # 注册渠道
        self.registry.register_channel("gmgn", self.mock_gmgn_channel, self.gmgn_config)
        self.assertEqual(len(self.registry.list_channels()), 1)
        
        # 移除渠道
        result = self.registry.remove_channel("gmgn")
        
        # 验证移除成功
        self.assertTrue(result)
        self.assertEqual(len(self.registry.list_channels()), 0)
        self.assertFalse(self.registry.is_channel_registered("gmgn"))
    
    def test_remove_nonexistent_channel(self) -> None:
        """测试移除不存在的渠道"""
        # 移除不存在的渠道
        result = self.registry.remove_channel("nonexistent")
        
        # 验证移除失败
        self.assertFalse(result)
    
    async def test_aremove_channel(self) -> None:
        """测试异步移除渠道"""
        # 注册渠道
        self.registry.register_channel("gmgn", self.mock_gmgn_channel, self.gmgn_config)
        
        # 异步移除渠道
        result = await self.registry.aremove_channel("gmgn")
        
        # 验证移除成功
        self.assertTrue(result)
        self.assertEqual(len(self.registry.list_channels()), 0)
        self.assertTrue(self.mock_gmgn_channel.close_called)
    
    async def test_close_all_channels(self) -> None:
        """测试关闭所有渠道"""
        # 注册多个渠道
        self.registry.register_channel("gmgn", self.mock_gmgn_channel, self.gmgn_config)
        self.registry.register_channel("jupiter", self.mock_solana_channel, self.solana_config)
        
        # 关闭所有渠道
        await self.registry.close_all_channels()
        
        # 验证所有渠道都被关闭
        self.assertTrue(self.mock_gmgn_channel.close_called)
        self.assertTrue(self.mock_solana_channel.close_called)


if __name__ == '__main__':
    unittest.main() 