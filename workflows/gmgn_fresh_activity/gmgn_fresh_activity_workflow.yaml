name: "GMGN 代币KOL活动记录"
description: "定期获取链上的KOL活动记录，并将数据存储到数据库"

nodes:
  - name: "FreshActivitySchedulerNode"
    node_type: "input"
    interval: 10
    generate_data: workflows.gmgn_token_link.handler.generate_data
    flow_control:
      max_pending_messages: 10
      check_interval: 1
      enable_flow_control: true

  - name: "FreshActivityMonitorNode"
    node_type: "process"
    depend_ons: ["FreshActivitySchedulerNode"]
    concurrency: 1
    interval: 1
    process_item: workflows.gmgn_fresh_activity.handler.process_fresh_activity

  - name: "FreshActivityStoreNode"
    node_type: "storage"
    depend_ons: ["FreshActivityMonitorNode"]
    batch_size: 1000  # 每批处理1000条数据
    store_data: workflows.gmgn_fresh_activity.handler.store_data
    validate: workflows.gmgn_fresh_activity.handler.validate 
