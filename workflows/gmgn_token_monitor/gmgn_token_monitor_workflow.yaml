name: 'GMGN代币监控工作流'
description: '定期获取不同时间范围的代币数据，并将数据存储到数据库'

nodes:
  - name: "TokenSchedulerNode"
    node_type: "input"
    interval: 1  # 每秒运行一次
    generate_data: workflows.gmgn_token_monitor.handler.generate_time_range
    flow_control:
      max_pending_messages: 10
      check_interval: 1
      enable_flow_control: true

  - name: "TokenMonitorNode"
    node_type: "process"
    depend_ons: ["TokenSchedulerNode"]
    concurrency: 1
    interval: 1
    process_item: workflows.gmgn_token_monitor.handler.process_token_data

  - name: "TokenStorageNode"
    node_type: "storage"
    depend_ons: ["TokenMonitorNode"]
    batch_size: 100  # 每批处理100条数据
    store_data: workflows.gmgn_token_monitor.handler.store_token_data
    validate: workflows.gmgn_token_monitor.handler.validate_token_data 