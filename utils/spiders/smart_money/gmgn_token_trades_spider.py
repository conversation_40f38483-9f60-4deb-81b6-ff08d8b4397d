import random
import asyncio
import traceback
import logging
from typing import AsyncGenerator, Dict, Any, List, Optional
import json
from utils.spiders.smart_money import BasicSpider

logger = logging.getLogger(__name__)

class GmgnTokenTradesSpider(BasicSpider):
    """GMGN代币交易爬虫
    
    用于获取代币的交易信息，包括：
    - 交易者地址
    - 交易金额和数量
    - 交易事件（买入/卖出）
    - 利润和收益率
    - 交易者标签
    - 活跃时间
    """
    
    BASE_URL = "https://gmgn.ai/api/v1/token_trades"
    
    def __init__(self, max_retries: int = 5, retry_interval: float = 1.0):
        super().__init__(max_retries=max_retries, retry_interval=retry_interval)
        self.headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-TW;q=0.6',
            'cache-control': 'no-cache',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://gmgn.ai/sol/token/dDO6ZdYE_CniPCE4b3s8gSUPhUiyMjXnytrEqUrMfSsnbBjLCpump?tab=activity',
            'sec-ch-ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-ch-ua-arch': '"arm"',
            'sec-ch-ua-full-version': '"134.0.6998.45"',
            'sec-ch-ua-platform-version': '"14.5.0"',
            'sec-ch-ua-bitness': '"64"',
            'sec-ch-ua-model': '""',
            'sec-ch-ua-full-version-list': '"Chromium";v="134.0.6998.45", "Not:A-Brand";v="********", "Google Chrome";v="134.0.6998.45"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
    
    async def setup(self):
        """设置爬虫，初始化会话并更新头信息"""
        await self.init_sessions()
        # 更新会话头信息
        self.session.headers.update(self.headers)
        # 设置浏览器模拟
        self.session.impersonate = "chrome110"  # 模拟Chrome浏览器
        
    async def _get_token_trades(self, url: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取代币交易数据，使用BasicSpider的重试机制
        
        Args:
            url: API请求URL
            params: 请求参数
            
        Returns:
            Dict[str, Any]: API响应数据
        """
        try:
            # 使用带有重试机制的get方法
            response = await self.get(url, params=params)
            
            # 记录响应状态和内容
            logger.debug(f"API响应状态码: {response.status_code}")
            logger.debug(f"API响应内容: {response.text[:500]}...")  # 只记录前500个字符，避免日志过大
            
            # 检查响应状态
            if response.status_code != 200:
                logger.error(f"获取代币交易信息失败: HTTP {response.status_code}")
                logger.error(f"错误响应: {response.text}")
                return {}
            
            data = response.json()
            
            # 检查API响应格式
            if not isinstance(data, dict):
                logger.error(f"API响应格式错误，期望dict类型，实际为: {type(data)}")
                return {}
            
            # 检查API响应状态
            if data.get('code') != 0:  # 0 表示成功
                logger.error(f"API返回错误: {data.get('message', '未知错误')}")
                return {}
            
            return data
        except Exception as e:
            logger.error(f"获取代币交易信息时发生错误: {traceback.format_exc()}")
            return {}
        
    def make_params(self, tag: str = "kol", limit: int = 100, revert: bool = False) -> Dict[str, Any]:
        return {
            'device_id': '65990bc3-0a13-4d99-8c66-eeea94916b0f',
            'client_id': 'gmgn_web_20250425-410-f5d2160',
            'from_app': 'gmgn',
            'app_ver': '20250425-410-f5d2160',
            'tz_name': 'Asia/Shanghai',
            'tz_offset': '28800',
            'app_lang': 'zh-CN',
            'fp_did': 'a7a28f6bc1de9c2bf214c1036f907cc6',
            'os': 'web',
            'limit': limit,
            'tag': tag,
            'revert': revert,
        }
        
    def make_url(self, chain: str, address: str) -> str:
        return f"{self.BASE_URL}/{chain}/{address}"
    
    async def get_token_trades(self, 
                               chain: str, 
                               address: str, 
                               tag: str = "kol", 
                               limit: int = 100, 
                               interval: float = 0.1, 
                               until_trade_timestamp: int = None,
                               revert: bool = False
                               ) -> List[Dict[str, Any]]:
        """
        获取代币的交易信息
        
        Args:
            chain: 链名称，如"sol"
            address: 代币地址
            tag: 交易者标签，默认为"kol"
            limit: 每页返回的记录数，默认为100
            interval: 请求间隔时间，默认为0.1秒
            until_trade_timestamp: 结束的timestamp，默认为None
            revert: 是否反转，默认为False，如果为True，则从后往前获取交易信息即从最早开始买入的开始
            
        Returns:
            List[Dict[str, Any]]: 交易信息列表
        """
        try:
            url = self.make_url(chain, address)
            logger.info(f"正在获取代币交易信息: {url}, tag={tag}, limit={limit}")
            
            # 初始化结果列表
            all_trades = []
            next_cursor = None
            
            while True:
                # 构建请求参数
                params = self.make_params(tag, limit, revert)
                if next_cursor:
                    params['cursor'] = next_cursor
                
                # 获取交易数据
                data = await self._get_token_trades(url, params)
                if not data:
                    break
                
                # 提取实际数据
                trades = data.get('data', {}).get('history', [])
                trades = [{**x, 'chain': chain, 'token_address': address} for x in trades]
                
                # 如果revert为True，则从后(最早购入)往前(最新购入)过滤，否则从前(最新购入)往后(最早购入)过滤
                if until_trade_timestamp:
                    if revert:
                        trades = [x for x in trades if x['timestamp'] <= until_trade_timestamp]
                    else:
                        trades = [x for x in trades if x['timestamp'] >= until_trade_timestamp]
                    all_trades.extend(trades)
                    break
                
                if not trades:
                    logger.warning("API响应中没有交易数据")
                    break
                
                # 添加到结果列表
                all_trades.extend(trades)
                
                # 检查是否有下一页
                next_cursor = data.get('data', {}).get('next')
                if not next_cursor:
                    break
                
                logger.info(f"获取到{len(trades)}条交易记录，准备获取下一页，等待{interval}秒")
                await asyncio.sleep(interval)
                
            logger.info(f"成功获取代币交易信息，共{len(all_trades)}条记录")
            return all_trades
            
        except Exception as e:
            logger.error(f"获取代币交易信息时发生错误: {traceback.format_exc()}")
            return []
        
    async def get_token_trades_yield(self, 
                               chain: str, 
                               address: str, 
                               tag: str = "",
                               limit: int = 100, 
                               interval: float = 0.1, 
                               until_trade_timestamp: Optional[int] = None,
                               start_cursor: Optional[str] = None,
                               revert: bool = False
                               ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        获取代币的交易信息，使用异步生成器，支持从指定游标开始。
        
        Args:
            chain: 链名称。
            address: 代币地址。
            tag: 交易者标签。
            limit: 每页记录数。
            interval: 请求间隔。
            until_trade_timestamp: 结束时间戳 (用于增量更新)。
            start_cursor: 开始获取的游标 (用于恢复中断)。
            revert: 是否反向获取。

        Yields:
            Dict[str, Any]: 单条交易记录。
            
        Raises:
            Exception: 如果在获取过程中发生不可恢复的错误。
        """
        current_cursor = start_cursor
        last_yielded_trade_timestamp = None
        total_yielded_count = 0
        
        try:
            url = self.make_url(chain, address)
            logger.info(f"开始获取交易: {url}, tag='{tag}', limit={limit}, start_cursor={current_cursor}, until_ts={until_trade_timestamp}, revert={revert}")
            
            while True:
                params = self.make_params(tag, limit, revert)
                if current_cursor:
                    params['cursor'] = current_cursor
                
                data = await self._get_token_trades(url, params)
                # 如果 _get_token_trades 内部出错并返回空字典，则中断
                if not data:
                    logger.error(f"获取交易数据失败或API返回错误 for {url} with cursor {current_cursor}.")
                    # 抛出异常，让调用者处理状态更新 (标记为 failed)
                    raise RuntimeError(f"Failed to fetch trade data for {chain}:{address} after cursor {current_cursor}") 
                
                trades_data = data.get('data', {})
                trades = trades_data.get('history', [])
                next_cursor_from_api = trades_data.get('next') # 获取下一页的 cursor
                
                if not trades:
                    logger.info(f"在 cursor {current_cursor} 后没有更多交易数据 for {url}.")
                    break # 正常结束

                processed_trades = []
                should_stop_pagination = False
                for trade_raw in trades:
                    trade = {**trade_raw, 'chain': chain, 'token_address': address}
                    trade_timestamp = trade.get('timestamp')
                    
                    # 检查时间戳 (仅在非首次、非恢复抓取时有效)
                    if until_trade_timestamp is not None and start_cursor is None:
                        if not revert and trade_timestamp < until_trade_timestamp:
                            should_stop_pagination = True
                            break # 比目标时间戳旧，停止处理本页并停止翻页
                        # 注意: revert=True 的情况，until_trade_timestamp 意义不大，通常用于正向增量更新
                            
                    processed_trades.append(trade)
                    last_yielded_trade_timestamp = trade_timestamp # 记录最后 yield 的时间戳
                
                logger.debug(f"获取到 {len(trades)} 条原始记录, 处理后 {len(processed_trades)} 条, 下一页 cursor: {next_cursor_from_api}")
                
                if not processed_trades and not should_stop_pagination:
                     # 如果 API 返回了 trades，但处理后为空（可能全被时间戳过滤掉了）
                     # 并且不是因为时间戳过滤而停止，可能意味着过滤逻辑有问题或API数据异常
                     logger.warning(f"原始 trades 存在但处理后为空 for {url}, cursor {current_cursor}. 检查过滤逻辑或API数据。")
                     # 决定是否继续翻页，这里保守起见，先停止
                     # should_stop_pagination = True 

                for trade in processed_trades:
                    yield trade
                    total_yielded_count += 1
                
                # 决定是否继续翻页
                if should_stop_pagination:
                    logger.info(f"已达到或超过目标时间戳 {until_trade_timestamp}, 停止翻页.")
                    break
                
                current_cursor = next_cursor_from_api # 更新 cursor 以便下次循环使用
                if not current_cursor:
                    logger.info(f"API 返回没有下一页 cursor, 抓取完成 for {url}.")
                    break # API 表示没有更多页了
                
                logger.debug(f"准备获取下一页 (cursor={current_cursor}), 等待 {interval} 秒...")
                await asyncio.sleep(interval)
                
            logger.info(f"代币交易信息获取完成: {url}, tag='{tag}', 共产生 {total_yielded_count} 条记录.")
            # 正常完成，隐式返回 None (表示 next cursor)

        except Exception as e:
            logger.error(f"获取交易时发生异常 ({url}, cursor={current_cursor}): {traceback.format_exc()}")
            # 异常发生时，需要将当前的 cursor (即导致错误的下一页cursor) 返回给调用者
            # 让调用者能够知道从哪里恢复
            # 注意：如果是在 _get_token_trades 内部就失败了，我们上面已经 raise 了异常
            # 如果是在处理 trades 时失败，这里的 current_cursor 可能是刚从 API 获取的 next_cursor
            raise e # 将异常重新抛出，并携带当前 cursor 信息（虽然需要调用者捕获）
        
        # 返回最后的 cursor 状态，改为在 finally 块或者通过其他方式传递
        # 这里先不修改返回类型，让调用者处理异常来获取 cursor


# 示例用法
async def main():
    spider = GmgnTokenTradesSpider()
    # 获取SOL链上特定代币的kol标签的交易信息
    async for trade in spider.get_token_trades_yield(
        chain="sol", 
        address="AHnQjWa4jxYNxzfJantHDpcUKAE9pzVSGGPLhthPxCSW",
        tag="",
        limit=100,
        revert=True,
    ):
        print(trade)
        # with open("gmgn_token_trades.json", "r") as f:
        #     json.dump(trade, f, indent=4)
    # 关闭爬虫会话连接
    await spider.close()

if __name__ == "__main__":
    # 使用asyncio运行异步主函数
    asyncio.run(main())
