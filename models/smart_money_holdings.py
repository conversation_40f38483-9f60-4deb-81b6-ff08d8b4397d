from datetime import datetime
from typing import Optional, List
from beanie import Document, Indexed
from pydantic import BaseModel, Field

class TokenInfo(BaseModel):
    """代币基本信息"""
    address: str = Field(description="代币地址")
    token_address: str = Field(description="代币地址(冗余)")
    symbol: str = Field(description="代币符号")
    name: str = Field(description="代币名称")
    decimals: int = Field(description="小数位数")
    logo: Optional[str] = Field(default=None, description="代币logo")
    price_change_6h: str = Field(default="0", description="6小时价格变化")
    is_show_alert: bool = Field(default=False, description="是否显示警告")
    is_honeypot: Optional[bool] = Field(default=None, description="是否是蜜罐")

class HoldingInfo(BaseModel):
    """持仓信息"""
    token: TokenInfo = Field(description="代币信息")
    balance: str = Field(description="持仓数量")
    usd_value: str = Field(description="USD价值")
    realized_profit_30d: str = Field(description="30天已实现收益")
    realized_profit: str = Field(description="总已实现收益")
    realized_pnl: str = Field(description="已实现收益率")
    realized_pnl_30d: str = Field(description="30天已实现收益率")
    unrealized_profit: str = Field(description="未实现收益")
    unrealized_pnl: str = Field(description="未实现收益率")
    total_profit: str = Field(description="总收益")
    total_profit_pnl: str = Field(description="总收益率")
    avg_cost: str = Field(description="平均成本")
    avg_sold: str = Field(description="平均卖出价")
    buy_30d: int = Field(description="30天买入次数")
    sell_30d: int = Field(description="30天卖出次数")
    sells: int = Field(description="总卖出次数")
    price: str = Field(description="当前价格")
    cost: str = Field(description="总成本")
    position_percent: str = Field(description="仓位比例")
    last_active_timestamp: int = Field(description="最后活动时间戳")
    history_sold_income: str = Field(description="历史卖出收入")
    history_bought_cost: str = Field(description="历史买入成本")
    start_holding_at: Optional[int] = Field(default=None, description="开始持仓时间戳")
    end_holding_at: Optional[int] = Field(default=None, description="结束持仓时间戳")
    liquidity: str = Field(description="流动性")
    total_supply: str = Field(description="总供应量")
    wallet_token_tags: Optional[List[str]] = Field(default=None, description="钱包代币标签")

class SmartMoneyHoldings(Document):
    """聪明钱持仓数据模型"""
    # 基本信息
    wallet_address: Indexed(str) = Field(description="钱包地址")
    chain: str = Field(description="链名称")
    
    # 持仓列表
    holdings: List[HoldingInfo] = Field(default_factory=list, description="持仓列表")
    
    # 时间信息
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")
    
    class Settings:
        name = "smart_money_holdings"
    
    @classmethod
    async def from_api_data(cls, chain: str, wallet_address: str, holdings: list) -> 'SmartMoneyHoldings':
        """从API数据创建模型实例
        
        Args:
            chain: 链名称
            wallet_address: 钱包地址
            data: API返回的数据
            
        Returns:
            SmartMoneyHoldings: 模型实例
        """
        result = []
        for holding in holdings:
            if isinstance(holding, dict):
                token_info = TokenInfo(**holding.get("token", {}))
                holding_info = HoldingInfo(
                    token=token_info,
                    **{k: v for k, v in holding.items() if k != 'token'}
                )
                result.append(holding_info)
            elif isinstance(holding, HoldingInfo):
                result.append(holding)
        
        
        return cls(
            chain=chain,
            wallet_address=wallet_address,
            holdings=result
        )
    
    class Config:
        json_schema_extra = {
            "example": {
                "wallet_address": "DgAhccuXs7mjbvY3EXX6XLkBB5WStESQyLGQ9vLL1Z5x",
                "chain": "sol",
                "holdings": [
                    {
                        "token": {
                            "address": "A9pzvgRHWJGMdHt7H1SYXH21fHdTzb88uNHp9jdDpump",
                            "token_address": "A9pzvgRHWJGMdHt7H1SYXH21fHdTzb88uNHp9jdDpump",
                            "symbol": "PUNK",
                            "name": "MrPunk",
                            "decimals": 6,
                            "logo": "https://pump.mypinata.cloud/ipfs/QmbUmSAXgJFPXob2RPMQXDD5UJx1GcAFGtpcACbCjD2oM8",
                            "price_change_6h": "0.0297537469214325",
                            "is_show_alert": False,
                            "is_honeypot": None
                        },
                        "balance": "3093372.972769",
                        "usd_value": "7.6881247260022442877",
                        "realized_profit_30d": "0",
                        "realized_profit": "0",
                        "realized_pnl": "0",
                        "realized_pnl_30d": "0",
                        "unrealized_profit": "-470.8918752739653064888098",
                        "unrealized_pnl": "-0.9839355494880631",
                        "total_profit": "-470.8918752739653064888098",
                        "total_profit_pnl": "-0.9839355494880631",
                        "avg_cost": "0.0001547113795242",
                        "avg_sold": "0",
                        "buy_30d": 0,
                        "sell_30d": 0,
                        "sells": 0,
                        "price": "0.0000024853533",
                        "cost": "478.5799999999675507765098",
                        "position_percent": "1",
                        "last_active_timestamp": 1738242583,
                        "history_sold_income": "0",
                        "history_bought_cost": "478.58",
                        "start_holding_at": 1738242583,
                        "end_holding_at": None,
                        "liquidity": "4820.08899522202",
                        "total_supply": "997791087",
                        "wallet_token_tags": ["diamond_hands"]
                    }
                ],
                "created_at": "2024-03-07T00:00:00",
                "updated_at": "2024-03-07T00:00:00"
            }
        } 