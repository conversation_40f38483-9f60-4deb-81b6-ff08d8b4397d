# KolActivityTimestampDiscrepancyChecker 功能单元测试
创建日期：2024-05-23
更新日期：2024-05-23
测试方法：自动化测试
测试级别：单元测试

## 测试用例

| 用例方法                                                              | 用例概述                                                                 | 前置条件                                                                                                                              | 输入                                                                                                                               | 预期输出                                                                                                                                                                                                                            | 实际输出 | 状态   |
| --------------------------------------------------------------------- | ------------------------------------------------------------------------ | ------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------- | ------ |
| `test_load_config`                                                    | 测试配置加载功能，包括从数据库加载管理员ID和监控参数。                                           | 数据库中存在符合条件的`Config`文档。                                                                                                            | 无                                                                                                                                 | `checker.admin_chat_ids` 和 `checker._config` 被正确填充。                                                                                                                                                                      |          | 待运行 |
| `test_load_alert_state`                                               | 测试告警状态加载功能，区分导入和非导入类别。                                                     | 数据库中可能存在符合条件的`MonitorAlertState`文档。                                                                                               | 无                                                                                                                                 | `checker._alert_state` 被正确填充，对于数据库中不存在的类别，使用默认值。                                                                                                                                                                 |          | 待运行 |
| `test_save_alert_state`                                               | 测试告警状态保存到数据库的功能。                                                               | 无特定前置，依赖mock数据库操作。                                                                                                                    | `imported_from_following: bool` (True/False)                                                                                       | 对 `MonitorAlertState.update_one` (或其底层motor调用) 的调用被正确执行，参数符合预期，包含正确的查询条件、更新内容和upsert选项。                                                                                                               |          | 失败   |
| `test_get_latest_kol_wallet`                                          | 测试获取最新活跃KOL钱包的逻辑，区分导入和非导入类别。                                                 | 数据库中可能存在符合条件的`KOLWallet`文档。                                                                                                         | `imported_from_following: bool` (True/False)                                                                                       | 返回正确的`KOLWallet`实例或`None`。对`KOLWallet.find().sort().first_or_none()`的调用被正确执行，包含正确的过滤和排序条件。                                                                                                                   |          | 通过   |
| `test_get_latest_activity`                                            | 测试获取指定钱包最新活动的逻辑。                                                               | 数据库中可能存在符合条件的`KOLWalletActivity`文档。                                                                                               | `wallet_address: str`                                                                                                              | 返回正确的`KOLWalletActivity`实例或`None`。对`KOLWalletActivity.find().sort().first_or_none()`的调用被正确执行，包含正确的过滤和排序条件。                                                                                                       |          | 通过   |
| `test_record_alert_event`                                             | 测试告警事件记录到数据库的功能。                                                               | 无特定前置，依赖mock数据库操作。                                                                                                                    | `imported_from_following`, `wallet_address`, `activity_tx_hash`, `discrepancy_seconds`, `message`, `details`                     | `AlertEventRecord`实例被正确创建并调用`insert`。返回的ID是预期的。                                                                                                                                                                      |          | 通过   |
| `test_record_notification_log`                                        | 测试通知日志记录到数据库的功能。                                                               | 无特定前置，依赖mock数据库操作。                                                                                                                    | `alert_event_id`, `recipient_chat_id`, `status`, `message_preview`, `error_message`                                                  | `NotificationLogRecord`实例被正确创建并调用`insert`。                                                                                                                                                                                 |          | 通过   |
| `test_should_send_notification`                                       | 测试是否发送通知的判断逻辑，包括阈值检查、连续告警计数、抑制时间和状态保存。                                         | `checker._config` 和 `checker._alert_state` 已配置。                                                                                                  | `imported_from_following`, `discrepancy_seconds`, `wallet_address`, `activity`                                                   | 返回正确的 `(should_send, alert_event_id)` 元组。`_record_alert_event` 和 `_save_alert_state` 在适当的时候被调用。`_alert_state` 被正确更新。                                                                                                 |          | 通过   |
| `test_check_and_notify_for_category_sends_notification`             | 测试核心检查与通知方法在需要发送通知时的行为。                                                       | `checker.admin_chat_ids` 已配置。相关内部方法 (`_get_latest_kol_wallet`, `_get_latest_activity`, `_should_send_notification`) 被mock。 | `imported_from_following: bool`                                                                                                    | `message_sender.send_message_to_user` 和 `_record_notification_log` 被调用，参数正确。                                                                                                                                                        |          | 通过   |
| `test_check_and_notify_no_admin_ids`                                  | 测试当没有配置管理员ID时，检查流程是否跳过。                                                           | `checker.admin_chat_ids` 为空列表。                                                                                                               | `imported_from_following: bool`                                                                                                    | `_get_latest_kol_wallet` 方法不被调用。                                                                                                                                                                                                  |          | 通过   |
| `test_run_check_cycle`                                                | 测试完整的检查周期方法。                                                                   | 内部方法 (`_load_config`, `_load_alert_state`, `check_and_notify_for_category`) 被mock。                                                  | 无                                                                                                                                 | `_load_config` 和 `_load_alert_state` 各被调用一次。`check_and_notify_for_category` 被调用两次（针对两个类别）。                                                                                                                                |          | 通过   |
| `test_perform_kol_activity_timestamp_discrepancy_check_task`          | 测试工作流的入口函数。                                                                     | `KolActivityTimestampDiscrepancyChecker` 类被mock。                                                                                               | 无                                                                                                                                 | `KolActivityTimestampDiscrepancyChecker` 实例被创建，其 `run_check_cycle` 方法被调用一次。                                                                                                                                                           |          | 通过   |


</rewritten_file> 