# 回测模块V2任务清单

**生成时间**: 2025-06-05T10:26:12+08:00  
**版本**: 0.1.0  
**模块**: backtesting  
**文档类型**: 任务清单  
**负责人**: AI Assistant + 用户  

## 项目状态: [>] 5.A.6 代码实现与测试用例编写 - 进行中

## 任务进度总览

- [x] 5.A.1. 指令理解与模块定位
- [x] 5.A.2. 文档查阅与影响分析
- [x] 5.A.3. 详细阅读源代码
- [x] 5.A.4. 生成前置文档
- [x] 5.A.5. 请求人工审阅
- [ ] 5.A.6. 代码实现与测试用例编写
- [ ] 5.A.7. 自动化测试执行与结果反馈
- [ ] 5.A.8. 自我核查与最终确认

## 详细任务分解

### 5.A.4. 生成前置文档 - [x] 已完成

#### 5.A.4.1 详细需求规格 - [x] 已完成
- [x] 创建 `backtest_v2_requirements_ai.md`
- [x] 定义核心需求概述
- [x] 详细功能需求规格
- [x] 配置参数说明
- [x] 性能和兼容性要求
- [x] 验收标准

#### 5.A.4.2 技术实现方案 - [x] 已完成
- [x] 创建 `backtest_v2_dev_plan_ai.md`
- [x] 总体架构设计
- [x] 详细技术方案
- [x] 性能优化策略
- [x] 关键文件实现计划

#### 5.A.4.3 测试用例设计 - [x] 已完成
- [x] 创建 `backtest_v2_test_cases_ai.md`
- [x] 测试策略概述
- [x] 单元测试用例设计
- [x] 集成测试用例设计
- [x] 性能测试用例设计

### 5.A.5. 请求人工审阅 - [ ] 待用户确认

等待用户审阅以下文档：
- `docs/features/0.1.0/backtesting/backtest_v2_requirements_ai.md`
- `docs/features/0.1.0/backtesting/backtest_v2_dev_plan_ai.md`
- `docs/features/0.1.0/backtesting/backtest_v2_test_cases_ai.md`

**用户需要确认的重点**:
1. 技术架构方向是否正确？
2. MongoDB聚合查询的参数化方案是否满足需求？
3. 滑动窗口信号检测算法是否符合预期？
4. 性能要求和兼容性要求是否合理？

### 5.A.6. 代码实现与测试用例编写 - [ ] 等待审阅确认后开始

#### 5.A.6.1 创建模块结构 - [ ] 待开始
- [ ] 创建 `utils/backtest_v2/` 目录
- [ ] 创建 `utils/backtest_v2/__init__.py`
- [ ] 创建 `test/utils/backtest_v2/` 目录

#### 5.A.6.2 核心组件实现 - [ ] 待开始
  - [ ] 实现 `data_query.py` - MongoDB聚合查询组件
    - [ ] DataQuery类基础框架
    - [ ] 聚合管道参数化
    - [ ] 新代币记录过滤逻辑
    - [ ] KOL数量预筛选优化
    - [ ] 查询执行和结果验证
    - [ ] 错误处理和日志记录
  - [ ] 实现 `signal_analyzer.py` - 信号分析组件
    - [ ] SignalAnalyzer类基础框架（接收卖出数据缓存）
    - [ ] 滑动窗口算法实现（支持多信号生成）
    - [ ] 买入即卖出过滤功能
    - [ ] 信号时间戳计算
    - [ ] 信号间隔限制检查（支持同一token多次信号）
    - [ ] 并发分析支持
- [ ] 实现 `sell_strategy.py` - 卖出策略组件
  - [ ] SellStrategy类基础框架
  - [ ] 卖出数据预加载功能（MongoDB聚合查询）
  - [ ] KOL比例卖出策略（基于预加载数据）
  - [ ] 超时卖出策略
  - [ ] 卖出信号时间戳精确计算
- [ ] 实现 `backtest_engine.py` - 回测执行引擎
  - [ ] BacktestEngine类基础框架
  - [ ] 卖出数据预加载流程集成
  - [ ] 信号分析器动态初始化（传递卖出数据）
  - [ ] 主要流程控制（9步骤流程）
  - [ ] 组件集成和协调
  - [ ] 进度监控和错误处理

#### 5.A.6.3 辅助组件实现 - [ ] 待开始
- [ ] 实现 `result_analyzer.py` - 结果分析组件
  - [ ] 统计指标计算
  - [ ] 可视化图表生成
  - [ ] 结果导出功能
- [ ] 实现 `config_manager.py` - 配置管理组件
  - [ ] 配置加载和验证
  - [ ] 默认参数管理
  - [ ] 多格式支持
- [ ] 实现 `utils.py` - 工具函数
  - [ ] 时间处理工具
  - [ ] 数据格式转换
  - [ ] 通用计算函数

#### 5.A.6.4 测试用例编写 - [ ] 待开始
- [ ] `test_data_query.py` - 数据查询组件测试
  - [ ] MongoDB聚合管道构建测试
  - [ ] 查询执行和结果验证测试
  - [ ] 参数验证测试
- [ ] `test_signal_analyzer.py` - 信号分析组件测试
  - [ ] 滑动窗口算法测试
  - [ ] 买入即卖出过滤测试
  - [ ] 时间戳精度测试
  - [ ] 信号间隔限制测试
  - [ ] 边界条件测试
- [ ] `test_sell_strategy.py` - 卖出策略组件测试
  - [ ] 卖出数据预加载测试
  - [ ] KOL比例卖出策略测试
  - [ ] 超时卖出策略测试
  - [ ] 卖出时间戳精度测试
- [ ] `test_backtest_engine.py` - 回测引擎测试
  - [ ] 卖出数据预加载集成测试
  - [ ] 完整流程集成测试（9步骤）
  - [ ] 买入即卖出过滤集成测试
  - [ ] 错误处理测试
  - [ ] 性能基准测试
- [ ] `test_integration.py` - 端到端集成测试
  - [ ] 与现有模块对比测试
  - [ ] 参数网格搜索测试

#### 5.A.6.5 入口文件集成 - [ ] 待开始
- [ ] 修改 `run_backtest_ed.py` 支持V2模式
  - [ ] 添加V2模块导入
  - [ ] 扩展命令行参数解析（single_v2, grid_v2）
  - [ ] 实现统一配置加载函数（支持V1/V2）
  - [ ] 实现单次V2回测函数
  - [ ] 实现V2参数网格搜索函数
  - [ ] 修改主函数支持新模式
  - [ ] 添加V2特有参数验证
- [ ] 保持向后兼容性
  - [ ] 确保现有V1回测功能不受影响
  - [ ] 统一结果格式和目录命名规范
  - [ ] 复用现有报告生成和分析逻辑
- [ ] 配置兼容性测试
  - [ ] 测试V1/V2配置文件兼容性
  - [ ] 测试命令行参数覆盖功能
  - [ ] 测试参数验证逻辑

### 5.A.7. 自动化测试执行与结果反馈 - [ ] 待代码实现完成后开始

#### 5.A.7.1 单元测试执行 - [ ] 待开始
- [ ] 执行数据查询组件测试
- [ ] 执行信号分析组件测试
- [ ] 执行卖出策略组件测试
- [ ] 执行回测引擎测试
- [ ] 生成测试覆盖率报告

#### 5.A.7.2 集成测试执行 - [ ] 待开始
- [ ] 执行端到端集成测试
- [ ] 执行性能基准测试
- [ ] 执行与现有模块对比测试
- [ ] 执行入口文件集成测试
  - [ ] 测试V2模式命令行调用
  - [ ] 测试配置兼容性
  - [ ] 测试结果输出格式

#### 5.A.7.3 测试结果分析 - [ ] 待开始
- [ ] 分析测试失败原因
- [ ] 修复发现的问题
- [ ] 验证修复效果

### 5.A.8. 自我核查与最终确认 - [ ] 待测试通过后开始

#### 5.A.8.1 代码质量检查 - [ ] 待开始
- [ ] 代码风格检查（PEP 8）
- [ ] 文档字符串完整性检查
- [ ] 错误处理逻辑检查

#### 5.A.8.2 功能完整性验证 - [ ] 待开始
- [ ] 对照需求规格验证功能实现
- [ ] 对照技术方案验证架构设计
- [ ] 对照测试用例验证覆盖率

#### 5.A.8.3 性能验证 - [ ] 待开始
- [ ] 执行大数据量处理测试
- [ ] 验证内存使用情况
- [ ] 验证执行时间性能

## 里程碑进度

### 里程碑1: 文档完成 - [x] 已完成 (2025-06-05)
- [x] 需求规格文档
- [x] 技术实现方案
- [x] 测试用例设计

### 里程碑2: 用户审阅 - [ ] 进行中
- [ ] 用户审阅文档
- [ ] 确认技术方案
- [ ] 开始代码实现

### 里程碑3: 核心功能实现 - [ ] 待开始
- [ ] 数据查询组件
- [ ] 信号分析组件
- [ ] 卖出策略组件
- [ ] 回测执行引擎

### 里程碑4: 完整功能实现 - [ ] 待开始
- [ ] 辅助组件实现
- [ ] 测试用例实现
- [ ] 入口文件集成
- [ ] 功能集成测试

### 里程碑5: 项目完成 - [ ] 待开始
- [ ] 所有测试通过
- [ ] 性能验证完成
- [ ] 文档更新完成

## 风险和问题

### 已识别风险
1. **数据库性能风险**: MongoDB聚合查询可能在大数据量下性能较低
   - **缓解措施**: 优化查询条件，添加索引，分批处理
2. **时间戳精度风险**: 信号时间戳计算可能不够精确
   - **缓解措施**: 详细设计算法，充分测试边界条件
3. **兼容性风险**: 与现有回测模块的兼容性问题
   - **缓解措施**: 对比测试，保持配置格式一致

### 已解决问题 - 用户确认 (2025-06-05)
1. ✅ 是否需要支持实时价格数据获取？**是** - 参考现有版本实现
2. ✅ 是否需要与现有事件驱动回测结果进行对比验证？**否** - 不需要对比验证
3. ✅ 是否有特定的性能基准要求？**否** - 暂不考虑特定基准
4. ✅ 卖出策略是否需要扩展新的策略类型？**否** - 先实现买入，一个个来
5. ✅ **参数标准化完成** - 参数名称与现有回测模块保持一致
6. ✅ **新增买入信号限制** - 添加 `token_mint_lookback_hours` 和 `same_token_notification_interval_minutes`
7. ✅ **修正新代币过滤逻辑** - 从过滤整个代币改为过滤买入记录（按记录粒度而不是token粒度）
8. ✅ **添加KOL数量预筛选优化** - 在新代币记录过滤后立即检查唯一KOL数量，提前过滤无法产生信号的token
9. ✅ **明确多信号生成逻辑** - 重复信号抑制允许满足间隔要求的多次信号，滑动窗口算法应支持同一token的多个信号生成
10. ✅ **详细卖出策略设计** - 预查询卖出数据，基于KOL比例和超时两种策略，包含完整的MongoDB聚合查询方案
11. ✅ **新增买入即卖出过滤** - 在生成买入信号时检查同一时间窗口内KOL卖出比例，过滤低质量信号

## 下一步行动

**当前状态**: 等待用户审阅文档

**下一步行动**: 
1. 用户审阅 `backtest_v2_requirements_ai.md`、`backtest_v2_dev_plan_ai.md` 和 `backtest_v2_test_cases_ai.md`
2. 用户确认技术方案和需求规格
3. 开始代码实现阶段

**预估完成时间**: 
- 如果文档审阅通过：8-12小时完成代码实现
- 总体项目完成时间：1-2天

---

**更新日志**:
- 2025-06-05 10:26 - 创建任务清单，完成前置文档生成
- 待更新 - 用户审阅结果
- 待更新 - 代码实现进度 