from dotenv import load_dotenv
from fastapi import FastAPI
import uvicorn

# 导入 models 包中的 init_db 函数
from models import init_db 

load_dotenv()

# 导入新创建的 API 路由
from api.v1 import signal_api

app = FastAPI(title="MemeMonitor API", version="0.1.0")

@app.on_event("startup")
async def startup_event():
    print("Starting up application...")
    await init_db() # 初始化数据库连接和 Beanie

# 包含 Signal 路由
app.include_router(
    signal_api.router, 
    prefix="/api/v1", 
    tags=["Signals"]
)


if __name__ == "__main__":
    # 运行 FastAPI 应用
    # 在开发环境中，通常使用 uvicorn main:app --reload 启动
    print("Starting Uvicorn server...")
    uvicorn.run(app, host="0.0.0.0", port=8000)
