# 交易记录实际金额定期更新功能需求规格

## 1. 功能概述

### 1.1 功能描述
实现一个定期工作流，自动更新交易记录中的实际输出金额（`token_out_actual_amount`），通过解析链上交易数据获取真实的交易结果。

### 1.2 业务背景
- 当前交易记录中的`token_out_actual_amount`字段可能为空或不准确
- 需要通过链上数据验证和更新实际交易金额
- 提高交易数据的准确性和完整性

### 1.3 目标用户
- 系统管理员
- 交易数据分析人员
- 自动交易系统

## 2. 功能需求

### 2.1 核心功能

#### 2.1.1 定期扫描机制
- **FR-001**: 系统应每10秒扫描一次需要更新的交易记录
- **FR-002**: 扫描范围为最近24小时内的交易记录
- **FR-003**: 优先处理`token_out_verified_amount`为空的记录

#### 2.1.2 交易数据解析
- **FR-004**: 系统应能解析Solana交易数据获取实际输出金额
- **FR-005**: 支持SOL和SPL代币的金额计算
- **FR-006**: 验证交易成功状态后再更新金额

#### 2.1.3 数据更新
- **FR-007**: 新增并更新`TradeRecord.token_out_verified_amount`字段（链上验证的实际金额）
- **FR-008**: 新增并更新`TradeRecord.token_out_verified_at`字段（验证时间戳）
- **FR-009**: 新增并更新`TradeRecord.verification_status`字段（验证状态）
- **FR-010**: 保持原有`token_out_actual_amount`字段不变
- **FR-011**: 批量更新以提高性能

### 2.2 非功能需求

#### 2.2.1 性能要求
- **NFR-001**: 单次扫描处理时间不超过8秒（适应10秒间隔）
- **NFR-002**: 支持并发处理多个交易记录
- **NFR-003**: 内存使用不超过100MB

#### 2.2.2 可靠性要求
- **NFR-004**: 系统异常时应能自动恢复
- **NFR-005**: 提供详细的错误日志
- **NFR-006**: 支持手动重试机制

#### 2.2.3 监控要求
- **NFR-007**: 记录每次扫描的处理统计
- **NFR-008**: 监控更新成功率
- **NFR-009**: 异常情况下发送告警

## 3. 数据模型扩展

### 3.1 TradeRecord模型新增字段

#### 3.1.1 新增字段定义
```python
# 在TradeRecord模型中新增以下字段
token_out_verified_amount: Optional[float] = Field(default=None, description="链上验证的实际输出金额")
token_out_verified_at: Optional[datetime] = Field(default=None, description="金额验证时间戳")
verification_status: Optional[str] = Field(default=VerificationStatus.PENDING, description="验证状态")
```

#### 3.1.2 验证状态枚举
```python
class VerificationStatus(str, Enum):
    PENDING = "pending"          # 待验证
    VERIFIED = "verified"        # 已验证
    FAILED = "failed"            # 验证失败
    SKIPPED = "skipped"          # 跳过验证（如交易失败）
```

#### 3.1.3 数据库索引
- 新增`verification_status`索引
- 新增`token_out_verified_at`索引
- 复合索引：`(verification_status, created_at)`

### 3.2 字段使用说明
- `token_out_verified_amount`: 存储从链上解析的真实输出金额
- `token_out_verified_at`: 记录验证完成的时间
- `verification_status`: 跟踪验证状态，便于查询和监控
- 保持`token_out_actual_amount`字段不变，确保向后兼容

## 4. 技术约束

### 4.1 系统约束
- 基于现有的workflow框架实现
- 使用现有的`get_confirmed_token_output_from_tx`方法
- 遵循项目的数据库访问模式
- 扩展现有TradeRecord模型而非创建新表

### 4.2 数据约束
- 只更新有效的交易记录
- 保持数据一致性
- 不修改历史审计数据
- 新字段设计确保向后兼容性

## 5. 接口规范

### 5.1 配置接口
```yaml
# trade_record_actual_amount_updater_workflow.yaml
name: trade_record_actual_amount_updater
description: '定期验证交易记录的实际输出金额'
concurrency: 1

nodes:
  - name: "TradeRecordScannerNode"
    node_type: "input"
    interval: 10  # 10秒扫描间隔
    concurrency: 1
    generate_data: workflows.trading.trade_record_verification.handler.scan_pending_records
    
  - name: "AmountVerificationNode"
    node_type: "process"
    depend_ons: ["TradeRecordScannerNode"]
    interval: 1
    concurrency: 2
    process_item: workflows.trading.trade_record_verification.handler.verify_amount
    
  - name: "RecordUpdateNode"
    node_type: "storage"
    depend_ons: ["AmountVerificationNode"]
    batch_size: 50  # 批处理大小
    concurrency: 1
    store_data: workflows.trading.trade_record_verification.handler.update_records
```

### 5.2 数据库接口
- 使用`TradeRecordDao`进行数据访问
- 调用`SolanaMonitor.get_confirmed_token_output_from_tx`解析交易

## 6. 验收标准

### 6.1 功能验收
- [ ] 工作流能够正常启动和运行
- [ ] 能够正确识别需要更新的交易记录
- [ ] 能够准确解析链上交易数据
- [ ] 能够正确更新数据库中的金额字段

### 6.2 性能验收
- [ ] 处理50条记录的时间不超过8秒
- [ ] 内存使用稳定在100MB以内
- [ ] 错误率低于1%

### 6.3 监控验收
- [ ] 提供完整的运行日志
- [ ] 统计信息准确
- [ ] 异常告警及时

## 7. 风险评估

### 7.1 技术风险
- **风险**: 链上数据解析失败
- **缓解**: 增加重试机制和错误处理

### 7.2 性能风险
- **风险**: 大量数据处理导致系统负载过高
- **缓解**: 实施批处理和限流机制

### 7.3 数据风险
- **风险**: 错误更新导致数据不一致
- **缓解**: 增加数据验证和回滚机制

## 8. 实施计划

### 8.1 开发阶段
1. 创建工作流配置文件
2. 实现核心处理逻辑
3. 集成现有组件
4. 添加监控和日志

### 8.2 测试阶段
1. 单元测试
2. 集成测试
3. 性能测试
4. 用户验收测试

### 8.3 部署阶段
1. 测试环境部署
2. 生产环境部署
3. 监控配置
4. 文档更新