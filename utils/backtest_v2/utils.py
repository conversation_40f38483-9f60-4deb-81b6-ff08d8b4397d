"""工具函数 - 回测模块V2

提供时间处理、数据格式转换、通用计算等工具函数。
"""

import logging
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timezone
import pandas as pd
import numpy as np

logger = logging.getLogger("UtilsV2")


def timestamp_to_datetime(timestamp: Union[int, float]) -> datetime:
    """将时间戳转换为datetime对象
    
    Args:
        timestamp: Unix时间戳
        
    Returns:
        datetime: datetime对象
    """
    return datetime.fromtimestamp(timestamp, tz=timezone.utc)


def datetime_to_timestamp(dt: datetime) -> int:
    """将datetime对象转换为时间戳
    
    Args:
        dt: datetime对象
        
    Returns:
        int: Unix时间戳
    """
    return int(dt.timestamp())


def format_duration(seconds: Union[int, float]) -> str:
    """格式化时间长度
    
    Args:
        seconds: 秒数
        
    Returns:
        str: 格式化的时间字符串
    """
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        return f"{seconds/60:.1f}分钟"
    elif seconds < 86400:
        return f"{seconds/3600:.1f}小时"
    else:
        return f"{seconds/86400:.1f}天"


def format_percentage(value: Union[int, float], decimals: int = 2) -> str:
    """格式化百分比
    
    Args:
        value: 数值（0.1表示10%）
        decimals: 小数位数
        
    Returns:
        str: 格式化的百分比字符串
    """
    return f"{value * 100:.{decimals}f}%"


def format_currency(value: Union[int, float], currency: str = "USD") -> str:
    """格式化货币
    
    Args:
        value: 数值
        currency: 货币符号
        
    Returns:
        str: 格式化的货币字符串
    """
    if abs(value) >= 1000000:
        return f"{value/1000000:.2f}M {currency}"
    elif abs(value) >= 1000:
        return f"{value/1000:.2f}K {currency}"
    else:
        return f"{value:.2f} {currency}"


def safe_divide(numerator: Union[int, float], denominator: Union[int, float], 
                default: Union[int, float] = 0) -> Union[int, float]:
    """安全除法，避免除零错误
    
    Args:
        numerator: 分子
        denominator: 分母
        default: 除零时的默认值
        
    Returns:
        Union[int, float]: 除法结果
    """
    if denominator == 0:
        return default
    return numerator / denominator


def calculate_compound_return(returns: List[Union[int, float]]) -> float:
    """计算复合收益率
    
    Args:
        returns: 收益率列表
        
    Returns:
        float: 复合收益率
    """
    if not returns:
        return 0.0
    
    compound = 1.0
    for r in returns:
        compound *= (1 + r)
    
    return compound - 1.0


def calculate_max_drawdown(equity_curve: List[Union[int, float]]) -> float:
    """计算最大回撤
    
    Args:
        equity_curve: 资金曲线
        
    Returns:
        float: 最大回撤（负数）
    """
    if not equity_curve:
        return 0.0
    
    equity_series = pd.Series(equity_curve)
    running_max = equity_series.expanding().max()
    drawdown = (equity_series - running_max) / running_max
    
    return float(drawdown.min())


def calculate_sharpe_ratio(returns: List[Union[int, float]], 
                          risk_free_rate: float = 0.0) -> float:
    """计算夏普比率
    
    Args:
        returns: 收益率列表
        risk_free_rate: 无风险利率
        
    Returns:
        float: 夏普比率
    """
    if not returns:
        return 0.0
    
    returns_array = np.array(returns)
    excess_returns = returns_array - risk_free_rate
    
    if np.std(excess_returns) == 0:
        return 0.0
    
    return float(np.mean(excess_returns) / np.std(excess_returns))


def validate_config_parameters(config: Dict[str, Any]) -> List[str]:
    """验证配置参数
    
    Args:
        config: 配置字典
        
    Returns:
        List[str]: 错误信息列表，空列表表示验证通过
    """
    errors = []
    
    # 检查必需参数
    required_params = [
        'backtest_start_time', 'backtest_end_time', 'transaction_min_amount',
        'kol_account_min_count', 'transaction_lookback_hours', 'sell_strategy_hours'
    ]
    
    for param in required_params:
        if param not in config:
            errors.append(f"缺少必需参数: {param}")
    
    # 检查参数范围
    if 'backtest_start_time' in config and 'backtest_end_time' in config:
        if config['backtest_start_time'] >= config['backtest_end_time']:
            errors.append("回测开始时间必须小于结束时间")
    
    if 'transaction_min_amount' in config and config['transaction_min_amount'] <= 0:
        errors.append("最小交易金额必须大于0")
    
    if 'kol_account_min_count' in config and config['kol_account_min_count'] <= 0:
        errors.append("最小KOL账号数量必须大于0")
    
    if 'sell_kol_ratio' in config:
        ratio = config['sell_kol_ratio']
        if not (0 < ratio <= 1):
            errors.append("卖出KOL比例阈值必须在(0, 1]范围内")
    
    return errors


def merge_token_data(data1: Dict[str, Any], data2: Dict[str, Any]) -> Dict[str, Any]:
    """合并两个token数据字典
    
    Args:
        data1: 第一个数据字典
        data2: 第二个数据字典
        
    Returns:
        Dict[str, Any]: 合并后的数据字典
    """
    merged = data1.copy()
    
    for token_address, token_data in data2.items():
        if token_address in merged:
            # 合并记录
            merged[token_address]['records'].extend(token_data['records'])
            # 更新KOL信息
            if 'kol_wallets' in token_data:
                merged[token_address]['kol_wallets'] = token_data['kol_wallets']
            if 'kol_wallets_count' in token_data:
                merged[token_address]['kol_wallets_count'] = token_data['kol_wallets_count']
        else:
            merged[token_address] = token_data
    
    return merged


def filter_records_by_time(records: List[Dict[str, Any]], 
                          start_time: int, end_time: int) -> List[Dict[str, Any]]:
    """按时间范围过滤记录
    
    Args:
        records: 记录列表
        start_time: 开始时间戳
        end_time: 结束时间戳
        
    Returns:
        List[Dict[str, Any]]: 过滤后的记录列表
    """
    filtered = []
    for record in records:
        timestamp = record.get('timestamp', 0)
        if start_time <= timestamp <= end_time:
            filtered.append(record)
    
    return filtered


def group_records_by_wallet(records: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
    """按钱包地址分组记录
    
    Args:
        records: 记录列表
        
    Returns:
        Dict[str, List[Dict[str, Any]]]: 按钱包地址分组的记录
    """
    grouped = {}
    for record in records:
        wallet = record.get('wallet', '')
        if wallet not in grouped:
            grouped[wallet] = []
        grouped[wallet].append(record)
    
    return grouped


def calculate_statistics_summary(trades: List[Dict[str, Any]]) -> Dict[str, Any]:
    """计算交易统计摘要
    
    Args:
        trades: 交易列表
        
    Returns:
        Dict[str, Any]: 统计摘要
    """
    if not trades:
        return {
            'total_trades': 0,
            'win_rate': 0.0,
            'avg_return': 0.0,
            'total_return': 0.0
        }
    
    df = pd.DataFrame(trades)
    
    total_trades = len(trades)
    winning_trades = len(df[df['return_rate'] > 0])
    win_rate = winning_trades / total_trades
    avg_return = df['return_rate'].mean()
    total_return = calculate_compound_return(df['return_rate'].tolist())
    
    return {
        'total_trades': total_trades,
        'winning_trades': winning_trades,
        'win_rate': win_rate,
        'avg_return': avg_return,
        'total_return': total_return
    }
