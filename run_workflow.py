import asyncio
import logging
import sys
import os
import signal
import argparse
import glob
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 设置日志
logging.basicConfig(
    level=os.getenv('LOG_LEVEL', 'DEBUG'),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 从环境变量设置第三方库的日志级别
pymongo_level = os.getenv('PYMONGO_LOG_LEVEL', 'WARNING')
beanie_level = os.getenv('BEANIE_LOG_LEVEL', 'WARNING')
motor_level = os.getenv('MOTOR_LOG_LEVEL', 'WARNING')
redis_level = os.getenv('REDIS_LOG_LEVEL', 'WARNING')

# 设置各个库的日志级别
logging.getLogger("pymongo").setLevel(getattr(logging, pymongo_level))
logging.getLogger("beanie").setLevel(getattr(logging, beanie_level))
logging.getLogger("motor").setLevel(getattr(logging, motor_level))
logging.getLogger("redis").setLevel(getattr(logging, redis_level))

# 确保当前目录在Python路径中
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 导入数据库初始化函数
from models import init_db

# 导入工作流
from utils.workflows.workflow import workflow_manager
from utils.workflows.workflow_config import create_workflow_from_yaml_file

# 全局变量用于存储工作流管理器
global_workflow_manager = None

def signal_handler(signum, frame):
    """处理中断信号"""
    print("\n收到中断信号，正在优雅停止...")
    if global_workflow_manager:
        global_workflow_manager.stop_all()

async def init_database():
    """初始化数据库连接和集合"""
    # 初始化 MongoDB 和 Beanie
    await init_db()

def find_yaml_files(directory=None):
    """查找指定目录下的YAML工作流配置文件
    
    Args:
        directory: 要搜索的目录，如果为None则使用默认目录
        
    Returns:
        List[str]: YAML文件路径列表
    """
    yaml_files = []
    
    if directory is None:
        # 搜索多个目录
        search_dirs = [
            os.path.join(os.path.dirname(__file__), 'utils', 'workflows'),
            os.path.join(os.path.dirname(__file__), 'workflows')
        ]
        
        for search_dir in search_dirs:
            if os.path.exists(search_dir):
                # 查找当前目录下的YAML文件
                yaml_files.extend(glob.glob(os.path.join(search_dir, '*.yaml')))
                yaml_files.extend(glob.glob(os.path.join(search_dir, '*.yml')))
                
                # 查找子目录下的YAML文件
                for subdir in os.listdir(search_dir):
                    subdir_path = os.path.join(search_dir, subdir)
                    if os.path.isdir(subdir_path):
                        yaml_files.extend(glob.glob(os.path.join(subdir_path, '*.yaml')))
                        yaml_files.extend(glob.glob(os.path.join(subdir_path, '*.yml')))
    else:
        # 确保目录存在
        if not os.path.exists(directory):
            logging.error(f"目录不存在: {directory}")
            return []
        
        # 查找所有.yaml和.yml文件
        yaml_files = glob.glob(os.path.join(directory, '*.yaml'))
        yaml_files.extend(glob.glob(os.path.join(directory, '*.yml')))
        
        # 查找子目录下的YAML文件
        for subdir in os.listdir(directory):
            subdir_path = os.path.join(directory, subdir)
            if os.path.isdir(subdir_path):
                yaml_files.extend(glob.glob(os.path.join(subdir_path, '*.yaml')))
                yaml_files.extend(glob.glob(os.path.join(subdir_path, '*.yml')))
    
    logging.info(f"找到 {len(yaml_files)} 个YAML配置文件")
    for file in yaml_files:
        logging.info(f"  - {file}")
    
    return yaml_files

async def load_workflow_from_yaml(yaml_file):
    """从YAML文件加载工作流
    
    Args:
        yaml_file: YAML文件路径
        
    Returns:
        Workflow: 加载的工作流，如果加载失败则返回None
    """
    try:
        logging.info(f"正在加载工作流配置: {yaml_file}")
        workflow = create_workflow_from_yaml_file(yaml_file)
        logging.info(f"成功加载工作流: {workflow.name}")
        return workflow
    except Exception as e:
        logging.error(f"加载工作流配置失败: {yaml_file}")
        logging.error(f"错误: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
        return None

async def main():
    global global_workflow_manager
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="运行YAML配置的工作流")
    parser.add_argument("--dir", nargs="+", help="YAML配置文件目录，可指定多个目录")
    parser.add_argument("--file", help="指定的YAML配置文件")
    args = parser.parse_args()
    
    try:
        # 初始化数据库
        logging.info("正在初始化数据库...")
        await init_database()
        
        # 查找YAML文件
        yaml_files = []
        if args.file:
            if os.path.exists(args.file):
                yaml_files = [args.file]
            else:
                logging.error(f"指定的YAML文件不存在: {args.file}")
                return
        else:
            # 处理多个目录的情况
            yaml_files = []
            if args.dir:
                for directory in args.dir:
                    yaml_files.extend(find_yaml_files(directory))
            else:
                yaml_files = find_yaml_files()
        
        if not yaml_files:
            logging.error("没有找到YAML配置文件")
            return
        
        # 加载工作流
        workflows = []
        for yaml_file in yaml_files:
            workflow = await load_workflow_from_yaml(yaml_file)
            if workflow:
                workflows.append(workflow)
        
        if not workflows:
            logging.error("没有成功加载任何工作流")
            return
        
        # 注册工作流
        for workflow in workflows:
            workflow_manager.register_workflow(workflow)
            logging.info(f"已注册工作流: {workflow.name}")
        
        # 设置全局工作流管理器
        global_workflow_manager = workflow_manager
        
        # 运行工作流管理器
        logging.info(f"开始运行 {len(workflows)} 个工作流")
        await workflow_manager.run()
        
    except Exception as e:
        logging.error(f"程序运行出错: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
        raise

if __name__ == "__main__":
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logging.info("程序已停止")
    except Exception as e:
        logging.error(f"程序运行出错: {str(e)}")
        raise