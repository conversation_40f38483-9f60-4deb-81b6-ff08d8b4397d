"""
图表生成器

使用Plotly生成交互式图表，包括总体统计、分Token统计、分策略统计、盈利亏损分布等图表。
"""

import logging
from typing import List, Dict, Any, Optional
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd

from .models import (
    StatisticsResult, OverallStats, TokenStats, StrategyStats,
    ProfitRanking, LossRanking, TradePair
)


class ChartGenerator:
    """
    图表生成器
    
    使用Plotly生成各种交互式图表。
    """
    
    def __init__(self):
        """初始化图表生成器"""
        self.logger = logging.getLogger(__name__)
        
        # 图表配置
        self.color_palette = {
            'profit': '#2E8B57',      # 海绿色
            'loss': '#DC143C',        # 深红色
            'neutral': '#4682B4',     # 钢蓝色
            'background': '#F8F9FA',  # 浅灰色背景
            'text': '#2C3E50'         # 深蓝灰色文字
        }
        
        self.chart_config = {
            'displayModeBar': True,
            'displaylogo': False,
            'modeBarButtonsToRemove': ['pan2d', 'lasso2d', 'select2d']
        }
    
    def generate_all_charts(self, statistics: StatisticsResult) -> Dict[str, str]:
        """
        生成所有图表
        
        Args:
            statistics: 统计结果
            
        Returns:
            图表HTML字符串字典，键为图表名称
        """
        try:
            self.logger.info("开始生成所有图表")
            
            charts = {}
            
            # 总体统计图表
            charts['overall_pie'] = self.generate_overall_pie_chart(statistics.overall_stats)
            charts['overall_bar'] = self.generate_overall_bar_chart(statistics.overall_stats)
            
            # 分Token统计图表
            if statistics.token_stats:
                charts['token_performance'] = self.generate_token_performance_chart(statistics.token_stats)
                charts['token_win_rate'] = self.generate_token_win_rate_chart(statistics.token_stats)
            
            # 分策略统计图表
            if statistics.strategy_stats:
                charts['strategy_performance'] = self.generate_strategy_performance_chart(statistics.strategy_stats)
                charts['strategy_comparison'] = self.generate_strategy_comparison_chart(statistics.strategy_stats)
            
            # 盈利亏损分布图表
            if statistics.profit_rankings or statistics.loss_rankings:
                charts['profit_loss_distribution'] = self.generate_profit_loss_distribution_chart(
                    statistics.profit_rankings, statistics.loss_rankings
                )
            
            # 排行榜图表
            if statistics.profit_rankings:
                charts['top_profits'] = self.generate_top_profits_chart(statistics.profit_rankings[:10])
            
            if statistics.loss_rankings:
                charts['top_losses'] = self.generate_top_losses_chart(statistics.loss_rankings[:10])
            
            self.logger.info(f"图表生成完成，共生成 {len(charts)} 个图表")
            return charts
            
        except Exception as e:
            self.logger.error(f"生成图表失败: {str(e)}")
            raise
    
    def generate_overall_pie_chart(self, overall_stats: OverallStats) -> str:
        """
        生成总体统计饼图（盈利vs亏损交易数）
        
        Args:
            overall_stats: 总体统计数据
            
        Returns:
            图表HTML字符串
        """
        try:
            if overall_stats.total_trades == 0:
                return self._create_empty_chart("暂无交易数据")
            
            win_count = int(overall_stats.total_trades * overall_stats.total_win_rate / 100)
            loss_count = overall_stats.total_trades - win_count
            
            fig = go.Figure(data=[go.Pie(
                labels=['盈利交易', '亏损交易'],
                values=[win_count, loss_count],
                hole=0.4,
                marker_colors=[self.color_palette['profit'], self.color_palette['loss']],
                textinfo='label+percent+value',
                textfont_size=12
            )])
            
            fig.update_layout(
                title={
                    'text': f'交易盈亏分布 (总计: {overall_stats.total_trades} 笔)',
                    'x': 0.5,
                    'font': {'size': 16, 'color': self.color_palette['text']}
                },
                font={'family': 'Arial, sans-serif'},
                plot_bgcolor=self.color_palette['background'],
                paper_bgcolor='white',
                margin=dict(t=60, b=40, l=40, r=40)
            )
            
            return fig.to_html(config=self.chart_config, div_id="overall_pie_chart")
            
        except Exception as e:
            self.logger.error(f"生成总体饼图失败: {str(e)}")
            return self._create_error_chart("生成总体饼图失败")
    
    def generate_overall_bar_chart(self, overall_stats: OverallStats) -> str:
        """
        生成总体统计柱状图
        
        Args:
            overall_stats: 总体统计数据
            
        Returns:
            图表HTML字符串
        """
        try:
            if overall_stats.total_trades == 0:
                return self._create_empty_chart("暂无交易数据")
            
            metrics = ['胜率 (%)', '总盈利率 (%)', '平均盈利率 (%)']
            values = [
                overall_stats.total_win_rate,
                overall_stats.total_profit_rate,
                overall_stats.avg_profit_rate
            ]
            
            colors = [
                self.color_palette['profit'] if v >= 0 else self.color_palette['loss'] 
                for v in values
            ]
            
            fig = go.Figure(data=[go.Bar(
                x=metrics,
                y=values,
                marker_color=colors,
                text=[f'{v:.2f}%' for v in values],
                textposition='auto',
                textfont={'size': 12}
            )])
            
            fig.update_layout(
                title={
                    'text': '总体统计指标',
                    'x': 0.5,
                    'font': {'size': 16, 'color': self.color_palette['text']}
                },
                xaxis_title='指标',
                yaxis_title='数值 (%)',
                font={'family': 'Arial, sans-serif'},
                plot_bgcolor=self.color_palette['background'],
                paper_bgcolor='white',
                margin=dict(t=60, b=60, l=60, r=40)
            )
            
            return fig.to_html(config=self.chart_config, div_id="overall_bar_chart")
            
        except Exception as e:
            self.logger.error(f"生成总体柱状图失败: {str(e)}")
            return self._create_error_chart("生成总体柱状图失败")
    
    def generate_token_performance_chart(self, token_stats: List[TokenStats]) -> str:
        """
        生成Token表现图表
        
        Args:
            token_stats: Token统计数据列表
            
        Returns:
            图表HTML字符串
        """
        try:
            if not token_stats:
                return self._create_empty_chart("暂无Token数据")
            
            # 取前10个表现最好的Token
            top_tokens = token_stats[:10]
            
            token_names = [f"Token_{i+1}" for i in range(len(top_tokens))]
            profit_rates = [stats.avg_profit_rate for stats in top_tokens]
            trade_counts = [stats.trade_count for stats in top_tokens]
            
            # 创建双轴图表
            fig = make_subplots(specs=[[{"secondary_y": True}]])
            
            # 添加盈利率柱状图
            fig.add_trace(
                go.Bar(
                    x=token_names,
                    y=profit_rates,
                    name='总盈利率 (%)',
                    marker_color=[
                        self.color_palette['profit'] if rate >= 0 else self.color_palette['loss']
                        for rate in profit_rates
                    ],
                    text=[f'{rate:.2f}%' for rate in profit_rates],
                    textposition='auto'
                ),
                secondary_y=False
            )
            
            # 添加交易数量线图
            fig.add_trace(
                go.Scatter(
                    x=token_names,
                    y=trade_counts,
                    mode='lines+markers',
                    name='交易数量',
                    line=dict(color=self.color_palette['neutral'], width=3),
                    marker=dict(size=8)
                ),
                secondary_y=True
            )
            
            # 设置轴标题
            fig.update_xaxes(title_text="Token")
            fig.update_yaxes(title_text="总盈利率 (%)", secondary_y=False)
            fig.update_yaxes(title_text="交易数量", secondary_y=True)
            
            fig.update_layout(
                title={
                    'text': 'Token表现排行榜 (Top 10)',
                    'x': 0.5,
                    'font': {'size': 16, 'color': self.color_palette['text']}
                },
                font={'family': 'Arial, sans-serif'},
                plot_bgcolor=self.color_palette['background'],
                paper_bgcolor='white',
                margin=dict(t=60, b=60, l=60, r=60),
                legend=dict(x=0.02, y=0.98)
            )
            
            return fig.to_html(config=self.chart_config, div_id="token_performance_chart")
            
        except Exception as e:
            self.logger.error(f"生成Token表现图表失败: {str(e)}")
            return self._create_error_chart("生成Token表现图表失败")
    
    def generate_token_win_rate_chart(self, token_stats: List[TokenStats]) -> str:
        """
        生成Token胜率图表
        
        Args:
            token_stats: Token统计数据列表
            
        Returns:
            图表HTML字符串
        """
        try:
            if not token_stats:
                return self._create_empty_chart("暂无Token数据")
            
            # 按胜率排序，取前15个
            sorted_tokens = sorted(token_stats, key=lambda x: x.win_rate, reverse=True)[:15]
            
            token_names = [f"Token_{i+1}" for i in range(len(sorted_tokens))]
            win_rates = [stats.win_rate for stats in sorted_tokens]
            
            fig = go.Figure(data=[go.Bar(
                x=token_names,
                y=win_rates,
                marker_color=self.color_palette['profit'],
                text=[f'{rate:.1f}%' for rate in win_rates],
                textposition='auto',
                textfont={'size': 10}
            )])
            
            fig.update_layout(
                title={
                    'text': 'Token胜率排行榜 (Top 15)',
                    'x': 0.5,
                    'font': {'size': 16, 'color': self.color_palette['text']}
                },
                xaxis_title='Token',
                yaxis_title='胜率 (%)',
                font={'family': 'Arial, sans-serif'},
                plot_bgcolor=self.color_palette['background'],
                paper_bgcolor='white',
                margin=dict(t=60, b=60, l=60, r=40)
            )
            
            return fig.to_html(config=self.chart_config, div_id="token_win_rate_chart")
            
        except Exception as e:
            self.logger.error(f"生成Token胜率图表失败: {str(e)}")
            return self._create_error_chart("生成Token胜率图表失败")
    
    def generate_strategy_performance_chart(self, strategy_stats: List[StrategyStats]) -> str:
        """
        生成策略表现图表
        
        Args:
            strategy_stats: 策略统计数据列表
            
        Returns:
            图表HTML字符串
        """
        try:
            if not strategy_stats:
                return self._create_empty_chart("暂无策略数据")
            
            strategy_names = [stats.strategy_name for stats in strategy_stats]
            profit_rates = [stats.avg_profit_rate for stats in strategy_stats]
            win_rates = [stats.win_rate for stats in strategy_stats]
            
            fig = make_subplots(specs=[[{"secondary_y": True}]])
            
            # 添加总盈利率柱状图
            fig.add_trace(
                go.Bar(
                    x=strategy_names,
                    y=profit_rates,
                    name='总盈利率 (%)',
                    marker_color=[
                        self.color_palette['profit'] if rate >= 0 else self.color_palette['loss']
                        for rate in profit_rates
                    ],
                    text=[f'{rate:.2f}%' for rate in profit_rates],
                    textposition='auto'
                ),
                secondary_y=False
            )
            
            # 添加胜率线图
            fig.add_trace(
                go.Scatter(
                    x=strategy_names,
                    y=win_rates,
                    mode='lines+markers',
                    name='胜率 (%)',
                    line=dict(color=self.color_palette['neutral'], width=3),
                    marker=dict(size=8)
                ),
                secondary_y=True
            )
            
            # 设置轴标题
            fig.update_xaxes(title_text="策略")
            fig.update_yaxes(title_text="总盈利率 (%)", secondary_y=False)
            fig.update_yaxes(title_text="胜率 (%)", secondary_y=True)
            
            fig.update_layout(
                title={
                    'text': '策略表现对比',
                    'x': 0.5,
                    'font': {'size': 16, 'color': self.color_palette['text']}
                },
                font={'family': 'Arial, sans-serif'},
                plot_bgcolor=self.color_palette['background'],
                paper_bgcolor='white',
                margin=dict(t=60, b=80, l=60, r=60),
                legend=dict(x=0.02, y=0.98)
            )
            
            return fig.to_html(config=self.chart_config, div_id="strategy_performance_chart")
            
        except Exception as e:
            self.logger.error(f"生成策略表现图表失败: {str(e)}")
            return self._create_error_chart("生成策略表现图表失败")
    
    def generate_strategy_comparison_chart(self, strategy_stats: List[StrategyStats]) -> str:
        """
        生成策略对比雷达图
        
        Args:
            strategy_stats: 策略统计数据列表
            
        Returns:
            图表HTML字符串
        """
        try:
            if not strategy_stats:
                return self._create_empty_chart("暂无策略数据")
            
            fig = go.Figure()
            
            # 为每个策略添加雷达图
            for i, stats in enumerate(strategy_stats[:5]):  # 最多显示5个策略
                # 标准化数据到0-100范围
                normalized_profit = min(max((stats.avg_profit_rate + 100) / 2, 0), 100)
                normalized_max_profit = min(max(stats.max_single_profit, 0), 100)
                normalized_max_loss = min(max(100 + stats.max_single_loss, 0), 100)  # max_single_loss是负数
                
                fig.add_trace(go.Scatterpolar(
                    r=[stats.win_rate, normalized_profit, normalized_max_profit, normalized_max_loss],
                    theta=['胜率', '总盈利率', '最大盈利', '最大亏损控制'],
                    fill='toself',
                    name=stats.strategy_name,
                    line_color=px.colors.qualitative.Set1[i % len(px.colors.qualitative.Set1)]
                ))
            
            fig.update_layout(
                polar=dict(
                    radialaxis=dict(
                        visible=True,
                        range=[0, 100]
                    )
                ),
                title={
                    'text': '策略综合对比雷达图',
                    'x': 0.5,
                    'font': {'size': 16, 'color': self.color_palette['text']}
                },
                font={'family': 'Arial, sans-serif'},
                paper_bgcolor='white',
                margin=dict(t=60, b=40, l=40, r=40)
            )
            
            return fig.to_html(config=self.chart_config, div_id="strategy_comparison_chart")
            
        except Exception as e:
            self.logger.error(f"生成策略对比图表失败: {str(e)}")
            return self._create_error_chart("生成策略对比图表失败")
    
    def generate_profit_loss_distribution_chart(self, profit_rankings: List[ProfitRanking], 
                                              loss_rankings: List[LossRanking]) -> str:
        """
        生成盈利亏损分布图表
        
        Args:
            profit_rankings: 盈利排行榜
            loss_rankings: 亏损排行榜
            
        Returns:
            图表HTML字符串
        """
        try:
            fig = make_subplots(
                rows=1, cols=2,
                subplot_titles=('盈利分布', '亏损分布'),
                specs=[[{"type": "histogram"}, {"type": "histogram"}]]
            )
            
            # 盈利分布直方图
            if profit_rankings:
                profit_amounts = [ranking.profit_amount for ranking in profit_rankings]
                fig.add_trace(
                    go.Histogram(
                        x=profit_amounts,
                        nbinsx=20,
                        marker_color=self.color_palette['profit'],
                        opacity=0.7,
                        name='盈利分布'
                    ),
                    row=1, col=1
                )
            
            # 亏损分布直方图
            if loss_rankings:
                loss_amounts = [ranking.loss_amount for ranking in loss_rankings]
                fig.add_trace(
                    go.Histogram(
                        x=loss_amounts,
                        nbinsx=20,
                        marker_color=self.color_palette['loss'],
                        opacity=0.7,
                        name='亏损分布'
                    ),
                    row=1, col=2
                )
            
            fig.update_layout(
                title={
                    'text': '盈利亏损分布',
                    'x': 0.5,
                    'font': {'size': 16, 'color': self.color_palette['text']}
                },
                font={'family': 'Arial, sans-serif'},
                plot_bgcolor=self.color_palette['background'],
                paper_bgcolor='white',
                margin=dict(t=80, b=60, l=60, r=60),
                showlegend=False
            )
            
            fig.update_xaxes(title_text="盈利金额 (SOL)", row=1, col=1)
            fig.update_xaxes(title_text="亏损金额 (SOL)", row=1, col=2)
            fig.update_yaxes(title_text="交易数量", row=1, col=1)
            fig.update_yaxes(title_text="交易数量", row=1, col=2)
            
            return fig.to_html(config=self.chart_config, div_id="profit_loss_distribution_chart")
            
        except Exception as e:
            self.logger.error(f"生成盈利亏损分布图表失败: {str(e)}")
            return self._create_error_chart("生成盈利亏损分布图表失败")
    
    def generate_top_profits_chart(self, top_profits: List[ProfitRanking]) -> str:
        """
        生成最佳盈利图表
        
        Args:
            top_profits: 前N个盈利交易
            
        Returns:
            图表HTML字符串
        """
        try:
            if not top_profits:
                return self._create_empty_chart("暂无盈利数据")
            
            ranks = [f"#{i+1}" for i in range(len(top_profits))]
            profit_amounts = [ranking.profit_amount for ranking in top_profits]
            profit_rates = [ranking.profit_rate for ranking in top_profits]
            
            fig = make_subplots(specs=[[{"secondary_y": True}]])
            
            # 盈利金额柱状图
            fig.add_trace(
                go.Bar(
                    x=ranks,
                    y=profit_amounts,
                    name='盈利金额 (SOL)',
                    marker_color=self.color_palette['profit'],
                    text=[f'{amount:.4f}' for amount in profit_amounts],
                    textposition='auto'
                ),
                secondary_y=False
            )
            
            # 盈利率线图
            fig.add_trace(
                go.Scatter(
                    x=ranks,
                    y=profit_rates,
                    mode='lines+markers',
                    name='盈利率 (%)',
                    line=dict(color=self.color_palette['neutral'], width=3),
                    marker=dict(size=8)
                ),
                secondary_y=True
            )
            
            fig.update_xaxes(title_text="排名")
            fig.update_yaxes(title_text="盈利金额 (SOL)", secondary_y=False)
            fig.update_yaxes(title_text="盈利率 (%)", secondary_y=True)
            
            fig.update_layout(
                title={
                    'text': f'最佳盈利排行榜 (Top {len(top_profits)})',
                    'x': 0.5,
                    'font': {'size': 16, 'color': self.color_palette['text']}
                },
                font={'family': 'Arial, sans-serif'},
                plot_bgcolor=self.color_palette['background'],
                paper_bgcolor='white',
                margin=dict(t=60, b=60, l=60, r=60),
                legend=dict(x=0.02, y=0.98)
            )
            
            return fig.to_html(config=self.chart_config, div_id="top_profits_chart")
            
        except Exception as e:
            self.logger.error(f"生成最佳盈利图表失败: {str(e)}")
            return self._create_error_chart("生成最佳盈利图表失败")
    
    def generate_top_losses_chart(self, top_losses: List[LossRanking]) -> str:
        """
        生成最大亏损图表
        
        Args:
            top_losses: 前N个亏损交易
            
        Returns:
            图表HTML字符串
        """
        try:
            if not top_losses:
                return self._create_empty_chart("暂无亏损数据")
            
            ranks = [f"#{i+1}" for i in range(len(top_losses))]
            loss_amounts = [ranking.loss_amount for ranking in top_losses]
            loss_rates = [ranking.loss_rate for ranking in top_losses]
            
            fig = make_subplots(specs=[[{"secondary_y": True}]])
            
            # 亏损金额柱状图
            fig.add_trace(
                go.Bar(
                    x=ranks,
                    y=loss_amounts,
                    name='亏损金额 (SOL)',
                    marker_color=self.color_palette['loss'],
                    text=[f'{amount:.4f}' for amount in loss_amounts],
                    textposition='auto'
                ),
                secondary_y=False
            )
            
            # 亏损率线图
            fig.add_trace(
                go.Scatter(
                    x=ranks,
                    y=loss_rates,
                    mode='lines+markers',
                    name='亏损率 (%)',
                    line=dict(color=self.color_palette['neutral'], width=3),
                    marker=dict(size=8)
                ),
                secondary_y=True
            )
            
            fig.update_xaxes(title_text="排名")
            fig.update_yaxes(title_text="亏损金额 (SOL)", secondary_y=False)
            fig.update_yaxes(title_text="亏损率 (%)", secondary_y=True)
            
            fig.update_layout(
                title={
                    'text': f'最大亏损排行榜 (Top {len(top_losses)})',
                    'x': 0.5,
                    'font': {'size': 16, 'color': self.color_palette['text']}
                },
                font={'family': 'Arial, sans-serif'},
                plot_bgcolor=self.color_palette['background'],
                paper_bgcolor='white',
                margin=dict(t=60, b=60, l=60, r=60),
                legend=dict(x=0.02, y=0.98)
            )
            
            return fig.to_html(config=self.chart_config, div_id="top_losses_chart")
            
        except Exception as e:
            self.logger.error(f"生成最大亏损图表失败: {str(e)}")
            return self._create_error_chart("生成最大亏损图表失败")
    
    def _create_empty_chart(self, message: str) -> str:
        """
        创建空数据图表
        
        Args:
            message: 显示消息
            
        Returns:
            图表HTML字符串
        """
        fig = go.Figure()
        fig.add_annotation(
            text=message,
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=16, color=self.color_palette['text'])
        )
        
        fig.update_layout(
            xaxis=dict(visible=False),
            yaxis=dict(visible=False),
            plot_bgcolor=self.color_palette['background'],
            paper_bgcolor='white',
            margin=dict(t=40, b=40, l=40, r=40)
        )
        
        return fig.to_html(config=self.chart_config)
    
    def _create_error_chart(self, error_message: str) -> str:
        """
        创建错误图表
        
        Args:
            error_message: 错误消息
            
        Returns:
            图表HTML字符串
        """
        fig = go.Figure()
        fig.add_annotation(
            text=f"❌ {error_message}",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=16, color=self.color_palette['loss'])
        )
        
        fig.update_layout(
            xaxis=dict(visible=False),
            yaxis=dict(visible=False),
            plot_bgcolor=self.color_palette['background'],
            paper_bgcolor='white',
            margin=dict(t=40, b=40, l=40, r=40)
        )
        
        return fig.to_html(config=self.chart_config) 