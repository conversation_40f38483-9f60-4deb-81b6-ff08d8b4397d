#!/usr/bin/env python3
"""
Solana直接交易服务测试脚本

这个脚本可以执行真实的Solana交易来测试服务功能。
请确保你的钱包中有足够的SOL来支付交易费用。

使用方法：
1. 设置环境变量 WALLET_PRIVATE_KEY 为你的钱包私钥（Base58格式）
2. 设置环境变量 WALLET_ADDRESS 为你的钱包地址
3. (可选) 设置要购买的代币地址和名称
4. 运行: python test_jupiter_trade_main.py

示例:
# 基本设置
export WALLET_PRIVATE_KEY='your_base58_private_key_here'
export WALLET_ADDRESS='your_wallet_address_here'

# 购买meme币 (可选，默认购买USDC)
export TARGET_TOKEN_ADDRESS='代币地址'
export TARGET_TOKEN_NAME='代币名称'

python test_jupiter_trade_main.py
"""

import os
import sys
import asyncio
import logging
from datetime import datetime, timezone

# 添加项目根目录到Python路径
sys.path.append('.')

from utils.trading.solana.jupiter_trade_service import (
    JupiterTradeService,
    create_jupiter_trade_service,
    SOL_MINT_ADDRESS
)
from utils.trading.solana.trade_interface import TradeType, TradeStatus
from beanie import PydanticObjectId


async def main():
    """测试主函数 - 执行真实的Solana直接交易"""
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("🚀 Solana直接交易服务测试")
    print("=" * 50)
    print(f"⏰ 测试时间: {datetime.now()}")
    print()
    
    # 从环境变量获取钱包信息
    wallet_private_key = os.getenv('WALLET_PRIVATE_KEY')
    wallet_address = os.getenv('WALLET_ADDRESS')
    
    if not wallet_private_key or not wallet_address:
        print("❌ 错误：请设置环境变量")
        print("   WALLET_PRIVATE_KEY=你的钱包私钥（Base58格式）")
        print("   WALLET_ADDRESS=你的钱包地址")
        print("\n可选设置（购买特定meme币）:")
        print("   TARGET_TOKEN_ADDRESS=代币地址")
        print("   TARGET_TOKEN_NAME=代币名称")
        print("\n示例:")
        print("   export WALLET_PRIVATE_KEY='your_base58_private_key_here'")
        print("   export WALLET_ADDRESS='your_wallet_address_here'")
        print("   export TARGET_TOKEN_ADDRESS='代币合约地址'")
        print("   export TARGET_TOKEN_NAME='MEME'")
        print("   python test_jupiter_trade_main.py")
        return False
    
    # 安全检查：确保不是测试值
    if wallet_private_key in ['test_key_not_set', 'your_base58_private_key_here']:
        print("❌ 错误：请设置真实的钱包私钥")
        return False
        
    if wallet_address in ['test_address_not_set', 'your_wallet_address_here']:
        print("❌ 错误：请设置真实的钱包地址")
        return False
    
    # 交易配置
    test_config = {
        'jupiter_api_host': 'https://quote-api.jup.ag',
        'solana_rpc_url': 'https://api.mainnet-beta.solana.com',
        'http_timeout': 30.0,
        'max_slippage_bps': 100,        # 1% 滑点
        'priority_fee_lamports': 50000,  # 0.00005 SOL 优先费
        'input_token_decimals': 9,       # SOL精度
        'enable_smart_routing': False    # 🔧 禁用智能路由，便于统计
    }
    
    # 从环境变量获取要购买的代币地址，默认为USDC
    target_token = os.getenv('TARGET_TOKEN_ADDRESS', 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v')
    target_token_name = os.getenv('TARGET_TOKEN_NAME', 'USDC')
    
    # 测试交易参数（小额度）
    input_token = SOL_MINT_ADDRESS  # SOL
    output_token = target_token
    trade_amount = 0.001  # 0.001 SOL (~$0.2左右，根据当前价格)
    
    print(f"📋 交易配置:")
    print(f"   输入代币: SOL")
    print(f"   输出代币: {target_token_name}")
    print(f"   代币地址: {target_token}")
    print(f"   交易数量: {trade_amount} SOL")
    print(f"   滑点: {test_config['max_slippage_bps']/100}%")
    print(f"   优先费: {test_config['priority_fee_lamports']} lamports")
    print(f"   钱包地址: {wallet_address}")
    print()
    
    # 确认交易
    user_input = input("⚠️  这将执行真实的交易，是否继续？(y/N): ").strip().lower()
    if user_input != 'y':
        print("❌ 交易已取消")
        return False
    
    print("\n🔧 创建交易服务...")
    service = create_jupiter_trade_service(test_config)
    
    try:
        # 生成测试用的ID
        test_signal_id = PydanticObjectId()
        test_trade_record_id = PydanticObjectId()
        
        print(f"🎯 开始执行测试交易...")
        print(f"   Signal ID: {test_signal_id}")
        print(f"   Trade Record ID: {test_trade_record_id}")
        print()
        
        # 执行交易
        result = await service.execute_trade(
            trade_type=TradeType.BUY,
            input_token_address=input_token,
            output_token_address=output_token,
            amount_input_token=trade_amount,
            wallet_private_key_b58=wallet_private_key,
            wallet_address=wallet_address,
            strategy_snapshot=test_config,
            signal_id=test_signal_id,
            trade_record_id=test_trade_record_id
        )
        
        # 输出结果
        print("📊 交易结果:")
        print(f"   状态: {result.status.value}")
        
        if result.status == TradeStatus.SUCCESS:
            print(f"✅ 交易成功!")
            print(f"   交易哈希: {result.tx_hash}")
            print(f"   执行时间: {result.executed_at}")
            print(f"   实际输入数量: {result.actual_amount_in}")
            print(f"   实际输出数量: {result.actual_amount_out}")
            
            if result.tx_hash:
                print(f"   🔗 Solscan链接: https://solscan.io/tx/{result.tx_hash}")
                print(f"   🔗 Explorer链接: https://explorer.solana.com/tx/{result.tx_hash}")
                
            return True
                
        elif result.status == TradeStatus.PENDING:
            print(f"⏳ 交易待确认:")
            print(f"   交易哈希: {result.tx_hash}")
            print(f"   错误信息: {result.error_message}")
            
            if result.tx_hash:
                print(f"   🔗 Solscan链接: https://solscan.io/tx/{result.tx_hash}")
                print(f"   🔗 Explorer链接: https://explorer.solana.com/tx/{result.tx_hash}")
                
            return False
                
        else:  # FAILED
            print(f"❌ 交易失败:")
            print(f"   错误信息: {result.error_message}")
            return False
            
    except Exception as e:
        print(f"💥 执行过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理资源
        print("\n🧹 清理资源...")
        await service.close()
        print("✨ 测试完成")


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1) 