import logging
from typing import Dict, List, Optional
from utils.trading.solana.trade_interface import TradeInterface
from models.config import TradeChannelConfig

logger = logging.getLogger(__name__)


class ChannelNotFoundError(Exception):
    """渠道未找到异常"""
    pass


class ChannelRegistry:
    """渠道注册表 - 管理所有可用的交易渠道实例"""
    
    def __init__(self):
        self._channels: Dict[str, TradeInterface] = {}
        self._channel_configs: Dict[str, TradeChannelConfig] = {}
        self._channel_health: Dict[str, bool] = {}
    
    def register_channel(
        self, 
        channel_type: str, 
        channel_instance: TradeInterface, 
        channel_config: TradeChannelConfig
    ) -> None:
        """
        注册交易渠道
        
        Args:
            channel_type: 渠道类型标识符
            channel_instance: 渠道实例
            channel_config: 渠道配置
            
        Raises:
            ValueError: 如果渠道类型已存在
        """
        if channel_type in self._channels:
            logger.warning(f"渠道 '{channel_type}' 已存在，将被替换")
        
        self._channels[channel_type] = channel_instance
        self._channel_configs[channel_type] = channel_config
        self._channel_health[channel_type] = True  # 默认认为是健康的
        
        logger.info(f"渠道 '{channel_type}' 注册成功")
    
    def get_channel(self, channel_type: str) -> TradeInterface:
        """
        获取指定渠道实例
        
        Args:
            channel_type: 渠道类型标识符
            
        Returns:
            TradeInterface: 渠道实例
            
        Raises:
            ChannelNotFoundError: 如果渠道不存在
        """
        if channel_type not in self._channels:
            raise ChannelNotFoundError(f"渠道 '{channel_type}' 未注册")
        
        return self._channels[channel_type]
    
    def get_channel_config(self, channel_type: str) -> TradeChannelConfig:
        """
        获取指定渠道配置
        
        Args:
            channel_type: 渠道类型标识符
            
        Returns:
            TradeChannelConfig: 渠道配置
            
        Raises:
            ChannelNotFoundError: 如果渠道不存在
        """
        if channel_type not in self._channel_configs:
            raise ChannelNotFoundError(f"渠道配置 '{channel_type}' 未找到")
        
        return self._channel_configs[channel_type]
    
    def list_channels(self) -> List[str]:
        """
        列出所有已注册的渠道类型
        
        Returns:
            List[str]: 渠道类型列表
        """
        return list(self._channels.keys())
    
    def list_enabled_channels(self) -> List[str]:
        """
        列出所有启用的渠道类型
        
        Returns:
            List[str]: 启用的渠道类型列表
        """
        return [
            channel_type 
            for channel_type, config in self._channel_configs.items() 
            if config.enabled
        ]
    
    def remove_channel(self, channel_type: str) -> bool:
        """
        移除渠道
        
        Args:
            channel_type: 渠道类型标识符
            
        Returns:
            bool: 是否成功移除
        """
        if channel_type not in self._channels:
            logger.warning(f"尝试移除不存在的渠道: '{channel_type}'")
            return False
        
        # 清理渠道实例
        try:
            channel_instance = self._channels[channel_type]
            if hasattr(channel_instance, 'close'):
                # 如果渠道实例有清理方法，调用它
                if asyncio.iscoroutinefunction(channel_instance.close):
                    # 异步关闭需要在异步上下文中处理
                    logger.warning(f"渠道 '{channel_type}' 需要异步关闭，请使用 aremove_channel")
                else:
                    channel_instance.close()
        except Exception as e:
            logger.error(f"关闭渠道 '{channel_type}' 时出错: {e}")
        
        # 移除注册信息
        del self._channels[channel_type]
        del self._channel_configs[channel_type]
        if channel_type in self._channel_health:
            del self._channel_health[channel_type]
        
        logger.info(f"渠道 '{channel_type}' 已移除")
        return True
    
    async def aremove_channel(self, channel_type: str) -> bool:
        """
        异步移除渠道
        
        Args:
            channel_type: 渠道类型标识符
            
        Returns:
            bool: 是否成功移除
        """
        if channel_type not in self._channels:
            logger.warning(f"尝试移除不存在的渠道: '{channel_type}'")
            return False
        
        # 清理渠道实例
        try:
            channel_instance = self._channels[channel_type]
            if hasattr(channel_instance, 'close'):
                if asyncio.iscoroutinefunction(channel_instance.close):
                    await channel_instance.close()
                else:
                    channel_instance.close()
        except Exception as e:
            logger.error(f"关闭渠道 '{channel_type}' 时出错: {e}")
        
        # 移除注册信息
        del self._channels[channel_type]
        del self._channel_configs[channel_type]
        if channel_type in self._channel_health:
            del self._channel_health[channel_type]
        
        logger.info(f"渠道 '{channel_type}' 已移除")
        return True
    
    def is_channel_registered(self, channel_type: str) -> bool:
        """
        检查渠道是否已注册
        
        Args:
            channel_type: 渠道类型标识符
            
        Returns:
            bool: 是否已注册
        """
        return channel_type in self._channels
    
    def is_channel_enabled(self, channel_type: str) -> bool:
        """
        检查渠道是否启用
        
        Args:
            channel_type: 渠道类型标识符
            
        Returns:
            bool: 是否启用
        """
        if channel_type not in self._channel_configs:
            return False
        
        return self._channel_configs[channel_type].enabled
    
    def set_channel_health(self, channel_type: str, is_healthy: bool) -> None:
        """
        设置渠道健康状态
        
        Args:
            channel_type: 渠道类型标识符
            is_healthy: 是否健康
        """
        if channel_type in self._channel_health:
            self._channel_health[channel_type] = is_healthy
            logger.debug(f"渠道 '{channel_type}' 健康状态设置为: {is_healthy}")
    
    def is_channel_healthy(self, channel_type: str) -> bool:
        """
        检查渠道健康状态
        
        Args:
            channel_type: 渠道类型标识符
            
        Returns:
            bool: 是否健康
        """
        return self._channel_health.get(channel_type, False)
    
    def get_registry_stats(self) -> dict:
        """
        获取注册表统计信息
        
        Returns:
            dict: 统计信息
        """
        total_channels = len(self._channels)
        enabled_channels = len(self.list_enabled_channels())
        healthy_channels = sum(1 for healthy in self._channel_health.values() if healthy)
        
        return {
            "total_channels": total_channels,
            "enabled_channels": enabled_channels,
            "healthy_channels": healthy_channels,
            "channels": {
                channel_type: {
                    "enabled": self._channel_configs[channel_type].enabled,
                    "healthy": self._channel_health.get(channel_type, False),
                    "priority": self._channel_configs[channel_type].priority
                }
                for channel_type in self._channels.keys()
            }
        }
    
    async def close_all_channels(self) -> None:
        """关闭所有渠道"""
        logger.info("开始关闭所有渠道")
        for channel_type in list(self._channels.keys()):
            await self.aremove_channel(channel_type)
        logger.info("所有渠道已关闭")


# 添加asyncio导入（用于异步关闭）
import asyncio 