import json
import logging
import os
import traceback
from datetime import datetime

from dotenv import load_dotenv
import httpx
from utils.spiders.x.idata_river import IDataRiverSpider
from utils.session import AsyncProxyClient


class UtoolsSpider(IDataRiverSpider):
    def __init__(self):
        super().__init__()
        self.base_url = 'https://twitter.good6.top/api/base/apitools/userByScreenNameV2'
        self.tweets_url = 'https://twitter.good6.top/api/base/apitools/userTweetsV2'
        self.apikey = os.getenv('UTOOLS_API_KEY')
        
    async def _get_user_info(self, username: str) -> dict | None:
        params = {
            'apiKey': self.apikey,
            'screenName': username
        }
        logging.info(f"Fetching user info for {username}")
        print(params)
        
        async with AsyncProxyClient() as client:
            response = await client.get(self.base_url, params=params)
            try:
                response_json = response.json()
                ok = response.status_code == 200 and response_json.get('code') == 1
                if ok:
                    user_data = json.loads(response_json['data'])['data']['user']['result']
                    return user_data, ok
                else:
                    return response.text, ok
            except Exception as e:
                logging.error(f"Failed to fetch user info: {traceback.format_exc()}")
                return traceback.format_exc(), False
        
    async def _get_user_tweets(self, user_id: str, page_id: str = '') -> dict | None:
        params = {
            'apiKey': self.apikey,
            'userId': user_id
        }
        
        async with AsyncProxyClient() as client:
            response = await client.get(self.tweets_url, params=params)
            try:
                response_json = response.json()
                ok = response.status_code == 200 and response_json.get('code') == 1
                if ok:
                    return json.loads(response_json['data'])['data']['user']['result']['timeline_v2']['timeline']['instructions'], ok
                else:
                    return response.text, ok
            except Exception as e:
                logging.error(f"Failed to fetch tweets: {traceback.format_exc()}")
                return traceback.format_exc(), False
                
    async def scrape_user_info(self, username: str) -> dict | None:
        user_data, ok = await self._get_user_info(username)
        if ok:
            try:
                legacy = user_data['legacy']
                
                # 解析创建时间
                created_at = datetime.strptime(
                    legacy['created_at'],
                    '%a %b %d %H:%M:%S +0000 %Y'
                )
                
                # 构建用户数据字典
                user_dict = {
                    'user_id': user_data['rest_id'],
                    'username': legacy['screen_name'],
                    'display_name': legacy['name'],
                    'description': legacy['description'],
                    'followers_count': legacy['followers_count'],
                    'following_count': legacy['friends_count'],
                    'tweets_count': legacy['statuses_count'],
                    'profile_image_url': legacy['profile_image_url_https'],
                    'verified': user_data.get('is_blue_verified', False),
                    'location': legacy['location'],
                    'created_at': created_at,
                    'last_updated': datetime.utcnow(),
                    'rest_id': user_data['rest_id']
                }
                logging.info(f"Successfully fetched user info for {username}")
                return user_dict
            except Exception as e:
                logging.error(f"Error parsing user data: {traceback.format_exc()}")
                return None
        else:
            logging.error(f"Failed to fetch user data: {user_data}")
            return None
            
    async def scrape_user_tweets(self, username: str, page_id: str = '') -> list[dict] | None:
        try:
            # 先获取用户ID
            logging.info(f"Starting to fetch tweets for {username}")
            user_info = await self.scrape_user_info(username)
            if not user_info:
                logging.error("Failed to get user info")
                return None
            
            user_id = user_info['user_id']
            
            instructions, ok = await self._get_user_tweets(user_id, page_id)
            if not ok:
                logging.error(f"Failed to fetch tweets: {traceback.format_exc()}")
                return None
                
            tweets = []
            
            logging.info(f"Found {len(instructions)} instructions")
            
            for instruction in instructions:
                if instruction.get('type') == 'TimelineAddEntries':
                    entries = instruction.get('entries', [])
                elif instruction.get('entries'):
                    entries = instruction.get('entries', [])
                else:
                    continue
                    
                logging.info(f"Processing {len(entries)} entries")
                for entry in entries:
                    try:
                        if 'content' not in entry:
                            continue
                            
                        content = entry['content']
                        if content.get('__typename') != 'TimelineTimelineItem':
                            continue
                            
                        tweet_content = content.get('itemContent', {})
                        if tweet_content.get('__typename') != 'TimelineTweet':
                            continue
                            
                        tweet_result = tweet_content.get('tweet_results', {}).get('result', {})
                        if not tweet_result:
                            continue
                            
                        # 处理转发的情况
                        original_tweet = tweet_result
                        if 'retweeted_status_result' in tweet_result.get('legacy', {}):
                            original_tweet = tweet_result['legacy']['retweeted_status_result']['result']
                            
                        legacy = original_tweet.get('legacy', {})
                        if not legacy:
                            continue
                            
                        # 解析创建时间
                        created_at = datetime.strptime(
                            legacy['created_at'],
                            '%a %b %d %H:%M:%S +0000 %Y'
                        )
                        
                        # 构建推文数据
                        tweet_dict = {
                            'tweet_id': legacy['id_str'],
                            'text': legacy['full_text'],
                            'author_id': user_id,
                            'sender_id': legacy['user_id_str'],
                            'created_at': created_at,
                            'language': legacy.get('lang'),
                            'like_count': legacy.get('favorite_count', 0),
                            'retweet_count': legacy.get('retweet_count', 0),
                            'reply_count': legacy.get('reply_count', 0),
                            'quote_count': legacy.get('quote_count', 0),
                            'is_retweet': 'retweeted_status_result' in tweet_result.get('legacy', {}),
                            'is_reply': bool(legacy.get('in_reply_to_status_id_str')),
                            'is_quote': legacy.get('is_quote_status', False),
                            'replied_to_tweet_id': legacy.get('in_reply_to_status_id_str'),
                            'quoted_tweet_id': legacy.get('quoted_status_id_str'),
                            'retweeted_tweet_id': legacy.get('retweeted_status_id_str'),
                            'media': self._extract_media(legacy.get('extended_entities', {}).get('media', [])),
                            'urls': [url['expanded_url'] for url in legacy.get('entities', {}).get('urls', [])],
                            'hashtags': [tag['text'] for tag in legacy.get('entities', {}).get('hashtags', [])],
                            'mentions': [mention['screen_name'] for mention in legacy.get('entities', {}).get('user_mentions', [])],
                            'fetched_at': datetime.utcnow(),
                            'last_updated': datetime.utcnow(),
                            'is_top': bool(content.get('clientEventInfo', {}).get('component') == 'pinned_tweets')
                        }
                        tweets.append(tweet_dict)
                        logging.debug(f"Successfully parsed tweet {tweet_dict['tweet_id']}")
                    except Exception as e:
                        logging.error(f"Error parsing tweet entry: {str(e)}")
                        continue
            
            logging.info(f"Successfully fetched {len(tweets)} tweets")
            return tweets
            
        except Exception as e:
            logging.error(f"Error parsing tweets: {str(e)}")
            return None
        

if __name__ == '__main__':
    load_dotenv()
    import asyncio
    
    async def main():
        utools = UtoolsSpider()
        tweets = await utools.scrape_user_tweets('elonmusk')
        print(tweets[0])
    
    asyncio.run(main())
