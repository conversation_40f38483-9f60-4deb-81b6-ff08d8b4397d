from datetime import datetime
from typing import Optional, List, Dict, Any
from beanie import Document, Indexed
from pydantic import BaseModel, Field

class GmgnTokenLinks(Document):
    """GMGN代币链接信息模型
    
    用于存储代币的相关链接信息，包括：
    - 社交媒体链接
    - 官方网站
    - 交易平台链接
    - 验证状态
    - Rug信息
    - 社区投票数据
    """
    
    # 基本信息
    chain: str = Field(description="链名称")
    address: Indexed(str) = Field(description="代币地址")
    
    # 链接信息
    gmgn_url: Optional[str] = Field(default=None, description="GMGN链接")
    geckoterminal_url: Optional[str] = Field(default=None, description="GeckoTerminal链接")
    twitter_username: Optional[str] = Field(default=None, description="Twitter用户名")
    website: Optional[str] = Field(default=None, description="官方网站")
    telegram: Optional[str] = Field(default=None, description="Telegram链接")
    discord: Optional[str] = Field(default=None, description="Discord链接")
    description: Optional[str] = Field(default=None, description="描述")
    facebook: Optional[str] = Field(default=None, description="Facebook链接")
    github: Optional[str] = Field(default=None, description="Github链接")
    instagram: Optional[str] = Field(default=None, description="Instagram链接")
    linkedin: Optional[str] = Field(default=None, description="LinkedIn链接")
    medium: Optional[str] = Field(default=None, description="Medium链接")
    reddit: Optional[str] = Field(default=None, description="Reddit链接")
    tiktok: Optional[str] = Field(default=None, description="TikTok链接")
    youtube: Optional[str] = Field(default=None, description="YouTube链接")
    verify_status: int = Field(default=0, description="验证状态")
    
    # Rug信息 代表代币被Rug的程度
    rug_ratio: str = Field(default="0", description="Rug比率(holder_rugged_num / holder_token_num)")  # 其实就是 holder_rugged_num / holder_token_num
    holder_rugged_num: int = Field(default=0, description="被Rug的持有者数量")
    holder_token_num: int = Field(default=0, description="代币持有者数量")
    
    # 投票信息
    like_count: int = Field(default=0, description="点赞数")
    unlike_count: int = Field(default=0, description="踩数")
    
    # 时间信息
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")
    
    class Settings:
        name = "gmgn_token_links"
        use_state_management = True
    
    @classmethod
    async def from_api_data(cls, chain: str, data: Dict[str, Any]) -> 'GmgnTokenLinks':
        """从API数据创建模型实例
        
        Args:
            chain: 链名称
            data: API返回的数据
            
        Returns:
            GmgnTokenLinks: 模型实例
        """
        
        if data.get('address') == '6p6xgHyF7AeE6TZkSmFsko444wqoP15icUSqi2jfGiPN':
            print(data)
        return cls(
            chain=chain,
            address=data.get('address', ''),
            
            # 链接信息
            gmgn_url=data.get('gmgn'),
            geckoterminal_url=data.get('geckoterminal'),
            twitter_username=data.get('twitter_username'),
            website=data.get('website'),
            telegram=data.get('telegram'),
            discord=data.get('discord'),
            description=data.get('description'),
            facebook=data.get('facebook'),
            github=data.get('github'),
            instagram=data.get('instagram'),
            linkedin=data.get('linkedin'),
            medium=data.get('medium'),
            reddit=data.get('reddit'),
            tiktok=data.get('tiktok'),
            youtube=data.get('youtube'),
            verify_status=data.get('verify_status', 0),
            
            # Rug信息
            rug_ratio=data.get('rug_ratio', '0'),
            holder_rugged_num=data.get('holder_rugged_num', 0),
            holder_token_num=data.get('holder_token_num', 0),
            
            # 投票信息
            like_count=data.get('like', 0),
            unlike_count=data.get('unlike', 0)
        )
    
    class Config:
        json_schema_extra = {
            "example": {
                "chain": "sol",
                "address": "6p6xgHyF7AeE6TZkSmFsko444wqoP15icUSqi2jfGiPN",
                "twitter_username": "GetTrumpMemes",
                "website": "https://gettrumpmemes.com/",
                "telegram": "https://t.me/isorui",
                "verify_status": 1,
                "rug_ratio": "0",
                "holder_rugged_num": 0,
                "holder_token_num": 1000,
                "like_count": 100,
                "unlike_count": 10,
                "created_at": "2024-03-02T00:00:00",
                "updated_at": "2024-03-02T00:00:00"
            }
        } 