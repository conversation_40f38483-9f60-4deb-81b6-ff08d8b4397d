import imaplib
import email
import re
from datetime import datetime, timedelta
import time


class MailHelper:
    def __init__(self, imap_config: dict):
        """
        初始化邮件助手
        
        Args:
            imap_config: 邮箱配置字典，包含以下键：
                - server: IMAP服务器地址
                - port: IMAP端口
                - user: 邮箱用户名
                - password: 邮箱密码
        """
        self.imap_config = imap_config
        
    def connect(self) -> tuple[bool, str, imaplib.IMAP4_SSL]:
        """
        连接到邮箱服务器
        
        Returns:
            tuple: (是否成功, 错误信息, IMAP连接对象)
        """
        try:
            mail = imaplib.IMAP4_SSL(self.imap_config['server'], self.imap_config['port'], timeout=30)
            mail.login(self.imap_config['user'], self.imap_config['password'])
            return True, "", mail
        except Exception as e:
            return False, str(e), None
            
    def get_verification_code(self, subject: str, pattern: str, max_retries: int = 10, start_time: datetime = None) -> tuple[bool, str, str]:
        """
        获取验证码
        
        Args:
            subject: 邮件主题
            pattern: 验证码匹配模式（正则表达式）
            max_retries: 最大重试次数
            start_time: 开始搜索的时间点
            
        Returns:
            tuple: (是否成功, 错误信息, 验证码)
        """
        retry_delay = 5  # 初始等待时间
        mail = None
        
        # 如果没有提供开始时间，使用当前时间
        if start_time is None:
            start_time = datetime.now()
        
        for attempt in range(max_retries):
            try:
                print(f"开始连接邮箱服务器... (尝试 {attempt + 1}/{max_retries})")
                
                # 连接邮箱
                success, error, mail = self.connect()
                if not success:
                    raise Exception(f"邮箱连接失败: {error}")
                
                print("连接成功，开始搜索验证码邮件...")
                # 选择收件箱
                mail.select('INBOX')
                
                # 获取1分钟前的时间
                one_minute_ago = datetime.now() - timedelta(minutes=1)
                date_str = one_minute_ago.strftime("%d-%b-%Y")
                
                # 使用更精确的搜索条件
                search_criteria = f'(SINCE "{date_str}" SUBJECT "是你的 X 验证码")'
                print(f"搜索条件: {search_criteria}, 搜索最近1分钟的邮件")
                _, message_numbers = mail.search(None, 'CHARSET', 'UTF-8', search_criteria.encode('utf-8'))
                
                if not message_numbers[0]:
                    print(f"未找到验证码邮件，等待20秒后重试...")
                    time.sleep(20)
                    continue
                
                # 获取最新的邮件
                latest_email_id = message_numbers[0].split()[-1]
                _, msg_data = mail.fetch(latest_email_id, '(RFC822)')
                email_body = msg_data[0][1]
                message = email.message_from_bytes(email_body)
                
                # 解析邮件内容
                code = None
                if message.is_multipart():
                    for part in message.walk():
                        if part.get_content_type() == "text/plain":
                            body = part.get_payload(decode=True).decode()
                            print(f"邮件内容: {body}")  # 打印邮件内容以便调试
                            # 尝试从正文中提取验证码
                            match = re.search(r'请输入此验证码以开始使用\s*X：\s*\n+\s*(\d{6})', body)
                            if match:
                                code = match.group(1)
                                break
                else:
                    body = message.get_payload(decode=True).decode()
                    print(f"邮件内容: {body}")  # 打印邮件内容以便调试
                    match = re.search(r'请输入此验证码以开始使用\s*X：\s*\n+\s*(\d{6})', body)
                    if match:
                        code = match.group(1)
                
                if code:
                    print(f"成功获取验证码: {code}")
                    return True, "", code
                
                print("未在邮件中找到验证码，等待20秒后重试...")
                time.sleep(20)
                
            except Exception as e:
                error_msg = f"获取验证码时出错 (尝试 {attempt + 1}/{max_retries}): {str(e)}"
                print(error_msg)
                if attempt < max_retries - 1:
                    print(f"等待 20 秒后重试...")
                    time.sleep(20)
                else:
                    return False, error_msg, None
                    
            finally:
                if mail:
                    try:
                        mail.close()
                        mail.logout()
                    except:
                        pass
        
        return False, "验证码获取失败，达到最大重试次数", None


def test_mail_helper():
    """测试邮件助手功能"""
    # 邮箱配置
    imap_config = {
        'server': 'imap.qq.com',
        'port': 993,
        'user': '<EMAIL>',
        'password': 'jeazgqkcjcixhhbh'
    }
    
    # 创建邮件助手实例
    mail_helper = MailHelper(imap_config)
    
    # 测试邮箱连接
    print("测试邮箱连接...")
    success, error, mail = mail_helper.connect()
    if success:
        print("邮箱连接成功！")
        mail.logout()
    else:
        print(f"邮箱连接失败: {error}")
        return
    
    # 测试获取验证码
    print("\n测试获取验证码...")
    success, error, code = mail_helper.get_verification_code(
        subject="是你的 X 验证码",  # 使用正确的邮件主题
        pattern=r'请输入此验证码以开始使用\s*X：\s*\n+\s*(\d{6})'  # 匹配实际邮件格式
    )
    
    if success:
        print(f"验证码获取成功: {code}")
    else:
        print(f"验证码获取失败: {error}")


if __name__ == "__main__":
    test_mail_helper() 