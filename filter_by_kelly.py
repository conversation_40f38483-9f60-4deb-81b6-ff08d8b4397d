import logging
import os
import sys
from utils.backtest_analysis.result_filter import filter_and_save_by_metric

# --- Configuration ---
# Input file from the previous check
INPUT_JSON_FILE = 'backtest_result/ed_param_search_20250411_121318/param_search_results.json'

# Determine output file name based on input file directory and format
input_dir_name = os.path.basename(os.path.dirname(INPUT_JSON_FILE))
OUTPUT_FORMAT = 'json' # Set the desired output format
OUTPUT_FILE = f'positive_kelly_results_{input_dir_name}.{OUTPUT_FORMAT}'

METRIC_COLUMN = 'kelly_fraction_calculated' # Column to filter on
THRESHOLD = 0.0                         # Filter threshold
COMPARISON_OP = 'gt'                    # Operator: greater than

# Setup basic logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("KellyFilter")

# --- Main Execution ---
if __name__ == "__main__":
    logger.info(f"开始筛选凯利分数 > 0 的结果从: {INPUT_JSON_FILE}")
    logger.info(f"筛选条件: {METRIC_COLUMN} {COMPARISON_OP} {THRESHOLD}")
    logger.info(f"输出文件将保存为 ({OUTPUT_FORMAT.upper()}): {OUTPUT_FILE}")

    success = filter_and_save_by_metric(
        json_file_path=INPUT_JSON_FILE,
        output_file_path=OUTPUT_FILE, # Use renamed variable
        metric_column=METRIC_COLUMN,
        threshold=THRESHOLD,
        comparison_op=COMPARISON_OP,
        output_format=OUTPUT_FORMAT # Specify the output format
    )

    if success:
        print(f"\n筛选脚本执行成功。结果已保存（如果找到匹配项）。")
        logger.info(f"筛选脚本执行成功。")
    else:
        print(f"\n筛选脚本执行失败。请检查日志获取详细信息。")
        logger.error(f"筛选脚本执行失败。")
        sys.exit(1) # Exit with error code if filtering failed

    print("\n脚本执行完毕。") 