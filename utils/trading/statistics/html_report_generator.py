"""
HTML报告生成器

使用Jinja2模板引擎生成美观的HTML报告，包含统计表格、图表和响应式设计。
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime
from jinja2 import Template
import os

from .models import StatisticsResult, StatisticsConfig


class HTMLReportGenerator:
    """
    HTML报告生成器
    
    使用Jinja2模板生成包含统计数据和图表的HTML报告。
    """
    
    def __init__(self):
        """初始化HTML报告生成器"""
        self.logger = logging.getLogger(__name__)
    
    def generate_report(self, statistics: StatisticsResult, charts: Dict[str, str], 
                       config: Optional[StatisticsConfig] = None) -> str:
        """
        生成完整的HTML报告
        
        Args:
            statistics: 统计结果
            charts: 图表HTML字符串字典
            config: 统计配置（可选）
            
        Returns:
            完整的HTML报告字符串
        """
        try:
            self.logger.info("开始生成HTML报告")
            
            # 准备模板数据
            template_data = {
                'statistics': statistics,
                'charts': charts,
                'config': config,
                'generated_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'title': '交易统计分析报告',
                'token_link_helper': self._create_token_link_helper(config)
            }
            
            # 渲染HTML模板
            html_content = self._render_template(template_data)
            
            self.logger.info("HTML报告生成完成")
            return html_content
            
        except Exception as e:
            self.logger.error(f"生成HTML报告失败: {str(e)}")
            raise
    
    def _create_token_link_helper(self, config: Optional[StatisticsConfig]) -> callable:
        """
        创建Token链接生成辅助函数
        
        Args:
            config: 统计配置
            
        Returns:
            Token链接生成函数
        """
        def generate_token_link(token_address: str) -> str:
            """
            生成Token链接HTML
            
            Args:
                token_address: Token地址
                
            Returns:
                Token链接HTML字符串
            """
            if not config or not config.enable_token_links:
                # 如果未启用链接功能，返回省略的地址
                return f"{token_address[:8]}...{token_address[-8:]}"
            
            # 生成完整的链接URL
            link_url = f"{config.token_link_base_url.rstrip('/')}/{token_address}"
            
            # 返回可点击的链接HTML
            return f'<a href="{link_url}" target="_blank" class="token-link" title="在GMGN上查看Token详情">{token_address[:8]}...{token_address[-8:]}</a>'
        
        return generate_token_link
    
    def _render_template(self, data: Dict[str, Any]) -> str:
        """
        渲染HTML模板
        
        Args:
            data: 模板数据
            
        Returns:
            渲染后的HTML字符串
        """
        template_str = self._get_html_template()
        template = Template(template_str)
        return template.render(**data)
    
    def _get_html_template(self) -> str:
        """
        获取HTML模板字符串
        
        Returns:
            HTML模板字符串
        """
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <style>
        {{ self._get_css_styles() }}
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <header class="header">
            <h1 class="title">{{ title }}</h1>
            <div class="meta-info">
                <span class="generated-time">生成时间: {{ generated_time }}</span>
                {% if config %}
                <div class="config-info">
                    {% if config.start_date %}
                    <span>开始时间: {{ config.start_date.strftime('%Y-%m-%d') }}</span>
                    {% endif %}
                    {% if config.end_date %}
                    <span>结束时间: {{ config.end_date.strftime('%Y-%m-%d') }}</span>
                    {% endif %}
                    {% if config.strategies %}
                    <span>策略: {{ config.strategies|join(', ') }}</span>
                    {% endif %}
                    {% if config.tokens %}
                    <span>Token: {{ config.tokens|join(', ') }}</span>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </header>

        <!-- 总体统计概览 -->
        <section class="overview-section">
            <h2 class="section-title">📊 总体统计概览</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">{{ statistics.overall_stats.total_trades }}</div>
                    <div class="stat-label">总交易数</div>
                </div>
                <div class="stat-card {{ 'positive' if statistics.overall_stats.total_win_rate >= 50 else 'negative' }}">
                    <div class="stat-value">{{ "%.2f"|format(statistics.overall_stats.total_win_rate) }}%</div>
                    <div class="stat-label">胜率</div>
                </div>
                <div class="stat-card {{ 'positive' if statistics.overall_stats.total_profit_rate >= 0 else 'negative' }}">
                    <div class="stat-value">{{ "%.2f"|format(statistics.overall_stats.total_profit_rate) }}%</div>
                    <div class="stat-label">总盈利率</div>
                </div>
                <div class="stat-card {{ 'positive' if statistics.overall_stats.avg_profit_rate >= 0 else 'negative' }}">
                    <div class="stat-value">{{ "%.2f"|format(statistics.overall_stats.avg_profit_rate) }}%</div>
                    <div class="stat-label">平均盈利率</div>
                </div>
                <div class="stat-card {{ 'positive' if statistics.overall_stats.overall_return_rate >= 0 else 'negative' }}">
                    <div class="stat-value">{{ "%.2f"|format(statistics.overall_stats.overall_return_rate) }}%</div>
                    <div class="stat-label">整体收益率</div>
                </div>
            </div>
        </section>

        <!-- 总体统计图表 -->
        {% if charts.overall_pie or charts.overall_bar %}
        <section class="charts-section">
            <h2 class="section-title">📈 总体统计图表</h2>
            <div class="charts-grid">
                {% if charts.overall_pie %}
                <div class="chart-container">
                    {{ charts.overall_pie|safe }}
                </div>
                {% endif %}
                {% if charts.overall_bar %}
                <div class="chart-container">
                    {{ charts.overall_bar|safe }}
                </div>
                {% endif %}
            </div>
        </section>
        {% endif %}

        <!-- Token统计 -->
        {% if statistics.token_stats %}
        <section class="token-section">
            <h2 class="section-title">🪙 Token统计分析</h2>
            
            <!-- Token统计表格 -->
            <div class="table-container">
                <h3 class="subsection-title">Token表现排行榜</h3>
                <table class="stats-table">
                    <thead>
                        <tr>
                            <th>排名</th>
                            <th>Token地址</th>
                            <th>交易数</th>
                            <th>胜率</th>
                            <th>总盈利率</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for token in statistics.token_stats[:20] %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td class="token-address">{{ token_link_helper(token.token_address)|safe }}</td>
                            <td>{{ token.trade_count }}</td>
                            <td class="{{ 'positive' if token.win_rate >= 50 else 'negative' }}">
                                {{ "%.2f"|format(token.win_rate) }}%
                            </td>
                            <td class="{{ 'positive' if token.avg_profit_rate >= 0 else 'negative' }}">
                                {{ "%.2f"|format(token.avg_profit_rate) }}%
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Token图表 -->
            {% if charts.token_performance or charts.token_win_rate %}
            <div class="charts-grid">
                {% if charts.token_performance %}
                <div class="chart-container">
                    {{ charts.token_performance|safe }}
                </div>
                {% endif %}
                {% if charts.token_win_rate %}
                <div class="chart-container">
                    {{ charts.token_win_rate|safe }}
                </div>
                {% endif %}
            </div>
            {% endif %}
        </section>
        {% endif %}

        <!-- 策略统计 -->
        {% if statistics.strategy_stats %}
        <section class="strategy-section">
            <h2 class="section-title">🎯 策略统计分析</h2>
            
            <!-- 策略统计表格 -->
            <div class="table-container">
                <h3 class="subsection-title">策略表现对比</h3>
                <table class="stats-table">
                    <thead>
                        <tr>
                            <th>策略名称</th>
                            <th>交易数</th>
                            <th>胜率</th>
                            <th>总盈利率</th>
                            <th>最大盈利</th>
                            <th>最大亏损</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for strategy in statistics.strategy_stats %}
                        <tr>
                            <td class="strategy-name">{{ strategy.strategy_name }}</td>
                            <td>{{ strategy.trade_count }}</td>
                            <td class="{{ 'positive' if strategy.win_rate >= 50 else 'negative' }}">
                                {{ "%.2f"|format(strategy.win_rate) }}%
                            </td>
                            <td class="{{ 'positive' if strategy.avg_profit_rate >= 0 else 'negative' }}">
                                {{ "%.2f"|format(strategy.avg_profit_rate) }}%
                            </td>
                            <td class="positive">{{ "%.4f"|format(strategy.max_single_profit) }} SOL</td>
                            <td class="negative">{{ "%.4f"|format(strategy.max_single_loss) }} SOL</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 策略图表 -->
            {% if charts.strategy_performance or charts.strategy_comparison %}
            <div class="charts-grid">
                {% if charts.strategy_performance %}
                <div class="chart-container">
                    {{ charts.strategy_performance|safe }}
                </div>
                {% endif %}
                {% if charts.strategy_comparison %}
                <div class="chart-container">
                    {{ charts.strategy_comparison|safe }}
                </div>
                {% endif %}
            </div>
            {% endif %}
        </section>
        {% endif %}

        <!-- 盈利排行榜 -->
        {% if statistics.profit_rankings %}
        <section class="ranking-section">
            <h2 class="section-title">🏆 盈利排行榜</h2>
            
            <div class="table-container">
                <h3 class="subsection-title">最佳盈利交易 (Top 20)</h3>
                <table class="stats-table">
                    <thead>
                        <tr>
                            <th>排名</th>
                            <th>信号ID</th>
                            <th>策略</th>
                            <th>Token地址</th>
                            <th>盈利金额</th>
                            <th>盈利率</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for profit in statistics.profit_rankings[:20] %}
                        <tr>
                            <td class="rank">{{ loop.index }}</td>
                            <td class="signal-id">{{ profit.signal_id }}</td>
                            <td class="strategy-name">{{ profit.strategy_name }}</td>
                            <td class="token-address">{{ token_link_helper(profit.token_address)|safe }}</td>
                            <td class="positive">{{ "%.4f"|format(profit.profit_amount) }} SOL</td>
                            <td class="positive">{{ "%.2f"|format(profit.profit_rate) }}%</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            {% if charts.top_profits %}
            <div class="chart-container">
                {{ charts.top_profits|safe }}
            </div>
            {% endif %}
        </section>
        {% endif %}

        <!-- 亏损排行榜 -->
        {% if statistics.loss_rankings %}
        <section class="ranking-section">
            <h2 class="section-title">📉 亏损排行榜</h2>
            
            <div class="table-container">
                <h3 class="subsection-title">最大亏损交易 (Top 20)</h3>
                <table class="stats-table">
                    <thead>
                        <tr>
                            <th>排名</th>
                            <th>信号ID</th>
                            <th>策略</th>
                            <th>Token地址</th>
                            <th>亏损金额</th>
                            <th>亏损率</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for loss in statistics.loss_rankings[:20] %}
                        <tr>
                            <td class="rank">{{ loop.index }}</td>
                            <td class="signal-id">{{ loss.signal_id }}</td>
                            <td class="strategy-name">{{ loss.strategy_name }}</td>
                            <td class="token-address">{{ token_link_helper(loss.token_address)|safe }}</td>
                            <td class="negative">{{ "%.4f"|format(loss.loss_amount) }} SOL</td>
                            <td class="negative">{{ "%.2f"|format(loss.loss_rate) }}%</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            {% if charts.top_losses %}
            <div class="chart-container">
                {{ charts.top_losses|safe }}
            </div>
            {% endif %}
        </section>
        {% endif %}

        <!-- 盈利亏损分布 -->
        {% if charts.profit_loss_distribution %}
        <section class="distribution-section">
            <h2 class="section-title">📊 盈利亏损分布</h2>
            <div class="single-chart-container">
                <div class="chart-container">
                    {{ charts.profit_loss_distribution|safe }}
                </div>
            </div>
        </section>
        {% endif %}

        <!-- 页面底部 -->
        <footer class="footer">
            <p>© 2024 交易统计分析系统 | 基于链上验证数据生成</p>
        </footer>
    </div>

    <script>
        // 添加表格排序功能
        document.addEventListener('DOMContentLoaded', function() {
            const tables = document.querySelectorAll('.stats-table');
            tables.forEach(table => {
                const headers = table.querySelectorAll('th');
                headers.forEach((header, index) => {
                    header.style.cursor = 'pointer';
                    header.addEventListener('click', () => sortTable(table, index));
                });
            });
        });

        function sortTable(table, column) {
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            
            rows.sort((a, b) => {
                const aVal = a.cells[column].textContent.trim();
                const bVal = b.cells[column].textContent.trim();
                
                // 尝试数值比较
                const aNum = parseFloat(aVal.replace(/[^0-9.-]/g, ''));
                const bNum = parseFloat(bVal.replace(/[^0-9.-]/g, ''));
                
                if (!isNaN(aNum) && !isNaN(bNum)) {
                    return bNum - aNum; // 降序
                }
                
                // 字符串比较
                return aVal.localeCompare(bVal);
            });
            
            rows.forEach(row => tbody.appendChild(row));
        }
    </script>
</body>
</html>
        """.replace('{{ self._get_css_styles() }}', self._get_css_styles())
    
    def _get_css_styles(self) -> str:
        """
        获取CSS样式
        
        Returns:
            CSS样式字符串
        """
        return """
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background-color: #f8f9fa;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 页面头部 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-align: center;
        }

        .meta-info {
            text-align: center;
            opacity: 0.9;
        }

        .generated-time {
            font-size: 1.1rem;
            display: block;
            margin-bottom: 10px;
        }

        .config-info {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .config-info span {
            background: rgba(255, 255, 255, 0.2);
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9rem;
        }

        /* 章节样式 */
        .section-title {
            font-size: 1.8rem;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #3498db;
        }

        .subsection-title {
            font-size: 1.3rem;
            color: #34495e;
            margin-bottom: 15px;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 1rem;
            color: #7f8c8d;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .stat-card.positive .stat-value {
            color: #27ae60;
        }

        .stat-card.negative .stat-value {
            color: #e74c3c;
        }

        /* 表格样式 */
        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .stats-table {
            width: 100%;
            border-collapse: collapse;
        }

        .stats-table th {
            background: #34495e;
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 0.9rem;
        }

        .stats-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #ecf0f1;
        }

        .stats-table tr:hover {
            background-color: #f8f9fa;
        }

        .stats-table tr:nth-child(even) {
            background-color: #fdfdfd;
        }

        .positive {
            color: #27ae60 !important;
            font-weight: 600;
        }

        .negative {
            color: #e74c3c !important;
            font-weight: 600;
        }

        .token-address, .signal-id {
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            background: #ecf0f1;
            padding: 2px 6px;
            border-radius: 4px;
        }
        
        /* Token链接样式 */
        .token-link {
            color: #3498db;
            text-decoration: none;
            transition: all 0.3s ease;
            border-bottom: 1px dotted #3498db;
        }
        
        .token-link:hover {
            color: #2980b9;
            text-decoration: none;
            border-bottom: 1px solid #2980b9;
            background-color: rgba(52, 152, 219, 0.1);
        }
        
        .token-link:visited {
            color: #8e44ad;
        }

        .strategy-name {
            font-weight: 600;
            color: #2980b9;
        }

        .rank {
            font-weight: 700;
            color: #f39c12;
            text-align: center;
        }

        /* 图表样式 - 修复布局问题 */
        .charts-section, .token-section, .strategy-section, 
        .ranking-section, .distribution-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* 修复图表网格布局 */
        .charts-grid {
            display: grid;
            gap: 30px;
            margin-top: 20px;
        }

        /* 大屏幕：2列布局 */
        @media (min-width: 1200px) {
            .charts-grid {
                grid-template-columns: 1fr 1fr;
            }
        }

        /* 中等屏幕：1列布局 */
        @media (max-width: 1199px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
        }

        .chart-container {
            background: #fafafa;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e1e8ed;
            min-height: 400px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            overflow: hidden;
        }

        /* 确保图表内容不溢出 */
        .chart-container > div {
            width: 100% !important;
            height: auto !important;
            max-width: 100%;
        }

        /* 单独的图表容器样式 */
        .single-chart-container {
            width: 100%;
            margin-bottom: 30px;
        }

        .single-chart-container .chart-container {
            min-height: 500px;
        }

        /* 页面底部 */
        .footer {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
            border-top: 1px solid #ecf0f1;
            margin-top: 40px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
                max-width: 100%;
            }

            .title {
                font-size: 2rem;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 15px;
            }

            .charts-grid {
                grid-template-columns: 1fr !important;
                gap: 20px;
            }

            .chart-container {
                padding: 15px;
                min-height: 300px;
            }

            .config-info {
                flex-direction: column;
                gap: 10px;
            }

            .stats-table {
                font-size: 0.8rem;
            }

            .stats-table th,
            .stats-table td {
                padding: 8px;
            }

            .charts-section, .token-section, .strategy-section, 
            .ranking-section, .distribution-section {
                padding: 20px;
            }
        }

        @media (max-width: 480px) {
            .header {
                padding: 20px;
            }

            .title {
                font-size: 1.5rem;
            }

            .section-title {
                font-size: 1.4rem;
            }

            .stat-value {
                font-size: 2rem;
            }

            .chart-container {
                padding: 10px;
                min-height: 250px;
            }
        }

        /* 打印样式 */
        @media print {
            body {
                background: white;
            }

            .header {
                background: #667eea !important;
                -webkit-print-color-adjust: exact;
            }

            .chart-container {
                break-inside: avoid;
                page-break-inside: avoid;
            }

            .stats-table {
                break-inside: avoid;
                page-break-inside: avoid;
            }

            .charts-grid {
                grid-template-columns: 1fr !important;
            }
        }
        """ 