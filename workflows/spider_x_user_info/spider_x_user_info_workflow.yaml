name: 'X用户信息爬取工作流'
description: '从数据库获取需要监控的用户列表，爬取每个用户的信息和推文，并将数据存储到数据库'

nodes:
  - name: "XUserSchedulerNode"
    node_type: "input"
    interval: 600  # 每分钟检查一次
    generate_data: workflows.spider_x_user_info.handler.generate_user_list
    flow_control:
      max_pending_messages: 10
      check_interval: 10
      enable_flow_control: true

  - name: "SpiderXUserInfoNode"
    node_type: "process"
    depend_ons: ["XUserSchedulerNode"]
    min_request_interval: 0.5  # 最小请求间隔（秒）
    max_request_interval: 2.0  # 最大请求间隔（秒）
    batch_size: 3  # 每批处理的用户数量
    batch_interval: 1.0  # 批次间隔（秒）
    item_interval: 0.5  # 用户处理间隔（秒）
    enable_streaming: true  # 启用流式处理
    process_item: workflows.spider_x_user_info.handler.process_user_info
    flow_control:
      max_pending_messages: 50
      check_interval: 10
      enable_flow_control: true

  - name: "XUserStorageNode"
    node_type: "storage"
    depend_ons: ["SpiderXUserInfoNode"]
    batch_size: 5  # 每批处理5条数据
    store_data: workflows.spider_x_user_info.handler.store_user_data
    validate: workflows.spider_x_user_info.handler.validate_user_data 