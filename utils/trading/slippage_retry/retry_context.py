"""重试上下文 - 跟踪重试过程中的状态和参数变化"""

from typing import List, Optional, Dict, Any
from datetime import datetime
import logging
from models.slippage_retry import (
    SlippageAdjustmentRecord, 
    RetryDecision, 
    SlippageRetryConfig,
    SlippageAdjustmentReason
)

logger = logging.getLogger(__name__)


class RetryContext:
    """重试上下文 - 跟踪整个重试过程的状态和历史"""
    
    def __init__(
        self,
        trade_type: str,
        initial_slippage: float,
        config: SlippageRetryConfig,
        max_retries: int = 3
    ):
        """
        初始化重试上下文
        
        Args:
            trade_type: 交易类型 (buy/sell)
            initial_slippage: 初始滑点百分比
            config: 滑点重试配置
            max_retries: 最大重试次数
        """
        self.trade_type = trade_type
        self.initial_slippage = initial_slippage
        self.current_slippage = initial_slippage
        self.config = config
        self.max_retries = max_retries
        
        # 重试状态
        self.retry_count = 0
        self.total_adjustments = 0
        self.slippage_at_limit = False
        
        # 历史记录
        self.adjustment_history: List[SlippageAdjustmentRecord] = []
        self.decision_history: List[RetryDecision] = []
        
        # 时间跟踪
        self.start_time = datetime.utcnow()
        self.last_attempt_time: Optional[datetime] = None
        
        logger.debug(f"初始化重试上下文: {trade_type.upper()}, 初始滑点={initial_slippage}%")
    
    def record_slippage_adjustment(
        self,
        new_slippage: float,
        reason: SlippageAdjustmentReason,
        error_message: Optional[str] = None
    ) -> SlippageAdjustmentRecord:
        """
        记录滑点调整
        
        Args:
            new_slippage: 调整后的滑点
            reason: 调整原因
            error_message: 触发调整的错误信息
            
        Returns:
            SlippageAdjustmentRecord: 调整记录
        """
        previous_slippage = self.current_slippage
        increment_applied = new_slippage - previous_slippage
        
        # 创建调整记录
        adjustment_record = SlippageAdjustmentRecord(
            adjustment_time=datetime.utcnow(),
            reason=reason,
            trade_type=self.trade_type,
            previous_slippage=previous_slippage,
            new_slippage=new_slippage,
            increment_applied=increment_applied,
            original_error_message=error_message,
            retry_attempt=self.retry_count + 1,
            max_slippage_limit=self.config.max_slippage_percentage,
            increment_step=self.config.increment_percentage
        )
        
        # 更新状态
        self.current_slippage = new_slippage
        self.total_adjustments += 1
        self.slippage_at_limit = (new_slippage >= self.config.max_slippage_percentage)
        
        # 添加到历史
        self.adjustment_history.append(adjustment_record)
        
        logger.info(f"记录滑点调整: {adjustment_record.adjustment_summary}")
        return adjustment_record
    
    def record_retry_decision(self, decision: RetryDecision) -> None:
        """
        记录重试决策
        
        Args:
            decision: 重试决策结果
        """
        self.decision_history.append(decision)
        logger.debug(f"记录重试决策: {decision.decision_summary}")
    
    def increment_retry_count(self) -> int:
        """
        增加重试次数
        
        Returns:
            int: 当前重试次数
        """
        self.retry_count += 1
        self.last_attempt_time = datetime.utcnow()
        logger.debug(f"重试次数增加至: {self.retry_count}/{self.max_retries}")
        return self.retry_count
    
    def get_total_slippage_increase(self) -> float:
        """
        获取累计滑点增加量
        
        Returns:
            float: 累计滑点增加百分比
        """
        return self.current_slippage - self.initial_slippage
    
    def get_retry_duration(self) -> float:
        """
        获取重试总耗时
        
        Returns:
            float: 重试总耗时（秒）
        """
        end_time = self.last_attempt_time or datetime.utcnow()
        return (end_time - self.start_time).total_seconds()
    
    def is_retry_exhausted(self) -> bool:
        """
        检查是否已耗尽重试次数
        
        Returns:
            bool: 是否已耗尽重试次数
        """
        return self.retry_count >= self.max_retries
    
    def can_adjust_slippage(self) -> bool:
        """
        检查是否还能调整滑点
        
        Returns:
            bool: 是否还能调整滑点
        """
        if not self.config.enabled:
            return False
        return self.current_slippage < self.config.max_slippage_percentage
    
    def get_next_slippage_preview(self) -> Optional[float]:
        """
        预览下次调整后的滑点值
        
        Returns:
            Optional[float]: 预览的滑点值，如果无法调整则返回None
        """
        if not self.can_adjust_slippage():
            return None
        
        next_slippage = self.current_slippage + self.config.increment_percentage
        return min(next_slippage, self.config.max_slippage_percentage)
    
    def get_summary(self) -> Dict[str, Any]:
        """
        获取重试上下文摘要
        
        Returns:
            Dict[str, Any]: 上下文摘要
        """
        return {
            "trade_type": self.trade_type,
            "retry_count": self.retry_count,
            "max_retries": self.max_retries,
            "initial_slippage": self.initial_slippage,
            "current_slippage": self.current_slippage,
            "total_slippage_increase": self.get_total_slippage_increase(),
            "total_adjustments": self.total_adjustments,
            "slippage_at_limit": self.slippage_at_limit,
            "retry_duration_seconds": self.get_retry_duration(),
            "config_enabled": self.config.enabled,
            "max_slippage_limit": self.config.max_slippage_percentage,
            "can_adjust_more": self.can_adjust_slippage(),
            "retry_exhausted": self.is_retry_exhausted()
        }
    
    def get_detailed_report(self) -> str:
        """
        获取详细的重试报告
        
        Returns:
            str: 详细报告
        """
        summary = self.get_summary()
        
        report_lines = [
            f"=== {self.trade_type.upper()} 重试上下文报告 ===",
            f"重试进度: {summary['retry_count']}/{summary['max_retries']}",
            f"滑点变化: {summary['initial_slippage']}% → {summary['current_slippage']}%",
            f"累计增加: {summary['total_slippage_increase']:.2f}%",
            f"调整次数: {summary['total_adjustments']}",
            f"耗时: {summary['retry_duration_seconds']:.2f}秒",
            f"状态: {'滑点已达上限' if summary['slippage_at_limit'] else '滑点未达上限'}",
            f"可继续调整: {'是' if summary['can_adjust_more'] else '否'}",
            f"重试已耗尽: {'是' if summary['retry_exhausted'] else '否'}"
        ]
        
        # 添加调整历史
        if self.adjustment_history:
            report_lines.append("\n--- 滑点调整历史 ---")
            for i, adjustment in enumerate(self.adjustment_history, 1):
                report_lines.append(f"{i}. {adjustment.adjustment_summary}")
        
        # 添加决策历史
        if self.decision_history:
            report_lines.append("\n--- 重试决策历史 ---")
            for i, decision in enumerate(self.decision_history, 1):
                report_lines.append(f"{i}. {decision.decision_summary}")
        
        return "\n".join(report_lines)
    
    def __str__(self) -> str:
        """字符串表示"""
        return (
            f"RetryContext({self.trade_type.upper()}: "
            f"{self.retry_count}/{self.max_retries} retries, "
            f"slippage {self.initial_slippage}%→{self.current_slippage}%)"
        ) 