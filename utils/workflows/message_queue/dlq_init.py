"""
死信队列初始化模块

提供全局死信队列实例和管理器
"""

from utils.workflows.message_queue.message_queue import KafkaQueue
from utils.workflows.message_queue.dead_letter_queue import (
    DeadLetterQueueManager, 
    DeadLetterType, 
    KafkaDeadLetterQueue
)

# 处理失败死信队列
process_failure_dlq = KafkaDeadLetterQueue(
    kafka_queue=KafkaQueue(
        name='process_failure_dlq',
    ),
    dlq_type=DeadLetterType.PROCESS_FAILURE
)

# 存储失败死信队列
storage_failure_dlq = KafkaDeadLetterQueue(
    kafka_queue=KafkaQueue(
        name='storage_failure_dlq',
    ),
    dlq_type=DeadLetterType.STORAGE_FAILURE
)

# 死信队列管理器
dlq_manager = DeadLetterQueueManager()
dlq_manager.register_queue(DeadLetterType.PROCESS_FAILURE, process_failure_dlq)
dlq_manager.register_queue(DeadLetterType.STORAGE_FAILURE, storage_failure_dlq)

# 导出全局实例
__all__ = [
    'dlq_manager'
] 