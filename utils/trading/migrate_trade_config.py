"""
自动交易配置迁移工具

支持：
1. 从现有策略配置中提取交易相关字段
2. 生成统一的auto_trade_manager配置
3. 保留配置迁移历史记录
4. 支持回滚和验证
"""

import logging
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple

from dao.config_dao import ConfigDAO
from models.config import (
    Config,
    KolActivityConfig,
    SingleKolStrategyConfig,
    WalletConfig,
    TradingParams,
    TradeChannelConfig,
    NotificationConfig,
    AutoTradeConfig,
    AutoTradeManagerConfig
)
from utils.trading.init_auto_trade_config import AutoTradeConfigInitializer

logger = logging.getLogger(__name__)


class TradingConfigMigrator:
    """交易配置迁移器"""
    
    def __init__(self):
        self.config_dao = ConfigDAO()
        self.initializer = AutoTradeConfigInitializer()
    
    async def migrate_from_kol_activity_config(
        self,
        source_config_name: str = "kol_activity",
        target_config_name: str = "auto_trade_manager",
        preserve_source: bool = True,
        create_backup: bool = True
    ) -> Dict[str, Any]:
        """
        从kol_activity配置迁移交易配置到auto_trade_manager
        
        Args:
            source_config_name: 源配置名称
            target_config_name: 目标配置名称
            preserve_source: 是否保留源配置
            create_backup: 是否创建备份
            
        Returns:
            Dict[str, Any]: 迁移结果
        """
        logger.info(f"开始迁移配置：{source_config_name} -> {target_config_name}")
        
        migration_result = {
            "success": False,
            "source_config_found": False,
            "migration_type": "unknown",
            "strategies_migrated": 0,
            "channels_created": 0,
            "warnings": [],
            "errors": [],
            "backup_created": False,
            "target_config_id": None,
            "migration_timestamp": datetime.now().isoformat()
        }
        
        try:
            # 1. 获取源配置
            source_config = await self.config_dao.get_config(source_config_name)
            if not source_config:
                migration_result["errors"].append(f"源配置 '{source_config_name}' 不存在")
                return migration_result
            
            migration_result["source_config_found"] = True
            
            # 2. 创建备份（如果需要）
            if create_backup:
                backup_result = await self._create_backup(source_config, source_config_name)
                migration_result["backup_created"] = backup_result["success"]
                if not backup_result["success"]:
                    migration_result["warnings"].append(f"备份创建失败: {backup_result.get('error', '未知错误')}")
            
            # 3. 分析配置类型并迁移
            if isinstance(source_config.data, KolActivityConfig):
                # 新格式：多策略配置
                migration_result["migration_type"] = "multi_strategy"
                result = await self._migrate_from_multi_strategy_config(
                    source_config.data, target_config_name
                )
            elif isinstance(source_config.data, SingleKolStrategyConfig):
                # 旧格式：单策略配置
                migration_result["migration_type"] = "single_strategy"
                result = await self._migrate_from_single_strategy_config(
                    source_config.data, target_config_name
                )
            else:
                # 尝试解析为SingleKolStrategyConfig（向后兼容）
                try:
                    strategy_config = SingleKolStrategyConfig(**source_config.data.model_dump())
                    migration_result["migration_type"] = "single_strategy_parsed"
                    result = await self._migrate_from_single_strategy_config(
                        strategy_config, target_config_name
                    )
                except Exception as e:
                    migration_result["errors"].append(f"无法解析源配置格式: {str(e)}")
                    return migration_result
            
            # 4. 更新迁移结果
            migration_result.update(result)
            
            # 5. 清理源配置中的交易字段（如果不保留源配置）
            if not preserve_source and migration_result["success"]:
                cleanup_result = await self._cleanup_source_config(source_config, source_config_name)
                if not cleanup_result["success"]:
                    migration_result["warnings"].append(f"清理源配置失败: {cleanup_result.get('error', '未知错误')}")
            
            logger.info(f"配置迁移完成：success={migration_result['success']}, "
                       f"strategies={migration_result['strategies_migrated']}, "
                       f"channels={migration_result['channels_created']}")
            
        except Exception as e:
            logger.error(f"配置迁移失败: {e}", exc_info=True)
            migration_result["errors"].append(f"迁移过程异常: {str(e)}")
        
        return migration_result
    
    async def _migrate_from_multi_strategy_config(
        self,
        kol_activity_config: KolActivityConfig,
        target_config_name: str
    ) -> Dict[str, Any]:
        """从多策略配置迁移"""
        result = {
            "success": False,
            "strategies_migrated": 0,
            "channels_created": 0,
            "warnings": [],
            "errors": [],
            "target_config_id": None
        }
        
        try:
            # 提取交易配置信息
            trade_configs = []
            unique_channels = {}
            
            for strategy in kol_activity_config.buy_strategies:
                if hasattr(strategy, 'auto_trade_enabled') and strategy.auto_trade_enabled:
                    extracted = self._extract_trading_config_from_strategy(strategy)
                    trade_configs.append(extracted)
                    
                    # 收集唯一的渠道配置
                    channel_key = f"{extracted['channel_type']}_{extracted['channel_config_hash']}"
                    if channel_key not in unique_channels:
                        unique_channels[channel_key] = extracted
            
            result["strategies_migrated"] = len(trade_configs)
            
            if not trade_configs:
                result["warnings"].append("没有找到启用自动交易的策略")
                # 创建默认配置
                config = await self.initializer.initialize_default_config()
                result["target_config_id"] = str(config.id)
                result["success"] = True
                return result
            
            # 生成聚合的auto_trade_manager配置
            auto_trade_config = self._generate_aggregated_config(unique_channels)
            result["channels_created"] = len(auto_trade_config["channels"])
            
            # 保存配置
            config = Config(
                config_type=target_config_name,
                data=AutoTradeManagerConfig(**auto_trade_config),
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            await self.config_dao.save_config(config)
            result["target_config_id"] = str(config.id)
            result["success"] = True
            
        except Exception as e:
            logger.error(f"多策略配置迁移失败: {e}", exc_info=True)
            result["errors"].append(f"多策略迁移异常: {str(e)}")
        
        return result
    
    async def _migrate_from_single_strategy_config(
        self,
        strategy_config: SingleKolStrategyConfig,
        target_config_name: str
    ) -> Dict[str, Any]:
        """从单策略配置迁移"""
        result = {
            "success": False,
            "strategies_migrated": 0,
            "channels_created": 0,
            "warnings": [],
            "errors": [],
            "target_config_id": None
        }
        
        try:
            # 检查是否启用自动交易
            if not hasattr(strategy_config, 'auto_trade_enabled') or not strategy_config.auto_trade_enabled:
                result["warnings"].append("策略未启用自动交易")
                # 创建默认配置
                config = await self.initializer.initialize_default_config()
                result["target_config_id"] = str(config.id)
                result["success"] = True
                return result
            
            # 提取交易配置
            extracted = self._extract_trading_config_from_strategy(strategy_config)
            result["strategies_migrated"] = 1
            
            # 生成auto_trade_manager配置
            auto_trade_config = self._generate_config_from_single_strategy(extracted)
            result["channels_created"] = len(auto_trade_config["channels"])
            
            # 保存配置
            config = Config(
                config_type=target_config_name,
                data=AutoTradeManagerConfig(**auto_trade_config),
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            await self.config_dao.save_config(config)
            result["target_config_id"] = str(config.id)
            result["success"] = True
            
        except Exception as e:
            logger.error(f"单策略配置迁移失败: {e}", exc_info=True)
            result["errors"].append(f"单策略迁移异常: {str(e)}")
        
        return result
    
    def _extract_trading_config_from_strategy(self, strategy: SingleKolStrategyConfig) -> Dict[str, Any]:
        """从策略配置中提取交易相关配置"""
        extracted = {
            "strategy_name": strategy.strategy_name,
            "enabled": getattr(strategy, 'auto_trade_enabled', False),
            
            # 钱包配置
            "private_key_env_var": getattr(strategy, 'gmgn_private_key_env_var', None),
            "wallet_address": getattr(strategy, 'gmgn_sol_wallet_address', None),
            
            # 交易参数
            "buy_amount_sol": getattr(strategy, 'gmgn_auto_trade_buy_amount_sol', 0.01),
            "buy_slippage_percentage": getattr(strategy, 'gmgn_buy_slippage_percentage', 10.0),
            "buy_priority_fee": getattr(strategy, 'gmgn_buy_priority_fee', 0.0005),
            "sell_slippage_percentage": getattr(strategy, 'gmgn_sell_slippage_percentage', 10.0),
            "sell_priority_fee": getattr(strategy, 'gmgn_sell_priority_fee', 0.0005),
            
            # 渠道配置
            "channel_type": "jupiter" if getattr(strategy, 'use_direct_solana_trading', False) else "gmgn",
            "gmgn_api_host": getattr(strategy, 'gmgn_api_host', None),
            "gmgn_max_retries": getattr(strategy, 'gmgn_max_retries', 3),
            "jupiter_api_host": getattr(strategy, 'jupiter_api_host', None),
            "solana_rpc_url": getattr(strategy, 'solana_rpc_url', None),
            
            # 通知配置
            "include_trade_details": getattr(strategy, 'include_trade_details_in_notification', True)
        }
        
        # 生成渠道配置哈希（用于去重）
        channel_params = {
            "type": extracted["channel_type"],
            "gmgn_api_host": extracted["gmgn_api_host"],
            "jupiter_api_host": extracted["jupiter_api_host"],
            "solana_rpc_url": extracted["solana_rpc_url"]
        }
        extracted["channel_config_hash"] = str(hash(str(sorted(channel_params.items()))))
        
        return extracted
    
    def _generate_config_from_single_strategy(self, extracted: Dict[str, Any]) -> Dict[str, Any]:
        """从单个策略的提取配置生成auto_trade_manager配置"""
        # 钱包配置
        wallet_config = WalletConfig(
            default_private_key_env_var=extracted["private_key_env_var"] or "SOLANA_WALLET_PRIVATE_KEY",
            default_wallet_address=extracted["wallet_address"] or ""
        )
        
        # 交易参数
        trading_params = TradingParams(
            default_buy_amount_sol=extracted["buy_amount_sol"],
            default_buy_slippage_percentage=extracted["buy_slippage_percentage"],
            default_buy_priority_fee_sol=extracted["buy_priority_fee"],
            default_sell_slippage_percentage=extracted["sell_slippage_percentage"],
            default_sell_priority_fee_sol=extracted["sell_priority_fee"]
        )
        
        # 渠道配置
        channels = []
        if extracted["channel_type"] == "gmgn":
            channels.append(TradeChannelConfig(
                channel_type="gmgn",
                enabled=True,
                priority=1,
                timeout_seconds=60,
                max_retries=extracted["gmgn_max_retries"],
                trading_params=trading_params,
                channel_params={
                    "gmgn_api_host": extracted["gmgn_api_host"] or "https://gmgn.ai",
                    "min_amount": 0.001,
                    "max_amount": 10.0
                }
            ))
        elif extracted["channel_type"] == "jupiter":
            channels.append(TradeChannelConfig(
                channel_type="jupiter",
                enabled=True,
                priority=1,
                timeout_seconds=90,
                max_retries=2,
                trading_params=trading_params,
                channel_params={
                    "jupiter_api_host": extracted["jupiter_api_host"] or "https://quote-api.jup.ag",
                    "solana_rpc_url": extracted["solana_rpc_url"] or "https://api.mainnet-beta.solana.com",
                    "min_amount": 0.001,
                    "max_amount": 5.0
                }
            ))
        
        # 通知配置
        notification_config = NotificationConfig(
            include_trade_details=extracted["include_trade_details"],
            notify_on_success=True,
            notify_on_failure=True
        )
        
        return {
            "enabled": extracted["enabled"],
            "wallet_config": wallet_config,
            "trading_params": trading_params,
            "channels": channels,
            "notification_config": notification_config,
            "max_concurrent_trades": 5,
            "trade_timeout_seconds": 120
        }
    
    def _generate_aggregated_config(self, unique_channels: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """从多个唯一渠道配置生成聚合的auto_trade_manager配置"""
        # 分析最常用的配置作为默认值
        all_configs = list(unique_channels.values())
        
        # 聚合钱包配置（使用第一个非空的）
        default_private_key_env_var = "SOLANA_WALLET_PRIVATE_KEY"
        default_wallet_address = ""
        
        for config in all_configs:
            if config["private_key_env_var"]:
                default_private_key_env_var = config["private_key_env_var"]
                break
        
        for config in all_configs:
            if config["wallet_address"]:
                default_wallet_address = config["wallet_address"]
                break
        
        wallet_config = WalletConfig(
            default_private_key_env_var=default_private_key_env_var,
            default_wallet_address=default_wallet_address
        )
        
        # 聚合交易参数（使用平均值或最常见值）
        buy_amounts = [c["buy_amount_sol"] for c in all_configs]
        avg_buy_amount = sum(buy_amounts) / len(buy_amounts)
        
        trading_params = TradingParams(
            default_buy_amount_sol=avg_buy_amount,
            default_buy_slippage_percentage=10.0,  # 使用安全默认值
            default_buy_priority_fee_sol=0.0005,
            default_sell_slippage_percentage=10.0,
            default_sell_priority_fee_sol=0.0005
        )
        
        # 生成渠道配置
        channels = []
        channel_priority = 1
        
        for channel_key, config in unique_channels.items():
            if config["channel_type"] == "gmgn":
                channels.append(TradeChannelConfig(
                    channel_type="gmgn",
                    enabled=True,
                    priority=channel_priority,
                    timeout_seconds=60,
                    max_retries=config["gmgn_max_retries"],
                    trading_params=TradingParams(
                        default_buy_amount_sol=config["buy_amount_sol"],
                        default_buy_slippage_percentage=config["buy_slippage_percentage"],
                        default_buy_priority_fee_sol=config["buy_priority_fee"],
                        default_sell_slippage_percentage=config["sell_slippage_percentage"],
                        default_sell_priority_fee_sol=config["sell_priority_fee"]
                    ),
                    channel_params={
                        "gmgn_api_host": config["gmgn_api_host"] or "https://gmgn.ai",
                        "min_amount": 0.001,
                        "max_amount": 10.0
                    }
                ))
            elif config["channel_type"] == "jupiter":
                channels.append(TradeChannelConfig(
                    channel_type="jupiter",
                    enabled=True,
                    priority=channel_priority,
                    timeout_seconds=90,
                    max_retries=2,
                    trading_params=TradingParams(
                        default_buy_amount_sol=config["buy_amount_sol"],
                        default_buy_slippage_percentage=config["buy_slippage_percentage"],
                        default_buy_priority_fee_sol=config["buy_priority_fee"],
                        default_sell_slippage_percentage=config["sell_slippage_percentage"],
                        default_sell_priority_fee_sol=config["sell_priority_fee"]
                    ),
                    channel_params={
                        "jupiter_api_host": config["jupiter_api_host"] or "https://quote-api.jup.ag",
                        "solana_rpc_url": config["solana_rpc_url"] or "https://api.mainnet-beta.solana.com",
                        "min_amount": 0.001,
                        "max_amount": 5.0
                    }
                ))
            channel_priority += 1
        
        # 通知配置
        notification_config = NotificationConfig(
            include_trade_details=True,
            notify_on_success=True,
            notify_on_failure=True
        )
        
        return {
            "enabled": True,
            "wallet_config": wallet_config,
            "trading_params": trading_params,
            "channels": channels,
            "notification_config": notification_config,
            "max_concurrent_trades": 5,
            "trade_timeout_seconds": 120
        }
    
    async def _create_backup(self, config: Config, config_name: str) -> Dict[str, Any]:
        """创建配置备份"""
        try:
            backup_name = f"{config_name}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            backup_config = Config(
                config_type=backup_name,
                data=config.data,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            await self.config_dao.save_config(backup_config)
            logger.info(f"配置备份已创建: {backup_name}")
            
            return {
                "success": True,
                "backup_name": backup_name,
                "backup_id": str(backup_config.id)
            }
            
        except Exception as e:
            logger.error(f"创建配置备份失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _cleanup_source_config(self, source_config: Config, config_name: str) -> Dict[str, Any]:
        """清理源配置中的交易相关字段"""
        try:
            # 这里需要根据具体的配置结构来实现清理逻辑
            # 由于字段已经移动到AutoTradeManager，这里主要是清理或标记
            logger.info(f"源配置 '{config_name}' 清理完成（交易字段已迁移）")
            
            return {"success": True}
            
        except Exception as e:
            logger.error(f"清理源配置失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def rollback_migration(self, backup_name: str, target_config_name: str) -> Dict[str, Any]:
        """回滚迁移操作"""
        try:
            logger.info(f"开始回滚迁移: {backup_name} -> {target_config_name}")
            
            # 获取备份配置
            backup_config = await self.config_dao.get_config(backup_name)
            if not backup_config:
                return {
                    "success": False,
                    "error": f"备份配置 '{backup_name}' 不存在"
                }
            
            # 恢复到目标位置
            restored_config = Config(
                config_type=target_config_name,
                data=backup_config.data,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            await self.config_dao.save_config(restored_config)
            logger.info(f"配置回滚完成: {target_config_name}")
            
            return {
                "success": True,
                "restored_config_id": str(restored_config.id)
            }
            
        except Exception as e:
            logger.error(f"配置回滚失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }


# 便捷函数
async def migrate_trading_config(
    source_config: str = "kol_activity",
    target_config: str = "auto_trade_manager",
    preserve_source: bool = True
) -> Dict[str, Any]:
    """
    便捷函数：迁移交易配置
    
    Args:
        source_config: 源配置名称
        target_config: 目标配置名称
        preserve_source: 是否保留源配置
        
    Returns:
        Dict[str, Any]: 迁移结果
    """
    migrator = TradingConfigMigrator()
    return await migrator.migrate_from_kol_activity_config(
        source_config_name=source_config,
        target_config_name=target_config,
        preserve_source=preserve_source
    ) 