from datetime import datetime
from beanie import Document, Indexed
from typing import Optional
from pydantic import Field


class SolanaMonitorAddress(Document):
    """Solana 监控地址模型"""
    
    address: Indexed(str) = Field(description="Solana钱包地址", unique=True)
    name: Optional[str] = Field(default=None, description="地址备注名称")
    description: Optional[str] = Field(default=None, description="地址描述")
    
    class Settings:
        name = "solana_monitor_addresses"
        
    class Config:
        json_schema_extra = {
            "example": {
                "address": "2j3MGgjTZnf5woD1dV9XScaSy5SxPeKh5eTTzcpZ142z",
                "name": "Whale Wallet 1",
                "description": "大户钱包，经常交易meme代币"
            }
        }
        