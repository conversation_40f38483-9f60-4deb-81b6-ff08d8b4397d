"""
SolanaMonitor.get_token_balance 方法的单元测试

测试Bug：当代币账户数据为整数列表格式时，方法返回错误的余额0.0
"""

import unittest
import asyncio
import json
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, Any

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from utils.spiders.solana.solana_monitor import SolanaMonitor


class TestSolanaMonitorTokenBalance(unittest.IsolatedAsyncioTestCase):
    """SolanaMonitor代币余额获取测试类"""
    
    def setUp(self):
        """测试前置设置"""
        self.monitor = SolanaMonitor()
        
        # 测试用的钱包和代币地址（来自实际Bug报告）
        self.wallet_address = "AHnQjWa4jxYNxzfJantHDpcUKAE9pzVSGGPLhthPxCSW"
        self.token_mint = "FMT9JbZ7DPSesSxM6vCnqYhwsG85cMeuMPvv5Cvrpump"
    
    def create_mock_token_account_integer_list_format(self) -> Dict[str, Any]:
        """创建整数列表格式的代币账户数据（会导致Bug的格式）"""
        # 这是实际从Solana RPC返回的数据格式
        # data字段是一个包含165个整数的列表，代表原始字节数据
        return {
            "data": [
                0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                # 前32字节是mint地址
                0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                # 32-64字节是owner地址
                170, 100, 124, 9, 0, 0, 0, 0,  # 64-71字节是amount (*********)
                # 后续字节是其他字段
                0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                0, 0, 0, 0, 0
            ],
            "executable": False,
            "lamports": 2039280,
            "owner": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
            "rentEpoch": 18446744073709551615,
            "space": 165
        }
    
    def create_mock_token_account_parsed_format(self) -> Dict[str, Any]:
        """创建解析格式的代币账户数据（正常工作的格式）"""
        return {
            "data": {
                "parsed": {
                    "info": {
                        "isNative": False,
                        "mint": self.token_mint,
                        "owner": self.wallet_address,
                        "state": "initialized",
                        "tokenAmount": {
                            "amount": "*********",
                            "decimals": 6,
                            "uiAmount": 159.147178,
                            "uiAmountString": "159.147178"
                        }
                    },
                    "type": "account"
                },
                "program": "spl-token",
                "space": 165
            },
            "executable": False,
            "lamports": 2039280,
            "owner": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
            "rentEpoch": 18446744073709551615
        }
    
    def create_mock_account_response(self, account_data: Dict[str, Any]):
        """创建模拟的账户响应"""
        mock_account = Mock()
        mock_account.account.to_json.return_value = json.dumps(account_data)
        mock_account.pubkey = "mock_token_account_address"
        
        mock_response = Mock()
        mock_response.value = [mock_account]
        
        return mock_response
    
    async def test_get_token_balance_with_integer_list_format_now_works(self):
        """测试修复后整数列表格式的数据能正确解析（之前这会导致Bug）"""
        # 创建整数列表格式的账户数据
        integer_list_account_data = self.create_mock_token_account_integer_list_format()
        mock_response = self.create_mock_account_response(integer_list_account_data)
        
        # Mock RPC客户端的响应
        with patch.object(self.monitor.client, 'get_token_accounts_by_owner', return_value=mock_response), \
             patch.object(self.monitor, '_wait_for_rate_limit'):
            
            # 执行测试
            balance = await self.monitor.get_token_balance(self.wallet_address, self.token_mint)
            
            # 验证修复：现在应该返回正确的余额而不是0.0
            self.assertEqual(balance, 159.147178, "修复后：整数列表格式的数据应该返回正确余额")
    
    async def test_get_token_balance_with_parsed_format_works_correctly(self):
        """测试解析格式的数据能正常工作"""
        # 创建解析格式的账户数据
        parsed_account_data = self.create_mock_token_account_parsed_format()
        mock_response = self.create_mock_account_response(parsed_account_data)
        
        # Mock RPC客户端的响应
        with patch.object(self.monitor.client, 'get_token_accounts_by_owner', return_value=mock_response), \
             patch.object(self.monitor, '_wait_for_rate_limit'):
            
            # 执行测试
            balance = await self.monitor.get_token_balance(self.wallet_address, self.token_mint)
            
            # 验证正常行为：应该返回正确的余额
            self.assertEqual(balance, 159.147178, "解析格式的数据应该返回正确的余额")
    
    async def test_get_token_balance_no_accounts_returns_zero(self):
        """测试没有代币账户时返回0"""
        # 创建空的响应
        mock_response = Mock()
        mock_response.value = []
        
        # Mock RPC客户端的响应
        with patch.object(self.monitor.client, 'get_token_accounts_by_owner', return_value=mock_response), \
             patch.object(self.monitor, '_wait_for_rate_limit'):
            
            # 执行测试
            balance = await self.monitor.get_token_balance(self.wallet_address, self.token_mint)
            
            # 验证：没有账户时应该返回0
            self.assertEqual(balance, 0.0, "没有代币账户时应该返回0")
    
    async def test_get_token_balance_exception_handling(self):
        """测试异常处理"""
        # Mock RPC客户端抛出异常
        with patch.object(self.monitor.client, 'get_token_accounts_by_owner', side_effect=Exception("RPC error")), \
             patch.object(self.monitor, '_wait_for_rate_limit'):
            
            # 执行测试
            balance = await self.monitor.get_token_balance(self.wallet_address, self.token_mint)
            
            # 验证：异常时应该返回0
            self.assertEqual(balance, 0.0, "异常时应该返回0")
    
    async def test_get_token_balance_with_integer_list_format_fixed(self):
        """测试修复后的整数列表格式数据能正确解析"""
        # 创建整数列表格式的账户数据
        integer_list_account_data = self.create_mock_token_account_integer_list_format()
        mock_response = self.create_mock_account_response(integer_list_account_data)
        
        # Mock RPC客户端的响应
        with patch.object(self.monitor.client, 'get_token_accounts_by_owner', return_value=mock_response), \
             patch.object(self.monitor, '_wait_for_rate_limit'):
            
            # 执行测试
            balance = await self.monitor.get_token_balance(self.wallet_address, self.token_mint)
            
            # 验证修复：应该返回正确的余额而不是0.0
            # 原始金额 *********，使用6位小数 = 159.147178
            self.assertEqual(balance, 159.147178, "修复后应该能正确解析整数列表格式的数据")
    
    def test_parse_spl_token_account_data_correct_amount(self):
        """测试SPL Token账户数据解析的正确性"""
        # 创建包含已知金额的测试数据
        test_data = [0] * 165  # 创建165个元素的列表
        # 在位置64-71设置金额 ********* (little endian)
        test_data[64:72] = [170, 100, 124, 9, 0, 0, 0, 0]
        
        # 执行解析
        balance = self.monitor._parse_spl_token_account_data(test_data)
        
        # 验证结果
        self.assertEqual(balance, 159.147178, "应该正确解析SPL Token账户数据")
    
    def test_parse_spl_token_account_data_zero_amount(self):
        """测试零余额的解析"""
        # 创建包含零金额的测试数据
        test_data = [0] * 165
        
        # 执行解析
        balance = self.monitor._parse_spl_token_account_data(test_data)
        
        # 验证结果
        self.assertEqual(balance, 0.0, "零余额应该返回0.0")
    
    def test_parse_spl_token_account_data_insufficient_length(self):
        """测试数据长度不足的情况"""
        # 创建长度不足的测试数据
        test_data = [0] * 50  # 少于72字节
        
        # 执行解析
        balance = self.monitor._parse_spl_token_account_data(test_data)
        
        # 验证结果
        self.assertIsNone(balance, "数据长度不足时应该返回None")


if __name__ == '__main__':
    unittest.main()