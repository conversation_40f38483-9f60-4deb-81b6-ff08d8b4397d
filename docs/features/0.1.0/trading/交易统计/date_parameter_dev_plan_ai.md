# 交易统计CLI日期参数增强功能技术实现方案

## 1. 技术架构

### 1.1 影响范围分析
此功能主要影响以下模块：
- `utils/trading/statistics/cli.py` - 主要修改点，新增参数解析和验证
- `utils/trading/statistics/summary_generator.py` - 需要修复周期类型计算逻辑
- 不影响核心统计分析逻辑（`TradeStatisticsAnalyzer`等）
- 不影响数据模型（`StatisticsConfig`等）

### 1.2 实现策略
采用最小侵入式设计，通过以下方式实现：
1. 在CLI参数解析阶段将新的时间参数转换为`start_date`和`end_date`
2. 保持现有的时间范围处理逻辑不变
3. 在参数验证阶段添加全面的互斥性检查
4. 修复周期类型显示问题

### 1.3 新增功能
- `--date` 参数：指定具体日期
- `--yesterday` 参数：自动获取昨天的数据
- `--last-week` 参数：自动获取上周的数据

## 2. 详细实现方案

### 2.1 CLI参数解析增强

#### 2.1.1 新增日期解析函数
```python
def parse_single_date(date_str: str) -> tuple:
    """
    解析单一日期字符串并转换为完整的时间范围
    
    Args:
        date_str: 日期字符串，格式为 YYYY-MM-DD
        
    Returns:
        tuple: (start_datetime, end_datetime) 表示该日期的完整24小时范围
    """
    try:
        # 解析基础日期
        base_date = datetime.strptime(date_str, '%Y-%m-%d')
        
        # 设置开始时间为当日00:00:00
        start_time = base_date.replace(hour=0, minute=0, second=0, microsecond=0)
        
        # 设置结束时间为当日23:59:59.999999
        end_time = base_date.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        return start_time, end_time
        
    except ValueError:
        raise argparse.ArgumentTypeError(f"无效的日期格式: {date_str}，请使用 YYYY-MM-DD 格式")


def get_yesterday_range() -> tuple:
    """
    获取昨天的时间范围
    
    Returns:
        tuple: (start_datetime, end_datetime) 表示昨天的完整24小时范围
    """
    from datetime import time
    
    today = datetime.now().date()
    yesterday = today - timedelta(days=1)
    
    start_time = datetime.combine(yesterday, time(0, 0, 0))
    end_time = datetime.combine(yesterday, time(23, 59, 59, 999999))
    
    return start_time, end_time


def get_last_week_range() -> tuple:
    """
    获取上周的时间范围（周一到周日）
    
    Returns:
        tuple: (start_datetime, end_datetime) 表示上周的完整时间范围
    """
    from datetime import time
    
    today = datetime.now().date()
    
    # 找到本周周一
    current_monday = today - timedelta(days=today.weekday())
    
    # 计算上周周一和周日
    last_monday = current_monday - timedelta(days=7)
    last_sunday = last_monday + timedelta(days=6)
    
    start_time = datetime.combine(last_monday, time(0, 0, 0))
    end_time = datetime.combine(last_sunday, time(23, 59, 59, 999999))
    
    return start_time, end_time
```

#### 2.1.2 参数定义修改
在`create_parser()`函数的时间范围参数组中添加：
```python
time_group.add_argument(
    '--date',
    type=str,  # 使用字符串类型，在validate_args中处理
    help='指定单一日期进行统计 (YYYY-MM-DD格式)，自动扩展为该日期的完整24小时范围'
)
time_group.add_argument(
    '--yesterday',
    action='store_true',
    help='获取昨天的交易统计数据'
)
time_group.add_argument(
    '--last-week',
    action='store_true',
    help='获取上周的交易统计数据 (周一到周日)'
)
```

#### 2.1.3 帮助信息更新
在`epilog`中添加新的示例用法：
```python
# 分析昨天的交易
python -m utils.trading.statistics.cli --yesterday

# 分析上周的交易
python -m utils.trading.statistics.cli --last-week

# 分析特定日期的交易
python -m utils.trading.statistics.cli --date 2025-05-29

# 生成昨天的HTML报告
python -m utils.trading.statistics.cli --yesterday --format html --output /www/report/

# 发送昨天的飞书日报
python -m utils.trading.statistics.cli --yesterday --send-feishu \\
  --feishu-webhook "https://open.feishu.cn/open-apis/bot/v2/hook/xxx"

# 发送上周的飞书周报
python -m utils.trading.statistics.cli --last-week --send-feishu \\
  --feishu-webhook "https://open.feishu.cn/open-apis/bot/v2/hook/xxx"
```

### 2.2 参数验证逻辑增强

#### 2.2.1 互斥性检查
在`validate_args()`函数中添加：
```python
def validate_args(args) -> None:
    """验证命令行参数"""
    
    # 检查时间范围参数的互斥性
    time_params = [
        ('date', args.date),
        ('yesterday', args.yesterday),
        ('last-week', args.last_week),
        ('days', args.days),
        ('start-date/end-date', args.start_date or args.end_date)
    ]
    
    # 统计非空的时间参数
    active_params = [name for name, value in time_params if value]
    
    if len(active_params) > 1:
        param_list = '、'.join(f'--{name}' for name in active_params)
        raise ValueError(f"时间范围参数互斥，请只使用一个：{param_list}")
    
    # 处理各种时间参数
    if args.date:
        # 解析并设置日期范围
        try:
            args.start_date, args.end_date = parse_single_date(args.date)
            logger.info(f"使用指定日期: {args.date} (完整时间范围: {args.start_date} 到 {args.end_date})")
        except argparse.ArgumentTypeError as e:
            raise ValueError(str(e))
    
    elif args.yesterday:
        # 设置昨天的时间范围
        args.start_date, args.end_date = get_yesterday_range()
        logger.info(f"使用昨天数据: {args.start_date.strftime('%Y-%m-%d')} (完整时间范围: {args.start_date} 到 {args.end_date})")
    
    elif args.last_week:
        # 设置上周的时间范围
        args.start_date, args.end_date = get_last_week_range()
        logger.info(f"使用上周数据: {args.start_date.strftime('%Y-%m-%d')} 到 {args.end_date.strftime('%Y-%m-%d')} (完整时间范围: {args.start_date} 到 {args.end_date})")
    
    elif args.days and (args.start_date or args.end_date):
        raise ValueError("--days 参数不能与 --start-date/--end-date 同时使用")
    
    # 现有的日期范围验证逻辑保持不变
    if args.start_date and args.end_date and args.start_date > args.end_date:
        raise ValueError("开始日期不能晚于结束日期")
    
    # 其他验证逻辑保持不变...
```

### 2.3 时间范围处理逻辑修改

#### 2.3.1 run_analysis函数修改
在`run_analysis()`函数中的时间范围设置部分：
```python
async def run_analysis(args) -> None:
    """运行交易统计分析"""
    # ... 现有的初始化代码 ...
    
    # 构建配置
    config = StatisticsConfig()
    
    # 设置时间范围 - 修改此部分
    if args.date:
        # --date参数已在validate_args中转换为start_date和end_date
        config.start_date = args.start_date
        config.end_date = args.end_date
        logger.info(f"分析指定日期: {args.date} ({config.start_date.strftime('%Y-%m-%d %H:%M:%S')} 到 {config.end_date.strftime('%Y-%m-%d %H:%M:%S')})")
    elif args.yesterday:
        # --yesterday参数已在validate_args中转换为start_date和end_date
        config.start_date = args.start_date
        config.end_date = args.end_date
        logger.info(f"分析昨天数据: {config.start_date.strftime('%Y-%m-%d')} ({config.start_date.strftime('%Y-%m-%d %H:%M:%S')} 到 {config.end_date.strftime('%Y-%m-%d %H:%M:%S')})")
    elif args.last_week:
        # --last-week参数已在validate_args中转换为start_date和end_date
        config.start_date = args.start_date
        config.end_date = args.end_date
        logger.info(f"分析上周数据: {config.start_date.strftime('%Y-%m-%d')} 到 {config.end_date.strftime('%Y-%m-%d')} ({config.start_date.strftime('%Y-%m-%d %H:%M:%S')} 到 {config.end_date.strftime('%Y-%m-%d %H:%M:%S')})")
    elif args.days:
        config.end_date = datetime.now()
        config.start_date = config.end_date - timedelta(days=args.days)
        logger.info(f"分析时间范围: {config.start_date.strftime('%Y-%m-%d')} 到 {config.end_date.strftime('%Y-%m-%d')}")
    else:
        config.start_date = args.start_date
        config.end_date = args.end_date
        if config.start_date:
            logger.info(f"开始日期: {config.start_date.strftime('%Y-%m-%d')}")
        if config.end_date:
            logger.info(f"结束日期: {config.end_date.strftime('%Y-%m-%d')}")
    
    # ... 其余代码保持不变 ...
```

### 2.4 飞书消息发送修复

#### 2.4.1 周期类型计算修复
在`summary_generator.py`的`generate_custom_summary()`方法中修复：
```python
async def generate_custom_summary(self, days=None, start_date=None, end_date=None, strategies=None, tokens=None):
    """生成自定义时间范围摘要"""
    
    # 确定时间范围和周期类型
    if days is not None:
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        period_type = f"{days}天"
    elif start_date and end_date:
        # 修复周期类型计算逻辑
        # 检查是否为同一天
        if start_date.date() == end_date.date():
            period_type = "1天"
        else:
            # 计算天数差
            delta = end_date.date() - start_date.date()
            days_count = delta.days + 1
            
            # 检查是否为一周的数据（7天）
            if days_count == 7:
                # 检查是否从周一开始到周日结束
                if start_date.weekday() == 0 and end_date.weekday() == 6:
                    period_type = "上周"
                else:
                    period_type = f"{days_count}天"
            else:
                period_type = f"{days_count}天"
    else:
        # 默认7天
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        period_type = "7天"
    
    # ... 其余实现保持不变 ...
```

#### 2.4.2 飞书消息处理逻辑
在`handle_feishu_notification()`函数中添加对新参数的识别：
```python
async def handle_feishu_notification(args, stats_result, output_path: str) -> None:
    """处理飞书消息发送"""
    
    # ... 现有代码 ...
    
    # 根据参数类型确定周期类型和摘要生成方式
    if hasattr(args, 'yesterday') and args.yesterday:
        # 使用--yesterday参数，确定为昨日报
        period_type = "昨日"
        summary_data = await summary_generator.generate_custom_summary(
            start_date=args.start_date,
            end_date=args.end_date,
            strategies=args.strategies,
            tokens=args.tokens
        )
    elif hasattr(args, 'last_week') and args.last_week:
        # 使用--last-week参数，确定为上周报
        period_type = "上周"
        summary_data = await summary_generator.generate_custom_summary(
            start_date=args.start_date,
            end_date=args.end_date,
            strategies=args.strategies,
            tokens=args.tokens
        )
    elif hasattr(args, 'date') and args.date:
        # 使用--date参数，确定为单日报
        period_type = "日"
        summary_data = await summary_generator.generate_custom_summary(
            start_date=args.start_date,
            end_date=args.end_date,
            strategies=args.strategies,
            tokens=args.tokens
        )
    elif args.period == "daily":
        period_type = "日"
        # ... 现有的日报逻辑 ...
    elif args.period == "weekly":
        period_type = "周"
        # ... 现有的周报逻辑 ...
    else:
        period_type = "自定义"
        # ... 现有的自定义周期逻辑 ...
```

## 3. 代码变更清单

### 3.1 主要修改文件

#### utils/trading/statistics/cli.py
1. **新增函数**:
   - `parse_single_date()` - 解析单日期并转换为时间范围
   - `get_yesterday_range()` - 获取昨天的时间范围
   - `get_last_week_range()` - 获取上周的时间范围

2. **修改函数**:
   - `create_parser()` - 添加`--date`、`--yesterday`、`--last-week`参数定义和帮助信息
   - `validate_args()` - 增强参数互斥性验证和时间处理
   - `run_analysis()` - 添加对新时间参数的处理逻辑
   - `handle_feishu_notification()` - 改进周期类型判断

#### utils/trading/statistics/summary_generator.py
1. **修改函数**:
   - `generate_custom_summary()` - 修复周期类型计算逻辑，支持识别上周数据

### 3.2 不需要修改的文件
- `models.py` - 数据模型保持不变
- `trade_statistics_analyzer.py` - 核心分析逻辑不变
- `statistics_calculator.py` - 统计计算逻辑不变
- 其他报告生成和图表生成模块

## 4. 测试策略

### 4.1 单元测试
- 测试`parse_single_date()`函数的各种输入情况
- 测试`get_yesterday_range()`函数的时间计算正确性
- 测试`get_last_week_range()`函数的周范围计算正确性
- 测试参数互斥性验证逻辑
- 测试时间范围扩展的正确性

### 4.2 集成测试
- 测试新时间参数与现有功能的兼容性
- 测试飞书消息发送功能
- 测试报告生成功能

### 4.3 端到端测试
- 使用真实数据测试完整的昨天数据统计流程
- 使用真实数据测试完整的上周数据统计流程
- 验证数据查询结果的正确性

## 5. 风险评估

### 5.1 低风险
- 功能是增量式的，不影响现有功能
- 使用现有的时间处理逻辑，只是参数来源不同
- 向后兼容性良好

### 5.2 注意事项
- 需要确保时间范围设置的精确性（microsecond级别）
- 参数验证逻辑要全面，避免冲突
- 错误信息要清晰且具有指导性
- 时区问题需要考虑（目前使用系统本地时区）
- 上周计算逻辑需要准确（周一到周日）

## 6. 部署计划

### 6.1 实施步骤
1. 实现CLI参数解析和验证逻辑
2. 添加新的时间范围计算函数
3. 修复摘要生成器的周期计算问题
4. 编写和运行单元测试
5. 进行集成测试
6. 更新文档和帮助信息

### 6.2 验证方法
1. 使用`--yesterday`参数查询昨天的数据，确认能正常返回结果
2. 使用`--last-week`参数查询上周的数据，确认返回正确的周范围结果
3. 使用`--date`参数查询有数据的日期，确认能正常返回结果
4. 测试参数冲突检测
5. 测试飞书消息发送时显示正确的周期类型 