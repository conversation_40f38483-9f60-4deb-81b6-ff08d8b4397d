---
description: 
globs: 
alwaysApply: true
---
---
description:项目通用规范和基本信息
globs: ["**"]
alwaysApply: true
---

# 项目通用规范

## 1. 技术栈
- Python 3.11

## 2. 代码风格
- **核心原则**: 保持代码简洁、清晰、可读、可维护。
- **命名**: 使用有意义且一致的变量名、函数名、类名和模块名。
- **注释**:
    - 为公共API、复杂算法、重要业务逻辑模块添加清晰的文档字符串 (docstrings) 和必要的行内注释。
    - 注释应解释“为什么”这样做，而不仅仅是“做了什么”。
- **格式**: 严格遵循对应语言的官方风格指南 (例如 Python 的 PEP 8)。推荐使用自动化格式化工具 (如 Black, Prettier)。
- **模块化**: 设计高内聚、低耦合的模块和函数。

## 3. 项目结构
- **清晰性**: 保持项目结构清晰、逻辑分明，遵循模块化原则。
- **约定**: 更多详细结构见 `docs/project/PROJECT_OVERVIEW.md`
    - `test/`: 存放所有单元测试代码，单元测试需要做好隔离和mock，不允许做外部调用（比如说数据库调用等）。
    - `docs/`: 存放所有项目文档。
        - `docs/features/[version]/[module_name]/`: **核心功能特性文档区。**
            - `[version]`: 功能迭代的版本号或日期标识 (例如 `v1.2`, `2025-q3`)。AI 应优先查找和使用最新版本，除非用户明确指定。
            - `[module_name]`: 具体的功能模块名称 (例如 `user_auth`, `order_processing`, `data_analysis_engine`)。此处的模块名应与代码中的模块划分概念保持一致。
            - 此目录下应包含该模块的需求文档、设计文档、API规范（若有）等。
    - `models/`: 存放所有models模型
    - `dao/`: 存放所有dao，所有的models操作都需要通过dao进行，不允许直接操作model

## 4. 通用开发原则
- **可测试性**: 编写可测试的代码。每个功能模块和重要逻辑单元都应有相应的单元测试和必要的集成测试。
- **DRY (Don't Repeat Yourself)**: 避免代码冗余。通过函数、类、模块等方式抽象和复用通用逻辑。
- **KISS (Keep It Simple, Stupid)**: 优先选择简单直接的解决方案，避免不必要的复杂性。
- **YAGNI (You Ain't Gonna Need It)**: 专注于当前实际需求，不预先实现未来可能用不到的功能。
- **SOLID原则**: (若适用，特别是面向对象设计时) 遵循SOLID原则。
- **依赖管理**: 使用明确的依赖管理工具和版本锁定 (当前项目使用 `pyproject.toml`)。
- **错误处理**: 实现健壮的错误处理和日志记录机制。
- **安全性**: 对于涉及用户数据、认证授权、外部交互的功能，必须优先考虑安全性。

## 5. AI 交互与工作流程指引

### 5.0. 任务类型识别、流程选择与Todo List (Task Type Identification 、 Workflow Selection and Todo List)
- **首要任务**: 当接收到用户请求时，请首先分析用户意图，判断任务属于以下哪种主要类型：
    1.  **新功能开发 (New Feature Development)**: 用户希望从头开始或在现有基础上构建全新的功能。
    2.  **Bug 修复 (Bug Fixing)**: 用户报告了现有代码中的一个错误、缺陷或非预期行为，需要被修正。
    3.  **其他 (Other)**: 如文档编写、代码解释、简单查询等。
- **澄清意图**: 如果用户意图不明确，请主动提问以确认任务类型。例如：“您的这个请求听起来像是要修复一个Bug，还是开发一个相关的新功能？”或“您是希望我为现有功能编写新的测试用例吗？”
- **流程选择**: 一旦任务类型被确定，请遵循下面对应类型的子流程进行工作。
    - 对于“新功能开发”，请遵循 `5.A` 系列流程。
    - 对于“Bug 修复”，请遵循 `5.B` 系列流程。
- **状态追踪**: 重要！！！
    - **内部状态标记**：在您的处理逻辑中，请为当前激活流程的每个步骤维护一个状态。使用以下标记：
        - [ ]：未开始
        - [>]：进行中/当前步骤
        - [x]：已完成
        - [!]：遇到问题/需注意：详细解释问题
        - [-]：不适用/已跳过
        每个步骤应当包含合适的小步骤(如有)，比如代码实现与测试用例编写，应当包含比如说代码实现与测试用例编写这个步骤：
            - [ ] 5.A.6. 代码实现与测试用例编写
                - [ ] 1. 修改 xxxx.py 文件
                - [ ] 2. 编写 xxxx 测试用例
        文档命名参照每个流程的文档命名和位置，如新功能开发为: `[feature_name]_todo_list.md`。
        重要：**如设计任务的完成或者变动，应当更新到`[feature_name]_todo_list.md`文件**
    - **沟通责任**: 在您的每一轮回复中，都必须清晰地向用户传达：
        1. 刚刚完成的步骤 (例如，"已完成 5.A.1 指令理解与模块定位。")
        2. 当前正在进行的步骤的概要 (如果一个步骤包含多个子任务，可以简述当前子任务)
        3. 下一步明确的计划 (例如，"接下来，我将开始 5.A.2 文档查阅与影响分析。")
    - **遇到问题**: 如果在某一步骤遇到困难或需要用户决策，标记为 [!]，并在回复中明确指出问题和需要的协助。
    - **步骤跳转**: 如果因用户指示或逻辑判断需要跳过某些步骤，标记为 [-] 并向用户说明。
- **通用原则**: 无论何种流程，本文件中的“1. 技术栈”、“2. 代码风格”、“3. 项目结构”、“4. 通用开发原则”和“6. 响应语言”始终适用。

### 5.A. 工作流程：新功能开发 (New Feature Development Workflow)

#### 5.A.1. 指令理解与模块定位
- 当接收到功能实现或修改的指令时，请首先**深入理解用户的核心需求**。
- **尝试将需求映射到项目中的一个或多个具体的功能模块 (`[module_name]`)**。参考 `docs/features/` 下的现有模块划分。
- 如果需求描述模糊或模块归属不明确，请**主动向用户提问以澄清**，例如：“您希望这个功能归属于哪个模块（例如 `user_auth` 还是 `profile_management`）？”
- 确认用户指定的 `[version]` (如果提供)，否则默认处理最新版本或引导用户确认版本。

所以此步骤要求你应该获取调用工具，获取 `docs/features/` 下的目录信息，首先确认当前的最新版本。

#### 5.A.2. 文档查阅与影响分析
- 根据确定的 `[module_name]` 和 `[version]`，**优先查阅并理解 `docs/features/[version]/[module_name]/` 目录下的相关文档**。
    - 例如：需求规格文档 (`requirements.md`)、设计文档 (`design_spec.md`)、API 文档等。
- 阅读项目整体架构文档 `docs/project/PROJECT_OVERVIEW.md`
- 结合文档信息和对 相关代码目录 中对应模块源代码的理解，**初步判断代码修改范围和潜在影响**。

#### 5.A.3. 详细阅读源代码
- 在分析影响后，**详细阅读并理解范围内涉及的源代码及其主要依赖关系**。

#### 5.A.4. 生成前置文档
- **在进行任何实际代码修改之前**，请根据您的理解，在对应的 `docs/features/[version]/[module_name]/` 目录下 (或其子目录如 `generated_by_ai/`) 生成以下文档的初稿 (Markdown 格式优先)：
    1.  **详细需求规格 (Clarified Requirements)**: 对用户原始指令的细化、量化和任何澄清点的记录。
    2.  **技术实现方案 (Development Plan)**:
        - 概述修改思路。
        - 列出计划创建/修改的主要文件和函数/类。
        - 描述关键的逻辑变更点和数据结构。
        - 提及需要考虑的边界条件或潜在风险。
        - 除了数据模型用代码表示，其他的代码使用纯文字伪代码描述
    3.  **测试用例设计 (Test Case Design)**:
        - 针对详细需求中的每个可测试点设计测试用例。
        - 包含输入、预期输出、测试步骤。
        - 至少覆盖核心成功路径和常见的错误路径。
- **文档命名建议**: 例如 `[feature_name]_requirements_ai.md`, `[feature_name]_dev_plan_ai.md`, `[feature_name]_test_cases_ai.md`。

#### 5.A.5. 请求人工审阅
- 生成上述文档后，**请明确在聊天中提示用户进行审阅**，并说明文档已生成在指定位置。
- 示例："我已经为您准备了 [功能名称] 的详细需求、技术方案和测试用例设计，请在 `docs/features/[version]/[module_name]/generated_by_ai/` 目录下查阅。如果您同意这些计划，请告诉我，我将开始编码。"
- **请等待用户明确表示同意后再进行下一步。**

#### 5.A.6. 代码实现与测试用例编写
- 获得用户对方案的同意后：
    - 严格按照审阅通过的**技术实现方案**进行代码开发或修改。
    - 遵循本文件中定义的“2. 代码风格”和“4. 通用开发原则”。
    - **同时，在 `test/` 目录下编写对应的自动化测试用例代码** (例如，使用 Unittest)。测试用例应覆盖“5.4. 测试用例设计”中定义的场景。
- **行为约束**:
    - **聚焦范围**: 严格在已分析和同意的范围内修改代码。避免未经讨论的、范围外的改动。
    - **最小化原则**: 只做必要的修改来满足需求。
    - **可追溯性**: 如果可能，将代码变更与相关的需求或任务 ID关联（例如通过注释或提交信息）。

#### 5.A.7. 自动化测试执行与结果反馈
- **利用 Cursor 调用命令行工具的能力，尝试自动执行已编写的测试用例。**
- **捕获并分析测试执行的输出结果**：
    - **目标是判断测试是否全部通过。** 关注测试框架输出的总结性信息（如 "X tests passed", "Y tests failed", 退出状态码等）。
    - **如果所有测试通过**：您可以向用户报告：“[功能名称] 的自动化测试已执行并通过。此需求已完成。”
    - **如果出现测试失败**：
        - 请向用户报告失败的测试用例和数量。
        - 提供从命令行输出中提取的关键错误信息或失败日志。
        - 可以尝试对常见的失败原因进行初步分析（例如，断言错误、依赖缺失等）。
        - 询问用户是否需要协助调试。
- **注意事项与策略**：
    - **输出解析的挑战**: 不同测试框架和配置的输出格式各异。如果直接解析复杂输出遇到困难，可以建议或要求测试脚本在完成时输出一个简单、明确的成功/失败指示符（例如，脚本最后打印 "ALL_TESTS_PASSED" 或 "TESTS_FAILED:[N]"）。
    - **执行环境**: 提醒用户确保 Cursor 执行命令行的环境已正确配置（例如，相关的依赖已安装，路径正确）。
    - **超时与资源**: 注意长时间运行的测试可能会遇到的问题。

#### 5.A.8. 自我核查与最终确认 (对应新增流程步骤 11)
- 在自动化测试执行完成并（理想情况下）全部通过后，**请进行一次最终的自我核查。**
- **核心任务**: 确保代码实现和测试用例**严格且全面地**符合先前议定的需求和方案。

- **核查依据**:
    1.  您在步骤 `5.4.` 中生成的“详细需求规格”文档。
    2.  您在步骤 `5.4.` 中生成的“技术实现方案”文档。
    3.  您在步骤 `5.4.` 中生成的“测试用例设计”文档。
    4.  **当前项目中实际存在的、由您在步骤 `5.6.` 中修改或创建的源代码文件。**
    5.  **当前项目中实际存在的、由您在步骤 `5.6.` 中编写的测试用例代码文件。**

- **核查操作指引**:
    - **明确动作：请使用您的代码阅读和分析能力，重新打开并仔细审查以下相关的最新版本文件：**
        - **对照“详细需求规格”文档**：
            - **指令AI**: “**请打开并阅读 [列出相关源代码文件名，例如 `module_A/service.py`, `module_B/models.py`] 和 ‘详细需求规格’ 文档 ([文档路径])。** 逐条检查需求规格中的每一项功能点、业务规则和验收标准，确认它们是否都在代码中得到了完整且正确的实现。记录任何未完全对应或有疑问的实现点。”
        - **对照“技术实现方案”文档**：
            - **指令AI**: “**请打开并阅读 [列出相关源代码文件名] 和 ‘技术实现方案’ 文档 ([文档路径])。** 比对代码的整体架构、关键模块/类/函数的设计、使用的算法或数据结构、与外部系统的交互方式等，是否与技术方案中的描述一致。记录任何显著的偏差，并分析偏差的原因（例如，原方案的技术限制、实现中发现的更优方法等）。”
        - **对照“测试用例设计”文档与实际测试代码**：
            - **指令AI**: “**请打开并阅读 [列出相关的测试用例代码文件名，例如 `tests/module_A/test_service.py`] 和 ‘测试用例设计’ 文档 ([文档路径])。** 检查实际编写的测试用例是否覆盖了测试用例设计中规划的所有场景（包括正向、负向、边界条件）。确认测试的断言是否准确、有效，并且与需求规格相符。记录任何未被覆盖的测试场景或不明确的测试断言。”

- **核查内容要点 (AI需基于上述阅读分析进行判断)**:
    1.  **需求完整性**: 代码功能是否完全、准确地覆盖了“详细需求规格”？
    2.  **方案一致性**: 代码实现是否遵循了“技术实现方案”？若有偏差，原因是什么？
    3.  **测试充分性与准确性**: 实际测试代码是否充分覆盖“测试用例设计”，并能有效验证需求？
    4.  **重点核查**: 测试用例是否正确测试了业务代码，而不是测试测试本身的代码、在测试实现了业务代码或者错误的mock了需要测试的业务代码块

- **输出总结报告**:
    - **如果核查通过，且所有测试均通过**：请向用户提交一份总结报告，例如：“针对[功能名称]的自我核查已完成。通过**重新阅读和分析相关代码与文档**，确认代码实现满足所有需求，与设计方案一致，并通过了所有规划的测试用例。此需求已完成。”
    - **如果发现任何偏差、遗漏或潜在问题**：
        - 请清晰地向用户列出这些点，并**明确指出是在哪个文件或哪部分代码/文档中发现的问题**。例如：“自我核查发现以下问题/偏差：1. 在文件 `[文件名]` 中，针对需求点 X.Y 的实现未能完全满足‘详细需求规格’中的 [具体条款]。2. 文件 `[测试文件名]` 中的测试用例未能覆盖‘测试用例设计’中描述的场景 [具体场景]。”
        - **您可以根据发现的问题，提出具体的修正建议（例如，修改哪些文件的哪些部分），或者请求用户进一步指示。**

### 5.B. 工作流程：Bug 修复 (Bug Fixing Workflow)

#### 5.B.1. Bug 理解与复现准备 (Understand Bug and Prepare for Reproduction)
- **仔细阅读用户报告**:
    - **仔细阅读用户提供的Bug报告/描述。** 重点理解以下信息：观察到的错误现象、期望的正确行为、复现步骤（如果提供）、相关的代码模块/文件（如果用户提及）、任何错误消息或日志片段。”
- **信息收集与澄清**:
    - 如果Bug描述不清晰或信息不足，请主动向用户提问以获取更多细节。
- **相关代码初步定位**:
    - 根据Bug描述和用户提供的信息，**初步定位可能涉及的源代码文件/模块。请使用工具打开并浏览这些文件**，以便对Bug发生的上下文有一个初步了解。
- **现有测试检查**:
    - **请检查 `test/` 目录下是否存在可能已经覆盖此Bug场景的测试用例。** 如果有，分析该测试是否能稳定复现Bug。
        
#### 5.B.2. 根源分析 (Root Cause Analysis - RCA)
- **深入分析代码**:
    - **请使用您的代码分析能力，结合Bug复现信息，深入分析已定位的相关代码，找出导致Bug的根本原因。** 注意变量状态、逻辑流程、边界条件、外部依赖等。
    - 你可能需要跟踪函数调用、检查数据流。
- **解释根源**:
    - 向用户清晰地解释你分析得出的Bug根本原因。例如：“通过分析 `[文件名]` 中的 `[函数名]`，我发现当 `[特定条件]` 时，变量 `[变量名]` 的值未能正确更新，导致了[观察到的错误现象]。”
- **用户确认**: 请求用户确认您对根源的分析是否合理。

#### 5.B.3. 修复方案设计、审阅与存档 (Fix Strategy Design, Review, and Archival)
- **提出修复方案**:
    - 基于已确认的根源 (步骤 `5.B.2`)，设计一个具体的代码修复方案。方案应尽可能简单、直接，并最小化对现有代码的影响。
    - **请详细描述你建议的修复方案，包括计划修改哪些文件的哪些具体部分以及应对这个bug你设计的单元测试用例。你可以使用伪代码、代码片段或清晰的文字描述来说明修改逻辑。**
- **解释方案**:
    - 向用户清晰地解释：
        - 这个方案为什么能解决已识别的Bug根源。
        - 它将如何具体地影响代码行为（即，修复后的预期行为是什么）。
        - 评估并提及此修复方案可能带来的潜在风险或副作用（目标是最小化这些）。
        - 解释为什么这个/这些测试用例能够覆盖这个场景
- **请求用户审阅**:
    - **指令AI**：“以上是我针对[Bug的简要描述或ID]的修复方案。核心思路是[对修复方案核心思路的1-2句话总结]。您认为这个方案可行吗？是否有需要调整的地方？”
    - **请等待用户明确表示同意或提出修改意见。如果用户提出修改，请更新方案并再次请求审阅，直至方案获得批准。**
- **修复方案存档 (核心留档步骤)**:
    - **一旦用户明确同意修复方案**，请立即新建文件，将该方案保存到文件中。
    - **指令AI**: “**感谢您的确认。现在我将把我们讨论并获得您批准的修复方案整理成一个Markdown文档进行存档。**”
    - **文档内容应至少包含**:
        1.  **Bug 标识**: Bug的简要描述，或用户提供的Bug ID/追踪链接。
        2.  **报告日期/发现日期**: 通过调用工具获取当前时间
        3.  **根源分析概要**: 对步骤 `5.B.2` 中确定的Bug根本原因的总结。
        4.  **详细的、已获批准的修复方案**: 清晰描述将如何修改代码，包括涉及的文件、函数/方法、具体的逻辑变更点。可以附上关键的代码片段（修改前后的对比更佳，如果可行）。
        5.  **测试用例设计**：针对该Bug，可覆盖的测试用例
        6.  **方案提出者/执行者**: (例如，AI的名称/版本)
        7.  **方案审阅者/批准者**: (即用户)
        8.  **方案批准日期**: (当前日期)
        9.  **(可选) 预期的验证方法**: 简述如何验证该修复是否有效。
    - **文档命名与存储**:
        - **命名规范建议**: `BUGFIX_PLAN_[ModuleName]_[BugShortDescriptionOrID]_[YYYYMMDD].md` (例如: `BUGFIX_PLAN_UserAuth_LoginFailures_20250519.md`)。请确保文件名简洁且能唯一识别。
        - **存储位置建议**:
            - 优先考虑存放在受影响模块的文档目录下，例如 `docs/features/[version]/[module_name]/fixes/`。
            - `[version]` 为现有的最新的版本，不可新建version
            - `[module_name]` 可能包含多个级别，比如说这个目录：“docs/features/0.1.0/workflows/自动交易/借助Gmgn平台的自动交易”中，`[module_name]`为 “自动交易/借助Gmgn平台的自动交易”
    - **存档后确认**:
        - **指令AI**: “修复方案已成功存档至 `[AI填写的实际存档路径和文件名]`。接下来，我将继续进行步骤 `5.B.4`：[编写/确认复现Bug的测试用例]。”

#### 5.B.4. 编写/确认复现Bug的测试用例 (Write/Confirm Test to Reproduce Bug)
- **目标**: 确保有一个自动化测试用例能够在修复前稳定地复现该Bug，并在修复后通过。
- **如果已有测试 (来自 5.B.1)**: 确认该测试确实因Bug而失败。
- **如果需要新测试**:
    - 请在 `test/` 目录下相关的测试文件中，编写一个新的自动化测试用例，该测试用例应能明确地复现当前Bug。
    - 测试用例应尽可能模拟用户报告的复现步骤或触发Bug的边界条件。
    - **执行这个测试用例，确认这个测试用例能够复现当前Bug。**
    - **测试用例代码应遵循项目中既有的测试框架和风格。**
- **请求用户审阅新测试用例**: “这是我为复现Bug编写的测试用例：[展示测试代码]。请确认它能准确反映Bug场景。”

#### 5.B.5. 代码修复与测试验证 (Implement Fix and Validate with Tests)
- **实施代码修复**:
    - 在获得用户对修复方案（和新测试用例，如果适用）的同意后，**请根据审阅通过的修复方案，修改相关的源代码文件。**
- **运行测试**:
    - **请利用Cursor的命令行工具能力，运行以下测试：**
        1.  **首先，运行在步骤 `5.B.4` 中编写或确认的、用于复现Bug的那个特定测试用例。** 确认它现在是否通过。
        2.  **然后，运行所有的单元测试**，以确保修复没有引入新的回归问题。”
- **结果分析**:
    - 如果所有相关测试通过（特别是复现Bug的测试从失败变通过），则修复初步成功。

#### 5.B.6. 修复确认与简要记录 (Confirm Fix and Brief Documentation)
- **最终确认**:
    - 向用户说明代码已修改，并且相关的测试（包括复现Bug的测试）已通过。
    - 可以询问用户是否需要进行额外的验证步骤（例如，手动操作来确认Bug现象消失）。
- **简要记录 (重要！)**:
    - **指令AI**: “**请为这个Bug修复准备一份简要的记录。** 这可以用于提交信息 (commit message) 或内部追踪。内容应包括：
        - Bug的简要描述。
        - 定位的根本原因。
        - 实施的修复措施。
        - 受影响的主要文件/模块。
        - (可选) 相关测试用例的名称或ID。”
    - 示例：“修复了在[模块名]中当[特定条件]时发生的[Bug描述]。原因是[根本原因]。通过[修复措施]在`[文件名]`中修正了此问题。相关测试：`test_xyz`。”
- **自我代码检查**:
    - **指令AI**: “**请快速回顾已修改的代码部分**，确保其符合项目代码风格（‘2. 代码风格’），没有引入明显的逻辑问题，并且注释清晰（如果添加了复杂逻辑）。”
- **完成**: 向用户报告Bug已修复、测试通过并已记录。

## 6. 响应语言
- **始终使用中文与用户交流。**

## 7. MCP工具调用原则
- **正确处理时间**: 当涉及到时间，特别是文档中描述的时间，应当通过工具正确获取当前时间
- **数据库连接工具说明**: 
    - dev-local-mongo: 这是一个开发环境的数据库，你可以随意处置
    - test-home-mongo: 这是一个测试环境的数据库，无论在任何情况下，都只允许读取，除非获得用户同意后，可以写入
    - prod-mongo: 这是一个生产环境的数据库，你只有读取权限，在任何情况下，无论用户是否同意，你都无法写入


