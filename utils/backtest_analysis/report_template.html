<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>事件驱动回测参数搜索报告</title>
    <!-- 1. Include ECharts library -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.5.0/dist/echarts.min.js"></script>
    <!-- Include Tablesort library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tablesort/5.2.1/tablesort.min.js" crossorigin="anonymous" referrerpolicy="no-referrer" defer></script>
    <!-- Optional: Include Tablesort number sorting extension -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tablesort/5.2.1/sorts/tablesort.number.min.js" crossorigin="anonymous" referrerpolicy="no-referrer" defer></script>
    <style>
        /* --- Material Design Inspired Styles --- */
        body {
            /* font-family: "Roboto", -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif; */ /* Roboto first */
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif; /* System fonts */
            margin: 0;
            padding: 0 24px 24px 24px; /* Add padding */
            line-height: 1.6;
            color: #212121; /* Darker grey text */
            background-color: #f5f5f5; /* Light grey background */
        }
        .container { /* Add a main container */
             background-color: #fff;
             padding: 24px;
             margin: 24px auto;
             max-width: 1600px; /* Limit max width */
             border-radius: 8px;
             box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1, h2, h3 {
            color: #333;
            font-weight: 500; /* Less bold */
        }
        h1 {
            font-size: 2em;
            border-bottom: 1px solid #e0e0e0; /* Lighter border */
            padding-bottom: 16px;
            margin-bottom: 8px; /* Reduced bottom margin */
        }
        /* Style for the generation time and result directory */
        h1 + p,
        h1 + p + p {
            font-size: 0.85em; /* Smaller font size */
            color: #757575; /* Grey color */
            margin-bottom: 16px; /* Space before tabs */
            margin-top: 4px; /* Smaller top margin */
        }
        h2 {
            font-size: 1.5em;
            margin-top: 32px;
            border-bottom: 1px solid #eee;
            padding-bottom: 8px;
        }
        h3 {
            font-size: 1.2em;
            margin-top: 24px;
            font-weight: 500;
            color: #424242;
        }
        /* Tab Styles - More Material-like */
        .tab-container {
            width: 100%;
            margin-top: 24px;
            border-radius: 4px;
            overflow: hidden; /* Clip shadow */
            /* box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24); */
        }
        .tab-buttons {
            background-color: #eee; /* Lighter background for tabs */
            /* border-bottom: 1px solid #ccc; */
            padding: 0 16px; /* Padding around buttons */
        }
        .tab-button {
            padding: 14px 20px;
            cursor: pointer;
            border: none;
            border-bottom: 2px solid transparent; /* Indicator line */
            background-color: transparent; 
            display: inline-block;
            margin-right: 8px;
            font-size: 0.95em;
            font-weight: 500;
            color: #616161; /* Grey text */
            transition: color 0.3s, border-color 0.3s;
            text-transform: uppercase; /* Material style */
        }
         .tab-button:hover {
             color: #000;
         }
        .tab-button.active {
            color: #1976D2; /* Material blue */
            border-bottom: 2px solid #1976D2;
        }
        .tab-content {
            display: none;
            padding: 24px;
            border: 1px solid #e0e0e0;
            border-top: none; 
            background-color: #fff; /* Content background */
        }
        .tab-content.active {
            display: block;
        }

        .chart-container {
            width: 98%;
            height: 450px;
            margin: 24px auto;
            border: 1px solid #e0e0e0;
            box-shadow: 0 1px 2px rgba(0,0,0,0.07);
            padding: 15px;
            border-radius: 4px;
        }
        .summary {
            background-color: #f9f9f9;
            padding: 24px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            margin-bottom: 24px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }
        .summary p {
            margin: 8px 0;
            font-size: 1em;
        }
        .best-result-section h3 {
             margin-bottom: 16px;
             font-size: 1.3em;
        }
        .best-result-section h4 {
             margin-top: 20px;
             margin-bottom: 8px;
             font-size: 1em;
             color: #555;
             font-weight: 500;
        }

        .code {
            background-color: #f0f0f0;
            padding: 3px 6px;
            border-radius: 4px;
            font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
            font-size: 0.9em;
            border: 1px solid #e0e0e0;
        }
        /* Parameter Table Style */
        .param-table {
            width: 100%;
            margin-top: 10px;
            border-collapse: collapse;
            font-size: 0.9em;
        }
        .param-table td {
            padding: 6px 8px;
            border-bottom: 1px solid #eee;
        }
        .param-table tr:last-child td {
            border-bottom: none;
        }
        .param-table td:first-child {
            width: 60%; /* Adjust width as needed */
            color: #555;
        }
        .param-table td:last-child {
            font-weight: 500;
        }
        /* Tooltip Style */
        #param-tooltip {
            position: absolute;
            display: none;
            background-color: #333; /* Dark background */
            color: #fff;
            padding: 10px 15px;
            border-radius: 4px;
            font-size: 0.85em;
            white-space: nowrap;
            z-index: 1000; /* Ensure it's on top */
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            pointer-events: none; /* Prevent tooltip from blocking mouse events */
        }
        #param-tooltip ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        #param-tooltip li {
            margin-bottom: 4px;
        }
        #param-tooltip code {
             background-color: #555;
             border: 1px solid #666;
        }

        /* Style for highlighted table row */
        .highlighted-row td {
            background-color: #e3f2fd !important; /* Lighter blue highlight */
            transition: background-color 0.3s ease;
        }
        /* Improved Table Styles */
        .table-container {
            overflow-x: auto; /* Enable horizontal scrolling */
            width: 100%;
            margin-top: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
        }
        .results-table {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 0; /* Remove bottom margin as container handles it */
            font-size: 0.875em; /* Slightly smaller font */
            /* border: 1px solid #ccc; */ /* Removed, handled by container */
            /* box-shadow: 0 1px 3px rgba(0,0,0,0.06); */ /* Removed, handled by container */
        }
        .results-table th,
        .results-table td {
            border: none; /* Remove internal borders */
            border-bottom: 1px solid #e0e0e0; /* Use bottom border for separation */
            padding: 12px 16px; /* Adjust padding */
            text-align: left;
            white-space: nowrap; /* Prevent text wrapping */
        }
         .results-table tr:last-child td {
             border-bottom: none; /* Remove border for last row */
         }
        .results-table th {
            background-color: #f5f5f5;
            font-weight: 500; /* Material uses medium weight */
            color: #757575; /* Grey header text */
            cursor: pointer;
            position: relative;
            padding-right: 25px;
            border-bottom-width: 2px; /* Thicker bottom border for header */
        }
        .results-table th:hover {
            background-color: #eeeeee;
        }
        /* Tablesort Arrows */
        .results-table th::before,
        .results-table th::after {
            content: '';
            position: absolute;
            right: 10px; /* Adjust position */
            border: 4px solid transparent; /* Slightly smaller arrows */
            opacity: 0.4;
        }
        .results-table th::before {
            top: 50%;
            margin-top: -8px; /* Adjust for smaller size */
            border-bottom-color: #888;
        }
        .results-table th::after {
            bottom: 50%;
            margin-bottom: -8px; /* Adjust for smaller size */
            border-top-color: #888;
        }
        .results-table th[aria-sort="ascending"]::before,
        .results-table th[aria-sort="descending"]::after {
            opacity: 1;
            color: #333;
        }
        .results-table th[aria-sort="ascending"]::after {
            opacity: 0.4;
        }
         .results-table th[aria-sort="descending"]::before {
            opacity: 0.4;
        }

        .results-table tbody tr:hover td {
            background-color: #f0f0f0;
        }
        .results-table td a {
             color: #1565C0; /* Darker blue link */
             text-decoration: none;
             font-weight: 500;
        }
        .results-table td a:hover {
             text-decoration: underline;
        }

        /* Metric value colors & styles */
        .metric-item {
            font-size: 1.1em; /* Slightly larger font for metric lines */
            margin-bottom: 8px; /* Add some space between metric lines */
        }
        .metric-value {
            font-weight: bold;
        }
        .metric-positive {
            color: green;
        }
        .metric-negative {
            color: red;
        }
        .metric-neutral {
            color: orange;
        }
        .metric-na {
            color: grey;
        }
        .param-list { 
            list-style: none; 
            padding-left: 15px; 
            margin-top: 5px;
            font-size: 0.9em; 
        }
        .param-list li { margin-bottom: 3px; }

        /* --- Accordion Styles --- */
        .accordion-item {
            margin-bottom: 16px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            overflow: hidden; /* Important for border-radius */
            background-color: #fff;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }
        .accordion-trigger {
            background-color: #f9f9f9;
            color: #333;
            cursor: pointer;
            padding: 15px 20px;
            width: 100%;
            border: none;
            text-align: left;
            outline: none;
            font-size: 1.2em; /* Match h3 */
            font-weight: 500;
            transition: background-color 0.3s ease;
            border-bottom: 1px solid #e0e0e0; /* Separator */
            display: flex; /* Use flex for icon */
            justify-content: space-between; /* Space between text and icon */
            align-items: center;
        }
        .accordion-trigger:hover {
            background-color: #f1f1f1;
        }
        .accordion-trigger::after { /* Indicator icon */
            content: '\\002B'; /* Plus sign */
            color: #777;
            font-weight: bold;
            font-size: 1.2em;
            transition: transform 0.2s ease-in-out;
        }
        .accordion-trigger.active::after {
            content: "\\2212"; /* Minus sign */
            /* transform: rotate(180deg); */ /* Remove rotation */
        }
        .accordion-panel {
            padding: 0; /* Remove direct padding */
            background-color: white;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        .accordion-panel.open {
             /* max-height will be set by JS */
            padding: 20px; /* Add padding when open */
            border-top: 1px solid #e0e0e0; /* Separator line */
        }
        /* --- Card Styles (for inside accordion) --- */
        .card {
            background-color: #ffffff;
            padding: 16px;
            border-radius: 4px;
            margin-bottom: 16px; /* Space between cards */
            border: 1px solid #eee;
            /* box-shadow: 0 1px 2px rgba(0,0,0,0.05); */ /* Optional lighter shadow */
        }
        .card h4 { /* Style for card titles */
             margin-top: 0;
             margin-bottom: 12px;
             font-size: 1em;
             color: #555;
             font-weight: 500;
             border-bottom: 1px solid #f0f0f0;
             padding-bottom: 6px;
             font-weight: bold; /* Make card titles bold */
        }
        .card:last-child {
            margin-bottom: 0; /* No margin for the last card in a panel */
        }

    </style>
</head>
<body>
    <!-- Wrap everything in a container -->
    <div class="container">
        <!-- 修改: 移除标题中的 (ECharts) -->
        <h1>回测报告</h1>
        <p>生成时间: {{ generation_time }}</p>
        <p>结果来源目录: <code class="code">{{ result_dir }}</code></p>

        <h2>策略描述</h2>
        <p>
            系统定期检查过去 <code>transaction_lookback_hours</code> 小时内，是否有至少 <code>kol_account_min_count</code> 个 KOL 进行了大额 (<code>transaction_min_amount</code>) 买入操作，
            且买入的是最近 <code>token_mint_lookback_hours</code> 小时内创建的新币。若满足条件，则生成买入信号，记录到数据库并通知用户。
        </p>
        <p>详细策略逻辑请参考：<a href="https://ycnfq8ff7rld.feishu.cn/wiki/FdRmwWPhuipgeRkH5ZOcUaWhnub?from=space_home_recent&pre_pathname=%2Fdrive%2Fhome%2F&previous_navigation_time=*************&disposable_login_token=eyJ1c2VyX2lkIjoiNzQ3NTIzNTM4OTA0NTkxNTY0OSIsImRldmljZV9sb2dpbl9pZCI6Ijc0NzU3MjY1MzY4MTM5MjAyNzUiLCJ0aW1lc3RhbXAiOjE3NDU4MjM0MDUsInVuaXQiOiJldV9uYyIsInB3ZF9sZXNzX2xvZ2luX2F1dGgiOiIxIiwidmVyc2lvbiI6InYzIiwidGVuYW50X2JyYW5kIjoiZmVpc2h1IiwicGtnX2JyYW5kIjoi6aOe5LmmIn0=.019dc0225f63ddce82b740bfc9e5ed7bca0bef731408a01654f6628a45af32f1" target="_blank">策略文档</a></p>

        <div class="tab-container">
            <div class="tab-buttons">
                <button class="tab-button active" onclick="openTab(event, 'summary-tab')">执行摘要</button>
                <button class="tab-button" onclick="openTab(event, 'table-tab')">详细结果表格</button>
                <button class="tab-button" onclick="openTab(event, 'charts-tab')">结果可视化</button>
            </div>

            <!-- Tab 1: Summary -->
            <div id="summary-tab" class="tab-content active">
                <h2>执行摘要</h2>
                <div class="summary">
                    <p>总参数组合数: {{ total_params }}</p>
                    <p>总执行时间: {{ total_execution_time }}</p>
                    <p>平均执行时间: {{ average_execution_time }}</p>
                </div>

                {# --- Accordion Container --- #}
                <div class="accordion-container" style="margin-top: 20px;">
                    {# --- Best Win Rate Accordion Item (Default Open) --- #}
                    {% if best_by_win_rate %}
                    <div class="accordion-item">
                        <button class="accordion-trigger active">最佳胜率参数 (组合 {{ best_by_win_rate.param_index }})</button>
                        <div class="accordion-panel open"> {# Add 'open' class #}
                            <div class="card"> {# Card for Key Metrics #}
                                <h4>关键指标:</h4>
                                {# Use a table for metrics #}
                                <table class="param-table"> {# Reuse param-table style #}
                                    <tbody>
                                        {% for key, value in best_by_win_rate.statistics.items() %}
                                        {% if key in ['return_rate', 'win_rate', 'kelly_fraction_calculated', 'max_drawdown', 'max_profit_per_trade'] %}
                                            <tr>
                                                <td>{{ param_descriptions.get(key, key) }}:</td> {# Metric Name #}
                                                <td> {# Metric Value #}
                                                    {% set val_float = value | float(default=0.0) %}
                                                    <span class="metric-value
                                                        {% if key == 'win_rate' and val_float > 0.5 %}metric-positive
                                                        {% elif key == 'win_rate' %}metric-negative
                                                        {% elif key == 'return_rate' and val_float > 0 %}metric-positive
                                                        {% elif key == 'return_rate' %}metric-negative
                                                        {% elif key == 'kelly_fraction_calculated' and val_float > 0 %}metric-positive
                                                        {% elif key == 'kelly_fraction_calculated' %}metric-negative {# Assume negative if not positive #}
                                                        {% elif key == 'max_drawdown' %}metric-negative {# Drawdown is always negative/neutral in nature #}
                                                        {% elif key == 'max_profit_per_trade' and val_float > 0 %}metric-positive
                                                        {% elif value is none %}metric-na
                                                        {% else %}metric-neutral
                                                        {% endif %}
                                                    ">
                                                    {# Format specific metrics #}
                                                    {% if key in ['return_rate', 'win_rate'] %}
                                                        {{ "%.2f%%" | format(val_float * 100) }}
                                                    {% elif key == 'max_drawdown' %}
                                                        {{ "%.4f" | format(value | abs) if value is not none else 'N/A' }}
                                                    {% elif key in ['kelly_fraction_calculated', 'max_profit_per_trade'] %}
                                                        {{ "%.4f" | format(val_float) if value is not none else 'N/A' }}
                                                    {% else %}
                                                        {{ value if value is not none else 'N/A' }}
                                                    {% endif %}
                                                    </span>
                                                </td>
                                            </tr>
                                        {% endif %}
                                        {% endfor %}
                                        {# Add Detailed Report link as a separate row #}
                                        <tr>
                                            <td>详细报告:</td>
                                            <td>
                                                {% if best_by_win_rate.single_report_rel_path %}
                                                    <a href="{{ best_by_win_rate.single_report_rel_path }}" target="_blank">点击查看</a>
                                                {% else %}
                                                    N/A {# Handle case where link doesn't exist #}
                                                {% endif %}
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="card"> {# Card for Parameters #}
                                <h4>参数配置:</h4>
                                <table class="param-table">
                                    <tbody>
                                    {% set hidden_params = ['backtest_start_time', 'backtest_end_time', 'use_real_price', 'skip_price_api_query', 'processing_interval', 'index', 'param_index'] %}
                                    {% for key, value in best_by_win_rate.params.items() %}
                                    {% if key not in hidden_params %}
                                        <tr>
                                            <td><code class="code">{{ param_descriptions.get(key, key) }}</code> ({{ key }})</td>
                                            <td>{{ value }}</td>
                                         </tr>
                                    {% endif %}
                                    {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    {# --- Best Return Rate Accordion Item (Default Closed) --- #}
                    {% if best_by_return %}
                    <div class="accordion-item">
                        <button class="accordion-trigger">最佳收益率参数 (组合 {{ best_by_return.param_index }})</button>
                        <div class="accordion-panel"> {# No 'open' class #}
                            <div class="card"> {# Card for Key Metrics #}
                                <h4>关键指标:</h4>
                                {# Use a table for metrics #}
                                <table class="param-table"> {# Reuse param-table style #}
                                    <tbody>
                                        {% for key, value in best_by_return.statistics.items() %}
                                        {% if key in ['return_rate', 'win_rate', 'kelly_fraction_calculated', 'max_drawdown', 'max_profit_per_trade'] %}
                                            <tr>
                                                <td>{{ param_descriptions.get(key, key) }}:</td> {# Metric Name #}
                                                <td> {# Metric Value #}
                                                    {% set val_float = value | float(default=0.0) %}
                                                    <span class="metric-value
                                                        {% if key == 'win_rate' and val_float > 0.5 %}metric-positive
                                                        {% elif key == 'win_rate' %}metric-negative
                                                        {% elif key == 'return_rate' and val_float > 0 %}metric-positive
                                                        {% elif key == 'return_rate' %}metric-negative
                                                        {% elif key == 'kelly_fraction_calculated' and val_float > 0 %}metric-positive
                                                        {% elif key == 'kelly_fraction_calculated' %}metric-negative
                                                        {% elif key == 'max_drawdown' %}metric-negative
                                                        {% elif key == 'max_profit_per_trade' and val_float > 0 %}metric-positive
                                                        {% elif value is none %}metric-na
                                                        {% else %}metric-neutral
                                                        {% endif %}
                                                    ">
                                                    {# Format specific metrics #}
                                                    {% if key in ['return_rate', 'win_rate'] %}
                                                        {{ "%.2f%%" | format(val_float * 100) }}
                                                    {% elif key == 'max_drawdown' %}
                                                        {{ "%.4f" | format(value | abs) if value is not none else 'N/A' }}
                                                    {% elif key in ['kelly_fraction_calculated', 'max_profit_per_trade'] %}
                                                        {{ "%.4f" | format(val_float) if value is not none else 'N/A' }}
                                                    {% else %}
                                                        {{ value if value is not none else 'N/A' }}
                                                    {% endif %}
                                                    </span>
                                                </td>
                                            </tr>
                                        {% endif %}
                                        {% endfor %}
                                        {# Add Detailed Report link as a separate row #}
                                        <tr>
                                            <td>详细报告:</td>
                                            <td>
                                                {% if best_by_return.single_report_rel_path %}
                                                    <a href="{{ best_by_return.single_report_rel_path }}" target="_blank">点击查看</a>
                                                {% else %}
                                                    N/A
                                                {% endif %}
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="card"> {# Card for Parameters #}
                                <h4>参数配置:</h4>
                                <table class="param-table">
                                     <tbody>
                                    {% set hidden_params = ['backtest_start_time', 'backtest_end_time', 'use_real_price', 'skip_price_api_query', 'processing_interval', 'index', 'param_index'] %}
                                    {% for key, value in best_by_return.params.items() %}
                                    {% if key not in hidden_params %}
                                        <tr>
                                            <td><code class="code">{{ param_descriptions.get(key, key) }}</code> ({{ key }})</td>
                                            <td>{{ value }}</td>
                                        </tr>
                                    {% endif %}
                                    {% endfor %}
                                     </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div> {# --- End Accordion Container --- #}
            </div>

            <!-- Tab 2: Table -->
            <div id="table-tab" class="tab-content">
                <h2>详细结果表格 (点击表头排序)</h2>
                <div class="table-container"> {# Wrap table for scrolling #}
                    {{ results_table | safe }} 
                </div>
            </div>

            <!-- Tab 3: Charts -->
            <div id="charts-tab" class="tab-content">
                <h2>结果可视化 (ECharts)</h2>
                <!-- 2. Add div containers for charts -->
                <h3>胜率 vs 收益率 (散点图)</h3>
                <div id="chart-scatter" class="chart-container"></div>
                
                <h3>最佳收益率 (Top {{ top_n }}) 参数组合对比</h3>
                <div id="chart-bar-return" class="chart-container"></div>
                
                <h3>最佳胜率 (Top {{ top_n }}) 参数组合对比</h3>
                <div id="chart-bar-winrate" class="chart-container"></div>
            </div>
        </div>

        <!-- 3. Add JavaScript block -->
        <script type="text/javascript">
            // Inject data from Python JSON strings using safe filter
            const scatterData = {{ scatter_data_json | safe }};
            const barReturnData = {{ bar_return_data_json | safe }};
            const barWinrateData = {{ bar_winrate_data_json | safe }};
            const metricsInfo = {{ metrics_info | tojson | safe }};
            const topN = {{ top_n | default(5) }};

            // --- ECharts Instances (store globally for resizing) --- 
            let scatterChart = null;
            let barReturnChart = null;
            let barWinrateChart = null;

            // --- ECharts Initialization Function --- 
            function initializeCharts() {
                // Chart 1: Scatter Plot
                const scatterChartDom = document.getElementById('chart-scatter');
                if (scatterChartDom && scatterData && scatterData.length > 0) {
                    if (!scatterChart) { // Initialize only once
                        scatterChart = echarts.init(scatterChartDom);
                    }

                    // --- Data Processing for Kelly Color ---
                    const positiveKellyData = [];
                    const negativeKellyData = [];
                    scatterData.forEach(d => {
                        const kellyValue = d[2];
                        if (kellyValue !== null && !isNaN(kellyValue) && kellyValue > 0) {
                            positiveKellyData.push(d);
                        } else {
                            negativeKellyData.push(d);
                        }
                    });

                    // --- Calculate Y-axis range (Original logic: ensure 0 visible + padding) ---
                    let minY = 0;
                    let maxY = 0;
                    if (scatterData.length > 0) {
                        const validReturnRates = scatterData.map(d => d[1]).filter(r => r !== null && r !== undefined && !isNaN(r));
                        if (validReturnRates.length > 0) {
                            minY = Math.min(...validReturnRates);
                            maxY = Math.max(...validReturnRates);
                        }
                    }
                    // Original padding logic
                    const yPadding = Math.max(0.1, (maxY - minY) * 0.1); 
                    const yAxisMin = Math.min(0, minY) - yPadding; 
                    const yAxisMax = Math.max(0, maxY) + yPadding;

                    const scatterOption = {
                        title: {
                            // Reverted title to mention reference lines, not origin
                            text: '胜率 vs 收益率 (红/绿: Kelly ≤/> 0; 参考线: 50%胜率, 0%收益率)',
                            left: 'center'
                        },
                        legend: {
                            data: ['Kelly > 0', 'Kelly ≤ 0'],
                            left: 'right',
                            top: 'middle',
                            orient: 'vertical'
                        },
                        tooltip: {
                            trigger: 'item',
                            formatter: function (params) {
                                const d = params.data;
                                if (!Array.isArray(d) || d.length < 5) return '';
                                const paramIndex = d[3];
                                const winRate = d[0];
                                const returnRate = d[1];
                                const kelly = d[2];
                                const totalTrades = d[4];
                                let tooltipText = `参数组合: ${paramIndex}<br/>`;
                                tooltipText += `胜率: ${(winRate * 100).toFixed(2)}%<br/>`;
                                tooltipText += `收益率: ${(returnRate * 100).toFixed(2)}%<br/>`;
                                tooltipText += `凯利分数: ${kelly !== null && !isNaN(kelly) ? kelly.toFixed(4) : 'N/A'}<br/>`;
                                tooltipText += `总交易数: ${totalTrades}<br/>`;
                                return tooltipText;
                            }
                        },
                        grid: {
                            left: '3%', right: '12%', bottom: '7%', containLabel: true
                        },
                        xAxis: {
                            name: '胜率',
                            type: 'value',
                            min: 0,
                            max: 1,
                            scale: false,
                            axisLabel: { formatter: '{value}' },
                            markLine: {
                                silent: true,
                                symbol: 'none',
                                lineStyle: {
                                    color: '#aaa', // Reverted to lighter grey
                                    width: 1,      // Reverted to thinner line
                                    type: 'dashed' // Reverted to dashed
                                },
                                label: {
                                    formatter: '50%',
                                    position: 'insideEndTop'
                                },
                                data: [{ xAxis: 0.5 }]
                            },
                            axisLine: { 
                                onZero: false, 
                                show: true // <-- Ensure default X axis line is shown
                            },
                            splitLine: { 
                                lineStyle: {
                                    color: '#eee' 
                                }
                            }
                        },
                        yAxis: {
                            name: '收益率',
                            type: 'value',
                            min: yAxisMin, // Use reverted calculation
                            max: yAxisMax, // Use reverted calculation
                            scale: false,
                            axisLabel: { formatter: '{value}' },
                            markLine: {
                                silent: true,
                                symbol: 'none',
                                lineStyle: {
                                    color: '#aaa', // Reverted to lighter grey
                                    width: 1,      // Reverted to thinner line
                                    type: 'dashed' // Reverted to dashed
                                },
                                label: {
                                    formatter: '0%',
                                    position: 'end'
                                },
                                data: [{ yAxis: 0 }]
                            },
                            axisLine: { 
                                onZero: false, 
                                show: true // <-- Ensure default Y axis line is shown
                            },
                            splitLine: { 
                                lineStyle: {
                                    color: '#eee'
                                }
                            }
                        },
                        series: [
                            {
                                name: 'Kelly > 0',
                                type: 'scatter',
                                data: positiveKellyData,
                                itemStyle: {
                                    color: 'green'
                                },
                                emphasis: {
                                    focus: 'series',
                                    label: { show: false }
                                },
                                symbolSize: function (dataItem) {
                                    return Math.min(Math.max(Math.sqrt(dataItem[4]) * 2, 8), 40);
                                }
                            },
                            {
                                name: 'Kelly ≤ 0',
                                type: 'scatter',
                                data: negativeKellyData,
                                itemStyle: {
                                    color: 'red'
                                },
                                emphasis: {
                                    focus: 'series',
                                    label: { show: false }
                                },
                                symbolSize: function (dataItem) {
                                    return Math.min(Math.max(Math.sqrt(dataItem[4]) * 2, 8), 40);
                                }
                            }
                        ]
                    };
                    scatterChart.setOption(scatterOption);
                } else if (scatterChartDom) {
                    scatterChartDom.innerHTML = '<p style="text-align:center; padding-top: 20px;">没有足够的数据绘制散点图。</p>';
                }

                // --- Helper function for Bar Charts --- 
                function renderBarChart(chartId, chartData, title) {
                    const chartDom = document.getElementById(chartId);
                    if (!chartDom || !chartData || !chartData.categories || chartData.categories.length === 0) {
                        if(chartDom) chartDom.innerHTML = `<p style="text-align:center; padding-top: 20px;">没有足够的数据绘制 ${title} 对比图。</p>`;
                        return null;
                    }
                    // Return existing instance or initialize
                    let chartInstance = echarts.getInstanceByDom(chartDom);
                    if (!chartInstance) {
                        chartInstance = echarts.init(chartDom);
                    }
                    const barOption = {
                        title: {
                            text: title + ' (Top ' + chartData.categories.length + ') 参数组合关键指标对比',
                            left: 'center'
                        },
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: { type: 'shadow' }
                        },
                        legend: {
                            data: chartData.legend,
                            bottom: 10
                        },
                        grid: {
                            left: '3%', right: '4%', bottom: '15%', containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            data: chartData.categories, 
                            axisLabel: { interval: 0, rotate: 30 } 
                        },
                        yAxis: {
                            type: 'value',
                            name: '指标值'
                        },
                        series: chartData.series.map(s => ({ ...s, type: 'bar' })) // Ensure type is bar
                    };
                    chartInstance.setOption(barOption);
                    return chartInstance;
                }

                // Chart 2: Bar Chart - Top N by Return
                barReturnChart = renderBarChart('chart-bar-return', barReturnData, '最佳收益率');

                // Chart 3: Bar Chart - Top N by Winrate
                barWinrateChart = renderBarChart('chart-bar-winrate', barWinrateData, '最佳胜率');
            }
            
            // --- Table Interaction Logic --- 
            const resultsTable = document.querySelector('.results-table'); // Get the table by class
            if (resultsTable && scatterData && scatterData.length > 0) { // Check if scatter data exists
                // Delay adding the listener until the chart is likely initialized
                setTimeout(() => {
                     if (scatterChart) {
                        scatterChart.on('click', function (params) {
                            if (params.componentType === 'series' && params.seriesType === 'scatter') {
                                const clickedIndex = params.data[3]; // param_index is the 4th element (index 3)
                                const tableRows = resultsTable.querySelectorAll('tbody tr');
                                
                                tableRows.forEach(row => row.classList.remove('highlighted-row'));

                                for (let i = 0; i < tableRows.length; i++) {
                                    const row = tableRows[i];
                                    const firstCell = row.cells[0]; 
                                    if (firstCell && clickedIndex !== null && clickedIndex !== undefined && parseInt(firstCell.textContent) === clickedIndex) {
                                        row.classList.add('highlighted-row');
                                        row.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                        break; 
                                    }
                                }
                            }
                        });
                     }
                }, 500); // Delay slightly to ensure chart is ready
               
            }

            // --- Tab Switching Logic --- 
            function openTab(evt, tabName) {
                let i, tabcontent, tablinks;
                tabcontent = document.getElementsByClassName("tab-content");
                for (i = 0; i < tabcontent.length; i++) {
                    tabcontent[i].style.display = "none";
                    tabcontent[i].classList.remove("active");
                }
                tablinks = document.getElementsByClassName("tab-button");
                for (i = 0; i < tablinks.length; i++) {
                    tablinks[i].classList.remove("active");
                }
                const currentTab = document.getElementById(tabName);
                currentTab.style.display = "block";
                currentTab.classList.add("active");
                evt.currentTarget.classList.add("active");

                // If charts tab is opened, resize charts
                if (tabName === 'charts-tab') {
                     // Ensure charts are initialized before resizing
                     if (!scatterChart && !barReturnChart && !barWinrateChart) {
                        initializeCharts();
                     } else {
                        setTimeout(() => { // Use setTimeout to ensure DOM is ready
                            scatterChart && scatterChart.resize();
                            barReturnChart && barReturnChart.resize();
                            barWinrateChart && barWinrateChart.resize();
                        }, 0);
                     } 
                }
            }

            // === 修改开始: 将初始化调用移至 DOMContentLoaded ===
            document.addEventListener('DOMContentLoaded', function() {
                // 如果 '结果可视化' 是默认激活的标签，则立即初始化
                // (当前默认是摘要，所以这里不会立即执行)
                // if (document.getElementById('charts-tab').classList.contains('active')) {
                //      initializeCharts();
                // }
                
                // --- Tablesort Initialization --- (移到这里)
                const resultsDetailTable = document.getElementById('results-detail-table');
                if (resultsDetailTable) {
                    try {
                        new Tablesort(resultsDetailTable);
                        console.log('Tablesort initialized for #results-detail-table');
                    } catch (e) {
                        console.error('Tablesort initialization failed:', e);
                    }
                } else {
                    console.error('Could not find table with ID #results-detail-table for Tablesort.');
                }

                // --- Tooltip Initialization ---
                const tooltip = document.getElementById('param-tooltip');
                const hoverTriggers = document.querySelectorAll('.params-hover-trigger');
                const paramDescriptions = {{ param_descriptions | tojson | safe }}; // Get descriptions

                if (tooltip && hoverTriggers.length > 0) {
                    hoverTriggers.forEach(trigger => {
                        trigger.addEventListener('mouseover', (event) => {
                            const paramsJson = event.target.getAttribute('data-params');
                            if (paramsJson) {
                                try {
                                    const params = JSON.parse(paramsJson);
                                    let tooltipContent = '<ul>';
                                    for (const key in params) {
                                        const description = paramDescriptions[key] || key; // Use description or key
                                        tooltipContent += `<li><code class="code">${description}</code> (${key}): ${params[key]}</li>`;
                                    }
                                    tooltipContent += '</ul>';
                                    tooltip.innerHTML = tooltipContent;
                                    tooltip.style.left = event.pageX + 15 + 'px';
                                    tooltip.style.top = event.pageY + 15 + 'px';
                                    tooltip.style.display = 'block';
                                } catch (e) {
                                    console.error('Error parsing params JSON or building tooltip:', e);
                                    tooltip.innerHTML = '加载配置错误';
                                    tooltip.style.display = 'block'; 
                                }
                            }
                        });

                        trigger.addEventListener('mouseout', () => {
                            tooltip.style.display = 'none';
                        });
                        
                        // Optional: Adjust tooltip position on mouse move slightly
                        trigger.addEventListener('mousemove', (event) => {
                             if (tooltip.style.display === 'block') {
                                 tooltip.style.left = event.pageX + 15 + 'px';
                                 tooltip.style.top = event.pageY + 15 + 'px';
                             }
                        });
                    });
                } else {
                    if (!tooltip) console.error('Tooltip element #param-tooltip not found.');
                    if (hoverTriggers.length === 0) console.warn('No .params-hover-trigger elements found for tooltip.');
                }

            });
            // === 修改结束 ===

            // --- Resize charts on window resize --- 
            window.addEventListener('resize', function() {
                 scatterChart && scatterChart.resize();
                 barReturnChart && barReturnChart.resize();
                 barWinrateChart && barWinrateChart.resize();
            });

            // --- Accordion Logic ---
            const accordionTriggers = document.querySelectorAll('.accordion-trigger');
            accordionTriggers.forEach(trigger => {
                trigger.addEventListener('click', function() {
                    const panel = this.nextElementSibling;
                    const isOpen = panel.classList.contains('open');

                    // Close all panels first if you want only one open at a time (optional)
                    // document.querySelectorAll('.accordion-panel.open').forEach(p => {
                    //     p.classList.remove('open');
                    //     p.style.maxHeight = null;
                    //     p.previousElementSibling.classList.remove('active');
                    // });

                    this.classList.toggle('active');
                    panel.classList.toggle('open');

                    if (panel.classList.contains('open')) {
                        // panel.style.maxHeight = panel.scrollHeight + "px";
                         panel.style.maxHeight = "1000px"; // Set a fixed large max-height or calculate scrollHeight
                    } else {
                        panel.style.maxHeight = null;
                    }
                });

                // Set initial max-height for default open panel
                if (trigger.classList.contains('active') && trigger.nextElementSibling.classList.contains('open')) {
                    const panel = trigger.nextElementSibling;
                    // panel.style.maxHeight = panel.scrollHeight + "px";
                    panel.style.maxHeight = "1000px"; // Use fixed large height for initial open
                }
            });

        </script>
    </div> <!-- Close container -->
    <!-- Tooltip Element -->
    <div id="param-tooltip"></div> 
</body>
</html> 