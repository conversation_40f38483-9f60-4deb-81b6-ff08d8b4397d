from datetime import datetime
from typing import Dict, List, Optional, Any
from beanie import Document, Indexed
from pydantic import Field
from pymongo import ASCENDING, DESCENDING # Import ASCENDING and DESCENDING for clarity


class GmgnFollowingKol(Document):
    """GMGN关注的KOL账号模型
    
    该模型存储GMGN平台上关注的KOL账号信息，包括地址、社交媒体信息、收益数据、
    余额信息、活跃时间、粉丝数量、描述信息和标签等。
    
    注意：由于API可能变动，模型设计时考虑了兼容性，使用Optional标记可能缺失的字段。
    """
    
    # 区块链地址（主键）
    address: str = Field(..., description="区块链地址")
    
    # 基本信息
    ens: Optional[str] = Field(None, description="ENS域名")
    twitter_username: Optional[str] = Field(None, description="Twitter用户名")
    twitter_name: Optional[str] = Field(None, description="Twitter显示名称")
    name: Optional[str] = Field(None, description="KOL名称")
    is_blue_verified: Optional[bool] = Field(None, description="是否有Twitter蓝V认证")
    avatar: Optional[str] = Field(None, description="头像URL")
    
    # 收益数据
    realized_profit_1d: Optional[float] = Field(None, description="1天已实现收益")
    realized_profit_7d: Optional[float] = Field(None, description="7天已实现收益")
    realized_profit_30d: Optional[float] = Field(None, description="30天已实现收益")
    realized_pnl_30d: Optional[float] = Field(None, description="30天已实现盈亏比例")
    total_profit: Optional[float] = Field(None, description="总收益")
    total_profit_pnl: Optional[float] = Field(None, description="总盈亏比例")
    
    # 活动数据
    last_active_timestamp: Optional[int] = Field(None, description="最后活跃时间戳")
    swaps_1d: Optional[int] = Field(None, description="1天内交易次数")
    swaps_7d: Optional[int] = Field(None, description="7天内交易次数")
    swaps_30d: Optional[int] = Field(None, description="30天内交易次数")
    
    # 余额信息
    eth_balance: Optional[float] = Field(None, description="ETH余额")
    sol_balance: Optional[float] = Field(None, description="SOL余额")
    trx_balance: Optional[float] = Field(None, description="TRX余额")
    bnb_balance: Optional[float] = Field(None, description="BNB余额")
    balance: Optional[float] = Field(None, description="总余额")
    total_value: Optional[float] = Field(None, description="总价值")
    
    # 社交和描述信息
    followers_count: Optional[int] = Field(None, description="粉丝数量")
    description: Optional[str] = Field(None, description="描述信息")
    
    # 标签信息
    tag_rank: Optional[Dict[str, Any]] = Field(default_factory=dict, description="标签排名")
    tags: Optional[List[str]] = Field(default_factory=list, description="标签列表")
    
    # 设置信息
    is_sticky: Optional[bool] = Field(None, description="是否置顶")
    tg_alert_enabled: Optional[bool] = Field(None, description="是否启用电报提醒")
    
    # 元数据
    fetched_at: datetime = Field(default_factory=datetime.now, description="数据获取时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="数据更新时间")
    created_at: datetime = Field(default_factory=datetime.now, description="数据创建时间")
    
    class Settings:
        name = "gmgn_following_kols"
        indexes = [
            [ ("address", ASCENDING) ],             # 单字段索引，明确方向
            [ ("twitter_username", ASCENDING) ],    # 单字段索引，明确方向
            [ ("last_active_timestamp", ASCENDING) ], # 单字段索引，明确方向
            [ ("address", ASCENDING), ("fetched_at", ASCENDING) ], # 复合索引
            [ ("last_active_timestamp", DESCENDING) ], # 带方向的索引
        ] 