import logging
from datetime import datetime, timedelta
from typing import Optional
from dao.config_dao import ConfigDAO
from models.config import (
    AutoTradeConfig, 
    AutoTradeManagerConfig, 
    WalletConfig, 
    TradingParams, 
    TradeChannelConfig, 
    NotificationConfig
)

logger = logging.getLogger(__name__)


class ConfigManager:
    """配置管理器 - 负责AutoTradeManager配置的加载、缓存和更新"""
    
    def __init__(self):
        self._config_cache: Optional[AutoTradeConfig] = None
        self._last_reload: Optional[datetime] = None
        self._reload_interval_seconds = 300  # 5分钟
        self._config_dao = ConfigDAO()
    
    async def get_config(self) -> AutoTradeConfig:
        """
        获取配置，支持缓存和定期刷新
        
        Returns:
            AutoTradeConfig: 自动交易配置
        """
        now = datetime.now()
        
        # 检查是否需要重新加载配置
        if (self._config_cache is None or 
            self._last_reload is None or 
            (now - self._last_reload).total_seconds() > self._reload_interval_seconds):
            
            await self._reload_config()
        
        return self._config_cache
    
    async def _reload_config(self) -> None:
        """从数据库重新加载配置"""
        try:
            logger.info("开始从数据库重新加载 auto_trade_manager 配置")
            config_doc = await self._config_dao.get_config("auto_trade_manager")
            
            if config_doc and isinstance(config_doc.data, AutoTradeManagerConfig):
                self._config_cache = config_doc.data.auto_trade
                self._last_reload = datetime.now()
                logger.info(f"已从数据库加载 auto_trade_manager 配置，版本: {config_doc.version}")
                logger.debug(f"配置详情: enabled={self._config_cache.enabled}, "
                           f"channels_count={len(self._config_cache.channels)}")
            else:
                logger.warning("数据库中未找到有效的 auto_trade_manager 配置，使用默认配置")
                self._config_cache = self._get_default_config()
                self._last_reload = datetime.now()
                
        except Exception as e:
            logger.error(f"加载 auto_trade_manager 配置失败: {e}，使用默认配置")
            self._config_cache = self._get_default_config()
            self._last_reload = datetime.now()
    
    async def update_config(self, new_config_data: dict) -> bool:
        """
        动态更新配置
        
        Args:
            new_config_data: 新的配置数据
            
        Returns:
            bool: 更新是否成功
        """
        try:
            logger.info("开始更新 auto_trade_manager 配置")
            config_doc = await self._config_dao.get_config("auto_trade_manager")
            
            if config_doc:
                await config_doc.update_config(new_config_data)
                # 清除缓存，强制重新加载
                self._config_cache = None
                await self.get_config()
                logger.info("auto_trade_manager 配置已更新并重新加载")
                return True
            else:
                logger.error("未找到要更新的 auto_trade_manager 配置")
                return False
                
        except Exception as e:
            logger.error(f"更新 auto_trade_manager 配置失败: {e}")
            return False
    
    async def force_reload(self) -> None:
        """强制重新加载配置"""
        logger.info("强制重新加载 auto_trade_manager 配置")
        self._config_cache = None
        await self.get_config()
    
    async def is_enabled(self) -> bool:
        """检查自动交易是否启用"""
        config = await self.get_config()
        return config.enabled
    
    async def get_enabled_channels(self) -> list[TradeChannelConfig]:
        """获取启用的交易渠道列表（按优先级排序）"""
        config = await self.get_config()
        enabled_channels = [ch for ch in config.channels if ch.enabled]
        # 按优先级排序（数字越小优先级越高）
        enabled_channels.sort(key=lambda x: x.priority)
        return enabled_channels
    
    async def get_wallet_config(self) -> WalletConfig:
        """获取钱包配置"""
        config = await self.get_config()
        return config.wallet_config
    
    async def get_notification_config(self) -> NotificationConfig:
        """获取通知配置"""
        config = await self.get_config()
        return config.notification_config
    
    def _get_default_config(self) -> AutoTradeConfig:
        """
        获取默认配置
        
        Returns:
            AutoTradeConfig: 默认的自动交易配置
        """
        logger.warning("使用默认配置：自动交易已禁用，无可用渠道")
        
        return AutoTradeConfig(
            enabled=False,  # 默认禁用，避免意外交易
            wallet_config=WalletConfig(
                default_private_key_env_var="DEFAULT_WALLET_PRIVATE_KEY",
                default_wallet_address="CONFIGURE_WALLET_ADDRESS"
            ),
            channels=[],  # 空渠道列表
            default_timeout=60,
            max_total_retries=3,
            notification_config=NotificationConfig(
                notify_on_failure=True,
                notify_on_fallback=True,
                admin_chat_ids=[],
                include_trade_details=True
            )
        )
    
    def get_cache_info(self) -> dict:
        """获取缓存信息（用于调试）"""
        return {
            "has_cache": self._config_cache is not None,
            "last_reload": self._last_reload.isoformat() if self._last_reload else None,
            "reload_interval_seconds": self._reload_interval_seconds,
            "cache_expires_at": (
                self._last_reload + timedelta(seconds=self._reload_interval_seconds)
            ).isoformat() if self._last_reload else None
        } 