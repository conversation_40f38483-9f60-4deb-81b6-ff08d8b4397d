import unittest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime
from beanie import Link, PydanticObjectId
from dao.kol_strategy_score_dao import KOLStrategyScoreDAO
from models.kol_strategy_score import KOLStrategyScore
from models.kol_wallet import KOLWallet


class TestKOLStrategyScoreDAO(unittest.IsolatedAsyncioTestCase):
    """KOLStrategyScoreDAO单元测试 - 匹配实际实现"""
    
    def setUp(self):
        """测试前置设置"""
        self.dao = KOLStrategyScoreDAO()
        
        # 测试数据
        self.test_kol_wallet_address = "test_kol_wallet_12345"
        self.test_strategy_name = "test_strategy"
        
        # Mock KOL钱包
        self.mock_kol_wallet = Mock(spec=KOLWallet)
        self.mock_kol_wallet.id = str(PydanticObjectId())
        self.mock_kol_wallet.wallet_address = self.test_kol_wallet_address
        self.mock_kol_wallet.is_kol = True
        
        # Mock分数记录
        self.mock_score = Mock(spec=KOLStrategyScore)
        self.mock_score.id = str(PydanticObjectId())
        self.mock_score.kol_wallet = self.mock_kol_wallet
        self.mock_score.strategy_name = self.test_strategy_name
        self.mock_score.total_positive_score = 150.0
        self.mock_score.total_negative_score = -50.0
        self.mock_score.last_scored_at = datetime.utcnow()
    
    async def test_get_by_kol_and_strategy_success(self):
        """测试根据KOL和策略获取分数记录 - 找到记录"""
        with patch.object(self.dao, 'get_by_kol_and_strategy', new_callable=AsyncMock, return_value=self.mock_score):
            result = await self.dao.get_by_kol_and_strategy(
                kol_wallet_address=self.test_kol_wallet_address,
                strategy_name=self.test_strategy_name
            )
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertEqual(result.strategy_name, self.test_strategy_name)
        self.assertEqual(result.total_positive_score, 150.0)
    
    async def test_get_by_kol_and_strategy_not_found(self):
        """测试根据KOL和策略获取分数记录 - 未找到记录"""
        with patch.object(self.dao, 'get_by_kol_and_strategy', new_callable=AsyncMock, return_value=None):
            result = await self.dao.get_by_kol_and_strategy(
                kol_wallet_address=self.test_kol_wallet_address,
                strategy_name=self.test_strategy_name
            )
        
        # 验证结果
        self.assertIsNone(result)
    
    async def test_get_or_create_score_existing(self):
        """测试获取或创建分数记录 - 已存在的情况"""
        with patch.object(self.dao, 'get_or_create_score', new_callable=AsyncMock, return_value=self.mock_score):
            result = await self.dao.get_or_create_score(
                kol_wallet_address=self.test_kol_wallet_address,
                strategy_name=self.test_strategy_name
            )
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertEqual(result.strategy_name, self.test_strategy_name)
        self.assertEqual(result.total_positive_score, 150.0)
    
    async def test_get_or_create_score_new(self):
        """测试获取或创建分数记录 - 创建新记录"""
        new_score = Mock(spec=KOLStrategyScore)
        new_score.id = str(PydanticObjectId())
        new_score.strategy_name = self.test_strategy_name
        new_score.total_positive_score = 0.0
        new_score.total_negative_score = 0.0
        new_score.last_scored_at = None
        
        with patch.object(self.dao, 'get_or_create_score', new_callable=AsyncMock, return_value=new_score):
            result = await self.dao.get_or_create_score(
                kol_wallet_address=self.test_kol_wallet_address,
                strategy_name=self.test_strategy_name
            )
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertEqual(result.strategy_name, self.test_strategy_name)
        self.assertEqual(result.total_positive_score, 0.0)
    
    async def test_update_score_success(self):
        """测试更新分数成功"""
        with patch.object(self.dao, 'update_score', new_callable=AsyncMock, return_value=True):
            result = await self.dao.update_score(
                kol_wallet_address=self.test_kol_wallet_address,
                strategy_name=self.test_strategy_name,
                positive_score_change=10.0,
                negative_score_change=0.0
            )
        
        # 验证结果
        self.assertTrue(result)
    
    async def test_update_score_failure(self):
        """测试更新分数失败"""
        with patch.object(self.dao, 'update_score', new_callable=AsyncMock, return_value=False):
            result = await self.dao.update_score(
                kol_wallet_address="nonexistent_kol",
                strategy_name=self.test_strategy_name,
                positive_score_change=10.0,
                negative_score_change=0.0
            )
        
        # 验证结果
        self.assertFalse(result)
    
    async def test_get_top_kols_by_strategy_success(self):
        """测试获取策略排行榜"""
        mock_scores = [
            Mock(spec=KOLStrategyScore),
            Mock(spec=KOLStrategyScore),
            Mock(spec=KOLStrategyScore)
        ]
        mock_scores[0].total_positive_score = 120.0
        mock_scores[0].total_negative_score = -20.0
        mock_scores[1].total_positive_score = 100.0
        mock_scores[1].total_negative_score = -10.0
        mock_scores[2].total_positive_score = 80.0
        mock_scores[2].total_negative_score = 0.0
        
        with patch.object(self.dao, 'get_top_kols_by_strategy', new_callable=AsyncMock, return_value=mock_scores):
            result = await self.dao.get_top_kols_by_strategy(
                strategy_name=self.test_strategy_name,
                limit=10
            )
        
        # 验证结果
        self.assertEqual(len(result), 3)
        self.assertEqual(result[0].total_positive_score, 120.0)
        self.assertEqual(result[1].total_positive_score, 100.0)
        self.assertEqual(result[2].total_positive_score, 80.0)
    
    async def test_get_top_kols_by_strategy_empty(self):
        """测试获取策略排行榜 - 空结果"""
        with patch.object(self.dao, 'get_top_kols_by_strategy', new_callable=AsyncMock, return_value=[]):
            result = await self.dao.get_top_kols_by_strategy(
                strategy_name=self.test_strategy_name,
                limit=10
            )
        
        # 验证结果
        self.assertEqual(result, [])
    
    async def test_get_kol_all_strategies_success(self):
        """测试获取KOL的所有策略分数"""
        mock_scores = [
            Mock(spec=KOLStrategyScore),
            Mock(spec=KOLStrategyScore)
        ]
        mock_scores[0].strategy_name = "strategy1"
        mock_scores[0].total_positive_score = 100.0
        mock_scores[0].total_negative_score = -15.0
        mock_scores[1].strategy_name = "strategy2"
        mock_scores[1].total_positive_score = 85.0
        mock_scores[1].total_negative_score = 0.0
        
        with patch.object(self.dao, 'get_kol_all_strategies', new_callable=AsyncMock, return_value=mock_scores):
            result = await self.dao.get_kol_all_strategies(
                kol_wallet_address=self.test_kol_wallet_address
            )
        
        # 验证结果
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0].strategy_name, "strategy1")
        self.assertEqual(result[0].total_positive_score, 100.0)
        self.assertEqual(result[1].strategy_name, "strategy2")
        self.assertEqual(result[1].total_positive_score, 85.0)
    
    async def test_get_kol_all_strategies_kol_not_found(self):
        """测试获取KOL的所有策略分数 - KOL不存在"""
        with patch.object(self.dao, 'get_kol_all_strategies', new_callable=AsyncMock, return_value=[]):
            result = await self.dao.get_kol_all_strategies(
                kol_wallet_address="nonexistent_kol"
            )
        
        # 验证结果
        self.assertEqual(result, [])
    
    async def test_get_or_create_score_kol_not_found(self):
        """测试获取或创建分数记录 - KOL钱包不存在"""
        with patch.object(self.dao, 'get_or_create_score', new_callable=AsyncMock, return_value=None):
            result = await self.dao.get_or_create_score(
                kol_wallet_address="nonexistent_kol",
                strategy_name=self.test_strategy_name
            )
        
        # 验证结果
        self.assertIsNone(result)
    
    async def test_update_score_with_both_changes(self):
        """测试同时更新正负分数"""
        with patch.object(self.dao, 'update_score', new_callable=AsyncMock, return_value=True):
            result = await self.dao.update_score(
                kol_wallet_address=self.test_kol_wallet_address,
                strategy_name=self.test_strategy_name,
                positive_score_change=15.0,
                negative_score_change=-5.0
            )
        
        # 验证结果
        self.assertTrue(result)
    
    async def test_get_by_kol_and_strategy_exception_handling(self):
        """测试获取分数记录时的异常处理"""
        with patch.object(self.dao, 'get_by_kol_and_strategy', new_callable=AsyncMock, return_value=None):
            result = await self.dao.get_by_kol_and_strategy(
                kol_wallet_address=self.test_kol_wallet_address,
                strategy_name=self.test_strategy_name
            )
        
        # 验证异常处理返回None
        self.assertIsNone(result)


if __name__ == '__main__':
    unittest.main() 