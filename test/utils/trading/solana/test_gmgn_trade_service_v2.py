import asyncio
import base64
import json
import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone

import httpx
from beanie import PydanticObjectId

from utils.trading.solana.gmgn_trade_service_v2 import GmgnTradeServiceV2, GMGN_V2_API_ENDPOINTS, SOL_MINT_ADDRESS
from utils.trading.solana.trade_interface import TradeResult, TradeStatus, TradeType


class TestGmgnTradeServiceV2:
    """GMGN v2交易服务测试类"""
    
    @pytest.fixture
    def mock_http_client(self):
        """创建模拟的HTTP客户端"""
        client = AsyncMock(spec=httpx.AsyncClient)
        return client
    
    @pytest.fixture
    def trade_service(self, mock_http_client):
        """创建测试用的交易服务实例"""
        return GmgnTradeServiceV2(
            gmgn_api_host="https://test.gmgn.ai",
            http_client=mock_http_client
        )
    
    @pytest.fixture
    def sample_strategy_snapshot(self):
        """示例策略配置"""
        return {
            'input_token_decimals': 6,
            'gmgn_v2_buy_slippage_percentage': 0.5,
            'gmgn_v2_sell_slippage_percentage': 1.0,
            'gmgn_v2_buy_priority_fee': 0.00005,
            'gmgn_v2_sell_priority_fee': 0.0001,
            'gmgn_v2_anti_mev': False,
            'gmgn_v2_partner': 'test_partner',
            'gmgn_v2_max_poll_attempts': 3,
            'gmgn_v2_poll_interval': 1
        }
    
    @pytest.fixture
    def sample_route_response(self):
        """示例路由响应"""
        return {
            "code": 0,
            "msg": "success",
            "data": {
                "quote": {
                    "inAmount": "1000000",
                    "outAmount": "2000000",
                    "priceImpact": "0.1"
                },
                "raw_tx": {
                    "swapTransaction": base64.b64encode(b"mock_transaction_data").decode(),
                    "lastValidBlockHeight": 123456789
                }
            }
        }
    
    @pytest.fixture
    def sample_submit_response(self):
        """示例提交响应"""
        return {
            "code": 0,
            "msg": "success",
            "data": {
                "hash": "test_tx_hash_123456"
            }
        }
    
    @pytest.fixture
    def sample_status_response_success(self):
        """示例成功状态响应"""
        return {
            "code": 0,
            "msg": "success",
            "data": {
                "success": True,
                "expired": False,
                "failed": False
            }
        }
    
    # 路由查询测试
    @pytest.mark.asyncio
    async def test_get_swap_route_success(self, trade_service, mock_http_client, sample_route_response):
        """测试成功获取交易路由"""
        # 设置模拟响应
        mock_response = MagicMock()
        mock_response.json.return_value = sample_route_response
        mock_response.raise_for_status.return_value = None
        mock_http_client.get.return_value = mock_response
        
        # 调用方法
        result = await trade_service._get_swap_route(
            token_in_address="token_in",
            token_out_address="token_out",
            in_amount="1000000",
            from_address="wallet_address",
            slippage=0.5,
            fee=0.00005,
            is_anti_mev=False,
            partner="test_partner"
        )
        
        # 验证结果
        assert result == sample_route_response
        
        # 验证HTTP调用
        mock_http_client.get.assert_called_once()
        call_args = mock_http_client.get.call_args
        assert call_args[0][0] == f"https://test.gmgn.ai{GMGN_V2_API_ENDPOINTS['GET_SWAP_ROUTE']}"
        assert call_args[1]['params']['token_in_address'] == "token_in"
        assert call_args[1]['params']['slippage'] == 0.5
        assert call_args[1]['params']['partner'] == "test_partner"
    
    @pytest.mark.asyncio
    async def test_get_swap_route_http_error(self, trade_service, mock_http_client):
        """测试HTTP错误处理"""
        # 设置HTTP错误
        mock_response = MagicMock()
        mock_response.status_code = 400
        mock_response.text = "Bad Request"
        mock_http_client.get.side_effect = httpx.HTTPStatusError(
            "HTTP Error", request=MagicMock(), response=mock_response
        )
        
        # 调用方法
        result = await trade_service._get_swap_route(
            token_in_address="token_in",
            token_out_address="token_out",
            in_amount="1000000",
            from_address="wallet_address",
            slippage=0.5,
            fee=0.00005
        )
        
        # 验证错误响应
        assert result["code"] == 400
        assert "HTTP 400" in result["msg"]
    
    @pytest.mark.asyncio
    async def test_get_swap_route_network_error(self, trade_service, mock_http_client):
        """测试网络错误处理"""
        # 设置网络错误
        mock_http_client.get.side_effect = Exception("Network error")
        
        # 调用方法
        result = await trade_service._get_swap_route(
            token_in_address="token_in",
            token_out_address="token_out",
            in_amount="1000000",
            from_address="wallet_address",
            slippage=0.5,
            fee=0.00005
        )
        
        # 验证错误响应
        assert result["code"] == -1
        assert "Network error" in result["msg"]
    
    # 交易签名测试
    def test_sign_transaction_success(self, trade_service):
        """测试成功签名交易"""
        # 创建模拟的交易数据和私钥
        mock_transaction_data = b"mock_transaction_bytes"
        swap_transaction_b64 = base64.b64encode(mock_transaction_data).decode()
        private_key_b58 = "test_private_key_base58"
        
        with patch('utils.trading.solana.gmgn_trade_service_v2.VersionedTransaction') as mock_tx_class, \
             patch('utils.trading.solana.gmgn_trade_service_v2.Keypair') as mock_keypair_class, \
             patch('utils.trading.solana.gmgn_trade_service_v2.base58') as mock_base58:
            
            # 设置模拟对象
            mock_transaction = MagicMock()
            mock_message = MagicMock()
            mock_transaction.message = mock_message
            mock_tx_class.from_bytes.return_value = mock_transaction
            
            mock_keypair = MagicMock()
            mock_keypair_class.from_bytes.return_value = mock_keypair
            
            mock_base58.b58decode.return_value = b"mock_private_key_bytes"
            
            # 模拟签名后的交易
            mock_signed_transaction = MagicMock()
            signed_tx_bytes = b"signed_transaction_bytes"
            
            # 设置VersionedTransaction构造函数返回签名后的交易
            mock_tx_class.return_value = mock_signed_transaction
            
            with patch('builtins.bytes', return_value=signed_tx_bytes):
                # 调用方法
                result = trade_service._sign_transaction(swap_transaction_b64, private_key_b58)
                
                # 验证结果
                expected_result = base64.b64encode(signed_tx_bytes).decode()
                assert result == expected_result
                
                # 验证基本调用
                mock_tx_class.from_bytes.assert_called_once_with(mock_transaction_data)
                mock_keypair_class.from_bytes.assert_called_once_with(b"mock_private_key_bytes")
                mock_base58.b58decode.assert_called_once_with(private_key_b58)
    
    def test_sign_transaction_invalid_private_key(self, trade_service):
        """测试无效私钥错误"""
        swap_transaction_b64 = base64.b64encode(b"mock_data").decode()
        invalid_private_key = "invalid_key"
        
        with patch('utils.trading.solana.gmgn_trade_service_v2.base58') as mock_base58:
            mock_base58.b58decode.side_effect = Exception("Invalid base58")
            
            # 调用方法并验证异常
            with pytest.raises(ValueError, match="Failed to sign transaction"):
                trade_service._sign_transaction(swap_transaction_b64, invalid_private_key)
    
    def test_sign_transaction_invalid_transaction(self, trade_service):
        """测试无效交易数据错误"""
        invalid_transaction_b64 = "invalid_base64_data"
        private_key_b58 = "test_private_key"
        
        # 调用方法并验证异常
        with pytest.raises(ValueError, match="Failed to sign transaction"):
            trade_service._sign_transaction(invalid_transaction_b64, private_key_b58)
    
    # 交易提交测试
    @pytest.mark.asyncio
    async def test_submit_transaction_success(self, trade_service, mock_http_client, sample_submit_response):
        """测试成功提交交易"""
        # 设置模拟响应
        mock_response = MagicMock()
        mock_response.json.return_value = sample_submit_response
        mock_response.raise_for_status.return_value = None
        mock_http_client.post.return_value = mock_response
        
        # 调用方法
        result = await trade_service._submit_transaction("signed_tx_data", is_anti_mev=True)
        
        # 验证结果
        assert result == sample_submit_response
        
        # 验证HTTP调用
        mock_http_client.post.assert_called_once()
        call_args = mock_http_client.post.call_args
        assert call_args[0][0] == f"https://test.gmgn.ai{GMGN_V2_API_ENDPOINTS['SUBMIT_TRANSACTION']}"
        assert call_args[1]['json']['chain'] == "sol"
        assert call_args[1]['json']['signedTx'] == "signed_tx_data"
        assert call_args[1]['json']['isAntiMev'] is True
    
    @pytest.mark.asyncio
    async def test_submit_transaction_failure(self, trade_service, mock_http_client):
        """测试提交失败"""
        # 设置HTTP错误
        mock_response = MagicMock()
        mock_response.status_code = 500
        mock_response.text = "Internal Server Error"
        mock_http_client.post.side_effect = httpx.HTTPStatusError(
            "HTTP Error", request=MagicMock(), response=mock_response
        )
        
        # 调用方法
        result = await trade_service._submit_transaction("signed_tx_data")
        
        # 验证错误响应
        assert result["code"] == 500
        assert "HTTP 500" in result["msg"]
    
    # 状态监控测试
    @pytest.mark.asyncio
    async def test_monitor_transaction_success(self, trade_service, mock_http_client, sample_status_response_success):
        """测试交易成功确认"""
        # 设置模拟响应
        mock_response = MagicMock()
        mock_response.json.return_value = sample_status_response_success
        mock_response.raise_for_status.return_value = None
        mock_http_client.get.return_value = mock_response
        
        # 调用方法
        result = await trade_service._monitor_transaction_status(
            tx_hash="test_hash",
            last_valid_height=123456789,
            max_attempts=1,
            poll_interval=0.1
        )
        
        # 验证结果
        assert result == sample_status_response_success
        assert result["data"]["success"] is True
    
    @pytest.mark.asyncio
    async def test_monitor_transaction_expired(self, trade_service, mock_http_client):
        """测试交易过期"""
        # 设置过期响应
        expired_response = {
            "code": 0,
            "data": {
                "success": False,
                "expired": True,
                "failed": False
            }
        }
        
        mock_response = MagicMock()
        mock_response.json.return_value = expired_response
        mock_response.raise_for_status.return_value = None
        mock_http_client.get.return_value = mock_response
        
        # 调用方法
        result = await trade_service._monitor_transaction_status(
            tx_hash="test_hash",
            last_valid_height=123456789,
            max_attempts=1,
            poll_interval=0.1
        )
        
        # 验证结果
        assert result["data"]["expired"] is True
    
    @pytest.mark.asyncio
    async def test_monitor_transaction_timeout(self, trade_service, mock_http_client):
        """测试监控超时"""
        # 设置持续的pending响应
        pending_response = {
            "code": 0,
            "data": {
                "success": False,
                "expired": False,
                "failed": False
            }
        }
        
        mock_response = MagicMock()
        mock_response.json.return_value = pending_response
        mock_response.raise_for_status.return_value = None
        mock_http_client.get.return_value = mock_response
        
        # 调用方法
        result = await trade_service._monitor_transaction_status(
            tx_hash="test_hash",
            last_valid_height=123456789,
            max_attempts=2,
            poll_interval=0.1
        )
        
        # 验证超时结果
        assert result["data"]["expired"] is True
        assert result["data"]["success"] is False
    
    # 错误处理测试
    def test_is_slippage_related_error_message(self, trade_service):
        """测试滑点错误识别 - 错误消息"""
        # 测试滑点相关错误
        assert trade_service.is_slippage_related_error("route failed due to slippage") is True
        assert trade_service.is_slippage_related_error("price moved too much") is True
        assert trade_service.is_slippage_related_error("insufficient output amount") is True
        
        # 测试非滑点错误
        assert trade_service.is_slippage_related_error("invalid wallet address") is False
        assert trade_service.is_slippage_related_error("network timeout") is False
        assert trade_service.is_slippage_related_error(None) is False
    
    def test_is_slippage_related_error_provider_response(self, trade_service):
        """测试滑点错误识别 - 提供商响应"""
        # 测试路由响应中的滑点错误
        provider_response = {
            "route_response": {
                "code": 1,
                "msg": "swap failed due to slippage tolerance exceeded"
            }
        }
        assert trade_service.is_slippage_related_error("", provider_response) is True
        
        # 测试提交响应中的滑点错误
        provider_response = {
            "submit_response": {
                "code": 1,
                "msg": "price impact too high"
            }
        }
        assert trade_service.is_slippage_related_error("", provider_response) is True
    
    def test_is_non_retryable_error(self, trade_service):
        """测试不可重试错误识别"""
        # 测试不可重试错误
        assert trade_service.is_non_retryable_error("invalid wallet address") is True
        assert trade_service.is_non_retryable_error("private key invalid") is True
        assert trade_service.is_non_retryable_error("token not supported") is True
        
        # 测试可重试错误
        assert trade_service.is_non_retryable_error("network timeout") is False
        assert trade_service.is_non_retryable_error("temporary server error") is False
        
        # 测试HTTP错误码
        provider_response = {
            "route_response": {
                "code": 400
            }
        }
        assert trade_service.is_non_retryable_error("", provider_response) is True
    
    # 数据转换测试
    def test_prepare_route_params_buy(self, trade_service, sample_strategy_snapshot):
        """测试买入参数准备"""
        params = trade_service._prepare_route_params(
            input_token_address=SOL_MINT_ADDRESS,
            output_token_address="target_token",
            amount_input_token=1.0,
            wallet_address="wallet_addr",
            strategy_snapshot=sample_strategy_snapshot,
            trade_type=TradeType.BUY
        )
        
        # 验证参数
        assert params["token_in_address"] == SOL_MINT_ADDRESS
        assert params["token_out_address"] == "target_token"
        assert params["in_amount"] == "**********"  # 1 SOL = 10^9 lamports
        assert params["from_address"] == "wallet_addr"
        assert params["slippage"] == 0.5  # 买入滑点
        assert params["fee"] == 0.00005  # 买入优先费
        assert params["partner"] == "test_partner"
    
    def test_prepare_route_params_sell(self, trade_service, sample_strategy_snapshot):
        """测试卖出参数准备"""
        params = trade_service._prepare_route_params(
            input_token_address="source_token",
            output_token_address=SOL_MINT_ADDRESS,
            amount_input_token=100.0,
            wallet_address="wallet_addr",
            strategy_snapshot=sample_strategy_snapshot,
            trade_type=TradeType.SELL
        )
        
        # 验证参数
        assert params["token_in_address"] == "source_token"
        assert params["in_amount"] == "100000000"  # 100 * 10^6 (6 decimals)
        assert params["slippage"] == 1.0  # 卖出滑点
        assert params["fee"] == 0.0001  # 卖出优先费
    
    def test_parse_trade_result_success(self, trade_service, sample_route_response, sample_submit_response, sample_status_response_success):
        """测试成功交易结果解析"""
        trade_record_id = PydanticObjectId()
        
        result = trade_service._parse_trade_result(
            route_response=sample_route_response,
            submit_response=sample_submit_response,
            status_response=sample_status_response_success,
            trade_record_id=trade_record_id
        )
        
        # 验证结果
        assert result.status == TradeStatus.SUCCESS
        assert result.tx_hash == "test_tx_hash_123456"
        assert result.error_message is None
        assert result.actual_amount_in == 1000000.0
        assert result.actual_amount_out == 2000000.0
        # TradeResult不包含trade_record_id字段，这个字段在外部管理
    
    def test_parse_trade_result_failed(self, trade_service, sample_route_response, sample_submit_response):
        """测试失败交易结果解析"""
        failed_status_response = {
            "data": {
                "success": False,
                "expired": False,
                "failed": True
            }
        }
        
        trade_record_id = PydanticObjectId()
        
        result = trade_service._parse_trade_result(
            route_response=sample_route_response,
            submit_response=sample_submit_response,
            status_response=failed_status_response,
            trade_record_id=trade_record_id
        )
        
        # 验证结果
        assert result.status == TradeStatus.FAILED
        assert result.error_message == "Transaction failed on chain"
    
    # 完整交易流程测试
    @pytest.mark.asyncio
    async def test_execute_trade_success(self, trade_service, mock_http_client, sample_strategy_snapshot, 
                                       sample_route_response, sample_submit_response, sample_status_response_success):
        """测试成功的完整交易流程"""
        # 设置模拟响应
        mock_responses = [
            # 路由查询响应
            MagicMock(json=MagicMock(return_value=sample_route_response), raise_for_status=MagicMock()),
            # 状态查询响应
            MagicMock(json=MagicMock(return_value=sample_status_response_success), raise_for_status=MagicMock()),
        ]
        
        mock_http_client.get.side_effect = mock_responses
        
        # 提交响应
        mock_post_response = MagicMock()
        mock_post_response.json.return_value = sample_submit_response
        mock_post_response.raise_for_status.return_value = None
        mock_http_client.post.return_value = mock_post_response
        
        # 模拟交易签名
        with patch.object(trade_service, '_sign_transaction', return_value="signed_tx_data"):
            # 调用交易
            result = await trade_service.execute_trade(
                trade_type=TradeType.BUY,
                input_token_address=SOL_MINT_ADDRESS,
                output_token_address="target_token",
                amount_input_token=1.0,
                wallet_private_key_b58="test_private_key",
                wallet_address="test_wallet",
                strategy_snapshot=sample_strategy_snapshot,
                signal_id=PydanticObjectId(),
                trade_record_id=PydanticObjectId()
            )
        
        # 验证结果
        assert result.status == TradeStatus.SUCCESS
        assert result.tx_hash == "test_tx_hash_123456"
        assert result.error_message is None
    
    @pytest.mark.asyncio
    async def test_execute_trade_route_failed(self, trade_service, mock_http_client, sample_strategy_snapshot):
        """测试路由失败的交易"""
        # 设置路由失败响应
        failed_route_response = {
            "code": 1,
            "msg": "Route not found"
        }
        
        mock_response = MagicMock()
        mock_response.json.return_value = failed_route_response
        mock_response.raise_for_status.return_value = None
        mock_http_client.get.return_value = mock_response
        
        # 调用交易
        result = await trade_service.execute_trade(
            trade_type=TradeType.BUY,
            input_token_address=SOL_MINT_ADDRESS,
            output_token_address="target_token",
            amount_input_token=1.0,
            wallet_private_key_b58="test_private_key",
            wallet_address="test_wallet",
            strategy_snapshot=sample_strategy_snapshot,
            signal_id=PydanticObjectId(),
            trade_record_id=PydanticObjectId()
        )
        
        # 验证结果
        assert result.status == TradeStatus.FAILED
        assert "Route failed" in result.error_message
    
    @pytest.mark.asyncio
    async def test_execute_trade_signing_failed(self, trade_service, mock_http_client, sample_strategy_snapshot, sample_route_response):
        """测试签名失败的交易"""
        # 设置路由成功响应
        mock_response = MagicMock()
        mock_response.json.return_value = sample_route_response
        mock_response.raise_for_status.return_value = None
        mock_http_client.get.return_value = mock_response
        
        # 模拟签名失败
        with patch.object(trade_service, '_sign_transaction', side_effect=ValueError("Signing failed")):
            # 调用交易
            result = await trade_service.execute_trade(
                trade_type=TradeType.BUY,
                input_token_address=SOL_MINT_ADDRESS,
                output_token_address="target_token",
                amount_input_token=1.0,
                wallet_private_key_b58="invalid_key",
                wallet_address="test_wallet",
                strategy_snapshot=sample_strategy_snapshot,
                signal_id=PydanticObjectId(),
                trade_record_id=PydanticObjectId()
            )
        
        # 验证结果
        assert result.status == TradeStatus.FAILED
        assert "Transaction signing failed" in result.error_message
    
    # 资源清理测试
    @pytest.mark.asyncio
    async def test_close_with_owned_client(self):
        """测试关闭自有HTTP客户端"""
        mock_client = AsyncMock()
        service = GmgnTradeServiceV2(http_client=None)  # 创建自有客户端
        service.http_client = mock_client
        service._should_close_client = True
        
        await service.close()
        
        mock_client.aclose.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_close_with_external_client(self, mock_http_client):
        """测试不关闭外部HTTP客户端"""
        service = GmgnTradeServiceV2(http_client=mock_http_client)
        
        await service.close()
        
        # 外部客户端不应该被关闭
        mock_http_client.aclose.assert_not_called()

    # 6.7 集成测试
    @pytest.mark.asyncio
    async def test_complete_buy_trade_success(self, trade_service, mock_http_client, sample_strategy_snapshot,
                                            sample_route_response, sample_submit_response, sample_status_response_success):
        """测试完整的成功买入交易流程"""
        # 设置模拟响应序列
        mock_responses = [
            # 路由查询响应
            MagicMock(json=MagicMock(return_value=sample_route_response), raise_for_status=MagicMock()),
            # 状态查询响应
            MagicMock(json=MagicMock(return_value=sample_status_response_success), raise_for_status=MagicMock()),
        ]
        mock_http_client.get.side_effect = mock_responses
        
        # 提交响应
        mock_post_response = MagicMock()
        mock_post_response.json.return_value = sample_submit_response
        mock_post_response.raise_for_status.return_value = None
        mock_http_client.post.return_value = mock_post_response
        
        # 模拟交易签名
        with patch.object(trade_service, '_sign_transaction', return_value="signed_tx_data"):
            result = await trade_service.execute_trade(
                trade_type=TradeType.BUY,
                input_token_address=SOL_MINT_ADDRESS,
                output_token_address="EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",  # USDC
                amount_input_token=0.001,
                wallet_private_key_b58="test_private_key",
                wallet_address="test_wallet",
                strategy_snapshot=sample_strategy_snapshot,
                signal_id=PydanticObjectId(),
                trade_record_id=PydanticObjectId()
            )
        
        # 验证完整流程
        assert result.status == TradeStatus.SUCCESS
        assert result.tx_hash == "test_tx_hash_123456"
        assert result.actual_amount_in == 1000000.0
        assert result.actual_amount_out == 2000000.0
        
        # 验证调用序列
        assert mock_http_client.get.call_count == 2  # 路由查询 + 状态查询
        assert mock_http_client.post.call_count == 1  # 交易提交

    @pytest.mark.asyncio
    async def test_complete_sell_trade_success(self, trade_service, mock_http_client, sample_strategy_snapshot,
                                             sample_route_response, sample_submit_response, sample_status_response_success):
        """测试完整的成功卖出交易流程"""
        # 修改策略快照为卖出配置
        sell_strategy = sample_strategy_snapshot.copy()
        sell_strategy['input_token_decimals'] = 6  # USDC小数位数
        
        # 设置模拟响应
        mock_responses = [
            MagicMock(json=MagicMock(return_value=sample_route_response), raise_for_status=MagicMock()),
            MagicMock(json=MagicMock(return_value=sample_status_response_success), raise_for_status=MagicMock()),
        ]
        mock_http_client.get.side_effect = mock_responses
        
        mock_post_response = MagicMock()
        mock_post_response.json.return_value = sample_submit_response
        mock_post_response.raise_for_status.return_value = None
        mock_http_client.post.return_value = mock_post_response
        
        with patch.object(trade_service, '_sign_transaction', return_value="signed_tx_data"):
            result = await trade_service.execute_trade(
                trade_type=TradeType.SELL,
                input_token_address="EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",  # USDC
                output_token_address=SOL_MINT_ADDRESS,
                amount_input_token=100.0,  # 100 USDC
                wallet_private_key_b58="test_private_key",
                wallet_address="test_wallet",
                strategy_snapshot=sell_strategy,
                signal_id=PydanticObjectId(),
                trade_record_id=PydanticObjectId()
            )
        
        assert result.status == TradeStatus.SUCCESS
        assert result.tx_hash == "test_tx_hash_123456"

    @pytest.mark.asyncio
    async def test_trade_slippage_error_handling(self, trade_service, mock_http_client, sample_strategy_snapshot):
        """测试滑点错误处理"""
        # 设置滑点错误响应
        slippage_error_response = {
            "code": 1,
            "msg": "slippage tolerance exceeded"
        }
        
        mock_response = MagicMock()
        mock_response.json.return_value = slippage_error_response
        mock_response.raise_for_status.return_value = None
        mock_http_client.get.return_value = mock_response
        
        result = await trade_service.execute_trade(
            trade_type=TradeType.BUY,
            input_token_address=SOL_MINT_ADDRESS,
            output_token_address="target_token",
            amount_input_token=1.0,
            wallet_private_key_b58="test_private_key",
            wallet_address="test_wallet",
            strategy_snapshot=sample_strategy_snapshot,
            signal_id=PydanticObjectId(),
            trade_record_id=PydanticObjectId()
        )
        
        # 验证滑点错误被正确识别
        assert result.status == TradeStatus.FAILED
        assert "slippage tolerance exceeded" in result.error_message
        
        # 验证滑点错误识别
        is_slippage_error = trade_service.is_slippage_related_error(
            result.error_message,
            {"route_response": slippage_error_response}
        )
        assert is_slippage_error is True

    # 边界条件测试
    @pytest.mark.asyncio
    async def test_minimum_trade_amount(self, trade_service, mock_http_client, sample_strategy_snapshot,
                                      sample_route_response, sample_submit_response, sample_status_response_success):
        """测试最小交易金额"""
        # 设置模拟响应
        mock_responses = [
            MagicMock(json=MagicMock(return_value=sample_route_response), raise_for_status=MagicMock()),
            MagicMock(json=MagicMock(return_value=sample_status_response_success), raise_for_status=MagicMock()),
        ]
        mock_http_client.get.side_effect = mock_responses
        
        mock_post_response = MagicMock()
        mock_post_response.json.return_value = sample_submit_response
        mock_post_response.raise_for_status.return_value = None
        mock_http_client.post.return_value = mock_post_response
        
        with patch.object(trade_service, '_sign_transaction', return_value="signed_tx_data"):
            # 测试极小金额交易
            result = await trade_service.execute_trade(
                trade_type=TradeType.BUY,
                input_token_address=SOL_MINT_ADDRESS,
                output_token_address="target_token",
                amount_input_token=0.000001,  # 1 lamport
                wallet_private_key_b58="test_private_key",
                wallet_address="test_wallet",
                strategy_snapshot=sample_strategy_snapshot,
                signal_id=PydanticObjectId(),
                trade_record_id=PydanticObjectId()
            )
        
        # 应该能处理极小金额
        assert result.status == TradeStatus.SUCCESS

    @pytest.mark.asyncio
    async def test_network_error_retry(self, trade_service, mock_http_client, sample_strategy_snapshot):
        """测试网络错误重试机制"""
        # 模拟网络错误
        error_response = MagicMock()
        error_response.raise_for_status.side_effect = httpx.NetworkError("Connection failed")
        
        mock_http_client.get.return_value = error_response
        
        result = await trade_service.execute_trade(
            trade_type=TradeType.BUY,
            input_token_address=SOL_MINT_ADDRESS,
            output_token_address="target_token",
            amount_input_token=1.0,
            wallet_private_key_b58="test_private_key",
            wallet_address="test_wallet",
            strategy_snapshot=sample_strategy_snapshot,
            signal_id=PydanticObjectId(),
            trade_record_id=PydanticObjectId()
        )
        
        # 验证网络错误被处理
        assert result.status == TradeStatus.FAILED
        assert "Connection failed" in result.error_message

    # 独立性测试
    def test_independent_channel_registration(self):
        """测试独立渠道注册"""
        # 验证GmgnTradeServiceV2可以被正确导入
        from utils.trading.solana.gmgn_trade_service_v2 import GmgnTradeServiceV2
        from utils.trading.solana import GmgnTradeServiceV2 as ImportedService
        
        # 验证类可以被实例化
        service = GmgnTradeServiceV2()
        imported_service = ImportedService()
        
        assert isinstance(service, GmgnTradeServiceV2)
        assert isinstance(imported_service, GmgnTradeServiceV2)
        assert service.__class__ == imported_service.__class__

    def test_gmgn_v2_config_isolation(self, sample_strategy_snapshot):
        """测试v2配置隔离性"""
        service = GmgnTradeServiceV2()
        
        # 测试v2特有的配置参数
        params = service._prepare_route_params(
            input_token_address=SOL_MINT_ADDRESS,
            output_token_address="target_token",
            amount_input_token=1.0,
            wallet_address="test_wallet",
            strategy_snapshot=sample_strategy_snapshot,
            trade_type=TradeType.BUY
        )
        
        # 验证使用了v2特有的配置前缀
        assert params["slippage"] == sample_strategy_snapshot['gmgn_v2_buy_slippage_percentage']
        assert params["fee"] == sample_strategy_snapshot['gmgn_v2_buy_priority_fee']
        assert params["is_anti_mev"] == sample_strategy_snapshot['gmgn_v2_anti_mev']
        assert params["partner"] == sample_strategy_snapshot['gmgn_v2_partner']

    # 6.8 性能测试
    @pytest.mark.asyncio
    async def test_trade_response_time(self, trade_service, mock_http_client, sample_strategy_snapshot,
                                     sample_route_response, sample_submit_response, sample_status_response_success):
        """测试交易响应时间"""
        import time
        
        # 设置模拟响应
        mock_responses = [
            MagicMock(json=MagicMock(return_value=sample_route_response), raise_for_status=MagicMock()),
            MagicMock(json=MagicMock(return_value=sample_status_response_success), raise_for_status=MagicMock()),
        ]
        mock_http_client.get.side_effect = mock_responses
        
        mock_post_response = MagicMock()
        mock_post_response.json.return_value = sample_submit_response
        mock_post_response.raise_for_status.return_value = None
        mock_http_client.post.return_value = mock_post_response
        
        with patch.object(trade_service, '_sign_transaction', return_value="signed_tx_data"):
            start_time = time.time()
            
            result = await trade_service.execute_trade(
                trade_type=TradeType.BUY,
                input_token_address=SOL_MINT_ADDRESS,
                output_token_address="target_token",
                amount_input_token=1.0,
                wallet_private_key_b58="test_private_key",
                wallet_address="test_wallet",
                strategy_snapshot=sample_strategy_snapshot,
                signal_id=PydanticObjectId(),
                trade_record_id=PydanticObjectId()
            )
            
            end_time = time.time()
            response_time = end_time - start_time
        
        # 验证响应时间合理（应该很快，因为是模拟）
        assert response_time < 1.0  # 小于1秒
        assert result.status == TradeStatus.SUCCESS

    @pytest.mark.asyncio
    async def test_concurrent_trades(self, mock_http_client, sample_strategy_snapshot,
                                   sample_route_response, sample_submit_response, sample_status_response_success):
        """测试并发交易处理"""
        import asyncio
        
        # 创建多个服务实例
        services = [GmgnTradeServiceV2(http_client=mock_http_client) for _ in range(3)]
        
        # 设置模拟响应
        mock_responses = [
            MagicMock(json=MagicMock(return_value=sample_route_response), raise_for_status=MagicMock()),
            MagicMock(json=MagicMock(return_value=sample_status_response_success), raise_for_status=MagicMock()),
        ]
        mock_http_client.get.side_effect = mock_responses * 3  # 为3个并发请求准备响应
        
        mock_post_response = MagicMock()
        mock_post_response.json.return_value = sample_submit_response
        mock_post_response.raise_for_status.return_value = None
        mock_http_client.post.return_value = mock_post_response
        
        # 并发执行交易
        async def execute_trade(service, index):
            with patch.object(service, '_sign_transaction', return_value=f"signed_tx_data_{index}"):
                return await service.execute_trade(
                    trade_type=TradeType.BUY,
                    input_token_address=SOL_MINT_ADDRESS,
                    output_token_address=f"target_token_{index}",
                    amount_input_token=1.0,
                    wallet_private_key_b58="test_private_key",
                    wallet_address="test_wallet",
                    strategy_snapshot=sample_strategy_snapshot,
                    signal_id=PydanticObjectId(),
                    trade_record_id=PydanticObjectId()
                )
        
        # 并发执行
        tasks = [execute_trade(service, i) for i, service in enumerate(services)]
        results = await asyncio.gather(*tasks)
        
        # 验证所有交易都成功
        for result in results:
            assert result.status == TradeStatus.SUCCESS
        
        # 清理资源
        for service in services:
            await service.close()

    @pytest.mark.asyncio
    async def test_memory_usage(self, mock_http_client, sample_strategy_snapshot,
                              sample_route_response, sample_submit_response, sample_status_response_success):
        """测试内存使用情况"""
        import gc
        import sys
        
        # 获取初始内存使用
        gc.collect()
        initial_objects = len(gc.get_objects())
        
        # 创建和销毁多个服务实例
        services = []
        for i in range(10):
            service = GmgnTradeServiceV2(http_client=mock_http_client)
            services.append(service)
        
        # 清理服务
        for service in services:
            await service.close()
        
        # 清理引用
        del services
        gc.collect()
        
        # 检查内存泄漏
        final_objects = len(gc.get_objects())
        object_growth = final_objects - initial_objects
        
        # 允许一定的对象增长（测试框架本身可能创建对象）
        assert object_growth < 100, f"Potential memory leak: {object_growth} objects created" 