import unittest
from unittest import mock
import asyncio
import os
import json
from datetime import datetime

from utils.workflows.message_queue.message_queue import Message, KafkaQueue, KafkaOffsetFlowController

class TestKafkaQueue(unittest.TestCase):
    """测试KafkaQueue类的功能"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建KafkaQueue对象
        self.queue_name = "test-topic"
        self.queue = KafkaQueue(
            name=self.queue_name,
            bootstrap_servers="localhost:9092",
            consumer_group="test-group",
            auto_offset_reset="earliest"
        )
        
        # 设置mock环境变量
        os.environ["KAFKA_SERVERS"] = "test-server:9092"
        
        # 创建一个事件循环用于异步测试
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
    
    def tearDown(self):
        """清理测试环境"""
        # 清理环境变量
        if "KAFKA_SERVERS" in os.environ:
            del os.environ["KAFKA_SERVERS"]
        
        # 关闭事件循环
        self.loop.close()
    
    @mock.patch('aiokafka.AIOKafkaProducer')
    @mock.patch('aiokafka.AIOKafkaConsumer')
    @mock.patch('aiokafka.admin.AIOKafkaAdminClient')
    def test_setup(self, mock_admin_client, mock_consumer, mock_producer):
        """测试setup方法"""
        # 设置mock对象
        producer_instance = mock.AsyncMock()
        consumer_instance = mock.AsyncMock()
        admin_instance = mock.AsyncMock()
        
        mock_producer.return_value = producer_instance
        mock_consumer.return_value = consumer_instance
        mock_admin_client.return_value = admin_instance
        
        # 设置mock管理客户端的行为
        admin_instance.describe_topics.return_value = {self.queue_name: mock.MagicMock()}
        admin_instance.start = mock.AsyncMock()
        admin_instance.close = mock.AsyncMock()
        
        # 运行异步测试
        self.loop.run_until_complete(self._test_setup(producer_instance, consumer_instance, admin_instance))
    
    async def _test_setup(self, producer_instance, consumer_instance, admin_instance):
        """测试setup方法的异步实现"""
        # 使用mock绕过_ensure_topic_exists方法
        self.queue._ensure_topic_exists = mock.AsyncMock(return_value=True)
        
        # 调用setup方法
        await self.queue.setup()
        
        # 验证结果
        self.assertIsNotNone(self.queue.producer)
        self.assertIsNotNone(self.queue.consumer)
        
        # 验证producer和consumer的start方法被调用
        producer_instance.start.assert_called_once()
        consumer_instance.start.assert_called_once()
        
        # 验证_ensure_topic_exists被调用
        self.queue._ensure_topic_exists.assert_called_once()
    
    @mock.patch('aiokafka.AIOKafkaProducer')
    @mock.patch('aiokafka.AIOKafkaConsumer')
    def test_send(self, mock_consumer, mock_producer):
        """测试send方法"""
        # 设置mock对象
        producer_instance = mock.AsyncMock()
        consumer_instance = mock.AsyncMock()
        
        mock_producer.return_value = producer_instance
        mock_consumer.return_value = consumer_instance
        
        # 运行异步测试
        self.loop.run_until_complete(self._test_send(producer_instance))
    
    async def _test_send(self, producer_instance):
        """测试send方法的异步实现"""
        # 设置测试消息
        message = Message(data={"test": "data"}, metadata={"key": "value"})
        
        # 绕过setup方法
        self.queue.setup = mock.AsyncMock()
        
        # 手动设置producer
        self.queue.producer = producer_instance
        
        # 调用send方法
        result = await self.queue.send(message)
        
        # 验证结果
        self.assertTrue(result)
        producer_instance.send_and_wait.assert_called_once()
        
        # 检查发送的数据
        args, kwargs = producer_instance.send_and_wait.call_args
        topic, message_dict = args
        self.assertEqual(topic, self.queue_name)
        self.assertEqual(message_dict["data"], {"test": "data"})
    
    @mock.patch('aiokafka.AIOKafkaProducer')
    @mock.patch('aiokafka.AIOKafkaConsumer')
    def test_pull(self, mock_consumer, mock_producer):
        """测试pull方法"""
        # 创建一个简化版的KafkaQueue类用于测试
        class SimpleKafkaQueueForTest(KafkaQueue):
            async def pull(self, max_count=10, timeout=1.0):
                # 简化版本，直接返回一条测试消息
                message = Message(
                    data={"test": "data"},
                    metadata={
                        "topic": self.name,
                        "partition": 0,
                        "message_id": 123,
                        "timestamp": datetime.now().timestamp()
                    }
                )
                message.id = "123"
                return [message]
        
        # 创建SimpleKafkaQueueForTest实例
        test_queue = SimpleKafkaQueueForTest(
            name=self.queue_name,
            bootstrap_servers="localhost:9092",
            consumer_group="test-group"
        )
        
        # 运行异步测试
        self.loop.run_until_complete(self._test_simple_pull(test_queue))
    
    async def _test_simple_pull(self, test_queue):
        """测试简化的pull方法"""
        # 调用pull方法
        messages = await test_queue.pull(max_count=1, timeout=0.1)
        
        # 验证结果
        self.assertEqual(len(messages), 1, "应该返回1条消息")
        self.assertEqual(messages[0].data, {"test": "data"})
        self.assertEqual(messages[0].id, "123")
        self.assertEqual(messages[0].metadata["topic"], self.queue_name)
        self.assertEqual(messages[0].metadata["partition"], 0)
    
    @mock.patch('aiokafka.AIOKafkaProducer')
    @mock.patch('aiokafka.AIOKafkaConsumer')
    def test_ack(self, mock_consumer, mock_producer):
        """测试ack方法"""
        # 设置mock对象
        producer_instance = mock.AsyncMock()
        consumer_instance = mock.AsyncMock()
        
        mock_producer.return_value = producer_instance
        mock_consumer.return_value = consumer_instance
        
        # 设置assignment返回值（不是协程）
        tp = mock.MagicMock()  # TopicPartition对象
        tp.topic = self.queue_name
        tp.partition = 0
        consumer_instance.assignment = mock.MagicMock(return_value=[tp])
        
        # 设置commit返回值
        consumer_instance.commit = mock.AsyncMock(return_value=None)
        
        # 运行异步测试
        self.loop.run_until_complete(self._test_ack(consumer_instance))
    
    async def _test_ack(self, consumer_instance):
        """测试ack方法的异步实现"""
        # 绕过setup方法
        self.queue.setup = mock.AsyncMock()
        
        # 手动设置consumer
        self.queue.consumer = consumer_instance
        
        # 调用ack方法
        result = await self.queue.ack("123")
        
        # 验证结果
        self.assertTrue(result)
        consumer_instance.commit.assert_called_once()
    
    @mock.patch('aiokafka.AIOKafkaProducer')
    @mock.patch('aiokafka.AIOKafkaConsumer')
    def test_get_pending_count(self, mock_consumer, mock_producer):
        """测试get_pending_count方法"""
        # 设置mock对象
        producer_instance = mock.AsyncMock()
        consumer_instance = mock.AsyncMock()
        
        mock_producer.return_value = producer_instance
        mock_consumer.return_value = consumer_instance
        
        # 设置assignment返回值（不是协程）
        tp = mock.MagicMock()  # TopicPartition对象
        tp.topic = self.queue_name
        tp.partition = 0
        consumer_instance.assignment = mock.MagicMock(return_value=[tp])
        
        # 设置end_offsets返回值
        consumer_instance.end_offsets = mock.AsyncMock(return_value={tp: 150})
        
        # 设置committed返回值
        consumer_instance.committed = mock.AsyncMock(return_value=100)
        
        # 运行异步测试
        self.loop.run_until_complete(self._test_get_pending_count(consumer_instance))
    
    async def _test_get_pending_count(self, consumer_instance):
        """测试get_pending_count方法的异步实现"""
        # 绕过setup方法
        self.queue.setup = mock.AsyncMock()
        
        # 手动设置consumer
        self.queue.consumer = consumer_instance
        
        # 调用get_pending_count方法
        count = await self.queue.get_pending_count()
        
        # 验证结果
        self.assertEqual(count, 50)  # 150 - 100 = 50
        consumer_instance.end_offsets.assert_called_once()
        consumer_instance.committed.assert_called_once()
    
    @mock.patch('aiokafka.AIOKafkaProducer')
    @mock.patch('aiokafka.AIOKafkaConsumer')
    def test_close(self, mock_consumer, mock_producer):
        """测试close方法"""
        # 设置mock对象
        producer_instance = mock.AsyncMock()
        consumer_instance = mock.AsyncMock()
        
        mock_producer.return_value = producer_instance
        mock_consumer.return_value = consumer_instance
        
        # 模拟_cleanup_resources方法，避免实际执行资源清理
        self.queue._cleanup_resources = mock.AsyncMock()
        
        # 运行异步测试
        self.loop.run_until_complete(self._test_close())
    
    async def _test_close(self):
        """测试close方法的异步实现"""
        # 调用close方法
        await self.queue.close()
        
        # 验证结果
        self.queue._cleanup_resources.assert_called_once()

class TestKafkaOffsetFlowController(unittest.TestCase):
    """测试KafkaOffsetFlowController类的功能"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建mock KafkaQueue
        self.queue = mock.MagicMock(spec=KafkaQueue)
        self.queue.name = "test-topic"
        self.queue.auto_offset_reset = "earliest"
        
        # 创建KafkaOffsetFlowController对象
        self.controller = KafkaOffsetFlowController(
            queue=self.queue,
            max_lag=100,
            check_interval=10.0,
            enable_flow_control=True
        )
        
        # 创建一个事件循环用于异步测试
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
    
    def tearDown(self):
        """清理测试环境"""
        self.loop.close()
    
    def test_init(self):
        """测试初始化"""
        self.assertEqual(self.controller.queue, self.queue)
        self.assertEqual(self.controller.max_lag, 100)
        self.assertEqual(self.controller.check_interval, 10.0)
        self.assertTrue(self.controller.enable_flow_control)
    
    def test_init_with_wrong_queue_type(self):
        """测试使用错误的队列类型初始化"""
        # 创建非KafkaQueue的队列
        wrong_queue = mock.MagicMock()
        wrong_queue.__class__ = object
        
        # 验证抛出类型错误
        with self.assertRaises(TypeError):
            KafkaOffsetFlowController(queue=wrong_queue)
    
    def test_should_proceed_disabled(self):
        """测试禁用流量控制时should_proceed方法"""
        # 禁用流量控制
        self.controller.enable_flow_control = False
        
        # 运行异步测试
        result = self.loop.run_until_complete(self.controller.should_proceed())
        
        # 验证结果
        self.assertTrue(result)
    
    @mock.patch('time.time')
    def test_should_proceed_grace_period(self, mock_time):
        """测试在启动宽限期内的should_proceed方法"""
        # 设置当前时间为启动时间+10秒
        self.controller.startup_time = 1000
        mock_time.return_value = 1010  # 启动后10秒
        
        # 设置宽限期为100秒
        self.controller.startup_grace_period = 100
        
        # 运行异步测试
        result = self.loop.run_until_complete(self.controller.should_proceed())
        
        # 验证结果
        self.assertTrue(result)
    
    @mock.patch('time.time')
    def test_should_proceed_under_limit(self, mock_time):
        """测试滞后量低于阈值时的should_proceed方法"""
        # 设置当前时间为启动时间+200秒，超过宽限期
        self.controller.startup_time = 1000
        mock_time.return_value = 1200
        
        # 设置宽限期为100秒
        self.controller.startup_grace_period = 100
        
        # 设置上次检查时间为0，确保会执行检查
        self.controller.last_check_time = 0
        
        # 设置_get_consumer_lag的返回值
        self.controller._get_consumer_lag = mock.AsyncMock(return_value=50)
        
        # 运行异步测试
        result = self.loop.run_until_complete(self.controller.should_proceed())
        
        # 验证结果
        self.assertTrue(result)
        self.controller._get_consumer_lag.assert_called_once()
    
    @mock.patch('time.time')
    def test_should_proceed_over_limit(self, mock_time):
        """测试滞后量超过阈值时的should_proceed方法"""
        # 设置当前时间为启动时间+200秒，超过宽限期
        self.controller.startup_time = 1000
        mock_time.return_value = 1200
        
        # 设置宽限期为100秒
        self.controller.startup_grace_period = 100
        
        # 设置上次检查时间为0，确保会执行检查
        self.controller.last_check_time = 0
        
        # 设置_get_consumer_lag的返回值
        self.controller._get_consumer_lag = mock.AsyncMock(return_value=150)
        
        # 运行异步测试
        result = self.loop.run_until_complete(self.controller.should_proceed())
        
        # 验证结果
        self.assertFalse(result)
        self.controller._get_consumer_lag.assert_called_once()
    
    def test_check_immediate(self):
        """测试check_immediate方法"""
        # 设置_get_consumer_lag的返回值
        self.controller._get_consumer_lag = mock.AsyncMock(return_value=50)
        
        # 运行异步测试
        result = self.loop.run_until_complete(self.controller.check_immediate())
        
        # 验证结果
        self.assertTrue(result)
        self.controller._get_consumer_lag.assert_called_once()
    
    def test_get_consumer_lag(self):
        """测试_get_consumer_lag方法"""
        # 设置consumer
        consumer_instance = mock.AsyncMock()
        self.queue.consumer = consumer_instance
        
        # 绕过queue.setup方法
        self.queue.setup = mock.AsyncMock()
        
        # 设置mock对象行为
        tp = mock.MagicMock()  # TopicPartition对象
        tp.topic = "test-topic"
        tp.partition = 0
        
        # 设置非协程的assignment方法
        consumer_instance.assignment = mock.MagicMock(return_value=[tp])
        
        # 设置协程的end_offsets和committed方法
        consumer_instance.end_offsets = mock.AsyncMock(return_value={tp: 200})
        consumer_instance.committed = mock.AsyncMock(return_value=100)
        
        # 运行异步测试
        result = self.loop.run_until_complete(self.controller._get_consumer_lag())
        
        # 验证结果
        self.assertEqual(result, 100)  # 200 - 100 = 100
        consumer_instance.assignment.assert_called_once()
        consumer_instance.end_offsets.assert_called_once()
        consumer_instance.committed.assert_called_once()
    
    def test_get_partition_lags(self):
        """测试get_partition_lags方法"""
        # 设置consumer
        consumer_instance = mock.AsyncMock()
        self.queue.consumer = consumer_instance
        
        # 绕过queue.setup方法
        self.queue.setup = mock.AsyncMock()
        
        # 设置mock对象行为
        tp = mock.MagicMock()  # TopicPartition对象
        tp.topic = "test-topic"
        tp.partition = 0
        
        # 设置非协程的assignment方法
        consumer_instance.assignment = mock.MagicMock(return_value=[tp])
        
        # 设置协程的end_offsets和committed方法
        consumer_instance.end_offsets = mock.AsyncMock(return_value={tp: 300})
        consumer_instance.committed = mock.AsyncMock(return_value=150)
        
        # 运行异步测试
        lags = self.loop.run_until_complete(self.controller.get_partition_lags())
        
        # 验证结果
        self.assertEqual(len(lags), 1)
        self.assertEqual(lags["test-topic-0"], 150)  # 300 - 150 = 150
        consumer_instance.assignment.assert_called_once()
        consumer_instance.end_offsets.assert_called_once()
        consumer_instance.committed.assert_called_once()

if __name__ == "__main__":
    unittest.main() 