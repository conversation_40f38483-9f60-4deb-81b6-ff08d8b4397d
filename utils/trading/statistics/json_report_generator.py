"""
JSON报告生成器

将统计数据序列化为JSON格式，支持数据格式验证和结构化输出。
"""

import logging
import json
from typing import Dict, Any, Optional, List
from datetime import datetime
from decimal import Decimal

from .models import (
    StatisticsResult, StatisticsConfig, TokenStats, StrategyStats, 
    ProfitRanking, LossRanking, OverallStats
)


class JSONReportGenerator:
    """
    JSON报告生成器
    
    将统计结果序列化为JSON格式，便于数据交换和API输出。
    """
    
    def __init__(self):
        """初始化JSON报告生成器"""
        self.logger = logging.getLogger(__name__)
    
    def generate_report(self, statistics: StatisticsResult, 
                       config: Optional[StatisticsConfig] = None) -> str:
        """
        生成JSON格式的统计报告
        
        Args:
            statistics: 统计结果
            config: 统计配置（可选）
            
        Returns:
            JSON格式的报告字符串
        """
        try:
            self.logger.info("开始生成JSON报告")
            
            # 构建报告数据结构
            report_data = {
                "metadata": self._build_metadata(config),
                "overall_statistics": self._serialize_overall_stats(statistics.overall_stats),
                "token_statistics": self._serialize_token_stats(statistics.token_stats),
                "strategy_statistics": self._serialize_strategy_stats(statistics.strategy_stats),
                "profit_rankings": self._serialize_profit_rankings(statistics.profit_rankings),
                "loss_rankings": self._serialize_loss_rankings(statistics.loss_rankings)
            }
            
            # 序列化为JSON
            json_content = json.dumps(
                report_data, 
                ensure_ascii=False, 
                indent=2,
                default=self._json_serializer
            )
            
            # 验证JSON格式
            self._validate_json_format(json_content)
            
            self.logger.info("JSON报告生成完成")
            return json_content
            
        except Exception as e:
            self.logger.error(f"生成JSON报告失败: {str(e)}")
            raise
    
    def _build_metadata(self, config: Optional[StatisticsConfig] = None) -> Dict[str, Any]:
        """
        构建元数据
        
        Args:
            config: 统计配置
            
        Returns:
            元数据字典
        """
        metadata = {
            "generation_time": datetime.now().isoformat(),
            "generator": "TradeStatisticsAnalyzer",
            "version": "1.0.0"
        }
        
        if config:
            metadata.update({
                "start_date": config.start_date.isoformat() if config.start_date else None,
                "end_date": config.end_date.isoformat() if config.end_date else None,
                "strategies": config.strategy_filter if config.strategy_filter else [],
                "tokens": config.token_filter if config.token_filter else [],
                "min_trade_amount": config.min_trade_amount
            })
        
        return metadata
    
    def _serialize_overall_stats(self, overall_stats) -> Dict[str, Any]:
        """
        序列化总体统计数据
        
        Args:
            overall_stats: 总体统计对象
            
        Returns:
            序列化后的字典
        """
        if not overall_stats:
            return {}
        
        return {
            "total_trades": overall_stats.total_trades,
            "total_win_rate": round(overall_stats.total_win_rate, 4),
            "total_profit_rate": round(overall_stats.total_profit_rate, 4),
            "avg_profit_rate": round(overall_stats.avg_profit_rate, 4)
        }
    
    def _serialize_token_stats(self, token_stats: List[TokenStats]) -> List[Dict[str, Any]]:
        """序列化Token统计数据"""
        return [
            {
                "token_address": token.token_address,
                "trade_count": token.trade_count,
                "win_rate": round(token.win_rate, 4),
                "avg_profit_rate": round(token.avg_profit_rate, 4),
                "total_profit_amount": round(token.total_profit_amount, 8),
                "profitable_trades": token.profitable_trades,
                "loss_trades": token.loss_trades,
                "total_buy_amount": round(token.total_buy_amount, 8),
                "total_sell_amount": round(token.total_sell_amount, 8)
            }
            for token in token_stats
        ]
    
    def _serialize_strategy_stats(self, strategy_stats: List[StrategyStats]) -> List[Dict[str, Any]]:
        """序列化策略统计数据"""
        return [
            {
                "strategy_name": strategy.strategy_name,
                "trade_count": strategy.trade_count,
                "win_rate": round(strategy.win_rate, 4),
                "avg_profit_rate": round(strategy.avg_profit_rate, 4),
                "total_profit_amount": round(strategy.total_profit_amount, 8),
                "max_single_profit": round(strategy.max_single_profit, 8),
                "max_single_loss": round(strategy.max_single_loss, 8),
                "profitable_trades": strategy.profitable_trades,
                "loss_trades": strategy.loss_trades
            }
            for strategy in strategy_stats
        ]
    
    def _serialize_profit_rankings(self, profit_rankings) -> list:
        """
        序列化盈利排行榜数据
        
        Args:
            profit_rankings: 盈利排行榜列表
            
        Returns:
            序列化后的列表
        """
        if not profit_rankings:
            return []
        
        return [
            {
                "rank": i + 1,
                "signal_id": profit.signal_id,
                "strategy_name": profit.strategy_name,
                "token_address": profit.token_address,
                "profit_amount": round(profit.profit_amount, 6),
                "profit_rate": round(profit.profit_rate, 4)
            }
            for i, profit in enumerate(profit_rankings)
        ]
    
    def _serialize_loss_rankings(self, loss_rankings) -> list:
        """
        序列化亏损排行榜数据
        
        Args:
            loss_rankings: 亏损排行榜列表
            
        Returns:
            序列化后的列表
        """
        if not loss_rankings:
            return []
        
        return [
            {
                "rank": i + 1,
                "signal_id": loss.signal_id,
                "strategy_name": loss.strategy_name,
                "token_address": loss.token_address,
                "loss_amount": round(loss.loss_amount, 6),
                "loss_rate": round(loss.loss_rate, 4)
            }
            for i, loss in enumerate(loss_rankings)
        ]
    
    def _json_serializer(self, obj) -> Any:
        """
        自定义JSON序列化器
        
        Args:
            obj: 需要序列化的对象
            
        Returns:
            可序列化的对象
        """
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, Decimal):
            return float(obj)
        elif hasattr(obj, '__dict__'):
            return obj.__dict__
        else:
            return str(obj)
    
    def _validate_json_format(self, json_content: str) -> None:
        """
        验证JSON格式
        
        Args:
            json_content: JSON字符串
            
        Raises:
            ValueError: 如果JSON格式无效
        """
        try:
            # 尝试解析JSON以验证格式
            parsed_data = json.loads(json_content)
            
            # 验证必要的字段
            required_fields = ["metadata", "overall_statistics"]
            for field in required_fields:
                if field not in parsed_data:
                    raise ValueError(f"缺少必要字段: {field}")
            
            # 验证元数据结构
            metadata = parsed_data["metadata"]
            if "generation_time" not in metadata:
                raise ValueError("元数据缺少生成时间")
            
            self.logger.debug("JSON格式验证通过")
            
        except json.JSONDecodeError as e:
            raise ValueError(f"JSON格式无效: {str(e)}")
        except Exception as e:
            raise ValueError(f"JSON验证失败: {str(e)}")
    
    def generate_summary_json(self, statistics: StatisticsResult) -> str:
        """
        生成简化的JSON摘要
        
        Args:
            statistics: 统计结果
            
        Returns:
            简化的JSON摘要字符串
        """
        try:
            self.logger.info("开始生成JSON摘要")
            
            summary_data = {
                "summary": {
                    "generated_time": datetime.now().isoformat(),
                    "total_trades": statistics.overall_stats.total_trades if statistics.overall_stats else 0,
                    "win_rate": round(statistics.overall_stats.total_win_rate, 2) if statistics.overall_stats else 0,
                    "total_profit_rate": round(statistics.overall_stats.total_profit_rate, 2) if statistics.overall_stats else 0,
                    "token_count": len(statistics.token_stats) if statistics.token_stats else 0,
                    "strategy_count": len(statistics.strategy_stats) if statistics.strategy_stats else 0,
                    "top_profit": {
                        "amount": round(statistics.profit_rankings[0].profit_amount, 4) if statistics.profit_rankings else 0,
                        "rate": round(statistics.profit_rankings[0].profit_rate, 2) if statistics.profit_rankings else 0
                    },
                    "top_loss": {
                        "amount": round(statistics.loss_rankings[0].loss_amount, 4) if statistics.loss_rankings else 0,
                        "rate": round(statistics.loss_rankings[0].loss_rate, 2) if statistics.loss_rankings else 0
                    }
                }
            }
            
            json_content = json.dumps(
                summary_data, 
                ensure_ascii=False, 
                indent=2
            )
            
            self.logger.info("JSON摘要生成完成")
            return json_content
            
        except Exception as e:
            self.logger.error(f"生成JSON摘要失败: {str(e)}")
            raise
    
    def save_to_file(self, json_content: str, file_path: str) -> None:
        """
        保存JSON内容到文件
        
        Args:
            json_content: JSON内容
            file_path: 文件路径
        """
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(json_content)
            
            self.logger.info(f"JSON报告已保存到: {file_path}")
            
        except Exception as e:
            self.logger.error(f"保存JSON文件失败: {str(e)}")
            raise 