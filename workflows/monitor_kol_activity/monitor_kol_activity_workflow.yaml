name: "监控KOL账号的活动记录以获取目标代币"
description: "监控KOL账号的活动记录以获取目标代币"

nodes:
  - name: "FindTargetTokensNode2"
    node_type: "input"
    interval: 1
    generate_data: workflows.monitor_kol_activity.handler.filter_target_tokens
    flow_control:
      max_pending_messages: 10
      check_interval: 1
      enable_flow_control: true

  - name: "SendMessageToChannelNode2"
    node_type: "storage"
    depend_ons: ["FindTargetTokensNode2"]
    store_data: workflows.monitor_kol_activity.handler.send_message_to_channel
    validate: workflows.monitor_kol_activity.handler.validate 