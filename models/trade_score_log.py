from beanie import Document, Link
from pydantic import Field
from typing import Optional, Any, Dict
from datetime import datetime
from models.kol_wallet import KOLWallet
from pymongo import IndexModel, ASCENDING

class TradeScoreLog(Document):
    """交易打分日志记录"""
    
    # 交易对标识
    buy_trade_record_id: str = Field(..., description="关联的买入TradeRecord的ID")
    sell_trade_record_id: str = Field(..., description="关联的卖出TradeRecord的ID")
    
    # KOL和策略信息
    kol_wallet: Link[KOLWallet] = Field(..., description="关联的KOL钱包")
    kol_wallet_address: str = Field(..., description="关联的KOL钱包地址 (冗余存储，用于查询优化)", index=True)
    strategy_name: str = Field(..., description="打分时应用的策略名称")
    
    # 盈亏信息
    pnl_at_scoring: float = Field(..., description="打分时计算的该笔交易对的盈亏 (PnL)")
    
    # 打分结果
    positive_score_applied: float = Field(default=0.0, description="本次应用的加分值")
    negative_score_applied: float = Field(default=0.0, description="本次应用的扣分值 (通常为负数或0)")
    
    # 快照当时的打分参数，用于追溯和调试
    scoring_params_snapshot: Dict[str, Any] = Field(default_factory=dict, description="打分时使用的参数快照")
    
    # 时间戳
    scored_at: datetime = Field(default_factory=datetime.utcnow, description="打分发生时间")

    @property
    def net_score_applied(self) -> float:
        """计算本次应用的净分数"""
        return self.positive_score_applied + self.negative_score_applied

    @property
    def is_profitable(self) -> bool:
        """判断该交易是否盈利"""
        return self.pnl_at_scoring > 0

    class Settings:
        name = "trade_score_logs"
        indexes = [
            # 核心索引，用于快速检查某交易对、某KOL、某策略是否已打分
            IndexModel([
                ("buy_trade_record_id", ASCENDING),
                ("sell_trade_record_id", ASCENDING),
                ("kol_wallet_address", ASCENDING),
                ("strategy_name", ASCENDING)
            ], unique=True, name="trade_pair_kol_strategy_unique"),
            
            # 单字段索引 (使用 Beanie 偏好的列表元组格式)
            [("kol_wallet._id", ASCENDING)],
            [("strategy_name", ASCENDING)], # strategy_name 已在复合索引中，但单独索引也可能有用
            [("scored_at", ASCENDING)],
            [("buy_trade_record_id", ASCENDING)], # buy_trade_record_id 已在复合索引中
            [("sell_trade_record_id", ASCENDING)],# sell_trade_record_id 已在复合索引中
        ]
        
    def __str__(self):
        return f"TradeScoreLog(buy={self.buy_trade_record_id}, sell={self.sell_trade_record_id}, kol={self.kol_wallet}, strategy={self.strategy_name}, net_score={self.net_score_applied})" 