# KOL 活动延迟监控 - 需求规格 (AI生成)

## 1. 概述

项目需要一个监控系统，用于检测KOL钱包活动记录中的时间字段差异，并在发现异常时通知管理员。本功能将作为一个持续运行的工作流实现，对两类KOL钱包（已导入的关注列表和未导入的）进行单独监控。所有告警事件及通知发送的尝试都将被记录到数据库中，以便审计和分析。

## 2. 背景

在当前系统中，KOL钱包活动有两个关键时间字段：
- `created_at`: UTC时区的datetime对象，表示记录创建时间
- `timestamp`: 整数Unix时间戳，表示东八区时间

这两个字段应该表示相同或非常接近的时间点（考虑到不同时区的转换）。如果这两个字段之间存在显著差异，可能表明数据处理或时间同步出现问题。

## 3. 需求详情

### 3.1 功能需求

1. **检测两类KOL**
   - 系统应分别监控两类KOL钱包：
     - 类别A: `imported_from_following = true` 的KOL钱包
     - 类别B: `imported_from_following = false` 或 `null` 的KOL钱包

2. **时间差异检测**
   - 针对每个类别，系统应从中选择`last_active`不为空且时间最新的钱包
   - 获取该钱包的最新活动记录(按`created_at`排序，因为这代表最近的活动变化)
   - 计算该活动记录内的`created_at`与`timestamp`字段之间的时间差异
   - 监控频率：每10秒执行一次检测

3. **连续告警触发机制**
   - 系统应分别跟踪两类KOL的告警状态
   - 仅当同一类别连续3次检测到时间差异超过阈值(60秒)时，才触发告警
   - 时间差异低于阈值时，连续计数应重置为0

4. **告警抑制**
   - 针对同一类别的KOL，在触发告警后的一段时间内(可配置，默认30分钟)不再重复发送告警
   - 即使在抑制期内检测到新的异常，也应只记录日志而不发送告警
   - 抑制期结束后，如果仍连续检测到异常，可再次触发告警

5. **通知管理员**
   - 当需要发送告警时，系统应通过Telegram向所有配置的管理员发送通知
   - 通知内容应包含:
     - 告警类型和KOL类别
     - 钱包地址
     - 交易哈希
     - `created_at` (UTC时间，ISO格式)
     - `timestamp` (原始整数值)
     - `timestamp` (转换为UTC时间，ISO格式)
     - `timestamp` (转换为东八区UTC+8时间，ISO格式)
     - 计算出的时间差异(秒)

6. **告警事件持久化**
   - 当系统判断一个差异满足所有触发告警的条件（包括连续次数、不在抑制期内）时，应在数据库中创建一条"告警事件记录"。
   - 此记录应包含：
     - 监控类型标识 (e.g., "kol_activity_timestamp_discrepancy")
     - KOL类别 (imported/non_imported)
     - 告警触发的UTC时间
     - 相关的KOL钱包地址
     - 相关活动的交易哈希
     - 计算出的时间差异秒数
     - 告警的核心消息文本
     - 其他详细信息 (如 `created_at`, `timestamp` 的原始值和转换值等)

7. **通知发送日志持久化**
   - 对于每一次尝试向管理员发送Telegram通知的操作（无论成功或失败），都应在数据库中创建一条"通知发送日志记录"。
   - 此记录应包含：
     - 关联的告警事件记录ID
     - 接收通知的管理员Telegram Chat ID
     - 发送尝试的UTC时间
     - 发送状态 (success/failure)
     - 发送消息的内容预览 (避免存储完整消息，可截断)
     - 如果发送失败，记录相关的错误信息。

### 3.2 配置要求

系统应支持以下配置项:

1. **告警触发设置**
   - `alert_threshold_seconds`: 触发告警的时间差异阈值(默认60秒)
   - `consecutive_alerts_required`: 触发告警所需的连续检测次数(默认3次)
   - `alert_suppression_minutes`: 告警抑制时间(默认30分钟)

2. **通知渠道**
   - `admin_telegram_chat_ids`: 接收告警通知的管理员Telegram聊天ID列表

3. **配置存储**
   - 所有配置应存储在MongoDB的`Config`集合中，类型为`application_config`的文档内

### 3.3 非功能需求

1. **性能要求**
   - 检测逻辑应在1秒内完成，不应影响其他系统功能
   - 数据库查询应高效，使用适当的索引

2. **可靠性要求**
   - 系统应能处理临时数据库连接失败
   - 通知发送失败应记录日志，但不影响后续检测周期

3. **日志记录**
   - 系统应详细记录每次检测的结果，包括:
     - 选定的KOL钱包
     - 检测到的活动记录
     - 计算出的时间差异 (基于 `created_at` 和 `timestamp`)
     - 当前连续告警计数
     - 告警触发或抑制事件
     - 所有错误和异常
   - **新增**: 记录告警事件和通知发送日志成功保存到数据库的动作。

## 4. 测试标准

系统应满足以下测试标准:

1. **功能测试**
   - 能正确选择每类KOL中`last_active`最新且非空的钱包
   - 能准确计算`created_at`与`timestamp`之间的时间差异
   - 能正确实现连续告警计数逻辑
   - 能正确实现告警抑制功能
   - 在需要时能成功发送通知到所有管理员
   - **新增**: 验证在告警条件满足时，`AlertEventRecord` 能被正确创建并包含所有必要信息。
   - **新增**: 验证在尝试发送通知（无论成功或失败）后，`NotificationLogRecord` 能被正确创建并包含所有必要信息，且能关联到正确的 `AlertEventRecord`。

2. **边界条件测试**
   - 处理时间差异刚好等于阈值的情况
   - 处理连续计数刚好达到要求次数的情况
   - 处理告警抑制期刚好结束的情况

3. **错误处理测试**
   - 优雅处理无符合条件的KOL钱包情况
   - 优雅处理KOL钱包无活动记录情况
   - 优雅处理数据库查询失败情况
   - 优雅处理通知发送失败情况

## 5. 交付物

1. **代码实现**
   - 工作流处理逻辑 (`handler.py`)
   - 工作流定义 (YAML配置文件)
   - **新增**: 数据模型定义 (`monitor_alert_state.py`, `alert_event_record.py`, `notification_log_record.py`)

2. **文档**
   - 技术实现方案
   - 测试用例设计
   - 部署和配置指南 