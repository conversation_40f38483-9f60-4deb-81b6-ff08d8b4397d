# KOL活动监控工作流 - GMGN自动交易功能 - 技术实现方案

## 1. 概述

本方案描述了在KOL活动监控工作流中集成GMGN自动交易功能的具体技术实现步骤。核心修改将涉及通用交易接口的定义、GMGN作为该接口的具体实现、新的`TradeRecord`数据模型的引入，以及对现有`handler.py`和`sell_signal_handler.py`的调整。

## 2. 配置文件与数据模型变更

### 2.1. 配置模型扩展

在 `models/config.py` 中的 `SingleKolStrategyConfig`（或全局的 `KolActivityConfig`，取决于配置粒度）中扩展交易相关参数。这里以 `SingleKolStrategyConfig` 为例：

```python
# In models/config.py within SingleKolStrategyConfig
from typing import Optional, List
from pydantic import BaseModel, Field

class SingleKolStrategyConfig(BaseModel):
    # ... existing fields ...
    strategy_name: str
    is_active: bool = True
    # ... other existing strategy params ...

    # --- Auto-Trading General Config ---
    auto_trade_enabled: bool = Field(default=False, description="General switch for this strategy's trading")
    trade_provider: str = Field(default="gmgn", description="Default provider, allows future expansion")
    include_trade_details_in_notification: bool = Field(default=True)
    # solana_rpc_url: Optional[str] = Field(default=None, description="Optional specific Solana RPC URL for this strategy, overrides global .env if set")

    # --- GMGN Specific Config (if trade_provider is 'gmgn' and auto_trade_enabled) ---
    gmgn_api_host: Optional[str] = Field(default=None, description="GMGN API 主机，例如 https://gmgn.ai/defi/v2。优先从策略配置中读取，若无则尝试读取环境变量 GMGN_API_HOST。")
    gmgn_private_key_env_var: Optional[str] = Field(default="GMM_DEFAULT_WALLET_PK", description="存储此策略交易私钥的环境变量名称。")
    gmgn_sol_wallet_address: Optional[str] = Field(default=None, description="交易钱包的公钥（如果私钥有效则自动派生，或在提供时用于验证）。")
    
    # Buy parameters
    # gmgn_buy_input_token_address: str = Field(default="So11111111111111111111111111111111111111112", description="e.g., SOL")
    gmgn_auto_trade_buy_amount_sol: float = Field(default=0.01, description="此策略执行的每笔买入交易花费的 SOL 数量。")
    gmgn_buy_slippage_percentage: float = Field(default=1.0, description="GMGN 买入交易的滑点（例如，1.0 表示 1%）。")
    gmgn_buy_priority_fee: Optional[float] = Field(default=0.00005, description="GMGN 买入交易的优先费（SOL）。")

    # --- GMGN Specific Sell Config (if trade_provider is 'gmgn' and gmgn_enable_auto_sell) ---
    gmgn_enable_auto_sell: bool = Field(default=False, description="Specific switch for auto-selling based on this strategy's buy")
    # gmgn_sell_output_token_address: str = Field(default="So11111111111111111111111111111111111111112", description="e.g., SOL")
    gmgn_sell_slippage_percentage: float = Field(default=1.0, description="GMGN 卖出交易的滑点（例如，1.0 表示 1%）。")
    gmgn_sell_priority_fee: Optional[float] = Field(default=0.00005)

class KolActivityConfig(BaseModel): # Assuming this is your top-level config for the workflow
    buy_strategies: List[SingleKolStrategyConfig]
    # ... other global settings ...
```

### 2.2. 新增数据模型: `TradeRecord`

创建一个新的 Beanie ODM 模型 `models/trade_record.py` 来存储每笔交易的详细信息。

```python
# models/trade_record.py
from beanie import Document, PydanticObjectId
from typing import Optional, Dict, Any
from datetime import datetime, timezone
from pydantic import BaseModel, Field
from enum import Enum

class TradeStatus(str, Enum):
    PENDING = "pending"
    SUCCESS = "success"
    FAILED = "failed"
    SKIPPED = "skipped" # Skipped due to config, pre-check, etc.
    MANUAL_INTERVENTION_REQUIRED = "manual_intervention_required"

class TradeType(str, Enum):
    BUY = "buy"
    SELL = "sell"

class TradeRecord(Document):
    signal_id: PydanticObjectId             # Link to the Signal that triggered this trade
    strategy_name: str                      # Name of the strategy that triggered this trade
    trade_provider: str                     # e.g., "gmgn", "jupiter", "manual"
    trade_type: TradeType                   # "buy" or "sell"
    status: TradeStatus = Field(default=TradeStatus.PENDING)
    
    token_in_address: str
    token_in_amount: Optional[float] = Field(default=None, description="Amount intended to spend/sell")
    token_in_actual_amount: Optional[float] = Field(default=None, description="Amount actually spent/sold (if available)")
    
    token_out_address: str
    token_out_amount_expected: Optional[float] = Field(default=None, description="Amount expected to receive")
    token_out_actual_amount: Optional[float] = Field(default=None, description="Amount actually received")

    wallet_address: str                     # The wallet executing the trade
    tx_hash: Optional[str] = None
    
    error_message: Optional[str] = None
    provider_response_raw: Optional[Dict[str, Any]] = Field(default=None, description="Store raw response from provider for debugging")

    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    executed_at: Optional[datetime] = None  # Timestamp of successful execution

    class Settings:
        name = "trade_records"
        indexes = [
            "signal_id",
            "tx_hash",
            "status",
            "trade_provider",
            "wallet_address",
            "token_in_address",
            "token_out_address",
            "created_at",
            "updated_at"
        ]

    async def save(self, *args, **kwargs):
        self.updated_at = datetime.now(timezone.utc)
        await super().save(*args, **kwargs)

# 需要在 models/__init__.py 的 init_db 函数中添加 TradeRecord
# from models.trade_record import TradeRecord
# await init_beanie(database=db, document_models=[..., TradeRecord])
```

### 2.3. `Signal` 模型调整

在 `models/signal.py` 中，`Signal` 模型可以添加一个字段来引用相关的交易记录。

```python
# In models/signal.py
from beanie import Document, PydanticObjectId
from typing import Optional, List 
# ... other imports

class Signal(Document):
    # ... existing fields: token_address, token_name, token_symbol, signal_type, trigger_conditions, hit_kol_wallets, trigger_timestamp, status ...
    
    # New field to link to trade records
    trade_record_ids: List[PydanticObjectId] = Field(default_factory=list)

    class Settings:
        name = "signals"
        # ... existing indexes ...
```

## 3. 核心代码实现

### 3.1. 交易接口定义 (`utils/trade_interface.py`)

定义一个抽象基类 `TradeInterface`，它规定了所有交易服务提供者（如GMGN、未来可能的Jupiter等）必须实现的方法。

```pseudocode
# 文件路径: utils/trade_interface.py

# 定义 TradeResult 数据类 (例如使用 Pydantic BaseModel)
# 包含字段:
#   status: TradeStatus (交易状态枚举，如 SUCCESS, FAILED, SKIPPED)
#   tx_hash: Optional[str] (交易哈希)
#   error_message: Optional[str] (错误信息)
#   provider_response_raw: Optional[Dict] (交易服务商原始响应)
#   actual_amount_in: Optional[float] (实际输入数量)
#   actual_amount_out: Optional[float] (实际输出数量)
#   executed_at: Optional[datetime] (交易执行时间)

# 定义 TradeInterface 抽象基类 (继承自 ABC)
  # 定义抽象方法: execute_trade
  #   参数:
  #     trade_type: TradeType (买入/卖出枚举)
  #     input_token_address: str
  #     output_token_address: str
  #     amount_input_token: float (输入代币数量) # 对于卖出，这可能是代币数量；对于买入，这可能是 SOL 的数量
  #     wallet_private_key_b58: str (钱包私钥)
  #     wallet_address: str (钱包公钥)
  #     strategy_snapshot: Dict (当前策略配置快照，包含如滑点、优先费等参数)
  #     signal_id: PydanticObjectId (关联的信号ID)
  #     trade_record_id: PydanticObjectId (关联的交易记录ID)
  #   返回: TradeResult 对象
  #   描述: 此方法负责执行实际的交易操作。
  #          实现时应包含错误处理和重试逻辑（如果适用）。
  #          在交易成功或失败后，应返回一个包含所有相关信息的 TradeResult 对象。

  # (get_balance 方法已移除，因为GMGN API不直接提供余额查询，且我们尽量避免直接RPC调用)
```

### 3.2. GMGN交易服务实现 (`utils/gmgn_trade_service.py`)

实现 `TradeInterface` 的具体类 `GmgnTradeService`。此类将通过调用外部Node.js脚本 (`gmgn_test.mjs`) 来执行交易，而不是直接进行HTTP API调用和本地Solana签名。

```pseudocode
# 文件路径: utils/gmgn_trade_service.py

# 导入必要的模块 (os, asyncio, logging, json, Decimal, TradeInterface, TradeResult, TradeType, TradeStatus, PydanticObjectId)
# 不再需要导入 httpx, solders.keypair, solders.transaction, solders.signature
# 初始化 logger

# 定义常量 (例如 SOL_MINT_ADDRESS, NODE_SCRIPT_PATH)
# GMGN_API_ENDPOINTS 将不再由此服务直接使用，相关端点调用在Node.js脚本中处理

# 类 GmgnTradeService (继承自 TradeInterface)
  # 构造函数 (__init__)
  #   参数:
  #     gmgn_api_host: str (GMGN API 主机地址，例如 "https://gmgn.ai")
  #   (不再需要 http_client 参数)

  # 实现 execute_trade 方法:
  #   参数: (与接口定义一致)
  #     trade_type, input_token_address, output_token_address, amount_input_token,
  #     wallet_private_key_b58, wallet_address, strategy_snapshot, signal_id, trade_record_id
  #   返回: TradeResult 对象
  #
  #   基本逻辑:
  #   1. 从 strategy_snapshot 中提取 GMGN 特定参数 (如滑点 `gmgn_buy_slippage_percentage` 或 `gmgn_sell_slippage_percentage`, 优先费 `gmgn_buy_priority_fee` 或 `gmgn_sell_priority_fee`)。
  #      这些参数将作为命令行参数传递给 Node.js 脚本。
  #   2. 将 `amount_input_token` 转换为原子单位字符串 (例如 lamports for SOL)。
  #   3. **构建并执行 Node.js 脚本命令**:
  #      - 使用 `asyncio.create_subprocess_exec` 执行 `node <NODE_SCRIPT_PATH> [args...]`。
  #      - 命令行参数 (`args`) 应包括:
  #        - `wallet_private_key_b58`
  #        - `input_token_address`
  #        - `output_token_address`
  #        - 原子单位的 `amount_input_token_str`
  #        - `slippage` (从策略快照获取)
  #        - `priority_fee` (从策略快照获取)
  #        - `gmgn_api_host` (传递给脚本供其内部使用)
  #        - `wallet_address` (传递给脚本供其内部使用)
  #      - 捕获脚本的 `stdout` 和 `stderr`。
  #      - 记录 `stderr` 中的任何输出。
  #   4. **处理 Node.js 脚本的输出**:
  #      - 检查脚本的 `returncode`。如果非零，表示脚本执行失败。
  #        - 返回 TradeResult(status=FAILED, error_message="Node.js script execution failed with return code X. Stderr: <stderr_content>", provider_response_raw={"raw_stdout": stdout, "raw_stderr": stderr})。
  #      - 如果 `returncode` 为零，尝试将 `stdout` 解析为 JSON。
  #        - 如果 JSON 解析失败: 返回 TradeResult(status=FAILED, error_message="Failed to parse JSON output from Node.js script. Raw stdout: <stdout_content>", provider_response_raw={"raw_stdout": stdout})。
  #      - 如果 JSON 解析成功，从解析后的对象中提取:
  #        - `status`: Node.js 脚本报告的交易状态 ("success", "error", "submitted_no_poll")。
  #        - `txHash`: 交易哈希 (如果可用)。
  #        - `message`: 来自脚本的附加消息。
  #        - `quoteResponse`, `submitResponse`, `statusResponse`, `details`: 作为 `provider_response_raw` 的一部分。
  #   5. **构建并返回 TradeResult**:
  #      - 如果脚本报告 `status == "success"`:
  #        - 从 `quoteResponse` (如果存在于脚本输出中) 提取 `inAmount` 和 `outAmount` 并转换为 `Decimal` 作为 `actual_amount_in` 和 `actual_amount_out`。
  #        - 返回 TradeResult(status=TradeStatus.SUCCESS, tx_hash=txHash, executed_at=datetime.now(), actual_amount_in=..., actual_amount_out=..., provider_response_raw=...)。
  #      - 如果脚本报告 `status == "submitted_no_poll"`:
  #        - 返回 TradeResult(status=TradeStatus.PENDING, tx_hash=txHash, error_message=message, provider_response_raw=...)。
  #      - 如果脚本报告 `status == "error"` (或任何其他非成功状态):
  #        - 返回 TradeResult(status=TradeStatus.FAILED, tx_hash=txHash, error_message=message, provider_response_raw=...)。
  #   6. **异常处理**: 捕获 `FileNotFoundError` (如果Node.js脚本未找到) 和其他执行脚本时可能发生的通用异常。
  #      - 返回相应的 TradeResult(status=FAILED, error_message=...)。

  # (get_balance 方法已移除，余额检查不由该服务负责)
  # (所有直接的 GMGN API 调用、交易签名、提交、轮询逻辑均已移至 Node.js 脚本中)

# 类 GmgnTradeService (继承自 TradeInterface)
  # 构造函数 (__init__)
  #   参数:
  #     gmgn_api_host: str (GMGN API 主机地址，例如 "https://gmgn.ai")
  #     (不再需要 http_client)
  #   内部存储 gmgn_api_host (传递给Node.js脚本)

  # 实现 execute_trade 方法:
  #   参数: (与接口定义一致)
  #     trade_type, input_token_address, output_token_address, amount_input_token,
  #     wallet_private_key_b58, wallet_address, strategy_snapshot, signal_id, trade_record_id
  #   返回: TradeResult 对象
  #
  #   基本逻辑:
  #   1. 从 strategy_snapshot 中提取 GMGN 特定参数 (如滑点 `gmgn_buy_slippage_percentage` 或 `gmgn_sell_slippage_percentage`, 优先费 `gmgn_buy_priority_fee` 或 `gmgn_sell_priority_fee`)。
  #   2. 将 `amount_input_token` (例如 SOL 数量) 转换为原子单位字符串 (例如 lamports)。
  #   3. **构建 Node.js 脚本的命令行参数**:
  #      - `node_script_path` (例如, "gmgn_test.mjs")
  #      - `wallet_private_key_b58`
  #      - `input_token_address`
  #      - `output_token_address`
  #      - `amount_input_token_lamports_str`
  #      - `slippage` (来自策略快照)
  #      - `priority_fee` (来自策略快照)
  #   4. **执行 Node.js 脚本**:
  #      - 使用 `asyncio.create_subprocess_exec` 异步执行 Node.js 脚本，捕获 `stdout` 和 `stderr`。
  #      - 记录 `stderr` 内容。
  #      - 检查 `process.returncode`。如果非零，表示脚本执行失败。
  #        - 构造并返回 TradeResult(status=TradeStatus.FAILED, error_message="Node.js script failed with code {returncode}. Stderr: {stderr_content}")。
  #   5. **解析 Node.js 脚本的 stdout**:
  #      - 尝试将 `stdout` 解析为 JSON。
  #        - 若解析失败: 构造并返回 TradeResult(status=TradeStatus.FAILED, error_message="Failed to parse JSON from Node.js script: {stdout_content}")。
  #      - 若解析成功，从中提取:
  #        - `status_from_script` (e.g., "success", "error", "submitted_no_poll")
  #        - `txHash` (交易哈希, 如果存在)
  #        - `message` (来自脚本的消息, 如果存在)
  #        - `quoteResponse`, `submitResponse`, `statusResponse`, `details` 等，用于填充 `provider_response_raw`。
  #   6. **根据脚本结果构建 TradeResult**:
  #      - if `status_from_script` == "success":
  #        - 从 `quoteResponse` 解析 `actual_amount_in` 和 `actual_amount_out`。
  #        - 返回 TradeResult(status=TradeStatus.SUCCESS, tx_hash=txHash, actual_amount_in=..., actual_amount_out=..., executed_at=datetime.now(timezone.utc), provider_response_raw=...)
  #      - elif `status_from_script` == "submitted_no_poll":
  #        - 返回 TradeResult(status=TradeStatus.PENDING, tx_hash=txHash, error_message=message, provider_response_raw=...)
  #      - else (e.g., "error" or unknown):
  #        - 返回 TradeResult(status=TradeStatus.FAILED, tx_hash=txHash, error_message=message or "Node.js script reported an error", provider_response_raw=...)
  #   7. **异常处理**:
  #      - 捕获 `FileNotFoundError` 如果 `NODE_SCRIPT_PATH` 无效。
  #      - 捕获其他可能的异常 (例如 `asyncio.create_subprocess_exec` 本身的错误)。
  #      - 返回相应的 TradeResult(status=TradeStatus.FAILED)。
```

### 3.3. 工作流处理器修改 (`workflows/monitor_kol_activity/handler.py` 和 `sell_signal_handler.py`)

这两个处理器将需要实例化并使用 `GmgnTradeService`（或通过配置选择的 `TradeInterface` 实现）。

```pseudocode
# 文件路径: workflows/monitor_kol_activity/handler.py (Buy Signal Handler)

# 导入 TradeInterface, GmgnTradeService, TradeRecord, TradeStatus, TradeType, Signal, SingleKolStrategyConfig, KolActivityConfig, PydanticObjectId 等

# 在 BuySignalHandlerNode 的 execute 方法 (或类似的核心处理逻辑中):
  # ... (现有逻辑，例如解析信号数据) ...

  # 遍历与信号关联的策略 (signal.strategy_configs or similar)
  # for strategy_config in relevant_strategies:
    # 检查策略是否启用了自动交易 (strategy_config.auto_trade_enabled)
    # if not strategy_config.auto_trade_enabled:
      # continue 或记录跳过

    # 1. 创建 TradeRecord 实例
    #    trade_record = TradeRecord(
    #        signal_id=signal.id,
    #        strategy_name=strategy_config.strategy_name,
    #        trade_provider=strategy_config.trade_provider, # e.g., "gmgn"
    #        trade_type=TradeType.BUY,
    #        token_in_address=strategy_config.gmgn_buy_input_token_address, # e.g., SOL
    #        token_in_amount=strategy_config.gmgn_buy_amount_input_token,
    #        token_out_address=signal.token_address, # The token to buy
    #        wallet_address=strategy_config.gmgn_sol_wallet_address, # Derived or loaded
    #        status=TradeStatus.PENDING
    #    )
    #    await trade_record.save()

    # 2. 获取交易服务实例
    #    trade_service: TradeInterface
    #    if strategy_config.trade_provider == "gmgn":
    #        # 从 strategy_config 或 .env 加载 GMGN API host
    #        gmgn_api_host_from_strategy = strategy_config.gmgn_api_host
    #        gmgn_api_host_to_use = gmgn_api_host_from_strategy if gmgn_api_host_from_strategy else os.getenv("GMGN_API_HOST")
    #        # 从 .env 加载钱包私钥 (使用 strategy_config.gmgn_private_key_env_var)
    #        private_key = os.getenv(strategy_config.gmgn_private_key_env_var)
    #        if not private_key:
    #            # 更新 TradeRecord 状态为 FAILED 或 SKIPPED，记录错误
    #            # continue
    #        # (可选) 验证 private_key 是否对应 gmgn_sol_wallet_address
    #        current_wallet_address = strategy_config.gmgn_sol_wallet_address # 获取钱包公钥
    #
    #        trade_service = GmgnTradeService(gmgn_api_host=gmgn_api_host_to_use)
    #    else:
    #        # 记录不支持的 provider，更新 TradeRecord 状态
    #        # continue

    # 3. 执行交易
    #    try:
    #        trade_result = await trade_service.execute_trade(
    #            trade_type=TradeType.BUY,
    #            input_token_address=trade_record.token_in_address,
    #            output_token_address=trade_record.token_out_address,
    #            amount_input_token=trade_record.token_in_amount,
    #            wallet_private_key_b58=private_key,
    #            wallet_address=current_wallet_address, # 使用获取到的钱包公钥
    #            strategy_snapshot=strategy_config.model_dump(), # 传递策略配置
    #            signal_id=signal.id,
    #            trade_record_id=trade_record.id
    #            # solana_rpc_url 不再直接传递给 execute_trade，如果需要获取余额，GmgnTradeService内部会处理
    #        )
    #
    #        # 4. 更新 TradeRecord 根据 trade_result
    #        trade_record.status = trade_result.status
    #        trade_record.tx_hash = trade_result.tx_hash
    #        trade_record.error_message = trade_result.error_message
    #        trade_record.provider_response_raw = trade_result.provider_response_raw
    #        trade_record.token_in_actual_amount = trade_result.actual_amount_in
    #        trade_record.token_out_actual_amount = trade_result.actual_amount_out
    #        trade_record.executed_at = trade_result.executed_at
    #
    #        # 如果交易失败且需要通知管理员 (根据 trade_result.error_type 或特定错误信息判断)
    #        if trade_result.status == TradeStatus.FAILED and should_notify_admin(trade_result.error_message):
    #            # 调用发送管理员Telegram通知的函数 (传入trade_result详情)
    #            await send_admin_trade_error_notification(trade_record, trade_result, strategy_config)
    #
    #    except Exception as e: # 捕获 execute_trade 之外的、在handler中发生的意外错误
    #        logger.error(f"Unexpected error during trade execution for signal {signal.id}, trade_record {trade_record.id}: {e}", exc_info=True)
    #        trade_record.status = TradeStatus.FAILED
    #        trade_record.error_message = f"Handler-level exception: {str(e)}"
    #        # 考虑是否也为这类未知handler层错误发送管理员通知
    #        await send_admin_trade_error_notification(trade_record, TradeResult(status=TradeStatus.FAILED, error_message=trade_record.error_message), strategy_config)
    #    finally:
    #        await trade_record.save()
    #
    #        # 5. 将 trade_record.id 添加到 signal.trade_record_ids
    #        signal.trade_record_ids.append(trade_record.id)
    #        # Consider signal.save() here or at the end of all strategy processing

  # ... (现有通知逻辑) ...
  # 修改通知内容以包含交易结果 (如果 strategy_config.include_trade_details_in_notification)
  # 构建通知信息时，从相关的 TradeRecord 中获取交易状态、哈希等。
  # 例如: "购买 [Token] 信号. GMGN 交易状态: [trade_record.status], Tx: [trade_record.tx_hash or 'N/A']"

# ---

# 文件路径: workflows/monitor_kol_activity/sell_signal_handler.py (Sell Signal Handler)
# 类似地修改 SellSignalHandlerNode 的 execute 方法:

  # ... (现有逻辑) ...
  # for strategy_config in relevant_strategies:
    # 检查策略是否启用了自动卖出 (strategy_config.gmgn_enable_auto_sell)
    # if not strategy_config.auto_trade_enabled or not strategy_config.gmgn_enable_auto_sell:
      # continue

    # 1. 创建 TradeRecord 实例 (trade_type=TradeType.SELL)
    #    # 获取原始买入信号 (buy_signal_ref_id)
    #    original_buy_signal = await signal_dao.get_signal(current_sell_signal.buy_signal_ref_id) # 假设 current_sell_signal 是当前处理的卖出信号对象
    #    if not original_buy_signal:
    #        # 记录错误，原始买入信号未找到，跳过
    #        # continue
    #
    #    # 查找与原始买入信号关联的、成功的买入 TradeRecord
    #    # (需要一个 DAO 方法: trade_record_dao.find_successful_trade_for_signal(original_buy_signal.id, TradeType.BUY))
    #    original_buy_trade_record = await trade_record_dao.find_successful_trade_for_signal(original_buy_signal.id, TradeType.BUY)
    #
    #    amount_to_sell: Optional[float] = None
    #    if original_buy_trade_record and original_buy_trade_record.token_out_actual_amount and original_buy_trade_record.token_out_actual_amount > 0:
    #        amount_to_sell = original_buy_trade_record.token_out_actual_amount
    #    else:
    #        # 记录警告: 找不到原始买入交易记录或买入数量为0，无法确定卖出数量，跳过
    #        # 创建一个 SKIPPED 状态的 TradeRecord
    #        # trade_record = TradeRecord(status=TradeStatus.SKIPPED, error_message="Cannot determine sell amount from original buy trade.", ...)
    #        # await trade_record.save()
    #        # continue
    #
    #    # 如果 amount_to_sell 有效 (例如大于最小交易阈值，可选检查)
    #    trade_record = TradeRecord(
    #        signal_id=current_sell_signal.id, # 关联到当前卖出信号
    #        strategy_name=strategy_config.strategy_name,
    #        trade_provider=strategy_config.trade_provider,
    #        trade_type=TradeType.SELL,
    #        status=TradeStatus.PENDING,
    #        token_in_address=original_buy_signal.token_address, # 要卖出的代币
    #        token_in_amount=amount_to_sell,
    #        token_out_address=strategy_config.gmgn_sell_output_token_address, # e.g., SOL
    #        wallet_address=strategy_config.gmgn_sol_wallet_address # 假设与买入时配置一致
    #    )
    #    await trade_record.save()
    #
    #    # 如果 trade_record.status 仍然是 PENDING (即没有在确定数量时跳过)
    #    if trade_record.status == TradeStatus.PENDING:
    #        # 2. 获取交易服务实例 (同买入逻辑)
    #        #    gmgn_api_host_from_strategy = strategy_config.gmgn_api_host
    #        #    gmgn_api_host_to_use = gmgn_api_host_from_strategy if gmgn_api_host_from_strategy else os.getenv("GMGN_API_HOST")
    #        #    private_key = os.getenv(strategy_config.gmgn_private_key_env_var)
    #        #    current_wallet_address = strategy_config.gmgn_sol_wallet_address
    #        #    trade_service = GmgnTradeService(gmgn_api_host=gmgn_api_host_to_use)
    #
    #        # 3. 执行交易 (trade_type=TradeType.SELL)
    #        #    try:
    #        #        trade_result = await trade_service.execute_trade(
    #        #            trade_type=TradeType.SELL,
    #        #            input_token_address=trade_record.token_in_address,
    #        #            output_token_address=trade_record.token_out_address,
    #        #            amount_input_token=trade_record.token_in_amount, # 此处传入已计算好的数量
    #        #            wallet_private_key_b58=private_key,
    #        #            wallet_address=current_wallet_address, # 使用获取到的钱包公钥
    #        #            strategy_snapshot=strategy_config.model_dump(),
    #        #            signal_id=current_sell_signal.id,
    #        #            trade_record_id=trade_record.id
    #        #            # solana_rpc_url 不再直接传递
    #        #        )
    #        #        # 4. 更新 TradeRecord (同买入逻辑)
    #        #        trade_record.status = trade_result.status
    #        #        ...
    #        #    except Exception as e:
    #        #        trade_record.status = TradeStatus.FAILED
    #        #        trade_record.error_message = str(e)
    #        #    finally:
    #        #        await trade_record.save()
    #
    #        # 5. 将 trade_record.id 添加到 current_sell_signal.trade_record_ids (同买入逻辑，但关联到卖出信号)
    #        # current_sell_signal.trade_record_ids.append(trade_record.id)
    #        # await current_sell_signal.save()

  # ... (现有通知逻辑，修改以包含交易结果) ...
```

## 4. Solana 交易签名与执行 (辅助函数)

**本节内容已大幅更改。`utils/solana_utils.py` 中原先设想的用于签名、发送和确认 Solana 交易的复杂逻辑不再需要，因为这些操作现在已全部委托给外部的 Node.js 脚本 (`gmgn_test.mjs`)。**

Python端的 `GmgnTradeService` 将不再直接进行以下操作：
-   使用 `solders` 库进行交易签名。
-   使用 `solana-py` (或 `httpx` 直接调用RPC) 发送交易到 Solana 网络。
-   轮询确认交易状态。

因此，`utils/solana_utils.py` 文件 (如果最初计划包含这些复杂功能) 现在可以被移除，或者其内容被简化到仅包含一些非常通用的、与交易执行无关的 Solana 工具函数 (如果项目其他地方有此需求)。

**对于GMGN自动交易功能而言，Python代码不直接执行Solana签名和广播。**

```pseudocode
# 文件路径: utils/solana_utils.py (如果保留，则内容大幅简化或移除)

# (原先设想的 client_side_sign_transaction, sign_and_send_solana_transaction 等函数不再适用此模块)
# (如果此文件不再有其他用途，可以考虑删除)
```

## 5. 环境变量和配置

- `.env` 文件需要添加:
// ... existing code ...

## 6. 依赖包 (`pyproject.toml`)

确保 `pyproject.toml` 文件中包含以下依赖。由于核心交易逻辑转移到 Node.js，Python端的直接依赖有所改变。

```toml
[tool.poetry.dependencies]
# ... 现有依赖 ...
# httpx = "^0.27.0" # GmgnTradeService 不再直接使用 httpx 进行 GMGN API 调用
# solana = "^0.35.0" # GmgnTradeService 不再直接使用 solana-py 进行签名
# solders = "^0.20.0" # GmgnTradeService 不再直接使用 solders 进行签名

python-dotenv = "^1.0.0" # 可选，用于从.env文件加载环境变量
# beanie = "..." (确保beanie版本支持Link和ODM功能)
# pydantic = "..." (确保pydantic版本与beanie兼容)
# asyncio (标准库，用于 subprocess)
# json (标准库，用于解析Node.js脚本输出)
# decimal (标准库)
```
修改 `pyproject.toml` 后，运行 `poetry lock && poetry install` 来更新依赖。
**(注意: Node.js 脚本将有其自身的依赖项，如 `@solana/web3.js`, `axios` 或 `node-fetch` 等，这些在其 `package.json` 中管理，与Python环境分离)。**

## 7. 日志记录

- `GmgnTradeService` 中的日志应详细记录交易的每一步：
    - API 请求参数（注意避免记录完整的私钥等敏感信息）。
    - API 响应的关键部分。
    - 交易签名成功或失败。
    - 交易提交成功或失败。
    - 交易状态轮询的尝试和最终结果。
- 所有与特定信号触发的交易相关的日志，都应包含信号ID (例如 `[Signal: <signal_id>] [TradeRec: <trade_record_id>]`) 和交易类型（买/卖），以便于追踪和调试。
- `handler.py` 和 `sell_signal_handler.py` 中调用交易服务前后的日志也应清晰。

## 8. 错误处理

本节详细描述了在技术实现层面如何处理错误，与需求文档中的 `2.5. 错误处理与重试机制 (细化)` 部分相对应。

### 8.1. `GmgnTradeService` (`execute_trade` 方法)
-   **Node.js 脚本调用**:
    -   错误处理主要集中在对 `asyncio.create_subprocess_exec` 的调用以及对返回的进程的 `returncode`, `stdout`, `stderr` 的处理。
    -   `TradeResult` 对象应包含 `error_message` 来反映是脚本执行失败、脚本输出解析失败还是脚本内部报告的逻辑错误。
    -   `provider_response_raw` 应存储来自Node.js脚本的原始 `stdout` 和 `stderr` (尤其是在出错时)，以及解析后的JSON数据部分。
-   **不再直接处理API错误**: `GmgnTradeService` 不再直接调用GMGN API，因此不再直接处理HTTP错误或实现API级别的重试逻辑。这些应由Node.js脚本内部处理。如果Node.js脚本因API错误而失败，它应通过其 `stdout` (JSON状态/消息) 或 `stderr` 及返回码向Python层报告。
-   **不再进行客户端签名**: 签名逻辑已移至Node.js脚本。

### 8.2. `utils/solana_utils.py` (...)
-   **(本节调整)** 此模块的角色已大大减弱或可能被移除。Python端不再进行签名和广播。

### 8.3. 工作流处理器 (`