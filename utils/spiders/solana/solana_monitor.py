import base64
import traceback
from solana.rpc.api import Client
from solders.pubkey import Pubkey
from solders.signature import Signature
from solana.rpc.types import TokenAccountOpts  # 添加导入
from datetime import datetime
from typing import List, Optional, Dict, Any
import logging
import json
import time
import random
import aiohttp
import requests
import asyncio
import subprocess  # 添加subprocess模块导入
from dao.token_dao import TokenDAO
from models.token import Token
from functools import partial
from curl_cffi.requests import AsyncSession
from utils.spiders.solana.token_info import TokenInfo

class SolanaMonitor:
    
    # Solana 常用程序 ID
    PROGRAM_IDS = {
        "TOKEN_PROGRAM": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",  # SPL Token Program
        "METADATA_PROGRAM": "metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s",  # Metaplex Metadata Program
        "MEMO_PROGRAM": "MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr",  # Memo Program
        "SYSTEM_PROGRAM": "11111111111111111111111111111111",  # System Program
        "WRAPPED_SOL": "So11111111111111111111111111111111111111112",  # Wrapped SOL
        "ASSOCIATED_TOKEN": "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",  # Associated Token Account Program
    }
    
    def __init__(self, rpc_url: str = "https://alien-lively-sailboat.solana-mainnet.quiknode.pro/a3ee77771b543bbe6f3ee8177fe84d7bb32ac61c/", proxy: Optional[str] = None):
        """
        初始化 Solana 监控器
        
        Args:
            rpc_url: Solana RPC 节点地址
            proxy: 代理服务器地址，格式为 "http://host:port" 或 "socks5://host:port"
        """
        self.client = Client(rpc_url, proxy=proxy)
        self.logger = logging.getLogger(__name__)
        self._last_request_time = 0
        self._min_request_interval = 1.0  # 最小请求间隔（秒）
        
        # 缓存已知的代币信息
        self._token_cache = {}

    def _wait_for_rate_limit(self):
        """等待以遵守速率限制"""
        now = time.time()
        elapsed = now - self._last_request_time
        if elapsed < self._min_request_interval:
            time.sleep(self._min_request_interval - elapsed)
        self._last_request_time = time.time()
        
    async def _get_token_info_from_database(self, mint_address: str) -> Optional[Dict[str, Any]]:
        """从数据库获取代币信息"""
        try:
            # 首先从数据库获取
            token = await Token.get_or_create(mint_address)
            
            # 如果数据库中有完整信息，直接返回
            if token.name and token.symbol and token.decimals and token.first_mint_tx:
                result = {
                    "name": token.name,
                    "symbol": token.symbol,
                    "decimals": token.decimals,
                    "icon": token.icon,
                    "supply": token.supply,
                    "creator": token.creator,
                    "created_tx": token.created_tx,
                    "created_time": token.created_time,
                    "first_mint_tx": token.first_mint_tx,
                    "first_mint_time": token.first_mint_time,
                    "first_mint_amount": token.first_mint_amount,
                    "token_authority": token.token_authority,
                    "freeze_authority": token.freeze_authority,
                    "metadata_uri": token.metadata_uri,
                    "metadata_update_authority": token.metadata_update_authority,
                    "extensions": {
                        "coingecko_id": token.coingecko_id,
                        "coinmarketcap_id": token.coinmarketcap_id,
                        "website": token.website,
                        "twitter": token.twitter,
                        "telegram": token.telegram,
                        "discord": token.discord,
                        "medium": token.medium,
                        "description": token.description
                    }
                }
                if token.first_mint_tx is None:
                    token.first_mint_tx = token.created_tx
                if token.first_mint_time is None:
                    token.first_mint_time = token.created_time
                return result
            return None
        except Exception as e:
            self.logger.error(f"从数据库获取代币信息失败: {str(e)}")
            self.logger.error(f"错误类型: {type(e).__name__}")
            import traceback
            self.logger.error(f"错误堆栈: {traceback.format_exc()}")
            return None
    
    async def _get_token_info_from_solscan(self, mint_address: str) -> Optional[Dict[str, Any]]:
        """从 Solscan API 获取代币信息"""
        async def _save_to_db(token_data: Dict[str, Any]) -> None:
            token = await Token.get_or_create(mint_address)
            
            # 从 tokenInfo 获取基本信息
            base_info = token_data.get('tokenInfo', {})
            token.icon = base_info.get('icon')
            token.decimals = base_info.get('decimals')
            token.supply = base_info.get('supply')
            token.token_authority = base_info.get('tokenAuthority')
            token.freeze_authority = base_info.get('freezeAuthority')
            token.created_tx = base_info.get('created_tx')
            token.creator = base_info.get('creator')
            
            if base_info.get('created_time'):
                token.created_time = datetime.fromtimestamp(base_info.get('created_time'))
            
            # 获取扩展信息
            extensions = base_info.get('ownExtensions', {})
            token.coingecko_id = extensions.get('coingeckoId')
            token.coinmarketcap_id = extensions.get('coinMarketcapId')
            token.website = extensions.get('website')
            token.twitter = extensions.get('twitter')
            token.telegram = extensions.get('telegram')
            token.discord = extensions.get('discord')
            token.medium = extensions.get('medium')
            token.description = extensions.get('description')
            
            # 从元数据获取名称和符号
            metadata = token_data.get('metadata', {}).get('data', {})
            token.name = metadata.get('name')
            token.symbol = metadata.get('symbol')
            token.metadata_uri = metadata.get('uri')
            token.metadata_update_authority = token_data.get('metadata', {}).get('updateAuthority')
            
            # 获取首次铸造信息
            first_mint = base_info.get('first_mint_tx')
            created_time = base_info.get('created_time')
            if first_mint:
                token.first_mint_tx = base_info.get('first_mint_tx')
                token.first_mint_time = datetime.fromtimestamp(base_info.get('first_mint_time', 0))
            elif created_time:
                token.first_mint_time = datetime.fromtimestamp(created_time)
                token.first_mint_tx = base_info.get('created_tx')
            
            # 保存到数据库
            await token.save()
            self.logger.info(f"保存代币信息到数据库: {token.name}, {token.symbol}, {token.decimals}")
        
        max_retries = 5
        retry_count = 0
        last_error = None
        
        while retry_count < max_retries:
            try:
                self.logger.info(f"[尝试 {retry_count+1}/{max_retries}] 使用curl获取Solscan API数据: {mint_address}")
                
                # 构建curl命令 - 不使用代理，直接请求
                curl_command = [
                    'curl',
                    f'https://api-v2.solscan.io/v2/account?address={mint_address}',
                    '-H', 'accept: application/json, text/plain, */*',
                    '-H', 'accept-language: zh-CN,zh;q=0.9',
                    '-H', 'cache-control: no-cache',
                    '-b', 'cf_clearance=rKn5eu4dryT31o.iizV_SEEnhfYsE.47LS427PSnr2U-**********-*******-EP5i5LJSsvsS7Q9SZdeNRg09xX5SloOjM5pMu3dQudxYB21xfKIdKEAB4YPus3BLpTnuH8pv8naaF8JPgeq91TcC9yP4GNX2_GGvLW_FGDoOSbyVc.V9MC6ohkFdZ_.n1HDCY4ZSpMoyNRol8Fks5Fbj_mrIKRTCbZ3OKukAutIpAHmMfaQW.T9FVD6vspVnU_vSil6boeNH0HcqX2ExUl4ZvpLzAZytXNrHS6Bj10K560W6uC1ZJN2JLgPH8_QQl6wii55bFkNxIO_TEQTpUyv8iqIsCuVKA1DXH1VeqlqtdH5dtIQiIhIFlW2B.WTqJa0IVaZWaI0aK9w.zGcb2PVB753VQRLDw.6vhfEFuCU; _ga=GA1.1.**********.**********; _ga_PS3V7B7KV0=GS1.1.**********.1.0.**********.0.0.0',
                    '-H', 'origin: https://solscan.io',
                    '-H', 'pragma: no-cache',
                    '-H', 'priority: u=1, i',
                    '-H', 'referer: https://solscan.io/',
                    '-H', 'sec-ch-ua: "Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
                    '-H', 'sec-ch-ua-mobile: ?0',
                    '-H', 'sec-ch-ua-platform: "macOS"',
                    '-H', 'sec-fetch-dest: empty',
                    '-H', 'sec-fetch-mode: cors',
                    '-H', 'sec-fetch-site: same-site',
                    '-H', 'sol-aut: zC-L7C0=FJqSzn=B9dls0fKaoQ581DYK52ORa3vL',
                    '-H', 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36',
                    '-s'  # 静默模式，不显示进度信息
                ]
                
                # 使用子进程直接执行命令(同步版本，简化问题)
                result = subprocess.run(
                    curl_command,
                    capture_output=True,
                    text=True,
                    check=False  # 即使命令返回非零退出码也不抛出异常
                )
                
                if result.returncode != 0:
                    self.logger.error(f"Curl命令执行失败，错误码: {result.returncode}")
                    self.logger.error(f"错误信息: {result}")
                    raise Exception(f"Curl命令执行失败，错误码: {result.returncode}")
                
                response_data = result.stdout.strip()
                
                # 尝试解析JSON响应
                try:
                    data = json.loads(response_data)
                    
                    # 检查API响应状态
                    if not data.get('success'):
                        self.logger.error(f"API返回错误: {data.get('message', '未知错误')}")
                        raise Exception(f"API返回错误: {data.get('message', '未知错误')}")
                    
                    # 记录成功响应
                    self.logger.info(f"成功获取到Solscan数据，长度: {len(response_data)}")
                    self.logger.debug(f"Solscan返回数据: {json.dumps(data)[:1000]}...")
                    
                    # 异步保存到数据库
                    await _save_to_db(data["data"])
                    
                    # 从数据库获取并构建返回数据
                    token = await Token.get_or_create(mint_address)
                    token_data = {
                        "name": token.name,
                        "symbol": token.symbol,
                        "decimals": token.decimals,
                        "icon": token.icon,
                        "supply": token.supply,
                        "creator": token.creator,
                        "created_tx": token.created_tx,
                        "created_time": token.created_time,
                        "first_mint_tx": token.first_mint_tx,
                        "first_mint_time": token.first_mint_time,
                        "first_mint_amount": token.first_mint_amount,
                        "token_authority": token.token_authority,
                        "freeze_authority": token.freeze_authority,
                        "metadata_uri": token.metadata_uri,
                        "metadata_update_authority": token.metadata_update_authority,
                        "extensions": {
                            "coingecko_id": token.coingecko_id,
                            "coinmarketcap_id": token.coinmarketcap_id,
                            "website": token.website,
                            "twitter": token.twitter,
                            "telegram": token.telegram,
                            "discord": token.discord,
                            "medium": token.medium,
                            "description": token.description
                        }
                    }
                    
                    # 验证返回的数据是否有值
                    if token.name or token.symbol or token.decimals:
                        self.logger.info(f"获取到有效的代币信息：{token.name} ({token.symbol})")
                        return token_data
                    else:
                        self.logger.warning(f"代币信息为空，将重试...")
                        retry_count += 1
                        await asyncio.sleep(1 * retry_count)  # 递增的重试延迟
                        continue
                    
                except json.JSONDecodeError as e:
                    self.logger.error(f"解析JSON响应失败: {str(e)}")
                    self.logger.error(f"原始响应: {response_data[:500]}...")  # 只记录前500个字符
                    last_error = e
                    retry_count += 1
                    await asyncio.sleep(1 * retry_count)
                    continue
                    
            except Exception as e:
                self.logger.error(f"获取代币信息失败: {str(e)}")
                self.logger.error(f"错误类型: {type(e).__name__}")
                last_error = e
                retry_count += 1
                
                if retry_count < max_retries:
                    retry_delay = 1 * retry_count  # 递增的重试延迟
                    self.logger.info(f"将在 {retry_delay} 秒后重试 ({retry_count}/{max_retries})...")
                    await asyncio.sleep(retry_delay)
                    continue
                else:
                    self.logger.error(f"重试 {max_retries} 次后仍然失败")
                    import traceback
                    self.logger.error(f"错误堆栈: {traceback.format_exc()}")
                    return None
        
        self.logger.error(f"在 {max_retries} 次尝试后无法获取代币信息")
        return None

    async def _get_token_info_from_rpc(self, mint_address: str) -> Optional[Dict[str, Any]]:
        """从 RPC 获取代币信息"""
        async def _save_to_db(token_data: Dict[str, Any]) -> None:
            token = await Token.get_or_create(mint_address)
            token.name = token_data.get('name')
            token.symbol = token_data.get('symbol')
            token.decimals = token_data.get('decimals')
            token.metadata_uri = token_data.get('metadata_uri')
            await token.save()
            
        self._wait_for_rate_limit()
        response = self.client.get_token_supply(
            Pubkey.from_string(mint_address)
        )
        
        if response.value:
            token_data = {}
            # 如果是 Wrapped SOL
            if mint_address == self.PROGRAM_IDS["WRAPPED_SOL"]:
                token_data = {
                    "name": "Wrapped SOL",
                    "symbol": "SOL",
                    "decimals": response.value.decimals
                }
                await _save_to_db(token_data)
                return token_data
            
            # 尝试获取元数据
            metadata_address = self._get_metadata_pda(mint_address)
            if metadata_address:
                metadata = await self._get_metadata(metadata_address)
                if metadata:
                    token_data = {
                        "name": metadata.get('name'),
                        "symbol": metadata.get('symbol'),
                        "decimals": response.value.decimals,
                        "metadata_uri": metadata.get('uri')
                    }
                    await _save_to_db(token_data)
                    return metadata
            
            # 如果无法获取元数据，返回基本信息
            token_data = {
                "name": "",
                "symbol": "???",
                "decimals": response.value.decimals
            }
            await _save_to_db(token_data)
            return token_data
        
        return None

    async def get_token_info(self, mint_address: str) -> Optional[Dict[str, Any]]:
        """获取代币信息"""
        try:
            self.logger.info(f"正在获取代币 {mint_address} 的信息...")
            
            # 从数据库获取代币信息
            token_info = await self._get_token_info_from_database(mint_address)
            if token_info:
                self.logger.info(f"从数据库获取到代币信息")
                return token_info
            
            # 从 Solscan API 获取代币信息
            self.logger.info(f"从 Solscan API 获取代币信息...")
            token_info = await self._get_token_info_from_solscan(mint_address)
            if token_info:
                self.logger.info(f"从 Solscan API 获取到代币信息")
                return token_info
            
            # 如果 Solscan API 获取失败，尝试使用 RPC 获取基本信息
            self.logger.info(f"从 RPC 获取代币基本信息...")
            token_info = await self._get_token_info_from_rpc(mint_address)
            if token_info:
                self.logger.info(f"从 RPC 获取到代币基本信息")
                return token_info
            
            self.logger.warning(f"无法获取代币 {mint_address} 的信息")
            return None
            
        except Exception as e:
            self.logger.error(f"获取代币信息失败: {str(e)}")
            self.logger.error(f"错误类型: {type(e).__name__}")
            import traceback
            self.logger.error(f"错误堆栈: {traceback.format_exc()}")
            return None

    def _get_metadata_pda(self, mint_address: str) -> Optional[str]:
        """获取代币元数据账户地址"""
        try:
            seeds = [
                b"metadata",
                bytes(Pubkey.from_string(self.PROGRAM_IDS["METADATA_PROGRAM"])),
                bytes(Pubkey.from_string(mint_address))
            ]
            metadata_address, _ = Pubkey.find_program_address(
                seeds,
                Pubkey.from_string(self.PROGRAM_IDS["METADATA_PROGRAM"])
            )
            return str(metadata_address)
        except Exception as e:
            self.logger.error(f"获取元数据PDA失败: {str(e)}")
            return None

    async def _get_metadata(self, metadata_address: str) -> Optional[Dict[str, Any]]:
        """获取代币元数据"""
        try:
            self._wait_for_rate_limit()
            response = self.client.get_account_info(
                Pubkey.from_string(metadata_address),
                commitment="confirmed",
                encoding="base64"
            )
            
            if not response.value:
                return None
                
            # 解码账户数据
            data = base64.b64decode(response.value.data[0])
            
            # 解析元数据
            if len(data) < 1 + 32 + 32 + 200:  # 最小元数据长度
                return None
                
            try:
                # 跳过第一个字节（用于版本和类型）
                name_len = int.from_bytes(data[1:3], byteorder='little')
                name = data[3:3+name_len].decode('utf-8').rstrip('\x00')
                
                symbol_start = 3 + name_len
                symbol_len = int.from_bytes(data[symbol_start:symbol_start+2], byteorder='little')
                symbol = data[symbol_start+2:symbol_start+2+symbol_len].decode('utf-8').rstrip('\x00')
                
                uri_start = symbol_start + 2 + symbol_len
                uri_len = int.from_bytes(data[uri_start:uri_start+2], byteorder='little')
                uri = data[uri_start+2:uri_start+2+uri_len].decode('utf-8').rstrip('\x00')
                
                return {
                    "name": name,
                    "symbol": symbol,
                    "uri": uri
                }
                
            except Exception as e:
                self.logger.error(f"解析元数据失败: {str(e)}")
                return None
                
        except Exception as e:
            self.logger.error(f"获取元数据失败: {str(e)}")
            return None

    async def analyze_transaction(self, tx_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析交易数据"""
        result = {
            "type": "unknown",
            "token_transfers": [],
            "sol_transfers": []
        }
        
        # 获取代币余额变化
        pre_token_balances = tx_data.get("meta", {}).get("preTokenBalances", [])
        post_token_balances = tx_data.get("meta", {}).get("postTokenBalances", [])
        
        # 创建预余额映射
        pre_balances_map = {}
        for pre_balance in pre_token_balances:
            account_index = pre_balance.get("accountIndex")
            pre_amount = float(pre_balance.get("uiTokenAmount", {}).get("uiAmountString", "0"))
            pre_balances_map[account_index] = pre_amount
        
        # 分析代币余额变化
        for post_balance in post_token_balances:
            account_index = post_balance.get("accountIndex")
            mint = post_balance.get("mint")
            owner = post_balance.get("owner")
            
            if not mint:
                continue
                
            # 获取余额数据
            post_amount = float(post_balance.get("uiTokenAmount", {}).get("uiAmountString", "0"))
            pre_amount = pre_balances_map.get(account_index, 0.0)
            
            if pre_amount != post_amount:
                token_info = await self.get_token_info(mint)
                transfer = {
                    "token_mint": mint,
                    "token_name": token_info.get("name", "Unknown Token") if token_info else "Unknown Token",
                    "token_symbol": token_info.get("symbol", "???") if token_info else "???",
                    "pre_amount": pre_amount,
                    "post_amount": post_amount,
                    "change": post_amount - pre_amount,
                    "owner": owner
                }
                result["token_transfers"].append(transfer)
                result["type"] = "token_transfer"
        
        # 分析 SOL 转账
        pre_balances = tx_data.get("meta", {}).get("preBalances", [])
        post_balances = tx_data.get("meta", {}).get("postBalances", [])
        account_keys = tx_data.get("transaction", {}).get("message", {}).get("accountKeys", [])
        
        for i, (pre, post) in enumerate(zip(pre_balances, post_balances)):
            if pre != post and i < len(account_keys):
                transfer = {
                    "address": account_keys[i],
                    "pre_balance": pre / 1e9,  # Convert lamports to SOL
                    "post_balance": post / 1e9,
                    "change": (post - pre) / 1e9
                }
                result["sol_transfers"].append(transfer)
                if not result["token_transfers"]:
                    result["type"] = "sol_transfer"
        
        return result

    async def get_account_transactions(
        self,
        address: str,
        before: Optional[str] = None,
        limit: int = 10,
        until: Optional[str] = None,
        commitment: str = "confirmed"
    ) -> List[Dict[str, Any]]:
        """
        获取指定地址的交易历史
        
        Args:
            address: Solana 钱包地址
            before: 在此签名之前的交易（用于分页）
            limit: 返回的最大交易数量
            commitment: 交易确认级别
            
        Returns:
            交易列表
        """
        try:
            self._wait_for_rate_limit()
            pubkey = Pubkey.from_string(address)
            response = self.client.get_signatures_for_address(
                pubkey,
                before=Signature.from_string(before) if before else None,
                until=Signature.from_string(until) if until else None,
                limit=limit,
                commitment=commitment
            )
            
            # 直接返回响应，因为它已经是结果列表了
            return [tx.to_json() for tx in response.value]
            
        except Exception as e:
            import traceback
            self.logger.error(f"获取交易历史失败: {str(traceback.format_exc())}")
            return []

    async def get_transaction_details(self, signature: str) -> Optional[Dict[str, Any]]:
        """获取交易详情"""
        try:
            self._wait_for_rate_limit()
            response = self.client.get_transaction(
                Signature.from_string(signature),
                max_supported_transaction_version=0
            )
            
            if response.value:
                return json.loads(response.value.to_json())
            return None
            
        except Exception as e:
            self.logger.error(f"获取交易详情失败: {str(e)}")
            return None

    def _parse_spl_token_account_data(self, data_bytes: list) -> Optional[float]:
        """
        解析 SPL Token 账户数据（整数列表格式）
        
        Args:
            data_bytes: 代币账户的原始字节数据（整数列表）
            
        Returns:
            代币余额，如果解析失败则返回None
        """
        try:
            if len(data_bytes) < 72:
                self.logger.warning(f"代币账户数据长度不足: {len(data_bytes)} < 72")
                return None
            
            self.logger.debug(f"解析 SPL Token 账户数据 (长度: {len(data_bytes)})")
            
            # SPL Token 账户结构:
            # 0-32: mint (32 bytes)
            # 32-64: owner (32 bytes) 
            # 64-72: amount (8 bytes, little endian)
            # 72+: 其他字段
            
            # 提取 amount (位置 64-71，8字节 little endian)
            amount_bytes = data_bytes[64:72]
            self.logger.debug(f"原始金额字节: {amount_bytes}")
            
            # 将整数列表转换为字节，然后解析为 little endian 整数
            byte_data = bytes(amount_bytes)
            amount = int.from_bytes(byte_data, byteorder='little')
            
            self.logger.debug(f"原始金额: {amount}")
            
            if amount == 0:
                self.logger.debug("余额为 0")
                return 0.0
            
            # 尝试不同的小数位数来找到合理的余额
            # 常见的代币小数位数: 6, 9, 8, 18
            possible_balances = []
            
            for decimals in [6, 9, 8, 18]:
                balance = amount / (10 ** decimals)
                possible_balances.append((decimals, balance))
                self.logger.debug(f"如果小数位数为 {decimals}: {balance}")
            
            # 选择最合理的余额（通常在 0.000001 到 1,000,000,000 之间）
            for decimals, balance in possible_balances:
                if 0.000001 <= balance <= 1000000000:
                    self.logger.debug(f"选择余额 (decimals={decimals}): {balance}")
                    return balance
            
            # 如果没有找到合理的余额，使用最常见的 6 位小数
            decimals = 6
            balance = amount / (10 ** decimals)
            self.logger.debug(f"使用默认小数位数 {decimals}: {balance}")
            return balance
            
        except Exception as e:
            self.logger.error(f"解析SPL Token账户数据失败: {e}")
            return None

    async def get_token_balance(self, owner_address: str, token_mint: str) -> float:
        """
        获取指定地址的代币余额
        
        Args:
            owner_address: 钱包地址
            token_mint: 代币铸币地址
            
        Returns:
            代币余额
        """
        try:
            self.logger.info(f"正在获取地址 {owner_address} 的代币 {token_mint} 余额...")
            self._wait_for_rate_limit()
            
            # 获取代币账户地址
            opts = TokenAccountOpts(mint=Pubkey.from_string(token_mint))
            token_account = self.client.get_token_accounts_by_owner(
                Pubkey.from_string(owner_address),
                opts
            )
            
            if token_account.value:
                self.logger.debug(f"找到 {len(token_account.value)} 个代币账户")
                # 获取第一个代币账户的余额
                account = token_account.value[0]
                
                # 解析账户数据
                account_data = json.loads(account.account.to_json())
                
                if "data" not in account_data:
                    self.logger.warning("账户数据中缺少 data 字段")
                    return 0.0
                
                data = account_data["data"]
                
                # 处理整数列表格式的数据（修复Bug的核心逻辑）
                if isinstance(data, list):
                    self.logger.debug("检测到整数列表格式的 data 字段")
                    balance = self._parse_spl_token_account_data(data)
                    if balance is not None:
                        self.logger.info(f"代币余额: {balance}")
                        return balance
                    else:
                        self.logger.warning("无法解析整数列表格式的数据")
                        return 0.0
                
                # 处理字典格式的数据（保持向后兼容）
                elif isinstance(data, dict) and "parsed" in data:
                    self.logger.debug("检测到字典格式的 data 字段")
                    parsed_info = data["parsed"]["info"]
                    token_amount = parsed_info["tokenAmount"]
                    
                    # 尝试不同的余额字段
                    if "uiAmountString" in token_amount:
                        balance = float(token_amount["uiAmountString"])
                        self.logger.info(f"代币余额: {balance}")
                        return balance
                    elif "uiAmount" in token_amount and token_amount["uiAmount"] is not None:
                        balance = float(token_amount["uiAmount"])
                        self.logger.info(f"代币余额: {balance}")
                        return balance
                    elif "amount" in token_amount and "decimals" in token_amount:
                        amount = int(token_amount["amount"])
                        decimals = token_amount["decimals"]
                        balance = amount / (10 ** decimals)
                        self.logger.info(f"代币余额: {balance}")
                        return balance
                    else:
                        self.logger.warning("无法从 tokenAmount 中提取余额")
                        return 0.0
                
                else:
                    self.logger.warning(f"未知的 data 格式: {type(data)}")
                    return 0.0
            
            self.logger.info("未找到代币账户或余额为0")
            return 0.0
        except Exception as e:
            self.logger.error(f"获取代币余额失败: {str(e)}")
            self.logger.error(f"错误类型: {type(e).__name__}")
            self.logger.error(f"错误详情: {str(e)}")
            return 0.0

    async def get_confirmed_token_output_from_tx(self, tx_hash: str, expected_output_token_mint: str, wallet_address: str = None) -> Optional[float]:
        """
        从链上交易中获取确认的代币输出数量
        
        Args:
            tx_hash: 交易哈希
            expected_output_token_mint: 预期输出代币的铸币地址
            wallet_address: 钱包地址（用于解析交易上下文）
            
        Returns:
            确认的代币输出数量（UI单位），如果获取失败则返回None
        """
        try:
            self.logger.info(f"正在从交易 {tx_hash} 中获取代币 {expected_output_token_mint} 的确认输出数量...")
            
            # 获取交易详情
            tx_details = await self.get_transaction_details(tx_hash)
            if not tx_details:
                self.logger.error(f"无法获取交易详情: {tx_hash}")
                return None
            
            # 检查交易是否成功
            meta = tx_details.get("meta", {})
            if meta.get("err"):
                self.logger.error(f"交易 {tx_hash} 在链上失败: {meta.get('err')}")
                return None
            
            # 获取代币余额变化
            pre_token_balances = meta.get("preTokenBalances", [])
            post_token_balances = meta.get("postTokenBalances", [])
            
            # 如果是SOL，处理SOL余额变化
            if expected_output_token_mint in ["So11111111111111111111111111111111111111112", "sol", "SOL"]:
                if wallet_address:
                    pre_balances = meta.get("preBalances", [])
                    post_balances = meta.get("postBalances", [])
                    account_keys = tx_details.get("transaction", {}).get("message", {}).get("accountKeys", [])
                    
                    # 查找钱包地址在账户列表中的索引
                    wallet_index = None
                    for i, account_key in enumerate(account_keys):
                        if account_key == wallet_address:
                            wallet_index = i
                            break
                    
                    if wallet_index is not None and wallet_index < len(pre_balances) and wallet_index < len(post_balances):
                        pre_sol_balance = pre_balances[wallet_index]
                        post_sol_balance = post_balances[wallet_index]
                        sol_change = (post_sol_balance - pre_sol_balance) / 1e9  # 转换为SOL单位
                        
                        if sol_change > 0:  # 只有当SOL增加时才认为是输出
                            self.logger.info(f"交易 {tx_hash} 中SOL输出数量: {sol_change}")
                            return sol_change
                
                self.logger.warning(f"无法从交易 {tx_hash} 中确定SOL输出数量")
                return None
            
            # 处理SPL代币余额变化
            # 创建预余额映射 (accountIndex -> balance)
            pre_balances_map = {}
            for pre_balance in pre_token_balances:
                account_index = pre_balance.get("accountIndex")
                mint = pre_balance.get("mint")
                owner = pre_balance.get("owner")
                if mint == expected_output_token_mint:
                    ui_amount = float(pre_balance.get("uiTokenAmount", {}).get("uiAmountString", "0"))
                    pre_balances_map[account_index] = {
                        "amount": ui_amount,
                        "owner": owner
                    }
            
            # 查找代币输出数量
            for post_balance in post_token_balances:
                account_index = post_balance.get("accountIndex")
                mint = post_balance.get("mint")
                owner = post_balance.get("owner")
                
                if mint == expected_output_token_mint:
                    post_amount = float(post_balance.get("uiTokenAmount", {}).get("uiAmountString", "0"))
                    pre_info = pre_balances_map.get(account_index, {"amount": 0.0, "owner": owner})
                    pre_amount = pre_info["amount"]
                    
                    # 计算余额变化
                    change = post_amount - pre_amount
                    
                    # 如果指定了钱包地址，只处理该钱包的余额变化
                    if wallet_address and owner != wallet_address:
                        continue
                    
                    # 只有当代币增加时才认为是输出
                    if change > 0:
                        self.logger.info(f"交易 {tx_hash} 中代币 {expected_output_token_mint} 输出数量: {change} (所有者: {owner})")
                        return change
            
            self.logger.warning(f"无法从交易 {tx_hash} 中找到代币 {expected_output_token_mint} 的输出数量")
            return None
            
        except Exception as e:
            self.logger.error(f"从交易 {tx_hash} 获取代币输出数量失败: {str(e)}")
            self.logger.error(f"错误类型: {type(e).__name__}")
            import traceback
            self.logger.error(f"错误堆栈: {traceback.format_exc()}")
            return None
            
    async def sync_token_holdings(self, address: str, token_holdings: Dict[str, float]):
        """
        同步所有代币的持仓数据
        
        Args:
            address: 钱包地址
            token_holdings: 代币持仓字典
        """
        try:
            self.logger.info(f"开始同步地址 {address} 的代币持仓数据...")
            self._wait_for_rate_limit()
            
            # 获取所有代币账户
            self.logger.info("正在获取代币账户列表...")
            opts = TokenAccountOpts(program_id=Pubkey.from_string(self.PROGRAM_IDS["TOKEN_PROGRAM"]))
            token_accounts = self.client.get_token_accounts_by_owner(
                Pubkey.from_string(address),
                opts
            )
            
            self.logger.info(f"获取到 {len(token_accounts.value) if token_accounts.value else 0} 个代币账户")
            self.logger.debug(f"代币账户数据: {token_accounts.value}")
            
            # 更新持仓信息
            token_holdings.clear()  # 清空现有数据
            
            # 添加 SOL 余额
            try:
                self.logger.info("正在获取 SOL 余额...")
                sol_balance = self.client.get_balance(Pubkey.from_string(address))
                if sol_balance.value is not None:
                    sol_amount = float(sol_balance.value) / 1e9
                    token_holdings[self.PROGRAM_IDS["WRAPPED_SOL"]] = sol_amount
                    self.logger.info(f"SOL 余额: {sol_amount}")
            except Exception as e:
                self.logger.error(f"获取 SOL 余额失败: {str(e)}")
                self.logger.error(f"错误类型: {type(e).__name__}")
                self.logger.error(f"错误详情: {str(e)}")
            
            # 添加其他代币余额
            if token_accounts.value:
                for i, account in enumerate(token_accounts.value):
                    try:
                        self.logger.debug(f"正在处理第 {i+1} 个代币账户...")
                        
                        # 获取账户数据
                        account_data = json.loads(account.account.to_json())
                        if "data" in account_data and "parsed" in account_data["data"]:
                            parsed_info = account_data["data"]["parsed"]["info"]
                            mint = parsed_info["mint"]
                            token_amount = parsed_info["tokenAmount"]
                            
                            if "uiAmountString" in token_amount:
                                balance = float(token_amount["uiAmountString"])
                                if balance > 0:  # 只记录有余额的代币
                                    token_holdings[mint] = balance
                                    self.logger.info(f"代币 {mint} 余额: {balance}")
                    except Exception as e:
                        self.logger.error(f"处理第 {i+1} 个代币账户时失败")
                        self.logger.error(f"错误类型: {type(e).__name__}")
                        self.logger.error(f"错误详情: {str(e)}")
                        continue
                
            self.logger.info(f"同步持仓数据完成，共 {len(token_holdings)} 个代币账户")
            self.logger.debug(f"当前持仓数据: {token_holdings}")
            
        except Exception as e:
            import traceback
            traceback.print_exc()
            self.logger.error(f"同步持仓数据失败: {str(e)}")
            self.logger.error(f"错误类型: {type(e).__name__}")
            self.logger.error(f"错误详情: {str(e)}")
            
        # 等待一小段时间，避免请求过于频繁
        await asyncio.sleep(1)

    async def monitor_address(
        self,
        address: str,
        callback=None,
        interval: int = 60,
        limit: int = 10,
        sync_interval: int = 3600  # 每小时同步一次持仓数据
    ):
        """
        持续监控地址的新交易
        
        Args:
            address: 要监控的地址
            callback: 发现新交易时的回调函数
            interval: 检查间隔（秒）
            limit: 每次检查的交易数量
            sync_interval: 同步持仓数据的间隔（秒）
        """
        last_signature = None
        processed_signatures = set()  # 用于存储已处理的交易签名
        token_holdings = {}  # 用于跟踪代币持仓
        last_sync_time = 0  # 上次同步时间
        
        # 初始化持仓数据
        self.logger.info(f"开始初始化持仓数据...")
        await self.sync_token_holdings(address, token_holdings)
        last_sync_time = time.time()
        
        self.logger.info(f"开始监控地址: {address}")
        
        while True:
            try:
                # 定期同步持仓数据
                current_time = time.time()
                if current_time - last_sync_time >= sync_interval:
                    self.logger.info("开始同步持仓数据...")
                    await self.sync_token_holdings(address, token_holdings)
                    last_sync_time = current_time
                
                # 获取最新的交易
                transactions = await self.get_account_transactions(
                    address,
                    before=last_signature,
                    limit=limit
                )
                
                if transactions:
                    # 更新最新的签名
                    newest_tx = json.loads(transactions[0])
                    last_signature = newest_tx["signature"]
                    
                    # 处理每个新交易
                    for tx_str in transactions:
                        tx = json.loads(tx_str)
                        signature = tx["signature"]
                        
                        # 跳过已处理的交易
                        if signature in processed_signatures:
                            continue
                            
                        processed_signatures.add(signature)
                        
                        # 获取交易详情
                        tx_details = await self.get_transaction_details(signature)
                        if not tx_details:
                            continue
                            
                        # 分析交易
                        analysis = await self.analyze_transaction(tx_details)
                        
                        # 构建交易信息
                        tx_info = {
                            "timestamp": datetime.fromtimestamp(tx["blockTime"]).strftime("%Y-%m-%d %H:%M:%S"),
                            "signature": signature,
                            "type": analysis["type"],
                            "token_transfers": [],
                            "sol_transfers": []
                        }
                        
                        # 处理代币转账（按照交易中的顺序处理）
                        for transfer in analysis["token_transfers"]:
                            mint = transfer["token_mint"]
                            owner = transfer["owner"]
                            
                            # 只处理监控地址的持仓变化
                            if owner == address:
                                # 使用交易中的实际数据
                                pre_balance = transfer["pre_amount"]
                                post_balance = transfer["post_amount"]
                                change = transfer["change"]
                                
                                # 更新持仓数据
                                token_holdings[mint] = post_balance
                                
                                # 计算交易规模指标
                                position_impact = abs(change / pre_balance * 100) if pre_balance != 0 else float('inf')
                                
                                # 确定交易类型
                                trade_type = self._determine_trade_type(change, position_impact)
                                
                                token_info = {
                                    "token_name": transfer["token_name"],
                                    "token_symbol": transfer["token_symbol"],
                                    "amount_change": change,
                                    "mint": mint,
                                    "owner": owner,
                                    "pre_balance": pre_balance,
                                    "post_balance": post_balance,
                                    "position_impact": position_impact,
                                    "trade_type": trade_type
                                }
                                tx_info["token_transfers"].append(token_info)
                        
                        # 处理 SOL 转账
                        for transfer in analysis["sol_transfers"]:
                            sol_info = {
                                "address": transfer["address"],
                                "amount_change": transfer["change"]
                            }
                            tx_info["sol_transfers"].append(sol_info)
                        
                        # 打印交易信息
                        self._print_transaction_info(tx_info)
                        
                        # 如果有回调函数，调用它
                        if callback and callable(callback):
                            callback(tx_info)
                
                # 限制已处理签名的集合大小
                if len(processed_signatures) > 1000:
                    processed_signatures = set(list(processed_signatures)[-1000:])
                    
                await asyncio.sleep(interval)
                
            except Exception as e:
                self.logger.error(f"监控过程发生错误: {str(e)}")
                await asyncio.sleep(interval)
                
    def _print_transaction_info(self, tx_info: Dict[str, Any]):
        """打印交易信息的辅助方法"""
        self.logger.info(f"发现新交易:")
        self.logger.info(f"时间: {tx_info['timestamp']}")
        self.logger.info(f"签名: {tx_info['signature']}")
        self.logger.info(f"类型: {tx_info['type']}")
        
        if tx_info["token_transfers"]:
            self.logger.info("代币转账:")
            for token_tx in tx_info["token_transfers"]:
                self.logger.info(f"  - {token_tx['token_name']} ({token_tx['token_symbol']})")
                self.logger.info(f"    交易类型: {token_tx['trade_type']}")
                self.logger.info(f"    数量变化: {token_tx['amount_change']}")
                self.logger.info(f"    交易前持仓: {token_tx['pre_balance']}")
                self.logger.info(f"    交易后持仓: {token_tx['post_balance']}")
                self.logger.info(f"    持仓影响: {token_tx['position_impact']:.2f}%")
                self.logger.info(f"    代币地址: {token_tx['mint']}")
                self.logger.info(f"    所有者: {token_tx['owner']}")
        
        if tx_info["sol_transfers"]:
            self.logger.info("SOL 转账:")
            for sol_tx in tx_info["sol_transfers"]:
                self.logger.info(f"  - 地址: {sol_tx['address']}")
                self.logger.info(f"    数量变化: {sol_tx['amount_change']} SOL")

    def _determine_trade_type(self, change: float, position_impact: float) -> str:
        """
        根据交易变化和持仓影响确定交易类型
        
        Args:
            change: 交易数量变化
            position_impact: 持仓影响百分比
            
        Returns:
            交易类型描述
        """
        if change > 0:
            if position_impact == float('inf'):
                return "首次建仓"
            elif position_impact >= 50:
                return "大幅加仓"
            elif position_impact >= 20:
                return "中幅加仓"
            else:
                return "小幅加仓"
        else:
            if position_impact >= 90:
                return "清仓卖出"
            elif position_impact >= 50:
                return "大幅减仓"
            elif position_impact >= 20:
                return "中幅减仓"
            else:
                return "小幅减仓"
            
    async def update_token_first_mint_info(self, token: Token, token_dao: TokenDAO):
        result = await TokenInfo(token["address"]).get_token_info()
        if not result:
            return
        return await token_dao.update_token(token["address"], result)
        

if __name__ == "__main__":
    # 设置日志级别
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    from models import init_db
    
    async def main():
        try:
            # 初始化数据库
            await init_db()
            # 创建监控器实例
            solana_monitor = SolanaMonitor()
            tx_hash = "3wRoGXxKj8Ka4owNAbQeyZ8t5BER896HKAHCV1UX7Sq5z2qzmg8LG43MSKGQKmNMDRtx51r5kwzHXp21DidvW54j"
            expected_output_token_mint = "So11111111111111111111111111111111111111112"
            wallet_address = "AHnQjWa4jxYNxzfJantHDpcUKAE9pzVSGGPLhthPxCSW"
            result = await solana_monitor.get_confirmed_token_output_from_tx(tx_hash, expected_output_token_mint, wallet_address)
            print(result)
            
        except Exception as e:
            print(f"发生错误: {str(e)}\n{traceback.format_exc()}")
            raise
    
    # 运行主程序
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序执行出错: {str(e)}")
    