# KOL Activity Monitor - 新功能开发 Todo List

**功能名称**: KOL 活动时间戳差异监控器 (KOL Activity Timestamp Discrepancy Monitor)
**版本**: 0.1.0
**模块**: monitors

---

- [x] 5.0. 任务类型识别、流程选择与Todo List
    - [x] 1. 任务类型识别: 新功能开发
    - [x] 2. 流程选择: 5.A 新功能开发流程
    - [x] 3. Todo List 生成: 本文档 (kol_activity_monitor_todo_list.md)
- [x] 5.A.1. 指令理解与模块定位
    - [x] 1. 深入理解用户核心需求 (多次澄清和迭代，最终确定为比较活动记录内部的`created_at`和`timestamp`字段，并加入连续告警、抑制逻辑、告警状态持久化、告警事件记录和通知发送记录)
    - [x] 2. 需求映射到功能模块: 新的工作流 `workflows/kol_activity_timestamp_discrepancy_monitor/`
    - [x] 3. 确认/获取 `docs/features/` 下的最新版本: `0.1.0`
- [x] 5.A.2. 文档查阅与影响分析
    - [-] 1. 查阅 `docs/features/0.1.0/[module_name]/` 相关文档 (新功能，尚无特定文档)
    - [x] 2. 阅读项目整体架构文档 `docs/project/PROJECT_OVERVIEW.md`
    - [x] 3. 初步判断代码修改范围和潜在影响:
        - 新建 `workflows/kol_activity_timestamp_discrepancy_monitor/handler.py`
        - 新建 `workflows/kol_activity_timestamp_discrepancy_monitor/kol_activity_timestamp_discrepancy_monitor_workflow.yaml`
        - 新建 `models/monitor_alert_state.py` (用于持久化告警状态)
        - 新建 `models/alert_event_record.py` (用于记录告警事件)
        - 新建 `models/notification_log_record.py` (用于记录通知发送尝试)
        - 修改 `models/__init__.py` (注册新模型)
        - 需要读取 `models/kol_wallet.py`, `models/kol_wallet_activity.py`, `models/config.py`
        - 需要 `utils/telegram_message_sender.py` (假设存在或创建)
- [x] 5.A.3. 详细阅读源代码
    - [x] 1. 阅读 `models/kol_wallet.py`
    - [x] 2. 阅读 `models/kol_wallet_activity.py` (关注 `created_at` 和 `timestamp` 字段)
    - [x] 3. 阅读 `models/config.py`
    - [x] 4. 阅读 `utils/telegram_message_sender.py` (或相关通知工具)
- [x] 5.A.4. 生成前置文档
    - [x] 1. 生成详细需求规格: `docs/features/0.1.0/monitors/kol_activity_latency_monitor_requirements_ai.md` (已更新为比较 `created_at` 和 `timestamp`, 获取活动按 `created_at` 排序)
    - [x] 2. 生成技术实现方案: `docs/features/0.1.0/monitors/kol_activity_latency_monitor_dev_plan_ai.md` (已更新为比较 `created_at` 和 `timestamp`, 获取活动按 `created_at` 排序)
    - [x] 3. 生成测试用例设计: `docs/features/0.1.0/monitors/kol_activity_latency_monitor_test_cases_ai.md` (已更新为比较 `created_at` 和 `timestamp`, 获取活动按 `created_at` 排序)
- [x] 5.A.5. 请求人工审阅
    - [x] 1. 用户审阅需求规格文档 (已根据反馈更新)
    - [x] 2. 用户审阅技术实现方案文档 (已根据反馈更新)
    - [x] 3. 用户审阅测试用例设计文档 (已根据反馈更新)
- [x] 5.A.6. 代码实现与测试用例编写
    - [x] 1. 创建 `models/monitor_alert_state.py` 并实现 `MonitorAlertState` 模型
    - [x] 2. 创建 `models/alert_event_record.py` 并实现 `AlertEventRecord` 模型
    - [x] 3. 创建 `models/notification_log_record.py` 并实现 `NotificationLogRecord` 模型
    - [x] 4. 修改 `models/__init__.py` 注册所有新模型
    - [x] 5. 创建 `workflows/kol_activity_timestamp_discrepancy_monitor/handler.py` 并实现 `KolActivityTimestampDiscrepancyChecker` 类
        - [x] 5.1 实现 `__init__`
        - [x] 5.2 实现 `_load_config`
        - [x] 5.3 实现 `_load_alert_state`
        - [x] 5.4 实现 `_save_alert_state`
        - [x] 5.5 实现 `_get_latest_kol_wallet`
        - [x] 5.6 实现 `_get_latest_activity` (按 `created_at` 排序)
        - [x] 5.7 实现 `_record_alert_event`
        - [x] 5.8 实现 `_record_notification_log`
        - [x] 5.9 实现 `_should_send_notification` (比较 `created_at` 和 `timestamp`)
        - [x] 5.10 实现 `check_and_notify_for_category` (使用 `created_at`)
        - [x] 5.11 实现 `run_check_cycle` 及工作流入口函数 `perform_kol_activity_timestamp_discrepancy_check_task`
    - [x] 6. 创建 `test/workflows/kol_activity_timestamp_discrepancy_monitor/test_handler.py` 并实现测试用例 (unittest)
        - [x] 6.1 实现 `asyncSetUp` 和 `asyncTearDown` (包括Beanie初始化和数据清理)
        - [x] 6.2 测试配置加载 (`test_load_config`)
        - [x] 6.3 测试告警状态加载 (`test_load_alert_state`)
        - [x] 6.4 测试告警状态保存 (`test_save_alert_state`)
        - [x] 6.5 测试KOL钱包选择逻辑 (`test_get_latest_kol_wallet`)
        - [x] 6.6 测试活动获取逻辑 (`test_get_latest_activity` - 验证按 `created_at` 排序)
        - [x] 6.7 测试告警事件记录 (`test_record_alert_event`)
        - [x] 6.8 测试通知日志记录 (`test_record_notification_log`)
        - [x] 6.9 测试告警判断逻辑 (`test_should_send_notification` - 验证基于 `created_at` 的比较)
        - [x] 6.10 测试核心检查与通知方法 (`test_check_and_notify_for_category_sends_notification`, `test_check_and_notify_no_admin_ids` - 验证基于 `created_at`)
        - [x] 6.11 测试完整检查周期 (`test_run_check_cycle`)
        - [x] 6.12 测试工作流入口函数 (`test_perform_kol_activity_timestamp_discrepancy_check_task`)
    - [x] 7. 创建工作流配置文件 `workflows/kol_activity_timestamp_discrepancy_monitor/kol_activity_timestamp_discrepancy_monitor_workflow.yaml`
- [x] 5.A.7. 自动化测试执行与结果反馈
    - [x] 1. 执行所有 `unittest` 测试用例
    - [x] 2. 所有测试通过
- [>] 5.A.8. 自我核查与最终确认 