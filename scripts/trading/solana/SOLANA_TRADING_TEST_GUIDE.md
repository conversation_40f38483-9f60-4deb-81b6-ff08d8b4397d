# Solana直接交易服务测试指南

## 🎯 **测试目的**

验证 Solana 直接交易服务是否能够：
1. 正确连接到 Jupiter API 和 Solana RPC
2. 获取交易报价
3. 构建和签名交易
4. 提交交易到区块链
5. 等待交易确认

## 📋 **准备工作**

### 1. 钱包准备
你需要：
- 一个 Solana 钱包地址
- 该钱包的私钥（Base58 格式）
- 钱包中至少有 0.002 SOL（0.001 SOL 用于交易 + 费用）

### 2. 获取钱包私钥
在 Phantom、Solflare 或其他钱包中导出私钥：
- 通常显示为 Base58 格式的长字符串
- 例如：`5Kq...xyz`（实际会更长）

## 🚀 **执行测试**

### 1. 设置环境变量

```bash
# 设置你的钱包私钥（Base58格式）
export WALLET_PRIVATE_KEY='你的私钥字符串'

# 设置你的钱包地址
export WALLET_ADDRESS='你的钱包地址'
```

### 2. 运行测试

```bash
python test_solana_direct_trade_main.py
```

### 3. 测试确认

脚本会要求你确认：
```
⚠️  这将执行真实的交易，是否继续？(y/N):
```

输入 `y` 继续，或任何其他键取消。

## 📊 **测试参数**

- **交易类型**: 买入 (SOL → USDC)
- **交易金额**: 0.001 SOL (~$0.2 USD)
- **滑点**: 1%
- **优先费**: 50,000 lamports (0.00005 SOL)
- **目标代币**: USDC

## ✅ **预期结果**

### 成功情况：
```
✅ 交易成功!
   交易哈希: 5KJ...xyz
   执行时间: 2025-05-25 16:20:59+00:00
   实际输入数量: 1000000.0
   实际输出数量: 998500.0
   🔗 Solscan链接: https://solscan.io/tx/5KJ...xyz
   🔗 Explorer链接: https://explorer.solana.com/tx/5KJ...xyz
```

### 失败情况：
```
❌ 交易失败:
   错误信息: [详细错误描述]
```

## 🔍 **验证交易**

使用提供的链接在区块链浏览器中查看：
- **Solscan**: https://solscan.io/tx/[交易哈希]
- **Explorer**: https://explorer.solana.com/tx/[交易哈希]

检查：
- 交易状态是否为 "Success"
- SOL 减少了正确的数量
- USDC 增加了预期的数量
- 手续费合理

## 🛠️ **故障排除**

### 常见错误：

1. **钱包余额不足**
   ```
   错误: Insufficient funds
   解决: 向钱包转入更多 SOL
   ```

2. **网络拥堵**
   ```
   错误: Transaction timeout
   解决: 增加优先费或稍后重试
   ```

3. **滑点过大**
   ```
   错误: Slippage exceeded
   解决: 增加滑点容忍度或减少交易金额
   ```

4. **私钥格式错误**
   ```
   错误: Invalid wallet private key
   解决: 确保私钥是正确的 Base58 格式
   ```

## 🔒 **安全提醒**

1. **私钥安全**: 测试完成后立即清除环境变量
   ```bash
   unset WALLET_PRIVATE_KEY
   unset WALLET_ADDRESS
   ```

2. **测试环境**: 建议使用专门的测试钱包
3. **小额测试**: 仅使用小额资金进行测试
4. **网络选择**: 确保在主网上测试（脚本默认主网）

## 📈 **性能指标**

正常情况下：
- **报价获取**: < 2 秒
- **交易构建**: < 1 秒
- **交易提交**: < 3 秒
- **交易确认**: 10-30 秒
- **总时间**: < 60 秒

## 🎉 **测试成功标志**

如果看到以下内容，说明服务完全正常：
1. ✅ 交易成功状态
2. 🔗 有效的交易哈希和区块链链接
3. 📊 正确的输入/输出数量
4. ⏰ 合理的执行时间

恭喜！你的 Solana 直接交易服务已经可以正常工作了！ 