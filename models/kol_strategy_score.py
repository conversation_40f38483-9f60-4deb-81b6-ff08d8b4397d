from beanie import Document, Link
from pydantic import Field
from typing import Optional
from datetime import datetime
from models.kol_wallet import KOLWallet
import pymongo

class KOLStrategyScore(Document):
    """KOL在特定策略下的累计分数记录"""
    
    kol_wallet: Link[KOLWallet] = Field(..., description="关联的KOL钱包")
    strategy_name: str = Field(..., description="策略名称")
    
    total_positive_score: float = Field(default=0.0, description="该策略下的总加分")
    total_negative_score: float = Field(default=0.0, description="该策略下的总扣分 (通常为负数或0)")
    
    last_scored_at: Optional[datetime] = Field(default=None, description="最后计分时间")
    
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    @property
    def net_score(self) -> float:
        """计算净分数（加分 + 扣分）"""
        return self.total_positive_score + self.total_negative_score

    class Settings:
        name = "kol_strategy_scores"
        indexes = [
            # 确保KOL和策略组合的唯一性
            [("kol_wallet._id", pymongo.ASCENDING), ("strategy_name", pymongo.ASCENDING)],
            [("strategy_name", pymongo.ASCENDING)],
            [("last_scored_at", pymongo.ASCENDING)],
            [("total_positive_score", pymongo.ASCENDING)],
            [("total_negative_score", pymongo.ASCENDING)],
        ]
        
    def __str__(self):
        return f"KOLStrategyScore(kol={self.kol_wallet}, strategy={self.strategy_name}, net_score={self.net_score})" 