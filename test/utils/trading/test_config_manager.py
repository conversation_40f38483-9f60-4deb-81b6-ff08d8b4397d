"""
ConfigManager单元测试

测试ConfigManager的核心功能：
1. 配置加载和缓存
2. 动态配置更新
3. 默认配置处理
4. 数据库集成
5. 错误处理
"""

import unittest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from models.config import Config, AutoTradeManagerConfig, AutoTradeConfig, WalletConfig, TradingParams, TradeChannelConfig, NotificationConfig
from utils.trading.config_manager import ConfigManager
from dao.config_dao import ConfigDAO


class TestConfigManager(unittest.IsolatedAsyncioTestCase):
    """ConfigManager单元测试类"""
    
    async def asyncSetUp(self) -> None:
        """异步测试前置设置"""
        self.mock_config_dao = Mock(spec=ConfigDAO)
        self.config_manager = ConfigManager()
        self.config_manager._config_dao = self.mock_config_dao
        
        # 创建测试配置
        self.test_auto_trade_config = self._create_test_auto_trade_config()
        self.test_manager_config = self._create_test_manager_config()
        # 使用简单的字典模拟Config文档，避免Beanie初始化依赖
        self.test_config_doc = type('MockConfig', (), {
            'type': "auto_trade_manager",
            'data': self.test_manager_config,
            'updated_at': datetime.now(),
            'version': 1
        })()
    
    def _create_test_auto_trade_config(self) -> AutoTradeConfig:
        """创建测试用AutoTradeConfig"""
        wallet_config = WalletConfig(
            default_private_key_env_var="TEST_PRIVATE_KEY",
            default_wallet_address="test_wallet_address"
        )
        
        trading_params = TradingParams(
            default_buy_amount_sol=0.01,
            default_buy_slippage_percentage=10.0,
            default_buy_priority_fee_sol=0.0005,
            default_sell_slippage_percentage=10.0,
            default_sell_priority_fee_sol=0.0005
        )
        
        gmgn_channel = TradeChannelConfig(
            channel_type="gmgn",
            enabled=True,
            priority=1,
            timeout_seconds=60,
            max_retries=3,
            trading_params=trading_params,
            channel_params={"gmgn_api_host": "https://gmgn.ai"}
        )
        
        solana_channel = TradeChannelConfig(
            channel_type="jupiter",
            enabled=True,
            priority=2,
            timeout_seconds=90,
            max_retries=2,
            trading_params=trading_params,
            channel_params={
                "jupiter_api_host": "https://quote-api.jup.ag",
                "solana_rpc_url": "https://api.mainnet-beta.solana.com"
            }
        )
        
        notification_config = NotificationConfig(
            notify_on_failure=True,
            notify_on_fallback=True,
            admin_chat_ids=[],
            include_trade_details=True
        )
        
        return AutoTradeConfig(
            enabled=True,
            wallet_config=wallet_config,
            channels=[gmgn_channel, solana_channel],
            default_timeout=60,
            max_total_retries=3,
            notification_config=notification_config
        )
    
    def _create_test_manager_config(self) -> AutoTradeManagerConfig:
        """创建测试用AutoTradeManagerConfig"""
        wallet_config = WalletConfig(
            default_private_key_env_var="TEST_PRIVATE_KEY",
            default_wallet_address="test_wallet_address"
        )
        
        trading_params = TradingParams(
            default_buy_amount_sol=0.01,
            default_buy_slippage_percentage=10.0,
            default_buy_priority_fee_sol=0.0005,
            default_sell_slippage_percentage=10.0,
            default_sell_priority_fee_sol=0.0005
        )
        
        notification_config = NotificationConfig(
            include_trade_details=True,
            notify_on_success=True,
            notify_on_failure=True
        )
        
        return AutoTradeManagerConfig(
            enabled=True,
            wallet_config=wallet_config,
            trading_params=trading_params,
            auto_trade=self.test_auto_trade_config,
            notification_config=notification_config
        )
    
    async def test_get_config_success(self) -> None:
        """测试成功获取配置"""
        # 设置Mock返回值
        self.mock_config_dao.get_config = AsyncMock(return_value=self.test_config_doc)
        
        # 获取配置
        config = await self.config_manager.get_config()
        
        # 验证配置获取
        self.assertIsNotNone(config)
        self.assertIsInstance(config, AutoTradeConfig)
        self.assertEqual(config.enabled, True)
        self.assertEqual(len(config.channels), 2)
        
        # 验证DAO被调用
        self.mock_config_dao.get_config.assert_called_once_with("auto_trade_manager")
    
    async def test_get_config_not_found(self) -> None:
        """测试配置不存在时获取默认配置"""
        # 设置Mock返回None
        self.mock_config_dao.get_config = AsyncMock(return_value=None)
        
        # 获取配置
        config = await self.config_manager.get_config()
        
        # 验证获取了默认配置
        self.assertIsNotNone(config)
        self.assertIsInstance(config, AutoTradeConfig)
        self.assertFalse(config.enabled)  # 默认禁用
        self.assertEqual(len(config.channels), 0)  # 默认无渠道
    
    async def test_get_config_dao_exception(self) -> None:
        """测试DAO异常时的处理"""
        # 设置Mock抛出异常
        self.mock_config_dao.get_config = AsyncMock(side_effect=Exception("Database error"))
        
        # 获取配置
        config = await self.config_manager.get_config()
        
        # 验证获取了默认配置
        self.assertIsNotNone(config)
        self.assertIsInstance(config, AutoTradeConfig)
        self.assertFalse(config.enabled)
    
    async def test_get_config_with_cache(self) -> None:
        """测试使用缓存获取配置"""
        # 预设缓存
        self.config_manager._config_cache = self.test_auto_trade_config
        self.config_manager._last_reload = datetime.now()
        
        # 获取配置
        config = await self.config_manager.get_config()
        
        # 验证返回缓存的配置
        self.assertEqual(config, self.test_auto_trade_config)
        
        # 验证DAO没有被调用
        self.mock_config_dao.get_config.assert_not_called()
    
    async def test_get_config_cache_expired(self) -> None:
        """测试缓存过期时重新加载"""
        # 设置过期的缓存
        self.config_manager._config_cache = self.test_auto_trade_config
        self.config_manager._last_reload = datetime.now() - timedelta(minutes=10)  # 超过5分钟
        
        # 设置Mock返回值
        self.mock_config_dao.get_config = AsyncMock(return_value=self.test_config_doc)
        
        # 获取配置
        config = await self.config_manager.get_config()
        
        # 验证重新加载了配置
        self.mock_config_dao.get_config.assert_called_once()
        self.assertIsNotNone(config)
    
    async def test_force_reload(self) -> None:
        """测试强制重新加载配置"""
        # 预设缓存
        self.config_manager._config_cache = self.test_auto_trade_config
        self.config_manager._last_reload = datetime.now()
        
        # 设置Mock返回值
        self.mock_config_dao.get_config = AsyncMock(return_value=self.test_config_doc)
        
        # 强制重新加载配置
        await self.config_manager.force_reload()
        
        # 验证强制重新加载
        self.mock_config_dao.get_config.assert_called_once()
        self.assertIsNotNone(self.config_manager._last_reload)
    
    async def test_update_config(self) -> None:
        """测试更新配置"""
        # 设置Mock返回值
        mock_config_doc = Mock()
        mock_config_doc.update_config = AsyncMock()
        self.mock_config_dao.get_config = AsyncMock(return_value=mock_config_doc)
        
        # 更新配置
        new_config_data = {"enabled": False}
        result = await self.config_manager.update_config(new_config_data)
        
        # 验证更新成功
        self.assertTrue(result)
        mock_config_doc.update_config.assert_called_once_with(new_config_data)
    
    async def test_update_config_not_found(self) -> None:
        """测试更新不存在的配置"""
        # 设置Mock返回None
        self.mock_config_dao.get_config = AsyncMock(return_value=None)
        
        # 更新配置
        new_config_data = {"enabled": False}
        result = await self.config_manager.update_config(new_config_data)
        
        # 验证更新失败
        self.assertFalse(result)
    
    async def test_update_config_dao_exception(self) -> None:
        """测试更新配置时DAO异常"""
        # 设置Mock抛出异常
        self.mock_config_dao.get_config = AsyncMock(side_effect=Exception("Update failed"))
        
        # 更新配置
        new_config_data = {"enabled": False}
        result = await self.config_manager.update_config(new_config_data)
        
        # 验证更新失败
        self.assertFalse(result)
    
    async def test_is_enabled_true(self) -> None:
        """测试启用状态检查 - 启用"""
        # 设置启用的缓存配置
        self.config_manager._config_cache = self.test_auto_trade_config
        self.config_manager._last_reload = datetime.now()
        
        # 检查启用状态
        is_enabled = await self.config_manager.is_enabled()
        
        # 验证返回True
        self.assertTrue(is_enabled)
    
    async def test_is_enabled_false(self) -> None:
        """测试启用状态检查 - 禁用"""
        # 创建禁用的配置
        disabled_config = AutoTradeConfig(
            enabled=False,
            wallet_config=WalletConfig(
                default_private_key_env_var="TEST_PRIVATE_KEY",
                default_wallet_address="test_wallet_address"
            ),
            channels=[],
            default_timeout=60,
            max_total_retries=3,
            notification_config=NotificationConfig(
                notify_on_failure=True,
                notify_on_fallback=True,
                admin_chat_ids=[],
                include_trade_details=True
            )
        )
        self.config_manager._config_cache = disabled_config
        self.config_manager._last_reload = datetime.now()
        
        # 检查启用状态
        is_enabled = await self.config_manager.is_enabled()
        
        # 验证返回False
        self.assertFalse(is_enabled)
    
    async def test_is_enabled_no_cache(self) -> None:
        """测试无缓存时的启用状态检查"""
        # 确保无缓存，设置Mock返回默认配置
        self.config_manager._config_cache = None
        self.mock_config_dao.get_config = AsyncMock(return_value=None)
        
        # 检查启用状态
        is_enabled = await self.config_manager.is_enabled()
        
        # 验证返回False（默认配置禁用）
        self.assertFalse(is_enabled)
    
    async def test_get_enabled_channels(self) -> None:
        """测试获取启用的渠道"""
        # 设置缓存配置
        self.config_manager._config_cache = self.test_auto_trade_config
        self.config_manager._last_reload = datetime.now()
        
        # 获取启用的渠道
        enabled_channels = await self.config_manager.get_enabled_channels()
        
        # 验证返回启用的渠道
        self.assertEqual(len(enabled_channels), 2)
        channel_types = [ch.channel_type for ch in enabled_channels]
        self.assertIn("gmgn", channel_types)
        self.assertIn("jupiter", channel_types)
        
        # 验证按优先级排序
        self.assertEqual(enabled_channels[0].priority, 1)
        self.assertEqual(enabled_channels[1].priority, 2)
    
    async def test_get_enabled_channels_filtered(self) -> None:
        """测试过滤禁用渠道"""
        # 创建包含禁用渠道的配置
        trading_params = TradingParams(
            default_buy_amount_sol=0.01,
            default_buy_slippage_percentage=10.0,
            default_buy_priority_fee_sol=0.0005,
            default_sell_slippage_percentage=10.0,
            default_sell_priority_fee_sol=0.0005
        )
        
        enabled_channel = TradeChannelConfig(
            channel_type="gmgn",
            enabled=True,
            priority=1,
            timeout_seconds=60,
            max_retries=3,
            trading_params=trading_params,
            channel_params={}
        )
        
        disabled_channel = TradeChannelConfig(
            channel_type="jupiter",
            enabled=False,  # 禁用
            priority=2,
            timeout_seconds=90,
            max_retries=2,
            trading_params=trading_params,
            channel_params={}
        )
        
        config_with_disabled = AutoTradeConfig(
            enabled=True,
            wallet_config=WalletConfig(
                default_private_key_env_var="TEST_PRIVATE_KEY",
                default_wallet_address="test_wallet_address"
            ),
            channels=[enabled_channel, disabled_channel],
            default_timeout=60,
            max_total_retries=3,
            notification_config=NotificationConfig(
                notify_on_failure=True,
                notify_on_fallback=True,
                admin_chat_ids=[],
                include_trade_details=True
            )
        )
        
        self.config_manager._config_cache = config_with_disabled
        self.config_manager._last_reload = datetime.now()
        
        # 获取启用的渠道
        enabled_channels = await self.config_manager.get_enabled_channels()
        
        # 验证只返回启用的渠道
        self.assertEqual(len(enabled_channels), 1)
        self.assertEqual(enabled_channels[0].channel_type, "gmgn")
    
    async def test_get_enabled_channels_no_cache(self) -> None:
        """测试无缓存时获取启用渠道"""
        # 确保无缓存，设置Mock返回默认配置
        self.config_manager._config_cache = None
        self.mock_config_dao.get_config = AsyncMock(return_value=None)
        
        # 获取启用的渠道
        enabled_channels = await self.config_manager.get_enabled_channels()
        
        # 验证返回空列表（默认配置无渠道）
        self.assertEqual(len(enabled_channels), 0)
    
    async def test_get_wallet_config(self) -> None:
        """测试获取钱包配置"""
        # 设置缓存配置
        self.config_manager._config_cache = self.test_auto_trade_config
        self.config_manager._last_reload = datetime.now()
        
        # 获取钱包配置
        wallet_config = await self.config_manager.get_wallet_config()
        
        # 验证返回正确的钱包配置
        self.assertIsNotNone(wallet_config)
        self.assertEqual(wallet_config.default_private_key_env_var, "TEST_PRIVATE_KEY")
        self.assertEqual(wallet_config.default_wallet_address, "test_wallet_address")
    
    async def test_get_wallet_config_no_cache(self) -> None:
        """测试无缓存时获取钱包配置"""
        # 确保无缓存，设置Mock返回默认配置
        self.config_manager._config_cache = None
        self.mock_config_dao.get_config = AsyncMock(return_value=None)
        
        # 获取钱包配置
        wallet_config = await self.config_manager.get_wallet_config()
        
        # 验证返回默认钱包配置
        self.assertIsNotNone(wallet_config)
        self.assertEqual(wallet_config.default_private_key_env_var, "DEFAULT_WALLET_PRIVATE_KEY")
        self.assertEqual(wallet_config.default_wallet_address, "CONFIGURE_WALLET_ADDRESS")
    
    async def test_get_notification_config(self) -> None:
        """测试获取通知配置"""
        # 设置缓存配置
        self.config_manager._config_cache = self.test_auto_trade_config
        self.config_manager._last_reload = datetime.now()
        
        # 获取通知配置
        notification_config = await self.config_manager.get_notification_config()
        
        # 验证返回正确的通知配置
        self.assertIsNotNone(notification_config)
        self.assertTrue(notification_config.notify_on_failure)
        self.assertTrue(notification_config.include_trade_details)
    
    def test_get_cache_info(self) -> None:
        """测试获取缓存信息"""
        # 设置缓存状态
        self.config_manager._config_cache = self.test_auto_trade_config
        self.config_manager._last_reload = datetime.now()
        
        # 获取缓存信息
        cache_info = self.config_manager.get_cache_info()
        
        # 验证缓存信息
        self.assertIsInstance(cache_info, dict)
        self.assertTrue(cache_info["has_cache"])
        self.assertIsNotNone(cache_info["last_reload"])
        self.assertEqual(cache_info["reload_interval_seconds"], 300)
        self.assertIsNotNone(cache_info["cache_expires_at"])
    
    def test_get_cache_info_no_cache(self) -> None:
        """测试无缓存时的缓存信息"""
        # 确保无缓存
        self.config_manager._config_cache = None
        self.config_manager._last_reload = None
        
        # 获取缓存信息
        cache_info = self.config_manager.get_cache_info()
        
        # 验证缓存信息
        self.assertIsInstance(cache_info, dict)
        self.assertFalse(cache_info["has_cache"])
        self.assertIsNone(cache_info["last_reload"])
        self.assertIsNone(cache_info["cache_expires_at"])
    
    async def test_config_type_validation(self) -> None:
        """测试配置类型验证"""
        # 创建错误类型的配置文档
        wrong_config_doc = type('MockConfig', (), {
            'type': "auto_trade_manager",
            'data': {"invalid": "data"},  # 不是AutoTradeManagerConfig
            'updated_at': datetime.now(),
            'version': 1
        })()
        
        self.mock_config_dao.get_config = AsyncMock(return_value=wrong_config_doc)
        
        # 获取配置
        config = await self.config_manager.get_config()
        
        # 验证获取了默认配置（由于类型错误）
        self.assertIsNotNone(config)
        self.assertFalse(config.enabled)
    
    async def test_concurrent_get_config(self) -> None:
        """测试并发获取配置"""
        # 设置Mock返回值
        self.mock_config_dao.get_config = AsyncMock(return_value=self.test_config_doc)
        
        # 并发获取配置
        tasks = [self.config_manager.get_config() for _ in range(5)]
        results = await asyncio.gather(*tasks)
        
        # 验证所有结果都是相同的配置对象
        for result in results:
            self.assertIsInstance(result, AutoTradeConfig)
            self.assertEqual(result.enabled, True)
        
        # 验证DAO调用次数合理（可能只调用一次由于缓存）
        self.assertGreaterEqual(self.mock_config_dao.get_config.call_count, 1)


if __name__ == '__main__':
    unittest.main() 