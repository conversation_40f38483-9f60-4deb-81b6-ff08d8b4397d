from typing import Dict, List, Any, Optional

from dao.base_dao import BaseDAO
from models.gmgn_fresh_wallet_holders import GmgnFreshWalletHolders
from pymongo import UpdateOne
import logging

logger = logging.getLogger(__name__)


class GmgnFreshWalletHoldersDAO(BaseDAO[GmgnFreshWalletHolders]):
    """GMGN 新钱包持有者数据访问对象"""
    
    def __init__(self):
        super().__init__(GmgnFreshWalletHolders)
    
    async def find_by_address(self, chain: str, address: str) -> Optional[GmgnFreshWalletHolders]:
        """根据链和地址查询新钱包持有者信息
        
        Args:
            chain: 链名称
            address: 钱包地址
            
        Returns:
            Optional[GmgnFreshWalletHolders]: 新钱包持有者信息
        """
        return await self.model.find_one({"chain": chain, "address": address})
    
    async def find_by_token_address(self, chain: str, token_address: str, limit: int = 100) -> List[GmgnFreshWalletHolders]:
        """根据代币地址查询新钱包持有者列表
        
        Args:
            chain: 链名称
            token_address: 代币地址
            limit: 返回记录数限制
            
        Returns:
            List[GmgnFreshWalletHolders]: 新钱包持有者列表
        """
        return await self.model.find(
            {"chain": chain, "token_address": token_address}
        ).limit(limit).to_list()
    
    async def find_by_account_address(self, chain: str, account_address: str) -> Optional[GmgnFreshWalletHolders]:
        """根据账户地址查询新钱包持有者信息
        
        Args:
            chain: 链名称
            account_address: 账户地址
            
        Returns:
            Optional[GmgnFreshWalletHolders]: 新钱包持有者信息
        """
        return await self.model.find_one({"chain": chain, "account_address": account_address})
    
    async def find_by_wallet_tag(self, chain: str, wallet_tag: str) -> List[GmgnFreshWalletHolders]:
        """根据钱包标签查询新钱包持有者列表
        
        Args:
            chain: 链名称
            wallet_tag: 钱包标签
            
        Returns:
            List[GmgnFreshWalletHolders]: 新钱包持有者列表
        """
        return await self.model.find({"chain": chain, "wallet_tag_v2": wallet_tag}).to_list()
    
    async def find_by_twitter_username(self, twitter_username: str) -> List[GmgnFreshWalletHolders]:
        """根据Twitter用户名查询新钱包持有者列表
        
        Args:
            twitter_username: Twitter用户名
            
        Returns:
            List[GmgnFreshWalletHolders]: 新钱包持有者列表
        """
        return await self.model.find({"twitter_username": twitter_username}).to_list()
    
    async def find_by_profit_range(
        self,
        chain: str,
        min_profit: float = 0,
        max_profit: Optional[float] = None,
        limit: int = 100
    ) -> List[GmgnFreshWalletHolders]:
        """根据利润范围查询新钱包持有者列表
        
        Args:
            chain: 链名称
            min_profit: 最小利润
            max_profit: 最大利润
            limit: 返回记录数限制
            
        Returns:
            List[GmgnFreshWalletHolders]: 新钱包持有者列表
        """
        query = {"chain": chain, "profit": {"$gte": min_profit}}
        if max_profit is not None:
            query["profit"]["$lte"] = max_profit
            
        return await self.model.find(query).limit(limit).to_list()
    
    async def find_by_tags(
        self,
        chain: str,
        tags: List[str],
        match_all: bool = True
    ) -> List[GmgnFreshWalletHolders]:
        """根据标签列表查询新钱包持有者列表
        
        Args:
            chain: 链名称
            tags: 标签列表
            match_all: 是否匹配所有标签
            
        Returns:
            List[GmgnFreshWalletHolders]: 新钱包持有者列表
        """
        if match_all:
            query = {"chain": chain, "tags": {"$all": tags}}
        else:
            query = {"chain": chain, "tags": {"$in": tags}}
            
        return await self.model.find(query).to_list()
    
    async def find_active_holders(
        self,
        chain: str,
        since_timestamp: int,
        limit: int = 100
    ) -> List[GmgnFreshWalletHolders]:
        """查询活跃新钱包持有者列表
        
        Args:
            chain: 链名称
            since_timestamp: 起始时间戳
            limit: 返回记录数限制
            
        Returns:
            List[GmgnFreshWalletHolders]: 新钱包持有者列表
        """
        return await self.model.find(
            {
                "chain": chain,
                "last_active_timestamp": {"$gte": since_timestamp}
            }
        ).limit(limit).to_list()
    
    async def find_by_holding_amount(
        self,
        chain: str,
        min_amount: float = 0,
        max_amount: Optional[float] = None,
        limit: int = 100
    ) -> List[GmgnFreshWalletHolders]:
        """根据持仓数量范围查询新钱包持有者列表
        
        Args:
            chain: 链名称
            min_amount: 最小持仓数量
            max_amount: 最大持仓数量
            limit: 返回记录数限制
            
        Returns:
            List[GmgnFreshWalletHolders]: 新钱包持有者列表
        """
        query = {"chain": chain, "amount_cur": {"$gte": min_amount}}
        if max_amount is not None:
            query["amount_cur"]["$lte"] = max_amount
            
        return await self.model.find(query).limit(limit).to_list()
    
    async def find_top_fresh_wallet_holders(self, chain: str, limit: int = 20) -> List[GmgnFreshWalletHolders]:
        """查询顶级新钱包持有者列表
        
        Args:
            chain: 链名称
            limit: 返回记录数限制
            
        Returns:
            List[GmgnFreshWalletHolders]: 新钱包持有者列表
        """
        return await self.model.find(
            {"chain": chain, "tags": "fresh_wallet"}
        ).sort("amount_cur", -1).limit(limit).to_list()
    
    async def find_by_native_transfer(self, chain: str, from_address: str) -> List[GmgnFreshWalletHolders]:
        """根据原生转账来源地址查询新钱包持有者列表
        
        Args:
            chain: 链名称
            from_address: 来源地址
            
        Returns:
            List[GmgnFreshWalletHolders]: 新钱包持有者列表
        """
        return await self.model.find(
            {
                "chain": chain,
                "native_transfer.from_address": from_address
            }
        ).to_list()
    
    async def upsert_holder(self, chain: str, token_address: str, data: Dict[str, Any]) -> bool:
        """更新或插入新钱包持有者信息
        
        Args:
            chain: 链名称
            token_address: 代币地址
            data: 新钱包持有者数据
            
        Returns:
            bool: 是否更新成功
        """
        try:
            holder = await GmgnFreshWalletHolders.from_api_data(chain, data)
            holder.token_address = token_address
            
            await self.model.find_one(
                {"chain": chain, "address": holder.address}
            ).upsert().replace_one(holder)
            return True
        except Exception as e:
            print(f"更新新钱包持有者信息时发生错误: {str(e)}")
            return False
    
    async def upsert_holders_many(
        self,
        chain: str,
        data_list: List[Dict[str, Any]]
    ) -> int:
        """批量更新或插入新钱包持有者信息
        
        Args:
            chain: 链名称
            data_list: 新钱包持有者数据列表
            
        Returns:
            int: 更新记录数
        """
        try:
            holders: List[GmgnFreshWalletHolders] = [await GmgnFreshWalletHolders.from_api_data(chain, data) for data in data_list]
            
            if not holders:
                return 0
            
            operations = [
                UpdateOne(
                    {
                        "token_address": holder.token_address,
                        "address": holder.address,
                        "last_active_timestamp": holder.last_active_timestamp
                    },
                    {
                        "$set": holder.dict()
                    },
                    upsert=True
                )
                for holder in holders
            ]
            results = await self.collection.bulk_write(operations)
            logger.info(f"Upserted {results.modified_count} activities and inserted {results.upserted_count} activities")
            return results.modified_count + results.upserted_count
        except Exception as e:
            print(f"批量更新新钱包持有者信息时发生错误: {str(e)}")
            return 0
    
    async def delete_by_address(self, chain: str, address: str) -> bool:
        """根据地址删除新钱包持有者信息
        
        Args:
            chain: 链名称
            address: 钱包地址
            
        Returns:
            bool: 是否删除成功
        """
        try:
            result = await self.model.delete_one({"chain": chain, "address": address})
            return result.deleted_count > 0
        except Exception as e:
            print(f"删除新钱包持有者信息时发生错误: {str(e)}")
            return False
    
    async def delete_by_token_address(self, chain: str, token_address: str) -> int:
        """根据代币地址删除新钱包持有者信息
        
        Args:
            chain: 链名称
            token_address: 代币地址
            
        Returns:
            int: 删除记录数
        """
        try:
            result = await self.model.delete_many({"chain": chain, "token_address": token_address})
            return result.deleted_count
        except Exception as e:
            print(f"删除新钱包持有者信息时发生错误: {str(e)}")
            return 0
    
    async def delete_inactive_holders(self, chain: str, before_timestamp: int) -> int:
        """删除不活跃的新钱包持有者信息
        
        Args:
            chain: 链名称
            before_timestamp: 截止时间戳
            
        Returns:
            int: 删除记录数
        """
        try:
            result = await self.model.delete_many(
                {
                    "chain": chain,
                    "last_active_timestamp": {"$lt": before_timestamp}
                }
            )
            return result.deleted_count
        except Exception as e:
            print(f"删除不活跃新钱包持有者信息时发生错误: {str(e)}")
            return 0 