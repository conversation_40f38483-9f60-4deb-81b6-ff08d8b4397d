import asyncio
import argparse
import signal
import sys
import logging
import os
from pathlib import Path
from dotenv import load_dotenv
from beanie import init_beanie
from motor.motor_asyncio import AsyncIOMotorClient
from models import init_db
from utils.workflows.solana_monitor_job import (
    SolanaAddressSchedulerJob,
    SolanaMonitorJob,
    SolanaTransactionStorageJob
)
from utils.workflows.spider_x_user_info_job import (
    XUserSchedulerJob,
    SpiderXJob,
    XStorageJob
)
from utils.workflows.smart_money_monitor_job import SmartMoneyMonitorJob
from utils.workflows.smart_money_storage_job import SmartMoneyStorageJob
from utils.workflows.smart_money_address_sync_job import SmartMoney<PERSON>ddressSyncJob
from utils.workflows.gmgn_token_monitor_job import GmgnTokenMonitorJob
from utils.workflows.gmgn_token_storage_job import GmgnTokenStorageJob
from utils.workflows.gmgn_token_links_monitor_job import GmgnTokenLinksMonitorJob
from utils.workflows.gmgn_token_links_storage_job import GmgnTokenLinksStorageJob
from utils.workflows.gmgn_token_buyers_monitor_job import GmgnTokenBuyersMonitorJob
from utils.workflows.gmgn_token_buyers_storage_job import GmgnTokenBuyersStorageJob
from utils.workflows.gmgn_token_stats_monitor_job import GmgnTokenStatsMonitorJob
from utils.workflows.gmgn_token_stats_storage_job import GmgnTokenStatsStorageJob
from utils.workflows.gmgn_token_window_monitor_job import GmgnTokenWindowMonitorJob
from utils.workflows.gmgn_token_window_storage_job import GmgnTokenWindowStorageJob
from utils.workflows.gmgn_gas_price_monitor_job import GmgnGasPriceMonitorJob
from utils.workflows.gmgn_gas_price_storage_job import GmgnGasPriceStorageJob
from utils.connectors.mongodb import init_mongodb, motor_client
from utils.workflows.job import Status
from utils.workflows.gmgn_wallet_holdings_monitor_job import GmgnWalletHoldingsMonitorJob
from utils.workflows.gmgn_wallet_holdings_storage_job import GmgnWalletHoldingsStorageJob

# 加载环境变量
env_path = Path(__file__).parent / '.env'
load_dotenv(dotenv_path=env_path)

# 配置日志记录器
logging.basicConfig(
    level=os.getenv('LOG_LEVEL', 'INFO'),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 从环境变量设置第三方库的日志级别
pymongo_level = os.getenv('PYMONGO_LOG_LEVEL', 'WARNING')
beanie_level = os.getenv('BEANIE_LOG_LEVEL', 'WARNING')
motor_level = os.getenv('MOTOR_LOG_LEVEL', 'WARNING')
httpx_level = os.getenv('HTTPX_LOG_LEVEL', 'WARNING')
httpcore_level = os.getenv('HTTPCORE_LOG_LEVEL', 'WARNING')
urllib3_level = os.getenv('URLLIB3_LOG_LEVEL', 'WARNING')

# 设置各个库的日志级别
logging.getLogger("pymongo").setLevel(getattr(logging, pymongo_level))
logging.getLogger("beanie").setLevel(getattr(logging, beanie_level))
logging.getLogger("motor").setLevel(getattr(logging, motor_level))
logging.getLogger("httpx").setLevel(getattr(logging, httpx_level))
logging.getLogger("httpcore").setLevel(getattr(logging, httpcore_level))
logging.getLogger("urllib3").setLevel(getattr(logging, urllib3_level))

# 全局变量用于存储当前运行的任务
current_job = None

def signal_handler(signum, frame):
    """处理中断信号"""
    print("\n收到中断信号，正在优雅停止...")
    if current_job:
        current_job.stop()

async def init_database():
    """初始化数据库连接和集合"""
    # 初始化 MongoDB 和 Beanie
    await init_mongodb()
    await init_db()

async def run_job(job_name: str):
    """运行指定的任务"""
    global current_job
    
    try:
        # 先初始化数据库
        await init_database()
        
        job_classes = {
            # Solana 相关任务
            "solana:scheduler": SolanaAddressSchedulerJob,
            "solana:monitor": SolanaMonitorJob,
            "solana:storage": SolanaTransactionStorageJob,
            # X 相关任务
            "x:scheduler": XUserSchedulerJob,
            "x:spider": SpiderXJob,
            "x:storage": XStorageJob,
            # 智能钱包相关任务
            "smart-money:monitor": SmartMoneyMonitorJob,
            "smart-money:storage": SmartMoneyStorageJob,
            "smart-money:sync": SmartMoneyAddressSyncJob,
            # GMGN代币相关任务
            "gmgn:monitor": GmgnTokenMonitorJob,
            "gmgn:storage": GmgnTokenStorageJob,
            # GMGN代币链接相关任务
            "gmgn-links:monitor": GmgnTokenLinksMonitorJob,
            "gmgn-links:storage": GmgnTokenLinksStorageJob,
            # GMGN代币买家相关任务
            "gmgn-buyers:monitor": GmgnTokenBuyersMonitorJob,
            "gmgn-buyers:storage": GmgnTokenBuyersStorageJob,
            # GMGN代币统计信息相关任务
            "gmgn-stats:monitor": GmgnTokenStatsMonitorJob,
            "gmgn-stats:storage": GmgnTokenStatsStorageJob,
            # GMGN代币多窗口信息监控和存储任务
            "gmgn-token-window:monitor": GmgnTokenWindowMonitorJob,
            "gmgn-token-window:storage": GmgnTokenWindowStorageJob,
            # GMGN Gas价格相关任务
            "gmgn-gas:monitor": GmgnGasPriceMonitorJob,
            "gmgn-gas:storage": GmgnGasPriceStorageJob,
            # GMGN钱包持仓监控和存储任务
            "gmgn-holdings:monitor": GmgnWalletHoldingsMonitorJob,
            "gmgn-holdings:storage": GmgnWalletHoldingsStorageJob,
        }
        
        if job_name not in job_classes:
            print(f"未知的任务名称: {job_name}")
            print(f"可用的任务: {', '.join(job_classes.keys())}")
            return
            
        # 只实例化需要运行的任务
        current_job = job_classes[job_name]()
        
        # 直接调用process_stream方法
        await current_job.process_stream()
    except (KeyboardInterrupt, asyncio.CancelledError):
        print(f"正在停止 {job_name} 任务...")
        if current_job:
            try:
                current_job._update_task_status(Status.STOPPED.value, message="任务被用户中断")
                current_job._stop = True
            except Exception as e:
                print(f"更新任务状态时发生错误: {str(e)}")
    except Exception as e:
        print(f"任务 {job_name} 发生错误: {str(e)}")
        if current_job:
            try:
                current_job._update_task_status(Status.ERROR.value, error=str(e))
            except Exception as update_error:
                print(f"更新任务状态时发生错误: {str(update_error)}")
    finally:
        if current_job:
            print(f"任务 {job_name} 已停止")

def main():
    parser = argparse.ArgumentParser(description="任务启动器")
    parser.add_argument(
        "job",
        choices=[
            "solana:scheduler", "solana:monitor", "solana:storage",
            "x:scheduler", "x:spider", "x:storage",
            "smart-money:monitor", "smart-money:storage", "smart-money:sync",
            "gmgn:monitor", "gmgn:storage",
            "gmgn-links:monitor", "gmgn-links:storage",
            "gmgn-buyers:monitor", "gmgn-buyers:storage",
            "gmgn-stats:monitor", "gmgn-stats:storage",
            "gmgn-token-window:monitor", "gmgn-token-window:storage",
            "gmgn-gas:monitor", "gmgn-gas:storage",
            "gmgn-holdings:monitor",
            "gmgn-holdings:storage",
        ],
        help="要运行的任务名称"
    )
    
    args = parser.parse_args()
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        asyncio.run(run_job(args.job))
    except KeyboardInterrupt:
        print("程序被用户中断")

if __name__ == "__main__":
    main() 