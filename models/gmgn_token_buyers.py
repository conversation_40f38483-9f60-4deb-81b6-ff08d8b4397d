from datetime import datetime
from typing import Optional, List, Dict, Any
from beanie import Document, Indexed
from pydantic import BaseModel, Field

class HolderInfo(BaseModel):
    """持有者信息模型"""
    status: str = Field(description="持有状态(sold/sold_part/hold/bought_more/transfered)")
    wallet_address: str = Field(description="钱包地址")
    tags: List[str] = Field(default_factory=list, description="钱包标签")
    maker_token_tags: List[str] = Field(default_factory=list, description="做市商代币标签")

class StatusStats(BaseModel):
    """状态统计模型"""
    hold: int = Field(default=0, description="持有数量")
    bought_more: int = Field(default=0, description="增持数量")
    sold_part: int = Field(default=0, description="部分卖出数量")
    sold: int = Field(default=0, description="完全卖出数量")
    transfered: int = Field(default=0, description="转移数量")
    bought_rate: str = Field(default="0", description="买入比率")
    holding_rate: str = Field(default="0", description="持有比率")
    top_10_holder_rate: float = Field(default=0, description="前10持有者比率")

class SmartStats(BaseModel):
    """智能钱包统计模型"""
    smart_pos: List[str] = Field(default_factory=list, description="智能持仓列表")
    smart_count_hold: Optional[int] = Field(default=None, description="智能持有数量")
    smart_count_bought_more: Optional[int] = Field(default=None, description="智能增持数量")
    smart_count_sold_part: Optional[int] = Field(default=None, description="智能部分卖出数量")
    smart_count_sold: Optional[int] = Field(default=None, description="智能完全卖出数量")
    smart_count_transfered: Optional[int] = Field(default=None, description="智能转移数量")

class Changes(BaseModel):
    """变化统计模型"""
    sold_diff: int = Field(default=0, description="卖出变化")
    sold_part_diff: int = Field(default=0, description="部分卖出变化")
    hold_diff: int = Field(default=0, description="持有变化")
    bought_more: int = Field(default=0, description="增持变化")

class GmgnTokenBuyers(Document):
    """GMGN代币买家信息模型
    
    用于存储代币的买家信息，包括：
    - 基本信息（链、地址、持有者数量）
    - 状态统计（各种状态的数量和比率）
    - 智能钱包统计
    - 变化统计
    - 持有者详细信息
    """
    
    # 基本信息
    chain: str = Field(description="链名称")
    address: Indexed(str) = Field(description="代币地址")
    holder_count: int = Field(default=0, description="持有者总数")
    
    # 状态统计
    status_stats: StatusStats = Field(default_factory=StatusStats, description="状态统计")
    
    # 智能钱包统计
    smart_stats: SmartStats = Field(default_factory=SmartStats, description="智能钱包统计")
    
    # 变化统计
    changes: Changes = Field(default_factory=Changes, description="变化统计")
    
    # 持有者详细信息
    holders: List[HolderInfo] = Field(default_factory=list, description="持有者详细信息列表")
    
    # 时间信息
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")
    
    class Settings:
        name = "gmgn_token_buyers"
        use_state_management = True
    
    @classmethod
    async def from_api_data(cls, chain: str, address: str, data: Dict[str, Any]) -> 'GmgnTokenBuyers':
        """从API数据创建模型实例
        
        Args:
            chain: 链名称
            address: 代币地址
            data: API返回的数据
            
        Returns:
            GmgnTokenBuyers: 模型实例
        """
        status_now = data.get('statusNow', {})
        
        return cls(
            chain=chain,
            address=address,
            holder_count=data.get('holder_count', 0),
            
            # 状态统计
            status_stats=StatusStats(
                hold=status_now.get('hold', 0),
                bought_more=status_now.get('bought_more', 0),
                sold_part=status_now.get('sold_part', 0),
                sold=status_now.get('sold', 0),
                transfered=status_now.get('transfered', 0),
                bought_rate=status_now.get('bought_rate', '0'),
                holding_rate=status_now.get('holding_rate', '0'),
                top_10_holder_rate=status_now.get('top_10_holder_rate', 0)
            ),
            
            # 智能钱包统计
            smart_stats=SmartStats(
                smart_pos=status_now.get('smart_pos', []),
                smart_count_hold=status_now.get('smart_count_hold'),
                smart_count_bought_more=status_now.get('smart_count_bought_more'),
                smart_count_sold_part=status_now.get('smart_count_sold_part'),
                smart_count_sold=status_now.get('smart_count_sold'),
                smart_count_transfered=status_now.get('smart_count_transfered')
            ),
            
            # 变化统计
            changes=Changes(
                sold_diff=data.get('sold_diff', 0),
                sold_part_diff=data.get('sold_part_diff', 0),
                hold_diff=data.get('hold_diff', 0),
                bought_more=data.get('bought_more', 0)
            ),
            
            # 持有者详细信息
            holders=[
                HolderInfo(
                    status=holder.get('status', ''),
                    wallet_address=holder.get('wallet_address', ''),
                    tags=holder.get('tags', []),
                    maker_token_tags=holder.get('maker_token_tags', [])
                )
                for holder in data.get('holders', [])
            ]
        )
    
    class Config:
        json_schema_extra = {
            "example": {
                "chain": "sol",
                "address": "6p6xgHyF7AeE6TZkSmFsko444wqoP15icUSqi2jfGiPN",
                "holder_count": 70,
                "status_stats": {
                    "hold": 0,
                    "bought_more": 0,
                    "sold_part": 7,
                    "sold": 63,
                    "transfered": 0,
                    "bought_rate": "0.0119486",
                    "holding_rate": "0.00000000138931",
                    "top_10_holder_rate": 0.0942367
                },
                "smart_stats": {
                    "smart_pos": [],
                    "smart_count_hold": None,
                    "smart_count_bought_more": None,
                    "smart_count_sold_part": None,
                    "smart_count_sold": None,
                    "smart_count_transfered": None
                },
                "changes": {
                    "sold_diff": 0,
                    "sold_part_diff": 0,
                    "hold_diff": 0,
                    "bought_more": 0
                },
                "holders": [
                    {
                        "status": "sold",
                        "wallet_address": "7NEL682SBJhKLwEQKUSpEQcM1wnNniFsVgVPKGAVcb3A",
                        "tags": [],
                        "maker_token_tags": ["sniper"]
                    }
                ],
                "created_at": "2024-03-02T00:00:00",
                "updated_at": "2024-03-02T00:00:00"
            }
        } 