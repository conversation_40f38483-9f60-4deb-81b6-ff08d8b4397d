#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
盈利计算准确性Bug测试

该测试用于验证盈利计算Bug的修复效果。

Bug描述：
- 修复前：买入成本使用计划投入金额，卖出收入使用实际钱包变化，导致计算不准确
- 修复后：买入成本和卖出收入都使用实际金额，计算准确

测试目标：
1. 验证修复前的Bug场景（用于回归测试）
2. 验证修复后的正确计算逻辑
"""

import unittest
import asyncio
from datetime import datetime, timezone
from unittest.mock import MagicMock
from beanie import PydanticObjectId

from utils.trading.statistics.trade_pair_matcher import TradePairMatcher
from models.trade_record import TradeRecord, TradeType, TradeStatus


class TestProfitCalculationAccuracyBug(unittest.TestCase):
    """盈利计算准确性Bug测试类"""

    def setUp(self):
        """测试设置"""
        # 创建模拟的TradeRecordVerificationUpdater
        self.mock_verification_updater = MagicMock()
        
        # 创建TradePairMatcher实例
        self.matcher = TradePairMatcher(verification_updater=self.mock_verification_updater)

    def create_mock_trade_record(
        self,
        record_id: str,
        signal_id: str,
        trade_type: TradeType,
        token_in_amount: float = None,
        token_out_verified_amount: float = None,
        token_in_actual_amount: float = None,  # 实际买入花费（在修复后应该使用）
        token_out_actual_amount: float = None,  # 实际卖出收入（在修复后应该使用）
        created_at: datetime = None,
        **kwargs
    ) -> MagicMock:
        """创建模拟交易记录"""
        record = MagicMock(spec=TradeRecord)
        record.id = PydanticObjectId(record_id)
        record.signal_id = PydanticObjectId(signal_id)
        record.trade_type = trade_type
        record.status = TradeStatus.SUCCESS
        record.verification_status = "verified"
        
        # 关键字段：体现Bug的核心
        record.token_in_amount = token_in_amount  # 计划投入（买入时）
        record.token_out_verified_amount = token_out_verified_amount  # 验证收入（卖出时）
        record.token_in_actual_amount = token_in_actual_amount  # 实际花费（修复后优先使用）
        record.token_out_actual_amount = token_out_actual_amount  # 实际收入（修复后优先使用）
        
        # 其他基本字段
        record.strategy_name = "test_strategy"
        record.token_in_address = "SOL"
        record.token_out_address = "token_out" if trade_type == TradeType.BUY else "SOL"
        record.tx_hash = f"tx_hash_{record_id}"
        record.wallet_address = "test_wallet"
        record.created_at = created_at or datetime.now(timezone.utc)
        
        return record

    async def test_fixed_profit_calculation_with_actual_amounts(self):
        """
        测试修复后的盈利计算逻辑：使用实际金额进行准确计算
        
        场景：
        1. 用户计划用0.001 SOL买入，但实际花费了0.00310428 SOL（包含网络费、服务费等）
        2. 卖出时实际收到0.001448464 SOL
        3. 修复后逻辑：盈利 = 0.001448464 - 0.00310428 = -0.001655816 SOL（亏损率53.3%）
        """
        print("\n=== 测试修复后的准确盈利计算 ===")
        
        # 创建买入记录
        buy_record = self.create_mock_trade_record(
            record_id="507f1f77bcf86cd799439012",
            signal_id="507f1f77bcf86cd799439011",
            trade_type=TradeType.BUY,
            token_in_amount=0.001,  # 计划投入0.001 SOL
            token_in_actual_amount=0.00310428,  # 实际花费0.00310428 SOL（修复后应使用）
            created_at=datetime(2024, 1, 1, 10, 0, 0, tzinfo=timezone.utc)
        )

        # 创建卖出记录
        sell_record = self.create_mock_trade_record(
            record_id="507f1f77bcf86cd799439013",
            signal_id="507f1f77bcf86cd799439011",
            trade_type=TradeType.SELL,
            token_out_verified_amount=0.001448464,  # 验证收入
            token_out_actual_amount=0.001448464,  # 实际收入（修复后应使用）
            created_at=datetime(2024, 1, 1, 11, 0, 0, tzinfo=timezone.utc)
        )

        trade_records = [buy_record, sell_record]

        # 使用测试模式匹配交易对（基于相同signal_id）
        trade_pairs = await self.matcher._match_pairs_test_mode(
            buy_records=[buy_record],
            sell_records=[sell_record]
        )

        # 验证匹配成功
        self.assertEqual(len(trade_pairs), 1)
        trade_pair = trade_pairs[0]

        # 修复后：应该使用实际金额
        expected_buy_amount = 0.00310428  # 使用实际花费（修复后逻辑）
        expected_sell_amount = 0.001448464  # 使用实际收入
        expected_profit = expected_sell_amount - expected_buy_amount  # -0.001655816
        expected_profit_rate = (expected_profit / expected_buy_amount) * 100  # ~-53.3%

        # 验证修复后的正确计算结果
        self.assertAlmostEqual(trade_pair.buy_amount_sol, expected_buy_amount, places=6)
        self.assertAlmostEqual(trade_pair.sell_amount_sol, expected_sell_amount, places=6)
        self.assertAlmostEqual(trade_pair.profit_amount, expected_profit, places=6)
        self.assertAlmostEqual(trade_pair.profit_rate, expected_profit_rate, places=2)
        
        # 验证这是一个亏损的交易
        self.assertFalse(trade_pair.is_profitable)

        print(f"✅ 修复后计算结果验证成功:")
        print(f"   买入成本: {trade_pair.buy_amount_sol:.8f} SOL (实际花费)")
        print(f"   卖出收入: {trade_pair.sell_amount_sol:.8f} SOL (实际收入)")
        print(f"   盈利金额: {trade_pair.profit_amount:.8f} SOL")
        print(f"   盈利率: {trade_pair.profit_rate:.2f}%")

    async def test_fallback_to_planned_amount_when_actual_unavailable(self):
        """
        测试当实际金额不可用时的回退逻辑
        """
        print("\n=== 测试回退到计划金额的逻辑 ===")
        
        # 创建买入记录（没有实际金额）
        buy_record = self.create_mock_trade_record(
            record_id="507f1f77bcf86cd799439014",
            signal_id="507f1f77bcf86cd799439012",
            trade_type=TradeType.BUY,
            token_in_amount=0.001,  # 计划投入
            token_in_actual_amount=None,  # 没有实际金额
            created_at=datetime(2024, 1, 1, 10, 0, 0, tzinfo=timezone.utc)
        )

        # 创建卖出记录（没有实际金额）
        sell_record = self.create_mock_trade_record(
            record_id="507f1f77bcf86cd799439015",
            signal_id="507f1f77bcf86cd799439012",
            trade_type=TradeType.SELL,
            token_out_verified_amount=0.001448464,  # 验证收入
            token_out_actual_amount=None,  # 没有实际金额
            created_at=datetime(2024, 1, 1, 11, 0, 0, tzinfo=timezone.utc)
        )

        # 使用测试模式匹配交易对
        trade_pairs = await self.matcher._match_pairs_test_mode(
            buy_records=[buy_record],
            sell_records=[sell_record]
        )

        # 验证匹配成功并使用回退值
        self.assertEqual(len(trade_pairs), 1)
        trade_pair = trade_pairs[0]

        # 应该回退到计划金额和验证金额
        self.assertAlmostEqual(trade_pair.buy_amount_sol, 0.001, places=6)
        self.assertAlmostEqual(trade_pair.sell_amount_sol, 0.001448464, places=6)

        print(f"✅ 回退逻辑验证成功:")
        print(f"   买入成本: {trade_pair.buy_amount_sol:.8f} SOL (回退到计划金额)")
        print(f"   卖出收入: {trade_pair.sell_amount_sol:.8f} SOL (回退到验证金额)")

    async def test_bug_deviation_calculation_comparison(self):
        """
        测试Bug修复前后的偏差对比计算
        """
        print("\n=== 测试Bug修复前后的偏差对比 ===")
        
        # 实际数据（来自真实案例）
        planned_buy_amount = 0.001  # 计划投入（Bug逻辑使用）
        actual_buy_amount = 0.00310428  # 实际花费（修复后使用）
        actual_sell_amount = 0.001448464  # 实际收入
        
        # Bug逻辑计算（修复前）
        bug_profit = actual_sell_amount - planned_buy_amount  # 0.000448464
        bug_profit_rate = (bug_profit / planned_buy_amount) * 100  # ~44.8%
        
        # 正确逻辑计算（修复后）
        correct_profit = actual_sell_amount - actual_buy_amount  # -0.001655816
        correct_profit_rate = (correct_profit / actual_buy_amount) * 100  # ~-53.3%
        
        # 计算偏差
        profit_rate_deviation = abs(bug_profit_rate - correct_profit_rate)  # 98.19%
        
        print(f"Bug逻辑（修复前）:")
        print(f"  买入成本: {planned_buy_amount:.8f} SOL (计划金额)")
        print(f"  卖出收入: {actual_sell_amount:.8f} SOL (实际金额)")
        print(f"  盈利率: {bug_profit_rate:.2f}%")
        print(f"正确逻辑（修复后）:")
        print(f"  买入成本: {actual_buy_amount:.8f} SOL (实际金额)")
        print(f"  卖出收入: {actual_sell_amount:.8f} SOL (实际金额)")
        print(f"  盈利率: {correct_profit_rate:.2f}%")
        print(f"盈利率偏差: {profit_rate_deviation:.2f}%")
        
        # 验证修复确实解决了偏差问题
        self.assertGreater(profit_rate_deviation, 90, "修复前后的偏差应该很大，证明Bug的严重性")
        
        # 验证修复后逻辑的正确性
        expected_correct_profit_rate = (actual_sell_amount - actual_buy_amount) / actual_buy_amount * 100
        self.assertAlmostEqual(correct_profit_rate, expected_correct_profit_rate, places=2)


def run_async_test(test_method):
    """运行异步测试的辅助函数"""
    def wrapper(self):
        return asyncio.run(test_method(self))
    return wrapper


# 将异步测试方法转换为同步
TestProfitCalculationAccuracyBug.test_fixed_profit_calculation_with_actual_amounts = run_async_test(
    TestProfitCalculationAccuracyBug.test_fixed_profit_calculation_with_actual_amounts
)
TestProfitCalculationAccuracyBug.test_fallback_to_planned_amount_when_actual_unavailable = run_async_test(
    TestProfitCalculationAccuracyBug.test_fallback_to_planned_amount_when_actual_unavailable
)
TestProfitCalculationAccuracyBug.test_bug_deviation_calculation_comparison = run_async_test(
    TestProfitCalculationAccuracyBug.test_bug_deviation_calculation_comparison
)


if __name__ == '__main__':
    unittest.main() 