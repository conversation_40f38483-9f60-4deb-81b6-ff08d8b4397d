import unittest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime
import logging

from utils.trading.trade_orchestrator import TradeOrchestrator, TradeRequest
from utils.trading.channel_registry import ChannelRegistry
from utils.trading.channel_selector import ChannelSelector
from utils.trading.solana.trade_interface import TradeInterface, TradeResult, TradeType, TradeStatus
from models.config import TradeChannelConfig, TradingParams
from beanie import PydanticObjectId


class MockJupiterTradeService(TradeInterface):
    """模拟JupiterTradeService，用于复现HTTP客户端关闭Bug"""
    
    def __init__(self, should_succeed: bool = True):
        self.should_succeed = should_succeed
        self.http_client_closed = False
        self.execute_count = 0
        
    async def execute_trade(self, *args, **kwargs) -> TradeResult:
        """模拟交易执行"""
        self.execute_count += 1
        
        # 如果HTTP客户端已关闭，抛出错误
        if self.http_client_closed:
            return TradeResult(
                status=TradeStatus.FAILED,
                error_message="Cannot send a request, as the client has been closed."
            )
        
        if self.should_succeed:
            return TradeResult(
                status=TradeStatus.SUCCESS,
                tx_hash=f"mock_tx_hash_{self.execute_count}",
                actual_amount_in=**********,
                actual_amount_out=10000000
            )
        else:
            return TradeResult(
                status=TradeStatus.FAILED,
                error_message="Mock trade failure"
            )
    
    async def close(self):
        """模拟关闭HTTP客户端"""
        self.http_client_closed = True
        logging.info("MockJupiterTradeService HTTP client closed")
    
    def is_slippage_related_error(self, error_message: str = None, provider_response: dict = None) -> bool:
        """模拟滑点错误检测"""
        if error_message and "slippage" in error_message.lower():
            return True
        return False


class TestTradeOrchestratorHTTPClientBug(unittest.IsolatedAsyncioTestCase):
    """测试TradeOrchestrator中HTTP客户端过早关闭的Bug"""
    
    def setUp(self):
        """设置测试环境"""
        # 配置日志
        logging.basicConfig(level=logging.DEBUG)
        
        # 创建Mock组件
        self.mock_channel_registry = Mock(spec=ChannelRegistry)
        self.mock_channel_selector = Mock(spec=ChannelSelector)
        
        # 创建交易编排器
        self.orchestrator = TradeOrchestrator(
            channel_registry=self.mock_channel_registry,
            channel_selector=self.mock_channel_selector
        )
        
        # 创建测试用的交易请求
        self.test_trade_request = TradeRequest(
            trade_type=TradeType.BUY,
            token_in_address="So11111111111111111111111111111111111111112",
            token_out_address="J5rwuQH37VYNC4QtGMQie5qPFjV5aTPNukbyxok8pump",
            amount=0.001,
            wallet_private_key_b58="test_private_key",
            wallet_address="test_wallet_address",
            strategy_snapshot={
                'max_slippage_bps': 100,
                'priority_fee_lamports': 50000
            },
            signal_id=PydanticObjectId(),
            trade_record_id=PydanticObjectId()
        )
        
        # 创建渠道配置
        self.channel_config = TradeChannelConfig(
            channel_type="jupiter",
            priority=1,
            enabled=True,
            max_retries=3,
            timeout_seconds=30,
            trading_params=TradingParams(
                default_buy_slippage_percentage=1.0,
                default_sell_slippage_percentage=1.5
            )
        )
    
    async def test_http_client_stays_open_after_fix(self):
        """测试修复后HTTP客户端保持开启状态"""
        # 创建共享的Jupiter服务实例
        shared_jupiter_service = MockJupiterTradeService(should_succeed=True)
        
        # 配置Mock
        available_channels = ["jupiter"]  # select_channels返回渠道类型列表
        self.mock_channel_selector.select_channels = AsyncMock(return_value=available_channels)
        self.mock_channel_registry.get_channel = Mock(return_value=shared_jupiter_service)
        self.mock_channel_registry.get_channel_config = Mock(return_value=self.channel_config)
        self.mock_channel_registry.set_channel_health = Mock()
        
        # 第一次交易 - 应该成功
        result1 = await self.orchestrator.execute_trade(self.test_trade_request)
        
        # 验证第一次交易成功
        self.assertEqual(result1.final_status.value, "success")
        self.assertEqual(shared_jupiter_service.execute_count, 1)
        
        # 验证HTTP客户端没有被关闭（这是修复后的预期行为）
        self.assertFalse(shared_jupiter_service.http_client_closed, 
                        "修复后HTTP客户端应该保持开启状态")
        
        # 第二次交易 - 应该也成功，因为HTTP客户端仍然开启
        result2 = await self.orchestrator.execute_trade(self.test_trade_request)
        
        # 验证第二次交易成功
        self.assertEqual(result2.final_status.value, "success")
        self.assertIsNone(result2.error_summary)
        
        # 验证总共执行了2次交易
        self.assertEqual(shared_jupiter_service.execute_count, 2)
        
        # 验证HTTP客户端仍然保持开启状态
        self.assertFalse(shared_jupiter_service.http_client_closed, 
                        "修复后HTTP客户端应该始终保持开启状态")
    
    async def test_concurrent_trades_work_after_fix(self):
        """测试修复后并发交易都能成功"""
        # 创建共享的Jupiter服务实例
        shared_jupiter_service = MockJupiterTradeService(should_succeed=True)
        
        # 配置Mock
        available_channels = ["jupiter"]  # select_channels返回渠道类型列表
        self.mock_channel_selector.select_channels = AsyncMock(return_value=available_channels)
        self.mock_channel_registry.get_channel = Mock(return_value=shared_jupiter_service)
        self.mock_channel_registry.get_channel_config = Mock(return_value=self.channel_config)
        self.mock_channel_registry.set_channel_health = Mock()
        
        # 创建多个交易请求
        trade_requests = []
        for i in range(3):
            request = TradeRequest(
                trade_type=TradeType.BUY,
                token_in_address="So11111111111111111111111111111111111111112",
                token_out_address="J5rwuQH37VYNC4QtGMQie5qPFjV5aTPNukbyxok8pump",
                amount=0.001,
                wallet_private_key_b58="test_private_key",
                wallet_address="test_wallet_address",
                strategy_snapshot={
                    'max_slippage_bps': 100,
                    'priority_fee_lamports': 50000
                },
                signal_id=PydanticObjectId(),
                trade_record_id=PydanticObjectId()
            )
            trade_requests.append(request)
        
        # 并发执行交易
        tasks = [self.orchestrator.execute_trade(request) for request in trade_requests]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 分析结果
        successful_trades = 0
        failed_trades = 0
        client_closed_errors = 0
        
        for result in results:
            if isinstance(result, Exception):
                failed_trades += 1
            elif result.final_status.value == "success":
                successful_trades += 1
            elif result.final_status.value == "failed":
                failed_trades += 1
                if "client has been closed" in (result.error_summary or ""):
                    client_closed_errors += 1
        
        # 验证修复后的行为：所有交易都应该成功，没有客户端关闭错误
        self.assertEqual(client_closed_errors, 0, 
                        "修复后不应该有HTTP客户端关闭错误")
        self.assertEqual(successful_trades, len(trade_requests), 
                       "修复后所有交易都应该成功")
        self.assertEqual(failed_trades, 0, 
                       "修复后不应该有失败的交易")
        
        # 验证HTTP客户端仍然开启
        self.assertFalse(shared_jupiter_service.http_client_closed, 
                        "修复后HTTP客户端应该始终保持开启状态")
        
        print(f"并发交易结果: 成功={successful_trades}, 失败={failed_trades}, 客户端关闭错误={client_closed_errors}")


if __name__ == '__main__':
    unittest.main() 