"""飞书消息发送器

实现飞书机器人的消息发送功能，支持：
- 卡片消息 (Interactive Card)
- 文本消息 (Text Message)
- 重试机制
- 错误处理
"""

import asyncio
import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

import aiohttp

from .message_sender import MessageSender


class FeishuMessageSender(MessageSender):
    """飞书消息发送器
    
    支持通过飞书机器人Webhook发送消息，包括卡片消息和文本消息。
    """
    
    def __init__(self, webhook_url: str, max_retries: int = 3, retry_delay: float = 1.0):
        """初始化飞书消息发送器
        
        Args:
            webhook_url: 飞书机器人Webhook URL
            max_retries: 最大重试次数
            retry_delay: 重试延迟时间(秒)
        """
        super().__init__()
        self.webhook_url = webhook_url
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        
        if not webhook_url:
            raise ValueError("飞书Webhook URL不能为空")
        
        self.logger.info(f"飞书消息发送器初始化完成，Webhook: {webhook_url[:50]}...")
    
    async def send_message_to_user(self, message: str, user_id: str, parse_mode: str = "text") -> bool:
        """发送消息给用户
        
        注意：飞书机器人通过Webhook发送消息到群组，不支持直接发送给个人用户
        
        Args:
            message: 消息内容
            user_id: 用户ID (飞书中不适用)
            parse_mode: 解析模式 ("text" 或 "card")
            
        Returns:
            bool: 是否发送成功
        """
        self.logger.warning("飞书机器人不支持直接发送消息给个人用户，将发送到配置的群组")
        return await self.send_text_message(message)
    
    async def send_message_to_channel(self, message: str, channel_id: str, parse_mode: str = "text") -> bool:
        """发送消息给频道
        
        Args:
            message: 消息内容
            channel_id: 频道ID (飞书中不适用，使用配置的Webhook)
            parse_mode: 解析模式 ("text" 或 "card")
            
        Returns:
            bool: 是否发送成功
        """
        if parse_mode == "card":
            # 如果是卡片模式，message应该是JSON格式的卡片数据
            try:
                card_data = json.loads(message) if isinstance(message, str) else message
                return await self.send_card_message(card_data)
            except json.JSONDecodeError:
                self.logger.error("卡片消息格式错误，降级为文本消息")
                return await self.send_text_message(message)
        else:
            return await self.send_text_message(message)
    
    async def send_text_message(self, text: str) -> bool:
        """发送文本消息
        
        Args:
            text: 文本内容
            
        Returns:
            bool: 是否发送成功
        """
        payload = {
            "msg_type": "text",
            "content": {
                "text": text
            }
        }
        
        return await self._send_with_retry(payload)
    
    async def send_card_message(self, card_data: Dict[str, Any]) -> bool:
        """发送卡片消息
        
        Args:
            card_data: 卡片数据，包含header、elements等
            
        Returns:
            bool: 是否发送成功
        """
        payload = {
            "msg_type": "interactive",
            "card": card_data
        }
        
        return await self._send_with_retry(payload)
    
    async def send_trading_summary_card(self, summary_data: Dict[str, Any], report_url: Optional[str] = None) -> bool:
        """发送交易摘要卡片消息
        
        Args:
            summary_data: 交易摘要数据
            report_url: 报告链接URL
            
        Returns:
            bool: 是否发送成功
        """
        card = self._build_trading_summary_card(summary_data, report_url)
        return await self.send_card_message(card)
    
    def _build_trading_summary_card(self, summary_data: Dict[str, Any], report_url: Optional[str] = None) -> Dict[str, Any]:
        """构建交易摘要卡片
        
        Args:
            summary_data: 交易摘要数据
            report_url: 报告链接URL
            
        Returns:
            Dict: 卡片数据
        """
        # 获取基本信息
        period_type = summary_data.get("period_type", "日")
        start_date = summary_data.get("start_date", "")
        end_date = summary_data.get("end_date", "")
        
        # 总体统计
        overall = summary_data.get("overall_stats", {})
        total_trades = overall.get("total_trades", 0)
        win_rate = overall.get("win_rate", 0)
        total_pnl_rate = overall.get("total_pnl_rate", 0)
        overall_return_rate = overall.get("overall_return_rate", 0)
        
        # 策略统计
        strategies = summary_data.get("top_strategies", [])
        
        # 极值统计
        extremes = summary_data.get("extremes", {})
        
        # 构建卡片
        card = {
            "config": {
                "wide_screen_mode": True
            },
            "header": {
                "template": "blue",
                "title": {
                    "content": f"📊 交易{period_type}报",
                    "tag": "plain_text"
                },
                "subtitle": {
                    "content": f"{start_date} ~ {end_date}",
                    "tag": "plain_text"
                }
            },
            "elements": []
        }
        
        # 总体统计部分
        overall_section = {
            "tag": "div",
            "fields": [
                {
                    "is_short": True,
                    "text": {
                        "content": f"**总交易次数**\n{total_trades}",
                        "tag": "lark_md"
                    }
                },
                {
                    "is_short": True,
                    "text": {
                        "content": f"**总胜率**\n{win_rate:.2f}%",
                        "tag": "lark_md"
                    }
                },
                {
                    "is_short": True,
                    "text": {
                        "content": f"**总盈利率**\n{total_pnl_rate:.2f}%",
                        "tag": "lark_md"
                    }
                },
                {
                    "is_short": True,
                    "text": {
                        "content": f"**整体收益率**\n{overall_return_rate:.2f}%",
                        "tag": "lark_md"
                    }
                }
            ]
        }
        card["elements"].append(overall_section)
        
        # 分隔线
        card["elements"].append({"tag": "hr"})
        
        # 策略排行部分
        if strategies:
            strategy_section = {
                "tag": "div",
                "text": {
                    "content": "**🏆 策略排行榜 (前5名)**",
                    "tag": "lark_md"
                }
            }
            card["elements"].append(strategy_section)
            
            for i, strategy in enumerate(strategies[:5], 1):
                strategy_name = strategy.get("strategy", "未知策略")
                strategy_win_rate = strategy.get("win_rate", 0)
                strategy_pnl_rate = strategy.get("total_pnl_rate", 0)
                strategy_trades = strategy.get("total_trades", 0)
                
                strategy_item = {
                    "tag": "div",
                    "text": {
                        "content": f"**{i}. {strategy_name}**\n胜率: {strategy_win_rate:.2f}% | 盈利率: {strategy_pnl_rate:.2f}% | 交易: {strategy_trades}次",
                        "tag": "lark_md"
                    }
                }
                card["elements"].append(strategy_item)
        
        # 极值统计部分
        if extremes:
            card["elements"].append({"tag": "hr"})
            
            extremes_section = {
                "tag": "div",
                "text": {
                    "content": "**📈 极值统计**",
                    "tag": "lark_md"
                }
            }
            card["elements"].append(extremes_section)
            
            # 最大盈利和最大亏损
            max_profit = extremes.get("max_profit", {})
            max_loss = extremes.get("max_loss", {})
            
            if max_profit:
                profit_text = f"**最大盈利**: {max_profit.get('pnl_rate', 0):.2f}% ({max_profit.get('token', 'N/A')})"
                card["elements"].append({
                    "tag": "div",
                    "text": {
                        "content": profit_text,
                        "tag": "lark_md"
                    }
                })
            
            if max_loss:
                loss_text = f"**最大亏损**: {max_loss.get('pnl_rate', 0):.2f}% ({max_loss.get('token', 'N/A')})"
                card["elements"].append({
                    "tag": "div",
                    "text": {
                        "content": loss_text,
                        "tag": "lark_md"
                    }
                })
        
        # 报告链接按钮
        if report_url:
            card["elements"].append({"tag": "hr"})
            
            button_section = {
                "tag": "action",
                "actions": [
                    {
                        "tag": "button",
                        "text": {
                            "content": "📋 查看详细报告",
                            "tag": "plain_text"
                        },
                        "type": "primary",
                        "url": report_url
                    }
                ]
            }
            card["elements"].append(button_section)
        
        # 时间戳
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        footer_section = {
            "tag": "div",
            "text": {
                "content": f"⏰ 生成时间: {timestamp}",
                "tag": "lark_md"
            }
        }
        card["elements"].append(footer_section)
        
        return card
    
    async def _send_with_retry(self, payload: Dict[str, Any]) -> bool:
        """带重试机制的消息发送
        
        Args:
            payload: 消息载荷
            
        Returns:
            bool: 是否发送成功
        """
        last_error = None
        
        for attempt in range(self.max_retries + 1):
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        self.webhook_url,
                        json=payload,
                        headers={"Content-Type": "application/json"}
                    ) as response:
                        response_text = await response.text()
                        
                        if response.status == 200:
                            response_data = await response.json() if response_text else {}
                            if response_data.get("code") == 0:
                                self.logger.info("飞书消息发送成功")
                                return True
                            else:
                                error_msg = response_data.get("msg", "未知错误")
                                self.logger.error(f"飞书API返回错误: {error_msg}")
                                last_error = f"API错误: {error_msg}"
                        else:
                            self.logger.error(f"HTTP错误: {response.status}, 响应: {response_text}")
                            last_error = f"HTTP错误: {response.status}"
                        
            except aiohttp.ClientError as e:
                self.logger.error(f"网络错误 (尝试 {attempt + 1}/{self.max_retries + 1}): {e}")
                last_error = f"网络错误: {e}"
            except Exception as e:
                self.logger.error(f"未知错误 (尝试 {attempt + 1}/{self.max_retries + 1}): {e}")
                last_error = f"未知错误: {e}"
            
            # 如果不是最后一次尝试，等待后重试
            if attempt < self.max_retries:
                await asyncio.sleep(self.retry_delay * (attempt + 1))  # 指数退避
        
        self.logger.error(f"飞书消息发送失败，已重试{self.max_retries}次，最后错误: {last_error}")
        return False 