"""
并行控制模块

提供工作流节点的并行处理控制功能，包括：
1. 锁管理
2. 死锁检测和解决
3. 并发度控制
4. 资源管理
"""

import asyncio
import logging
import traceback
import time
import random
from typing import Dict, Set, Optional, List, Any
from dataclasses import dataclass
from datetime import datetime
from abc import ABC, abstractmethod

@dataclass
class LockInfo:
    """锁信息"""
    resource_id: str  # 资源ID
    owner: str       # 持有者（节点ID）
    acquired_time: datetime  # 获取时间
    timeout: float   # 超时时间（秒）

class DeadlockDetector:
    """死锁检测器"""
    
    def __init__(self):
        """初始化死锁检测器"""
        self.logger = logging.getLogger("DeadlockDetector")
        self.resource_allocation = {}  # 资源分配图：{resource_id: owner_id}
        self.resource_waiting = {}     # 资源等待图：{owner_id: set(resource_id)}
    
    def add_allocation(self, resource_id: str, owner_id: str):
        """添加资源分配记录"""
        self.resource_allocation[resource_id] = owner_id
    
    def remove_allocation(self, resource_id: str):
        """移除资源分配记录"""
        self.resource_allocation.pop(resource_id, None)
    
    def add_waiting(self, owner_id: str, resource_id: str):
        """添加资源等待记录"""
        if owner_id not in self.resource_waiting:
            self.resource_waiting[owner_id] = set()
        self.resource_waiting[owner_id].add(resource_id)
    
    def remove_waiting(self, owner_id: str, resource_id: str):
        """移除资源等待记录"""
        if owner_id in self.resource_waiting:
            self.resource_waiting[owner_id].discard(resource_id)
            if not self.resource_waiting[owner_id]:
                del self.resource_waiting[owner_id]
    
    def detect_deadlock(self) -> Optional[Set[str]]:
        """检测死锁
        
        使用深度优先搜索检测等待图中的环
        
        Returns:
            Optional[Set[str]]: 如果存在死锁，返回涉及的资源集合；否则返回None
        """
        visited = set()
        path = set()
        
        def dfs(owner_id: str) -> Optional[Set[str]]:
            if owner_id in path:
                # 发现环，说明存在死锁
                return {r for r in self.resource_waiting.get(owner_id, set())}
            
            if owner_id in visited:
                return None
            
            visited.add(owner_id)
            path.add(owner_id)
            
            # 检查该所有者等待的资源
            for resource_id in self.resource_waiting.get(owner_id, set()):
                # 获取资源当前的持有者
                current_owner = self.resource_allocation.get(resource_id)
                if current_owner:
                    result = dfs(current_owner)
                    if result is not None:
                        result.add(resource_id)
                        return result
            
            path.remove(owner_id)
            return None
        
        # 对每个等待资源的所有者进行检查
        for owner_id in self.resource_waiting:
            result = dfs(owner_id)
            if result is not None:
                return result
        
        return None

class ResourceLockManager:
    """资源锁管理器"""
    
    def __init__(self):
        """初始化资源锁管理器"""
        self.logger = logging.getLogger("ResourceLockManager")
        self._locks: Dict[str, asyncio.Lock] = {}  # 资源锁字典
        self._lock_info: Dict[str, LockInfo] = {}  # 锁信息字典
        self._deadlock_detector = DeadlockDetector()
        self._global_lock = asyncio.Lock()  # 用于保护内部数据结构
    
    async def acquire_lock(self, resource_id: str, owner_id: str, timeout: float = 30.0) -> bool:
        """获取资源锁
        
        Args:
            resource_id: 资源ID
            owner_id: 请求锁的所有者ID
            timeout: 超时时间（秒）
            
        Returns:
            bool: 是否成功获取锁
        """
        try:
            # 首先获取全局锁
            async with self._global_lock:
                # 创建资源锁（如果不存在）
                if resource_id not in self._locks:
                    self._locks[resource_id] = asyncio.Lock()
            
            # 记录等待信息
            self._deadlock_detector.add_waiting(owner_id, resource_id)
            
            # 尝试获取资源锁
            lock = self._locks[resource_id]
            acquired = await asyncio.wait_for(lock.acquire(), timeout)
            
            if acquired:
                # 更新锁信息和分配记录
                async with self._global_lock:
                    self._lock_info[resource_id] = LockInfo(
                        resource_id=resource_id,
                        owner=owner_id,
                        acquired_time=datetime.now(),
                        timeout=timeout
                    )
                    self._deadlock_detector.remove_waiting(owner_id, resource_id)
                    self._deadlock_detector.add_allocation(resource_id, owner_id)
                
                self.logger.debug(f"所有者 {owner_id} 成功获取资源 {resource_id} 的锁")
                return True
            
            return False
        except asyncio.TimeoutError:
            self.logger.warning(f"所有者 {owner_id} 获取资源 {resource_id} 的锁超时")
            self._deadlock_detector.remove_waiting(owner_id, resource_id)
            return False
        except Exception as e:
            self.logger.error(f"获取锁时发生错误: {str(e)}")
            self._deadlock_detector.remove_waiting(owner_id, resource_id)
            return False
    
    async def release_lock(self, resource_id: str, owner_id: str) -> bool:
        """释放资源锁
        
        Args:
            resource_id: 资源ID
            owner_id: 释放锁的所有者ID
            
        Returns:
            bool: 是否成功释放锁
        """
        try:
            async with self._global_lock:
                # 检查锁是否存在且属于该所有者
                lock_info = self._lock_info.get(resource_id)
                if not lock_info or lock_info.owner != owner_id:
                    self.logger.warning(f"所有者 {owner_id} 尝试释放未持有的资源 {resource_id} 的锁")
                    return False
                
                # 释放锁
                self._locks[resource_id].release()
                del self._lock_info[resource_id]
                self._deadlock_detector.remove_allocation(resource_id)
                
                self.logger.debug(f"所有者 {owner_id} 释放了资源 {resource_id} 的锁")
                return True
        except Exception as e:
            self.logger.error(f"释放锁时发生错误: {str(e)}")
            return False
    
    async def check_deadlocks(self) -> Optional[Set[str]]:
        """检查是否存在死锁
        
        Returns:
            Optional[Set[str]]: 如果存在死锁，返回涉及的资源集合；否则返回None
        """
        async with self._global_lock:
            return self._deadlock_detector.detect_deadlock()
    
    async def force_release_locks(self, resources: Set[str]) -> None:
        """强制释放一组资源的锁
        
        用于解决死锁情况
        
        Args:
            resources: 要释放的资源ID集合
        """
        async with self._global_lock:
            for resource_id in resources:
                if resource_id in self._locks and resource_id in self._lock_info:
                    lock_info = self._lock_info[resource_id]
                    self._locks[resource_id].release()
                    del self._lock_info[resource_id]
                    self._deadlock_detector.remove_allocation(resource_id)
                    self.logger.warning(f"强制释放资源 {resource_id} 的锁（原所有者: {lock_info.owner}）")

class ParallelExecutor:
    """并行执行器"""
    
    def __init__(self, concurrency: int = 5, test_mode: bool = False):
        """初始化并行执行器
        
        Args:
            concurrency: 并发度（同时运行的协程数）
            test_mode: 是否处于测试模式，在测试模式下不会抛出异常
        """
        self.logger = logging.getLogger("ParallelExecutor")
        self.concurrency = concurrency
        self._semaphore = asyncio.Semaphore(concurrency)
        self._tasks: Set[asyncio.Task] = set()
        self._task_metadata = {}  # 存储任务元数据：{task: {"coro": 原始协程, "retry_count": 重试次数}}
        self._base_retry_delay = 1.0  # 基础重试延迟时间（秒）
        self._max_retry_delay = 60.0  # 最大重试延迟时间（秒）
        self._test_mode = test_mode  # 测试模式标志
        
    def set_test_mode(self, enabled: bool = True):
        """设置测试模式
        
        Args:
            enabled: 是否启用测试模式
        """
        self._test_mode = enabled
        
    def get_task_metadata(self, task: asyncio.Task) -> Optional[Dict]:
        """获取任务元数据
        
        Args:
            task: 任务对象
            
        Returns:
            Optional[Dict]: 任务元数据，如果任务不存在则返回None
        """
        return self._task_metadata.get(task)
    
    def _calculate_retry_delay(self, retry_count: int) -> float:
        """计算重试延迟时间
        
        使用指数退避策略计算重试延迟时间，基础公式为：
        delay = base_delay * (2 ^ retry_count)
        例如：基础延迟为1秒时，第一次重试等待1秒，第二次2秒，第三次4秒，以此类推
        
        Args:
            retry_count: 当前重试次数
            
        Returns:
            float: 延迟时间（秒）
        """
        # 指数退避算法
        delay = self._base_retry_delay * (2 ** retry_count)
        # 添加随机抖动，避免多个任务同时重试
        jitter = random.uniform(0, 0.1 * delay)
        # 限制最大延迟时间
        return min(delay + jitter, self._max_retry_delay)
    
    async def restart_task(self, task: asyncio.Task) -> Optional[asyncio.Task]:
        """重启任务
        
        使用指数退避策略进行无限重试，每次重试的等待时间随重试次数增加而增加
        
        Args:
            task: 要重启的任务
            
        Returns:
            Optional[asyncio.Task]: 重启后的新任务，如果无法重启则返回None
        """
        metadata = self.get_task_metadata(task)
        if not metadata:
            self.logger.warning(f"无法重启任务 {task}：未找到任务元数据")
            return None
            
        # 增加重试计数并创建新任务
        original_coro_factory = metadata["coro"]
        retry_count = metadata["retry_count"] + 1
        
        # 计算等待时间
        retry_delay = self._calculate_retry_delay(retry_count - 1)  # -1 因为我们要用原始的重试次数计算延迟
        
        # 清理旧任务记录
        self._tasks.discard(task)
        if task in self._task_metadata:
            del self._task_metadata[task]
        
        # 记录重试信息
        self.logger.info(f"任务将在 {retry_delay:.2f} 秒后重试，第 {retry_count} 次重试")
        
        # 等待指定时间
        await asyncio.sleep(retry_delay)
        
        # 创建新任务
        try:
            # 检查original_coro_factory是函数还是协程
            if callable(original_coro_factory) and not asyncio.iscoroutine(original_coro_factory):
                # 如果是函数，需要调用它来获取新的协程
                new_coro = original_coro_factory()
            else:
                # 如果已经是协程，直接使用
                new_coro = original_coro_factory
            
            # 执行新协程
            return await self.execute(new_coro, retry_count=retry_count)
        except Exception as e:
            # 正常抛出异常
            self.logger.error(f"任务重启失败: {str(e)}")
            raise
    
    async def execute_batch(self, coros: List[Any]) -> List[asyncio.Task]:
        """批量执行协程
        
        Args:
            coros: 要执行的协程列表
            
        Returns:
            List[asyncio.Task]: 任务列表，不等待执行完成
        """
        if not coros:
            self.logger.warning("协程列表为空，不执行任何操作")
            return []
        
        # 记录协程信息用于调试
        self.logger.info(f"开始执行批量协程，数量: {len(coros)}")
        for i, coro in enumerate(coros):
            self.logger.debug(f"协程 {i}: {coro}")
        
        # 创建任务列表
        tasks = []
        for coro in coros:
            # 确保处理的是协程对象
            if asyncio.iscoroutine(coro):
                # 直接创建任务并添加到内部跟踪集合
                task = asyncio.create_task(coro)
                self._tasks.add(task)
                
                # 存储任务元数据
                self._task_metadata[task] = {
                    "coro": coro,
                    "retry_count": 0,
                    "start_time": datetime.now()
                }
                
                # 添加完成回调来处理任务完成
                task.add_done_callback(lambda t: self._cleanup_task(t))
                
                tasks.append(task)
            else:
                self.logger.error(f"对象 {coro} 不是协程，已跳过")
            
        if not tasks:
            self.logger.error("没有有效的协程可执行")
            return []
            
        # 直接返回任务列表，不等待执行完成
        self.logger.info(f"创建了 {len(tasks)} 个任务，直接返回不等待执行完成")
        return tasks
        
    def _cleanup_task(self, task: asyncio.Task) -> None:
        """清理完成的任务
        
        Args:
            task: 已完成的任务
        """
        # 从跟踪集合中移除
        self._tasks.discard(task)
        
        # 清理元数据
        if task in self._task_metadata:
            del self._task_metadata[task]
            
        # 检查是否有异常
        if task.done() and not task.cancelled():
            try:
                task.result()  # 获取结果，如果有异常会被捕获
            except Exception as e:
                self.logger.error(f"任务执行失败: {str(e)}\n{traceback.format_exc()}")
    
    def stop(self) -> None:
        """停止所有执行器"""
        # 取消所有正在运行的任务
        for task in list(self._tasks):
            if not task.done():
                task.cancel()
        
        # 清空任务集合
        self._tasks.clear()
        
        # 释放信号量以防止死锁
        for _ in range(self.concurrency):
            try:
                self._semaphore.release()
            except ValueError:
                # 如果信号量计数已经达到上限会抛出ValueError
                break
                
        self.logger.info("并行执行器已停止，所有任务已取消")

    async def execute(self, coro, retry_count: int = 0) -> Any:
        """执行一个协程
        
        Args:
            coro: 要执行的协程
            retry_count: 当前重试次数
            
        Returns:
            Any: 协程的执行结果
        """
        async with self._semaphore:
            task = asyncio.create_task(coro)
            self._tasks.add(task)
            
            # 存储任务元数据
            self._task_metadata[task] = {
                "coro": coro,
                "retry_count": retry_count,
                "start_time": datetime.now()
            }
            
            try:
                result = await task
                return result
            except Exception as e:
                # 在测试模式下不抛出异常，让测试可以继续
                if self._test_mode:
                    self.logger.info(f"测试模式：捕获异常: {str(e)}")
                    return None
                # 非测试模式正常抛出异常
                raise
            finally:
                self._tasks.discard(task)
                # 任务完成后清理元数据
                if task in self._task_metadata:
                    del self._task_metadata[task]

class IParallelControl(ABC):
    """并行控制接口"""
    
    @abstractmethod
    async def acquire_resource(self, resource_id: str, owner_id: str, timeout: float = 30.0) -> bool:
        """获取资源
        
        Args:
            resource_id: 资源ID
            owner_id: 请求资源的所有者ID
            timeout: 超时时间（秒）
            
        Returns:
            bool: 是否成功获取资源
        """
        pass
    
    @abstractmethod
    async def release_resource(self, resource_id: str, owner_id: str) -> bool:
        """释放资源
        
        Args:
            resource_id: 资源ID
            owner_id: 释放资源的所有者ID
            
        Returns:
            bool: 是否成功释放资源
        """
        pass
    
    @abstractmethod
    async def execute_parallel(self, coros: List[Any]) -> List[asyncio.Task]:
        """并行执行多个协程
        
        Args:
            coros: 要执行的协程列表
            
        Returns:
            List[asyncio.Task]: 任务列表，不等待执行完成
        """
        pass
    
    @abstractmethod
    async def set_concurrency(self, concurrency: int) -> None:
        """设置并发度
        
        Args:
            concurrency: 新的并发度
        """
        pass
    
    @abstractmethod
    async def stop_all_tasks(self) -> None:
        """停止所有正在运行的任务"""
        pass
    
    @abstractmethod
    async def check_and_restart_task(self) -> None:
        """检查任务状态并重启任务"""
        pass
    
    
class ParallelControl(IParallelControl):
    """并行控制实现类"""
    
    def __init__(self, concurrency: int = 1):
        """初始化并行控制器
        
        Args:
            concurrency: 初始并发度
        """
        self.logger = logging.getLogger("ParallelControl")
        self._lock_manager = ResourceLockManager()
        self._executor = ParallelExecutor(concurrency)
        
        # 启动死锁检测任务
        self._deadlock_check_task = asyncio.create_task(self._check_deadlocks())
    
    async def acquire_resource(self, resource_id: str, owner_id: str, timeout: float = 30.0) -> bool:
        """获取资源"""
        return await self._lock_manager.acquire_lock(resource_id, owner_id, timeout)
    
    async def release_resource(self, resource_id: str, owner_id: str) -> bool:
        """释放资源"""
        return await self._lock_manager.release_lock(resource_id, owner_id)
    
    async def execute_parallel(self, coros: List[Any]) -> List[asyncio.Task]:
        """并行执行协程"""
        return await self._executor.execute_batch(coros)
    
    async def set_concurrency(self, concurrency: int) -> None:
        """设置并发度"""
        if concurrency < 1:
            raise ValueError("并发度必须大于0")
        
        # 创建新的执行器
        self._executor = ParallelExecutor(concurrency)
        self.logger.info(f"并发度已设置为 {concurrency}")
    
    async def _check_deadlocks(self):
        """定期检查死锁"""
        while True:
            try:
                # 检查死锁
                deadlocked_resources = await self._lock_manager.check_deadlocks()
                if deadlocked_resources:
                    self.logger.warning(f"检测到死锁，涉及资源: {deadlocked_resources}")
                    # 强制释放死锁资源的锁
                    await self._lock_manager.force_release_locks(deadlocked_resources)
                
                # 每5秒检查一次
                await asyncio.sleep(5)
            except Exception as e:
                self.logger.error(f"死锁检查时发生错误: {str(e)}")
                await asyncio.sleep(5)  # 发生错误时也等待一段时间 
                
    async def stop_all_tasks(self) -> None:
        """停止所有正在运行的任务"""
        self._executor.stop()
    
    async def check_and_restart_task(self) -> None:
        """检查任务状态并重启任务
        
        检查所有任务的状态，对于已取消或异常终止的任务，
        根据配置的重试策略进行重启。
        """
        tasks_to_restart = []
        
        # 检查执行器中的任务状态
        for task in list(self._executor._tasks):
            if task.done():
                try:
                    # 尝试获取结果，如果有异常会被捕获
                    task.result()
                except (asyncio.CancelledError, Exception) as e:
                    # 记录异常信息
                    if isinstance(e, asyncio.CancelledError):
                        status = "取消"
                    else:
                        status = f"失败: {str(e)}"
                    
                    self.logger.warning(f"检测到任务 {task} {status}，准备重启")
                    tasks_to_restart.append(task)
        
        # 重启需要重启的任务
        restart_count = 0
        for task in tasks_to_restart:
            new_task = await self._executor.restart_task(task)
            if new_task:
                restart_count += 1
                
        if restart_count > 0:
            self.logger.info(f"总共重启了 {restart_count} 个任务")
