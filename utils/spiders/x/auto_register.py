import random
import string
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.webdriver import <PERSON><PERSON>hains
import time
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from utils.mail_helper import MailHelper
import imaplib
import email
import re
from email.header import decode_header
from datetime import datetime, timedelta


class AutoRegisterXSpider:
    def __init__(self, email_domain: str):
        self.username = self.random_string(10)
        self.password = self.random_string(10)
        self.email_prefix = self.random_string(5)
        self.email_domain = email_domain
        self.email = f"{self.email_prefix}@{self.email_domain}"
        self.birth_month = random.randint(1, 12)
        self.birth_day = random.randint(1, 28)
        self.birth_year = random.randint(1980, 2000)
        self.driver = None
        self.wait = None
        self.action_chains = None
        
        # 邮箱配置
        self.mail_helper = MailHelper({
            'server': 'imap.qq.com',
            'port': 993,
            'user': '<EMAIL>',
            'password': 'jeazgqkcjcixhhbh'
        })
    
    def random_string(self, length: int) -> str:
        return ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(length))
    
    def random_delay(self, min_delay=1, max_delay=3):
        """添加随机延时"""
        time.sleep(random.uniform(min_delay, max_delay))

    def human_like_type(self, element, text):
        """模拟人类输入"""
        for char in text:
            element.send_keys(char)
            time.sleep(random.uniform(0.1, 0.3))  # 每个字符之间的随机延迟

    def human_like_drag(self, source, target):
        """模拟人类拖拽动作"""
        # 获取源元素和目标元素的位置
        source_location = source.location
        target_location = target.location
        
        # 计算拖拽路径的中间点（添加一些随机偏移）
        mid_x = (source_location['x'] + target_location['x']) / 2 + random.randint(-20, 20)
        mid_y = (source_location['y'] + target_location['y']) / 2 + random.randint(-20, 20)
        
        # 移动到源元素
        self.action_chains.move_to_element(source)
        self.random_delay(0.2, 0.5)
        
        # 按下鼠标
        self.action_chains.click_and_hold()
        self.random_delay(0.2, 0.5)
        
        # 通过中间点移动到目标（使用多个点来模拟人类移动）
        self.action_chains.move_by_offset(
            (mid_x - source_location['x']) / 2,
            (mid_y - source_location['y']) / 2
        )
        self.random_delay(0.1, 0.3)
        
        self.action_chains.move_by_offset(
            (target_location['x'] - mid_x) / 2,
            (target_location['y'] - mid_y) / 2
        )
        self.random_delay(0.1, 0.3)
        
        # 移动到目标并释放
        self.action_chains.move_to_element(target)
        self.random_delay(0.2, 0.5)
        self.action_chains.release()
        
        # 执行动作
        self.action_chains.perform()
        self.random_delay(1, 2)

    def setup_driver(self):
        """设置 Selenium WebDriver 和浏览器指纹"""
        options = webdriver.ChromeOptions()
        
        # 添加代理设置
        options.add_argument('--proxy-server=http://127.0.0.1:7897')
        
        # 基本反检测设置
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-infobars')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--ignore-certificate-errors')
        options.add_argument('--disable-gpu')
        
        # 设置常见的 MacBook 显示配置
        display_configs = [
            # MacBook Air M1/M2 13-inch
            {'width': 1440, 'height': 900, 'pixel_ratio': 2},
            # MacBook Pro 14-inch
            {'width': 1512, 'height': 982, 'pixel_ratio': 2},
            # MacBook Pro 16-inch
            {'width': 1728, 'height': 1117, 'pixel_ratio': 2},
        ]
        
        # 随机选择一个显示配置
        display = random.choice(display_configs)
        
        # 设置窗口大小（实际物理像素）
        window_width = display['width']
        window_height = display['height']
        options.add_argument(f'--window-size={window_width},{window_height}')
        
        # 设置设备像素比
        options.add_argument(f'--force-device-scale-factor={display["pixel_ratio"]}')
        
        # 随机用户代理（确保与选择的显示配置匹配）
        user_agents = [
            # 最新版 Chrome on macOS
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            # 最新版 Safari on macOS
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2.1 Safari/605.1.15',
        ]
        options.add_argument(f'user-agent={random.choice(user_agents)}')
        
        # 禁用自动化标志
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        self.driver = webdriver.Chrome(options=options)
        self.action_chains = ActionChains(self.driver)
        
        # 注入 JavaScript 来修改浏览器指纹
        self.driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
            'source': '''
                // 修改 webdriver 标志
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined
                });
                
                // 修改 plugins
                Object.defineProperty(navigator, 'plugins', {
                    get: () => {
                        // 模拟真实的 Chrome 插件
                        return [
                            {
                                0: {type: "application/x-google-chrome-pdf", suffixes: "pdf", description: "Portable Document Format"},
                                description: "Portable Document Format",
                                filename: "internal-pdf-viewer",
                                name: "Chrome PDF Plugin",
                                length: 1
                            },
                            {
                                0: {type: "application/pdf", suffixes: "pdf", description: ""},
                                description: "",
                                filename: "chrome-pdf-viewer",
                                name: "Chrome PDF Viewer",
                                length: 1
                            }
                        ];
                    }
                });
                
                // 修改语言设置
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['zh-CN', 'zh', 'en-US', 'en']
                });
                
                // 修改 Chrome 运行时
                window.chrome = {
                    app: {
                        InstallState: {
                            DISABLED: 'disabled',
                            INSTALLED: 'installed',
                            NOT_INSTALLED: 'not_installed'
                        },
                        RunningState: {
                            CANNOT_RUN: 'cannot_run',
                            READY_TO_RUN: 'ready_to_run',
                            RUNNING: 'running'
                        },
                        getDetails: function() {},
                        getIsInstalled: function() {},
                        installState: function() {},
                        isInstalled: false,
                        runningState: function() {}
                    },
                    runtime: {
                        OnInstalledReason: {
                            CHROME_UPDATE: 'chrome_update',
                            INSTALL: 'install',
                            SHARED_MODULE_UPDATE: 'shared_module_update',
                            UPDATE: 'update'
                        },
                        OnRestartRequiredReason: {
                            APP_UPDATE: 'app_update',
                            OS_UPDATE: 'os_update',
                            PERIODIC: 'periodic'
                        },
                        PlatformArch: {
                            ARM: 'arm',
                            ARM64: 'arm64',
                            MIPS: 'mips',
                            MIPS64: 'mips64',
                            X86_32: 'x86-32',
                            X86_64: 'x86-64'
                        },
                        PlatformNaclArch: {
                            ARM: 'arm',
                            MIPS: 'mips',
                            MIPS64: 'mips64',
                            X86_32: 'x86-32',
                            X86_64: 'x86-64'
                        },
                        PlatformOs: {
                            ANDROID: 'android',
                            CROS: 'cros',
                            LINUX: 'linux',
                            MAC: 'mac',
                            OPENBSD: 'openbsd',
                            WIN: 'win'
                        },
                        RequestUpdateCheckStatus: {
                            NO_UPDATE: 'no_update',
                            THROTTLED: 'throttled',
                            UPDATE_AVAILABLE: 'update_available'
                        }
                    }
                };
                
                // 修改 Permissions API
                const originalQuery = window.navigator.permissions.query;
                window.navigator.permissions.query = (parameters) => (
                    parameters.name === 'notifications' ?
                        Promise.resolve({state: Notification.permission}) :
                        originalQuery(parameters)
                );
            '''
        })
        
        # 设置视口大小和缩放比例
        self.driver.execute_cdp_cmd('Emulation.setDeviceMetricsOverride', {
            'mobile': False,
            'width': window_width,
            'height': window_height,
            'deviceScaleFactor': display['pixel_ratio'],
            'screenOrientation': {'type': 'portraitPrimary', 'angle': 0}
        })
        
        # 设置视口大小
        self.driver.set_window_size(window_width, window_height)
        
        # 最大化窗口以确保一致的显示
        self.driver.maximize_window()

    def get_verification_code(self):
        """从邮箱获取X的验证码"""
        max_retries = 3
        retry_delay = 2
        mail = None
        
        for attempt in range(max_retries):
            try:
                print(f"开始连接邮箱服务器... (尝试 {attempt + 1}/{max_retries})")
                # 连接到IMAP服务器
                mail = imaplib.IMAP4_SSL(self.mail_helper.config['server'], self.mail_helper.config['port'], timeout=30)
                
                # 尝试登录
                mail.login(self.mail_helper.config['user'], self.mail_helper.config['password'])
                
                print("连接成功，开始搜索验证码邮件...")
                # 选择收件箱
                mail.select('INBOX')
                
                # 搜索最近30分钟内的邮件
                date = (datetime.now() - timedelta(minutes=30)).strftime("%d-%b-%Y")
                # 使用主题来搜索
                search_criteria = f'(SINCE "{date}" SUBJECT "是你的 X 验证码")'
                _, message_numbers = mail.search(None, search_criteria)
                
                if not message_numbers[0]:
                    print("未找到验证码邮件，等待2秒后重试...")
                    time.sleep(2)
                    continue
                
                # 获取最新的邮件
                latest_email_id = message_numbers[0].split()[-1]
                _, msg_data = mail.fetch(latest_email_id, '(RFC822)')
                email_body = msg_data[0][1]
                message = email.message_from_bytes(email_body)
                
                # 解析邮件内容
                code = None
                if message.is_multipart():
                    for part in message.walk():
                        if part.get_content_type() == "text/plain":
                            body = part.get_payload(decode=True).decode()
                            # 使用更精确的正则表达式匹配验证码
                            match = re.search(r'(\d{6})\s+是你的\s+X\s+验证码', body)
                            if match:
                                code = match.group(1)
                                break
                else:
                    body = message.get_payload(decode=True).decode()
                    match = re.search(r'(\d{6})\s+是你的\s+X\s+验证码', body)
                    if match:
                        code = match.group(1)
                
                if code:
                    print(f"成功获取验证码: {code}")
                    return code
                else:
                    print("未在邮件中找到验证码，等待2秒后重试...")
                    time.sleep(2)
                    continue
                
            except Exception as e:
                print(f"获取验证码时出错 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                if attempt < max_retries - 1:
                    print(f"等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避
                continue
                
            finally:
                if mail:
                    try:
                        mail.close()
                        mail.logout()
                    except:
                        pass
        
        print("验证码获取失败，达到最大重试次数")
        return None

    def handle_email_verification(self):
        """处理邮箱验证码"""
        try:
            print("等待验证码输入页面...")
            # 等待验证码输入框出现
            code_input = self.wait.until(EC.presence_of_element_located((
                By.CSS_SELECTOR, "div.css-146c3p1 input"
            )))
            
            # 获取验证码
            success, error, verification_code = self.mail_helper.get_verification_code(
                subject="是你的 X 验证码",
                pattern=r'请输入此验证码以开始使用\s*X：\s*\n+\s*(\d{6})'
            )
            
            if not success:
                print(f"无法获取验证码: {error}")
                return False
            
            # 输入验证码
            self.human_like_type(code_input, verification_code)
            self.random_delay()
            
            # 点击下一步按钮 - 使用多个选择器尝试定位按钮
            try:
                # 首先尝试使用role和文本定位
                next_button = self.wait.until(EC.element_to_be_clickable((
                    By.XPATH, "//div[@role='button'][.//span[text()='下一步']]"
                )))
            except:
                try:
                    # 尝试使用简单的文本匹配
                    next_button = self.wait.until(EC.element_to_be_clickable((
                        By.XPATH, "//*[text()='下一步']"
                    )))
                except:
                    # 最后尝试使用完整的CSS路径
                    next_button = self.wait.until(EC.element_to_be_clickable((
                        By.CSS_SELECTOR, "div[role='button'] div[dir='ltr']"
                    )))
            
            print("找到下一步按钮，准备点击...")
            self.random_delay(1, 2)
            
            # 尝试多种点击方式
            try:
                # 常规点击
                next_button.click()
            except:
                try:
                    # JavaScript点击
                    self.driver.execute_script("arguments[0].click();", next_button)
                except:
                    # Action Chains点击
                    ActionChains(self.driver).move_to_element(next_button).click().perform()
            
            print("已点击下一步按钮")
            self.random_delay(2, 3)
            
            # 等待密码输入框出现
            print("等待密码输入页面...")
            password_input = self.wait.until(EC.presence_of_element_located((
                By.CSS_SELECTOR, "input[type='password']"
            )))
            
            # 输入密码
            print("输入密码...")
            self.human_like_type(password_input, self.password)
            self.random_delay(1, 2)
            
            # 点击注册按钮
            print("准备点击注册按钮...")
            register_button = None
            
            # 尝试多种选择器定位注册按钮
            try:
                # 方法1：使用data-testid
                register_button = self.wait.until(EC.element_to_be_clickable((
                    By.CSS_SELECTOR, "button[data-testid='LoginForm_Login_Button']"
                )))
                print("通过data-testid找到注册按钮")
            except:
                try:
                    # 方法2：使用完整的class和role
                    register_button = self.wait.until(EC.element_to_be_clickable((
                        By.CSS_SELECTOR, "button[role='button'].css-175oi2r.r-sdzlij"
                    )))
                    print("通过class和role找到注册按钮")
                except:
                    try:
                        # 方法3：使用XPath和完整路径
                        register_button = self.wait.until(EC.element_to_be_clickable((
                            By.XPATH, "//button[@role='button']//div[contains(@class, 'css-146c3p1')]//span[contains(@class, 'css-1jxf684')]/span[text()='注册']"
                        )))
                        print("通过XPath完整路径找到注册按钮")
                    except:
                        try:
                            # 方法4：使用组合属性
                            register_button = self.wait.until(EC.element_to_be_clickable((
                                By.XPATH, "//button[@role='button' and @type='button']//span[contains(@class, 'css-1jxf684')]//span[text()='注册']"
                            )))
                            print("通过组合属性找到注册按钮")
                        except:
                            # 方法5：使用JavaScript和完整选择器
                            register_button = self.driver.execute_script("""
                                return document.querySelector('button[data-testid="LoginForm_Login_Button"], button.css-175oi2r.r-sdzlij');
                            """)
                            print("通过JavaScript找到注册按钮")
            
            if not register_button:
                raise Exception("无法找到注册按钮")
            
            print("找到注册按钮，准备点击...")
            self.random_delay(1, 2)
            
            # 确保按钮在视图中
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", register_button)
            self.random_delay(0.5, 1)
            
            # 尝试多种点击方式
            click_success = False
            
            # 方法1：JavaScript点击（优先使用，因为更可靠）
            try:
                self.driver.execute_script("""
                    arguments[0].click();
                    arguments[0].dispatchEvent(new MouseEvent('click', {
                        'view': window,
                        'bubbles': true,
                        'cancelable': true
                    }));
                """, register_button)
                click_success = True
                print("JavaScript点击成功")
            except:
                pass
            
            # 方法2：常规点击
            if not click_success:
                try:
                    register_button.click()
                    click_success = True
                    print("常规点击成功")
                except:
                    pass
            
            # 方法3：Action Chains点击
            if not click_success:
                try:
                    ActionChains(self.driver).move_to_element(register_button).click().perform()
                    click_success = True
                    print("Action Chains点击成功")
                except:
                    pass
            
            if not click_success:
                raise Exception("所有点击方法都失败了")
            
            print("已点击注册按钮")
            self.random_delay(2, 3)
            
            # 等待并点击跳过按钮
            print("等待跳过按钮出现...")
            try:
                # 方法1：使用文本定位
                skip_button = self.wait.until(EC.element_to_be_clickable((
                    By.XPATH, "//span[text()='跳过']"
                )))
                print("通过文本找到跳过按钮")
            except:
                try:
                    # 方法2：使用更宽松的XPath
                    skip_button = self.wait.until(EC.element_to_be_clickable((
                        By.XPATH, "//*[contains(text(), '跳过')]"
                    )))
                    print("通过模糊文本找到跳过按钮")
                except:
                    try:
                        # 方法3：使用完整的CSS路径
                        skip_button = self.wait.until(EC.element_to_be_clickable((
                            By.CSS_SELECTOR, "div[role='button'] div[dir='ltr']"
                        )))
                        print("通过CSS路径找到跳过按钮")
                    except Exception as e:
                        print(f"无法找到跳过按钮: {str(e)}")
                        return False

            print("找到跳过按钮，准备点击...")
            self.random_delay(1, 2)
            
            # 尝试多种点击方式
            click_success = False
            
            # 方法1：JavaScript点击
            try:
                self.driver.execute_script("arguments[0].click();", skip_button)
                click_success = True
                print("JavaScript点击跳过按钮成功")
            except:
                pass
            
            # 方法2：常规点击
            if not click_success:
                try:
                    skip_button.click()
                    click_success = True
                    print("常规点击跳过按钮成功")
                except:
                    pass
            
            # 方法3：Action Chains点击
            if not click_success:
                try:
                    ActionChains(self.driver).move_to_element(skip_button).click().perform()
                    click_success = True
                    print("Action Chains点击跳过按钮成功")
                except:
                    pass
            
            if not click_success:
                print("无法点击跳过按钮")
                return False
            
            print("已点击跳过按钮")
            self.random_delay(2, 3)
            
            return True
            
        except Exception as e:
            print(f"处理邮箱验证码时出错: {str(e)}")
            return False

    def handle_verification(self):
        """处理验证码弹窗，如果存在的话"""
        try:
            print("检查是否需要验证...")
            # 使用较短的超时时间检查验证弹窗是否存在
            short_wait = WebDriverWait(self.driver, 5)
            
            try:
                # 检查验证弹窗是否出现
                short_wait.until(EC.presence_of_element_located((
                    By.XPATH, "//div[contains(text(), '验证你的账户') or contains(text(), '验证')]"
                )))
                print("检测到验证弹窗，开始处理...")
                
                # 使用多种选择器尝试定位验证按钮
                verify_button = None
                try:
                    # 尝试CSS选择器
                    verify_button = short_wait.until(EC.element_to_be_clickable((
                        By.CSS_SELECTOR, "button.sc-nkuzb1-0[data-theme='home.verifyButton']"
                    )))
                    print("通过CSS选择器找到验证按钮")
                except:
                    try:
                        # 尝试XPath
                        verify_button = short_wait.until(EC.element_to_be_clickable((
                            By.XPATH, "//button[text()='验证']"
                        )))
                        print("通过XPath找到验证按钮")
                    except:
                        try:
                            # 尝试使用JavaScript点击
                            verify_button = self.driver.execute_script(
                                "return document.querySelector('button[data-theme=\"home.verifyButton\"]')"
                            )
                            print("通过JavaScript找到验证按钮")
                        except:
                            print("无法找到验证按钮")
                            return False

                if verify_button:
                    try:
                        # 尝试常规点击
                        verify_button.click()
                        print("常规点击验证按钮成功")
                    except:
                        try:
                            # 尝试JavaScript点击
                            self.driver.execute_script("arguments[0].click();", verify_button)
                            print("JavaScript点击验证按钮成功")
                        except Exception as e:
                            print(f"点击验证按钮失败: {str(e)}")
                            return False

                self.random_delay(2, 3)
                print("点击验证按钮后等待")
                
                # 等待图片加载完成
                self.wait.until(EC.presence_of_element_located((By.XPATH, "//img[contains(@alt, '验证码图片')]")))
                self.random_delay(1, 2)
                
                # 找到拖动源元素（左侧图标）
                source_element = self.wait.until(EC.presence_of_element_located((
                    By.XPATH, "//div[contains(@aria-label, '拖动') or contains(@role, 'button')]"
                )))
                
                # 找到目标元素（右侧杯子）
                target_element = self.wait.until(EC.presence_of_element_located((
                    By.XPATH, "//div[contains(@aria-label, '目标') or contains(@role, 'button')]"
                )))
                
                # 执行拖拽
                self.human_like_drag(source_element, target_element)
                return True
                
            except TimeoutException:
                print("未检测到验证弹窗，继续下一步...")
                return True
                
        except Exception as e:
            print(f"处理验证码时出错: {str(e)}")
            return False

    def handle_turnstile(self):
        """处理 Turnstile 验证，如果存在的话"""
        try:
            print("检查是否存在 Turnstile 验证...")
            # 使用较短的超时时间检查 Turnstile iframe 是否存在
            short_wait = WebDriverWait(self.driver, 5)
            
            try:
                # 检查是否存在 Turnstile iframe
                turnstile_frame = short_wait.until(EC.presence_of_element_located((
                    By.XPATH, "//iframe[contains(@src, 'challenges.cloudflare.com')]"
                )))
                print("检测到 Turnstile 验证，开始处理...")
                
                # 切换到 Turnstile iframe
                self.driver.switch_to.frame(turnstile_frame)
                
                # 等待验证按钮出现并点击
                verify_button = short_wait.until(EC.element_to_be_clickable((
                    By.XPATH, "//div[contains(@class, 'turnstile-checkbox')]"
                )))
                self.random_delay(1, 2)
                verify_button.click()
                
                # 等待验证完成
                self.random_delay(2, 3)
                
                # 切回主框架
                self.driver.switch_to.default_content()
                
                print("Turnstile 验证完成")
                return True
                
            except TimeoutException:
                print("未检测到 Turnstile 验证，继续下一步...")
                return True
                
        except Exception as e:
            print(f"处理 Turnstile 验证时出错: {str(e)}")
            return False

    def register(self):
        """执行注册流程"""
        try:
            self.setup_driver()
            self.driver.get("https://x.com/i/flow/signup")
            self.wait = WebDriverWait(self.driver, 20)
            
            # 处理 Turnstile 验证
            if not self.handle_turnstile():
                raise Exception("Turnstile 验证失败")
            
            # 随机延迟后点击创建账号按钮
            self.random_delay(1, 2)
            create_account = self.wait.until(EC.element_to_be_clickable((By.XPATH, "//span[text()='创建账号']")))
            create_account.click()
            self.random_delay()
            
            # 输入名字和邮箱
            name_input = self.wait.until(EC.presence_of_element_located((By.NAME, "name")))
            self.human_like_type(name_input, self.username)
            self.random_delay()
            
            email_input = self.driver.find_element(By.NAME, "email")
            self.human_like_type(email_input, self.email)
            self.random_delay()
            
            # 选择出生日期
            print("开始选择出生日期...")
            
            # 选择月份
            print(f"选择月份: {self.birth_month}")
            month_dropdown = self.wait.until(EC.presence_of_element_located((By.ID, "SELECTOR_1")))
            self.random_delay(0.5, 1)
            month_dropdown.click()
            self.random_delay(0.5, 1)
            month_option = self.wait.until(EC.presence_of_element_located((
                By.XPATH, f"//select[@id='SELECTOR_1']/option[@value='{self.birth_month}']"
            )))
            month_option.click()
            self.random_delay(0.5, 1)
            
            # 选择日期
            print(f"选择日期: {self.birth_day}")
            day_dropdown = self.wait.until(EC.presence_of_element_located((By.ID, "SELECTOR_2")))
            # 确保日期下拉框可以点击
            self.wait.until(EC.element_to_be_clickable((By.ID, "SELECTOR_2")))
            self.random_delay(0.5, 1)
            
            # 使用 JavaScript 点击日期下拉框
            self.driver.execute_script("arguments[0].click();", day_dropdown)
            self.random_delay(0.5, 1)
            
            # 等待日期选项出现并选择
            day_option = self.wait.until(EC.presence_of_element_located((
                By.XPATH, f"//select[@id='SELECTOR_2']/option[@value='{self.birth_day}']"
            )))
            # 使用 JavaScript 选择日期
            self.driver.execute_script("arguments[0].selected = true; arguments[0].click();", day_option)
            self.random_delay(0.5, 1)
            
            # 触发 change 事件
            self.driver.execute_script("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", day_dropdown)
            self.random_delay(0.5, 1)
            
            # 选择年份
            print(f"选择年份: {self.birth_year}")
            year_dropdown = self.wait.until(EC.presence_of_element_located((By.ID, "SELECTOR_3")))
            self.random_delay(0.5, 1)
            year_dropdown.click()
            self.random_delay(0.5, 1)
            year_option = self.wait.until(EC.presence_of_element_located((
                By.XPATH, f"//select[@id='SELECTOR_3']/option[@value='{self.birth_year}']"
            )))
            year_option.click()
            self.random_delay(0.5, 1)
            
            # 验证日期是否被正确选择
            print("验证日期选择...")
            selected_day = self.driver.find_element(By.ID, "SELECTOR_2").get_attribute("value")
            if selected_day != str(self.birth_day):
                print(f"日期选择可能不成功，重试中... (选中的值: {selected_day}, 期望值: {self.birth_day})")
                # 重试选择日期
                day_dropdown.click()
                self.random_delay(0.5, 1)
                day_option = self.wait.until(EC.presence_of_element_located((
                    By.XPATH, f"//select[@id='SELECTOR_2']/option[@value='{self.birth_day}']"
                )))
                day_option.click()
                self.random_delay(0.5, 1)
            
            # 点击下一步
            next_button = self.driver.find_element(By.XPATH, "//span[text()='下一步']")
            next_button.click()
            self.random_delay(2, 4)
            
            # 处理可能出现的验证码
            if not self.handle_verification():
                raise Exception("验证码处理失败")
            
            # 处理邮箱验证码
            if not self.handle_email_verification():
                raise Exception("邮箱验证码处理失败")
            
            # 注册完成后保存账号信息
            print(f"注册成功！\n用户名: {self.username}\n邮箱: {self.email}\n密码: {self.password}")
            
            # 保持浏览器打开一段时间以便查看结果
            time.sleep(10)
            
        except Exception as e:
            print(f"注册过程中出现错误: {str(e)}")
        finally:
            if self.driver:
                self.driver.quit()


if __name__ == "__main__":
    spider = AutoRegisterXSpider("gaojbindev.xyz")
    spider.register()
