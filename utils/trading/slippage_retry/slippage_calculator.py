"""滑点计算器 - 负责滑点递增计算和边界检查"""

from typing import Optional, Tuple
import logging
from models.slippage_retry import SlippageRetryConfig

logger = logging.getLogger(__name__)


class SlippageCalculator:
    """滑点计算器 - 负责滑点递增计算和边界检查"""
    
    def __init__(self):
        """初始化滑点计算器"""
        pass
    
    def calculate_next_slippage(
        self,
        current_slippage: float,
        config: SlippageRetryConfig,
        trade_type: str = "buy"
    ) -> Tuple[float, bool]:
        """
        计算下一次重试的滑点值
        
        Args:
            current_slippage: 当前滑点百分比
            config: 滑点重试配置
            trade_type: 交易类型 (buy/sell)
            
        Returns:
            Tuple[float, bool]: (新滑点值, 是否达到上限)
        """
        # 检查是否启用滑点递增
        if not config.enabled:
            logger.debug(f"滑点递增未启用，保持当前滑点: {current_slippage}%")
            return current_slippage, False
        
        # 计算新滑点值
        new_slippage = current_slippage + config.increment_percentage
        
        # 检查是否超过上限
        if new_slippage > config.max_slippage_percentage:
            logger.warning(
                f"{trade_type.upper()} 滑点调整达到上限: "
                f"尝试调整至 {new_slippage}%, 但上限为 {config.max_slippage_percentage}%"
            )
            return config.max_slippage_percentage, True
        
        logger.info(
            f"{trade_type.upper()} 滑点从 {current_slippage}% 增加至 {new_slippage}% "
            f"(步长: {config.increment_percentage}%)"
        )
        return new_slippage, False
    
    def is_slippage_at_limit(
        self,
        current_slippage: float,
        config: SlippageRetryConfig
    ) -> bool:
        """
        检查当前滑点是否已达到上限
        
        Args:
            current_slippage: 当前滑点百分比
            config: 滑点重试配置
            
        Returns:
            bool: 是否已达到上限
        """
        return current_slippage >= config.max_slippage_percentage
    
    def get_remaining_slippage_room(
        self,
        current_slippage: float,
        config: SlippageRetryConfig
    ) -> float:
        """
        获取剩余的滑点调整空间
        
        Args:
            current_slippage: 当前滑点百分比
            config: 滑点重试配置
            
        Returns:
            float: 剩余可调整的滑点百分比
        """
        return max(0, config.max_slippage_percentage - current_slippage)
    
    def calculate_slippage_steps_remaining(
        self,
        current_slippage: float,
        config: SlippageRetryConfig
    ) -> int:
        """
        计算剩余的滑点调整步数
        
        Args:
            current_slippage: 当前滑点百分比
            config: 滑点重试配置
            
        Returns:
            int: 剩余调整步数
        """
        if not config.enabled or config.increment_percentage <= 0:
            return 0
        
        remaining_room = self.get_remaining_slippage_room(current_slippage, config)
        return int(remaining_room / config.increment_percentage)
    
    def validate_slippage_config(self, config: SlippageRetryConfig) -> bool:
        """
        验证滑点配置的有效性
        
        Args:
            config: 滑点重试配置
            
        Returns:
            bool: 配置是否有效
        """
        if not isinstance(config, SlippageRetryConfig):
            logger.error("配置类型错误：必须是 SlippageRetryConfig")
            return False
        
        if config.increment_percentage <= 0:
            logger.error(f"滑点增加步长必须大于0，当前值: {config.increment_percentage}")
            return False
        
        if config.max_slippage_percentage <= 0:
            logger.error(f"最大滑点必须大于0，当前值: {config.max_slippage_percentage}")
            return False
        
        if config.increment_percentage > config.max_slippage_percentage:
            logger.warning(
                f"滑点增加步长({config.increment_percentage}%) 大于最大滑点"
                f"({config.max_slippage_percentage}%)，可能导致一步到位"
            )
        
        return True
    
    def get_calculation_summary(
        self,
        original_slippage: float,
        current_slippage: float,
        config: SlippageRetryConfig
    ) -> str:
        """
        获取滑点计算摘要
        
        Args:
            original_slippage: 原始滑点百分比
            current_slippage: 当前滑点百分比
            config: 滑点重试配置
            
        Returns:
            str: 计算摘要
        """
        total_increase = current_slippage - original_slippage
        remaining_steps = self.calculate_slippage_steps_remaining(current_slippage, config)
        
        return (
            f"滑点调整摘要: {original_slippage}% → {current_slippage}% "
            f"(累计增加: {total_increase}%, 剩余调整步数: {remaining_steps})"
        ) 