import logging
import asyncio
import os
import traceback
from typing import Dict, List, Optional, Tuple
from dao.config_dao import Config<PERSON><PERSON>
from dao.telegram_users_dao import Telegram<PERSON>serDA<PERSON>
from dao.kol_wallet_activity_dao import KOLWalletActivityDAO
from dao.token_message_send_history_dao import TokenMessageSendHistoryDAO
from dao.token_dao import TokenDAO
from models.signal import Signal
from models.config import KolActivityConfig, SingleKolStrategyConfig, ApplicationConfig
from dao.signal_dao import SignalDAO
import time
from datetime import datetime, timedelta
from jinja2 import Template
from beanie import PydanticObjectId
from utils.common import check_kol_sell_ratio
from utils.basic_utils import get_current_time_dt
from utils.connectors.mongodb import get_db
from utils.message_sender.message_sender import MessageSender, TelegramMessageSender
from utils.spiders.solana.token_info import TokenInfo
from models.trade_record import TradeRecord, TradeStatus as ModelTradeStatus, TradeType as ModelTradeType
from dao.trade_record_dao import TradeRecordDAO
from utils.trading.solana.trade_interface import TradeInterface, TradeResult, TradeStatus as InterfaceTradeStatus, TradeType as InterfaceTradeType
from utils.trading.solana.gmgn_trade_service import GmgnTradeService, SOL_MINT_ADDRESS
from utils.trading import AutoTradeManager, get_auto_trade_manager
from models.trade_execution import TradeExecutionResult, TradeStatus


logger = logging.getLogger("MonitorKolActivityHandler")

_notification_locks = {} # Module-level dictionary to store locks


def html_escape(text: str) -> str:
    """
    转义HTML特殊字符以确保Telegram消息安全
    
    Args:
        text: 需要转义的文本
        
    Returns:
        转义后的安全文本
    """
    if not text:
        return text
    return (text.replace("&", "&amp;")
                .replace("<", "&lt;")
                .replace(">", "&gt;")
                .replace('"', "&quot;")
                .replace("'", "&#x27;"))


async def find_tokens_by_address(addresses: list[str]) -> list[Dict]:
    """
    通过API并发查找Token信息
    """
    async def fetch_token(address):
        logger.info(f"开始使用 TokenInfo 查找Token信息: {address}")
        token_info_getter = TokenInfo(address=address)
        token_data = await token_info_getter.get_token_info()
        logger.info(f"TokenInfo 查找Token信息完成 for: {address}, Data: {'Found' if token_data else 'Not Found'}")
        
        if token_data:
            token_data['address'] = address
            return token_data
        return None
    
    tokens_results = await asyncio.gather(*[fetch_token(address) for address in addresses])
    
    return [token for token in tokens_results if token]


async def _execute_kol_buy_strategy(
    strategy_params: SingleKolStrategyConfig
) -> Optional[List[Dict]]:
    """
    Core logic to find target tokens based on KOL activity using provided strategy parameters.
    """
    kol_activity_dao = KOLWalletActivityDAO()
    
    logger.info(
        f"Executing KOL buy strategy '{strategy_params.strategy_name}' with params: "
        f"tx_lookback_hours={strategy_params.transaction_lookback_hours}, min_amount_usd={strategy_params.transaction_min_amount}, "
        f"min_kol_count={strategy_params.kol_account_min_count}, kol_min_txs={strategy_params.kol_account_min_txs}, "
        f"kol_max_txs={strategy_params.kol_account_max_txs}, mint_lookback_hours={strategy_params.token_mint_lookback_hours}"
    )
    
    current_time_dt = get_current_time_dt()
    current_time = int(current_time_dt.timestamp())
    start_time = current_time - strategy_params.transaction_lookback_hours * 3600
    
    pipeline = [
        {
            '$match': {
                'timestamp': {'$lt': current_time, '$gt': start_time},
                'event_type': 'buy'
            }
        },
        {
            '$project': {
                'cost_usd': {'$toDouble': '$cost_usd'},
                'price_usd': {'$toDouble': '$price_usd'},
                'token_amount': {'$toDouble': '$token_amount'},
                'quote_amount': {'$toDouble': '$quote_amount'},
                'allFields': '$$ROOT'
            }
        },
        {
            '$replaceRoot': {
                'newRoot': {
                    '$mergeObjects': [
                        '$allFields', {
                            'cost_usd': '$cost_usd',
                            'price_usd': '$price_usd',
                            'token_amount': '$token_amount',
                            'quote_amount': '$quote_amount'
                        }
                    ]
                }
            }
        },
        {'$match': {'cost_usd': {'$gt': strategy_params.transaction_min_amount}}},
        {'$sort': {'timestamp': 1}},
        {
            '$group': {
                '_id': '$token.address',
                'records': {'$push': '$$ROOT'},
                'unique_wallets': {'$addToSet': '$wallet'}
            }
        },
        {
            '$lookup': {
                'from': 'kol_wallets',
                'localField': 'unique_wallets',
                'foreignField': 'wallet_address',
                'as': 'matched_kols'
            }
        },
        {
            '$addFields': {
                'kol_wallets': {
                    '$filter': {
                        'input': '$matched_kols',
                        'as': 'wallet',
                        'cond': {
                            '$and': [
                                {'$in': ['kol', '$$wallet.tags']},
                                {'$gte': ['$$wallet.txs', strategy_params.kol_account_min_txs]},
                                {'$lte': ['$$wallet.txs', strategy_params.kol_account_max_txs]}
                            ]
                        }
                    }
                }
            }
        },
        {'$addFields': {'kol_wallets_count': {'$size': '$kol_wallets'}}},
        {'$match': {'kol_wallets_count': {'$gte': strategy_params.kol_account_min_count}}}
    ]
    
    result = await kol_activity_dao.aggregate(pipeline)
    logger.info(f"Strategy '{strategy_params.strategy_name}': Found {len(result)} token addresses matching KOL activity criteria (aggregation result).")

    kol_wallets_map = {doc['_id']: [kol['wallet_address'] for kol in doc['kol_wallets']] for doc in result}
    kol_focused_tokens = [doc['_id'] for doc in result]
    
    if not kol_focused_tokens:
        return []
    
    token_dao = TokenDAO()
    tokens = await token_dao.find_by_addresses(kol_focused_tokens)
    token_addresses_in_db = [token["address"] for token in tokens]
    
    if len(token_addresses_in_db) != len(kol_focused_tokens):
        unfound_tokens_addresses = list(set(kol_focused_tokens) - set(token_addresses_in_db))
        logger.info(f"Strategy '{strategy_params.strategy_name}': Found {len(unfound_tokens_addresses)} token addresses not in DB: {unfound_tokens_addresses}")
        newly_fetched_tokens = await find_tokens_by_address(unfound_tokens_addresses)
        if newly_fetched_tokens:
            logger.info(f"Strategy '{strategy_params.strategy_name}': Fetched {len(newly_fetched_tokens)} tokens via API.")
            await token_dao.save_tokens(newly_fetched_tokens)
        tokens = await token_dao.find_by_addresses(kol_focused_tokens)
    
    unfound_first_mint_time_tokens_addresses = [
        token["address"] for token in tokens if not token.get("first_mint_time")
    ]
    if unfound_first_mint_time_tokens_addresses:
        logger.info(f"Strategy '{strategy_params.strategy_name}': Found {len(unfound_first_mint_time_tokens_addresses)} tokens missing first_mint_time: {unfound_first_mint_time_tokens_addresses}")
        refetched_for_mint_time = await find_tokens_by_address(unfound_first_mint_time_tokens_addresses)
        if refetched_for_mint_time:
            logger.info(f"Strategy '{strategy_params.strategy_name}': Re-fetched {len(refetched_for_mint_time)} tokens for mint time.")
            await token_dao.save_tokens(refetched_for_mint_time)
        tokens = await token_dao.find_by_addresses(kol_focused_tokens)

    target_tokens_with_kols = []
    current_time_for_mint_and_sell_check_dt = current_time_dt
    
    for t in tokens:
        mint_timestamp = 0
        first_mint_time_value = t.get("first_mint_time")
        if first_mint_time_value:
            if isinstance(first_mint_time_value, str):
                try:
                    iso_time = first_mint_time_value
                    if 'Z' in iso_time:
                        iso_time = iso_time.replace('Z', '+08:00')
                    elif '+' not in iso_time and '-' not in iso_time[-6:]:
                        iso_time += '+08:00'
                    mint_datetime = datetime.fromisoformat(iso_time)
                    mint_timestamp = mint_datetime.timestamp()
                except ValueError:
                    logger.warning(f"Strategy '{strategy_params.strategy_name}': Cannot parse timestamp: {first_mint_time_value} for token {t['address']}")
                    continue
            elif isinstance(first_mint_time_value, datetime):
                mint_timestamp = first_mint_time_value.timestamp()
            elif isinstance(first_mint_time_value, int):
                mint_timestamp = first_mint_time_value
            else:
                logger.warning(f"Strategy '{strategy_params.strategy_name}': Unknown first_mint_time type: {type(first_mint_time_value)} for token {t['address']}")
                continue
        else:
            logger.warning(f"Strategy '{strategy_params.strategy_name}': Token {t['address']} missing first_mint_time.")
            continue
        
        # Check mint time condition
        if mint_timestamp >= int(current_time_for_mint_and_sell_check_dt.timestamp()) - strategy_params.token_mint_lookback_hours * 3600:
            token_address = t['address']
            hit_kols_for_token = kol_wallets_map.get(token_address, [])

            # Crucial Check: Must have qualified KOLs for a buy signal
            if not hit_kols_for_token:
                logger.info(f"Strategy '{strategy_params.strategy_name}': Token {token_address} has no *qualified* hit KOLs after filtering according to strategy. Skipping as buy signal.")
                continue # Skip this token entirely if no qualified KOLs

            # Now, hit_kols_for_token is guaranteed to be non-empty. Proceed with sell check.
            logger.info(f"Strategy '{strategy_params.strategy_name}': Token {token_address} met initial buy criteria with {len(hit_kols_for_token)} KOLs. Checking immediate sell conditions...")
            
            sell_condition_met, calculated_ratio, selling_kols_count = await check_kol_sell_ratio(
                kol_activity_dao=kol_activity_dao,
                token_address=token_address,
                hit_kol_wallets=hit_kols_for_token,
                buy_signal_time_dt=current_time_for_mint_and_sell_check_dt,
                evaluation_time_dt=current_time_for_mint_and_sell_check_dt,
                sell_ratio_threshold=strategy_params.sell_kol_ratio,
                sell_activity_lookback_hours=strategy_params.transaction_lookback_hours,
                logger=logger
            )

            if sell_condition_met:
                logger.info(f"Strategy '{strategy_params.strategy_name}': Token {token_address} also meets sell condition (ratio: {calculated_ratio:.2f} >= {strategy_params.sell_kol_ratio}, selling KOLs: {selling_kols_count}). Skipping buy signal.")
                continue 
            else:
                logger.info(f"Strategy '{strategy_params.strategy_name}': Token {token_address} does NOT meet immediate sell condition (ratio: {calculated_ratio:.2f} < {strategy_params.sell_kol_ratio}). Proceeding with buy signal.")

            # This point is reached if:
            # 1. Mint time is OK
            # 2. hit_kols_for_token is NOT empty
            # 3. sell_condition_met is False
            if "_id" in t:
                del t["_id"]
            t['hit_kol_wallets'] = hit_kols_for_token 
            target_tokens_with_kols.append(t)
        # else: (mint time too old, already implicitly handled by the if)
    
    logger.info(f"Strategy '{strategy_params.strategy_name}': Found {len(target_tokens_with_kols)} tokens matching all criteria (including not meeting immediate sell conditions). Addresses: {[token['address'] for token in target_tokens_with_kols]}")
    return target_tokens_with_kols


async def filter_target_tokens() -> Optional[List[Dict]]:
    """
    Fetches the 'kol_activity' config, which may contain multiple buy strategies,
    and executes each active strategy, returning a combined list of found tokens
    with their associated triggering strategy configuration.
    """
    config_dao = ConfigDAO()
    config_doc = await config_dao.get_config("kol_activity")
    
    if not config_doc or not isinstance(config_doc.data, KolActivityConfig):
        logger.error("Failed to load 'kol_activity' config or it is not of type KolActivityConfig.")
        # Attempt to parse as old SingleKolStrategyConfig for backward compatibility during transition
        if config_doc and hasattr(config_doc, 'data'):
            try:
                logger.warning("Attempting to parse 'kol_activity' config as SingleKolStrategyConfig for backward compatibility.")
                single_strategy_params = SingleKolStrategyConfig(**config_doc.data.model_dump())
                if single_strategy_params.is_active:
                    found_tokens = await _execute_kol_buy_strategy(single_strategy_params)
                    results = []
                    if found_tokens:
                        for token_info in found_tokens:
                            results.append({'token_data': token_info, 'strategy_config_snapshot': single_strategy_params.model_dump()})
                    logger.info(f"Processed legacy 'kol_activity' as single strategy '{single_strategy_params.strategy_name}', found {len(results)} signals.")
                    return results
                else:
                    logger.info("Legacy 'kol_activity' (parsed as single strategy) is not active.")
                    return []
            except Exception as e:
                logger.error(f"Failed to parse 'kol_activity' config as SingleKolStrategyConfig during fallback: {e}. No strategies processed.")
                return []
        return [] # Return empty list if config is truly invalid or not KolActivityConfig

    all_triggered_signals_data = []
    active_strategies_count = 0

    for strategy_config in config_doc.data.buy_strategies:
        if not strategy_config.is_active:
            logger.info(f"Strategy '{strategy_config.strategy_name}' is not active, skipping.")
            continue
        active_strategies_count += 1
        
        try:
            found_tokens_for_strategy = await _execute_kol_buy_strategy(strategy_config)
            
            if found_tokens_for_strategy:
                for token_info in found_tokens_for_strategy:
                    all_triggered_signals_data.append({
                        'token_data': token_info,
                        'strategy_config_snapshot': strategy_config.model_dump()
                    })
        except Exception as e:
            logger.error(f"Error executing strategy '{strategy_config.strategy_name}': {e}", exc_info=True)
            # Continue to the next strategy even if one fails
            
    logger.info(f"Processed {active_strategies_count} active strategies. Found a total of {len(all_triggered_signals_data)} potential signals across all strategies.")
    return all_triggered_signals_data


async def validate(data: List[Dict]) -> bool:
    """
    验证数据
    """
    return True


async def send_message_to_channel(data: List[Dict]): # data is List of {'token_data': ..., 'strategy_config_snapshot': ...}
    """
    为符合条件的代币创建信号并发送消息给频道。
    严格遵循策略独立性原则：每个策略触发独立判断间隔并发送消息。
    信号优先原则：先创建信号，再尝试发送消息。
    """
    if not data:
        logger.info("没有需要处理的数据，send_message_to_channel 结束。")
        return True

    message_sender: MessageSender = TelegramMessageSender()
    signal_dao = SignalDAO()
    token_message_send_history_dao = TokenMessageSendHistoryDAO()
    trade_record_dao = TradeRecordDAO()
    config_dao = ConfigDAO()

    # 注意：交易失败通知现在由AutoTradeManager处理，不再需要在handler中获取admin_chat_ids

    # 修改消息模板以包含交易信息
    message_template_base = """📣 Purchase Alert for New Token:
Token Name: {{token.name}}
Token Symbol: {{token.symbol}}
Mint Address: {{token.address}}
Strategy: {{strategy_name}}
"""
    trade_success_template = message_template_base + """Trade Status: ✅ SUCCESS
Tx Hash: <a href="https://solscan.io/tx/{{trade_result.tx_hash}}">{{trade_result.tx_hash_short}}</a>
Amount Bought: {{trade_result.actual_amount_out_ui}} {{token.symbol}}
Amount Spent: {{trade_result.actual_amount_in_ui}} SOL
GMGN Bot: <a href="https://t.me/GMGN_sol_bot?start=i_e4bi1uZO_c_{{token.address}}">Trade on GMGN</a>"""
    
    trade_failed_template = message_template_base + """Trade Status: ❌ FAILED
Error: {{trade_result.error_message}}
GMGN Bot: <a href="https://t.me/GMGN_sol_bot?start=i_e4bi1uZO_c_{{token.address}}">Try Manual Trade on GMGN</a>"""

    trade_skipped_template = message_template_base + """Trade Status: ⚠️ SKIPPED (Auto-trade disabled by config)
GMGN Bot: <a href="https://t.me/GMGN_sol_bot?start=i_e4bi1uZO_c_{{token.address}}">Trade on GMGN</a>"""

    no_trade_info_template = message_template_base + """GMGN Bot: <a href="https://t.me/GMGN_sol_bot?start=i_e4bi1uZO_c_{{token.address}}">Trade on GMGN</a>"""

    telegram_users = await TelegramUserDAO().get_all_users()
    logger.info(f"找到{len(telegram_users)}个Telegram用户")
    if not telegram_users:
        logger.warning("没有Telegram用户可供通知，send_message_to_channel 结束。")
        return True

    logger.info(f"开始处理 {len(data)} 个策略触发事件")

    # 1. 直接迭代每个策略触发项
    for item in data:
        token_info = item['token_data']
        strategy_snapshot = item['strategy_config_snapshot']
        token_address = token_info['address']
        strategy_name = strategy_snapshot.get('strategy_name', 'unknown')
        same_token_notification_interval = strategy_snapshot.get('same_token_notification_interval', 60)
        current_time = get_current_time_dt()
        start_time_for_signal_check = current_time - timedelta(minutes=same_token_notification_interval)
        
        logger.info(f"处理 Token: {token_address}, 策略: {strategy_name}, 间隔: {same_token_notification_interval} 分钟")

        # 2. 检查 Signal Cooldown: 是否已存在此Token+策略的近期信号？
        try:
            existing_signal = await signal_dao.collection.find_one({
                "token_address": token_address,
                "signal_type": "kol_buy", 
                "trigger_timestamp": {"$gte": start_time_for_signal_check},
                "trigger_conditions.strategy_name": strategy_name
            })
            
            if existing_signal:
                logger.info(f"Token {token_address}, 策略 {strategy_name}: 已存在近期信号 (ID: {existing_signal['_id']})，跳过此触发事件")
                continue # 跳到下一个 item
            
            # 3. 如果不存在近期信号，则创建新 Signal
            logger.info(f"Token {token_address}, 策略 {strategy_name}: 无近期信号，准备创建新信号")
            new_signal = Signal(
                token_address=token_address,
                token_name=token_info.get('name'),
                token_symbol=token_info.get('symbol'),
                signal_type='kol_buy', 
                trigger_conditions=strategy_snapshot,
                hit_kol_wallets=token_info.get('hit_kol_wallets', []), 
                trigger_timestamp=current_time
            )
            
            await new_signal.save()
            str_signal_id = str(new_signal.id)
            logger.info(f"Token {token_address}, 策略 {strategy_name}: 创建新信号成功: {str_signal_id}")

            # --- 开始自动交易逻辑（使用AutoTradeManager）---
            execution_result: Optional[TradeExecutionResult] = None
            strategy_trading_overrides = {}  # 初始化变量以避免UnboundLocalError
            
            # 检查全局AutoTradeConfig是否启用自动交易
            try:
                auto_trade_manager = await get_auto_trade_manager()
                auto_trade_enabled = await auto_trade_manager.config_manager.is_enabled()
            except Exception as e:
                logger.error(f"[Signal:{str_signal_id}] 获取AutoTradeManager配置失败: {e}")
                auto_trade_enabled = False
            
            if auto_trade_enabled:
                logger.info(f"[Signal:{str_signal_id}] 策略 '{strategy_name}' 已启用自动交易。Token: {token_address}")
                
                try:
                    # auto_trade_manager 已在上面获取
                    
                    # 准备策略级别的交易参数覆盖（重新初始化以覆盖上面的空字典）
                    
                    # 从策略快照中提取交易参数（如果存在）
                    if strategy_snapshot.get('buy_amount_sol') is not None:
                        strategy_trading_overrides['buy_amount_sol'] = strategy_snapshot['buy_amount_sol']
                    
                    if strategy_snapshot.get('buy_slippage_percentage') is not None:
                        strategy_trading_overrides['buy_slippage_percentage'] = strategy_snapshot['buy_slippage_percentage']
                    
                    if strategy_snapshot.get('buy_priority_fee_sol') is not None:
                        strategy_trading_overrides['buy_priority_fee_sol'] = strategy_snapshot['buy_priority_fee_sol']
                    
                    # 获取买入金额
                    buy_amount = None
                    if 'buy_amount_sol' in strategy_trading_overrides:
                        buy_amount = strategy_trading_overrides['buy_amount_sol']
                    elif strategy_snapshot.get('buy_amount_sol'):
                        buy_amount = strategy_snapshot['buy_amount_sol']
                    
                    # 执行交易
                    execution_result = await auto_trade_manager.execute_trade(
                        trade_type="buy",
                        token_in_address=SOL_MINT_ADDRESS,
                        token_out_address=token_address,
                        amount=buy_amount,  # 添加amount参数
                        wallet_private_key_env_var=strategy_snapshot.get('wallet_private_key_env_var'),
                        wallet_address=strategy_snapshot.get('wallet_address'),
                        strategy_trading_overrides=strategy_trading_overrides,
                        signal_id=new_signal.id,
                        strategy_name=strategy_name
                    )
                    
                    logger.info(f"[Signal:{str_signal_id}] AutoTradeManager执行完成。状态: {execution_result.final_status.value}")
                    # 注意：交易失败通知现在由AutoTradeManager内部处理
                
                except Exception as e:
                    logger.error(f"[Signal:{str_signal_id}] AutoTradeManager执行异常: {e}", exc_info=True)
                    # 创建一个失败的执行结果
                    execution_result = TradeExecutionResult(
                        final_status=TradeStatus.FAILED,
                        successful_channel=None,
                        final_trade_record_id=None,
                        channel_attempts=[],
                        total_execution_time=0.0,
                        error_summary=f"AutoTradeManager exception: {str(e)}",
                        started_at=get_current_time_dt(),
                        completed_at=get_current_time_dt()
                    )
                    
                    # 注意：异常通知现在由AutoTradeManager内部处理
            else:
                logger.info(f"[Signal:{str_signal_id}] 策略 '{strategy_name}' 未启用自动交易，跳过。Token: {token_address}")
                # 创建一个跳过的执行结果
                execution_result = TradeExecutionResult(
                    final_status=TradeStatus.SKIPPED,
                    successful_channel=None,
                    final_trade_record_id=None,
                    channel_attempts=[],
                    total_execution_time=0.0,
                    error_summary="Auto-trade disabled by strategy configuration",
                    started_at=get_current_time_dt(),
                    completed_at=get_current_time_dt()
                )

            # 关联 TradeRecord 到 Signal (如果有交易记录ID)
            if execution_result and execution_result.final_trade_record_id:
                new_signal.trade_record_ids.append(execution_result.final_trade_record_id)
                await new_signal.save()
                logger.info(f"[Signal:{str_signal_id}] Associated TradeRecord {execution_result.final_trade_record_id} with Signal.")

            # --- 结束自动交易逻辑 ---

            # 4. 准备向用户发送（信号已创建，交易已尝试或跳过）
            # 根据交易执行结果选择模板
            message = ""
            template_render_data = {
                'token': token_info,
                'strategy_name': strategy_name,
                'trade_result': None # 默认值
            }

            if execution_result:
                # 从执行结果中获取交易信息
                successful_attempt = None
                if execution_result.channel_attempts:
                    successful_attempt = next(
                        (attempt for attempt in execution_result.channel_attempts 
                         if attempt.status == TradeStatus.SUCCESS), 
                        None
                    )
                
                template_render_data['trade_result'] = {
                    'tx_hash': successful_attempt.tx_hash if successful_attempt else None,
                    'tx_hash_short': (successful_attempt.tx_hash[:4] + "..." + successful_attempt.tx_hash[-4:] 
                                    if successful_attempt and successful_attempt.tx_hash else "N/A"),
                    'error_message': execution_result.error_summary,
                    'channel_used': execution_result.successful_channel,
                    'execution_time': execution_result.total_execution_time,
                    'channels_attempted': len(execution_result.channel_attempts)
                }

                # 处理交易金额显示 - 使用实际交易数据
                amount_in_ui = "N/A"
                amount_out_ui = "N/A"
                
                if successful_attempt:
                    # --- 处理 Amount Spent (花费的SOL数量) ---
                    if successful_attempt.actual_amount_in is not None:
                        try:
                            # SOL的decimals为9
                            sol_decimals = 9
                            raw_amount_in = float(successful_attempt.actual_amount_in)
                            # 如果actual_amount_in已经是UI格式（小数），直接使用
                            # 如果是lamports格式（大整数），需要转换
                            if raw_amount_in > 1000:  # 假设超过1000的是lamports格式
                                adjusted_amount_in = raw_amount_in / (10 ** sol_decimals)
                            else:
                                adjusted_amount_in = raw_amount_in
                            amount_in_ui = f"{adjusted_amount_in:.{sol_decimals}f} SOL"
                            logger.info(f"[Signal:{str_signal_id}] 使用实际SOL花费金额: {amount_in_ui}")
                        except (ValueError, TypeError) as e:
                            logger.error(f"[Signal:{str_signal_id}] 无法转换实际SOL花费金额: {successful_attempt.actual_amount_in}, Error: {e}")
                            amount_in_ui = f"{successful_attempt.actual_amount_in} (raw SOL)"
                    else:
                        # 回退到计划金额
                        planned_amount = "N/A"
                        if 'buy_amount_sol' in (strategy_trading_overrides or {}):
                            planned_amount = f"{strategy_trading_overrides['buy_amount_sol']:.4f}"
                        elif strategy_snapshot.get('buy_amount_sol'):
                            planned_amount = f"{strategy_snapshot['buy_amount_sol']:.4f}"
                        amount_in_ui = f"{planned_amount} SOL"
                        logger.warning(f"[Signal:{str_signal_id}] 实际SOL花费金额为空，使用计划金额: {amount_in_ui}")

                    # --- 处理 Amount Bought (购买到的Token数量) ---
                    if successful_attempt.actual_amount_out is not None:
                        try:
                            # 获取输出token的decimals
                            output_token_decimals = token_info.get('decimals')
                            raw_amount_out = float(successful_attempt.actual_amount_out)
                            
                            if output_token_decimals is not None:
                                # 如果actual_amount_out已经是UI格式（小数），直接使用
                                # 如果是最小单位格式（大整数），需要转换
                                decimals_int = int(output_token_decimals)
                                if decimals_int > 0 and raw_amount_out > (10 ** (decimals_int - 2)):  # 启发式判断是否为最小单位
                                    adjusted_amount_out = raw_amount_out / (10 ** decimals_int)
                                else:
                                    adjusted_amount_out = raw_amount_out
                                
                                # 动态确定显示的小数位数，最多显示6位小数
                                display_decimals = min(decimals_int, 6)
                                amount_out_ui = f"{adjusted_amount_out:.{display_decimals}f}"
                                logger.info(f"[Signal:{str_signal_id}] 使用实际Token购买数量: {amount_out_ui} (decimals: {decimals_int})")
                            else:
                                # 没有decimals信息，直接显示原始值
                                amount_out_ui = f"{raw_amount_out:.6f}"
                                logger.warning(f"[Signal:{str_signal_id}] Token {token_info.get('address')} 缺少decimals信息，显示原始值: {amount_out_ui}")
                        except (ValueError, TypeError) as e:
                            logger.error(f"[Signal:{str_signal_id}] 无法转换实际Token购买数量: {successful_attempt.actual_amount_out}, decimals: {token_info.get('decimals')}, Error: {e}")
                            amount_out_ui = f"{successful_attempt.actual_amount_out} (raw)"
                    else:
                        logger.warning(f"[Signal:{str_signal_id}] 实际Token购买数量为空")
                        amount_out_ui = f"N/A"
                        token_symbol = token_info.get('symbol', '')
                        if token_symbol: # 添加符号
                            amount_out_ui += f" {token_symbol.upper()}"
                else:
                    # 没有成功的尝试，使用计划金额作为回退
                    planned_amount = "N/A"
                    if 'buy_amount_sol' in (strategy_trading_overrides or {}):
                        planned_amount = f"{strategy_trading_overrides['buy_amount_sol']:.4f}"
                    elif strategy_snapshot.get('buy_amount_sol'):
                        planned_amount = f"{strategy_snapshot['buy_amount_sol']:.4f}"
                    amount_in_ui = f"{planned_amount} SOL"
                    amount_out_ui = "N/A"
                    token_symbol = token_info.get('symbol', '')
                    if token_symbol: # 添加符号
                        amount_out_ui += f" {token_symbol.upper()}"
                    logger.warning(f"[Signal:{str_signal_id}] 没有成功的交易尝试，使用计划金额: {amount_in_ui}")

                template_render_data['trade_result']['actual_amount_in_ui'] = f"{amount_in_ui}"
                template_render_data['trade_result']['actual_amount_out_ui'] = f"{amount_out_ui}"

                # 选择消息模板
                if execution_result.final_status == TradeStatus.SUCCESS:
                    template_to_use = Template(trade_success_template)
                elif execution_result.final_status == TradeStatus.FAILED:
                    template_to_use = Template(trade_failed_template)
                elif execution_result.final_status == TradeStatus.SKIPPED:
                    template_to_use = Template(trade_skipped_template)
                else:
                    logger.warning(f"[Signal:{str_signal_id}] Unexpected execution_result status: {execution_result.final_status}. Falling back to no_trade_info_template.")
                    template_to_use = Template(no_trade_info_template)
            else:
                # 如果没有执行结果，使用默认模板
                template_to_use = Template(no_trade_info_template)
            
            # HTML转义处理：在模板渲染前对可能包含特殊字符的字段进行转义
            try:
                # 转义Token信息中的危险字段
                if 'token' in template_render_data and template_render_data['token']:
                    token_data = template_render_data['token'].copy()
                    if 'name' in token_data and token_data['name']:
                        token_data['name'] = html_escape(str(token_data['name']))
                    if 'symbol' in token_data and token_data['symbol']:
                        token_data['symbol'] = html_escape(str(token_data['symbol']))
                    template_render_data['token'] = token_data
                
                # 转义策略名称
                if 'strategy_name' in template_render_data and template_render_data['strategy_name']:
                    template_render_data['strategy_name'] = html_escape(str(template_render_data['strategy_name']))
                
                # 转义交易结果中的错误消息（主要的bug来源）
                if 'trade_result' in template_render_data and template_render_data['trade_result']:
                    trade_result_data = template_render_data['trade_result'].copy()
                    if 'error_message' in trade_result_data and trade_result_data['error_message']:
                        original_error = trade_result_data['error_message']
                        escaped_error = html_escape(str(original_error))
                        trade_result_data['error_message'] = escaped_error
                        logger.info(f"[Signal:{str_signal_id}] HTML escaped error message: Original length={len(original_error)}, Escaped length={len(escaped_error)}")
                    template_render_data['trade_result'] = trade_result_data
                
                logger.info(f"[Signal:{str_signal_id}] HTML escape processing completed for template data")
            except Exception as e_escape:
                logger.error(f"[Signal:{str_signal_id}] Error during HTML escape processing: {e_escape}. Proceeding with original data.", exc_info=True)
            
            try:
                message = template_to_use.render(template_render_data)
            except Exception as e_render:
                logger.error(f"[Signal:{str_signal_id}] Failed to render Telegram message template: {e_render}. Falling back to basic message.", exc_info=True)
                # 后备消息
                message = f"📣 Purchase Alert for {token_info.get('symbol', token_address)}! Strategy: {strategy_name}. Mint: {token_address}. Please check system logs for trade details."
            
            # 5. 向所有用户发送消息并记录历史
            for user in telegram_users:
                chat_id = user['chat_id']
                
                # 移除用户级别的历史检查 - 直接发送
                logger.info(f"Token {token_address}, 策略 {strategy_name}: 准备向用户 {chat_id} 发送消息 (关联新信号: {str_signal_id})")
                send_success = await message_sender.send_message_to_user(message, chat_id)
                
                history_record = {
                    "token_address": token_address,
                    "chat_id": chat_id,
                    "channel": "telegram",
                    "signal_id": str_signal_id, 
                    "status": "sent" if send_success else "failed",
                    "created_at": current_time, # 使用信号创建时的时间
                    "strategy_name": strategy_name
                }
                
                if send_success:
                    logger.info(f"Token {token_address}, 策略 {strategy_name}: 发送消息给用户 {chat_id} 完成, 结果: {send_success}")
                else:
                    logger.error(f"Token {token_address}, 策略 {strategy_name}: 发送消息给用户 {chat_id} 失败")
                    
                try:
                    await token_message_send_history_dao.insert_one(history_record)
                    logger.info(f"Token {token_address}, 策略 {strategy_name}: 保存消息发送历史完成 ({history_record['status']}) for user {chat_id}")
                except Exception as e:
                    logger.error(f"Token {token_address}, 策略 {strategy_name}: 保存消息发送历史时出错 ({history_record['status']}) for user {chat_id}, Error: {e}", exc_info=True)

        except Exception as e:
            logger.error(f"处理 Token {token_address}, 策略 {strategy_name} 时发生意外错误: {e}", exc_info=True)
            # 发生意外错误时，也继续处理下一个item
            continue
            
    logger.info(f"所有 {len(data)} 个策略触发事件处理完毕")
    return True


async def main():
    """
    用于测试 send_message_to_channel 函数的主函数
    """
    from models import init_db
    await init_db() # 初始化数据库连接

    logger.info("开始测试 filter_target_tokens (which uses 'kol_activity' config with potentially multiple strategies)")
    
    # Ensure you have a config in DB of type 'kol_activity' that uses KolActivityConfig model
    # Example: KolActivityConfig(buy_strategies=[SingleKolStrategyConfig(strategy_name='aggressive', transaction_min_amount=500), 
    #                                           SingleKolStrategyConfig(strategy_name='conservative', transaction_min_amount=2000)])
    
    triggered_signals_data = await filter_target_tokens()
    if triggered_signals_data:
        logger.info(f"测试 filter_target_tokens 完成, 找到 {len(triggered_signals_data)} 个潜在信号。")
        await send_message_to_channel(triggered_signals_data)
    else:
        logger.info("测试 filter_target_tokens 完成, 未找到代币或配置错误/无有效策略。")


if __name__ == "__main__":
    import asyncio
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    # SOL_MINT_ADDRESS for trade_record, if not defined elsewhere
    SOL_MINT_ADDRESS = "So11111111111111111111111111111111111111112"
    asyncio.run(main())