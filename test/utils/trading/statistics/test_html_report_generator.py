"""
HTML报告生成器测试

测试HTML报告生成器的各项功能，包括：
- 基本报告生成
- Token链接功能
- 模板渲染
- CSS样式
"""

import pytest
from unittest.mock import MagicMock
from datetime import datetime
from typing import Dict, Any

from utils.trading.statistics.html_report_generator import HTMLReportGenerator
from utils.trading.statistics.models import (
    StatisticsResult, OverallStats, TokenStats, StrategyStats,
    ProfitRanking, LossRanking, StatisticsConfig
)


class TestHTMLReportGenerator:
    """HTML报告生成器测试类"""
    
    @pytest.fixture
    def html_generator(self) -> HTMLReportGenerator:
        """创建HTML报告生成器实例"""
        return HTMLReportGenerator()
    
    @pytest.fixture
    def sample_statistics(self) -> StatisticsResult:
        """创建示例统计数据"""
        return StatisticsResult(
            overall_stats=OverallStats(
                total_trades=10,
                total_win_rate=60.0,
                total_profit_rate=25.5,
                avg_profit_rate=12.3
            ),
            token_stats=[
                TokenStats(
                    token_address="7tSR3YqA...Nc3ahlpm",
                    trade_count=3,
                    win_rate=66.67,
                    avg_profit_rate=15.2
                ),
                TokenStats(
                    token_address="4dG7Qnii...X2awd3Sm",
                    trade_count=2,
                    win_rate=50.0,
                    avg_profit_rate=8.5
                )
            ],
            strategy_stats=[
                StrategyStats(
                    strategy_name="gmgn_smart_money",
                    trade_count=5,
                    win_rate=80.0,
                    avg_profit_rate=18.7
                )
            ],
            profit_rankings=[
                ProfitRanking(
                    signal_id="signal_001",
                    strategy_name="gmgn_smart_money",
                    token_address="7tSR3YqA...Nc3ahlpm",
                    profit_amount=1.25,
                    profit_rate=25.0,
                    buy_time=datetime.now(),
                    sell_time=datetime.now()
                )
            ],
            loss_rankings=[
                LossRanking(
                    signal_id="signal_002",
                    strategy_name="manual_trading",
                    token_address="4dG7Qnii...X2awd3Sm",
                    loss_amount=0.5,
                    loss_rate=10.0,
                    buy_time=datetime.now(),
                    sell_time=datetime.now()
                )
            ],
            generation_time=datetime.now(),
            data_range={}
        )
    
    @pytest.fixture
    def sample_charts(self) -> Dict[str, str]:
        """创建示例图表数据"""
        return {
            'overall_pie': '<div>Overall Pie Chart</div>',
            'token_performance': '<div>Token Performance Chart</div>',
            'strategy_comparison': '<div>Strategy Comparison Chart</div>'
        }
    
    def test_generate_report_with_token_links_enabled(self, html_generator, sample_statistics, sample_charts):
        """测试启用Token链接的报告生成"""
        config = StatisticsConfig(
            enable_token_links=True,
            token_link_base_url="https://gmgn.ai/sol/token/"
        )
        
        html_content = html_generator.generate_report(sample_statistics, sample_charts, config)
        
        # 验证HTML内容包含Token链接
        assert 'href="https://gmgn.ai/sol/token/7tSR3YqA...Nc3ahlpm"' in html_content
        assert 'href="https://gmgn.ai/sol/token/4dG7Qnii...X2awd3Sm"' in html_content
        assert 'target="_blank"' in html_content
        assert 'class="token-link"' in html_content
        assert '在GMGN上查看Token详情' in html_content
    
    def test_generate_report_with_token_links_disabled(self, html_generator, sample_statistics, sample_charts):
        """测试禁用Token链接的报告生成"""
        config = StatisticsConfig(
            enable_token_links=False
        )
        
        html_content = html_generator.generate_report(sample_statistics, sample_charts, config)
        
        # 验证HTML内容不包含Token链接
        assert 'href="https://gmgn.ai/sol/token/' not in html_content
        assert 'class="token-link"' not in html_content
        # 但应该包含省略的Token地址
        assert '7tSR3YqA...Nc3ahlpm' in html_content
        assert '4dG7Qnii...X2awd3Sm' in html_content
    
    def test_generate_report_with_custom_base_url(self, html_generator, sample_statistics, sample_charts):
        """测试自定义基础URL的Token链接"""
        config = StatisticsConfig(
            enable_token_links=True,
            token_link_base_url="https://custom.example.com/token/"
        )
        
        html_content = html_generator.generate_report(sample_statistics, sample_charts, config)
        
        # 验证使用了自定义的基础URL
        assert 'href="https://custom.example.com/token/7tSR3YqA...Nc3ahlpm"' in html_content
        assert 'href="https://custom.example.com/token/4dG7Qnii...X2awd3Sm"' in html_content
    
    def test_generate_report_without_config(self, html_generator, sample_statistics, sample_charts):
        """测试没有配置时的报告生成"""
        html_content = html_generator.generate_report(sample_statistics, sample_charts, None)
        
        # 验证没有配置时不生成链接
        assert 'href="https://gmgn.ai/sol/token/' not in html_content
        assert 'class="token-link"' not in html_content
        # 但应该包含省略的Token地址
        assert '7tSR3YqA...Nc3ahlpm' in html_content
    
    def test_token_link_helper_function(self, html_generator):
        """测试Token链接辅助函数"""
        # 测试启用链接
        config_enabled = StatisticsConfig(
            enable_token_links=True,
            token_link_base_url="https://gmgn.ai/sol/token/"
        )
        helper_enabled = html_generator._create_token_link_helper(config_enabled)
        result_enabled = helper_enabled("7tSR3YqANc3ahlpmTest")
        
        assert 'href="https://gmgn.ai/sol/token/7tSR3YqANc3ahlpmTest"' in result_enabled
        assert 'class="token-link"' in result_enabled
        assert 'target="_blank"' in result_enabled
        
        # 测试禁用链接
        config_disabled = StatisticsConfig(enable_token_links=False)
        helper_disabled = html_generator._create_token_link_helper(config_disabled)
        result_disabled = helper_disabled("7tSR3YqANc3ahlpmTest")
        
        assert 'href=' not in result_disabled
        assert '7tSR3YqA...hlpmTest' in result_disabled
    
    def test_css_styles_include_token_link_styles(self, html_generator):
        """测试CSS样式包含Token链接样式"""
        css_styles = html_generator._get_css_styles()
        
        # 验证包含Token链接相关的CSS样式
        assert '.token-link' in css_styles
        assert 'color: #3498db' in css_styles
        assert 'text-decoration: none' in css_styles
        assert ':hover' in css_styles
        assert ':visited' in css_styles
    
    def test_html_template_uses_token_link_helper(self, html_generator):
        """测试HTML模板正确使用token_link_helper"""
        template_str = html_generator._get_html_template()
        
        # 验证模板中使用了token_link_helper
        assert 'token_link_helper(token.token_address)|safe' in template_str
        assert 'token_link_helper(profit.token_address)|safe' in template_str
        assert 'token_link_helper(loss.token_address)|safe' in template_str 