# 滑点递增重试功能测试用例设计

**创建日期**: 2025-05-26  
**版本**: 0.1.1  
**基于**: AutoTradeManager v0.1.0  
**目标**: 全面的测试用例设计，确保滑点递增重试功能的可靠性和正确性

## 1. 测试范围概述

### 1.1 核心组件测试
- SlippageRetryConfig 配置模型验证
- SlippageCalculator 滑点计算器
- RetryDecisionEngine 重试决策引擎
- ParameterMerger 参数合并器  
- RetryContext 重试上下文

### 1.2 集成测试
- 端到端滑点递增重试流程
- 多层级配置覆盖测试
- 与现有AutoTradeManager集成

### 1.3 边界条件测试
- 滑点上限达到处理
- 配置错误处理
- 异常场景处理

## 2. 单元测试用例

### 2.1 SlippageRetryConfig 测试

#### 2.1.1 配置验证测试
**测试用例**: test_slippage_retry_config_validation
**前置条件**: 提供各种配置参数
**输入**: 
- 有效配置：enable_slippage_retry=True, slippage_increment_percentage=0.5, max_slippage_percentage=10.0
- 无效增长步长：slippage_increment_percentage=-0.5
- 无效最大滑点：max_slippage_percentage=60.0
**预期输出**: 
- 有效配置正常创建
- 无效配置抛出 ValueError

#### 2.1.2 配置有效性获取测试  
**测试用例**: test_get_effective_config
**前置条件**: 
- 创建买卖独立配置的SlippageRetryConfig
**输入**: 
- trade_type: "buy"
- trade_type: "sell"
**预期输出**: 
- 买入配置返回买入专用参数或通用参数
- 卖出配置返回卖出专用参数或通用参数

#### 2.1.3 默认配置测试
**测试用例**: test_default_slippage_retry_config
**前置条件**: 无
**输入**: 创建默认 SlippageRetryConfig()
**预期输出**: 
- enable_slippage_retry = False
- slippage_increment_percentage = 0.5
- max_slippage_percentage = 10.0

### 2.2 SlippageCalculator 测试

#### 2.2.1 正常滑点递增计算
**测试用例**: test_calculate_next_slippage_normal
**前置条件**: 初始化 SlippageCalculator
**输入**: 
- current_slippage: 1.0
- increment_percentage: 0.5
- max_slippage: 10.0
- trade_type: "buy"
**预期输出**: 
- new_slippage = 1.5
- exceeded_limit = False

#### 2.2.2 达到滑点上限计算
**测试用例**: test_calculate_next_slippage_limit_reached
**前置条件**: 初始化 SlippageCalculator
**输入**: 
- current_slippage: 9.8
- increment_percentage: 0.5
- max_slippage: 10.0
- trade_type: "sell"
**预期输出**: 
- new_slippage = 10.0
- exceeded_limit = True

#### 2.2.3 滑点计算边界测试
**测试用例**: test_slippage_calculation_edge_cases
**前置条件**: 初始化 SlippageCalculator
**输入**: 
- 接近上限：current=9.9, increment=0.5, max=10.0
- 正好达到上限：current=9.5, increment=0.5, max=10.0
- 零增量：increment=0.0
**预期输出**: 
- 接近上限正确处理
- 正好达到上限时exceeded_limit=True
- 零增量返回当前值

### 2.3 交易接口错误识别测试

#### 2.3.1 GMGN接口滑点错误识别
**测试用例**: test_gmgn_slippage_error_recognition
**前置条件**: 初始化 GmgnTradeInterface
**输入**: 
- GMGN滑点错误："slippage tolerance exceeded"
- GMGN滑点错误："price impact too high"
- 非滑点错误："insufficient funds"
- 非滑点错误："token not found"
**预期输出**: 
- GMGN滑点相关错误返回 True
- 非滑点错误返回 False

#### 2.3.2 Solana接口滑点错误识别
**测试用例**: test_solana_slippage_error_recognition
**前置条件**: 初始化 SolanaDirectTradeInterface
**输入**: 
- 错误代码模式：error_code="0x1771"
- 错误信息模式：error_message="slippage tolerance exceeded"
- Solana特有错误："insufficient lamports"
- 非滑点错误："invalid signature"
**预期输出**: 
- Solana滑点相关错误（代码和信息）返回 True
- 非滑点错误返回 False

#### 2.3.3 交易接口错误识别边界测试
**测试用例**: test_trade_interface_error_recognition_edge_cases
**前置条件**: 各交易接口实例
**输入**: 
- 大小写混合：各接口的错误信息
- 空字符串和None
- 错误代码为None的情况
**预期输出**: 
- 各接口正确处理边界情况
- 空值安全处理

### 2.4 RetryDecisionEngine 测试

#### 2.4.1 重试继续性测试
**测试用例**: test_should_continue_retry
**前置条件**: 
- 初始化 RetryDecisionEngine
**输入**: 
- attempt_count: 1, max_attempts: 3
- attempt_count: 3, max_attempts: 3  
**预期输出**: 
- 第一种情况：should_continue = True, reason = "继续重试"
- 第二种情况：should_continue = False, reason = "已达到最大重试次数 3"

#### 2.4.2 滑点递增决策测试
**测试用例**: test_should_increase_slippage_with_slippage_error
**前置条件**: 
- 初始化 RetryDecisionEngine
- 配置启用滑点递增
**输入**: 
- is_slippage_error: True (由交易接口判断)
- trade_type: "buy"
- current_buy_slippage: 1.0, current_sell_slippage: 1.0
- trading_params: TradingParams 启用滑点递增配置
**预期输出**: 
- should_retry = True
- new_buy_slippage = 1.5
- new_sell_slippage = 1.0 (保持不变)

#### 2.3.3 不应该调整滑点的情况测试
**测试用例**: test_should_not_increase_slippage_various_reasons
**前置条件**: 初始化 RetryDecisionEngine
**输入**: 
- 功能未启用：enable_slippage_retry=False
- 非滑点错误："insufficient funds"
- 已达滑点上限：current_slippage = max_slippage
**预期输出**: 
- should_retry = False
- 不同情况有相应的 reason 说明

#### 2.3.4 滑点达到上限但继续重试测试
**测试用例**: test_continue_retry_even_when_slippage_maxed
**前置条件**: 
- 初始化 RetryDecisionEngine
- 当前滑点已达上限
**输入**: 
- should_continue_retry: attempt_count=1, max_attempts=3
- should_increase_slippage: 滑点相关错误，但已达上限
**预期输出**: 
- should_continue = True（继续重试）
- should_increase = False（不调整滑点）
- 系统应该用当前滑点继续重试

#### 2.3.5 卖出交易滑点递增测试
**测试用例**: test_sell_trade_slippage_increase
**前置条件**: 
- 初始化 RetryDecisionEngine
- 配置卖出滑点重试
**输入**: 
- error_message: "price impact too high"
- trade_type: "sell"
- current_buy_slippage: 1.0, current_sell_slippage: 2.0
- 启用卖出滑点重试配置
**预期输出**: 
- should_retry = True
- new_buy_slippage = 1.0 (保持不变)
- new_sell_slippage = 2.5

#### 2.3.6 调整信息记录测试
**测试用例**: test_slippage_adjustment_info
**前置条件**: 
- 初始化 RetryDecisionEngine
- 配置重试参数
**输入**: 
- 滑点相关错误和有效重试配置
**预期输出**: 
- adjustment_info 包含 type, old_value, new_value, increment
- exceeded_limit 正确标记

### 2.4 ParameterMerger 测试

#### 2.4.1 三层级配置合并测试
**测试用例**: test_merge_three_level_slippage_config
**前置条件**: 
- 创建全局、渠道、策略三级配置
**输入**: 
- global_config: enable=False, increment=0.5, max=10.0
- channel_config: increment=0.8, max=15.0
- strategy_overrides: enable=True, max=8.0
**预期输出**: 
- 最终配置：enable=True (策略), increment=0.8 (渠道), max=8.0 (策略)

#### 2.4.2 部分配置覆盖测试
**测试用例**: test_partial_config_override
**前置条件**: 
- 创建不完整的覆盖配置
**输入**: 
- global_config: 完整配置
- channel_config: None
- strategy_overrides: 只有 enable_slippage_retry=True
**预期输出**: 
- 最终配置保留全局其他参数，只覆盖 enable_slippage_retry

#### 2.4.3 空配置处理测试
**测试用例**: test_merge_with_null_configs
**前置条件**: 
- 某些配置层级为 None
**输入**: 
- global_config: None
- channel_config: None
- strategy_overrides: 部分配置
**预期输出**: 
- 使用默认值 + 策略覆盖
- 不抛出异常

#### 2.4.4 交易参数合并测试
**测试用例**: test_merge_trading_params_with_slippage_retry
**前置条件**: 
- 创建包含滑点重试配置的交易参数
**输入**: 
- global_params: TradingParams 包含默认滑点重试配置
- strategy_overrides: 包含strategy_xxx滑点重试字段覆盖
**预期输出**: 
- 合并后参数正确包含滑点重试配置
- strategy_xxx字段正确映射到实际字段

### 2.5 RetryContext 测试

#### 2.5.1 调整记录添加测试
**测试用例**: test_add_slippage_adjustment
**前置条件**: 
- 初始化 RetryContext
**输入**: 
- SlippageAdjustment 买入调整记录
**预期输出**: 
- current_buy_slippage 更新
- slippage_adjustments 包含新记录
- is_slippage_retry = True

#### 2.5.2 调整摘要生成测试
**测试用例**: test_get_adjustment_summary
**前置条件**: 
- RetryContext 包含多次调整记录
**输入**: 
- 添加2次买入滑点调整记录
**预期输出**: 
- total_adjustments = 2
- buy_slippage_change 正确计算
- adjustment_history 包含详细记录

#### 2.5.3 买卖分别调整测试
**测试用例**: test_separate_buy_sell_adjustments
**前置条件**: 
- 初始化 RetryContext
**输入**: 
- 先添加买入调整，再添加卖出调整
**预期输出**: 
- 买入和卖出滑点分别正确更新
- 调整历史记录类型正确

## 3. 集成测试用例

### 3.1 端到端滑点重试测试

#### 3.1.1 成功的滑点递增重试
**测试用例**: test_end_to_end_successful_slippage_retry
**前置条件**: 
- 配置支持滑点重试的AutoTradeManager
- Mock交易服务：第一次滑点失败，第二次成功
**输入**: 
- 正常的买入交易请求
- 初始滑点1.0%，增量0.5%
**预期输出**: 
- 最终交易成功
- ChannelAttemptResult 记录滑点从1.0%增加到1.5%
- is_slippage_retry = True

#### 3.1.2 滑点达到上限后继续重试
**测试用例**: test_retry_continues_after_slippage_limit
**前置条件**: 
- 配置最大滑点5.0%，最大重试次数3次
- Mock交易服务：持续返回滑点错误
**输入**: 
- 交易请求，初始滑点4.5%，增量0.8%
**预期输出**: 
- 第1次重试：滑点增加到5.0%
- 第2、3次重试：保持5.0%滑点继续重试
- 最终因达到重试次数上限而失败
- 错误信息包含"已达到最大重试次数"

#### 3.1.3 重试次数限制测试
**测试用例**: test_retry_count_limit_without_slippage_increase
**前置条件**: 
- 配置：滑点递增功能禁用，最大重试次数3次
- Mock交易服务：持续返回滑点错误
**输入**: 
- 交易请求，enable_slippage_retry=False
**预期输出**: 
- 所有重试都使用相同滑点值
- 第3次重试后停止
- 没有任何滑点调整记录
- 最终失败原因为"已达到最大重试次数"

#### 3.1.4 多渠道环境下的滑点重试
**测试用例**: test_multi_channel_slippage_retry
**前置条件**: 
- 配置两个渠道：GMGN(优先级1)，Solana Direct(优先级2)
- 每个渠道都支持滑点重试
**输入**: 
- GMGN 渠道滑点重试失败
- Solana Direct 渠道滑点重试成功
**预期输出**: 
- 正确切换到第二个渠道
- 第二个渠道使用初始滑点(不继承第一个渠道的调整)
- 最终交易成功

### 3.2 配置层级测试

#### 3.2.1 策略覆盖渠道配置测试
**测试用例**: test_strategy_overrides_channel_slippage_config
**前置条件**: 
- 渠道配置：enable=False, increment=0.5
- 策略覆盖：enable=True, increment=1.0
**输入**: 
- 滑点相关的交易失败
**预期输出**: 
- 使用策略配置执行滑点重试
- 滑点增量为1.0%（策略值）

#### 3.2.2 全局默认配置测试
**测试用例**: test_global_default_slippage_config
**前置条件**: 
- 只配置全局滑点重试参数
- 策略和渠道无覆盖配置
**输入**: 
- 交易失败触发滑点重试
**预期输出**: 
- 使用全局配置参数
- 重试行为符合全局配置

### 3.3 错误处理集成测试

#### 3.3.1 非滑点错误不触发重试
**测试用例**: test_non_slippage_error_no_retry
**前置条件**: 
- 启用滑点重试配置
- Mock交易服务返回"insufficient funds"错误
**输入**: 
- 正常交易请求
**预期输出**: 
- 不执行滑点重试
- 直接返回失败结果
- slippage_adjustment_reason 说明不是滑点错误

#### 3.3.2 配置错误降级处理
**测试用例**: test_config_error_fallback
**前置条件**: 
- 滑点重试配置无效（如increment为负数）
**输入**: 
- 交易请求触发滑点重试
**预期输出**: 
- 使用默认配置或禁用滑点重试
- 系统继续正常运行
- 错误日志记录配置问题

### 3.4 性能和并发测试

#### 3.4.1 并发滑点重试安全性
**测试用例**: test_concurrent_slippage_retry_safety
**前置条件**: 
- 多个交易并发执行
- 每个交易都可能触发滑点重试
**输入**: 
- 10个并发交易请求
**预期输出**: 
- 每个交易的滑点调整独立
- 无资源竞争或状态混乱
- 所有交易正确处理

#### 3.4.2 滑点计算性能测试
**测试用例**: test_slippage_calculation_performance
**前置条件**: 
- 大量滑点计算请求
**输入**: 
- 1000次滑点递增计算
**预期输出**: 
- 单次计算耗时 < 1ms
- 内存使用稳定
- 无内存泄漏

## 4. 边界条件测试

### 4.1 极限值测试

#### 4.1.1 极小滑点增量测试
**测试用例**: test_minimal_slippage_increment
**前置条件**: 
- 配置极小增量：0.01%
**输入**: 
- 初始滑点1.0%，需要多次重试
**预期输出**: 
- 正确进行精细滑点调整
- 计算精度保持正确

#### 4.1.2 极大滑点上限测试
**测试用例**: test_maximum_slippage_limit
**前置条件**: 
- 配置极大上限：50.0%
**输入**: 
- 持续滑点重试直到上限
**预期输出**: 
- 正确到达50%上限
- 不超过配置的最大值

#### 4.1.3 零增量配置测试
**测试用例**: test_zero_increment_config
**前置条件**: 
- 尝试配置增量为0
**输入**: 
- SlippageRetryConfig(slippage_increment_percentage=0)
**预期输出**: 
- 配置验证失败
- 抛出 ValueError

### 4.2 异常场景测试

#### 4.2.1 无限循环保护测试
**测试用例**: test_infinite_retry_protection
**前置条件**: 
- 配置可能导致无限重试的参数
**输入**: 
- 持续的滑点错误
**预期输出**: 
- 受到重试次数限制保护
- 最终停止重试并返回失败

#### 4.2.2 配置冲突解决测试
**测试用例**: test_config_conflict_resolution
**前置条件**: 
- 配置冲突：max_slippage < default_slippage
**输入**: 
- 冲突的配置参数
**预期输出**: 
- 配置验证检测冲突
- 提供清晰的错误信息

## 5. 兼容性测试

### 5.1 向后兼容测试

#### 5.1.1 默认禁用测试
**测试用例**: test_slippage_retry_disabled_by_default
**前置条件**: 
- 使用默认配置的AutoTradeManager
**输入**: 
- 滑点相关的交易失败
**预期输出**: 
- 不执行滑点重试
- 行为与原有系统完全一致

#### 5.1.2 现有接口兼容测试
**测试用例**: test_existing_interface_compatibility
**前置条件**: 
- 使用原有的execute_trade接口
**输入**: 
- 不包含滑点重试配置的调用
**预期输出**: 
- 接口正常工作
- 返回结果格式保持兼容

### 5.2 渐进启用测试

#### 5.2.1 部分策略启用测试
**测试用例**: test_partial_strategy_enablement
**前置条件**: 
- 某些策略启用滑点重试，某些不启用
**输入**: 
- 两种策略的交易请求
**预期输出**: 
- 启用策略正确执行滑点重试
- 未启用策略保持原有行为

## 6. 数据记录测试

### 6.1 详细记录测试

#### 6.1.1 滑点调整历史记录
**测试用例**: test_slippage_adjustment_history_recording
**前置条件**: 
- 多次滑点调整的交易
**输入**: 
- 需要3次滑点调整才成功的交易
**预期输出**: 
- ChannelAttemptResult 包含完整调整历史
- 每次调整的时间戳、原因、数值都被记录

#### 6.1.2 统计信息准确性测试
**测试用例**: test_slippage_retry_statistics_accuracy
**前置条件**: 
- 执行多次交易，部分需要滑点重试
**输入**: 
- 10次交易，其中5次需要滑点重试，3次重试成功
**预期输出**: 
- slippage_retry_count = 5
- 重试成功率 = 60%
- 平均滑点增加量正确计算

## 7. Mock数据和测试工具

### 7.1 Mock配置数据

```python
# 标准滑点重试配置
STANDARD_SLIPPAGE_RETRY_CONFIG = SlippageRetryConfig(
    enable_slippage_retry=True,
    slippage_increment_percentage=0.5,
    max_slippage_percentage=10.0
)

# 买卖独立配置
SEPARATE_BUY_SELL_CONFIG = SlippageRetryConfig(
    enable_slippage_retry=True,
    enable_buy_slippage_retry=True,
    buy_slippage_increment_percentage=0.3,
    max_buy_slippage_percentage=8.0,
    enable_sell_slippage_retry=True,
    sell_slippage_increment_percentage=0.8,
    max_sell_slippage_percentage=15.0
)

# 禁用配置
DISABLED_SLIPPAGE_RETRY_CONFIG = SlippageRetryConfig(
    enable_slippage_retry=False
)
```

### 7.2 Mock错误消息

```python
# 滑点相关错误
SLIPPAGE_ERRORS = [
    "slippage tolerance exceeded",
    "insufficient output amount",
    "price impact too high", 
    "would result in 0.95 SOL, minimum 1.0 SOL",
    "Slippage Tolerance Exceeded"  # 大小写测试
]

# 非滑点错误  
NON_SLIPPAGE_ERRORS = [
    "insufficient funds",
    "token not found",
    "wallet not authorized",
    "network timeout",
    "invalid signature"
]
```

### 7.3 测试辅助函数

```python
def create_mock_trade_service_with_slippage_retry(
    fail_attempts: int = 1,
    success_on_retry: bool = True,
    error_type: str = "slippage"
) -> Mock:
    """创建支持滑点重试测试的Mock交易服务"""
    
def assert_slippage_adjustment_recorded(
    result: ChannelAttemptResult,
    expected_adjustments: int,
    expected_final_slippage: float
):
    """断言滑点调整被正确记录"""
    
def setup_multi_level_config(
    global_config: dict,
    channel_config: dict,
    strategy_config: dict
) -> tuple:
    """设置多层级配置用于测试"""
```

## 8. 测试执行策略

### 8.1 测试分组

- **核心功能测试**: 每次代码变更都执行
- **集成测试**: 每日执行
- **性能测试**: 每周执行
- **兼容性测试**: 发布前执行

### 8.2 测试环境

- **单元测试**: 完全Mock环境
- **集成测试**: 模拟交易环境
- **端到端测试**: 测试网络环境

### 8.3 覆盖率目标

- **单元测试覆盖率**: 95%以上
- **集成测试覆盖率**: 85%以上  
- **关键路径覆盖**: 100%

这个测试设计确保了滑点递增重试功能的全面测试覆盖，包括正常流程、异常处理、边界条件和性能要求。 