from typing import Optional, List, Dict, Any, Tuple
from decimal import Decimal
from beanie.odm.fields import PydanticObjectId
from dao.kol_strategy_score_dao import KOLStrategyScoreDAO
from dao.trade_score_log_dao import TradeScoreLogDAO
from dao.trade_record_dao import TradeRecordDAO
from dao.signal_dao import SignalDAO
from dao.config_dao import ConfigDAO
from dao.kol_wallet_dao import KOLWalletDAO
from models.config import KOLScoringConfig
from models.trade_record import TradeRecord, TradeType
from models.signal import Signal
from models.kol_wallet import KOLWallet
import logging

logger = logging.getLogger(__name__)

class KOLScoringService:
    """KOL打分服务"""
    
    def __init__(self):
        self.score_dao = KOLStrategyScoreDAO()
        self.log_dao = TradeScoreLogDAO()
        self.trade_dao = TradeRecordDAO()
        self.signal_dao = SignalDAO()
        self.config_dao = ConfigDAO()
        self.kol_wallet_dao = KOLWalletDAO()
        
        # 默认配置
        self.default_config = KOLScoringConfig()
    
    async def get_scoring_config(self) -> KOLScoringConfig:
        """获取KOL打分配置"""
        try:
            config = await self.config_dao.get_config("kol_scoring")
            if config and isinstance(config.data, KOLScoringConfig):
                return config.data
            else:
                logger.warning("未找到KOL打分配置，使用默认配置")
                return self.default_config
        except Exception as e:
            logger.error(f"获取KOL打分配置失败: {str(e)}")
            return self.default_config
    
    def calculate_pnl(self, buy_trade: TradeRecord, sell_trade: TradeRecord) -> float:
        """
        计算已完成交易对的盈亏 (PnL)。

        Args:
            buy_trade: 买入交易记录。
            sell_trade: 卖出交易记录。

        Returns:
            PnL金额 (以计价货币，如SOL或USDC计)。
        """
        if not buy_trade or not sell_trade:
            logger.warning("计算PnL失败：买入或卖出交易记录缺失")
            return 0.0

        # 根据需求 FR-KOLSC-005，PnL基于实际交易金额
        # 买入成本: buy_trade_record.token_in_actual_amount (实际SOL花费)
        # 卖出收入: sell_trade_record.token_out_actual_amount (实际SOL收入)

        buy_cost = buy_trade.token_in_actual_amount
        sell_revenue = sell_trade.token_out_actual_amount

        if buy_cost is None or sell_revenue is None:
            logger.warning(
                f"计算PnL失败：交易 {buy_trade.id} 或 {sell_trade.id} 的实际金额缺失。 "
                f"Buy_cost: {buy_cost}, Sell_revenue: {sell_revenue}"
            )
            # 如果实际金额缺失，应如何处理？当前返回0.0，但可能需要更复杂的错误处理或标记
            # 或者依赖上游确保这两个字段总是被填充
            return 0.0

        pnl = float(sell_revenue) - float(buy_cost)
        logger.info(f"交易对 PnL 计算完成: Buy ID {buy_trade.id}, Sell ID {sell_trade.id}, PnL: {pnl:.4f}")
        return pnl
    
    def calculate_score_changes(
        self, 
        pnl: float, 
        strategy_name: str, 
        config: KOLScoringConfig
    ) -> Tuple[float, float]:
        """
        根据PnL和策略配置计算分数变化
        
        Args:
            pnl: 盈亏金额（保留用于记录和分析）
            strategy_name: 策略名称
            config: 打分配置
            
        Returns:
            (positive_score_change, negative_score_change) 元组
        """
        try:
            # 获取策略特定参数，如果没有则使用全局默认值
            strategy_params = config.strategy_specific_params.get(strategy_name)
            
            if pnl > 0:
                # 盈利时给加分（固定分数）
                if strategy_params and strategy_params.positive_score is not None:
                    positive_score = strategy_params.positive_score
                else:
                    positive_score = config.default_positive_score
                
                return positive_score, 0.0
            
            elif pnl < 0:
                # 亏损时给扣分（固定分数，不再按PnL比例计算）
                if strategy_params and strategy_params.negative_score_multiplier is not None:
                    # 使用策略特定的固定负向分数
                    negative_score = strategy_params.negative_score_multiplier * -1
                else:
                    # 使用全局默认的固定负向分数
                    negative_score = config.default_negative_score_multiplier * -1
                
                return 0.0, negative_score
            
            else:
                # PnL为0，不变分数
                return 0.0, 0.0
                
        except Exception as e:
            logger.error(f"计算分数变化失败: pnl={pnl}, strategy={strategy_name}, error={str(e)}")
            return 0.0, 0.0
    
    async def score_individual_kol_combination(
        self,
        buy_trade: TradeRecord,
        sell_trade: TradeRecord,
        kol_wallet_address: str,
        strategy_name: str,
        pnl: float,
        config: KOLScoringConfig,
        force_rescore: bool = False
    ) -> Dict[str, Any]:
        """
        为单个 (交易对, KOL, 策略) 组合打分。
        假定此组合需要打分 (除非 force_rescore 为 True 时会重新检查日志)。
        """
        kol_detail_result = {
            "success": False,
            "kol_wallet_address": kol_wallet_address,
            "strategy_name": strategy_name,
            "pnl_at_scoring": pnl,
            "positive_score_applied": 0.0,
            "negative_score_applied": 0.0,
            "log_created": False,
            "score_updated": False,
            "error": None
        }

        try:
            # 0. 验证交易对本身是否有效 (例如，类型、顺序、同一KOL等)
            # _validate_trade_pair 移到服务层内部，因为它强依赖于服务内的具体逻辑
            validation_passed, validation_message = self._validate_trade_pair(buy_trade, sell_trade, kol_wallet_address, strategy_name)
            if not validation_passed:
                kol_detail_result["error"] = validation_message
                return kol_detail_result # success 仍然是 False (初始值)

            # 1. 如果强制重新打分，则检查日志；否则，跳过检查（因为工作流已保证这是新组合）
            if force_rescore:
                already_scored = await self.log_dao.has_log_entry_existed(
                    str(buy_trade.id), str(sell_trade.id), kol_wallet_address, strategy_name
                )
                if already_scored:
                    logger.info(f"强制重打分模式：跳过已打分的组合: kol={kol_wallet_address}, strategy={strategy_name}")
                    kol_detail_result["error"] = "Combination already scored (force_rescore)"
                    # 即使已打分也返回success=False，因为没有执行新的打分操作
                    return kol_detail_result

            # 2. 计算分数变化
            positive_change, negative_change = self.calculate_score_changes(pnl, strategy_name, config)
            kol_detail_result["positive_score_applied"] = positive_change
            kol_detail_result["negative_score_applied"] = negative_change

            # 3. 更新KOL策略总分
            # KOLStrategyScoreDAO.update_score 需要 kol_wallet_address (str)
            score_updated_success = await self.score_dao.update_score(
                kol_wallet_address, strategy_name, positive_change, negative_change
            )
            kol_detail_result["score_updated"] = score_updated_success

            if score_updated_success:
                # 4. 创建打分日志
                # TradeScoreLogDAO.create_scoring_log 需要 kol_wallet_address (str)
                scoring_params_snapshot = {
                    "strategy_config": config.strategy_specific_params.get(strategy_name, {}).model_dump() if config.strategy_specific_params.get(strategy_name) else {},
                    "default_positive_score": config.default_positive_score,
                    "default_negative_score_multiplier": config.default_negative_score_multiplier,
                    "pnl_used_for_scoring": pnl # 使用传入的PnL
                }
                
                log_entry = await self.log_dao.create_scoring_log(
                    buy_trade_record_id=str(buy_trade.id),
                    sell_trade_record_id=str(sell_trade.id),
                    kol_wallet_address=kol_wallet_address,
                    strategy_name=strategy_name,
                    pnl_at_scoring=pnl, # 使用传入的PnL
                    positive_score_applied=positive_change,
                    negative_score_applied=negative_change,
                    scoring_params_snapshot=scoring_params_snapshot
                )
                
                if log_entry:
                    kol_detail_result["log_created"] = True
                    kol_detail_result["success"] = True # 整体成功当且仅当分数更新且日志创建
                    logger.info(f"成功为KOL打分并记录日志: kol={kol_wallet_address}, strategy={strategy_name}, pnl={pnl:.4f}")
                else:
                    kol_detail_result["error"] = "Failed to create trade score log"
                    logger.error(f"创建打分日志失败: kol={kol_wallet_address}, strategy={strategy_name}")
                    # 注意：如果日志创建失败，但分数已更新，这可能是一个需要关注的数据不一致状态。
                    # 当前设计下，如果score_updated_success为True而log_entry为None，success仍然为False。
            else:
                kol_detail_result["error"] = "Failed to update KOL strategy score"
                logger.error(f"更新KOL分数失败: kol={kol_wallet_address}, strategy={strategy_name}")

            return kol_detail_result

        except Exception as e:
            logger.error(f"为KOL组合打分时发生异常: kol={kol_wallet_address}, strategy={strategy_name}, error={str(e)}")
            kol_detail_result["error"] = str(e)
            return kol_detail_result

    async def score_trade_pair(
        self, 
        buy_trade_id: str,
        sell_trade_id: str,
        force_rescore: bool = False
    ) -> Dict[str, Any]:
        """
        (过渡方法) 为一对买卖交易记录关联的所有KOL打分。
        此方法将逐步被取代，工作流应直接调用 score_individual_kol_combination (或其包装器)。
        主要修改：移除了内部的 has_log_entry_existed 检查，因为它将由工作流聚合查询处理。
        """
        overall_result = {
            "success": False,
            "buy_trade_id": buy_trade_id,
            "sell_trade_id": sell_trade_id,
            "kols_processed_count": 0,
            "kols_successfully_scored_count": 0,
            "error": None,
            "details": []
        }
        
        try:
            # 1. 获取交易记录 (从ID)
            buy_trade = await self.trade_dao.get_by_id(buy_trade_id)
            sell_trade = await self.trade_dao.get_by_id(sell_trade_id)
            
            if not buy_trade or not sell_trade:
                overall_result["error"] = f"交易记录不存在: buy_id={buy_trade_id}, sell_id={sell_trade_id}"
                logger.error(overall_result["error"])
                return overall_result
            
            # 2. 计算PnL (一次性)
            pnl = self.calculate_pnl(buy_trade, sell_trade)
            
            # 3. 获取打分配置 (一次性)
            config = await self.get_scoring_config()
            
            # 4. 获取相关KOL列表 (仍然需要此逻辑，直到工作流完全重构)
            # 注意: _get_kols_for_trade_pair 返回的是KOL钱包地址列表 (List[str])
            kol_wallet_addresses = await self._get_kols_for_trade_pair(buy_trade, sell_trade)
            overall_result["kols_processed_count"] = len(kol_wallet_addresses)
            
            if not kol_wallet_addresses:
                overall_result["error"] = f"未找到交易对 ({buy_trade_id}, {sell_trade_id}) 相关的KOL钱包"
                logger.warning(overall_result["error"])
                # 如果没有KOL，可以认为处理完成但无事可做，不一定是整体错误
                # overall_result["success"] = True # 或者保持False，取决于如何定义"成功"
                return overall_result
            
            # 5. 为每个KOL调用新的打分方法
            for kol_addr_str in kol_wallet_addresses:
                kol_wallet_obj = await self.kol_wallet_dao.find_by_wallet_address(kol_addr_str)
                if not kol_wallet_obj:
                    logger.warning(f"无法为地址 {kol_addr_str} 找到KOLWallet对象，跳过打分。")
                    detail = {
                        "success": False, "kol_wallet_address": kol_addr_str, 
                        "error": "KOLWallet object not found", "strategy_name": buy_trade.strategy_name or "default"
                    }
                    overall_result["details"].append(detail)
                    continue

                strategy_name = buy_trade.strategy_name or "default"
                
                # 调用新的、更细粒度的方法
                # 注意：这里传递了已经获取的 buy_trade, sell_trade, kol_wallet_obj, pnl, config
                # 避免了在 score_individual_kol_combination 内部重复获取这些信息
                kol_score_result = await self.score_individual_kol_combination(
                    buy_trade=buy_trade,
                    sell_trade=sell_trade,
                    kol_wallet_address=kol_addr_str,
                    strategy_name=strategy_name,
                    pnl=pnl,
                    config=config,
                    force_rescore=force_rescore # force_rescore 会在 score_individual_kol_combination 中处理日志检查
                )
                overall_result["details"].append(kol_score_result)
                
                if kol_score_result.get("success"):
                    overall_result["kols_successfully_scored_count"] += 1
            
            if overall_result["kols_successfully_scored_count"] > 0:
                overall_result["success"] = True # 至少有一个KOL成功打分，则整体视为部分或完全成功
            
            if overall_result["kols_successfully_scored_count"] < overall_result["kols_processed_count"] and not overall_result["success"]:
                 # 如果有处理的KOL，但没有一个成功，且整体未标记为成功，则设置一个概括性错误
                overall_result["error"] = "One or more KOLs could not be scored successfully for the trade pair."

            return overall_result
            
        except Exception as e:
            logger.error(f"为交易对 ({buy_trade_id}, {sell_trade_id}) 打分过程中发生严重异常: {str(e)}")
            overall_result["error"] = str(e)
            return overall_result
    
    def _validate_trade_pair(self, buy_trade: TradeRecord, sell_trade: TradeRecord, kol_wallet_address: str, strategy_name: str) -> Tuple[bool, str]:
        """
        验证买卖交易对的有效性，现在包含KOL和策略上下文。
        
        Args:
            buy_trade: 买入交易
            sell_trade: 卖出交易
            kol_wallet_address: KOL钱包地址
            strategy_name: 策略名称
            
        Returns:
            (bool, str) 元组，表示是否有效以及相关的消息
        """
        try:
            buy_action = getattr(buy_trade, 'trade_type', getattr(buy_trade, 'action', None))
            sell_action = getattr(sell_trade, 'trade_type', getattr(sell_trade, 'action', None))
            
            from models.trade_record import TradeType # Local import to avoid circular dependency if any at module level
            
            if hasattr(buy_action, 'value'):
                if buy_action != TradeType.BUY:
                    msg = f"买入交易动作不正确: buy_action={buy_action}"
                    logger.warning(msg)
                    return False, msg
            else:
                buy_action_str = str(buy_action).lower() if buy_action else None
                if buy_action_str != "buy":
                    msg = f"买入交易动作不正确: buy_action={buy_action}"
                    logger.warning(msg)
                    return False, msg
                    
            if hasattr(sell_action, 'value'):
                if sell_action != TradeType.SELL:
                    msg = f"卖出交易动作不正确: sell_action={sell_action}"
                    logger.warning(msg)
                    return False, msg
            else:
                sell_action_str = str(sell_action).lower() if sell_action else None
                if sell_action_str != "sell":
                    msg = f"卖出交易动作不正确: sell_action={sell_action}"
                    logger.warning(msg)
                    return False, msg
            
            # 简化代币地址检查逻辑 (确保测试能通过类型检查)
            # 实际项目中这里的逻辑需要更严谨
            buy_token_out_addr = getattr(buy_trade, 'token_out_address', "UNKNOWN_BUY_TOKEN_OUT")
            sell_token_in_addr = getattr(sell_trade, 'token_in_address', "UNKNOWN_SELL_TOKEN_IN")

            if buy_token_out_addr != sell_token_in_addr:
                 # 为了让现有测试用例的mock trade可以通过，暂时不在这里做严格的 token 匹配失败
                 msg = f"交易的标的代币地址不匹配: buy_trade.token_out_address={buy_token_out_addr}, sell_trade.token_in_address={sell_token_in_addr}"
                 logger.warning(msg)
                 return False, msg
                 # pass # 允许测试用例中的mock数据

            # 简化策略名称检查逻辑 (确保测试能通过类型检查)
            # 实际项目中这里的逻辑需要更严谨
            # The strategy_name argument is the reference strategy for this scoring context.
            if not strategy_name:
                msg = "策略名称缺失，无法进行验证。"
                logger.warning(msg)
                return False, msg

            if buy_trade.strategy_name != strategy_name:
                msg = f"买入交易策略 '{buy_trade.strategy_name}' 与预期策略 '{strategy_name}' 不匹配。"
                logger.warning(msg)
                return False, msg
            
            if sell_trade.strategy_name != strategy_name:
                # 为了让现有测试用例的mock trade可以通过，暂时不在这里做严格的 strategy 匹配失败
                msg = f"卖出交易策略 '{sell_trade.strategy_name}' 与预期策略 '{strategy_name}' 不匹配。"
                logger.warning(msg)
                return False, msg
                # pass # 允许测试用例中的mock数据
            
            if buy_trade.created_at >= sell_trade.created_at:
                msg = f"时间顺序错误: buy_time={buy_trade.created_at}, sell_time={sell_trade.created_at}"
                logger.warning(msg)
                return False, msg
            
            return True, "Validation successful"
            
        except Exception as e:
            error_msg = f"验证交易对时发生异常: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    async def _get_kols_for_trade_pair(self, buy_trade: TradeRecord, sell_trade: TradeRecord) -> List[str]:
        """
        (暂时保留) 获取与指定买卖交易对关联的KOL钱包地址列表。
        未来此逻辑应移至工作流的聚合查询中。
        """
        kol_wallet_addresses = set()

        # 从买入信号获取KOL
        if buy_trade.signal_id:
            buy_signal = await self.signal_dao.get_signal_by_id(str(buy_trade.signal_id))
            if buy_signal and buy_signal.hit_kol_wallets:
                for kol_addr in buy_signal.hit_kol_wallets:
                    kol_wallet_addresses.add(kol_addr)
        
        # 从卖出信号获取KOL (如果卖出信号也可能直接关联KOL)
        # 注意：通常KOL关联是在买入时确定的，卖出信号的KOL可能与买入时不同或不存在
        if sell_trade.signal_id:
            sell_signal = await self.signal_dao.get_signal_by_id(str(sell_trade.signal_id))
            if sell_signal and sell_signal.hit_kol_wallets:
                 for kol_addr in sell_signal.hit_kol_wallets:
                    kol_wallet_addresses.add(kol_addr)
        
        if not kol_wallet_addresses:
            logger.info(f"未找到交易对 ({buy_trade.id}, {sell_trade.id}) 直接关联的KOL信号。")

        # 根据需求 FR-KOLSC-004, 如果一个交易对的买入信号或卖出信号关联了某个KOL
        # 此处已覆盖。
        
        unique_kol_list = list(kol_wallet_addresses)
        if unique_kol_list:
            logger.info(f"找到与交易对 ({buy_trade.id}, {sell_trade.id}) 关联的KOL钱包: {unique_kol_list}")
        return unique_kol_list

    async def get_kol_ranking(self, strategy_name: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取指定策略的KOL排名
        
        Args:
            strategy_name: 策略名称
            limit: 返回数量限制
            
        Returns:
            KOL排名列表
        """
        try:
            scores = await self.score_dao.get_top_kols_by_strategy(strategy_name, limit)
            ranking = []
            for score_entry in scores:
                if score_entry.kol_wallet and score_entry.kol_wallet.resolved: # 确保Link已解析
                    kol_info = score_entry.kol_wallet.resolved
                    ranking.append({
                        "kol_wallet_address": kol_info.wallet_address,
                        "kol_name": kol_info.name,
                        "kol_avatar_url": kol_info.avatar_url,
                        "strategy_name": score_entry.strategy_name,
                        "total_positive_score": score_entry.total_positive_score,
                        "total_negative_score": score_entry.total_negative_score,
                        "net_score": score_entry.total_positive_score + score_entry.total_negative_score, # 简单净分
                        "last_scored_at": score_entry.last_scored_at
                    })
            return ranking
        except Exception as e:
            logger.error(f"获取KOL排名失败: strategy={strategy_name}, error={str(e)}")
            return []
    
    async def get_kol_score_summary(self, kol_wallet_address: str) -> Dict[str, Any]:
        """
        获取指定KOL的分数摘要
        
        Args:
            kol_wallet_address: KOL钱包地址
            
        Returns:
            KOL分数摘要字典
        """
        summary = {
            "kol_wallet_address": kol_wallet_address,
            "strategies": [],
            "overall_positive_score": 0.0,
            "overall_negative_score": 0.0,
            "overall_net_score": 0.0
        }
        try:
            kol_wallet = await self.kol_wallet_dao.find_by_wallet_address(kol_wallet_address)
            if not kol_wallet:
                logger.warning(f"获取KOL分数摘要失败：未找到KOL钱包 {kol_wallet_address}")
                return summary # 或者抛出异常

            scores = await self.score_dao.get_scores_by_kol_wallet_id(kol_wallet.id) # Pass ObjectId
            for score_entry in scores:
                net_score = score_entry.total_positive_score + score_entry.total_negative_score
                summary["strategies"].append({
                    "strategy_name": score_entry.strategy_name,
                    "total_positive_score": score_entry.total_positive_score,
                    "total_negative_score": score_entry.total_negative_score,
                    "net_score": net_score,
                    "last_scored_at": score_entry.last_scored_at
                })
                summary["overall_positive_score"] += score_entry.total_positive_score
                summary["overall_negative_score"] += score_entry.total_negative_score
            
            summary["overall_net_score"] = summary["overall_positive_score"] + summary["overall_negative_score"]
            return summary
        except Exception as e:
            logger.error(f"获取KOL分数摘要失败: kol={kol_wallet_address}, error={str(e)}")
            return summary # 或者返回包含错误信息的字典

# (可能还有其他方法)
# 注意：PydanticObjectId 通常在模型定义中使用，在服务层或DAO层的方法参数中，
# 通常接收 str 类型的ID，然后在DAO内部转换为 PydanticObjectId 进行查询，
# 或者直接使用 str ID 进行查询（如果Beanie支持）。
# 为了清晰和类型安全，如果方法期望ObjectId，应明确注解。
# 但当前 score_trade_pair 接收 str ID，内部 get_by_id 也应能处理 str。
# 新的 score_individual_kol_combination 为了减少转换，可以直接接收 PydanticObjectId。
# 不，为了与DAO层 get_by_id(str) 保持一致，新方法也用str ID，然后在内部获取对象。
# 再次思考：为了性能，如果上游（如工作流的聚合查询）已经能提供 TradeRecord 和 KOLWallet 对象，
# 直接传递对象给 score_individual_kol_combination 会更好，避免重复DB查询。
# 我将修改 score_individual_kol_combination 使其接收对象。
# score_trade_pair 则负责从ID获取对象，再调用 score_individual_kol_combination。 