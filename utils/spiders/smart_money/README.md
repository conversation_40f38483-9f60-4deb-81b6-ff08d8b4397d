# 智能爬虫工具

这是一个具有自动代理切换和重试功能的爬虫工具，特别适用于处理请求限制（429状态码）的情况。

## 主要功能

- 自动检测并处理 429 状态码（请求过多）
- 支持代理自动切换
- 可配置的重试次数和重试间隔
- 支持常见的 HTTP 请求方法（GET, POST, PUT, DELETE）
- 异常捕获和自动重试

## 安装依赖

```bash
pip install curl-cffi
```

## 基本用法

### 创建爬虫实例

```python
from utils.spiders.smart_money import BasicSpider

# 使用默认参数创建爬虫实例（最大重试3次，间隔1秒）
spider = BasicSpider()

# 自定义重试参数创建爬虫实例
spider = BasicSpider(max_retries=5, retry_interval=2.5)
```

### 发送请求

```python
# 发送 GET 请求
response = spider.get("https://example.com/api/data")

# 发送 POST 请求
response = spider.post("https://example.com/api/submit", json={"key": "value"})

# 发送 PUT 请求
response = spider.put("https://example.com/api/update", json={"status": "updated"})

# 发送 DELETE 请求
response = spider.delete("https://example.com/api/remove/123")
```

### 自定义单次请求的重试间隔

```python
# 为单次请求指定不同的重试间隔
response = spider.get("https://example.com/api/data", retry_interval=0.5)
```

### 使用高级选项

```python
# 使用自定义请求头
headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"
}
response = spider.get("https://example.com/api/data", headers=headers)

# 发送带参数的请求
params = {"page": 1, "limit": 10}
response = spider.get("https://example.com/api/search", params=params)

# 发送带JSON数据的POST请求
data = {"username": "test", "password": "password123"}
response = spider.post("https://example.com/api/login", json=data)
```

## 代理配置

代理配置在 `utils/spiders/smart_money/__init__.py` 文件中的 `PROXYs` 列表中定义：

```python
PROXYs = [{
    'url': '37.49.150.220:49227',
    'user': 'VvRgl7q1C5W6TKt',
    'pass': 'FVXDOYItgkR3yf7'
}]
```

您可以添加更多代理到此列表，系统会在遇到 429 状态码时随机选择一个代理进行重试。

## 高级用法

### 直接使用 request_with_retry 方法

```python
# 使用自定义方法和参数
response = spider.request_with_retry(
    method="patch",
    url="https://example.com/api/partial-update",
    json={"status": "in_progress"},
    retry_interval=1.5
)
```

### 使用上下文管理器手动切换代理

```python
from utils.spiders.smart_money import UseProxySession

# 创建爬虫实例
spider = BasicSpider()

# 使用上下文管理器临时切换到随机代理
with UseProxySession(spider, spider.sessions):
    response = spider.get("https://example.com/api/protected")
```

## 注意事项

1. 请确保合理设置重试间隔，避免对目标服务器造成过大压力
2. 添加多个代理可以提高成功率，特别是在面对严格的请求限制时
3. 对于需要保持会话的请求，请注意代理切换可能会导致会话丢失
4. 请遵守目标网站的使用条款和robots.txt规则

## 错误处理

爬虫会自动捕获并处理请求过程中的异常，但如果所有重试都失败，将返回最后一次的响应对象。您可以检查响应的状态码来确定请求是否成功：

```python
response = spider.get("https://example.com/api/data")
if response.status_code == 200:
    # 请求成功
    data = response.json()
else:
    # 请求失败
    print(f"请求失败，状态码: {response.status_code}")
``` 