# 过滤测试策略信号测试用例设计

**创建日期**: 2025-05-27  
**测试设计者**: AI助手  
**功能模块**: APIs - 信号记录列表API  
**版本**: 0.1.0  

## 1. 测试概述

本文档定义了过滤测试策略信号功能的完整测试用例，确保API能正确过滤掉`trigger_conditions.strategy_name`为"测试"的信号记录，同时保持现有功能的正常运行。

## 2. 测试环境准备

### 2.1 测试数据准备

**测试数据集**:
```json
[
  {
    "_id": "test_signal_1",
    "token_address": "So11111111111111111111111111111111111111112",
    "token_name": "Test Token 1",
    "token_symbol": "TT1",
    "signal_type": "kol_buy",
    "trigger_conditions": {
      "strategy_name": "测试",
      "other_param": "value1"
    },
    "created_at": "2025-05-27T08:00:00Z"
  },
  {
    "_id": "test_signal_2", 
    "token_address": "TokenAddress2",
    "token_name": "Normal Token",
    "token_symbol": "NT",
    "signal_type": "kol_buy",
    "trigger_conditions": {
      "strategy_name": "正常策略",
      "other_param": "value2"
    },
    "created_at": "2025-05-27T09:00:00Z"
  },
  {
    "_id": "test_signal_3",
    "token_address": "TokenAddress3", 
    "token_name": "Token Without Strategy",
    "token_symbol": "TWS",
    "signal_type": "kol_sell",
    "trigger_conditions": {
      "other_param": "value3"
    },
    "created_at": "2025-05-27T10:00:00Z"
  },
  {
    "_id": "test_signal_4",
    "token_address": "TokenAddress4",
    "token_name": "Token Without Trigger Conditions", 
    "token_symbol": "TWTC",
    "signal_type": "kol_buy",
    "created_at": "2025-05-27T11:00:00Z"
  },
  {
    "_id": "test_signal_5",
    "token_address": "TokenAddress5",
    "token_name": "Test Strategy Variant",
    "token_symbol": "TSV", 
    "signal_type": "kol_buy",
    "trigger_conditions": {
      "strategy_name": "测试策略",
      "other_param": "value5"
    },
    "created_at": "2025-05-27T12:00:00Z"
  }
]
```

## 3. 功能测试用例

### 3.1 基本过滤功能测试

#### TC-001: 基本过滤测试
**测试目标**: 验证API能正确过滤掉strategy_name为"测试"的记录

**前置条件**: 
- 数据库中存在上述测试数据
- API服务正常运行

**测试步骤**:
1. 发送GET请求到 `/api/v1/signals`
2. 不添加任何查询参数

**预期结果**:
- 响应状态码: 200
- 响应数据中不包含`test_signal_1`（strategy_name为"测试"）
- 响应数据包含`test_signal_2`, `test_signal_3`, `test_signal_4`, `test_signal_5`
- 返回记录总数为4

**测试数据**:
```bash
curl -X GET "http://localhost:8000/api/v1/signals" -H "accept: application/json"
```

#### TC-002: 字段不存在情况测试
**测试目标**: 验证缺失trigger_conditions或strategy_name字段的记录不被过滤

**前置条件**: 数据库中存在测试数据

**测试步骤**:
1. 发送GET请求到 `/api/v1/signals`
2. 检查返回结果

**预期结果**:
- `test_signal_3`（缺失strategy_name字段）正常返回
- `test_signal_4`（缺失trigger_conditions字段）正常返回

### 3.2 组合查询测试

#### TC-003: 结合signal_type过滤测试
**测试目标**: 验证过滤功能与signal_type参数正常配合

**测试步骤**:
1. 发送GET请求: `/api/v1/signals?signal_type=kol_buy`

**预期结果**:
- 只返回signal_type为"kol_buy"的记录
- 不包含`test_signal_1`（被测试策略过滤掉）
- 包含`test_signal_2`, `test_signal_4`, `test_signal_5`

#### TC-004: 结合时间范围过滤测试
**测试目标**: 验证过滤功能与period参数正常配合

**测试步骤**:
1. 发送GET请求: `/api/v1/signals?period=today`

**预期结果**:
- 只返回今天的记录
- 测试策略记录仍被正确过滤

#### TC-005: 结合分页参数测试
**测试目标**: 验证过滤功能与分页参数正常配合

**测试步骤**:
1. 发送GET请求: `/api/v1/signals?skip=0&limit=2`

**预期结果**:
- 返回最多2条记录
- 测试策略记录不在结果中
- 分页逻辑正常工作

### 3.3 边界情况测试

#### TC-006: strategy_name为null值测试
**测试目标**: 验证strategy_name为null的记录不被过滤

**测试数据准备**:
```json
{
  "_id": "test_signal_null",
  "token_address": "TokenAddressNull",
  "signal_type": "kol_buy", 
  "trigger_conditions": {
    "strategy_name": null,
    "other_param": "value"
  },
  "created_at": "2025-05-27T13:00:00Z"
}
```

**预期结果**: 该记录正常返回

#### TC-007: strategy_name为空字符串测试
**测试目标**: 验证strategy_name为空字符串的记录不被过滤

**测试数据准备**:
```json
{
  "_id": "test_signal_empty",
  "token_address": "TokenAddressEmpty",
  "signal_type": "kol_buy",
  "trigger_conditions": {
    "strategy_name": "",
    "other_param": "value"
  },
  "created_at": "2025-05-27T14:00:00Z"
}
```

**预期结果**: 该记录正常返回

#### TC-008: 大小写敏感性测试
**测试目标**: 验证过滤条件区分大小写

**测试数据准备**:
```json
[
  {
    "_id": "test_signal_case1",
    "signal_type": "kol_buy",
    "trigger_conditions": {"strategy_name": "Test"},
    "created_at": "2025-05-27T15:00:00Z"
  },
  {
    "_id": "test_signal_case2", 
    "signal_type": "kol_buy",
    "trigger_conditions": {"strategy_name": "测试"},
    "created_at": "2025-05-27T16:00:00Z"
  }
]
```

**预期结果**:
- `test_signal_case1`（"Test"）正常返回
- `test_signal_case2`（"测试"）被过滤掉

## 4. 性能测试用例

### 4.1 响应时间测试

#### TC-009: 基准性能测试
**测试目标**: 验证添加过滤条件后API响应时间变化

**测试步骤**:
1. 准备1000条测试数据（包含各种情况）
2. 记录修改前的API响应时间（基准值）
3. 应用过滤功能
4. 记录修改后的API响应时间

**预期结果**:
- 响应时间增幅不超过10%
- API仍能在合理时间内响应

### 4.2 大数据量测试

#### TC-010: 大数据集过滤测试
**测试目标**: 验证大数据量下过滤功能的正确性和性能

**测试步骤**:
1. 准备10000条测试数据
2. 其中20%的记录strategy_name为"测试"
3. 调用API并验证结果

**预期结果**:
- 正确过滤掉所有测试策略记录
- 返回记录数量正确（约8000条）
- 响应时间在可接受范围内

## 5. 错误处理测试用例

### 5.1 异常情况测试

#### TC-011: 数据库连接异常测试
**测试目标**: 验证数据库异常时的错误处理

**测试步骤**:
1. 模拟数据库连接失败
2. 调用API

**预期结果**:
- 返回标准错误响应
- 错误码为5001
- 错误信息描述清晰

#### TC-012: 无效查询参数测试
**测试目标**: 验证无效参数的处理

**测试步骤**:
1. 发送包含无效参数的请求: `/api/v1/signals?limit=1000`

**预期结果**:
- 返回422状态码
- 参数验证错误信息

## 6. 兼容性测试用例

### 6.1 向后兼容性测试

#### TC-013: 现有客户端兼容性测试
**测试目标**: 验证现有API调用方式继续有效

**测试步骤**:
1. 使用现有的各种API调用方式
2. 验证除了测试策略过滤外，其他行为保持不变

**预期结果**:
- 所有现有功能正常工作
- 响应格式保持不变
- 只是结果中不再包含测试策略记录

## 7. 集成测试用例

### 7.1 端到端测试

#### TC-014: 完整业务流程测试
**测试目标**: 验证过滤功能在完整业务流程中的表现

**测试步骤**:
1. 创建包含测试策略的信号记录
2. 通过API查询信号列表
3. 验证测试策略记录不出现在结果中
4. 验证其他业务逻辑不受影响

**预期结果**:
- 测试策略记录被正确过滤
- 其他业务功能正常运行

## 8. 自动化测试脚本

### 8.1 Python单元测试框架

```python
import unittest
from unittest.mock import patch, AsyncMock
import asyncio

class TestSignalApiFilter(unittest.IsolatedAsyncioTestCase):
    
    async def test_filter_test_strategy_signals(self):
        """测试过滤测试策略信号功能"""
        # 测试实现代码
        pass
    
    async def test_keep_records_without_strategy_name(self):
        """测试保留没有strategy_name字段的记录"""
        # 测试实现代码
        pass
    
    async def test_case_sensitive_filtering(self):
        """测试大小写敏感的过滤"""
        # 测试实现代码
        pass
```

## 9. 测试执行计划

### 9.1 测试阶段
1. **单元测试阶段**: 执行TC-001到TC-008
2. **集成测试阶段**: 执行TC-009到TC-012  
3. **系统测试阶段**: 执行TC-013到TC-014
4. **性能测试阶段**: 重点执行TC-009和TC-010

### 9.2 通过标准
- 所有功能测试用例100%通过
- 性能测试用例满足预期指标
- 兼容性测试确认无破坏性变更

### 9.3 测试报告
测试完成后需要提供详细的测试报告，包括：
- 测试用例执行结果
- 性能对比数据
- 发现的问题和解决方案
- 建议和改进意见 