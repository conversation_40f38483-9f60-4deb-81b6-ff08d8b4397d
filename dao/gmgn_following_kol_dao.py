import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
import pymongo

from models.gmgn_following_kol import GmgnFollowingKol

logger = logging.getLogger(__name__)

class GmgnFollowingKolDAO:
    """GMGN关注KOL的数据访问对象
    
    负责处理GMGN关注KOL数据的数据库操作，包括插入、查询、更新和删除等。
    实现了通用的CRUD操作以及批量处理方法，考虑API变动的兼容性设计。
    """
    
    @staticmethod
    async def upsert_kol(kol_data: Dict[str, Any]) -> Optional[GmgnFollowingKol]:
        """插入或更新单个KOL数据
        
        如果数据库中存在相同address的KOL，则更新其信息；否则插入新记录。
        
        Args:
            kol_data: KOL数据字典
            
        Returns:
            Optional[GmgnFollowingKol]: 更新或插入的KOL对象，操作失败则返回None
        """
        try:
            # 确保address存在
            if 'address' not in kol_data or not kol_data['address']:
                logger.error("无法插入KOL数据: 缺少address字段")
                return None
            
            # 查找现有记录
            existing_kol = await GmgnFollowingKol.find_one({"address": kol_data['address']})
            
            # 设置更新时间
            kol_data['updated_at'] = datetime.now()
            
            if existing_kol:
                # 如果已存在，更新记录
                logger.debug(f"更新现有KOL记录: {kol_data['address']}")
                # 保留现有的created_at
                kol_data['created_at'] = existing_kol.created_at
                
                # 更新文档
                await existing_kol.update({"$set": kol_data})
                # 重新获取更新后的文档
                return await GmgnFollowingKol.find_one({"address": kol_data['address']})
            else:
                # 如果不存在，创建新记录
                logger.debug(f"创建新KOL记录: {kol_data['address']}")
                # 设置创建时间
                kol_data['created_at'] = datetime.now()
                
                # 创建新文档
                new_kol = GmgnFollowingKol(**kol_data)
                await new_kol.insert()
                return new_kol
                
        except Exception as e:
            logger.error(f"插入或更新KOL数据时发生错误: {str(e)}")
            return None
    
    @staticmethod
    async def batch_upsert_kols(kols_data: List[Dict[str, Any]]) -> int:
        """批量插入或更新KOL数据
        
        Args:
            kols_data: KOL数据字典列表
            
        Returns:
            int: 成功处理的KOL数量
        """
        success_count = 0
        
        try:
            # 构建批量操作列表
            operations = []
            current_time = datetime.now()
            
            for kol_data in kols_data:
                # 检查必要字段
                if 'address' not in kol_data or not kol_data['address']:
                    logger.warning("跳过无效KOL数据: 缺少address字段")
                    continue
                
                # 设置元数据
                kol_data['updated_at'] = current_time
                
                # 定义upsert操作
                operations.append(
                    pymongo.UpdateOne(
                        {"address": kol_data['address']},
                        {
                            "$set": kol_data,
                            "$setOnInsert": {"created_at": current_time}
                        },
                        upsert=True
                    )
                )
            
            if operations:
                # 执行批量写入
                result = await GmgnFollowingKol.get_motor_collection().bulk_write(operations)
                success_count = result.upserted_count + result.modified_count
                logger.info(f"批量操作结果: 插入 {result.upserted_count}, 更新 {result.modified_count}")
            
            return success_count
        except Exception as e:
            logger.error(f"批量插入或更新KOL数据时发生错误: {str(e)}")
            return success_count
    
    @staticmethod
    async def find_by_address(address: str) -> Optional[GmgnFollowingKol]:
        """通过地址查找KOL
        
        Args:
            address: 区块链地址
            
        Returns:
            Optional[GmgnFollowingKol]: 匹配的KOL对象，未找到则返回None
        """
        try:
            return await GmgnFollowingKol.find_one({"address": address})
        except Exception as e:
            logger.error(f"查找KOL时发生错误: {str(e)}")
            return None
    
    @staticmethod
    async def find_by_twitter_username(twitter_username: str) -> Optional[GmgnFollowingKol]:
        """通过Twitter用户名查找KOL
        
        Args:
            twitter_username: Twitter用户名
            
        Returns:
            Optional[GmgnFollowingKol]: 匹配的KOL对象，未找到则返回None
        """
        try:
            return await GmgnFollowingKol.find_one({"twitter_username": twitter_username})
        except Exception as e:
            logger.error(f"通过Twitter用户名查找KOL时发生错误: {str(e)}")
            return None
    
    @staticmethod
    async def find_kols(
        filter_dict: Dict[str, Any] = None,
        skip: int = 0,
        limit: int = 100,
        sort_by: List[tuple] = None
    ) -> List[GmgnFollowingKol]:
        """按条件查找KOL列表
        
        Args:
            filter_dict: 过滤条件字典
            skip: 跳过记录数
            limit: 返回记录数上限
            sort_by: 排序条件列表，如[(\"followers_count\", -1)]表示按粉丝数降序排列
            
        Returns:
            List[GmgnFollowingKol]: KOL对象列表
        """
        try:
            filter_dict = filter_dict or {}
            query = GmgnFollowingKol.find(filter_dict).skip(skip).limit(limit)
            
            # 应用排序
            if sort_by:
                # Pass the list of tuples directly to sort
                query = query.sort(sort_by)
            
            return await query.to_list()
        except Exception as e:
            logger.error(f"查找KOL列表时发生错误: {str(e)}")
            return []
    
    @staticmethod
    async def count_kols(filter_dict: Dict[str, Any] = None) -> int:
        """统计符合条件的KOL数量
        
        Args:
            filter_dict: 过滤条件字典
            
        Returns:
            int: KOL数量
        """
        try:
            filter_dict = filter_dict or {}
            return await GmgnFollowingKol.find(filter_dict).count()
        except Exception as e:
            logger.error(f"统计KOL数量时发生错误: {str(e)}")
            return 0
    
    @staticmethod
    async def delete_by_address(address: str) -> bool:
        """通过地址删除KOL
        
        Args:
            address: 区块链地址
            
        Returns:
            bool: 删除成功返回True，否则返回False
        """
        try:
            result = await GmgnFollowingKol.find_one({"address": address})
            if result:
                await result.delete()
                return True
            return False
        except Exception as e:
            logger.error(f"删除KOL时发生错误: {str(e)}")
            return False
    
    @staticmethod
    async def get_top_kols(
        sort_field: str = "followers_count",
        limit: int = 10,
        filter_dict: Dict[str, Any] = None
    ) -> List[GmgnFollowingKol]:
        """获取排名靠前的KOL列表
        
        Args:
            sort_field: 排序字段
            limit: 返回记录数上限
            filter_dict: 额外的过滤条件
            
        Returns:
            List[GmgnFollowingKol]: KOL对象列表
        """
        try:
            filter_dict = filter_dict or {}
            
            # 确保排序字段存在于模型中
            valid_sort_fields = [
                "followers_count", "total_profit", "realized_profit_30d",
                "sol_balance", "eth_balance", "total_value"
            ]
            
            if sort_field not in valid_sort_fields:
                logger.warning(f"无效的排序字段: {sort_field}，使用默认排序字段: followers_count")
                sort_field = "followers_count"
            
            return await GmgnFollowingKol.find(filter_dict).sort(sort_field, -1).limit(limit).to_list()
        except Exception as e:
            logger.error(f"获取排名靠前的KOL列表时发生错误: {str(e)}")
            return []
    
    @staticmethod
    async def get_latest_fetched_kols(limit: int = 100) -> List[GmgnFollowingKol]:
        """获取最近爬取的KOL列表
        
        返回按爬取时间倒序排列的KOL列表，每个地址只返回最新一条记录
        
        Args:
            limit: 返回记录数上限
            
        Returns:
            List[GmgnFollowingKol]: KOL对象列表
        """
        try:
            # 使用聚合管道获取每个地址的最新记录
            pipeline = [
                {"$sort": {"fetched_at": -1}},
                {"$group": {
                    "_id": "$address",
                    "doc": {"$first": "$$ROOT"}
                }},
                {"$replaceRoot": {"newRoot": "$doc"}},
                {"$sort": {"fetched_at": -1}},
                {"$limit": limit}
            ]
            
            cursor = GmgnFollowingKol.get_motor_collection().aggregate(pipeline)
            results = []
            
            async for doc in cursor:
                # 将原始文档转换为Beanie文档对象
                kol = GmgnFollowingKol.parse_obj(doc)
                results.append(kol)
            
            return results
        except Exception as e:
            logger.error(f"获取最近爬取的KOL列表时发生错误: {str(e)}")
            return [] 