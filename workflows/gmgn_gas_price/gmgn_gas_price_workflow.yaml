name: 'GMGN Gas价格监控工作流'
description: '定期获取链上的gas价格信息，并将数据存储到数据库'

nodes:
  - name: "GasPriceSchedulerNode"
    node_type: "input"
    interval: 5  # 每5秒检查一次
    generate_data: workflows.gmgn_gas_price.handler.generate_chain_list
    flow_control:
      max_pending_messages: 10
      check_interval: 5
      enable_flow_control: true

  - name: "GasPriceMonitorNode"
    node_type: "process"
    depend_ons: ["GasPriceSchedulerNode"]
    concurrency: 1
    interval: 1
    process_item: workflows.gmgn_gas_price.handler.process_gas_price

  - name: "GasPriceStorageNode"
    node_type: "storage"
    depend_ons: ["GasPriceMonitorNode"]
    batch_size: 1000  # 每批处理1000条数据
    store_data: workflows.gmgn_gas_price.handler.store_gas_price_data
    validate: workflows.gmgn_gas_price.handler.validate_gas_price_data 
