import random
import asyncio
from curl_cffi import requests
import logging
from typing import Dict, Any, List
import json

from utils.spiders.smart_money import BasicSpider

logger = logging.getLogger(__name__)

class GmgnTopHoldersSpider(BasicSpider):
    """GMGN顶级持有者爬虫
    
    用于获取代币的顶级持有者信息，包括：
    - 持有者地址
    - 持仓数量和价值
    - 买入/卖出数量和金额
    - 利润和收益率
    - 持有者标签
    - 活跃时间
    """
    
    BASE_URL = "https://gmgn.ai/defi/quotation/v1/tokens/top_holders"
    
    def __init__(self):
        super().__init__()
        self.headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-TW;q=0.6',
            'cache-control': 'no-cache',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://gmgn.ai/sol/token/dDO6ZdYE_6p6xgHyF7AeE6TZkSmFsko444wqoP15icUSqi2jfGiPN',
            'sec-ch-ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-ch-ua-arch': '"arm"',
            'sec-ch-ua-full-version': '"134.0.6998.44"',
            'sec-ch-ua-platform-version': '"14.5.0"',
            'sec-ch-ua-bitness': '"64"',
            'sec-ch-ua-model': '""',
            'sec-ch-ua-full-version-list': '"Chromium";v="134.0.6998.44", "Not:A-Brand";v="********", "Google Chrome";v="134.0.6998.44"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
    
    async def setup(self):
        """设置爬虫，初始化会话并更新头信息"""
        await self.init_sessions()
        # 更新会话头信息
        self.session.headers.update(self.headers)
        # 设置浏览器模拟
        self.session.impersonate = "chrome110"  # 模拟Chrome浏览器
        
    async def _get_top_holders(self, url, params):
        """获取顶级持有者数据
        
        使用锁机制控制多线程环境下的请求频率，避免触发反爬机制
        
        TODO: 
        - 实现多账号轮换机制，每个账号有独立的请求配额
        - 实现请求频率的自适应调整，根据响应状态动态调整请求间隔
        - 考虑添加更多的浏览器指纹随机化，避免被识别为爬虫
        """
        retry_count = 0
        max_retry_count = 3
        wait_interval_seconds = lambda : random.randint(1, 5)
        
        # 确保会话已设置
        if self.session is None:
            await self.setup()
            
        # 使用类级别的锁控制请求频率
        while retry_count < max_retry_count:
            try:
                
                # 使用curl_cffi发送请求
                response = await self.get(url, params=params)
                
                print(f"API响应内容: {response.text[:500]}...")
                
                # 记录响应状态和内容
                logger.debug(f"API响应状态码: {response.status_code}")
                logger.debug(f"API响应内容: {response.text[:500]}...")  # 只记录前500个字符，避免日志过大
                
                # 检查响应状态
                if response.status_code != 200:
                    logger.error(f"获取代币顶级持有者信息失败: HTTP {response.status_code}")
                    logger.error(f"错误响应: {response.text}")
                    return []
                
                data = response.json()
                
                if not isinstance(data, dict):
                    logger.error(f"API响应格式错误，期望dict类型，实际为: {type(data)}")
                    return []
                
                # 检查API响应格式
                if not isinstance(data, dict):
                    logger.error(f"API响应格式错误，期望dict类型，实际为: {type(data)}")
                    return []
                
                # 检查API响应状态
                if data.get('code') != 0:  # 0 表示成功
                    logger.error(f"API返回错误: {data.get('message', '未知错误')}")
                    return []
                
                return data
            
            except Exception as e:
                logger.error(f"获取代币顶级持有者信息时发生错误: {str(e)}")
                retry_count += 1
                # 在锁内等待，确保其他线程不会同时发送请求
                await asyncio.sleep(wait_interval_seconds() * (retry_count+1))
            
        return []
    
    async def get_top_holders(self, 
                        chain: str, 
                        address: str, 
                        tag: str = "smart_degen", 
                        orderby: str = "amount_percentage", 
                        direction: str = "desc",
                        limit: int = 20, 
                        cost: int = 20
                    ) -> List[Dict[str, Any]]:
        """
        获取代币的顶级持有者信息
        
        Args:
            chain: 链名称，如"sol"
            address: 代币地址
            tag: 持有者标签，默认为"smart_degen"
            orderby: 排序字段，默认为"amount_percentage"
            direction: 排序方向，默认为"desc"
            limit: 返回结果数量限制，默认为20
            cost: 成本限制，默认为20
            
        Returns:
            List[Dict[str, Any]]: 顶级持有者信息列表
        """
        try:
                
            url = f"{self.BASE_URL}/{chain}/{address}"
            logger.info(f"正在获取代币顶级持有者信息: {url}, tag={tag}, orderby={orderby}, direction={direction}")
            
            params = {
                'device_id': '65990bc3-0a13-4d99-8c66-eeea94916b0f',
                'client_id': 'gmgn_web_2025.0311.180038',
                'from_app': 'gmgn',
                'app_ver': '2025.0311.180038',
                'tz_name': 'Asia/Shanghai',
                'tz_offset': '28800',
                'app_lang': 'zh-CN',
                'orderby': orderby,
                'direction': direction,
                'tag': tag,
                'limit': limit,
                'cost': cost,
            }
            
            # 解析响应数据
            try:
                data = await self._get_top_holders(url, params)
                
                # 提取实际数据
                holders = data.get('data', [])
                if not holders:
                    logger.warning("API响应中没有持有者数据")
                    return []
                
                # 格式化数据
                formatted_holders = []
                for holder in holders:
                    formatted_holder = {
                        'address': holder.get('address', ''),
                        'token_address': address,
                        'account_address': holder.get('account_address', ''),
                        'addr_type': holder.get('addr_type', 0),
                        'amount_cur': holder.get('amount_cur', 0),
                        'usd_value': holder.get('usd_value', 0),
                        'cost_cur': holder.get('cost_cur', 0),
                        'sell_amount_cur': holder.get('sell_amount_cur', 0),
                        'sell_amount_percentage': holder.get('sell_amount_percentage', 0),
                        'sell_volume_cur': holder.get('sell_volume_cur', 0),
                        'buy_volume_cur': holder.get('buy_volume_cur', 0),
                        'buy_amount_cur': holder.get('buy_amount_cur', 0),
                        'netflow_usd': holder.get('netflow_usd', 0),
                        'netflow_amount': holder.get('netflow_amount', 0),
                        'buy_tx_count_cur': holder.get('buy_tx_count_cur', 0),
                        'sell_tx_count_cur': holder.get('sell_tx_count_cur', 0),
                        'wallet_tag_v2': holder.get('wallet_tag_v2', ''),
                        'eth_balance': holder.get('eth_balance', '0'),
                        'sol_balance': holder.get('sol_balance', '0'),
                        'trx_balance': holder.get('trx_balance', '0'),
                        'balance': holder.get('balance', '0'),
                        'profit': holder.get('profit', 0),
                        'realized_profit': holder.get('realized_profit', 0),
                        'profit_change': holder.get('profit_change', 0),
                        'amount_percentage': holder.get('amount_percentage', 0),
                        'unrealized_profit': holder.get('unrealized_profit', 0),
                        'unrealized_pnl': holder.get('unrealized_pnl', 0),
                        'avg_cost': holder.get('avg_cost', 0),
                        'avg_sold': holder.get('avg_sold', 0),
                        'tags': holder.get('tags', []),
                        'maker_token_tags': holder.get('maker_token_tags', []),
                        'name': holder.get('name', ''),
                        'avatar': holder.get('avatar', ''),
                        'twitter_username': holder.get('twitter_username', ''),
                        'twitter_name': holder.get('twitter_name', ''),
                        'tag_rank': holder.get('tag_rank', {}),
                        'last_active_timestamp': holder.get('last_active_timestamp', 0),
                        'created_at': holder.get('created_at', 0),
                        'accu_amount': holder.get('accu_amount', 0),
                        'accu_cost': holder.get('accu_cost', 0),
                        'cost': holder.get('cost', 0),
                        'total_cost': holder.get('total_cost', 0),
                        'transfer_in': holder.get('transfer_in', False),
                        'is_new': holder.get('is_new', False),
                        'is_suspicious': holder.get('is_suspicious', False),
                        'start_holding_at': holder.get('start_holding_at'),
                        'end_holding_at': holder.get('end_holding_at')
                    }
                    
                    # 添加原生转账信息（如果有）
                    if 'native_transfer' in holder:
                        formatted_holder['native_transfer'] = holder['native_transfer']
                    
                    formatted_holders.append(formatted_holder)
                
                logger.info(f"成功获取代币顶级持有者信息，共{len(formatted_holders)}条记录")
                return formatted_holders
                
            except json.JSONDecodeError as e:
                logger.error(f"解析API响应JSON失败: {str(e)}")
                return []
                
        except Exception as e:
            logger.error(f"获取代币顶级持有者信息时发生错误: {str(e)}")
            return []

async def main():
    spider = GmgnTopHoldersSpider()
    # 获取SOL链上特定代币的smart_degen标签的顶级持有者，按持仓比例降序排列
    holders = await spider.get_top_holders(
        chain="sol", 
        address="Egwsbi8mAEZTL1JfjCHxKa4RcaUGRVpKR1M8Jf31Nx45",
        tag="blue_chip",
        orderby="amount_percentage",
        direction="desc"
    )
    print(f"获取到{len(holders)}个顶级持有者")
    # if holders:
    #     # 打印前3个持有者的基本信息
    #     for i, holder in enumerate(holders[:3]):
    #         print(f"\n持有者 {i+1}:")
    #         print(f"地址: {holder['address']}")
    #         print(f"账户地址: {holder['account_address']}")
    #         print(f"当前持有量: {holder['amount_cur']}")
    #         print(f"持仓比例: {holder['amount_percentage']}")
    #         print(f"已实现利润: {holder['realized_profit']}")
    #         print(f"标签: {', '.join(holder['tags'])}")
    #         print(f"Twitter: {holder['twitter_username']}")
    
    # 关闭爬虫会话连接
    await spider.close()

if __name__ == "__main__":
    # 使用asyncio运行异步主函数
    asyncio.run(main()) 