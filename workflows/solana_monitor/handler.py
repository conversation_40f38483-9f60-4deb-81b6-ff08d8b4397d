"""
Solana监控工作流处理函数模块

提供YAML配置中引用的处理函数
"""

import logging
import traceback
import json
import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime

from utils.spiders.solana.solana_monitor import SolanaMonitor
from dao.solana_transaction_dao import SolanaTransactionDAO
from dao.solana_monitor_address_dao import SolanaMonitorAddressDAO
from models.solana_transaction import SolanaTransaction

# 创建日志记录器
logger = logging.getLogger("SolanaMonitorHandler")

# 创建爬虫和DAO实例
monitor = SolanaMonitor(proxy="**********************************************************")
transaction_dao = SolanaTransactionDAO()
monitor_address_dao = SolanaMonitorAddressDAO()

async def generate_monitor_addresses() -> Optional[List[Dict]]:
    """
    生成需要监控的地址列表
    
    Returns:
        List[Dict]: 地址列表
    """
    try:
        # 使用 DAO 获取所有监控地址
        addresses = await monitor_address_dao.find_all_addresses()
        
        if not addresses:
            logger.info("没有需要监控的地址")
            return None
            
        # 转换为字典列表
        address_list = []
        for addr in addresses:
            address_dict = {
                "address": addr.address,
                "name": addr.name,
                "description": addr.description
            }
            address_list.append(address_dict)
            
        logger.info(f"生成了 {len(address_list)} 个监控地址")
        return address_list
        
    except Exception as e:
        logger.error(f"生成监控地址列表时发生错误: {str(e)}\n{traceback.format_exc()}")
        return None

async def process_address_transactions(address_data: Dict) -> Optional[List[Dict]]:
    """
    处理单个地址的最新交易
    
    Args:
        address_data: 地址数据
        
    Returns:
        List[Dict]: 处理后的交易数据列表
    """
    try:
        address = address_data.get("address")
        if not address:
            logger.error("缺少地址")
            return None
            
        logger.info(f"处理地址: {address}")
        
        # 使用 DAO 获取该地址最后处理的交易签名
        last_tx = await transaction_dao.find_latest_transaction(address)
        last_signature = last_tx.signature if last_tx else None
        logger.info(f"last_signature: {last_signature}")
        
        # 从before这个签名开始获取交易
        transactions = await monitor.get_account_transactions(
            address,
            until=last_signature,
            limit=1000  # 每次获取1000笔交易
        )
        
        if not transactions:
            logger.info(f"地址 {address} 没有新交易")
            return None
            
        # 处理交易
        processed_transactions = []
        processed_signatures = set()
        
        for tx_str in transactions:
            tx = json.loads(tx_str)
            signature = tx["signature"]
            
            # 使用 DAO 验证该交易是否已存在于数据库
            exists = await transaction_dao.find_by_signature(signature)
            if exists or signature in processed_signatures:
                continue
                
            processed_signatures.add(signature)
            
            # 获取交易详情前等待0.5秒
            await asyncio.sleep(0.5)
            
            # 获取交易详情
            tx_details = await monitor.get_transaction_details(signature)
            if not tx_details:
                logger.error(f"无法获取交易详情: {signature}")
                continue
                
            # 分析交易前等待0.5秒
            await asyncio.sleep(0.5)
            
            # 分析交易
            analysis = await monitor.analyze_transaction(tx_details)
            
            # 构建标准化的交易数据
            transaction_data = {
                "address": address,
                "signature": signature,
                "timestamp": datetime.fromtimestamp(tx["blockTime"]),
                "block_time": tx["blockTime"],
                "block_slot": tx.get("slot"),
                "created_at": datetime.utcnow(),
                "token_transfers": [],  # 添加数组来存储所有代币转账
                "sol_transfers": []     # 添加数组来存储所有SOL转账
            }
            
            has_transfers = False  # 标记是否有任何转账
            
            # 处理代币转账
            if analysis["token_transfers"]:
                for transfer in analysis["token_transfers"]:
                    if transfer["owner"] == address:  # 只处理监控地址的转账
                        position_impact = abs(transfer["change"] / transfer["pre_amount"] * 100) if transfer["pre_amount"] != 0 else float('inf')
                        transfer_data = {
                            "token_name": transfer["token_name"],
                            "token_symbol": transfer["token_symbol"],
                            "token_mint": transfer["token_mint"],
                            "amount_change": transfer["change"],
                            "pre_balance": transfer["pre_amount"],
                            "post_balance": transfer["post_amount"],
                            "position_impact": position_impact,
                            "trade_type": determine_trade_type(
                                transfer["change"],
                                position_impact
                            )
                        }
                        transaction_data["token_transfers"].append(transfer_data)
                        has_transfers = True
            
            # 处理SOL转账
            if analysis["sol_transfers"]:
                for transfer in analysis["sol_transfers"]:
                    if transfer["address"] == address:  # 只处理监控地址的转账
                        sol_transfer_data = {
                            "is_sol_transfer": True,
                            "sol_amount": transfer["change"]
                        }
                        transaction_data["sol_transfers"].append(sol_transfer_data)
                        has_transfers = True
            
            # 设置交易类型
            if has_transfers:
                if transaction_data["token_transfers"] and transaction_data["sol_transfers"]:
                    transaction_data["transaction_type"] = "mixed_transfer"
                elif transaction_data["token_transfers"]:
                    transaction_data["transaction_type"] = "token_transfer"
                else:
                    transaction_data["transaction_type"] = "sol_transfer"
            else:
                transaction_data["transaction_type"] = "other"  # 如果没有任何转账，标记为其他类型
            
            processed_transactions.append(transaction_data)
            logger.info(f"已处理交易: {signature}")
            
            # 每处理3个交易等待2秒，避免请求过于频繁
            if len(processed_transactions) % 3 == 0:
                await asyncio.sleep(2)
        
        logger.info(f"已处理 {len(processed_transactions)} 个交易")
        return processed_transactions
        
    except Exception as e:
        logger.error(f"处理地址 {address} 的交易时发生错误: {str(e)}\n{traceback.format_exc()}")
        return None

def determine_trade_type(change: float, position_impact: float) -> str:
    """
    根据交易变化和持仓影响确定交易类型
    
    Args:
        change: 交易数量变化
        position_impact: 持仓影响百分比
        
    Returns:
        交易类型描述
    """
    if change > 0:
        if position_impact == float('inf'):
            return "首次建仓"
        elif position_impact >= 50:
            return "大幅加仓"
        elif position_impact >= 20:
            return "中幅加仓"
        else:
            return "小幅加仓"
    else:
        if position_impact >= 90:
            return "清仓卖出"
        elif position_impact >= 50:
            return "大幅减仓"
        elif position_impact >= 20:
            return "中幅减仓"
        else:
            return "小幅减仓"

async def validate_transaction_data(data: Any) -> bool:
    """
    验证交易数据的有效性
    
    Args:
        data: 要验证的数据
        
    Returns:
        bool: 数据是否有效
    """
    try:
        if not data:
            logger.warning("数据为空")
            return False
            
        # 检查是否为列表
        if isinstance(data, list):
            if not data:
                logger.warning("数据列表为空")
                return False
                
            # 检查每个项目是否包含必要字段
            for item in data:
                if not all(field in item for field in ["address", "signature", "timestamp", "transaction_type"]):
                    logger.error(f"数据缺少必要字段: {item}")
                    return False
        # 检查单个项目
        elif isinstance(data, dict):
            if not all(field in data for field in ["address", "signature", "timestamp", "transaction_type"]):
                logger.error(f"数据缺少必要字段: {data}")
                return False
        else:
            logger.error(f"数据类型错误: {type(data)}")
            return False
            
        return True
        
    except Exception as e:
        logger.error(f"验证交易数据时发生错误: {str(e)}\n{traceback.format_exc()}")
        return False

async def store_transaction_data(data_list: List[Dict]) -> int:
    """
    存储交易数据到数据库
    
    Args:
        data_list: 交易数据列表
        
    Returns:
        int: 成功存储的数据数量
    """
    try:
        if not data_list:
            logger.warning("没有数据需要存储")
            return 0
            
        total_saved = 0
        
        for data in data_list:
            transactions_to_save = []
            
            # 对于每个代币转账创建一个交易记录
            if data.get("token_transfers"):
                for token_transfer in data["token_transfers"]:
                    if not token_transfer.get("token_mint"):  # 跳过没有代币信息的转账
                        logger.warning(f"跳过无效的代币转账数据: {token_transfer}")
                        continue
                        
                    transaction = SolanaTransaction(
                        address=data["address"],
                        signature=data["signature"],
                        timestamp=data["timestamp"],
                        transaction_type="token_transfer",  # 明确指定类型
                        token_name=token_transfer.get("token_name"),
                        token_symbol=token_transfer.get("token_symbol"),
                        token_mint=token_transfer.get("token_mint"),
                        amount_change=token_transfer.get("amount_change", 0.0),
                        pre_balance=token_transfer.get("pre_balance"),
                        post_balance=token_transfer.get("post_balance"),
                        position_impact=token_transfer.get("position_impact"),
                        trade_type=token_transfer.get("trade_type"),
                        is_sol_transfer=False,
                        block_time=data["block_time"],
                        block_slot=data.get("block_slot")
                    )
                    transactions_to_save.append(transaction)
            
            # 对于每个SOL转账创建一个交易记录
            if data.get("sol_transfers"):
                for sol_transfer in data["sol_transfers"]:
                    if sol_transfer.get("sol_amount") is None:  # 跳过没有金额的转账
                        logger.warning(f"跳过无效的SOL转账数据: {sol_transfer}")
                        continue
                        
                    transaction = SolanaTransaction(
                        address=data["address"],
                        signature=data["signature"],
                        timestamp=data["timestamp"],
                        transaction_type="sol_transfer",  # 明确指定类型
                        is_sol_transfer=True,
                        sol_amount=sol_transfer.get("sol_amount"),
                        block_time=data["block_time"],
                        block_slot=data.get("block_slot")
                    )
                    transactions_to_save.append(transaction)
            
            # 如果没有任何有效的转账记录，创建一个基本交易记录
            if not transactions_to_save:
                transaction = SolanaTransaction(
                    address=data["address"],
                    signature=data["signature"],
                    timestamp=data["timestamp"],
                    transaction_type=data["transaction_type"],
                    block_time=data["block_time"],
                    block_slot=data.get("block_slot"),
                    # 添加必填字段的默认值
                    amount_change=0.0,
                    is_sol_transfer=False,
                    pre_balance=0.0,
                    post_balance=0.0,
                    position_impact=0.0
                )
                transactions_to_save.append(transaction)
            
            # 使用 DAO 批量保存所有交易记录
            try:
                saved_count = await transaction_dao.batch_save_transactions(transactions_to_save)
                logger.info(f"已存储 {saved_count} 条交易记录")
                total_saved += saved_count
                
            except Exception as e:
                logger.error(f"批量存储交易记录失败: {str(e)}\n{traceback.format_exc()}")
        
        return total_saved
        
    except Exception as e:
        logger.error(f"存储交易数据时发生错误: {str(e)}\n{traceback.format_exc()}")
        return 0 