import unittest
from unittest.mock import patch
from datetime import datetime
from typing import Optional, List

from beanie import PydanticObjectId
from pydantic import ValidationError

from models.trade_record import TradeRecord, TradeStatus, TradeType


@patch('models.trade_record.TradeRecord.get_motor_collection')
class TestTradeRecord(unittest.TestCase):
    """单元测试 TradeRecord 模型"""

    def test_trade_record_instantiation_valid(self, mock_get_collection):
        """测试使用有效数据成功实例化 TradeRecord"""
        signal_id = PydanticObjectId()
        record_data = {
            "signal_id": signal_id,
            "strategy_name": "test_strategy",
            "trade_provider": "gmgn",
            "trade_type": TradeType.BUY,
            "status": TradeStatus.PENDING,
            "token_in_address": "SOL_ADDRESS",
            "token_in_amount": 1.0,
            "token_out_address": "TOKEN_B_ADDRESS",
            "wallet_address": "WALLET_XYZ",
            # 可选字段可以不提供，依赖默认值
        }
        try:
            record = TradeRecord(**record_data)
            self.assertIsInstance(record, TradeRecord)
            self.assertEqual(record.signal_id, signal_id)
            self.assertEqual(record.strategy_name, "test_strategy")
            self.assertEqual(record.trade_type, TradeType.BUY)
            self.assertEqual(record.status, TradeStatus.PENDING)
            self.assertIsInstance(record.created_at, datetime)
            self.assertIsInstance(record.updated_at, datetime)
            self.assertIsNone(record.tx_hash) # 默认应为 None
        except ValidationError as e:
            self.fail(f"TradeRecord instantiation failed with valid data: {e}")

    def test_trade_record_instantiation_minimal(self, mock_get_collection):
        """测试使用最少必需数据（依赖默认值）成功实例化"""
        # 对于 TradeRecord，很多字段都有默认值或可选，这里测试没有显式提供这些字段的情况
        # 假设 signal_id, trade_provider, trade_type, status, token_in_address, token_out_address, wallet_address 是核心
        # 但实际上模型定义中它们也有 Optional 或默认值，使得完全"最小"的定义变得复杂
        # 此处我们假设一些基本字段，并检查默认值
        signal_id = PydanticObjectId()
        record_data = {
            "signal_id": signal_id,
            "strategy_name": "default_strat", # 假设这个是业务逻辑上的"必需"
            "trade_provider": "gmgn",
            "trade_type": TradeType.SELL,
            "status": TradeStatus.SUCCESS,
            "token_in_address": "TOKEN_A",
            "token_in_amount": 10.0,
            "token_out_address": "SOL",
            "wallet_address": "WALLET_ABC",
            "tx_hash": "some_tx_hash_on_success",
            "executed_at": datetime.utcnow()
        }
        try:
            record = TradeRecord(**record_data)
            self.assertIsInstance(record, TradeRecord)
            self.assertEqual(record.status, TradeStatus.SUCCESS)
            self.assertEqual(record.error_message, None) # 默认
            self.assertEqual(record.provider_response_raw, None) # 默认应为 None
        except ValidationError as e:
            self.fail(f"TradeRecord minimal instantiation failed: {e}")

    def test_trade_record_invalid_trade_type_enum(self, mock_get_collection):
        """测试当 trade_type 不是有效的 TradeType 枚举成员时引发 ValidationError"""
        signal_id = PydanticObjectId()
        with self.assertRaises(ValidationError):
            TradeRecord(
                signal_id=signal_id, strategy_name="test", trade_provider="gmgn",
                trade_type="INVALID_TYPE", # 无效枚举值
                status=TradeStatus.PENDING,
                token_in_address="IN", token_in_amount=1.0, token_out_address="OUT", wallet_address="W"
            )

    def test_trade_record_invalid_status_enum(self, mock_get_collection):
        """测试当 status 不是有效的 TradeStatus 枚举成员时引发 ValidationError"""
        signal_id = PydanticObjectId()
        with self.assertRaises(ValidationError):
            TradeRecord(
                signal_id=signal_id, strategy_name="test", trade_provider="gmgn",
                trade_type=TradeType.BUY,
                status="INVALID_STATUS", # 无效枚举值
                token_in_address="IN", token_in_amount=1.0, token_out_address="OUT", wallet_address="W"
            )

    def test_trade_record_timestamps_auto_creation(self, mock_get_collection):
        """测试 created_at 和 updated_at 时间戳是否自动创建"""
        signal_id = PydanticObjectId()
        record = TradeRecord(
            signal_id=signal_id, strategy_name="ts_test", trade_provider="gmgn",
            trade_type=TradeType.BUY, status=TradeStatus.PENDING,
            token_in_address="IN", token_in_amount=1.0, token_out_address="OUT", wallet_address="W"
        )
        self.assertIsInstance(record.created_at, datetime)
        self.assertIsInstance(record.updated_at, datetime)
        self.assertAlmostEqual(record.created_at.timestamp(), record.updated_at.timestamp(), delta=1) # 应该非常接近

    def test_trade_record_optional_fields_default_to_none_or_empty(self, mock_get_collection):
        """测试可选字段在未提供时默认为 None 或其指定的默认空值"""
        signal_id = PydanticObjectId()
        record = TradeRecord(
            signal_id=signal_id, strategy_name="opt_test", trade_provider="gmgn",
            trade_type=TradeType.BUY, status=TradeStatus.PENDING,
            token_in_address="IN", token_in_amount=1.0, token_out_address="OUT", wallet_address="W"
        )
        self.assertIsNone(record.tx_hash)
        self.assertIsNone(record.error_message)
        self.assertEqual(record.provider_response_raw, None) # 默认应为 None
        self.assertIsNone(record.token_in_actual_amount)
        self.assertIsNone(record.token_out_actual_amount)
        self.assertIsNone(record.executed_at)

    def test_trade_record_with_all_fields(self, mock_get_collection):
        """测试当所有字段（包括可选）都被提供时的情况"""
        signal_id = PydanticObjectId()
        buy_record_id = PydanticObjectId()
        now = datetime.utcnow()
        record_data = {
            "signal_id": signal_id,
            "strategy_name": "full_test",
            "trade_provider": "gmgn",
            "trade_type": TradeType.SELL,
            "status": TradeStatus.SUCCESS,
            "token_in_address": "TOKEN_SOLD",
            "token_in_amount": 50.0,
            "token_out_address": "SOL_RECEIVED",
            "wallet_address": "MY_WALLET",
            "tx_hash": "tx_hash_example_sell_success",
            "error_message": None, # 显式设为 None
            "provider_response_raw": {"gmgn_status": "filled", "price": 0.5},
            "token_in_actual_amount": 49.9,
            "token_out_actual_amount": 24.95,
            "created_at": now, # 可以覆盖默认值
            "updated_at": now, # 可以覆盖默认值
            "executed_at": now,
        }
        try:
            record = TradeRecord(**record_data)
            self.assertEqual(record.token_in_actual_amount, 49.9)
            self.assertEqual(record.provider_response_raw, {"gmgn_status": "filled", "price": 0.5})
            self.assertEqual(record.executed_at, now)
        except ValidationError as e:
            self.fail(f"TradeRecord instantiation with all fields failed: {e}")

if __name__ == '__main__':
    unittest.main() 