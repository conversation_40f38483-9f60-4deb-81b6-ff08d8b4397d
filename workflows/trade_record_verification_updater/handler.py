#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Trade Record Verification Updater Handler
交易记录验证更新器处理器

该模块负责处理交易记录验证更新工作流的各个节点任务。

Bug修复说明（2025-05-29）：
修复盈利计算不准确的Bug，通过使用GmgN API获取统一口径的交易金额数据：
- 买入成本：使用GmgN API获取的实际花费（替代计划投入金额）
- 卖出收入：使用GmgN API获取的实际收入（替代链上余额变化）
- 统一口径：GmgN API返回的金额已经扣除了平台服务费等，提供了一致的交易计算基础
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from decimal import Decimal

from dao.trade_record_dao import TradeRecordDAO
from models.trade_record import TradeRecord, TradeType
from utils.spiders.smart_money.gmgn_wallet_token_activity_spider import GmgnWalletTokenActivitySpider

# 配置日志
logger = logging.getLogger(__name__)


async def scan_pending_trade_records() -> List[Dict[str, Any]]:
    """
    扫描待验证的交易记录
    
    修复说明：扫描所有需要验证实际金额的记录，不再局限于pending状态
    
    Returns:
        待验证的交易记录列表
    """
    try:
        start_time = datetime.now()
        logger.info("开始扫描待验证的交易记录...")
        
        # 配置参数
        batch_size = 30
        
        # 初始化DAO
        trade_record_dao = TradeRecordDAO()
        
        # 查询买入记录（缺少实际金额）
        buy_records = await trade_record_dao.find_trades_missing_actual_amounts(
            trade_type=TradeType.BUY,
            missing_field="verification_status",
            limit=batch_size // 2
        )
        
        # 查询卖出记录（缺少实际金额）
        sell_records = await trade_record_dao.find_trades_missing_actual_amounts(
            trade_type=TradeType.SELL,
            missing_field="verification_status", 
            limit=batch_size // 2
        )
        
        # 合并并转换为字典格式
        all_records = list(buy_records) + list(sell_records)
        result = []
        
        for record in all_records:
            # 验证必需字段
            if not record.tx_hash or not record.wallet_address:
                logger.warning(f"跳过缺少必需字段的记录 {record.id}: tx_hash={record.tx_hash}, wallet_address={record.wallet_address}")
                continue
            
            # 确定目标代币地址
            if record.trade_type == TradeType.BUY:
                # 买入：目标代币是 token_out_address
                target_token = record.token_out_address
            else:
                # 卖出：目标代币是 token_in_address（用于查询交易活动）
                target_token = record.token_in_address
            
            if not target_token:
                logger.warning(f"跳过缺少目标代币地址的记录 {record.id}")
                continue
            
            result.append({
                'id': str(record.id),
                'trade_type': record.trade_type.name,
                'tx_hash': record.tx_hash,
                'wallet_address': record.wallet_address,
                'target_token': target_token,
                'token_in_address': record.token_in_address,
                'token_out_address': record.token_out_address,
                'created_at': record.created_at.isoformat() if record.created_at else None,
                'strategy_name': record.strategy_name
            })
        
        execution_time = (datetime.now() - start_time).total_seconds()
        logger.info(f"扫描完成，找到 {len(result)} 条需要验证的记录（买入: {len(buy_records)}, 卖出: {len(sell_records)}），耗时 {execution_time:.2f}s")
        
        return result
        
    except Exception as e:
        logger.error(f"扫描待验证交易记录失败: {str(e)}")
        logger.error(f"错误类型: {type(e).__name__}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        return []


async def verify_trade_amounts(record: Dict[str, Any]) -> Dict[str, Any]:
    """
    验证单个交易记录的实际金额
    
    修复说明：使用GmgN API替代原有的Solana RPC验证，获取统一口径的交易金额
    
    Args:
        record: 单个待验证的交易记录
        
    Returns:
        验证结果字典
    """
    try:
        start_time = datetime.now()
        logger.info(f"开始验证交易记录 {record.get('id', 'unknown')} 的实际金额...")
        
        if not record:
            logger.warning("收到空记录")
            return {
                'id': None,
                'verification_status': 'failed',
                'verified_amount': None,
                'trade_type': None,
                'verification_timestamp': datetime.utcnow().isoformat(),
                'error_message': 'Empty record'
            }
        
        # 配置参数
        timeout_seconds = 10
        
        # 初始化GmgN API客户端
        spider = GmgnWalletTokenActivitySpider()
        
        try:
            # 使用GmgN API查找特定交易的实际金额
            activity_result = await asyncio.wait_for(
                spider.find_activity_by_signature(
                    chain="sol",
                    wallet_address=record['wallet_address'],
                    token_mint=record['target_token'],
                    tx_signature=record['tx_hash'],
                    limit=50
                ),
                timeout=timeout_seconds
            )
            
            if not activity_result:
                logger.warning(f"未找到交易 {record['tx_hash']} 的GmgN活动记录")
                return {
                    'id': record['id'],
                    'verification_status': 'failed',
                    'verified_amount': None,
                    'trade_type': record['trade_type'],
                    'verification_timestamp': datetime.utcnow().isoformat(),
                    'error_message': 'Transaction activity not found in GmgN API'
                }
            
            # 提取实际金额（SOL）
            actual_amount = activity_result.get('amount')
            trade_type_from_api = activity_result.get('type')  # "buy" 或 "sell"
            
            # 验证交易类型匹配
            expected_type = record['trade_type'].lower()
            if trade_type_from_api != expected_type:
                logger.warning(f"交易类型不匹配 - 记录: {expected_type}, API: {trade_type_from_api}")
            
            # 根据交易类型处理金额
            if record['trade_type'] == 'BUY':
                # 买入：GmgN API返回负数，取绝对值作为实际成本
                if actual_amount is not None:
                    verified_amount = abs(float(actual_amount))
                else:
                    verified_amount = None
            else:
                # 卖出：GmgN API返回正数，直接使用作为实际收入
                if actual_amount is not None:
                    verified_amount = float(actual_amount)
                else:
                    verified_amount = None
            
            result = {
                'id': record['id'],
                'verification_status': 'verified' if verified_amount is not None else 'failed',
                'verified_amount': verified_amount,
                'trade_type': record['trade_type'],
                'verification_timestamp': datetime.utcnow().isoformat(),
                'data_source': 'gmgn_api',  # 标记数据来源
                'api_activity': activity_result  # 保留原始API数据用于调试
            }
            
            execution_time = (datetime.now() - start_time).total_seconds()
            logger.info(f"验证记录 {record['id']} 完成，实际金额: {verified_amount} SOL，耗时 {execution_time:.2f}s")
            
            return result
            
        except asyncio.TimeoutError:
            logger.warning(f"验证记录 {record['id']} 超时")
            return {
                'id': record['id'],
                'verification_status': 'failed',
                'verified_amount': None,
                'trade_type': record['trade_type'],
                'verification_timestamp': datetime.utcnow().isoformat(),
                'error_message': 'GmgN API timeout'
            }
            
        except Exception as e:
            logger.error(f"验证记录 {record['id']} 失败: {str(e)}")
            return {
                'id': record['id'],
                'verification_status': 'failed',
                'verified_amount': None,
                'trade_type': record['trade_type'],
                'verification_timestamp': datetime.utcnow().isoformat(),
                'error_message': str(e)
            }
        
    except Exception as e:
        logger.error(f"验证交易金额失败: {str(e)}")
        logger.error(f"错误类型: {type(e).__name__}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        return {
            'id': record.get('id', 'unknown'),
            'verification_status': 'failed',
            'verified_amount': None,
            'trade_type': record.get('trade_type'),
            'verification_timestamp': datetime.utcnow().isoformat(),
            'error_message': str(e)
        }


async def update_verification_results(results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    更新验证结果到数据库
    
    修复说明：根据交易类型更新相应的实际金额字段，实现统一口径的数据存储
    
    Args:
        results: 验证结果列表
        
    Returns:
        更新结果统计
    """
    try:
        start_time = datetime.now()
        logger.info(f"开始更新 {len(results)} 条验证结果...")
        
        if not results:
            logger.info("没有验证结果需要更新")
            return {'updated_count': 0, 'failed_count': 0}
        
        # 配置参数
        batch_size = 30
        
        # 初始化DAO
        trade_record_dao = TradeRecordDAO()
        
        updated_count = 0
        failed_count = 0
        
        # 分批处理更新
        for i in range(0, len(results), batch_size):
            batch = results[i:i + batch_size]
            
            for result in batch:
                try:
                    # 准备基础更新数据
                    update_data = {
                        'verification_status': result['verification_status'],
                        'verification_timestamp': datetime.fromisoformat(result['verification_timestamp'].replace('Z', '+00:00'))
                    }
                    
                    # 根据交易类型更新相应的实际金额字段
                    if result.get('verified_amount') is not None and result.get('trade_type'):
                        if result['trade_type'] == 'BUY':
                            # 买入：更新实际花费金额
                            update_data['token_in_actual_amount'] = float(result['verified_amount'])
                            logger.debug(f"更新买入记录 {result['id']} 的实际成本: {result['verified_amount']} SOL")
                        elif result['trade_type'] == 'SELL':
                            # 卖出：更新实际收入金额
                            update_data['token_out_actual_amount'] = float(result['verified_amount'])
                            logger.debug(f"更新卖出记录 {result['id']} 的实际收入: {result['verified_amount']} SOL")
                    
                    # 添加数据来源信息
                    if result.get('data_source'):
                        update_data['actual_amount_source'] = result['data_source']
                        update_data['actual_amount_updated_at'] = datetime.utcnow()
                    
                    # 添加错误信息（如果有）
                    if result.get('error_message'):
                        update_data['verification_error'] = result['error_message']
                    
                    # 数据验证
                    if not result.get('id'):
                        logger.error(f"验证结果缺少ID: {result}")
                        failed_count += 1
                        continue
                    
                    if result['verification_status'] not in ['verified', 'failed', 'skipped']:
                        logger.error(f"无效的验证状态: {result['verification_status']}")
                        failed_count += 1
                        continue
                    
                    # 执行更新 - 使用新的更新方法
                    success = await trade_record_dao.update_trade_record(
                        record_id=result['id'],
                        update_data=update_data
                    )
                    
                    if success:
                        updated_count += 1
                        logger.debug(f"成功更新记录 {result['id']}")
                    else:
                        failed_count += 1
                        logger.warning(f"更新记录 {result['id']} 失败")
                        
                except Exception as e:
                    failed_count += 1
                    logger.error(f"更新记录 {result.get('id', 'unknown')} 失败: {str(e)}")
        
        execution_time = (datetime.now() - start_time).total_seconds()
        logger.info(f"更新完成，成功 {updated_count} 条，失败 {failed_count} 条，耗时 {execution_time:.2f}s")
        
        return {
            'updated_count': updated_count,
            'failed_count': failed_count,
            'execution_time': execution_time
        }
        
    except Exception as e:
        logger.error(f"更新验证结果失败: {str(e)}")
        logger.error(f"错误类型: {type(e).__name__}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        return {'updated_count': 0, 'failed_count': len(results)}


async def execute_trade_record_verification_workflow() -> Dict[str, Any]:
    """
    执行完整的交易记录验证工作流
    
    修复说明：执行基于GmgN API的统一验证流程
    
    Returns:
        工作流执行结果
    """
    try:
        start_time = datetime.now()
        logger.info("开始执行交易记录验证工作流（使用GmgN API）...")
        
        # 步骤1: 扫描待验证记录
        pending_records = await scan_pending_trade_records()
        
        if not pending_records:
            logger.info("没有待验证的记录，工作流结束")
            return {
                'status': 'completed',
                'scanned_count': 0,
                'verified_count': 0,
                'updated_count': 0,
                'execution_time': (datetime.now() - start_time).total_seconds()
            }
        
        # 步骤2: 验证交易金额（使用GmgN API）
        verification_results = []
        for record in pending_records:
            result = await verify_trade_amounts(record)
            verification_results.append(result)
        
        # 步骤3: 更新验证结果
        update_result = await update_verification_results(verification_results)
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        return {
            'status': 'completed',
            'scanned_count': len(pending_records),
            'verified_count': len([r for r in verification_results if r['verification_status'] == 'verified']),
            'updated_count': update_result['updated_count'],
            'failed_count': update_result['failed_count'],
            'execution_time': execution_time
        }
        
    except Exception as e:
        logger.error(f"执行工作流失败: {str(e)}")
        logger.error(f"错误类型: {type(e).__name__}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        return {
            'status': 'failed',
            'error_message': str(e),
            'execution_time': (datetime.now() - start_time).total_seconds()
        }


if __name__ == "__main__":
    # 测试运行
    async def main():
        await execute_trade_record_verification_workflow()
    
    asyncio.run(main())