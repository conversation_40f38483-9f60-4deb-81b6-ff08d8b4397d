"""
交易统计分析模块

提供基于链上验证数据的交易统计分析功能，包括：
- 交易对匹配和盈利计算
- 总体、分Token、分策略统计
- 盈利亏损排行榜
- HTML和JSON格式报告生成
"""

from .models import (
    TradePair,
    OverallStats,
    TokenStats,
    StrategyStats,
    ProfitRanking,
    LossRanking,
    StatisticsResult,
    StatisticsConfig,
    ReportFormat
)

from .trade_statistics_analyzer import TradeStatisticsAnalyzer
from .trade_pair_matcher import TradePairMatcher
from .statistics_calculator import StatisticsCalculator
from .chart_generator import ChartGenerator
from .html_report_generator import HTMLReportGenerator
from .json_report_generator import JSONReportGenerator

__all__ = [
    # 数据模型
    'TradePair',
    'OverallStats', 
    'TokenStats',
    'StrategyStats',
    'ProfitRanking',
    'LossRanking',
    'StatisticsResult',
    'StatisticsConfig',
    'ReportFormat',
    
    # 核心组件
    'TradeStatisticsAnalyzer',
    'TradePairMatcher',
    'StatisticsCalculator',
    'ChartGenerator',
    'HTMLReportGenerator',
    'JSONReportGenerator'
] 