# Bug修复记录

## Bug简要描述
SolanaMonitor.get_token_balance 方法在处理整数列表格式的代币账户数据时返回错误的余额0.0，导致"钱包代币余额不足"错误。

## 定位的根本原因
代码只能处理字典格式的 `parsed` 数据，无法处理Solana RPC返回的整数列表格式的原始字节数据。当遇到整数列表格式时，代码直接返回0.0。

## 实施的修复措施
1. **添加新方法** `_parse_spl_token_account_data`：专门解析整数列表格式的SPL Token账户数据
2. **修改主方法** `get_token_balance`：添加对整数列表格式的检测和处理分支
3. **保持向后兼容**：继续支持原有的字典格式数据处理
4. **增强错误处理**：改进了各种数据格式的处理逻辑

## 受影响的主要文件/模块
- `utils/spiders/solana/solana_monitor.py`：主要修改文件
- `test/utils/spiders/solana/test_solana_monitor_token_balance.py`：新增测试文件

## 相关测试用例
- `test_get_token_balance_with_integer_list_format_now_works`：验证修复后能正确处理整数列表格式
- `test_get_token_balance_with_parsed_format_works_correctly`：验证向后兼容性
- `test_parse_spl_token_account_data_correct_amount`：验证SPL Token数据解析的正确性
- `test_parse_spl_token_account_data_zero_amount`：验证零余额处理
- `test_parse_spl_token_account_data_insufficient_length`：验证边界情况

## 修复验证结果
✅ **所有单元测试通过**：8个测试用例全部通过
✅ **实际场景验证**：使用真实钱包和代币地址测试，成功获取余额159.147178
✅ **向后兼容性**：原有的字典格式数据处理逻辑保持不变
✅ **错误处理**：改进了异常情况的处理

## 修复前后对比
- **修复前**：整数列表格式数据 → 返回0.0（错误）
- **修复后**：整数列表格式数据 → 返回159.147178（正确）

## 技术细节
修复的核心是正确解析SPL Token账户结构中的金额字段：
- 位置64-71的8个字节存储代币数量（little endian格式）
- 原始数据：`[170, 100, 124, 9, 0, 0, 0, 0]` → *********
- 使用6位小数：********* / 10^6 = 159.147178

## 修复日期
2025-05-27T10:40:26+08:00

## 修复状态
✅ **已完成并验证** 