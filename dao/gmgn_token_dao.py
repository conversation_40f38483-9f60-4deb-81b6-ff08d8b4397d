from typing import List, Optional, Dict, Any, Type
from datetime import datetime, timedelta
from models.gmgn_token import (
    GmgnTokenBase,
    GmgnToken1m,
    GmgnToken5m,
    GmgnToken1h,
    GmgnToken6h,
    GmgnToken24h
)
from dao.base_dao import BaseDAO
import logging
import traceback
import json
from pymongo import UpdateOne

logger = logging.getLogger(__name__)

class GmgnTokenDAO:
    """GMGN代币数据访问对象"""
    
    TIME_RANGE_MODELS = {
        "1m": GmgnToken1m,
        "5m": GmgnToken5m,
        "1h": GmgnToken1h,
        "6h": GmgnToken6h,
        "24h": GmgnToken24h
    }
    
    def __init__(self):
        self.daos = {
            time_range: BaseDAO(model_class)
            for time_range, model_class in self.TIME_RANGE_MODELS.items()
        }
    
    async def bulk_upsert_tokens(self, tokens_data: List[Dict[str, Any]], time_range: str) -> int:
        """
        批量更新或插入代币数据
        使用批量写入操作而不是循环单个更新
        
        Args:
            tokens_data: 代币数据列表
            time_range: 时间范围（1m, 5m, 1h, 6h, 24h）
            
        Returns:
            int: 更新的文档数量
        """
        if not tokens_data:
            logger.warning("没有数据需要更新")
            return 0
            
        if time_range not in self.TIME_RANGE_MODELS:
            logger.error(f"无效的时间范围: {time_range}")
            return 0
            
        dao = self.daos[time_range]
        
        try:
            # 准备批量写入操作
            operations = []
            current_time = datetime.utcnow()
            
            for token_data in tokens_data:
                try:
                    # 确保基本字段存在
                    if not all(k in token_data for k in ['id', 'address']):
                        logger.warning(f"跳过无效的代币数据: {json.dumps(token_data)}")
                        continue
                    
                    # 将id字段重命名为token_id
                    if 'id' in token_data:
                        token_data['token_id'] = token_data.pop('id')
                    
                    # 添加更新时间
                    token_data['updated_at'] = current_time
                    
                    # 创建更新操作
                    operations.append(
                        UpdateOne(
                            {'token_id': token_data['token_id']},
                            {'$set': token_data},
                            upsert=True
                        )
                    )
                except Exception as e:
                    logger.error(f"处理单个代币数据时出错: {str(e)}\n{traceback.format_exc()}\n数据: {json.dumps(token_data)}")
                    continue
            
            if not operations:
                logger.warning("没有有效的更新操作")
                return 0
            
            logger.info(f"准备执行 {len(operations)} 个更新操作到 {time_range} 时间段")
            
            # 执行批量写入
            try:
                result = await dao.collection.bulk_write(operations, ordered=False)
                modified_count = result.modified_count + result.upserted_count
                logger.info(f"批量更新完成: 修改 {result.modified_count} 条，插入 {result.upserted_count} 条")
                return modified_count
            except Exception as e:
                logger.error(f"执行批量写入时出错: {str(e)}\n{traceback.format_exc()}")
                return 0
                
        except Exception as e:
            logger.error(f"批量更新失败: {str(e)}\n{traceback.format_exc()}")
            return 0
    
    async def find_hot_tokens(
        self,
        time_range: str,
        min_smart_degen_count: int = None,
        min_volume: float = None,
        max_market_cap: float = None,
        min_price_change: float = None,
        exclude_wash_trading: bool = True,
        limit: int = 50
    ) -> List[GmgnTokenBase]:
        """
        查找热门代币
        支持多个筛选条件的组合查询
        
        Args:
            time_range: 时间范围（1m, 5m, 1h, 6h, 24h）
            min_smart_degen_count: 最小智能投机者数量
            min_volume: 最小交易量
            max_market_cap: 最大市值
            min_price_change: 最小价格变化百分比
            exclude_wash_trading: 是否排除洗盘交易
            limit: 返回结果数量限制
            
        Returns:
            List[GmgnTokenBase]: 符合条件的代币列表
        """
        if time_range not in self.TIME_RANGE_MODELS:
            logger.error(f"无效的时间范围: {time_range}")
            return []
            
        dao = self.daos[time_range]
        filter_conditions = []
        
        if min_smart_degen_count is not None:
            filter_conditions.append({'smart_degen_count': {'$gte': min_smart_degen_count}})
            
        if min_volume is not None:
            filter_conditions.append({'volume': {'$gte': min_volume}})
            
        if max_market_cap is not None:
            filter_conditions.append({'market_cap': {'$lte': max_market_cap}})
            
        if min_price_change is not None:
            filter_conditions.append({'price_change_percent': {'$gte': min_price_change}})
            
        if exclude_wash_trading:
            filter_conditions.append({'is_wash_trading': False})
            
        filter_dict = {'$and': filter_conditions} if filter_conditions else {}
        
        return await dao.find_many(
            filter_dict=filter_dict,
            limit=limit,
            sort=[('smart_degen_count', -1), ('volume', -1)]
        )
    
    async def find_by_addresses(self, addresses: List[str], time_range: str) -> List[GmgnTokenBase]:
        """
        根据地址列表批量查询代币
        
        Args:
            addresses: 代币地址列表
            time_range: 时间范围（1m, 5m, 1h, 6h, 24h）
            
        Returns:
            List[GmgnTokenBase]: 代币列表
        """
        if time_range not in self.TIME_RANGE_MODELS:
            logger.error(f"无效的时间范围: {time_range}")
            return []
            
        dao = self.daos[time_range]
        return await dao.find_many({'address': {'$in': addresses}})
    
    async def find_recently_updated(
        self,
        time_range: str,
        minutes: int = 5,
        min_price_change: float = None
    ) -> List[GmgnTokenBase]:
        """
        查找最近更新的代币
        
        Args:
            time_range: 时间范围（1m, 5m, 1h, 6h, 24h）
            minutes: 最近多少分钟内更新的
            min_price_change: 最小价格变化百分比
            
        Returns:
            List[GmgnTokenBase]: 最近更新的代币列表
        """
        if time_range not in self.TIME_RANGE_MODELS:
            logger.error(f"无效的时间范围: {time_range}")
            return []
            
        dao = self.daos[time_range]
        filter_dict = {
            'updated_at': {'$gte': datetime.utcnow() - timedelta(minutes=minutes)}
        }
        
        if min_price_change is not None:
            filter_dict['price_change_percent'] = {'$gte': min_price_change}
            
        return await dao.find_many(
            filter_dict=filter_dict,
            sort=[('updated_at', -1)]
        )
    
    async def find_potential_gems(
        self,
        time_range: str,
        max_market_cap: float = 1000000,  # 100万市值以下
        min_holder_count: int = 100,      # 至少100个持有人
        min_liquidity: float = 10000,     # 至少1万流动性
        exclude_wash_trading: bool = True
    ) -> List[GmgnTokenBase]:
        """
        查找潜在的宝石代币
        
        Args:
            time_range: 时间范围（1m, 5m, 1h, 6h, 24h）
            max_market_cap: 最大市值
            min_holder_count: 最小持有人数
            min_liquidity: 最小流动性
            exclude_wash_trading: 是否排除洗盘交易
            
        Returns:
            List[GmgnTokenBase]: 潜在的宝石代币列表
        """
        if time_range not in self.TIME_RANGE_MODELS:
            logger.error(f"无效的时间范围: {time_range}")
            return []
            
        dao = self.daos[time_range]
        filter_dict = {
            'market_cap': {'$lte': max_market_cap},
            'holder_count': {'$gte': min_holder_count},
            'liquidity': {'$gte': min_liquidity},
            'is_show_alert': False
        }
        
        if exclude_wash_trading:
            filter_dict['is_wash_trading'] = False
            
        return await dao.find_many(
            filter_dict=filter_dict,
            sort=[('smart_degen_count', -1), ('volume', -1)]
        )
    
    async def get_token_stats(self, time_range: str) -> Dict[str, Any]:
        """
        获取代币统计信息
        
        Args:
            time_range: 时间范围（1m, 5m, 1h, 6h, 24h）
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        if time_range not in self.TIME_RANGE_MODELS:
            logger.error(f"无效的时间范围: {time_range}")
            return {}
            
        dao = self.daos[time_range]
        pipeline = [
            {
                '$group': {
                    '_id': None,
                    'total_tokens': {'$sum': 1},
                    'avg_market_cap': {'$avg': '$market_cap'},
                    'avg_volume': {'$avg': '$volume'},
                    'avg_holder_count': {'$avg': '$holder_count'},
                    'total_volume': {'$sum': '$volume'},
                }
            }
        ]
        
        result = await dao.collection.aggregate(pipeline).to_list(1)
        return result[0] if result else {} 