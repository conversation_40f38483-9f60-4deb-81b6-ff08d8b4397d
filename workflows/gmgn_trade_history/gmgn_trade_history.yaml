name: "GMGN 代币交易历史记录"
description: "定期获取链上的代币交易历史记录，并将数据存储到数据库"

nodes:
  - name: "TradeHistorySchedulerNode"
    node_type: "input"
    interval: 10
    generate_data: workflows.gmgn_trade_history.handler.generate_data
    flow_control:
      max_pending_messages: 10
      check_interval: 1
      enable_flow_control: true

  - name: "TradeHistoryMonitorNode"
    node_type: "process"
    depend_ons: ["TradeHistorySchedulerNode"]
    concurrency: 1
    interval: 1
    process_item: workflows.gmgn_trade_history.handler.process_trade_history

  - name: "TradeHistoryStoreNode"
    node_type: "storage"
    depend_ons: ["TradeHistoryMonitorNode"]
    batch_size: 1000  # 每批处理1000条数据
    store_data: workflows.gmgn_trade_history.handler.store_data
    validate: workflows.gmgn_trade_history.handler.validate 
