from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field
from beanie import Document
from enum import Enum


class SlippageAdjustmentReason(str, Enum):
    """滑点调整原因枚举"""
    SLIPPAGE_ERROR = "slippage_error"           # 滑点不足错误
    PRICE_IMPACT_ERROR = "price_impact_error"   # 价格影响过大错误
    INSUFFICIENT_OUTPUT = "insufficient_output"  # 输出金额不足错误
    MANUAL_ADJUSTMENT = "manual_adjustment"      # 手动调整
    MARKET_CONDITION = "market_condition"       # 市场条件调整


class SlippageAdjustmentRecord(Document):
    """滑点调整记录"""
    adjustment_time: datetime = Field(default_factory=datetime.utcnow, description="调整时间")
    reason: SlippageAdjustmentReason = Field(..., description="调整原因")
    trade_type: str = Field(..., description="交易类型 (buy/sell)")
    
    # 滑点变化记录
    previous_slippage: float = Field(..., description="调整前滑点(%)")
    new_slippage: float = Field(..., description="调整后滑点(%)")
    increment_applied: float = Field(..., description="实际增加的滑点(%)")
    
    # 错误信息
    original_error_message: Optional[str] = Field(default=None, description="触发调整的原始错误信息")
    retry_attempt: int = Field(..., description="重试次数")
    
    # 配置信息
    max_slippage_limit: float = Field(..., description="当前最大滑点限制(%)")
    increment_step: float = Field(..., description="配置的滑点增加步长(%)")
    
    @property
    def adjustment_summary(self) -> str:
        """调整摘要"""
        return f"{self.trade_type.upper()} 滑点从 {self.previous_slippage}% 调整至 {self.new_slippage}% (第{self.retry_attempt}次重试)"
    
    class Settings:
        name = "slippage_adjustment_records"


class RetryDecision(Document):
    """重试决策结果"""
    should_retry: bool = Field(..., description="是否应该继续重试")
    should_adjust_slippage: bool = Field(..., description="是否应该调整滑点")
    
    # 决策依据
    retry_count: int = Field(..., description="当前重试次数")
    max_retries: int = Field(..., description="最大重试次数")
    current_slippage: float = Field(..., description="当前滑点(%)")
    max_slippage: float = Field(..., description="最大滑点限制(%)")
    
    # 错误分析
    is_slippage_related_error: bool = Field(..., description="是否为滑点相关错误")
    error_message: Optional[str] = Field(default=None, description="错误信息")
    
    # 配置信息
    slippage_retry_enabled: bool = Field(..., description="滑点递增重试是否启用")
    
    # 决策原因
    decision_reason: str = Field(..., description="决策原因说明")
    
    @property
    def decision_summary(self) -> str:
        """决策摘要"""
        status = "继续重试" if self.should_retry else "停止重试"
        slippage_action = "并调整滑点" if self.should_adjust_slippage else "但不调整滑点"
        return f"{status}{slippage_action}: {self.decision_reason}"
    
    class Settings:
        name = "retry_decisions"


class SlippageRetryConfig(BaseModel):
    """滑点重试配置摘要（用于运行时配置传递）"""
    enabled: bool = Field(..., description="是否启用滑点递增重试")
    increment_percentage: float = Field(..., description="滑点增加步长(%)")
    max_slippage_percentage: float = Field(..., description="最大滑点限制(%)")
    retry_delay_seconds: float = Field(..., description="重试间隔(秒)")
    
    # 来源信息
    config_source: str = Field(default="unknown", description="配置来源")
    
    def __str__(self) -> str:
        status = "启用" if self.enabled else "禁用"
        return f"滑点重试{status} (步长:{self.increment_percentage}%, 上限:{self.max_slippage_percentage}%, 间隔:{self.retry_delay_seconds}s)" 