from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from models.gmgn_token_window import GmgnTokenWindow

class GmgnTokenWindowDAO:
    """GMGN代币多窗口信息DAO类
    
    提供以下功能：
    - 更新或插入代币信息
    - 批量更新或插入代币信息
    - 查询代币信息
    - 查询代币历史信息
    """
    
    @staticmethod
    async def upsert_token_window(chain: str, address: str, data: Dict[str, Any]) -> GmgnTokenWindow:
        """更新或插入代币多窗口信息
        
        Args:
            chain: 链名称
            address: 代币地址
            data: 代币数据
            
        Returns:
            GmgnTokenWindow: 更新后的代币信息
        """
        token_window = await GmgnTokenWindow.from_api_data(chain, data)
        await token_window.save()
        return token_window
    
    @staticmethod
    async def upsert_token_windows(chain: str, token_data_list: List[Dict[str, Any]]) -> List[GmgnTokenWindow]:
        """批量更新或插入代币多窗口信息
        
        Args:
            chain: 链名称
            token_data_list: 代币数据列表
            
        Returns:
            List[GmgnTokenWindow]: 更新后的代币信息列表
        """
        token_windows = []
        for data in token_data_list:
            token_window = await GmgnTokenWindow.from_api_data(chain, data)
            token_windows.append(token_window)
        
        await GmgnTokenWindow.insert_many(token_windows)
        return token_windows
    
    @staticmethod
    async def get_token_window(chain: str, address: str) -> Optional[GmgnTokenWindow]:
        """获取代币多窗口信息
        
        Args:
            chain: 链名称
            address: 代币地址
            
        Returns:
            Optional[GmgnTokenWindow]: 代币信息，如果不存在则返回None
        """
        return await GmgnTokenWindow.find_one({"chain": chain, "address": address})
    
    @staticmethod
    async def get_token_windows(chain: str, addresses: List[str]) -> List[GmgnTokenWindow]:
        """批量获取代币多窗口信息
        
        Args:
            chain: 链名称
            addresses: 代币地址列表
            
        Returns:
            List[GmgnTokenWindow]: 代币信息列表
        """
        return await GmgnTokenWindow.find({"chain": chain, "address": {"$in": addresses}}).to_list()
    
    @staticmethod
    async def get_token_window_history(
        chain: str,
        address: str,
        start_time: datetime,
        end_time: datetime
    ) -> List[GmgnTokenWindow]:
        """获取代币多窗口历史信息
        
        Args:
            chain: 链名称
            address: 代币地址
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            List[GmgnTokenWindow]: 代币历史信息列表
        """
        return await GmgnTokenWindow.find({
            "chain": chain,
            "address": address,
            "created_at": {
                "$gte": start_time,
                "$lte": end_time
            }
        }).sort("created_at", 1).to_list()
    
    @staticmethod
    async def get_latest_token_windows(chain: str, limit: int = 100) -> List[GmgnTokenWindow]:
        """获取最新的代币多窗口信息
        
        Args:
            chain: 链名称
            limit: 返回数量限制
            
        Returns:
            List[GmgnTokenWindow]: 代币信息列表
        """
        return await GmgnTokenWindow.find({"chain": chain}).sort("created_at", -1).limit(limit).to_list()
    
    @staticmethod
    async def get_hot_token_windows(chain: str, limit: int = 100) -> List[GmgnTokenWindow]:
        """获取热门代币多窗口信息
        
        Args:
            chain: 链名称
            limit: 返回数量限制
            
        Returns:
            List[GmgnTokenWindow]: 代币信息列表
        """
        return await GmgnTokenWindow.find({
            "chain": chain,
            "price.hot_level": {"$gt": 0}
        }).sort([
            ("price.hot_level", -1),
            ("created_at", -1)
        ]).limit(limit).to_list()
    
    @staticmethod
    async def get_token_windows_by_volume(
        chain: str,
        min_volume: float,
        time_window: str = "24h",
        limit: int = 100
    ) -> List[GmgnTokenWindow]:
        """根据交易量获取代币多窗口信息
        
        Args:
            chain: 链名称
            min_volume: 最小交易量
            time_window: 时间窗口，支持 1m/5m/1h/6h/24h
            limit: 返回数量限制
            
        Returns:
            List[GmgnTokenWindow]: 代币信息列表
        """
        volume_field = f"price.volume_{time_window}"
        return await GmgnTokenWindow.find({
            "chain": chain,
            volume_field: {"$gte": str(min_volume)}
        }).sort([
            (volume_field, -1),
            ("created_at", -1)
        ]).limit(limit).to_list()
    
    @staticmethod
    async def get_token_windows_by_price_change(
        chain: str,
        time_window: str = "24h",
        min_change_percent: float = 5.0,
        limit: int = 100
    ) -> List[GmgnTokenWindow]:
        """根据价格变化获取代币多窗口信息
        
        Args:
            chain: 链名称
            time_window: 时间窗口，支持 1m/5m/1h/6h/24h
            min_change_percent: 最小价格变化百分比
            limit: 返回数量限制
            
        Returns:
            List[GmgnTokenWindow]: 代币信息列表
        """
        pipeline = [
            {
                "$match": {
                    "chain": chain
                }
            },
            {
                "$addFields": {
                    "price_change": {
                        "$abs": {
                            "$multiply": [
                                {
                                    "$subtract": [
                                        {"$toDouble": "$price.price"},
                                        {"$toDouble": f"$price.price_{time_window}"}
                                    ]
                                },
                                100,
                                {
                                    "$cond": [
                                        {"$eq": [{"$toDouble": f"$price.price_{time_window}"}, 0]},
                                        0,
                                        {
                                            "$divide": [
                                                1,
                                                {"$toDouble": f"$price.price_{time_window}"}
                                            ]
                                        }
                                    ]
                                }
                            ]
                        }
                    }
                }
            },
            {
                "$match": {
                    "price_change": {"$gte": min_change_percent}
                }
            },
            {
                "$sort": {
                    "price_change": -1,
                    "created_at": -1
                }
            },
            {
                "$limit": limit
            }
        ]
        
        return await GmgnTokenWindow.aggregate(pipeline).to_list()
    
    @staticmethod
    async def cleanup_old_data(days: int = 7) -> int:
        """清理旧数据
        
        Args:
            days: 保留天数
            
        Returns:
            int: 删除的记录数
        """
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        result = await GmgnTokenWindow.delete_many({
            "created_at": {"$lt": cutoff_date}
        })
        return result.deleted_count 