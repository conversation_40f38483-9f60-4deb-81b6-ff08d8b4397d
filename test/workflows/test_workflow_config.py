import unittest
import asyncio
import logging
from unittest import mock
import os
import yaml

from utils.workflows.workflow_config import WorkflowConfigParser, DynamicInputNode, DynamicProcessNode, DynamicStorageNode
from utils.workflows.workflow import Workflow
from utils.workflows.nodes import InputNode, ProcessNode, StorageNode


class TestWorkflowConfigParser(unittest.TestCase):
    """WorkflowConfigParser类的单元测试"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.logger = logging.getLogger("TestWorkflowConfigParser")
        self.parser = WorkflowConfigParser()
        
        # 读取实际的YAML配置文件
        self.yaml_file_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
            "workflows", "gmgn_blue_chip_holders", "gmgn_blue_chip_holders_workflow.yaml"
        )
        
        # 如果实际文件不存在，使用内置的示例配置
        if not os.path.exists(self.yaml_file_path):
            self.yaml_content = """
name: "GMGN 代币蓝筹持有者记录"
description: "定期获取链上的蓝筹持有者记录，并将数据存储到数据库"

nodes:
  - name: "BlueChipHoldersSchedulerNode"
    node_type: "input"
    interval: 10
    generate_data: workflows.gmgn_token_link.handler.generate_data
    flow_control:
      max_pending_messages: 10
      check_interval: 1
      enable_flow_control: true

  - name: "BlueChipHoldersMonitorNode"
    node_type: "process"
    depend_ons: ["BlueChipHoldersSchedulerNode"]
    concurrency: 1
    interval: 1
    process_item: workflows.gmgn_blue_chip_holders.handler.process_blue_chip_holders

  - name: "BlueChipHoldersStoreNode"
    node_type: "storage"
    depend_ons: ["BlueChipHoldersMonitorNode"]
    batch_size: 1000  # 每批处理1000条数据
    store_data: workflows.gmgn_blue_chip_holders.handler.store_data
    validate: workflows.gmgn_blue_chip_holders.handler.validate 
"""
        else:
            with open(self.yaml_file_path, 'r', encoding='utf-8') as f:
                self.yaml_content = f.read()
        
        # 解析YAML内容为配置字典
        self.config = yaml.safe_load(self.yaml_content)
    
    def test_parse_yaml(self):
        """测试解析YAML内容"""
        # 调用被测方法
        result = self.parser.parse_yaml(self.yaml_content)
        
        # 验证结果
        self.assertIsInstance(result, dict)
        self.assertEqual(result["name"], "GMGN 代币蓝筹持有者记录")
        self.assertEqual(result["description"], "定期获取链上的蓝筹持有者记录，并将数据存储到数据库")
        self.assertIn("nodes", result)
        self.assertEqual(len(result["nodes"]), 3)
    
    def test_load_yaml_file(self):
        """测试从文件加载YAML配置"""
        # 只有在文件存在时才测试
        if os.path.exists(self.yaml_file_path):
            # 调用被测方法
            result = self.parser.load_yaml_file(self.yaml_file_path)
            
            # 验证结果
            self.assertIsInstance(result, dict)
            self.assertEqual(result["name"], "GMGN 代币蓝筹持有者记录")
            self.assertEqual(result["description"], "定期获取链上的蓝筹持有者记录，并将数据存储到数据库")
            self.assertIn("nodes", result)
            self.assertEqual(len(result["nodes"]), 3)
    
    @mock.patch("utils.workflows.workflow_config.importlib.import_module")
    def test_import_class_or_function(self, mock_import_module):
        """测试导入类或函数"""
        # 设置模拟返回值
        mock_module = mock.MagicMock()
        mock_function = mock.MagicMock()
        mock_module.generate_data = mock_function
        mock_import_module.return_value = mock_module
        
        # 调用被测方法
        result = self.parser.import_class_or_function("workflows.gmgn_token_link.handler.generate_data")
        
        # 验证结果
        self.assertEqual(result, mock_function)
        mock_import_module.assert_called_once_with("workflows.gmgn_token_link.handler")
    
    def test_determine_node_type(self):
        """测试确定节点类型"""
        # 测试输入节点
        input_node_config = {
            "name": "InputNode",
            "generate_data": "test.function"
        }
        self.assertEqual(self.parser._determine_node_type(input_node_config), "input")
        
        # 测试处理节点
        process_node_config = {
            "name": "ProcessNode",
            "process_item": "test.function"
        }
        self.assertEqual(self.parser._determine_node_type(process_node_config), "process")
        
        # 测试存储节点
        storage_node_config = {
            "name": "StorageNode",
            "store_data": "test.function"
        }
        self.assertEqual(self.parser._determine_node_type(storage_node_config), "storage")
        
        # 测试存储节点（使用validate关键字）
        storage_node_config_2 = {
            "name": "StorageNode",
            "validate": "test.function"
        }
        self.assertEqual(self.parser._determine_node_type(storage_node_config_2), "storage")
        
        # 测试无法确定类型的情况
        unknown_node_config = {
            "name": "UnknownNode"
        }
        with self.assertRaises(ValueError):
            self.parser._determine_node_type(unknown_node_config)
    
    def test_create_node_instance(self):
        """测试创建节点实例"""
        # 测试创建输入节点
        input_node = self.parser._create_node_instance("input", {"name": "InputNode"})
        self.assertIsInstance(input_node, InputNode)
        self.assertEqual(input_node.name, "InputNode")
        
        # 测试创建处理节点
        process_node = self.parser._create_node_instance("process", {"name": "ProcessNode"})
        self.assertIsInstance(process_node, ProcessNode)
        self.assertEqual(process_node.name, "ProcessNode")
        
        # 测试创建存储节点
        storage_node = self.parser._create_node_instance("storage", {"name": "StorageNode"})
        self.assertIsInstance(storage_node, StorageNode)
        self.assertEqual(storage_node.name, "StorageNode")
        
        # 测试不支持的节点类型
        with self.assertRaises(ValueError):
            self.parser._create_node_instance("unknown", {"name": "UnknownNode"})
    
    @mock.patch("utils.workflows.workflow_config.WorkflowConfigParser.import_class_or_function")
    def test_create_node_from_config(self, mock_import):
        """测试从配置创建节点"""
        # 模拟导入函数的返回值
        mock_function = mock.MagicMock()
        mock_import.return_value = mock_function
        
        # 测试创建输入节点
        input_node_config = {
            "name": "InputNode",
            "node_type": "input",
            "generate_data": "test.function",
            "interval": 10,
            "flow_control": {
                "max_pending_messages": 100,
                "check_interval": 5,
                "enable_flow_control": True
            }
        }
        input_node = self.parser.create_node_from_config(input_node_config)
        self.assertIsInstance(input_node, InputNode)
        self.assertEqual(input_node.name, "InputNode")
        self.assertEqual(input_node.interval, 10)
        self.assertEqual(input_node.max_pending_messages, 100)
        self.assertEqual(input_node.check_interval, 5)
        self.assertTrue(input_node.enable_flow_control)
        
        # 测试创建处理节点
        process_node_config = {
            "name": "ProcessNode",
            "node_type": "process",
            "process_item": "test.function",
            "concurrency": 5
        }
        process_node = self.parser.create_node_from_config(process_node_config)
        self.assertIsInstance(process_node, ProcessNode)
        self.assertEqual(process_node.name, "ProcessNode")
        
        # 测试创建存储节点
        storage_node_config = {
            "name": "StorageNode",
            "node_type": "storage",
            "store_data": "test.function",
            "validate": "test.validate_function",
            "batch_size": 1000
        }
        storage_node = self.parser.create_node_from_config(storage_node_config)
        self.assertIsInstance(storage_node, StorageNode)
        self.assertEqual(storage_node.name, "StorageNode")
        self.assertEqual(storage_node.batch_size, 1000)
    
    @mock.patch("utils.workflows.workflow_config.WorkflowConfigParser.import_class_or_function")
    def test_set_node_attributes(self, mock_import):
        """测试设置节点属性"""
        # 模拟导入函数的返回值
        mock_function = mock.MagicMock()
        mock_import.return_value = mock_function
        
        # 创建一个动态输入节点
        input_node = DynamicInputNode(name="InputNode")
        
        # 测试设置输入节点属性
        node_config = {
            "name": "InputNode",
            "node_type": "input",
            "generate_data": "test.function",
            "interval": 10,
            "flow_control": {
                "max_pending_messages": 100,
                "check_interval": 5,
                "enable_flow_control": True
            }
        }
        
        # 调用被测方法
        self.parser._set_node_attributes(input_node, node_config)
        
        # 验证结果
        mock_import.assert_called_with("test.function")
        self.assertEqual(input_node.interval, 10)
        self.assertEqual(input_node.max_pending_messages, 100)
        self.assertEqual(input_node.check_interval, 5)
        self.assertTrue(input_node.enable_flow_control)
    
    @mock.patch('utils.workflows.workflow_config.WorkflowConfigParser._set_node_attributes')
    @mock.patch('utils.workflows.workflow_config.WorkflowConfigParser._create_node_instance')
    def test_create_workflow_from_config(self, mock_create_node_instance, mock_set_node_attributes):
        # Mock _create_node_instance to return a mock node with necessary attributes
        def side_effect_create_node(node_type, node_config):
            # node_mock = mock.MagicMock()
            # Instead of a bare MagicMock, use spec based on actual Node classes
            # This helps catch attribute errors earlier if the spec is violated.
            node_name = node_config.get('name')
            if node_type.lower() == "input":
                # spec=InputNode might be too restrictive if DynamicInputNode has different attrs
                # For now, ensuring 'name' and 'output_queues' should be enough for connection logic.
                node_mock = mock.MagicMock(spec_set=['name', 'output_queues', 'input_queue', 'logger', 'status', 'error', '_stop', 'concurrency', 'parallel_control', 'message_manager', 'task_check_interval', 'max_pending_messages', 'check_interval', 'enable_flow_control', 'interval', 'setup_flow_controller', 'process', 'setup_node_status', 'setup_parallel_control', 'setup_message_manager', 'setup', '_retry_on_error', '_finalize', 'wrap_process', '_process_loop', '_handle_fatal_error', '_cleanup', 'run', 'send_data_to_output_queue', 'receive_message', 'receive_messages', 'generate_data_func', 'is_async_generator_func', 'is_generator_func', 'batch_size', 'enable_streaming', 'retry_count', 'max_retry_count', 'retry_interval', 'timeout'])
            elif node_type.lower() == "process":
                node_mock = mock.MagicMock(spec_set=['name', 'output_queues', 'input_queue', 'logger', 'status', 'error', '_stop', 'concurrency', 'parallel_control', 'message_manager', 'task_check_interval', 'max_pending_messages', 'check_interval', 'enable_flow_control', 'interval', 'setup_flow_controller', 'process', 'setup_node_status', 'setup_parallel_control', 'setup_message_manager', 'setup', '_retry_on_error', '_finalize', 'wrap_process', '_process_loop', '_handle_fatal_error', '_cleanup', 'run', 'send_data_to_output_queue', 'receive_message', 'receive_messages', 'process_item_func', 'is_async_generator_func', 'is_generator_func', 'batch_size', 'enable_streaming', 'retry_count', 'max_retry_count', 'retry_interval', 'timeout'])
            elif node_type.lower() == "storage":
                # StorageNode typically doesn't have output_queues for workflow connections
                node_mock = mock.MagicMock(spec_set=['name', 'input_queue', 'output_queues', 'logger', 'status', 'error', '_stop', 'concurrency', 'parallel_control', 'message_manager', 'task_check_interval', 'max_pending_messages', 'check_interval', 'enable_flow_control', 'interval', 'setup_flow_controller', 'process', 'setup_node_status', 'setup_parallel_control', 'setup_message_manager', 'setup', '_retry_on_error', '_finalize', 'wrap_process', '_process_loop', '_handle_fatal_error', '_cleanup', 'run', 'send_data_to_output_queue', 'receive_message', 'receive_messages', 'validate_data_func', 'store_data_func', 'batch_size', 'enable_streaming', 'retry_count', 'max_retry_count', 'retry_interval', 'timeout'])
            else:
                node_mock = mock.MagicMock(spec_set=['name', 'output_queues', 'input_queue', 'logger', 'status', 'error', '_stop', 'concurrency', 'parallel_control', 'message_manager', 'task_check_interval', 'max_pending_messages', 'check_interval', 'enable_flow_control', 'interval', 'setup_flow_controller', 'process', 'setup_node_status', 'setup_parallel_control', 'setup_message_manager', 'setup', '_retry_on_error', '_finalize', 'wrap_process', '_process_loop', '_handle_fatal_error', '_cleanup', 'run', 'send_data_to_output_queue', 'receive_message', 'receive_messages', 'retry_count', 'max_retry_count', 'retry_interval', 'timeout'])
            
            node_mock.name = node_name
            # Ensure 'output_queues' is present for nodes that can be a source
            # Workflow.connect_nodes expects source_node to have .output_queues.append()
            node_mock.output_queues = [] 
            # ProcessNode and OutputNode (StorageNode) need an input_queue attribute
            node_mock.input_queue = None 
            # Add a basic logger mock to avoid AttributeError if node.logger is accessed
            node_mock.logger = mock.MagicMock(spec_set=['debug', 'info', 'warning', 'error', 'critical', 'exception', 'log'])

            # Mock setup_flow_controller as it's called in connect_nodes
            node_mock.setup_flow_controller = mock.MagicMock()
            return node_mock

        mock_create_node_instance.side_effect = side_effect_create_node
        # Mock _set_node_attributes to do nothing as we are testing node creation and connection
        mock_set_node_attributes.return_value = None

        workflow = self.parser.create_workflow_from_config(self.config)

        self.assertIsInstance(workflow, Workflow)
        self.assertEqual(workflow.name, "GMGN 代币蓝筹持有者记录")
        self.assertEqual(workflow.description, "定期获取链上的蓝筹持有者记录，并将数据存储到数据库")
        
        # 验证节点创建次数
        self.assertEqual(mock_create_node_instance.call_count, 3)
        
        # 验证节点添加方法被调用
        self.assertGreater(len(workflow.nodes), 0)

if __name__ == "__main__":
    unittest.main() 