# KOL活动监控工作流 - GMGN自动交易功能 - 详细需求规格

## 1. 概述

本需求旨在为现有的KOL活动监控工作流增加自动交易功能。当系统根据预设策略生成买入信号时，除了向用户发送Telegram通知外，还应能自动调用GMGN Solana交易API执行实际的代币购买操作。交易的结果（成功、失败、交易哈希等）应记录下来，并可选择性地包含在发送给用户的通知中。

## 2. 功能需求

### 2.1. 自动交易执行
- **触发条件**: 当 `workflows/monitor_kol_activity/handler.py` 中的 `send_message_to_channel` 函数处理一个有效的买入信号时，在发送Telegram通知前（或可配置为之后）触发自动交易逻辑。
- **交易API**: 使用GMGN Solana交易API ([https://docs.gmgn.ai/cn/he-zuo-api-ji-cheng-gmgn-solana-jiao-yi-api文档](https://docs.gmgn.ai/cn/he-zuo-api-ji-cheng-gmgn-solana-jiao-yi-api)) 执行交易。
- **交易标的**: 购买买入信号中指定的 `token_address`。
- **交易资产**: 使用SOL (`So11111111111111111111111111111111111111112`) 进行购买。
- **交易钱包**: 使用配置中指定的钱包私钥对应的钱包地址执行交易。
- **交易参数配置**:
    - `auto_trade_enabled` (bool): 是否启用自动交易功能。默认为 `False`。
    - `gmgn_api_host` (Optional[str]): GMGN API的主机地址 (例如: "https://gmgn.ai/defi/v2")。优先从各策略的具体配置中获取，若策略中未指定，则尝试从全局环境变量 `GMGN_API_HOST` 中读取。
    - `gmgn_private_key_env_var` (Optional[str]): 存储此策略交易私钥的环境变量名称。 (例如: "GMM_DEFAULT_WALLET_PK")
    - `gmgn_sol_wallet_address` (Optional[str]): 交易钱包的公钥（如果私钥有效则自动派生，或在提供时用于验证）。
    - `gmgn_auto_trade_buy_amount_sol` (float): 每次交易花费的SOL数量。(在`models/config.py`中为`gmgn_auto_trade_buy_amount_sol`)
    - `gmgn_buy_slippage_percentage` (float): 交易滑点百分比 (例如 `1.0` 代表 1.0%)。(在`models/config.py`中为`gmgn_buy_slippage_percentage`)
    - `gmgn_buy_priority_fee` (float): 优先费（SOL单位），用于加速交易。(在`models/config.py`中为`gmgn_buy_priority_fee`)

### 2.2. 自动买入交易流程
自动买入执行函数应遵循以下步骤：
1.  从配置中获取必要的交易参数（API host, 私钥对应的环境变量名, 钱包地址, 交易金额, 滑点等）。
2.  从环境变量中读取钱包私钥。
3.  调用GMGN API的"查询路由接入点" (`/defi/router/v1/sol/tx/get_swap_route`) 获取交易路由和原始交易数据。
4.  使用钱包私钥对原始交易数据进行签名（需在Python环境中实现Solana交易签名逻辑）。
5.  调用GMGN API的"提交交易接入点" (`/defi/router/v1/sol/tx/submit_signed_transaction`) 提交签名后的交易。
6.  获取交易哈希 (`tx_hash`) 和最后有效区块高度 (`last_valid_block_height`)。
7.  轮询GMGN API的"查询交易状态接入点" (`/defi/router/v1/sol/tx/get_transaction_status`) 直到交易成功 (`success: true`) 或过期 (`expired: true`) 或达到最大尝试次数。

### 2.3. 自动卖出功能 (新增)

#### 2.3.1. 自动卖出执行
- **触发条件**: 当 `workflows/monitor_kol_activity/sell_signal_handler.py` 中的 `process_sell_signal` 函数处理一个有效的卖出信号（该信号源自一个已执行自动买入的买入信号）时，触发自动卖出逻辑。
- **交易API**: 使用GMGN Solana交易API执行卖出操作。
- **交易标的**: 卖出与原始买入信号关联的代币 (`token_address`)。
- **目标资产**: 兑换回SOL (`So11111111111111111111111111111111111111112`)。
- **交易钱包**: 使用与自动买入时相同的钱包执行交易。
- **卖出模式**: 当收到此策略的卖出信号时，仅卖出与触发此卖出信号的**特定原始买入交易**相对应的代币数量。
- **卖出参数配置**:
    - `gmgn_enable_auto_sell` (bool): 是否启用对应买入信号的自动卖出功能。默认为 `False`。
    - `gmgn_sell_slippage_percentage` (float): 卖出交易的滑点百分比。(在`models/config.py`中为`gmgn_sell_slippage_percentage`)
    - `gmgn_sell_priority_fee` (float, 可选): 卖出交易的优先费。(在`models/config.py`中为`gmgn_sell_priority_fee`)

#### 2.3.2. 自动卖出交易流程
自动卖出执行函数应遵循以下步骤：
1.  从配置中获取当前策略的卖出相关参数（如滑点、优先费）。
2.  确认原始买入信号 (由 `buy_signal_ref_id` 关联) 存在，并获取其相关信息，特别是原始买入时所属的策略名称 (`strategy_name`) 和买入的代币地址。
3.  确定需要卖出的代币数量:
    a.  查找与原始买入信号 (`buy_signal_ref_id`) 关联的、状态为成功的买入 `TradeRecord`。
    b.  获取该买入 `TradeRecord` 中的实际买入数量 (`token_out_actual_amount`) 作为本次的计划卖出数量 (`amount_to_sell`)。
    c.  如果找不到对应的买入交易记录或该记录中 `token_out_actual_amount` 为空或小于等于0，则记录警告并跳过此次卖出（因为没有明确的买入数量可供卖出）。
    d.  如果确定的 `amount_to_sell` 小于一个合理的最小交易量（例如避免尘埃交易），可以考虑记录日志并跳过，或按原样尝试交易（取决于具体实现）。
4.  调用GMGN API的"查询路由接入点" (`/defi/router/v1/sol/tx/get_swap_route`) 获取卖出交易的路由（此时 `inputMint` 是要卖出的代币，`outputMint` 是SOL，`inAmount` 是上一步确定的 `amount_to_sell` 转换为API要求的单位）。
5.  使用钱包私钥对原始交易数据进行签名。
6.  调用GMGN API的"提交交易接入点" (`/defi/router/v1/sol/tx/submit_signed_transaction`) 提交签名后的交易。
7.  获取交易哈希 (`tx_hash`) 和最后有效区块高度 (`last_valid_block_height`)。
8.  轮询GMGN API的"查询交易状态接入点" (`/defi/router/v1/sol/tx/get_transaction_status`) 直到交易成功或过期。

### 2.4. 结果处理与通知 (更新)
- **交易日志**: 详细记录自动买入和自动卖出交易的每一步操作、API请求、API响应、签名过程、交易结果（成功/失败、交易哈希、错误信息）到系统日志。
- **交易记录**: 应为每一笔自动执行的买入和卖出交易创建独立的交易记录（新模型，见开发计划）。这些记录应包含交易渠道（例如 "gmgn"）、关联的 `Signal` ID (买入信号ID或卖出信号ID)、交易类型("buy"/"sell")、状态、交易哈希、时间戳、花费/获得的代币和数量等。
- **信号关联**:
    - 买入 `Signal` 应能关联到其触发的买入交易记录。
    - 卖出 `Signal` 应能关联到其触发的卖出交易记录，并且也应能追溯到原始的买入 `Signal` 和买入交易记录。
- **Telegram通知**:
    - 自动买入成功/失败时，Telegram通知中应包含相应信息。
    - 自动卖出成功/失败时，Telegram通知中应包含相应信息。
    - 是否在通知中包含交易详情（对买入和卖出）可以作为配置项。

### 2.5. 错误处理与重试机制 (细化)

系统应能健壮地处理自动交易（买入和卖出）过程中可能出现的各种异常情况。对于不同类型的错误，应采取不同的处理策略，包括记录日志、更新`TradeRecord`状态、尝试重试以及向管理员（或特定配置的Telegram用户）发送详细的错误通知。

#### 2.5.1. 通用错误处理原则
-   **日志记录**: 所有错误，无论是否可重试，都必须详细记录到系统日志中，包括错误类型、错误信息、发生时间、关联的信号ID、交易记录ID以及堆栈跟踪（如果适用）。
-   **`TradeRecord` 更新**: 
    -   对于任何导致交易无法进行的错误，关联的 `TradeRecord` 的 `status` 应更新为 `FAILED` 或 `MANUAL_INTERVENTION_REQUIRED`（视情况而定）。
    -   `error_message` 字段应记录具体的错误信息。
    -   `provider_response_raw` 字段应存储导致错误的API响应（如果错误发生在与外部API交互时）。
-   **Telegram 错误通知**: 关键的交易执行错误应通过Telegram通知相关人员（例如，管理员或特定配置的监控群组）。通知内容应清晰指出错误类型、影响的交易（代币、买/卖方向、策略）、发生时间以及建议的操作（如果明确）。此错误通知独立于给普通用户的信号通知。

#### 2.5.2. 常见错误类型及处理策略

**A. API 调用相关错误 (GMGN API)**
    -   **错误场景**:
        -   网络连接超时/失败 (`httpx.RequestError`)。
        -   API 返回非2xx状态码 (例如，4xx客户端错误如参数无效，5xx服务器端错误) (`httpx.HTTPStatusError`)。
        -   API 响应结构不符合预期 (例如，缺少关键字段)。
    -   **处理策略**:
        -   **可重试错误**: 对于临时的网络问题、GMGN API返回的特定可重试错误码（如502, 503, 504，或API文档中指明的可重试错误）以及 **HTTP 429 (Too Many Requests)** 错误，系统应实现有限次数的重试机制。
            -   重试次数: 可配置，例如 3-5 次。
            -   重试间隔: 可配置，例如采用指数退避策略 (e.g., 5s, 15s, 30s)。对于 HTTP 429 错误，间隔时间可能需要更长，并遵照API返回的 `Retry-After` 头部（如果提供）。
            -   **IP/代理切换 (针对HTTP 429)**: 如果系统架构支持（例如，交易服务组件借鉴了项目中 `BasicSpider` 的能力或实现了类似的IP/代理池管理），在遇到HTTP 429错误时，重试前应优先尝试切换到不同的出口IP/代理。
            -   `TradeRecord`状态: 在重试期间可保持 `PENDING`。
            -   重试耗尽后仍失败: `TradeRecord` 状态更新为 `FAILED`，记录最终错误，并发送Telegram错误通知。
        -   **不可重试错误**: 对于明确的客户端错误（如400 Bad Request, 401 Unauthorized, 403 Forbidden, 404 Not Found, **但不包括 429**）或API明确指示的永久性失败，不应重试。
            -   `TradeRecord` 状态立即更新为 `FAILED`。
            -   记录错误，发送Telegram错误通知。
        -   **实现说明参考**: 负责直接与GMGN API通信的组件（如 `GmgnTradeService`）在实现其HTTP请求逻辑时，应考虑借鉴或复用项目内已有的 `utils/spiders/smart_money/__init__.py` 中 `BasicSpider` 类提供的成熟的请求重试、User-Agent管理和代理切换机制，以增强对API速率限制和反爬策略的适应性。

**B. Solana 交易签名与广播错误 (本地或与RPC节点交互)**
    -   **错误场景**:
        -   无效的钱包私钥 (导致签名失败)。
        -   反序列化从GMGN获取的交易数据失败。
        -   签名交易对象失败。
        -   发送已签名交易到Solana网络时RPC节点错误 (连接问题、节点过载、特定RPC错误响应)。
        -   交易预检失败 (simulation failure)。
        -   交易在链上确认超时或失败 (例如，区块未包含该交易，或交易执行失败但未被GMGN API层面直接捕获其最终链上状态)。
    -   **处理策略**:
        -   **无效私钥/签名失败**: 此为严重配置错误。`TradeRecord` 状态更新为 `FAILED` (或 `MANUAL_INTERVENTION_REQUIRED`)。立即发送Telegram错误通知，建议检查钱包配置。不应重试。
        -   **RPC节点交互错误/广播失败**: 
            -   **可重试**: 临时的RPC节点连接问题或节点过载。可尝试有限次数的重试（如果可行，可考虑轮换到备用RPC节点，若已配置）。
            -   `TradeRecord`状态: 重试期间 `PENDING`，重试耗尽后 `FAILED` 并发送Telegram错误通知。
            -   **不可重试**: 若交易因明确的逻辑错误（如某些预检失败信息指示）而无法广播。`TradeRecord` 状态更新为 `FAILED`。发送Telegram错误通知。
        -   **链上确认超时/失败**: 如果交易已成功发送（获得交易哈希）但长时间未确认或最终在链上（通过轮询GMGN的`get_transaction_status`或直接查询Solana链）显示为失败。
            -   `TradeRecord` 状态更新为 `FAILED`，记录最后确认状态或错误。
            -   发送Telegram错误通知，包含交易哈希供手动查询。

**C. 资金/余额不足错误**
    -   **错误场景 (买入)**: 交易钱包中SOL余额不足以支付购买代币的费用及交易费。
    -   **错误场景 (卖出)**: 交易钱包中要卖出的代币余额不足（当尝试卖出的数量大于实际可用余额时）。
    -   **检测时机**: 主要依赖GMGN API调用（如 `get_swap_route` 或交易提交后返回相关错误）或Solana交易预检（simulation）阶段（如果GMGN API内部执行此类检查并返回相应错误）。系统本身不主动进行独立的余额预检查。
    -   **处理策略**:
        -   `TradeRecord` 状态更新为 `FAILED`，错误信息明确指出资金/余额不足。
        -   发送Telegram错误通知，提示检查钱包余额。
        -   通常不应重试，除非有机制在短时间内自动补充资金/代币。系统应避免因余额不足反复尝试。

**D. 内部逻辑/配置错误**
    -   **错误场景**:
        -   无法从配置中完整、正确地读取必要的交易参数。
        -   无法从环境变量中加载指定的钱包私钥。
        -   在确定卖出数量时，找不到关联的、成功的原始买入交易记录或买入数量无效。
    -   **处理策略**:
        -   `TradeRecord` 状态更新为 `FAILED` (或 `SKIPPED`，如果适用，例如找不到买入记录导致无法卖出，应记录清晰原因)。
        -   记录详细错误信息。
        -   发送Telegram错误通知，指出配置或内部逻辑问题，可能需要人工介入检查。
        -   通常不应重试。

#### 2.5.3. Telegram 错误通知内容
发送给管理员或监控群组的错误通知应至少包含：
-   **时间戳**: 错误发生的时间。
-   **错误级别**: 例如，警告, 错误, 严重。
-   **错误摘要**: 清晰概括错误类型 (例如，"GMGN API请求失败", "Solana交易签名失败", "资金不足")。
-   **关联信息**:
    -   策略名称 (`strategy_name`)
    -   信号类型 (买入/卖出)
    -   代币地址/名称
    -   交易记录ID (`TradeRecord.id`)
    -   信号ID (`Signal.id`)
-   **详细错误信息**: 从 `TradeRecord.error_message` 或系统日志中提取的关键错误内容。
-   **交易哈希**: (如果交易已发送到链上，无论最终成功与否)。
-   **建议操作**: (如果明确，例如 "请检查钱包配置中的私钥: [环境变量名]", "请检查钱包 [钱包地址] 的SOL余额", "GMGN API返回错误，请检查API状态或请求参数")。

#### 2.5.4. 对主信号通知流程的影响
-   如原需求所述，即使自动交易（买入或卖出）的任何环节失败（包括所有重试均失败后），系统仍应继续执行后续给普通用户的信号通知流程。
-   普通用户的信号通知内容应根据 `TradeRecord` 的最终状态进行调整，清晰地告知用户自动交易尝试的结果（例如，"自动买入尝试成功，交易哈希：[tx_hash]" 或 "自动买入尝试失败：网络超时，请关注后续手动操作可能" 或 "自动卖出已提交，交易哈希：[tx_hash]，请等待链上确认"）。通知措辞应简洁明了，避免过多技术细节，但需明确交易状态。

## 3. 非功能性需求

- **安全性**: 钱包私钥必须通过环境变量安全地提供给应用程序，不能硬编码或明文存储在配置文件中。
- **可配置性**: 自动交易功能本身及其关键参数必须是可配置的。
- **可靠性**: 交易执行和状态查询过程应具备一定的重试机制（针对可恢复的错误）。
- **日志完备性**: 提供充足的日志信息，方便问题排查和审计。

## 4. 外部依赖 (更新，使用 httpx)

- `httpx`: 用于异步HTTP API调用。
- `solana-py` (及其依赖 `solders`): 主要用于GMGN API流程中必要的客户端交易签名。如果GMGN API返回需客户端签名的原始交易数据 (`raw_tx`)，则此库用于本地签名。应尽量减少对 `solana-py` 的依赖，优先使用GMGN API自身提供的端点来获取如余额、交易状态等信息。若GMGN API无法提供所需信息，或在特定辅助场景下必须直接与Solana节点交互，则应谨慎评估是否有更稳定或更易于维护的替代方案，其次才考虑使用 `solana-py` 进行此类直接查询。尽量避免使用 `solana-py` 进行交易签名，优先使用GMGN API提供的端点。
- `python-dotenv`: 用于方便地从 `.env` 文件加载环境变量。

## 5. 未来考虑 (可选)

- 支持多种交易对（不仅仅是SOL <-> Token）。
- 更复杂的交易金额计算逻辑（例如基于风险、代币热度等）。
- 交易成功后的止盈止损策略集成（目前卖出信号依赖于超时或KOL行为，可扩展更精细的止盈止损）。
- 卖出时对于未能获取精确买入数量的更优处理策略。
- 引入可选的交易前余额检查机制，以更早地发现潜在的资金不足问题（可能需要直接与Solana RPC节点交互或依赖未来GMGN API的更新）。

## 6. 主动余额检查的描述

主动余额检查的描述被移除，因为它不再作为需求的一部分。 