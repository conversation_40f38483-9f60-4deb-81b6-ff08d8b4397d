name: "KOL Activity Timestamp Discrepancy Monitor"
description: "Monitors KOL wallet activity for significant discrepancies between 'updated_at' (UTC) and 'timestamp' (East Eight Unix ts) fields within the same record. Alerts after 3 consecutive checks with discrepancy > 60s."

nodes:
  - name: "CheckKOLTimestampDiscrepancyNode"
    node_type: "input" 
    interval: 10 # 每10秒执行一次
    generate_data: workflows.kol_activity_timestamp_discrepancy_monitor.handler.perform_kol_activity_timestamp_discrepancy_check_task 
    flow_control:
      max_pending_messages: 1 
      check_interval: 0.5   
      enable_flow_control: true 