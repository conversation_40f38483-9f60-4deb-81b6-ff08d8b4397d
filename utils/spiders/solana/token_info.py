from abc import ABC, abstractmethod
import asyncio
import decimal
import json
import subprocess
import traceback
from typing import Any, Dict, Optional
import logging

from dao.token_dao import TokenDAO
from models import init_db
from utils.spiders.smart_money import BasicSpider

class BasicTokenInfo(ABC):
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    async def get_token_info(self, address: str) -> Optional[Dict[str, Any]]:
        """获取代币信息"""
        pass


class FetchBySolscan(BasicTokenInfo):
    """
    使用solscan获取代币信息,这里是不带代理的
    """
    async def get_token_info(self, address: str) -> Optional[Dict[str, Any]]:
        """获取代币信息"""
        self.logger.info(f"获取代币信息: {address}")
        # 构建curl命令 - 不使用代理，直接请求
        curl_command = [
            'curl',
            f'https://api-v2.solscan.io/v2/account?address={address}',
            '-H', 'accept: application/json, text/plain, */*',
            '-H', 'accept-language: zh-CN,zh;q=0.9',
            '-H', 'cache-control: no-cache',
            '-b', 'cf_clearance=rKn5eu4dryT31o.iizV_SEEnhfYsE.47LS427PSnr2U-**********-*******-EP5i5LJSsvsS7Q9SZdeNRg09xX5SloOjM5pMu3dQudxYB21xfKIdKEAB4YPus3BLpTnuH8pv8naaF8JPgeq91TcC9yP4GNX2_GGvLW_FGDoOSbyVc.V9MC6ohkFdZ_.n1HDCY4ZSpMoyNRol8Fks5Fbj_mrIKRTCbZ3OKukAutIpAHmMfaQW.T9FVD6vspVnU_vSil6boeNH0HcqX2ExUl4ZvpLzAZytXNrHS6Bj10K560W6uC1ZJN2JLgPH8_QQl6wii55bFkNxIO_TEQTpUyv8iqIsCuVKA1DXH1VeqlqtdH5dtIQiIhIFlW2B.WTqJa0IVaZWaI0aK9w.zGcb2PVB753VQRLDw.6vhfEFuCU; _ga=GA1.1.**********.**********; _ga_PS3V7B7KV0=GS1.1.**********.1.0.**********.0.0.0',
            '-H', 'origin: https://solscan.io',
            '-H', 'pragma: no-cache',
            '-H', 'priority: u=1, i',
            '-H', 'referer: https://solscan.io/',
            '-H', 'sec-ch-ua: "Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
            '-H', 'sec-ch-ua-mobile: ?0',
            '-H', 'sec-ch-ua-platform: "macOS"',
            '-H', 'sec-fetch-dest: empty',
            '-H', 'sec-fetch-mode: cors',
            '-H', 'sec-fetch-site: same-site',
            '-H', 'sol-aut: zC-L7C0=FJqSzn=B9dls0fKaoQ581DYK52ORa3vL',
            '-H', 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            '-s'  # 静默模式，不显示进度信息
        ]
        # 使用子进程直接执行命令(同步版本，简化问题)
        result = subprocess.run(
            curl_command,
            capture_output=True,
            text=True,
            check=False  # 即使命令返回非零退出码也不抛出异常
        )
        
        if result.returncode != 0:
            self.logger.error(f"Curl命令执行失败，错误码: {result.returncode}")
            self.logger.error(f"错误信息: {result}")
            raise Exception(f"Curl命令执行失败，错误码: {result.returncode}")
        
        response_data = result.stdout.strip()
        data = json.loads(response_data)
        # 检查API响应状态
        if not data.get('success'):
            self.logger.error(f"API返回错误: {data.get('message', '未知错误')}")
            raise Exception(f"API返回错误: {data.get('message', '未知错误')}")

        # 记录成功响应
        self.logger.debug(f"Solscan返回数据: {json.dumps(data)[:1000]}...")
        
        # solscan这里包含2个接口版本，
        # 一个是first_mint_time+first_mint_tx版本
        # 一个是created_time+created_tx版本
        # 但是业务实际使用第一个版本，所以我们还需要转换这2个版本
        token_info = data["data"]
        if token_info.get("first_mint_time") is None and token_info.get("created_time") is not None:
            token_info["first_mint_tx"] = token_info["created_tx"]
            token_info["first_mint_time"] = token_info["created_time"]
        
        return token_info


class FetchByGmgn(BasicSpider, BasicTokenInfo):
    """
    使用gmgn获取代币信息,带代理,但是这里获取的是池子的创建信息。只有在别的方式获取不到,才使用这个方式来获取
    """
    
    def __init__(self, max_retries: int = 3, retry_interval: float = 1.0):
        super().__init__(max_retries=max_retries, retry_interval=retry_interval)
        self.headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-TW;q=0.6',
            'cache-control': 'no-cache',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://gmgn.ai/sol/token/dDO6ZdYE_AJa66Ze7dJ3jTZp1uo8ymNXyThBnVTKqP7rYTgSjWdjG',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
    
    async def setup(self):
        """设置爬虫，初始化会话并更新头信息"""
        await self.init_sessions()
        # 更新会话头信息
        self.session.headers.update(self.headers)
        # 设置浏览器模拟
        self.session.impersonate = "chrome110"  # 模拟Chrome浏览器
    
    async def get_token_info(self, address: str, chain: str = "sol") -> Optional[Dict[str, Any]]:
        """获取代币信息"""
        data = {
            "chain": chain,
            "addresses": [
                address
            ]
        }
        params = {
            "device_id": "65990bc3-0a13-4d99-8c66-eeea94916b0f",
            "client_id": "gmgn_web_2025.0414.203636",
            "from_app": "gmgn",
            "app_ver": "2025.0414.203636",
            "tz_name": "Asia/Shanghai",
            "tz_offset": "28800",
            "app_lang": "zh-CN",
            "fp_did": "a7a28f6bc1de9c2bf214c1036f907cc6",
            "os": "web",
        }
        url = "https://gmgn.ai/api/v1/mutil_window_token_info"
        response = await self.post(url, json=data, params=params)
        if response.status_code != 200:
            self.logger.error(f"GMGN API返回错误: {response.status_code}")
            raise Exception(f"GMGN API返回错误: {response.status_code}, {response.text}")
        
        data = response.json()["data"][0]
        
        token_info = {
            "address": address,
            "name": data["name"],
            "symbol": data["symbol"],
            "decimals": data["decimals"],
            "supply": data["total_supply"],
            "creator": data["dev"]["creator_address"],
            "created_time": data["creation_timestamp"],
            "first_mint_time": data["creation_timestamp"],
            "GmgnToken1m": float(data["price"]["price_1m"]),
            "GmgnToken5m": float(data["price"]["price_5m"]),
            "GmgnToken1h": float(data["price"]["price_1h"]),
            "GmgnToken6h": float(data["price"]["price_6h"]),
            "GmgnToken24h": float(data["price"]["price_24h"]),
        }
        
        return token_info
    

class ByDatabase(BasicTokenInfo):
    """
    使用数据库获取代币信息
    """
    async def get_token_info(self, address: str) -> Optional[Dict[str, Any]]:
        """获取代币信息"""
        token_dao = TokenDAO()
        token = await token_dao.find_by_address(address)
        if token and token.get("first_mint_tx"):
            return token
        return None
    
    async def upsert_token_info(self, address: str, token_info: Dict[str, Any]):
        """更新代币信息"""
        token_dao = TokenDAO()
        await token_dao.update_token(address, token_info)


class TokenInfo:
    """
    代币信息
    """
    
    get_token_info_orders = [
        ByDatabase(),
        FetchByGmgn(),
        FetchBySolscan(),
    ]
    
    def __init__(self, address: str, chain: str = "sol"):
        self.address = address
        self.chain = chain
        self.logger = logging.getLogger(self.__class__.__name__)
        
    async def get_token_info(self) -> Optional[Dict[str, Any]]:
        """获取代币信息"""
        try:
            for order in self.get_token_info_orders:
                try:
                    token_info = await order.get_token_info(self.address)
                    if token_info:
                        await ByDatabase().upsert_token_info(self.address, token_info)
                        return token_info
                except Exception as e:
                    self.logger.info(f"获取代币信息失败: {e}-----{order.__class__.__name__}")
            return None
        except Exception as e:
            self.logger.info(f"获取代币信息失败: {e}\n{traceback.format_exc()}")
            return None


if __name__ == "__main__":
    async def main():
        await init_db()
        token_info = await TokenInfo("DkBaMrnQFUyx3u7jdgfacFsyFABUApgkgtwTUdThHNmu").get_token_info()
        print(token_info)
    asyncio.run(main())
