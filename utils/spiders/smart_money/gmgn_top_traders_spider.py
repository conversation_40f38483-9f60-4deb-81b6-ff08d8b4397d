import logging
import asyncio
from typing import Dict, Any, List
import json
from utils.spiders.smart_money import BasicSpider

logger = logging.getLogger(__name__)

class GmgnTopTradersSpider(BasicSpider):
    """GMGN顶级交易者爬虫
    
    用于获取代币的顶级交易者信息，包括：
    - 交易者地址
    - 持仓数量和价值
    - 买入/卖出数量和金额
    - 利润和收益率
    - 交易者标签
    - 活跃时间
    """
    
    BASE_URL = "https://gmgn.ai/api/v1/tokens/top_traders"
    
    def __init__(self, max_retries: int = 3, retry_interval: float = 1.0):
        super().__init__(max_retries=max_retries, retry_interval=retry_interval)
        self.headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-TW;q=0.6',
            'cache-control': 'no-cache',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://gmgn.ai/sol/token/dDO6ZdYE_6p6xgHyF7AeE6TZkSmFsko444wqoP15icUSqi2jfGiPN',
            'sec-ch-ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-ch-ua-arch': '"arm"',
            'sec-ch-ua-full-version': '"134.0.6998.44"',
            'sec-ch-ua-platform-version': '"14.5.0"',
            'sec-ch-ua-bitness': '"64"',
            'sec-ch-ua-model': '""',
            'sec-ch-ua-full-version-list': '"Chromium";v="134.0.6998.44", "Not:A-Brand";v="********", "Google Chrome";v="134.0.6998.44"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
    
    async def setup(self):
        """设置爬虫，初始化会话并更新头信息"""
        await self.init_sessions()
        # 更新会话头信息
        self.session.headers.update(self.headers)
        # 设置浏览器模拟
        self.session.impersonate = "chrome110"  # 模拟Chrome浏览器
    
    async def get_top_traders(self, chain: str, address: str, tag: str = "smart_degen", 
                        orderby: str = "realized_profit", direction: str = "desc") -> List[Dict[str, Any]]:
        """
        获取代币的顶级交易者信息
        
        Args:
            chain: 链名称，如"sol"
            address: 代币地址
            tag: 交易者标签，默认为"smart_degen"
            orderby: 排序字段，默认为"realized_profit"
            direction: 排序方向，默认为"desc"
            
        Returns:
            List[Dict[str, Any]]: 顶级交易者信息列表
        """
        try:
                
            url = f"{self.BASE_URL}/{chain}/{address}"
            logger.info(f"正在获取代币顶级交易者信息: {url}, tag={tag}, orderby={orderby}, direction={direction}")
            
            # 使用带有重试机制的get方法
            response = await self.get(url, params={
                'device_id': '65990bc3-0a13-4d99-8c66-eeea94916b0f',
                'client_id': 'gmgn_web_2025.0311.180038',
                'from_app': 'gmgn',
                'app_ver': '2025.0311.180038',
                'tz_name': 'Asia/Shanghai',
                'tz_offset': '28800',
                'app_lang': 'zh-CN',
                'orderby': orderby,
                'direction': direction,
                'tag': tag
            })
            
            # 记录响应状态和内容
            logger.debug(f"API响应状态码: {response.status_code}")
            logger.debug(f"API响应内容: {response.text[:500]}...")  # 只记录前500个字符，避免日志过大
            
            # 检查响应状态
            if response.status_code != 200:
                logger.error(f"获取代币顶级交易者信息失败: HTTP {response.status_code}")
                logger.error(f"错误响应: {response.text}")
                return []
                
            # 解析响应数据
            try:
                data = response.json()
                
                # 检查API响应格式
                if not isinstance(data, dict):
                    logger.error(f"API响应格式错误，期望dict类型，实际为: {type(data)}")
                    return []
                    
                # 检查API响应状态
                if data.get('code') != 0:  # 0 表示成功
                    logger.error(f"API返回错误: {data.get('message', '未知错误')}")
                    return []
                    
                # 提取实际数据
                traders = data.get('data', [])
                if not traders:
                    logger.warning("API响应中没有交易者数据")
                    return []
                
                # 格式化数据
                formatted_traders = []
                for trader in traders:
                    formatted_trader = {
                        'address': trader.get('address', ''),
                        'token_address': address,
                        'account_address': trader.get('account_address', ''),
                        'amount_cur': trader.get('amount_cur', 0),
                        'usd_value': trader.get('usd_value', 0),
                        'cost_cur': trader.get('cost_cur', 0),
                        'sell_amount_cur': trader.get('sell_amount_cur', 0),
                        'sell_amount_percentage': trader.get('sell_amount_percentage', 0),
                        'sell_volume_cur': trader.get('sell_volume_cur', 0),
                        'buy_volume_cur': trader.get('buy_volume_cur', 0),
                        'buy_amount_cur': trader.get('buy_amount_cur', 0),
                        'netflow_usd': trader.get('netflow_usd', 0),
                        'netflow_amount': trader.get('netflow_amount', 0),
                        'buy_tx_count_cur': trader.get('buy_tx_count_cur', 0),
                        'sell_tx_count_cur': trader.get('sell_tx_count_cur', 0),
                        'wallet_tag_v2': trader.get('wallet_tag_v2', ''),
                        'balance': trader.get('balance', '0'),
                        'profit': trader.get('profit', 0),
                        'realized_profit': trader.get('realized_profit', 0),
                        'profit_change': trader.get('profit_change', 0),
                        'amount_percentage': trader.get('amount_percentage', 0),
                        'unrealized_profit': trader.get('unrealized_profit', 0),
                        'unrealized_pnl': trader.get('unrealized_pnl', 0),
                        'avg_cost': trader.get('avg_cost', 0),
                        'avg_sold': trader.get('avg_sold', 0),
                        'tags': trader.get('tags', []),
                        'maker_token_tags': trader.get('maker_token_tags', []),
                        'name': trader.get('name', ''),
                        'avatar': trader.get('avatar', ''),
                        'twitter_username': trader.get('twitter_username', ''),
                        'twitter_name': trader.get('twitter_name', ''),
                        'tag_rank': trader.get('tag_rank', {}),
                        'last_active_timestamp': trader.get('last_active_timestamp', 0),
                        'created_at': trader.get('created_at', 0),
                        'start_holding_at': trader.get('start_holding_at'),
                        'end_holding_at': trader.get('end_holding_at'),
                        'is_suspicious': trader.get('is_suspicious', False),
                        'is_new': trader.get('is_new', False),
                        'transfer_in': trader.get('transfer_in', False)
                    }
                    
                    # 添加原生转账信息（如果有）
                    if 'native_transfer' in trader:
                        formatted_trader['native_transfer'] = trader['native_transfer']
                    
                    formatted_traders.append(formatted_trader)
                
                logger.info(f"成功获取代币顶级交易者信息，共{len(formatted_traders)}条记录")
                return formatted_traders
                
            except json.JSONDecodeError as e:
                logger.error(f"解析API响应JSON失败: {str(e)}")
                logger.error(f"原始响应: {response.text[:500]}...")  # 只记录前500个字符
                return []
                
        except Exception as e:
            logger.error(f"获取代币顶级交易者信息时发生错误: {str(e)}")
            return []

async def main():
    spider = GmgnTopTradersSpider()
    # 获取SOL链上特定代币的smart_degen标签的顶级交易者，按已实现利润降序排列
    traders = await spider.get_top_traders(
        chain="sol", 
        address="6p6xgHyF7AeE6TZkSmFsko444wqoP15icUSqi2jfGiPN",
        tag="smart_degen",
        orderby="realized_profit",
        direction="desc"
    )
    print(f"获取到{len(traders)}个顶级交易者")
    if traders:
        # 打印前3个交易者的基本信息
        for i, trader in enumerate(traders[:3]):
            print(f"\n交易者 {i+1}:")
            print(f"地址: {trader['address']}")
            print(f"账户地址: {trader['account_address']}")
            print(f"当前持有量: {trader['amount_cur']}")
            print(f"已实现利润: {trader['realized_profit']}")
            print(f"标签: {', '.join(trader['tags'])}")
            print(f"Twitter: {trader['twitter_username']}")
    
    # 关闭爬虫会话连接
    await spider.close()

if __name__ == "__main__":
    # 使用asyncio运行异步主函数
    asyncio.run(main()) 