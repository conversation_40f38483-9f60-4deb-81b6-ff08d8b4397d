from typing import Optional
from redis import Redis
from pydantic import BaseModel, Field
from contextlib import contextmanager
import os


class RedisSettings(BaseModel):
    """Redis连接配置"""
    host: str = Field(default="localhost", description="Redis主机地址")
    port: int = Field(default=6379, description="Redis端口")
    db: int = Field(default=0, description="Redis数据库编号")
    password: Optional[str] = Field(default=None, description="Redis密码")
    socket_timeout: int = Field(default=5, description="Socket超时时间")
    socket_connect_timeout: int = Field(default=5, description="连接超时时间")
    retry_on_timeout: bool = Field(default=True, description="超时重试")


class RedisConnector:
    def __init__(self, settings: RedisSettings):
        self.settings = settings
        self._client: Optional[Redis] = None
        self._initialized = False

    @property
    def client(self) -> Redis:
        if not self._client:
            raise RuntimeError("Redis client not initialized. Please use within context manager.")
        return self._client

    def connect(self):
        """建立Redis连接"""
        try:
            self._client = Redis(
                host=self.settings.host,
                port=self.settings.port,
                db=self.settings.db,
                password=self.settings.password,
                socket_timeout=self.settings.socket_timeout,
                socket_connect_timeout=self.settings.socket_connect_timeout,
                retry_on_timeout=self.settings.retry_on_timeout
            )
            # 验证连接
            self._client.ping()
            self._initialized = True
        except Exception as e:
            self.disconnect()
            raise ConnectionError(f"Failed to connect to Redis: {str(e)}")

    def disconnect(self):
        """关闭Redis连接"""
        if self._client:
            self._client.close()
            self._client = None
        self._initialized = False

    def __enter__(self):
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.disconnect()


@contextmanager
def get_redis(settings: RedisSettings):
    """Redis连接器的上下文管理器工厂函数"""
    connector = RedisConnector(settings)
    try:
        connector.connect()
        yield connector
    finally:
        connector.disconnect()


# Celery Task 基类

class RedisTask():
    _redis = None
    
    @property
    def redis(self):
        if self._redis is None:
            self._redis = Redis(
                host=os.getenv('REDIS_HOST', 'localhost'),
                port=int(os.getenv('REDIS_PORT', 6379)),
                db=int(os.getenv('REDIS_DB', 0)),
                password=os.getenv('REDIS_PASSWORD')
            )
        return self._redis
    
    def __del__(self):
        """清理资源"""
        if self._redis:
            self._redis.close() 