import logging
import json
import csv
import os
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime

# 导入凯利计算器标准实现
from utils.backtest_analysis.kelly_calculator import calculate_kelly

logger = logging.getLogger("ResultAnalyzer")


class ResultAnalyzer:
    """结果分析器，负责分析回测结果和生成报告"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """初始化结果分析器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        
    def calculate_statistics(self, portfolio) -> Dict[str, Any]:
        """计算回测统计数据
        
        Args:
            portfolio: 投资组合对象
            
        Returns:
            Dict[str, Any]: 统计数据
        """
        trade_history = portfolio.trade_history
        
        if not trade_history:
            return {
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'win_rate': 0,
                'return_rate': 0,
                'max_drawdown': 0,
                'sharpe_ratio': 0,
                'positions_count': 0,
                'total_realized_pnl': 0,
                'total_unrealized_pnl': 0,
                'total_invested': 0,
                'final_value': 0,
                'max_profit_per_trade': 0,
                'kelly_fraction_calculated': 0
            }
            
        # 构建交易分析数据
        trades_df = pd.DataFrame(trade_history)
        
        # 计算交易数量
        buy_trades = trades_df[trades_df['type'] == 'BUY']
        sell_trades = trades_df[trades_df['type'] == 'SELL']
        total_trades = len(sell_trades)  # 只计算完整的交易，即卖出次数
        
        # 计算胜率
        if 'realized_pnl' in trades_df.columns:
            sell_with_pnl = sell_trades.dropna(subset=['realized_pnl'])
            winning_trades = len(sell_with_pnl[sell_with_pnl['realized_pnl'] > 0])
            losing_trades = len(sell_with_pnl[sell_with_pnl['realized_pnl'] <= 0])
            win_rate = winning_trades / len(sell_with_pnl) if len(sell_with_pnl) > 0 else 0
            
            # 计算总盈亏 (来自所有卖出交易)
            total_pnl = sell_with_pnl['realized_pnl'].sum() if len(sell_with_pnl) > 0 else 0

            # 计算收益率 (基于实际总投资额 portfolio.total_invested)
            actual_total_invested = portfolio.total_invested # 获取实际总投资
            return_rate = total_pnl / actual_total_invested if actual_total_invested > 0 else 0

            # --- Revised Max Drawdown & Max Profit per Trade Calculation ---
            max_drawdown = 0
            max_profit_per_trade = 0
            trade_return_rates = []

            if len(sell_with_pnl) > 0:
                for index, trade in sell_with_pnl.iterrows():
                    realized_pnl = trade['realized_pnl']
                    proceeds = trade.get('proceeds') # Proceeds = amount * price - commission

                    if proceeds is None:
                        # Fallback if proceeds not stored (should be)
                        amount = trade.get('amount', 0)
                        price = trade.get('price', 0)
                        commission = trade.get('commission', 0)
                        proceeds = amount * price - commission

                    # Calculate cost basis for this specific sell trade
                    # cost_basis = proceeds - realized_pnl
                    cost_basis = proceeds - realized_pnl

                    if cost_basis != 0:
                        # Calculate return rate based on actual cost basis
                        trade_return_rate = realized_pnl / cost_basis
                        trade_return_rates.append(trade_return_rate)
                    # else: If cost basis is 0 (e.g., gifted tokens sold?), skip this trade for rate calculation

                if trade_return_rates:
                    min_loss_rate = min(trade_return_rates)
                    max_profit_rate = max(trade_return_rates)
                    max_drawdown = abs(min(min_loss_rate, 0))
                    max_profit_per_trade = max(max_profit_rate, 0)
            # --- End Revised Calculation ---

            # 计算夏普比率（简化版）
            sharpe_ratio = 0
            if len(sell_trades) > 1 and 'realized_pnl' in trades_df.columns and trade_return_rates: # Use new rates
                # 计算平均收益率和标准差 (使用基于实际成本的收益率)
                mean_return = np.mean(trade_return_rates)
                std_return = np.std(trade_return_rates)

                # 计算简化版夏普比率
                # Assuming daily returns calculation approximation
                sharpe_ratio = (mean_return / std_return) * np.sqrt(252) if std_return > 0 else 0
            
            # 计算凯利系数 - 使用kelly_calculator的标准实现
            kelly_fraction = 0
            if winning_trades > 0 and losing_trades > 0:
                # 计算平均盈利和平均亏损
                winning_trades_df = sell_with_pnl[sell_with_pnl['realized_pnl'] > 0]
                losing_trades_df = sell_with_pnl[sell_with_pnl['realized_pnl'] <= 0]
                
                avg_win = winning_trades_df['realized_pnl'].mean()
                avg_loss = abs(losing_trades_df['realized_pnl'].mean())
                
                # 避免除以零
                if avg_loss > 0:
                    # 计算赔率 (avg_win / avg_loss)
                    r = avg_win / avg_loss
                    
                    # 使用标准凯利计算函数
                    raw_f, capped_f, status = calculate_kelly(win_rate, r)
                    
                    if capped_f is not None:
                        kelly_fraction = capped_f
                        logger.info(f"单次回测凯利分数计算 (原始值): {raw_f:.4f} (状态: {status}) - 已保存约束值")
                    else:
                        logger.warning(f"单次回测凯利分数计算失败: {status}")
                    
                    # 记录胜率是否达标
                    if win_rate >= 0.5:
                        logger.info(f"单次回测胜率 {win_rate:.2%} 达到或超过 50%")
                    else:
                        logger.info(f"单次回测胜率 {win_rate:.2%} 未超过 50%")
        else:
            winning_trades = 0
            losing_trades = 0
            win_rate = 0
            return_rate = 0
            total_pnl = 0
            max_drawdown = 0
            max_profit_per_trade = 0
            sharpe_ratio = 0
            kelly_fraction = 0
            
        # 计算最终价值
        # 最终价值 = 本金 + 盈亏，不能为负
        final_value = max(0, portfolio.total_invested + total_pnl)
            
        # 组合统计数据
        return {
            'total_trades': total_trades,  # 只计算完整的交易
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'return_rate': return_rate,
            'max_drawdown': max_drawdown,
            'max_profit_per_trade': max_profit_per_trade,
            'sharpe_ratio': sharpe_ratio,
            'positions_count': len(portfolio.positions),
            'total_realized_pnl': total_pnl,
            'total_unrealized_pnl': portfolio.total_unrealized_pnl,
            'total_invested': portfolio.total_invested,
            'final_value': max(0, portfolio.total_invested + total_pnl),
            'kelly_fraction_calculated': kelly_fraction
        }
        
    def export_to_json(self, portfolio, statistics: Dict[str, Any], file_path: str) -> None:
        """导出回测结果到JSON文件
        
        Args:
            portfolio: 投资组合对象
            statistics: 统计数据
            file_path: 文件路径
        """
        # 准备导出数据
        export_data = {
            'statistics': statistics,
            'config': self.config,
            'positions': self._prepare_positions_for_export(portfolio.positions),
            'trades': self._prepare_trades_for_export(portfolio.trade_history)
        }
        
        # 创建目录（如果不存在）
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # 导出到JSON文件
        with open(file_path, 'w') as f:
            json.dump(export_data, f, indent=4, default=str)
            
        logger.info(f"回测结果已导出到: {file_path}")
        
    def export_to_csv(self, portfolio, file_path, include_trades=True, include_positions=False):
        """导出回测结果到CSV文件"""
        # 检查交易历史是否为空
        if not portfolio.trade_history:
            logger.warning("交易历史为空，没有数据可以导出到CSV")
            return

        # 准备交易数据
        trades = self._prepare_trades_for_export(portfolio.trade_history)

        # 确保文件所在目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        if trades:
            # 处理KOL钱包列表 - 从token_info字段中提取并添加到每个交易记录中
            for trade in trades:
                # 处理买入KOL钱包
                kol_wallets = []
                if 'token_info' in trade and trade['token_info'] and 'kol_wallets' in trade['token_info']:
                    kol_wallets = trade['token_info']['kol_wallets']
                
                # 处理卖出KOL钱包
                sell_kol_wallets = []
                if 'token_info' in trade and trade['token_info'] and 'sell_kol_wallets' in trade['token_info']:
                    sell_kol_wallets = trade['token_info']['sell_kol_wallets']
                
                # 格式化KOL钱包字符串
                kol_wallets_str = self._format_kol_wallets(kol_wallets)
                sell_kol_wallets_str = self._format_kol_wallets(sell_kol_wallets)
                
                # 直接添加到交易记录中
                trade['buy_kol_wallets'] = kol_wallets
                trade['sell_kol_wallets'] = sell_kol_wallets
                trade['buy_kol_wallets_str'] = kol_wallets_str
                trade['sell_kol_wallets_str'] = sell_kol_wallets_str

            # 首先收集所有可能的字段名
            all_fields = set()
            for trade in trades:
                all_fields.update(trade.keys())
            
            # 移除token_info字段，因为它是一个复杂对象，不适合CSV格式
            if 'token_info' in all_fields:
                all_fields.remove('token_info')
            
            # 定义字段顺序的优先级
            priority_fields = [
                'token_symbol', 'token_address', 'type', 'buy_time', 'sell_time', 'timestamp',
                'holding_time_hours', 'amount', 'buy_price', 'sell_price', 'price',
                'cost', 'commission', 'proceeds', 'realized_pnl', 'roi', 'annualized_roi',
                'buy_signal_type', 'sell_signal_type', 'capital_after', 'total_value_after',
                'buy_kol_wallets_str', 'sell_kol_wallets_str'
            ]
            
            # 构建最终的字段列表
            fieldnames = []
            # 首先添加优先字段
            for field in priority_fields:
                if field in all_fields:
                    fieldnames.append(field)
                    all_fields.remove(field)
            
            # 添加剩余字段
            fieldnames.extend(sorted(all_fields))
            
            # 写入CSV文件
            with open(file_path, 'w', newline='') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                for trade in trades:
                    # 创建一个新字典，只包含fieldnames中的字段
                    row = {field: trade.get(field, '') for field in fieldnames}
                    writer.writerow(row)
            
            logger.info(f"交易历史已导出到: {file_path}")
        
        # 导出持仓信息到CSV（如有需要）
        if include_positions and portfolio.positions:
            positions_file_path = file_path.replace('.csv', '_positions.csv')
            positions = self._prepare_positions_for_export(portfolio.positions)
            
            with open(positions_file_path, 'w', newline='') as f:
                writer = csv.DictWriter(f, fieldnames=[
                    'token_symbol', 'token_address', 'amount', 'avg_price', 'current_price',
                    'cost', 'market_value', 'unrealized_pnl', 'unrealized_roi'
                ])
                writer.writeheader()
                for position in positions:
                    writer.writerow(position)
            
            logger.info(f"持仓信息已导出到: {positions_file_path}")

    def _prepare_positions_for_export(self, positions: Dict[str, Any]) -> List[Dict[str, Any]]:
        """处理持仓信息以适合CSV导出"""
        export_positions = []
        
        for token_address, position in positions.items():
            # 处理Position对象而不是字典
            if hasattr(position, 'token_info'):
                token_info = position.token_info
                token_symbol = token_info.get('symbol', 'Unknown') if token_info else 'Unknown'
                
                export_position = {
                    'token_symbol': token_symbol,
                    'token_address': token_address,
                    'amount': position.amount,
                    'avg_price': position.cost / position.amount if position.amount > 0 else 0,
                    'current_price': position.current_price,
                    'cost': position.cost,
                    'market_value': position.current_value,
                    'unrealized_pnl': position.unrealized_pnl,
                    'unrealized_roi': f"{(position.unrealized_pnl / position.cost if position.cost > 0 else 0):.2%}"
                }
            else:
                # 兼容旧的字典格式
                token_info = position.get('token_info', {})
                token_symbol = token_info.get('symbol', 'Unknown')
                
                export_position = {
                    'token_symbol': token_symbol,
                    'token_address': token_address,
                    'amount': position.get('amount', 0),
                    'avg_price': position.get('avg_price', 0),
                    'current_price': position.get('current_price', 0),
                    'cost': position.get('cost', 0),
                    'market_value': position.get('market_value', 0),
                    'unrealized_pnl': position.get('unrealized_pnl', 0),
                    'unrealized_roi': f"{position.get('unrealized_roi', 0):.2%}"
                }
            
            export_positions.append(export_position)
            
        return export_positions
    
    def _format_kol_wallets(self, wallets: List[str]) -> str:
        """格式化KOL钱包列表为字符串
        
        Args:
            wallets: KOL钱包地址列表
            
        Returns:
            格式化后的字符串，限制显示5个地址，并显示总数
        """
        if not wallets:
            return ""
            
        # 只显示前5个钱包地址，并指示总数
        display_wallets = wallets[:5]
        wallet_count = len(wallets)
        
        if wallet_count > 5:
            return f"{', '.join(display_wallets)} (共{wallet_count}个)"
        else:
            return f"{', '.join(display_wallets)}"
            
    def _prepare_trades_for_export(self, trade_history):
        """
        准备交易记录以导出到CSV
        
        Args:
            trade_history: 交易历史记录
            
        Returns:
            处理后的交易记录列表
        """
        trades = []
        for trade in trade_history:
            # 直接复制原始交易数据，确保包含新添加的字段
            trade_copy = trade.copy()
            
            # 计算持仓时间（小时）
            if 'buy_time' in trade and 'sell_time' in trade:
                buy_time = trade['buy_time']
                sell_time = trade['sell_time']
                holding_time = (sell_time - buy_time) / 3600  # 转换为小时
                trade_copy['holding_time_hours'] = round(holding_time, 2)
                
            # 确保ROI为百分比形式
            if 'roi' in trade:
                trade_copy['roi'] = f"{trade['roi'] * 100:.2f}%"
                
            # 确保年化ROI为百分比形式
            if 'annualized_roi' in trade:
                trade_copy['annualized_roi'] = f"{trade['annualized_roi'] * 100:.2f}%"
                
            trades.append(trade_copy)
            
        return trades
        
    def plot_equity_curve(self, portfolio, file_path: str) -> None:
        """绘制资金曲线图
        
        Args:
            portfolio: 投资组合对象
            file_path: 图片保存路径
        """
        trade_history = portfolio.trade_history
        
        if not trade_history or 'total_value' not in trade_history[0]:
            logger.warning("没有足够的数据绘制资金曲线")
            return
            
        # 提取时间和资金数据
        timestamps = [trade['timestamp'] for trade in trade_history]
        equity = [trade['total_value'] for trade in trade_history]
        
        # 转换时间戳为可读格式
        dates = [datetime.fromtimestamp(ts) for ts in timestamps]
        
        # 计算基准线（总投资额）
        benchmark = [portfolio.total_invested] * len(dates) if len(dates) > 0 else []
        
        # 创建图表
        plt.figure(figsize=(12, 6))
        plt.plot(dates, equity, label='策略收益')
        if benchmark:
            plt.plot(dates, benchmark, '--', label='总投资额', color='gray')
        plt.title('回测资金曲线')
        plt.xlabel('日期')
        plt.ylabel('价值')
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        # 创建目录（如果不存在）
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # 保存图表
        plt.tight_layout()
        plt.savefig(file_path)
        plt.close()
        
        logger.info(f"资金曲线图已保存到: {file_path}")
        
    def plot_returns_distribution(self, portfolio, file_path: str) -> None:
        """绘制收益分布图
        
        Args:
            portfolio: 投资组合对象
            file_path: 图片保存路径
        """
        trade_history = portfolio.trade_history
        
        # 过滤出卖出交易
        sell_trades = [t for t in trade_history if t.get('type') == 'SELL' and 'realized_pnl' in t]
        
        if not sell_trades:
            logger.warning("没有卖出交易数据，跳过绘制收益分布图")
            return
            
        # 提取已实现盈亏数据
        realized_pnls = [t.get('realized_pnl', 0) for t in sell_trades]
        
        # 创建图表
        plt.figure(figsize=(10, 6))
        plt.hist(realized_pnls, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        plt.axvline(x=0, color='r', linestyle='--', alpha=0.7)
        plt.title('交易收益分布')
        plt.xlabel('收益')
        plt.ylabel('频率')
        plt.grid(True, alpha=0.3)
        
        # 创建目录（如果不存在）
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # 保存图表
        plt.tight_layout()
        plt.savefig(file_path)
        plt.close()
        
        logger.info(f"收益分布图已保存到: {file_path}") 