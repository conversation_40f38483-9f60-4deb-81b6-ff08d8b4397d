from datetime import datetime
from typing import Optional, Dict, Any, List
from beanie import Document, Indexed
from pydantic import BaseModel, Field

class TokenPool(BaseModel):
    """代币池子信息模型"""
    address: str = Field(description="代币地址")
    pool_address: str = Field(description="池子地址")
    quote_address: str = Field(description="报价代币地址")
    quote_symbol: str = Field(description="报价代币符号")
    liquidity: str = Field(description="流动性")
    base_reserve: str = Field(description="基础储备")
    quote_reserve: str = Field(description="报价储备")
    initial_liquidity: str = Field(default="0", description="初始流动性")
    initial_base_reserve: str = Field(default="0", description="初始基础储备")
    initial_quote_reserve: str = Field(default="0", description="初始报价储备")
    creation_timestamp: int = Field(description="创建时间戳")
    base_reserve_value: str = Field(description="基础储备价值")
    quote_reserve_value: str = Field(description="报价储备价值")
    quote_vault_address: str = Field(description="报价金库地址")
    base_vault_address: str = Field(description="基础金库地址")
    creator: str = Field(default="", description="创建者")
    exchange: str = Field(default="", description="交易所")
    token0_address: str = Field(default="", description="代币0地址")
    token1_address: str = Field(default="", description="代币1地址")
    base_address: str = Field(default="", description="基础代币地址")
    fee_ratio: str = Field(description="手续费率")

class TwitterNameChange(BaseModel):
    address: str
    username: str

class TokenDev(BaseModel):
    """代币开发者信息模型"""
    address: str = Field(description="代币地址")
    creator_address: str = Field(description="创建者地址")
    creator_token_balance: str = Field(description="创建者代币余额")
    creator_token_status: str = Field(description="创建者代币状态")
    twitter_name_change_history: list[Any]
    top_10_holder_rate: str = Field(description="前10持有者比率")
    dexscr_ad: int = Field(description="DEX广告")
    dexscr_update_link: int = Field(description="DEX更新链接")
    cto_flag: int = Field(description="CTO标志")

class TokenPrice(BaseModel):
    """代币价格信息模型"""
    address: str = Field(description="代币地址")
    price: str = Field(description="当前价格")
    price_1m: str = Field(description="1分钟价格")
    price_5m: str = Field(description="5分钟价格")
    price_1h: str = Field(description="1小时价格")
    price_6h: str = Field(description="6小时价格")
    price_24h: str = Field(description="24小时价格")
    buys_1m: int = Field(description="1分钟买入次数")
    buys_5m: int = Field(description="5分钟买入次数")
    buys_1h: int = Field(description="1小时买入次数")
    buys_6h: int = Field(description="6小时买入次数")
    buys_24h: int = Field(description="24小时买入次数")
    sells_1m: int = Field(description="1分钟卖出次数")
    sells_5m: int = Field(description="5分钟卖出次数")
    sells_1h: int = Field(description="1小时卖出次数")
    sells_6h: int = Field(description="6小时卖出次数")
    sells_24h: int = Field(description="24小时卖出次数")
    volume_1m: str = Field(description="1分钟交易量")
    volume_5m: str = Field(description="5分钟交易量")
    volume_1h: str = Field(description="1小时交易量")
    volume_6h: str = Field(description="6小时交易量")
    volume_24h: str = Field(description="24小时交易量")
    buy_volume_1m: str = Field(description="1分钟买入量")
    buy_volume_5m: str = Field(description="5分钟买入量")
    buy_volume_1h: str = Field(description="1小时买入量")
    buy_volume_6h: str = Field(description="6小时买入量")
    buy_volume_24h: str = Field(description="24小时买入量")
    sell_volume_1m: str = Field(description="1分钟卖出量")
    sell_volume_5m: str = Field(description="5分钟卖出量")
    sell_volume_1h: str = Field(description="1小时卖出量")
    sell_volume_6h: str = Field(description="6小时卖出量")
    sell_volume_24h: str = Field(description="24小时卖出量")
    swaps_1m: int = Field(description="1分钟交换次数")
    swaps_5m: int = Field(description="5分钟交换次数")
    swaps_1h: int = Field(description="1小时交换次数")
    swaps_6h: int = Field(description="6小时交换次数")
    swaps_24h: int = Field(description="24小时交换次数")
    hot_level: int = Field(description="热度等级")

class GmgnTokenWindow(Document):
    """GMGN代币多窗口信息模型
    
    用于存储代币的多窗口信息，包括：
    - 基本信息（地址、符号、名称等）
    - 池子信息
    - 开发者信息
    - 价格和交易量信息
    """
    
    # 基本信息
    chain: str = Field(description="链名称")
    address: Indexed(str) = Field(description="代币地址")
    symbol: str = Field(description="代币符号")
    name: str = Field(description="代币名称")
    decimals: int = Field(description="小数位数")
    logo: str = Field(description="代币logo")
    biggest_pool_address: str = Field(description="最大池子地址")
    open_timestamp: int = Field(description="开放时间戳")
    holder_count: int = Field(description="持有者数量")
    circulating_supply: str = Field(description="流通供应量")
    total_supply: str = Field(description="总供应量")
    max_supply: str = Field(description="最大供应量")
    liquidity: float = Field(description="流动性")
    creation_timestamp: int = Field(description="创建时间戳")
    
    # 池子信息
    pool: TokenPool = Field(description="池子信息")
    
    # 开发者信息
    dev: TokenDev = Field(description="开发者信息")
    
    # 价格信息
    price: TokenPrice = Field(description="价格信息")
    
    # 时间信息
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")
    
    class Settings:
        name = "gmgn_token_window"
        use_state_management = True
    
    @classmethod
    async def from_api_data(cls, chain: str, data: Dict[str, Any]) -> 'GmgnTokenWindow':
        """从API数据创建模型实例
        
        Args:
            chain: 链名称
            data: API返回的数据
            
        Returns:
            GmgnTokenWindow: 模型实例
        """
        return cls(
            chain=chain,
            address=data.get('address'),
            symbol=data.get('symbol'),
            name=data.get('name'),
            decimals=data.get('decimals'),
            logo=data.get('logo'),
            biggest_pool_address=data.get('biggest_pool_address'),
            open_timestamp=data.get('open_timestamp'),
            holder_count=data.get('holder_count'),
            circulating_supply=data.get('circulating_supply'),
            total_supply=data.get('total_supply'),
            max_supply=data.get('max_supply'),
            liquidity=float(data.get('liquidity', '0')),
            creation_timestamp=data.get('creation_timestamp'),
            
            # 池子信息
            pool=TokenPool(**data.get('pool', {})),
            
            # 开发者信息
            dev=TokenDev(**data.get('dev', {})),
            
            # 价格信息
            price=TokenPrice(**data.get('price', {}))
        )
    
    class Config:
        json_schema_extra = {
            "example": {
                "chain": "sol",
                "address": "6p6xgHyF7AeE6TZkSmFsko444wqoP15icUSqi2jfGiPN",
                "symbol": "TRUMP",
                "name": "OFFICIAL TRUMP",
                "decimals": 6,
                "logo": "https://gmgn.ai/defi/images/tokenmedia/b71639ceb0f9226c0f98a06dfa0d260c_128x128.jpg",
                "biggest_pool_address": "9d9mb8kooFfaD3SctgZtkxQypkshx6ezhbKio89ixyy2",
                "open_timestamp": 1737165693,
                "holder_count": 629059,
                "circulating_supply": "999999558",
                "total_supply": "999999558",
                "max_supply": "999999558",
                "liquidity": 550449475.2070508,
                "creation_timestamp": 1737122459,
                "pool": {
                    "address": "6p6xgHyF7AeE6TZkSmFsko444wqoP15icUSqi2jfGiPN",
                    "pool_address": "9d9mb8kooFfaD3SctgZtkxQypkshx6ezhbKio89ixyy2",
                    "quote_address": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
                    "quote_symbol": "USDC",
                    "liquidity": "519270134.021046",
                    "base_reserve": "10142178.441002",
                    "quote_reserve": "259635067.010523",
                    "initial_liquidity": "0",
                    "initial_base_reserve": "0",
                    "initial_quote_reserve": "0",
                    "creation_timestamp": 1737196721,
                    "base_reserve_value": "128637105.93342412583054",
                    "quote_reserve_value": "259635067.010523",
                    "quote_vault_address": "81BadRGfaHFpAmuXpJ65k8tYtUWsZ54EFSmsVo1rbDTV",
                    "base_vault_address": "AK93dERw7MJsGFBUPfV1bkXzDviJZM1K6vg2yGDugk7L",
                    "creator": "",
                    "exchange": "",
                    "token0_address": "",
                    "token1_address": "",
                    "base_address": "",
                    "fee_ratio": "0.1"
                },
                "dev": {
                    "address": "6p6xgHyF7AeE6TZkSmFsko444wqoP15icUSqi2jfGiPN",
                    "creator_address": "9k8jWWqfmTTXrc8gmZqYVxSFJhygURvyVRztivJXFTYP",
                    "creator_token_balance": "0",
                    "creator_token_status": "creator_hold",
                    "twitter_name_change_history": [],
                    "top_10_holder_rate": "0.093937",
                    "dexscr_ad": 0,
                    "dexscr_update_link": 1,
                    "cto_flag": 1
                },
                "price": {
                    "address": "6p6xgHyF7AeE6TZkSmFsko444wqoP15icUSqi2jfGiPN",
                    "price": "12.68338027",
                    "price_1m": "12.70161568",
                    "price_5m": "12.7301309",
                    "price_1h": "12.75309619",
                    "price_6h": "12.62079757",
                    "price_24h": "13.57518029",
                    "buys_1m": 4,
                    "buys_5m": 38,
                    "buys_1h": 688,
                    "buys_6h": 5221,
                    "buys_24h": 35739,
                    "sells_1m": 4,
                    "sells_5m": 14,
                    "sells_1h": 1042,
                    "sells_6h": 7341,
                    "sells_24h": 40318,
                    "volume_1m": "94.61955205",
                    "volume_5m": "17417.41899297",
                    "volume_1h": "3378068.16386852",
                    "volume_6h": "20405125.00443099",
                    "volume_24h": "145866005.41258836",
                    "buy_volume_1m": "49.726551",
                    "buy_volume_5m": "17079.34313885",
                    "buy_volume_1h": "1677443.42976315",
                    "buy_volume_6h": "10271109.88036834",
                    "buy_volume_24h": "69904521.38559411",
                    "sell_volume_1m": "44.89300105",
                    "sell_volume_5m": "338.075854117",
                    "sell_volume_1h": "1700624.73410536",
                    "sell_volume_6h": "10134015.12406265",
                    "sell_volume_24h": "75961484.02699427",
                    "swaps_1m": 8,
                    "swaps_5m": 52,
                    "swaps_1h": 1730,
                    "swaps_6h": 12562,
                    "swaps_24h": 76057,
                    "hot_level": 0
                },
                "created_at": "2024-03-02T00:00:00",
                "updated_at": "2024-03-02T00:00:00"
            }
        } 