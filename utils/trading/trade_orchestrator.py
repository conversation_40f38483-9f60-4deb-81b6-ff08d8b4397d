import logging
import asyncio
from datetime import datetime
from typing import List, Optional, Dict, Any, Tuple
from dataclasses import dataclass
import time

from solana.rpc.core import RPCException

from utils.trading.channel_registry import ChannelRegistry, ChannelNotFoundError
from utils.trading.channel_selector import ChannelSelector
from utils.trading.solana.trade_interface import TradeInterface, TradeResult, TradeStatus as InterfaceTradeStatus, TradeType as InterfaceTradeType
from models.trade_execution import TradeExecutionResult, ChannelAttemptResult, TradeStatus
from models.config import TradeChannelConfig, TradingParams
from models.slippage_retry import SlippageRetryConfig, SlippageAdjustmentReason
from models.dynamic_retry_config import DynamicRetryConfig

# 导入滑点重试组件
from utils.trading.slippage_retry import (
    SlippageCalculator,
    RetryDecisionEngine,
    RetryDelayCalculator, 
    ParameterMerger,
    RetryContext
)

logger = logging.getLogger(__name__)


@dataclass
class TradeRequest:
    """交易请求数据结构"""
    trade_type: InterfaceTradeType
    token_in_address: str
    token_out_address: str
    amount: float
    wallet_private_key_b58: str
    wallet_address: str
    strategy_snapshot: Dict[str, Any]
    signal_id: Optional[str] = None
    trade_record_id: Optional[str] = None
    timeout_seconds: int = 60
    max_retries: int = 3
    
    # 新增：滑点重试相关配置
    merged_trading_params: Optional[TradingParams] = None  # 合并后的交易参数
    dynamic_retry_config: Optional[DynamicRetryConfig] = None  # 动态重试配置


class TradeOrchestrator:
    """交易编排器 - 负责故障转移、重试和执行控制"""
    
    def __init__(self, channel_registry: ChannelRegistry, channel_selector: ChannelSelector):
        self.channel_registry = channel_registry
        self.channel_selector = channel_selector
        self._execution_stats = {
            "total_trades": 0,
            "successful_trades": 0,
            "failed_trades": 0,
            "fallback_count": 0,
            "slippage_retry_trades": 0,  # 新增：使用滑点重试的交易数
            "slippage_adjustments": 0,   # 新增：滑点调整次数
        }
        
        # 初始化滑点重试组件
        self.slippage_calculator = SlippageCalculator()
        self.retry_decision_engine = RetryDecisionEngine()
        self.retry_delay_calculator = RetryDelayCalculator()
        self.parameter_merger = ParameterMerger()
        self.active_trades_lock = asyncio.Lock()
        self.execution_stats: Dict[str, Any] = {"total_trades": 0, "successful_trades": 0, "failed_trades": 0}
    
    async def _delay(self, seconds: float) -> None:
        """辅助方法，用于异步延迟，方便测试中进行 mock"""
        await asyncio.sleep(seconds)
    
    async def execute_trade(self, trade_request: TradeRequest) -> TradeExecutionResult:
        """
        执行交易，包含故障转移和重试逻辑
        
        Args:
            trade_request: 交易请求
            
        Returns:
            TradeExecutionResult: 交易执行结果
        """
        start_time = datetime.now()
        self._execution_stats["total_trades"] += 1
        
        logger.info(f"开始执行交易：{trade_request.trade_type.value} "
                   f"{trade_request.amount} {trade_request.token_in_address} -> {trade_request.token_out_address}")
        
        # 选择可用渠道
        available_channels = await self.channel_selector.select_channels(
            trade_type=trade_request.trade_type.value,
            token_in_address=trade_request.token_in_address,
            token_out_address=trade_request.token_out_address,
            amount=trade_request.amount
        )
        
        if not available_channels:
            logger.error("没有可用的交易渠道")
            end_time = datetime.now()
            self._execution_stats["failed_trades"] += 1
            return TradeExecutionResult(
                final_status=TradeStatus.FAILED,
                successful_channel=None,
                final_trade_record_id=trade_request.trade_record_id,
                channel_attempts=[],
                total_execution_time=(end_time - start_time).total_seconds(),
                error_summary="No available channels",
                started_at=start_time,
                completed_at=end_time
            )
        
        # 执行交易（依次尝试可用渠道）
        channel_attempts = []
        excluded_channels = []
        
        for attempt_number, channel_type in enumerate(available_channels, 1):
            logger.info(f"尝试使用渠道 '{channel_type}' 执行交易 (尝试 {attempt_number}/{len(available_channels)})")
            
            # 检查是否启用滑点重试
            if trade_request.merged_trading_params and trade_request.merged_trading_params.enable_slippage_retry:
                # merged_trading_params 存在且启用了滑点重试
                attempt_result = await self._execute_on_channel_with_slippage_retry(
                    trade_request, 
                    channel_type, 
                    attempt_number
                )
                # 只有实际执行了滑点重试逻辑才增加统计
                # _execute_on_channel_with_slippage_retry 内部会根据配置决定是否真的进行重试
                # 这里的统计可能需要更精细化，比如基于 attempt_result.slippage_retries_count > 0
                self._execution_stats["slippage_retry_trades"] += 1 # 简化：只要走了这个分支就认为可能涉及滑点
            else:
                # merged_trading_params 不存在，或存在但未启用滑点重试，执行普通调用
                try:
                    channel_instance = self.channel_registry.get_channel(channel_type)
                    channel_config = self.channel_registry.get_channel_config(channel_type)
                    if not channel_instance or not channel_config:
                        logger.error(f"普通执行分支：无法获取渠道 '{channel_type}' 的实例或配置。")
                        completed_at_err = datetime.now()
                        attempt_result = ChannelAttemptResult(
                            channel_type=channel_type,
                            status=TradeStatus.FAILED,
                            error_message=f"Channel '{channel_type}' instance or config not found for non-slippage path.",
                            attempt_number=attempt_number,
                            started_at=completed_at_err, # Or a more accurate start time for this attempt
                            completed_at=completed_at_err,
                            execution_time=0
                        )
                    else:
                        channel_info_tuple: Tuple[str, TradeInterface] = (channel_type, channel_instance)
                        attempt_result = await self._execute_on_channel(
                            trade_request, 
                            channel_info_tuple,    # 正确传递 channel_info
                            channel_config,        # 正确传递 channel_config
                            attempt_number         # 正确传递 attempt_number
                        )
                except ChannelNotFoundError:
                    logger.error(f"普通执行分支：渠道 '{channel_type}' 未在注册表中找到。")
                    completed_at_err = datetime.now()
                    attempt_result = ChannelAttemptResult(
                        channel_type=channel_type,
                        status=TradeStatus.FAILED,
                        error_message=f"Channel '{channel_type}' not found in registry for non-slippage path.",
                        attempt_number=attempt_number,
                        started_at=completed_at_err, 
                        completed_at=completed_at_err,
                        execution_time=0
                    )
            
            channel_attempts.append(attempt_result)
            
            # 如果成功，返回结果
            if attempt_result.status == TradeStatus.SUCCESS:
                logger.info(f"交易在渠道 '{channel_type}' 成功执行")
                end_time = datetime.now()
                self._execution_stats["successful_trades"] += 1
                
                if attempt_number > 1:
                    self._execution_stats["fallback_count"] += 1
                
                # 注释：不再关闭渠道实例，保持复用以提高性能和避免并发冲突
                # 渠道实例将在程序生命周期内保持活跃
                logger.debug(f"交易成功，保持渠道 '{channel_type}' 实例活跃以供复用")
                
                return TradeExecutionResult(
                    final_status=TradeStatus.SUCCESS,
                    successful_channel=channel_type,
                    final_trade_record_id=trade_request.trade_record_id,
                    channel_attempts=channel_attempts,
                    total_execution_time=(end_time - start_time).total_seconds(),
                    error_summary=None,
                    started_at=start_time,
                    completed_at=end_time
                )
            
            # 如果失败，记录并继续下一个渠道
            logger.warning(f"渠道 '{channel_type}' 执行失败: {attempt_result.error_message}")
            excluded_channels.append(channel_type)
            
            # 标记渠道为不健康（临时）
            self.channel_registry.set_channel_health(channel_type, False)
            
            # 如果还有其他渠道可尝试，稍等一下再继续
            if attempt_number < len(available_channels):
                await asyncio.sleep(1)  # 短暂延迟
        
        # 所有渠道都失败了
        logger.error(f"所有 {len(available_channels)} 个渠道都执行失败")
        end_time = datetime.now()
        self._execution_stats["failed_trades"] += 1
        
        # 生成错误总结
        error_messages = [attempt.error_message for attempt in channel_attempts if attempt.error_message]
        error_summary = "; ".join(error_messages) if error_messages else "All channels failed"
        
        return TradeExecutionResult(
            final_status=TradeStatus.FAILED,
            successful_channel=None,
            final_trade_record_id=trade_request.trade_record_id,
            channel_attempts=channel_attempts,
            total_execution_time=(end_time - start_time).total_seconds(),
            error_summary=error_summary,
            started_at=start_time,
            completed_at=end_time
        )
    
    async def _execute_on_channel(
        self,
        trade_request: TradeRequest,
        channel_info: Tuple[str, TradeInterface],
        channel_config: TradeChannelConfig,
        attempt_number: int
    ) -> ChannelAttemptResult:
        """在单个渠道上执行交易（带标准重试但不调整滑点）"""
        
        # PRE_UNPACK_CHECK for Pydantic error
        logger.debug(f"PRE_UNPACK_CHECK: channel_info is '{channel_info}', type is {type(channel_info)}")

        actual_channel_type_str, channel_instance = channel_info
        
        started_at = datetime.now()
        
        # Determine the correct slippage based on trade type and config
        current_slippage_percentage: float
        if trade_request.trade_type == InterfaceTradeType.BUY:
            current_slippage_percentage = trade_request.strategy_snapshot.get(
                "slippage_percentage", 
                channel_config.trading_params.default_buy_slippage_percentage
            )
        elif trade_request.trade_type == InterfaceTradeType.SELL:
            current_slippage_percentage = trade_request.strategy_snapshot.get(
                "slippage_percentage", 
                channel_config.trading_params.default_sell_slippage_percentage
            )
        else:
            # Should not happen with current TradeType enum
            logger.warning(f"未知或不支持的交易类型: {trade_request.trade_type}，将使用默认购买滑点。")
            current_slippage_percentage = channel_config.trading_params.default_buy_slippage_percentage 

        # 进行标准重试（不调整滑点）
        max_retries = channel_config.max_retries
        last_error_message = "No error captured"
        
        for retry_attempt in range(max_retries + 1):
            attempt_start = datetime.now()
            trade_result_obj: Optional[TradeResult] = None
            error_message_str: Optional[str] = None
            
            logger.debug(f"在渠道 '{actual_channel_type_str}' 执行交易 (重试 {retry_attempt + 1}/{max_retries + 1})，滑点: {current_slippage_percentage}%, 超时: {channel_config.timeout_seconds}秒")

            status: TradeStatus
            try:
                timeout = min(trade_request.timeout_seconds, channel_config.timeout_seconds)
                
                trade_result_obj = await asyncio.wait_for(
                    channel_instance.execute_trade(
                        trade_type=trade_request.trade_type,
                        input_token_address=trade_request.token_in_address,
                        output_token_address=trade_request.token_out_address,
                        amount_input_token=trade_request.amount,
                        wallet_private_key_b58=trade_request.wallet_private_key_b58,
                        wallet_address=trade_request.wallet_address,
                        strategy_snapshot=trade_request.strategy_snapshot,
                        signal_id=trade_request.signal_id,
                        trade_record_id=trade_request.trade_record_id
                    ),
                    timeout=timeout
                )

                if trade_result_obj and trade_result_obj.status == InterfaceTradeStatus.SUCCESS:
                    status = TradeStatus.SUCCESS
                elif trade_result_obj and trade_result_obj.status == InterfaceTradeStatus.SKIPPED:
                    status = TradeStatus.SKIPPED
                else:
                    status = TradeStatus.FAILED
                error_message_str = trade_result_obj.error_message if trade_result_obj else "TradeResult was None despite no exception"
                
            except asyncio.TimeoutError:
                status = TradeStatus.FAILED
                error_message_str = f"渠道 '{actual_channel_type_str}' 执行超时 ({timeout}秒)"
                logger.warning(error_message_str)
            except ChannelNotFoundError as e: # Should ideally not happen if channel_instance is correctly obtained before calling
                status = TradeStatus.FAILED
                error_message_str = str(e)
                logger.error(error_message_str)
            except Exception as e:
                status = TradeStatus.FAILED
                error_message_str = f"渠道 '{actual_channel_type_str}' 执行时发生意外错误: {type(e).__name__}: {str(e)}"
                logger.exception(error_message_str) # Log with stack trace
                trade_result_obj = TradeResult(success=False, error_message=error_message_str, status=InterfaceTradeStatus.FAILED) # Ensure trade_result_obj is not None

            attempt_end = datetime.now()
            attempt_execution_time = (attempt_end - attempt_start).total_seconds()

            logger.debug(f"渠道 '{actual_channel_type_str}' (重试 {retry_attempt + 1}) 执行完成，状态: {status.value}, 耗时: {attempt_execution_time:.2f}秒")

            # 如果成功，返回结果
            if status == TradeStatus.SUCCESS:
                completed_at = datetime.now()
                total_execution_time = (completed_at - started_at).total_seconds()
                
                return ChannelAttemptResult(
                    channel_type=actual_channel_type_str,
                    attempt_number=attempt_number,
                    status=status,
                    tx_hash=trade_result_obj.tx_hash if trade_result_obj else None,
                    provider_response=trade_result_obj.provider_response_raw if trade_result_obj else None,
                    error_message=error_message_str,
                    execution_time=total_execution_time,
                    started_at=started_at,
                    completed_at=completed_at,
                    actual_amount_in=trade_result_obj.actual_amount_in if trade_result_obj else None,
                    actual_amount_out=trade_result_obj.actual_amount_out if trade_result_obj else None,
                    initial_buy_slippage=current_slippage_percentage if trade_request.trade_type == InterfaceTradeType.BUY else None,
                    final_buy_slippage=current_slippage_percentage if trade_request.trade_type == InterfaceTradeType.BUY else None,
                    initial_sell_slippage=current_slippage_percentage if trade_request.trade_type == InterfaceTradeType.SELL else None,
                    final_sell_slippage=current_slippage_percentage if trade_request.trade_type == InterfaceTradeType.SELL else None,
                    slippage_retry_enabled=False,  # 标准重试不启用滑点调整
                    slippage_adjustments_count=0,  # 没有滑点调整
                    total_slippage_retries=retry_attempt  # 记录重试次数
                )
            
            # 如果失败，记录错误并继续重试（如果还有重试机会）
            last_error_message = error_message_str or "Unknown error"
            
            # 如果还有重试机会，等待一下再继续
            if retry_attempt < max_retries:
                await asyncio.sleep(0.5)  # 简单的固定延迟
        
        # 所有重试都失败了
        completed_at = datetime.now()
        total_execution_time = (completed_at - started_at).total_seconds()
        
        # VALIDATION_CHECK for Pydantic error
        logger.debug(f"VALIDATION_CHECK: actual_channel_type_str is '{actual_channel_type_str}', type is {type(actual_channel_type_str)}")

        return ChannelAttemptResult(
            channel_type=actual_channel_type_str,
            attempt_number=attempt_number,
            status=TradeStatus.FAILED,
            tx_hash=None,
            provider_response=None,
            error_message=f"标准重试耗尽: {last_error_message}",
            execution_time=total_execution_time,
            started_at=started_at,
            completed_at=completed_at,
            actual_amount_in=None,
            actual_amount_out=None,
            initial_buy_slippage=current_slippage_percentage if trade_request.trade_type == InterfaceTradeType.BUY else None,
            final_buy_slippage=current_slippage_percentage if trade_request.trade_type == InterfaceTradeType.BUY else None,
            initial_sell_slippage=current_slippage_percentage if trade_request.trade_type == InterfaceTradeType.SELL else None,
            final_sell_slippage=current_slippage_percentage if trade_request.trade_type == InterfaceTradeType.SELL else None,
            slippage_retry_enabled=False,  # 标准重试不启用滑点调整
            slippage_adjustments_count=0,  # 没有滑点调整
            total_slippage_retries=max_retries  # 记录重试次数
        )
    
    def get_execution_stats(self) -> dict:
        """获取执行统计信息"""
        stats = self._execution_stats.copy()
        
        if stats["total_trades"] > 0:
            stats["success_rate"] = stats["successful_trades"] / stats["total_trades"]
            stats["failure_rate"] = stats["failed_trades"] / stats["total_trades"]
            stats["fallback_rate"] = stats["fallback_count"] / stats["total_trades"]
        else:
            stats["success_rate"] = 0.0
            stats["failure_rate"] = 0.0
            stats["fallback_rate"] = 0.0
        
        return stats
    
    def reset_stats(self) -> None:
        """重置统计信息"""
        self._execution_stats = {
            "total_trades": 0,
            "successful_trades": 0,
            "failed_trades": 0,
            "fallback_count": 0,
            "slippage_retry_trades": 0,
            "slippage_adjustments": 0
        }
        logger.info("交易执行统计已重置")
    
    async def _execute_on_channel_with_slippage_retry(
        self, 
        trade_request: TradeRequest, 
        channel_type: str, 
        attempt_number: int
    ) -> ChannelAttemptResult:
        """
        在指定渠道执行交易，支持滑点递增重试
        
        Args:
            trade_request: 交易请求
            channel_type: 渠道类型
            attempt_number: 尝试次数
            
        Returns:
            ChannelAttemptResult: 渠道尝试结果
        """
        started_at = datetime.now()
        
        # 获取渠道实例和配置
        try:
            channel_instance = self.channel_registry.get_channel(channel_type)
            channel_config = self.channel_registry.get_channel_config(channel_type)
        except ChannelNotFoundError as e:
            completed_at = datetime.now()
            execution_time = (completed_at - started_at).total_seconds()
            error_msg = f"渠道 '{channel_type}' 未找到: {str(e)}"
            logger.error(error_msg)
            
            return ChannelAttemptResult(
                channel_type=channel_type,
                attempt_number=attempt_number,
                status=TradeStatus.FAILED,
                tx_hash=None,
                error_message=error_msg,
                execution_time=execution_time,
                started_at=started_at,
                completed_at=completed_at
            )
        
        # 获取合并后的交易参数
        merged_params = trade_request.merged_trading_params
        if not merged_params:
            # 如果没有合并参数，降级为普通执行
            logger.warning("未找到合并的交易参数，降级为普通交易执行")
            return await self._execute_on_channel(trade_request, channel_type, attempt_number)
        
        # 初始化重试上下文
        initial_buy_slippage = trade_request.strategy_snapshot.get('buy_slippage_percentage', merged_params.default_buy_slippage_percentage)
        initial_sell_slippage = trade_request.strategy_snapshot.get('sell_slippage_percentage', merged_params.default_sell_slippage_percentage)
        
        # 构建滑点重试配置对象
        effective_config = merged_params.get_effective_slippage_config(trade_request.trade_type.value)
        slippage_retry_config = SlippageRetryConfig(
            enabled=effective_config['enabled'],
            increment_percentage=effective_config['increment'],
            max_slippage_percentage=effective_config['max_slippage'],
            retry_delay_seconds=merged_params.retry_delay_seconds,
            config_source="trade_orchestrator_init"
        )
        
        retry_context = RetryContext(
            trade_type=trade_request.trade_type.value,
            initial_slippage=initial_buy_slippage if trade_request.trade_type.value.lower() == 'buy' else initial_sell_slippage,
            config=slippage_retry_config,
            max_retries=channel_config.max_retries
        )
        
        # 在渠道级别进行滑点递增重试
        max_channel_retries = channel_config.max_retries
        
        logger.info(f"[_execute_on_channel_with_slippage_retry] Starting for channel {channel_type}. Max retries: {max_channel_retries}")
        last_error_message_for_channel_attempt = "No error message captured before all retries exhausted for channel."
        
        for retry_attempt in range(max_channel_retries + 1):
            logger.debug(f"LOOP_ITER: channel={channel_type}, retry_attempt_loop_var={retry_attempt}, current_retry_context_count={retry_context.retry_count}")
            started_at_loop = datetime.now()
            trade_result: Optional[TradeResult] = None # Initialize trade_result
            current_error_message: Optional[str] = None
            current_exception: Optional[Exception] = None

            try:
                # 使用当前的滑点参数更新策略快照
                updated_snapshot = trade_request.strategy_snapshot.copy()
                if trade_request.trade_type.value.lower() == 'buy':
                    updated_snapshot['buy_slippage_percentage'] = retry_context.current_slippage
                else:
                    updated_snapshot['sell_slippage_percentage'] = retry_context.current_slippage
                
                # 设置超时时间
                timeout = min(trade_request.timeout_seconds, channel_config.timeout_seconds)
                
                # 滑点和超时针对本次尝试
                slippage_for_this_attempt = retry_context.current_slippage
                timeout_for_this_attempt = min(trade_request.timeout_seconds, channel_config.timeout_seconds) # TODO: Consider if timeout should change per retry
                
                logger.debug(f"渠道 '{channel_type}' 第{retry_context.retry_count + 1}次尝试，滑点: {slippage_for_this_attempt:.2f}%，超时: {timeout_for_this_attempt}秒")
                
                # 执行交易（带超时）
                trade_result = await asyncio.wait_for(
                    channel_instance.execute_trade(
                        trade_type=trade_request.trade_type,
                        input_token_address=trade_request.token_in_address,
                        output_token_address=trade_request.token_out_address,
                        amount_input_token=trade_request.amount,
                        wallet_private_key_b58=trade_request.wallet_private_key_b58,
                        wallet_address=trade_request.wallet_address,
                        strategy_snapshot=updated_snapshot,
                        signal_id=trade_request.signal_id,
                        trade_record_id=trade_request.trade_record_id
                    ),
                    timeout=timeout
                )
                current_error_message = trade_result.error_message # Capture error message if any from successful execution
            
            except asyncio.TimeoutError as te:
                current_exception = te
                current_error_message = f"渠道 '{channel_type}' 执行超时 ({timeout}秒)"
                logger.warning(f"[_execute_on_channel_with_slippage_retry] Timeout for channel {channel_type} on attempt {retry_attempt + 1}. {current_error_message}")
                # For Timeout, we might want to retry without slippage adjustment or based on policy
                # Let it fall through to the retry decision logic

            except RPCException as rpc_e: # Catch RPCException specifically
                current_exception = rpc_e
                current_error_message = str(rpc_e)
                logger.warning(f"[_execute_on_channel_with_slippage_retry] RPCException for channel {channel_type} on attempt {retry_attempt + 1}. Error: {current_error_message}")
                # This error will be evaluated by is_slippage_related_error and retry_decision_engine
            
            except Exception as e: # Catch other unexpected errors during execute_trade
                current_exception = e
                current_error_message = f"渠道 '{channel_type}' 执行时发生意外交易错误: {type(e).__name__}: {str(e)}"
                logger.error(f"[_execute_on_channel_with_slippage_retry] Unexpected Trade Exception for channel {channel_type} on attempt {retry_attempt + 1}. {current_error_message}", exc_info=True)
                # For truly unexpected errors, we might not want to retry, or log and then decide.
                # For now, let it fall through to retry decision logic which might stop it.

            # --- Post-execution logic (whether success, timeout, or specific exception) ---
            completed_at = datetime.now()
            execution_time = (completed_at - started_at_loop).total_seconds()

            # 转换状态 - 仅当 trade_result 非 None (即 execute_trade 未抛出异常或超时)
            if trade_result and trade_result.status == InterfaceTradeStatus.SUCCESS:
                status = TradeStatus.SUCCESS
                logger.info(f"[_execute_on_channel_with_slippage_retry] Channel {channel_type} SUCCESS on attempt {retry_attempt + 1}")
                
                return ChannelAttemptResult(
                    channel_type=channel_type,
                    attempt_number=attempt_number,
                    status=status,
                    tx_hash=trade_result.tx_hash,
                    error_message=trade_result.error_message,
                    execution_time=execution_time,
                    started_at=started_at_loop,
                    completed_at=completed_at,
                    actual_amount_in=trade_result.actual_amount_in,
                    actual_amount_out=trade_result.actual_amount_out,
                    initial_buy_slippage=initial_buy_slippage,
                    initial_sell_slippage=initial_sell_slippage,
                    final_buy_slippage=retry_context.current_slippage if trade_request.trade_type.value.lower() == 'buy' else None,
                    final_sell_slippage=retry_context.current_slippage if trade_request.trade_type.value.lower() == 'sell' else None,
                    slippage_retry_enabled=slippage_retry_config.enabled,
                    slippage_adjustments_count=retry_context.total_adjustments,
                    total_slippage_increase=retry_context.get_total_slippage_increase(),
                    total_slippage_retries=retry_context.retry_count
                )
            # else, it's a failure (either from trade_result.status or an exception)
            status = TradeStatus.FAILED # Default to FAILED if not explicitly success
            # current_error_message should already be set from try/except blocks or trade_result
            if not current_error_message and trade_result:
                 current_error_message = trade_result.error_message or "Unknown error after trade execution (trade_result available)"
            elif not current_error_message:
                current_error_message = "Unknown error after trade execution (trade_result is None)"

            last_error_message_for_channel_attempt = current_error_message
            logger.warning(f"[_execute_on_channel_with_slippage_retry] Channel {channel_type} FAILED/SKIPPED on attempt {retry_attempt + 1}. Effective Error: {current_error_message}")
            
            # 检查是否为滑点相关错误（通过交易接口判断）
            is_slippage_error = False
            if hasattr(channel_instance, 'is_slippage_related_error'):
                is_slippage_error = channel_instance.is_slippage_related_error(
                    error_message=current_error_message, 
                    provider_response=trade_result.provider_response_raw if trade_result else None
                )
            
            # 使用重试决策引擎判断是否需要调整滑点
            if is_slippage_error and merged_params.enable_slippage_retry and slippage_retry_config.enabled:
                logger.info(f"[_execute_on_channel_with_slippage_retry] Slippage error on {channel_type}, slippage retry ENABLED. Effective config: {slippage_retry_config}")
                # 调用 slippage_calculator 计算下一个滑点
                calculated_slippage_value, at_limit = self.slippage_calculator.calculate_next_slippage(
                    current_slippage=slippage_for_this_attempt, # 使用本次尝试的滑点计算下一个
                    config=retry_context.config, # 直接传递 SlippageRetryConfig
                    trade_type=trade_request.trade_type.value
                )
                
                # 只有当计算出的新滑点确实比当前滑点大，并且没有超出限制（或者说，即使超出限制，也要记录这次调整尝试）
                # SlippageCalculator 内部会处理上限，所以这里我们信任它返回的值
                # if calculated_slippage_value > slippage_for_this_attempt or at_limit: # 如果新滑点更大或已达上限就记录
                retry_context.record_slippage_adjustment(
                    new_slippage=calculated_slippage_value, 
                    reason=SlippageAdjustmentReason.SLIPPAGE_ERROR,
                    error_message=current_error_message # 使用 effective_error_message
                )
            
            logger.debug("ORCHESTRATOR_DEBUG: About to call make_retry_decision from _execute_on_channel_with_slippage_retry")
            retry_decision = self.retry_decision_engine.make_retry_decision(
                retry_count=retry_context.retry_count, 
                max_retries=retry_context.max_retries,
                current_slippage=slippage_for_this_attempt, # <--- 使用本次尝试的滑点
                config=retry_context.config, # Pass the whole SlippageRetryConfig
                error_message=current_error_message,
                provider_response=trade_result.provider_response_raw if trade_result else None
            )
            retry_context.record_retry_decision(retry_decision)
            logger.info(f"DECISION_MADE: channel={channel_type}, retry_attempt_loop_var={retry_attempt}, should_retry={retry_decision.should_retry}, reason='{retry_decision.decision_reason}'")

            if retry_decision.should_retry and retry_attempt < max_channel_retries:
                delay_config = self.retry_delay_calculator.get_effective_delay_config(
                    trading_params=merged_params,
                    trade_type=trade_request.trade_type.value
                )
                retry_delay = self.retry_delay_calculator.calculate_delay(
                    retry_count=retry_context.retry_count + 1, # Next attempt number for delay calculation
                    trading_params=merged_params,
                    is_slippage_error=is_slippage_error,
                    trade_type=trade_request.trade_type.value
                )
                logger.info(f"[_execute_on_channel_with_slippage_retry] Waiting {retry_delay:.2f}s for channel {channel_type} before next attempt ({retry_context.retry_count + 1}) (overall attempt {retry_attempt + 2})")
                await self._delay(retry_delay)
                retry_context.increment_retry_count()
            else:
                logger.warning(f"[_execute_on_channel_with_slippage_retry] Decision not to retry for channel {channel_type} after attempt {retry_attempt + 1} (loop var). Reason: {retry_decision.decision_reason}. Or max retries reached.")
                break # Break from the for loop for this channel
            
        # 所有重试都失败了 (loop finished or broke due to no_retry decision)
        logger.warning(f"[_execute_on_channel_with_slippage_retry] All retries failed for channel {channel_type}. Last error: {last_error_message_for_channel_attempt}")
        completed_at_after_all_retries = datetime.now()
        # 使用第一次尝试的开始时间 和 所有重试结束的时间
        total_execution_time_for_channel = (completed_at_after_all_retries - started_at).total_seconds() 
        
        return ChannelAttemptResult(
            channel_type=channel_type,
            attempt_number=attempt_number,
            status=TradeStatus.FAILED,
            tx_hash=None,
            error_message=f"滑点重试耗尽: {last_error_message_for_channel_attempt}", # 使用最后捕获的错误
            execution_time=total_execution_time_for_channel, # 使用总时间
            started_at=started_at, # 第一次尝试的开始时间
            completed_at=completed_at_after_all_retries, # 所有重试结束的时间
            initial_buy_slippage=initial_buy_slippage,
            initial_sell_slippage=initial_sell_slippage,
            final_buy_slippage=initial_buy_slippage if trade_request.trade_type.value.lower() == 'buy' else None,
            final_sell_slippage=initial_sell_slippage if trade_request.trade_type.value.lower() == 'sell' else None,
            slippage_retry_enabled=slippage_retry_config.enabled,
            total_slippage_retries=retry_context.retry_count
        ) 
    
    async def _close_channel_safely(self, channel_instance: Optional[TradeInterface], channel_type: str):
        """安全地关闭渠道"""
        if channel_instance and hasattr(channel_instance, 'close') and callable(channel_instance.close):
            try:
                logger.info(f"正在关闭渠道 {channel_type}...")
                await channel_instance.close()
                logger.info(f"渠道 {channel_type} 已关闭。")
            except Exception as e:
                logger.error(f"关闭渠道 {channel_type} 时发生错误: {e}", exc_info=True)
        else:
            logger.warning(f"渠道 {channel_type} 实例无效或没有可用的 close 方法，跳过关闭。") 