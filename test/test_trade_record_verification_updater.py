import unittest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone, timedelta
from beanie import PydanticObjectId

from utils.trading.trade_record_verification_updater import TradeRecordVerificationUpdater
from dao.trade_record_dao import TradeRecordDAO
from models.trade_record import TradeRecord, TradeStatus, TradeType
from utils.spiders.solana.solana_monitor import SolanaMonitor


class TestTradeRecordVerificationUpdater(unittest.TestCase):
    """交易记录验证更新器测试用例"""
    
    def setUp(self):
        """测试前置设置"""
        self.mock_dao = AsyncMock(spec=TradeRecordDAO)
        self.mock_solana_monitor = AsyncMock(spec=SolanaMonitor)
        
        self.updater = TradeRecordVerificationUpdater()
        # 替换内部的DAO和SolanaMonitor为mock对象
        self.updater.trade_record_dao = self.mock_dao
        self.updater.solana_monitor = self.mock_solana_monitor
    
    def test_init_with_default_params(self):
        """TC-003: 测试使用默认参数初始化更新器"""
        updater = TradeRecordVerificationUpdater()
        self.assertIsNotNone(updater.trade_record_dao)
        self.assertIsNotNone(updater.solana_monitor)
        self.assertEqual(updater.max_retry_attempts, 3)
        self.assertEqual(updater.retry_delay_seconds, 2)
        self.assertEqual(updater.verification_timeout_seconds, 8)
    
    def test_init_with_custom_params(self):
        """测试初始化后可以修改配置参数"""
        updater = TradeRecordVerificationUpdater()
        
        # 修改配置参数
        updater.max_retry_attempts = 5
        updater.retry_delay_seconds = 3
        updater.verification_timeout_seconds = 10
        
        self.assertEqual(updater.max_retry_attempts, 5)
        self.assertEqual(updater.retry_delay_seconds, 3)
        self.assertEqual(updater.verification_timeout_seconds, 10)
    
    @patch('asyncio.sleep')
    async def test_run_verification_cycle_success(self, mock_sleep):
        """测试验证周期成功执行"""
        # 模拟待验证记录
        mock_records = [
            self._create_mock_trade_record("tx1", "SOL"),
            self._create_mock_trade_record("tx2", "USDC")
        ]
        self.mock_dao.find_pending_verification_records.return_value = mock_records
        
        # 模拟验证成功
        self.mock_solana_monitor.get_confirmed_token_output_from_tx.side_effect = [1.5, 100.0]
        self.mock_dao.update_verification_result.return_value = AsyncMock()
        
        # 执行验证周期
        result = await self.updater.run_verification_cycle()
        
        # 验证结果
        self.assertEqual(result['processed'], 2)
        self.assertEqual(result['verified'], 2)
        self.assertEqual(result['failed'], 0)
        self.assertEqual(result['skipped'], 0)
        
        # 验证方法调用
        self.mock_dao.find_pending_verification_records.assert_called_once_with(24, 50)
        self.assertEqual(self.mock_solana_monitor.get_confirmed_token_output_from_tx.call_count, 2)
        self.assertEqual(self.mock_dao.update_verification_result.call_count, 2)
    
    async def test_run_verification_cycle_no_records(self):
        """TC-004: 测试没有待验证记录的情况"""
        self.mock_dao.find_pending_verification_records.return_value = []
        
        result = await self.updater.run_verification_cycle()
        
        self.assertEqual(result['processed'], 0)
        self.assertEqual(result['verified'], 0)
        self.assertEqual(result['failed'], 0)
        self.assertEqual(result['skipped'], 0)
        
        # 验证只调用了查询方法
        self.mock_dao.find_pending_verification_records.assert_called_once()
        self.mock_solana_monitor.get_confirmed_token_output_from_tx.assert_not_called()
    
    @patch('asyncio.sleep')
    async def test_verify_single_record_success(self, mock_sleep):
        """测试单个记录验证成功"""
        record = self._create_mock_trade_record("tx123", "SOL")
        self.mock_solana_monitor.get_confirmed_token_output_from_tx.return_value = 1.5
        
        result = await self.updater.verify_single_record(record)
        
        self.assertEqual(result['status'], 'verified')
        self.assertEqual(result['verified_amount'], 1.5)
        self.assertIsNone(result['error'])
        
        # 验证调用参数
        self.mock_solana_monitor.get_confirmed_token_output_from_tx.assert_called_once_with(
            "tx123", "SOL", "wallet123"
        )
    
    @patch('asyncio.sleep')
    async def test_verify_single_record_failure(self, mock_sleep):
        """测试单个记录验证失败"""
        record = self._create_mock_trade_record("tx123", "SOL")
        self.mock_solana_monitor.get_confirmed_token_output_from_tx.return_value = None
        
        result = await self.updater.verify_single_record(record)
        
        self.assertEqual(result['status'], 'failed')
        self.assertIsNone(result['verified_amount'])
        self.assertIn('验证失败', result['error'])
    
    @patch('asyncio.sleep')
    async def test_verify_single_record_with_retries(self, mock_sleep):
        """测试单个记录验证重试机制"""
        record = self._create_mock_trade_record("tx123", "SOL")
        
        # 前两次失败，第三次成功
        self.mock_solana_monitor.get_confirmed_token_output_from_tx.side_effect = [
            None, None, 1.5
        ]
        
        result = await self.updater.verify_single_record(record)
        
        self.assertEqual(result['status'], 'verified')
        self.assertEqual(result['verified_amount'], 1.5)
        self.assertEqual(self.mock_solana_monitor.get_confirmed_token_output_from_tx.call_count, 3)
        self.assertEqual(mock_sleep.call_count, 2)  # 两次重试间隔
    
    @patch('asyncio.sleep')
    async def test_verify_single_record_max_retries_exceeded(self, mock_sleep):
        """测试超过最大重试次数"""
        record = self._create_mock_trade_record("tx123", "SOL")
        
        # 所有尝试都失败
        self.mock_solana_monitor.get_confirmed_token_output_from_tx.return_value = None
        
        result = await self.updater.verify_single_record(record)
        
        self.assertEqual(result['status'], 'failed')
        self.assertIsNone(result['verified_amount'])
        self.assertIn('重试3次后仍然失败', result['error'])
        self.assertEqual(self.mock_solana_monitor.get_confirmed_token_output_from_tx.call_count, 3)
    
    @patch('asyncio.sleep')
    async def test_verify_single_record_exception_handling(self, mock_sleep):
        """测试验证过程中的异常处理"""
        record = self._create_mock_trade_record("tx123", "SOL")
        
        # 模拟异常
        self.mock_solana_monitor.get_confirmed_token_output_from_tx.side_effect = Exception("网络错误")
        
        result = await self.updater.verify_single_record(record)
        
        self.assertEqual(result['status'], 'failed')
        self.assertIsNone(result['verified_amount'])
        self.assertIn('验证过程中发生异常', result['error'])
        self.assertIn('网络错误', result['error'])
    
    async def test_verify_and_update_record_success(self):
        """测试验证并更新记录成功"""
        record = self._create_mock_trade_record("tx123", "SOL")
        
        # 模拟验证成功
        with patch.object(self.updater, 'verify_single_record') as mock_verify:
            mock_verify.return_value = {
                'status': 'verified',
                'verified_amount': 1.5,
                'error': None
            }
            
            self.mock_dao.update_verification_result.return_value = AsyncMock()
            
            result = await self.updater.verify_and_update_record(record)
            
            self.assertEqual(result, 'verified')
            mock_verify.assert_called_once_with(record)
            self.mock_dao.update_verification_result.assert_called_once_with(
                record.id, 1.5, 'verified', None
            )
    
    async def test_verify_and_update_record_failure(self):
        """测试验证并更新记录失败"""
        record = self._create_mock_trade_record("tx123", "SOL")
        
        # 模拟验证失败
        with patch.object(self.updater, 'verify_single_record') as mock_verify:
            mock_verify.return_value = {
                'status': 'failed',
                'verified_amount': None,
                'error': '验证失败'
            }
            
            self.mock_dao.update_verification_result.return_value = AsyncMock()
            
            result = await self.updater.verify_and_update_record(record)
            
            self.assertEqual(result, 'failed')
            self.mock_dao.update_verification_result.assert_called_once_with(
                record.id, None, 'failed', '验证失败'
            )
    
    async def test_get_verification_statistics(self):
        """测试获取验证统计信息"""
        expected_stats = {
            'total_success_records': 100,
            'verified': 80,
            'failed': 10,
            'pending': 5,
            'skipped': 5
        }
        self.mock_dao.get_verification_statistics.return_value = expected_stats
        
        result = await self.updater.get_verification_statistics()
        
        self.assertEqual(result, expected_stats)
        self.mock_dao.get_verification_statistics.assert_called_once_with(24)
    
    async def test_cleanup_old_verification_data(self):
        """测试清理旧验证数据"""
        # 这里可以添加清理旧数据的测试逻辑
        # 目前只测试方法存在
        result = await self.updater.cleanup_old_verification_data(days_to_keep=30)
        self.assertIsNotNone(result)
    
    def _create_mock_trade_record(self, tx_hash: str, token_out_address: str) -> TradeRecord:
        """创建模拟的交易记录"""
        record = MagicMock(spec=TradeRecord)
        record.id = PydanticObjectId()
        record.tx_hash = tx_hash
        record.token_out_address = token_out_address
        record.wallet_address = "wallet123"
        record.status = TradeStatus.SUCCESS
        record.trade_type = TradeType.BUY
        record.created_at = datetime.now(timezone.utc)
        return record


class TestTradeRecordVerificationUpdaterIntegration(unittest.TestCase):
    """交易记录验证更新器集成测试"""
    
    def setUp(self):
        """测试前置设置"""
        # 这里可以设置真实的数据库连接进行集成测试
        pass
    
    async def test_full_verification_workflow(self):
        """测试完整的验证工作流程"""
        # 这里可以添加端到端的集成测试
        # 包括真实的数据库操作和Solana网络调用
        pass


if __name__ == '__main__':
    # 运行异步测试
    def run_async_test(test_func):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(test_func())
        finally:
            loop.close()
    
    # 创建测试套件
    suite = unittest.TestSuite()
    
    # 添加同步测试
    suite.addTest(TestTradeRecordVerificationUpdater('test_init_with_default_params'))
    suite.addTest(TestTradeRecordVerificationUpdater('test_init_with_custom_params'))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    runner.run(suite)
    
    # 运行异步测试（示例）
    test_instance = TestTradeRecordVerificationUpdater()
    test_instance.setUp()
    
    print("\n运行异步测试...")
    try:
        run_async_test(test_instance.test_run_verification_cycle_no_records)
        print("✓ test_run_verification_cycle_no_records 通过")
    except Exception as e:
        print(f"✗ test_run_verification_cycle_no_records 失败: {e}")