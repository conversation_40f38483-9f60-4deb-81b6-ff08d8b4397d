# 重试间隔计算器功能单元测试
创建日期：2025-05-26
更新日期：2025-05-26
测试方法：自动化测试
测试级别：单元测试

## 测试用例
| 用例方法 | 用例概述 | 前置条件 | 输入 | 预期输出 | 实际输出 | 状态 |
| --- | --- | --- | --- | --- | --- | --- |
| test_calculate_delay_fixed_strategy | 测试固定间隔策略 | 固定策略配置 | retry_count=1,2,3,5 | 所有重试使用相同间隔 | 通过 | ✅ |
| test_calculate_delay_linear_strategy | 测试线性递增策略 | 线性策略配置 | retry_count=1,2,3,5 | 间隔=base_delay*retry_count | 通过 | ✅ |
| test_calculate_delay_exponential_strategy | 测试指数退避策略 | 指数策略配置 | retry_count=1,2,3,4 | 间隔=base_delay*(2^(retry_count-1)) | 通过 | ✅ |
| test_calculate_delay_max_limit | 测试最大延迟限制 | 指数策略+最大限制 | retry_count=4 | 超过限制时被约束为最大值 | 通过 | ✅ |
| test_calculate_delay_slippage_error_priority | 测试滑点错误专用延迟的优先级 | 配置滑点专用延迟 | is_slippage_error=True | 使用slippage_error_delay_seconds | 通过 | ✅ |
| test_calculate_delay_non_slippage_error | 测试非滑点错误使用基础延迟 | 非滑点错误 | is_slippage_error=False | 使用基础延迟 | 通过 | ✅ |
| test_get_effective_delay_config_buy_specific | 测试买入特定延迟配置 | 配置买入专用延迟 | trade_type="buy" | 返回买入特定延迟配置 | 通过 | ✅ |
| test_get_effective_delay_config_sell_specific | 测试卖出特定延迟配置 | 配置卖出专用延迟 | trade_type="sell" | 返回卖出特定延迟配置 | 通过 | ✅ |
| test_get_effective_delay_config_no_specific | 测试没有特定延迟时使用默认配置 | 无特定延迟配置 | trade_type="buy/sell" | 使用默认延迟配置 | 通过 | ✅ |
| test_validate_delay_config_valid | 测试有效配置验证 | 正常配置参数 | valid_params | 返回True | 通过 | ✅ |
| test_validate_delay_config_negative_delay | 测试负延迟配置验证 | 负延迟值 | retry_delay_seconds=-1.0 | 模型验证拦截(ValueError) | 通过 | ✅ |
| test_validate_delay_config_max_less_than_base | 测试最大延迟小于基础延迟的配置验证 | 最大延迟<基础延迟 | max<base | 返回False | 通过 | ✅ |
| test_validate_delay_config_negative_slippage_delay | 测试负滑点延迟配置验证 | 负滑点延迟值 | slippage_error_delay_seconds<0 | 模型验证拦截 | 通过 | ✅ |
| test_validate_delay_config_meme_coin_warnings | 测试meme币市场配置警告 | 过大的延迟配置 | 大延迟值+meme_coin_market=True | 通过验证但有警告 | 通过 | ✅ |
| test_validate_delay_config_exponential_warning | 测试指数退避在meme币市场的警告 | 指数策略+meme币市场 | exponential+meme_coin=True | 通过验证但有警告 | 通过 | ✅ |
| test_get_strategy_info_fixed | 测试固定策略信息 | 固定策略 | RetryDelayStrategy.FIXED | 返回固定策略描述信息 | 通过 | ✅ |
| test_get_strategy_info_linear | 测试线性策略信息 | 线性策略 | RetryDelayStrategy.LINEAR | 返回线性策略描述信息 | 通过 | ✅ |
| test_get_strategy_info_exponential | 测试指数策略信息 | 指数策略 | RetryDelayStrategy.EXPONENTIAL | 返回指数策略描述信息 | 通过 | ✅ |
| test_get_strategy_info_unknown | 测试未知策略信息 | 未知策略对象 | unknown_strategy | 返回"未知策略"信息 | 通过 | ✅ |
| test_estimate_total_retry_time_fixed | 测试固定策略的总重试时间预估 | 固定策略 | max_retries=3 | 总时间=base_delay*max_retries | 通过 | ✅ |
| test_estimate_total_retry_time_linear | 测试线性策略的总重试时间预估 | 线性策略 | max_retries=3 | 总时间=1+2+3=6.0 | 通过 | ✅ |
| test_estimate_total_retry_time_exponential | 测试指数策略的总重试时间预估 | 指数策略 | max_retries=3 | 总时间=1+2+4=7.0 | 通过 | ✅ |
| test_estimate_total_retry_time_with_max_limit | 测试带最大限制的总重试时间预估 | 指数策略+最大限制 | max_retries=4 | 受最大限制约束的总时间 | 通过 | ✅ |
| test_trade_type_specific_delays | 测试买卖特定延迟 | 配置买卖专用延迟 | buy/sell特定延迟 | 使用对应的特定延迟 | 通过 | ✅ |
| test_edge_cases | 测试边界情况 | 极端参数配置 | retry_count=0,100等 | 正确处理边界情况 | 通过 | ✅ |
| test_slippage_error_delay_none | 测试滑点错误延迟为None时的行为 | slippage_error_delay_seconds=None | is_slippage_error=True | 回退到基础延迟 | 通过 | ✅ |
| test_case_insensitive_trade_type | 测试交易类型大小写不敏感 | 不同大小写的交易类型 | "BUY"、"SELL"、"Buy"等 | 大小写不敏感处理 | 通过 | ✅ |
| test_complex_scenarios | 测试复杂场景 | 复杂的配置组合 | meme币配置、保守配置等 | 正确处理复杂场景 | 通过 | ✅ |
| test_validation_comprehensive | 测试全面的配置验证 | 各种配置组合 | 有效/无效配置 | 正确验证各种配置 | 通过 | ✅ |

## 测试覆盖功能点
- 三种重试延迟策略（固定、线性、指数）
- 最大延迟限制机制
- 滑点错误专用延迟优先级
- 买卖特定延迟配置
- 延迟配置验证
- meme币市场配置建议
- 策略信息查询
- 总重试时间预估
- 大小写不敏感处理
- 边界条件和异常处理

## 备注
- 测试涵盖了重试间隔计算器的所有核心功能
- 验证了三种延迟策略的正确实现
- 包含了meme币市场的特殊考虑
- 测试了各种边界条件和配置验证 