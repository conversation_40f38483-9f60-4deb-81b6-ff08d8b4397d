import logging
import time
from typing import Dict, List, Any, Optional, Tuple, Generator, AsyncGenerator
import asyncio
from datetime import datetime

from dao.kol_wallet_activity_dao import KOLWalletActivityDAO
from dao.kol_wallet_dao import KOLWalletDAO
from dao.token_dao import TokenDAO
from utils.backtest_event_driven.events import TransactionEvent, Event
from utils.spiders.solana.solana_monitor import SolanaMonitor
from utils.spiders.smart_money.gmgn_token_candles import GmgnTokenCandlesSpider

logger = logging.getLogger("DataHandler")


class DataHandler:
    """数据处理器，负责加载和处理回测数据"""
    
    def __init__(self, skip_token_api_query: bool = False):
        """初始化数据处理器
        
        Args:
            skip_token_api_query: 是否跳过代币API查询，如果为True，则不从外部API获取代币信息
        """
        self.skip_token_api_query = skip_token_api_query
        
        # 直接在初始化时导入依赖
        from dao.token_dao import TokenDAO
        from dao.kol_wallet_dao import KOLWalletDAO
        from dao.kol_wallet_activity_dao import KOLWalletActivityDAO
        from utils.spiders.solana.solana_monitor import SolanaMonitor
        
        self.token_dao = TokenDAO()
        self.kol_wallet_dao = KOLWalletDAO()
        self.activity_dao = KOLWalletActivityDAO()
        self.solana_monitor = SolanaMonitor()
        self._price_cache = {}  # 价格缓存
        self._candles_spider = None  # K线数据爬虫
        
        # 创建初始化任务
        asyncio.create_task(self._init_async())
        
    async def _init_async(self):
        """异步初始化K线数据爬虫"""
        # 初始化K线数据爬虫
        self._candles_spider = GmgnTokenCandlesSpider()
        await self._candles_spider.setup()
        
    async def load_wallets(self) -> List[Dict[str, Any]]:
        """加载KOL钱包数据
        
        Returns:
            List[Dict[str, Any]]: KOL钱包列表
        """
        logger.info("加载KOL钱包数据...")
        wallets = await self.kol_wallet_dao.find_all_wallets()
        logger.info(f"已加载 {len(wallets)} 个KOL钱包")
        return wallets
        
    async def stream_activities(self, start_time: int, end_time: int, 
                               batch_size: int = 100, 
                               kol_account_min_txs: int = 1, 
                               kol_account_max_txs: int = 10, 
                               min_amount: float = 0,
                               kol_min_winrate_7d: Optional[float] = None) -> AsyncGenerator[List[TransactionEvent], None]:
        """流式加载指定时间范围内的符合条件的KOL活动记录，并将其转换为事件
        
        Args:
            start_time: 起始时间戳
            end_time: 结束时间戳
            batch_size: 每批加载的记录数量
            kol_account_min_txs: 每个KOL账号最低交易数
            kol_account_max_txs: 每个KOL账号最高交易数
            min_amount: 最低交易金额
            kol_min_winrate_7d: 可选的KOL最低7日胜率阈值
            
        Yields:
            List[TransactionEvent]: 活动事件列表
        """
        logger.info(f"开始加载活动数据，时间范围: {datetime.fromtimestamp(start_time)} - {datetime.fromtimestamp(end_time)}")
        
        # 第一步：筛选符合条件的KOL账户
        logger.info(f"筛选KOL账户 (交易数范围: {kol_account_min_txs}-{kol_account_max_txs})")
        qualified_kol_wallets = await self.kol_wallet_dao.find_many(
            {
                "tags": "kol", 
                "txs": {
                    "$gte": kol_account_min_txs,
                    "$lte": kol_account_max_txs
                }
            }
        )
        
        # 可选：根据7日胜率进一步筛选KOL账户
        if kol_min_winrate_7d is not None and 0 <= kol_min_winrate_7d <= 1:
            initial_kol_count = len(qualified_kol_wallets)
            logger.info(f"根据7日胜率 (>={kol_min_winrate_7d:.2%}) 进一步筛选 {initial_kol_count} 个KOL账户...")
            winrate_filtered_wallets = [
                wallet for wallet in qualified_kol_wallets
                if wallet.winrate_7d is not None and wallet.winrate_7d >= kol_min_winrate_7d
            ]
            qualified_kol_wallets = winrate_filtered_wallets # Update the list
            logger.info(f"胜率筛选后剩余 {len(qualified_kol_wallets)} 个KOL账户。")
        elif kol_min_winrate_7d is not None:
            logger.warning(f"指定的 kol_min_winrate_7d ({kol_min_winrate_7d}) 无效，应在 0 到 1 之间，本次筛选将被忽略。")
        
        if not qualified_kol_wallets:
            logger.warning("没有找到符合条件的KOL账户，无法加载活动记录")
            return
            
        # 获取符合条件的钱包地址列表
        qualified_wallet_addresses = [wallet.wallet_address for wallet in qualified_kol_wallets]
        logger.info(f"找到 {len(qualified_wallet_addresses)} 个符合条件的KOL账户")
        
        # 分页查询，避免一次性加载过多数据
        skip = 0
        total_count = 0
        
        # 先尝试获取记录总数，以便了解是否有符合条件的数据
        count_query = {
            "timestamp": {"$gte": start_time, "$lte": end_time},
            "wallet": {"$in": qualified_wallet_addresses}
        }
        
        try:
            total_records = await self.activity_dao.collection.count_documents(count_query)
            logger.info(f"在时间范围内找到 {total_records} 条活动记录")
            
            # 如果没有记录，则直接返回
            if total_records == 0:
                logger.warning("在指定时间范围内没有找到任何活动记录")
                return
        except Exception as e:
            logger.error(f"获取记录总数时出错: {e}")
        
        while True:
            # 查询一批符合条件的KOL的活动记录
            query = {
                "timestamp": {"$gte": start_time, "$lte": end_time},
                "wallet": {"$in": qualified_wallet_addresses}
            }
            
            # 如果设置了最低交易金额，添加筛选条件
            if min_amount > 0:
                # cost_usd可能是字符串或数字，使用$toDouble操作符处理
                query["$expr"] = {"$gte": [{"$toDouble": {"$ifNull": ["$cost_usd", "0"]}}, min_amount]}
            
            logger.debug(f"执行查询: {query}")
            
            # 使用原生MongoDB查询
            cursor = self.activity_dao.collection.find(
                query,
                skip=skip,
                limit=batch_size
            ).sort([("timestamp", 1), ("_id", 1)])  # 按时间戳升序排序
            
            # 将MongoDB游标转换为文档列表
            activities = await cursor.to_list(length=batch_size)
            
            if not activities:
                if skip == 0:
                    logger.warning("查询未返回任何活动记录，请检查查询条件")
                break  # 没有更多记录，结束循环
                
            logger.debug(f"获取到 {len(activities)} 条活动记录")
            
            # 处理文档，确保关键字段存在
            for doc in activities:
                # 处理_id字段
                if '_id' in doc:
                    doc['id'] = str(doc.pop('_id'))
                    
                # 确保cost_usd和price_usd不为None
                if doc.get('cost_usd') is None:
                    doc['cost_usd'] = "0"
                if doc.get('price_usd') is None:
                    doc['price_usd'] = "0"
            
            # 将活动记录转换为事件
            events = []
            for activity in activities:
                timestamp = activity.get('timestamp', 0)
                events.append(TransactionEvent(timestamp, activity))
                
            total_count += len(events)
            skip += batch_size
            
            logger.info(f"已加载 {total_count} 条活动记录")
            yield events
            
        logger.info(f"活动数据加载完成，共 {total_count} 条记录")
    
    async def get_token_info(self, token_address: str, skip_api_query: bool = None) -> Dict[str, Any]:
        """获取代币信息，如果数据库中不存在则通过API查询
        
        Args:
            token_address: 代币地址
            skip_api_query: 是否跳过API查询，如果为None则使用实例变量的值
            
        Returns:
            Dict[str, Any]: 代币信息
        """
        # 如果未指定skip_api_query，使用实例变量的值
        if skip_api_query is None:
            skip_api_query = self.skip_token_api_query
            
        # 首先从数据库查询
        token = await self.token_dao.find_by_address(token_address)
        
        if token:
            return token
            
        # 如果设置了跳过API查询，直接返回基本信息
        if skip_api_query:
            logger.info(f"跳过API查询，返回代币 {token_address} 的基本信息")
            return {
                'address': token_address,
                'symbol': 'UNKNOWN',
                'name': 'Unknown Token',
                'created_at': 0
            }
            
        # 数据库中不存在，通过API查询
        logger.info(f"数据库中不存在代币 {token_address}，通过API查询")
        token_info = await self.solana_monitor.get_token_info(token_address)
        
        if token_info:
            # 将代币信息保存到数据库
            token_info['address'] = token_address
            await self.token_dao.save_tokens([token_info])
            return token_info
        
        # API查询失败，返回基本信息
        return {
            'address': token_address,
            'symbol': 'UNKNOWN',
            'name': 'Unknown Token',
            'created_at': 0
        }
        
    async def get_tokens_for_addresses(self, token_addresses: List[str], skip_api_query: bool = None) -> Dict[str, Dict[str, Any]]:
        """批量获取代币信息
        
        Args:
            token_addresses: 代币地址列表
            skip_api_query: 是否跳过API查询，如果为None则使用实例变量的值
            
        Returns:
            Dict[str, Dict[str, Any]]: 地址到代币信息的映射
        """
        # 如果未指定skip_api_query，使用实例变量的值
        if skip_api_query is None:
            skip_api_query = self.skip_token_api_query
            
        logger.info(f"获取 {len(token_addresses)} 个代币的信息")
        
        # 从数据库查询所有代币
        db_tokens = await self.token_dao.find_by_addresses(token_addresses)
        
        # 创建地址到代币信息的映射
        result = {token['address']: token for token in db_tokens}
        
        # 找出数据库中不存在的代币地址
        missing_addresses = [addr for addr in token_addresses if addr not in result]
        
        if missing_addresses:
            logger.info(f"数据库中不存在 {len(missing_addresses)} 个代币，通过API查询")
            
            # 对于每个缺失的地址，通过API查询
            for address in missing_addresses:
                token_info = await self.get_token_info(address, skip_api_query)
                result[address] = token_info
                
        return result

    def get_token_price(self, token_address: str, timestamp: int) -> Dict[str, Any]:
        """获取代币在指定时间点的价格数据（同步方法，保留以向后兼容）
        
        Args:
            token_address: 代币地址
            timestamp: 时间戳
            
        Returns:
            Dict[str, Any]: 价格数据，包含open, high, low, close字段
        """
        # 检查缓存
        cache_key = f"{token_address}_{timestamp}"
        if cache_key in self._price_cache:
            return self._price_cache[cache_key]
            
        # 如果爬虫未初始化，返回空结果
        if not self._candles_spider:
            return {}
            
        try:
            # 获取K线数据
            # 由于这是同步方法，而爬虫方法是异步的，我们需要使用新的事件循环
            # 不过这可能在嵌套事件循环中导致问题，所以使用try-except捕获可能的错误
            loop = asyncio.new_event_loop()
            try:
                price_data = loop.run_until_complete(
                    self._candles_spider.get_price_point_in_time(token_address, timestamp)
                )
            finally:
                loop.close()
                
            # 如果获取到数据，缓存并返回
            if price_data:
                self._price_cache[cache_key] = price_data
                return price_data
        except Exception as e:
            logger.warning(f"获取价格数据失败: {e}")
            
        # 如果无法获取数据，返回空结果
        return {}
        
    async def get_token_price_async(self, token_address: str, timestamp: int) -> Dict[str, Any]:
        """异步获取代币在指定时间点的价格数据
        
        Args:
            token_address: 代币地址
            timestamp: 时间戳
            
        Returns:
            Dict[str, Any]: 价格数据，包含open, high, low, close字段
        """
        # 检查缓存
        cache_key = f"{token_address}_{timestamp}"
        if cache_key in self._price_cache:
            return self._price_cache[cache_key]
            
        # 如果爬虫未初始化，返回空结果
        if not self._candles_spider:
            return {}
            
        try:
            # 直接异步调用爬虫方法
            price_data = await self._candles_spider.get_price_point_in_time(token_address, timestamp)
                
            # 如果获取到数据，缓存并返回
            if price_data:
                self._price_cache[cache_key] = price_data
                return price_data
        except Exception as e:
            logger.warning(f"异步获取价格数据失败: {e}")
            
        # 如果无法获取数据，返回空结果
        return {}