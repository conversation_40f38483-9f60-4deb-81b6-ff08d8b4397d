from typing import List, Optional, Dict, Any
from datetime import datetime
from models.x_user import XUser
from .base_dao import BaseDAO

class XUserDAO(BaseDAO[XUser]):
    """X用户 DAO"""
    
    def __init__(self):
        super().__init__(XUser)
    
    async def find_all_users(self) -> List[XUser]:
        """获取所有用户"""
        return await self.model.find_all().to_list()
    
    async def find_by_username(self, username: str) -> Optional[XUser]:
        """根据用户名查找用户"""
        return await self.find_one({"username": username})
    
    async def find_by_usernames(self, usernames: List[str]) -> List[XUser]:
        """根据用户名列表批量查找用户"""
        return await self.find_many({"username": {"$in": usernames}})
    
    async def upsert_user(self, user_data: Dict[str, Any]) -> bool:
        """更新或插入用户数据"""
        username = user_data["username"]
        existing_user = await self.find_by_username(username)
        
        if existing_user:
            # 如果存在，保留first_seen_at
            user_data["first_seen_at"] = existing_user.first_seen_at
        else:
            # 如果不存在，设置first_seen_at为当前时间
            user_data["first_seen_at"] = datetime.utcnow()
        
        # 更新last_updated_at
        user_data["last_updated_at"] = datetime.utcnow()
        
        # 使用原子更新操作
        return await self.update_one(
            {"username": username},
            user_data,
            upsert=True
        )
    
    async def delete_user(self, username: str) -> bool:
        """删除用户"""
        return await self.delete_one({"username": username}) 