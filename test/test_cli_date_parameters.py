#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试交易统计CLI的日期参数功能
"""

import pytest
import asyncio
from datetime import datetime, timedelta, time
from unittest.mock import Mock, patch
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from utils.trading.statistics.cli import (
    parse_single_date, 
    get_yesterday_range, 
    get_last_week_range,
    validate_args
)


class TestDateParsing:
    """测试日期解析功能"""
    
    def test_parse_single_date_valid(self):
        """测试有效日期解析"""
        # 测试正常日期
        start_time, end_time = parse_single_date("2025-05-29")
        
        assert start_time == datetime(2025, 5, 29, 0, 0, 0, 0)
        assert end_time == datetime(2025, 5, 29, 23, 59, 59, 999999)
    
    def test_parse_single_date_invalid(self):
        """测试无效日期格式"""
        import argparse
        
        with pytest.raises(argparse.ArgumentTypeError):
            parse_single_date("2025/05/29")
        
        with pytest.raises(argparse.ArgumentTypeError):
            parse_single_date("05-29-2025")
        
        with pytest.raises(argparse.ArgumentTypeError):
            parse_single_date("invalid-date")
    
    def test_get_yesterday_range(self):
        """测试获取昨天时间范围"""
        start_time, end_time = get_yesterday_range()
        
        # 验证是昨天的日期
        yesterday = datetime.now().date() - timedelta(days=1)
        assert start_time.date() == yesterday
        assert end_time.date() == yesterday
        
        # 验证时间范围
        assert start_time.time() == time(0, 0, 0)
        assert end_time.time() == time(23, 59, 59, 999999)
    
    def test_get_last_week_range(self):
        """测试获取上周时间范围"""
        start_time, end_time = get_last_week_range()
        
        # 验证是一周的时间跨度
        delta = end_time.date() - start_time.date()
        assert delta.days == 6  # 周一到周日，相差6天
        
        # 验证开始是周一，结束是周日
        assert start_time.weekday() == 0  # 周一
        assert end_time.weekday() == 6   # 周日
        
        # 验证时间范围
        assert start_time.time() == time(0, 0, 0)
        assert end_time.time() == time(23, 59, 59, 999999)


class TestArgValidation:
    """测试参数验证功能"""
    
    def test_mutual_exclusion(self):
        """测试参数互斥性"""
        # 模拟参数对象
        args = Mock()
        
        # 测试date和days冲突
        args.date = "2025-05-29"
        args.yesterday = False
        args.last_week = False
        args.days = 7
        args.start_date = None
        args.end_date = None
        
        with pytest.raises(ValueError, match="时间范围参数互斥"):
            validate_args(args)
        
        # 测试yesterday和last_week冲突
        args.date = None
        args.yesterday = True
        args.last_week = True
        args.days = None
        
        with pytest.raises(ValueError, match="时间范围参数互斥"):
            validate_args(args)
    
    def test_date_parameter_processing(self):
        """测试date参数处理"""
        args = Mock()
        args.date = "2025-05-29"
        args.yesterday = False
        args.last_week = False
        args.days = None
        args.start_date = None
        args.end_date = None
        args.send_feishu = False
        args.output = None
        
        validate_args(args)
        
        # 验证参数被正确设置
        assert args.start_date == datetime(2025, 5, 29, 0, 0, 0, 0)
        assert args.end_date == datetime(2025, 5, 29, 23, 59, 59, 999999)
    
    def test_yesterday_parameter_processing(self):
        """测试yesterday参数处理"""
        args = Mock()
        args.date = None
        args.yesterday = True
        args.last_week = False
        args.days = None
        args.start_date = None
        args.end_date = None
        args.send_feishu = False
        args.output = None
        
        validate_args(args)
        
        # 验证参数被正确设置为昨天的时间范围
        yesterday = datetime.now().date() - timedelta(days=1)
        assert args.start_date.date() == yesterday
        assert args.end_date.date() == yesterday
        assert args.start_date.time() == time(0, 0, 0)
        assert args.end_date.time() == time(23, 59, 59, 999999)
    
    def test_last_week_parameter_processing(self):
        """测试last_week参数处理"""
        args = Mock()
        args.date = None
        args.yesterday = False
        args.last_week = True
        args.days = None
        args.start_date = None
        args.end_date = None
        args.send_feishu = False
        args.output = None
        
        validate_args(args)
        
        # 验证参数被正确设置为上周的时间范围
        delta = args.end_date.date() - args.start_date.date()
        assert delta.days == 6  # 周一到周日，相差6天
        assert args.start_date.weekday() == 0  # 周一
        assert args.end_date.weekday() == 6   # 周日


class TestIntegration:
    """集成测试"""
    
    def test_command_line_parsing(self):
        """测试命令行参数解析"""
        import argparse
        from utils.trading.statistics.cli import create_parser
        
        parser = create_parser()
        
        # 测试--yesterday参数
        args = parser.parse_args(['--yesterday'])
        assert args.yesterday is True
        assert args.date is None
        assert args.last_week is False
        
        # 测试--last-week参数
        args = parser.parse_args(['--last-week'])
        assert args.last_week is True
        assert args.yesterday is False
        assert args.date is None
        
        # 测试--date参数
        args = parser.parse_args(['--date', '2025-05-29'])
        assert args.date == '2025-05-29'
        assert args.yesterday is False
        assert args.last_week is False


if __name__ == '__main__':
    pytest.main([__file__, '-v']) 