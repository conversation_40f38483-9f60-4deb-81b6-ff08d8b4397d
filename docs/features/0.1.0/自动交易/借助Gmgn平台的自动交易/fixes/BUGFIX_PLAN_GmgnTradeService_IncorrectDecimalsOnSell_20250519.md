# Bug 修复方案：GMGN 交易服务卖出非 SOL 代币时小数位数错误

## 1. Bug 标识

- **描述**: 在通过 `GmgnTradeService` 执行自动卖出非 SOL 代币的操作时，系统错误地将从买入记录中获取的代币数量（已经是最小单位）当作UI单位数量传递，并在后续流程中再次乘以 \(10^{\text{decimals}}\) ，导致请求卖出的数量远超实际拥有的代币数量，从而引发 "token balance is less than available balance" 错误。
- **影响**: 导致用户无法成功自动卖出钱包中实际足额的非 SOL 代币。

## 2. 报告日期/发现日期

- **日期**: 2025-05-19 (根据AI与用户的交互确认)

## 3. 根源分析概要

最初的假设是 `utils/gmgn_trade_service.py` 在处理卖出SPL代币时错误地默认使用9位小数。然而，经过进一步分析和用户提供的日志/数据，核心问题被定位在 `workflows/monitor_kol_activity/sell_signal_handler.py`。

该 Handler 在确定要自动卖出的代币数量时，直接使用了从先前买入交易的 `TradeRecord.token_out_actual_amount` 字段获取的值。此字段存储的是代币的**最小单位数量**（例如 lamports）。但是，`SellSignalHandler` 随后将这个已经是最小单位的数量错误地作为 **UI单位数量** 传递给了 `GmgnTradeService.execute_trade` 方法的 `amount_input_token` 参数。

`GmgnTradeService` 接收到这个被误解为UI单位的巨大数值后，会再次使用正确获取到的小数位数（例如6）将其乘以 \(10^6\)，从而计算出一个天文数字般的 lamports 数量。这个最终的 lamports 数量被传递给下游的 Node.js 脚本或 GMGN API，自然远超用户实际拥有的代币余额，导致 "token balance is less than available balance" 错误。

例如，如果用户买入后实际拥有 `134.097247` 个某代币 (小数位数为6)，其最小单位为 `134097247`。
- `SellSignalHandler` 获取到 `134097247` (最小单位)。
- `SellSignalHandler` 错误地将其作为 `amount_input_token = 134097247.0` (UI单位) 传给 `GmgnTradeService`。
- `GmgnTradeService` (假设已知小数位为6) 计算 lamports: `134097247.0 * (10^6) = 134097247000000`。
- 这个 `134097247000000` 被发送给交易API，远大于实际持有的 `134097247`。

因此，修复的核心在于确保 `SellSignalHandler` 在将从买入记录获得的最小单位数量传递给 `GmgnTradeService` 之前，将其正确转换回 UI 单位。

## 4. 详细的、已获批准的修复方案

### 4.1. (已部分完成，逻辑正确) `utils/gmgn_trade_service.py` 中的 `execute_trade` 方法

该方法在其参数 `strategy_snapshot` (字典类型) 中接收一个名为 `input_token_decimals` 的键。该键的值应为正在被操作的 `input_token_address` 的实际小数位数。此部分的逻辑已按预期修改，能够根据提供的小数位数正确处理金额转换。

`input_decimals` 变量的确定逻辑：
1.  尝试从 `strategy_snapshot.get('input_token_decimals')` 获取小数位数。
2.  如果获取成功且值不为 `None`:
    *   验证其为整数并在合理范围内 (0-18)。失败则交易失败。
    *   成功则使用此值。
3.  如果 `input_token_address` 是 SOL，`input_decimals` 固定为 9。
4.  如果 `input_token_address` 不是 SOL，且 `strategy_snapshot` 未提供 `input_token_decimals`:
    *   配置错误，交易失败。
5.  使用最终确定的 `input_decimals` 将 `amount_input_token` (UI单位) 转换为 `in_amount_lamports_str` (最小单位字符串)。

### 4.2. (核心修复点) `workflows/monitor_kol_activity/sell_signal_handler.py` 中的 `process_sell_signal` 函数

这是主要的修复点。在调用 `trade_service.execute_trade` 以执行自动卖出操作之前，必须进行以下处理：

1.  **获取原始买入的最小单位数量**:
    *   当确定了原始的、成功的买入 `TradeRecord` 后，从 `original_buy_trade_record.token_out_actual_amount` 获取要卖出的代币数量。这个值是**最小单位数量** (例如 lamports)。将其存储在变量如 `lamports_to_sell_from_buy_record`。

2.  **获取代币的实际小数位数**:
    *   对于要卖出的代币 (`token_address`)，使用 `utils.spiders.solana.token_info.TokenInfo` 类获取其详细信息，特别是 `decimals` 字段。
    *   将获取到的小数位数存储在 `input_token_decimals_for_sell`，并同时注入到 `buy_strategy_snapshot['input_token_decimals']` 中，以便 `GmgnTradeService` 后续使用。

3.  **将最小单位数量转换回 UI 单位数量**:
    *   **这是关键步骤。** 如果成功获取了 `input_token_decimals_for_sell`：
        ```python
        from decimal import Decimal
        # ...
        if lamports_to_sell_from_buy_record is not None and input_token_decimals_for_sell is not None:
            ui_amount_to_sell_float = float(
                Decimal(str(lamports_to_sell_from_buy_record)) / (Decimal('10') ** input_token_decimals_for_sell)
            )
        else:
            # 处理无法确定UI数量的情况，例如跳过交易
            pass
        ```
    *   对于卖出 SOL 的情况，使用固定的9位小数进行此转换。

4.  **传递正确的UI数量给交易服务**:
    *   将计算得到的 `ui_amount_to_sell_float` 作为 `amount_input_token` 参数传递给 `trade_service.execute_trade` 方法。
    *   同时，在创建 PENDING 状态的卖出 `TradeRecord` 时，其 `token_in_amount` 字段也应记录这个 `ui_amount_to_sell_float`。

5.  **错误处理**:
    *   如果在获取小数位数或在将 lamports 转换回 UI 单位的过程中发生任何错误，应记录清晰的错误日志。
    *   交易应被安全地跳过（例如，创建状态为 SKIPPED 或 FAILED 的 `TradeRecord`），并提供明确的错误信息。不应继续调用 `trade_service.execute_trade`。

此修改确保了 `GmgnTradeService` 接收到的是正确的、以UI为单位的代币数量，然后它内部再结合从 `strategy_snapshot` 中获取的（由 `SellSignalHandler` 注入的）正确小数位数，将其转换为 lamports 传递给底层交易脚本。

## 5. 方案提出者/执行者

-   AI (Gemini 2.5 Pro, via Cursor)

## 6. 方案审阅者/批准者

-   用户 (您)

## 7. 方案批准日期

-   2025-05-19 (原始方案批准日期，根源分析及具体方案细节于后续讨论中修正)

## 8. 预期的验证方法

1.  **单元测试 (`test/utils/test_gmgn_trade_service.py`)**: (此部分测试 `GmgnTradeService` 按预期行为)
    *   已创建 `test_sell_spl_uses_correct_decimals_from_snapshot`：验证当 `strategy_snapshot` 正确提供了 `input_token_decimals` 时，`execute_trade` 能基于此小数位数正确计算 lamports。
    *   验证 `input_token_decimals` 缺失或无效（非整数、超出范围）时，交易会失败。
    *   验证卖出 SOL 时，即使不提供 `input_token_decimals`，也会默认使用9位小数。

2.  **集成/端到端测试 (`test/workflows/monitor_kol_activity/test_sell_signal_handler.py`)**: (此部分测试 `SellSignalHandler` 的核心修复逻辑)
    *   **场景1: 成功转换并调用交易**
        *   模拟一个成功的买入 `TradeRecord`，其中 `token_out_actual_amount` 是一个已知的 lamports 数量 (例如 `12345600` lamports)。
        *   Mock `TokenInfo.get_token_info()` 使其成功返回代币的小数位数 (例如 `6`)。
        *   验证 `SellSignalHandler`：
            *   正确计算出 UI 单位的卖出数量 (例如 `12.3456`)。
            *   将此 UI 单位数量 (`12.3456`) 传递给 `GmgnTradeService.execute_trade` 的 `amount_input_token` 参数。
            *   传递给 `GmgnTradeService.execute_trade` 的 `strategy_snapshot` 中包含了正确的 `input_token_decimals` (例如 `6`)。
        *   (可选) Mock `GmgnTradeService.execute_trade` 以确认其接收到的参数。
    *   **场景2: 获取小数位数失败**
        *   模拟 `TokenInfo.get_token_info()` 获取小数位数失败 (例如返回 `None` 或抛出异常)。
        *   验证 `SellSignalHandler`:
            *   创建了一个状态为 `SKIPPED` 或 `FAILED` 的 `TradeRecord`。
            *   记录了相应的错误日志。
            *   **没有** 调用 `GmgnTradeService.execute_trade`。
    *   **场景3: Lamports 转换为 UI 单位失败**
        *   模拟 `input_token_decimals_for_sell` 获取成功，但在 `Decimal(...) / (Decimal('10') ** ...)` 转换步骤中出现异常 (例如，如果 `lamports_to_sell_from_buy_record` 意外地不是数字类型)。
        *   验证行为与场景2类似：跳过交易，记录错误，不调用交易服务。
    *   **场景4: 卖出 SOL 代币**
        *   模拟买入记录表示获得了 SOL (lamports)。
        *   验证 `SellSignalHandler` 正确使用9位小数将其转换为UI单位，并传递给 `GmgnTradeService`，同时 `strategy_snapshot` 中包含 `input_token_decimals: 9`。

3.  **手动测试 (已执行，并帮助定位到真实根源)**:
    *   通过实际执行买入后自动卖出的流程，观察日志中 `SellSignalHandler` 计算的卖出数量、传递给 `GmgnTradeService` 的数量，以及最终 Node.js 脚本接收到的 lamports 数量，确认其与钱包实际余额和小数位数一致。 