# KOL活动监控工作流 - GMGN自动交易功能 - 测试用例设计

## 1. 概述

本文档定义了用于验证 `GmgnTradeService` 与KOL活动监控工作流集成的 **单元测试用例**。
由于 `GmgnTradeService` 现在通过调用外部 Node.js 脚本 (`gmgn_test.mjs`) 来执行其核心交易逻辑，单元测试的重点将从 mock HTTP API 调用和 Solana 库操作，转移到 **mock Node.js 脚本的执行及其各种输出**。

测试将严格通过 **mocking** `asyncio.create_subprocess_exec` (用于执行 Node.js 脚本) 及其返回的 `Process` 对象的行为，以及其他必要的依赖（如数据库DAO层、Telegram发送模块等）来覆盖成功路径、错误处理、配置变化和边界条件。本文档不涉及集成测试或端到端测试。所有测试都应是可独立运行和确定性的。

## 2. 测试环境与准备

-   **环境**:
    *   一个可以运行 `memeMonitor` 项目的Python环境。
    *   已安装所有依赖。
    *   可访问的MongoDB实例 (在测试中可能使用内存数据库如 `mongomock` 或针对DAO层进行mock)。
-   **核心原则**: 所有测试用例必须在不进行真实外部Node.js脚本调用或区块链交互的前提下执行。
-   **配置 (`.env`) Mocking**:
    *   测试中应通过 `unittest.mock.patch.dict` 或类似机制来模拟环境变量（如 `GMM_WALLET_PRIVATE_KEY`, `GMGN_API_HOST`, `NODE_SCRIPT_PATH`）的读取。
-   **数据准备 (Mocked/In-Memory)**:
    *   模拟 `ConfigDAO` 返回 `KolActivityConfig` 及其中 `SingleKolStrategyConfig` 的行为，包括所有自动交易相关参数。
    *   模拟 `KOLWalletActivityDAO`、`TokenDAO`、`SignalDAO`、`TradeRecordDAO` 等DAO层方法。
    *   模拟 `Signal` 数据以触发买入和卖出逻辑。
    *   模拟 `TradeRecord` 数据，特别是用于卖出逻辑时，需要一个关联的成功的买入 `TradeRecord`。
-   **Mocking/Patching Strategy (Unit Test Level)**:
    *   **Node.js 脚本执行 (`GmgnTradeService`)**:
        *   使用 `unittest.mock.patch('asyncio.create_subprocess_exec')` 来 mock Node.js 脚本的调用。
        *   Mock `asyncio.create_subprocess_exec` 返回一个 mock 的 `Process` 对象。
        *   Mock `Process` 对象的 `communicate()` 方法以返回不同的 `stdout` (包含JSON字符串或无效数据), `stderr` (包含错误信息或为空) 和 `returncode` (0 表示成功, 非0表示失败)。
        *   覆盖场景：
            *   脚本成功执行，`stdout` 返回有效的JSON，包含 `status: "success"`, `txHash`, `quoteResponse` 等。
            *   脚本成功执行，`stdout` 返回有效的JSON，包含 `status: "error"`, `message`。
            *   脚本成功执行，`stdout` 返回有效的JSON，包含 `status: "submitted_no_poll"`, `txHash`。
            *   脚本成功执行，但 `stdout` 不是有效的JSON。
            *   脚本执行失败 (`returncode != 0`)，`stderr` 包含错误信息。
            *   脚本文件未找到 (`FileNotFoundError` 在 `create_subprocess_exec` 层面，或通过 `returncode` 和 `stderr` 模拟脚本内部的此类错误)。
    *   **Telegram Notifications**: 负责发送普通用户信号通知和管理员错误通知的模块/函数应被mock。
    *   **DAO Operations**: 所有数据库交互都应通过mock DAO对象的方法来完成。

## 3. 测试用例

### 3.1. 买入交易 (`handler.py` - 自动交易逻辑)

**用例 TC-BUY-001: 成功执行自动买入 (Node.js 脚本返回成功)**
-   **描述**: 验证当买入信号生成且自动交易启用时，`GmgnTradeService` 成功调用Node.js脚本，脚本返回成功，并正确记录交易。
-   **前提条件**:
    -   `auto_trade_enabled` 为 `True`.
    -   环境变量配置正确。
-   **步骤**:
    1.  Mock `GmgnTradeService.execute_trade` 返回 `TradeResult(status=TradeStatus.SUCCESS, tx_hash='mock_tx_hash_buy', ...)`。
    2.  调用 `send_message_to_channel`。
-   **预期结果**:
    1.  `GmgnTradeService.execute_trade` 被正确调用。
    2.  创建 `TradeRecord` (状态 `SUCCESS`)。
    3.  `Signal` 记录更新。
    4.  Telegram通知 (用户/管理员) 按预期发送。

**用例 TC-BUY-002: 自动买入因 Node.js 脚本报告错误而失败**
-   **描述**: 验证当 `GmgnTradeService` 调用Node.js脚本，脚本返回 `status: "error"` 时，交易标记为失败。
-   **步骤**:
    1.  Mock `GmgnTradeService.execute_trade` 返回 `TradeResult(status=TradeStatus.FAILED, error_message='Node.js script error: ...', provider_response_raw=...)`。
    2.  调用 `send_message_to_channel`。
-   **预期结果**:
    1.  `TradeRecord` 状态 `FAILED`，包含错误信息。
    2.  管理员Telegram通知发送。
    3.  用户Telegram通知包含失败信息。

**用例 TC-BUY-003: 自动买入配置禁用而被跳过**
-   **描述**: 验证当 `auto_trade_enabled` 为 `False` 时，不执行自动交易。
-   **预期结果**: `GmgnTradeService.execute_trade` 未被调用。

**用例 TC-BUY-004: 自动买入因私钥缺失/无效失败 (由 `GmgnTradeService` 在调用脚本前检测或脚本返回特定错误)**
-   **描述**: 验证如果私钥配置错误，交易失败。
-   **步骤**: Mock `GmgnTradeService.execute_trade` 返回 `TradeResult(status=TradeStatus.FAILED, error_message='Invalid or missing wallet private key')`。
-   **预期结果**: `TradeRecord` 状态 `FAILED`，管理员收到通知。

**用例 TC-BUY-005: 自动买入因资金不足失败 (Node.js 脚本报告)**
-   **描述**: Node.js 脚本因资金不足返回错误。
-   **步骤**: Mock `GmgnTradeService.execute_trade` 返回 `TradeResult(status=TradeStatus.FAILED, error_message='Node.js script error: Insufficient SOL balance')`。
-   **预期结果**: `TradeRecord` 状态 `FAILED`，管理员收到通知。

**用例 TC-BUY-006: 自动买入因 Node.js 脚本执行本身失败 (非零返回码)**
-   **描述**: Node.js 脚本崩溃或因未捕获异常返回非零 `returncode`。
-   **步骤**: Mock `GmgnTradeService.execute_trade` 返回 `TradeResult(status=TradeStatus.FAILED, error_message='Node.js script execution failed with return code X. Stderr: ...')`。
-   **预期结果**: `TradeRecord` 状态 `FAILED`，管理员收到通知。

**用例 TC-BUY-007: 自动买入因 Node.js 脚本输出无法解析 (非JSON)**
-   **描述**: Node.js 脚本 `stdout` 不是有效JSON。
-   **步骤**: Mock `GmgnTradeService.execute_trade` 返回 `TradeResult(status=TradeStatus.FAILED, error_message='Failed to parse JSON output from Node.js script. Raw stdout: ...')`。
-   **预期结果**: `TradeRecord` 状态 `FAILED`，管理员收到通知。

### 3.2. 卖出交易 (`sell_signal_handler.py` - 自动交易逻辑)

(测试用例结构类似于买入交易，但关注卖出特定逻辑)

**用例 TC-SELL-001: 成功执行自动卖出 (Node.js 脚本返回成功)**
-   **描述**: 验证当卖出条件满足、自动卖出启用、相关配置正确（包括`gmgn_api_host`的正确获取）且找到有效买入记录时，`GmgnTradeService` 成功调用Node.js脚本执行卖出，脚本返回成功，并正确记录交易。
-   **前提条件**:
    -   `buy_strategy_snapshot` 包含 `gmgn_enable_auto_sell = True`。
    -   `buy_strategy_snapshot` 包含有效的 `gmgn_api_host` (或环境变量 `GMGN_API_HOST` 有效，如果前者为空)。
    -   `buy_strategy_snapshot` 包含有效的 `gmgn_private_key_env_var` 且对应环境变量已设置。
    -   `buy_strategy_snapshot` 包含有效的 `gmgn_sol_wallet_address`。
    -   存在一个关联的、成功的买入 `TradeRecord` 且 `token_out_actual_amount > 0`。
-   **步骤**:
    1.  Mock `trade_record_dao.get_by_id` (或类似方法) 返回一个成功的买入 `TradeRecord`。
    2.  Mock `os.getenv` 返回有效的私钥和 (如果策略快照中没有) `gmgn_api_host`。
    3.  Mock `GmgnTradeService.__init__` 验证传入的 `gmgn_api_host` 符合预期 (来自快照或环境变量)。
    4.  Mock `GmgnTradeService.execute_trade` (type SELL) 返回 `TradeResult(status=TradeStatus.SUCCESS, tx_hash='mock_tx_hash_sell', ...)`。
    5.  调用 `process_sell_signal` 处理一个触发自动卖出的信号数据。
-   **预期结果**:
    1.  `GmgnTradeService.execute_trade` 被正确调用。
    2.  创建 `TradeRecord` (状态 `SUCCESS`，类型 `SELL`)。
    3.  原始买入 `Signal` 记录状态更新为 `sold`。
    4.  创建卖出 `Signal` 记录。
    5.  Telegram通知 (用户/管理员) 按预期发送，包含成功交易信息。

**用例 TC-SELL-002: 自动卖出因找不到原始买入记录/数量而跳过**
-   **描述**: 验证当自动卖出启用，但无法找到对应的成功买入交易记录或买入数量无效时，交易被跳过。
-   **前提条件**:
    -   `buy_strategy_snapshot` 包含 `gmgn_enable_auto_sell = True`。
    -   `buy_strategy_snapshot` 包含 `gmgn_sol_wallet_address = "test_wallet_address_skipped"`。
-   **步骤**:
    1.  Mock `trade_record_dao.get_by_id` 返回 `None` 或一个不符合条件的买入 `TradeRecord` (例如，非成功状态或数量为0)。
    2.  Mock `trade_record_dao.save` 以捕获创建的 `TradeRecord`。
    3.  调用 `process_sell_signal`。
-   **预期结果**:
    1.  `GmgnTradeService.execute_trade` 未被调用。
    2.  创建 `TradeRecord` (状态 `SKIPPED`，类型 `SELL`)，其 `error_message` 指明原因。
    3.  `TradeRecord` 的 `wallet_address` 应为 `"test_wallet_address_skipped"` (或相应的 fallback "UNKNOWN" 如果快照中此字段也缺失)。
    4.  Telegram通知包含跳过信息。

**用例 TC-SELL-003: 自动卖出因 Node.js 脚本报告错误而失败**
-   **描述**: 验证当自动卖出流程中 `GmgnTradeService` 调用Node.js脚本，脚本返回 `status: "error"` 时，交易标记为失败。
-   **前提条件**: (同 TC-SELL-001，除了 `GmgnTradeService.execute_trade` 的 mock 返回)
-   **步骤**:
    1.  (同 TC-SELL-001 前置步骤)
    2.  Mock `GmgnTradeService.execute_trade` (type SELL) 返回 `TradeResult(status=TradeStatus.FAILED, error_message='Node.js script error during sell: ...', provider_response_raw=...)`。
    3.  调用 `process_sell_signal`。
-   **预期结果**:
    1.  创建 `TradeRecord` (状态 `FAILED`，类型 `SELL`)，包含错误信息。
    2.  管理员Telegram通知发送。
    3.  用户Telegram通知包含失败信息。

**用例 TC-SELL-004: 自动卖出配置禁用而被跳过**
-   **描述**: 验证当 `buy_strategy_snapshot` 中 `gmgn_enable_auto_sell` 为 `False` 时，不执行自动卖出。
-   **前提条件**:
    -   `buy_strategy_snapshot` 包含 `gmgn_enable_auto_sell = False`。
    -   `buy_strategy_snapshot` 包含 `gmgn_sol_wallet_address = "test_wallet_address_disabled"`。
-   **步骤**:
    1.  Mock `trade_record_dao.save` 以捕获创建的 `TradeRecord`。
    2.  调用 `process_sell_signal`。
-   **预期结果**:
    1.  `GmgnTradeService.execute_trade` (type SELL) 未被调用。
    2.  创建 `TradeRecord` (状态 `SKIPPED`，类型 `SELL`)，其 `error_message` 指明原因为"自动卖出禁用"。
    3.  `TradeRecord` 的 `wallet_address` 应为 `"test_wallet_address_disabled"` (或相应的 fallback "CONFIG_DISABLED" 如果快照中此字段也缺失)。

**用例 TC-SELL-API-HOST-001: `gmgn_api_host` 从策略快照成功获取 (卖出场景)**
-   **描述**: 验证在卖出流程中，当策略快照中提供了 `gmgn_api_host` 时，会优先使用该值。
-   **前提条件**:
    -   自动卖出启用并满足所有前置条件以尝试交易。
    -   `buy_strategy_snapshot` 包含 `gmgn_api_host = "host_from_snapshot"`。
    -   环境变量 `GMGN_API_HOST` 设置为 `"host_from_env"`。
-   **步骤**:
    1.  (设置其他必要的 mocks 以使流程到达 `GmgnTradeService` 初始化点)
    2.  Mock `GmgnTradeService.__init__`。
    3.  调用 `process_sell_signal`。
-   **预期结果**:
    1.  `GmgnTradeService.__init__` 被调用时，其 `gmgn_api_host` 参数为 `"host_from_snapshot"`。

**用例 TC-SELL-API-HOST-002: `gmgn_api_host` 从环境变量成功获取 (卖出场景, 策略快照中无此配置)**
-   **描述**: 验证在卖出流程中，当策略快照中未提供 `gmgn_api_host` (或为 `None`) 时，会回退使用环境变量 `GMGN_API_HOST` 的值。
-   **前提条件**:
    -   自动卖出启用并满足所有前置条件以尝试交易。
    -   `buy_strategy_snapshot` 中 `gmgn_api_host` 为 `None` 或不存在该键。
    -   环境变量 `GMGN_API_HOST` 设置为 `"host_from_env"`。
-   **步骤**:
    1.  (设置其他必要的 mocks)
    2.  Mock `GmgnTradeService.__init__`。
    3.  调用 `process_sell_signal`。
-   **预期结果**:
    1.  `GmgnTradeService.__init__` 被调用时，其 `gmgn_api_host` 参数为 `"host_from_env"`。

**用例 TC-SELL-API-HOST-003: `gmgn_api_host` 获取失败导致卖出跳过 (策略快照和环境变量均无此配置)**
-   **描述**: 验证在卖出流程中，当策略快照和环境变量中都未提供 `gmgn_api_host` 时，交易应被跳过并记录配置错误。
-   **前提条件**:
    -   自动卖出启用并满足前置条件以尝试交易（除了API Host）。
    -   `buy_strategy_snapshot` 中 `gmgn_api_host` 为 `None` 或不存在该键。
    -   环境变量 `GMGN_API_HOST` 未设置或为空。
    -   `buy_strategy_snapshot` 包含 `gmgn_sol_wallet_address = "test_wallet_no_host"`。
-   **步骤**:
    1.  (设置其他必要的 mocks, 如私钥和钱包地址)
    2.  Mock `trade_record_dao.save` 以捕获创建的 `TradeRecord`。
    3.  调用 `process_sell_signal`。
-   **预期结果**:
    1.  `GmgnTradeService.execute_trade` 未被调用。
    2.  创建 `TradeRecord` (状态 `SKIPPED`，类型 `SELL`)，其 `error_message` 指出 `gmgn_api_host` 配置缺失。
    3.  `TradeRecord` 的 `wallet_address` 应为 `"test_wallet_no_host"` (或相应的 fallback "CONFIG_DISABLED" 如果快照中此字段也缺失)。

**用例 TC-SELL-SKIP-WALLET-001: 跳过交易 (无买入记录) - `wallet_address` 正确获取**
-   **描述**: (同原 TC-SELL-002，强调 `wallet_address` 来源) 验证在因"无成功买入记录"而跳过卖出交易时，创建的 SKIPPED `TradeRecord` 中 `wallet_address` 是从策略快照的 `gmgn_sol_wallet_address` 字段获取的。
-   **前提条件**:
    -   `buy_strategy_snapshot` 包含 `gmgn_enable_auto_sell = True`。
    -   `buy_strategy_snapshot` 包含 `gmgn_sol_wallet_address = "wallet_for_no_buy_skip"`。
-   **步骤**:
    1.  Mock `trade_record_dao.get_by_id` 返回 `None`。
    2.  Mock `trade_record_dao.save` 并捕获 `TradeRecord`。
    3.  调用 `process_sell_signal`。
-   **预期结果**:
    1.  保存的 SKIPPED `TradeRecord` 的 `wallet_address` 为 `"wallet_for_no_buy_skip"`。

**用例 TC-SELL-SKIP-WALLET-002: 跳过交易 (配置不完整, 如私钥缺失) - `wallet_address` 正确获取**
-   **描述**: 验证在因"自动卖出配置不完整（例如私钥缺失）"而跳过卖出交易时，创建的 SKIPPED `TradeRecord` 中 `wallet_address` 是从策略快照的 `gmgn_sol_wallet_address` 字段获取的。
-   **前提条件**:
    -   自动卖出启用，可找到买入记录。
    -   `buy_strategy_snapshot` 包含 `gmgn_api_host` 和 `gmgn_sol_wallet_address = "wallet_for_config_skip"`。
    -   `buy_strategy_snapshot` 中 `gmgn_private_key_env_var` 设置，但 `os.getenv` 对其返回 `None`。
-   **步骤**:
    1.  Mock `trade_record_dao.get_by_id` 返回成功的买入记录。
    2.  Mock `os.getenv` for private key env var to return `None`.
    3.  Mock `trade_record_dao.save` 并捕获 `TradeRecord`。
    4.  调用 `process_sell_signal`。
-   **预期结果**:
    1.  保存的 SKIPPED `TradeRecord` 的 `wallet_address` 为 `"wallet_for_config_skip"` (或 fallback "CONFIG_DISABLED" 如果快照中 `gmgn_sol_wallet_address` 也缺失，但本例中假设其存在)。
    2.  `error_message` 指出配置不完整。

**用例 TC-SELL-SKIP-WALLET-003: 跳过交易 (自动卖出禁用) - `wallet_address` 正确获取**
-   **描述**: (同原 TC-SELL-004，强调 `wallet_address` 来源) 验证在因"自动卖出被策略禁用"而跳过卖出交易时，创建的 SKIPPED `TradeRecord` 中 `wallet_address` 是从策略快照的 `gmgn_sol_wallet_address` 字段获取的。
-   **前提条件**:
    -   `buy_strategy_snapshot` 包含 `gmgn_enable_auto_sell = False`。
    -   `buy_strategy_snapshot` 包含 `gmgn_sol_wallet_address = "wallet_for_disabled_skip"`。
-   **步骤**:
    1.  Mock `trade_record_dao.save` 并捕获 `TradeRecord`。
    2.  调用 `process_sell_signal`。
-   **预期结果**:
    1.  保存的 SKIPPED `TradeRecord` 的 `wallet_address` 为 `"wallet_for_disabled_skip"`。

### 3.3. `GmgnTradeService` 单元测试 (Mocking `asyncio.create_subprocess_exec`)

这些测试用例与 `test/utils/test_gmgn_trade_service.py` 中已实现的测试用例对齐。

**用例 TC-GTS-BUY-001: 买入成功**
-   **描述**: 验证通过 Node.js 脚本成功执行买入交易。
-   **Mock `create_subprocess_exec`**:
    -   `stdout`: `{"status": "success", "txHash": "mock_tx_buy", "quoteResponse": {"inAmount": "100", "outAmount": "2000"}}`
    -   `stderr`: `b''`
    -   `returncode`: 0
-   **预期 `TradeResult`**: `status=SUCCESS, tx_hash="mock_tx_buy", actual_in=Decimal("100"), actual_out=Decimal("2000")`

**用例 TC-GTS-SELL-001: 卖出成功**
-   **描述**: 验证通过 Node.js 脚本成功执行卖出交易。
-   **Mock `create_subprocess_exec`**:
    -   `stdout`: `{"status": "success", "txHash": "mock_tx_sell", "quoteResponse": {"inAmount": "1000", "outAmount": "50"}}`
    -   `stderr`: `b''`
    -   `returncode`: 0
-   **预期 `TradeResult`**: `status=SUCCESS, tx_hash="mock_tx_sell", actual_in=Decimal("1000"), actual_out=Decimal("50")`

**用例 TC-GTS-002: Node.js 脚本返回 "error" 状态**
-   **描述**: Node.js 脚本执行成功，但其内部逻辑返回 "error" 状态。
-   **Mock `create_subprocess_exec`**:
    -   `stdout`: `{"status": "error", "message": "API key invalid", "details": {"code": 401}}`
    -   `stderr`: `b''`
    -   `returncode`: 0
-   **预期 `TradeResult`**: `status=FAILED, error_message="Node.js script error: API key invalid", provider_response_raw` 包含 `{"status": "error", ...}`

**用例 TC-GTS-003: Node.js 脚本返回 "submitted_no_poll" 状态**
-   **描述**: Node.js 脚本成功提交交易但指示Python层不要轮询状态。
-   **Mock `create_subprocess_exec`**:
    -   `stdout`: `{"status": "submitted_no_poll", "txHash": "mock_tx_pending", "message": "Submitted, poll externally"}`
    -   `stderr`: `b''`
    -   `returncode`: 0
-   **预期 `TradeResult`**: `status=PENDING, tx_hash="mock_tx_pending", error_message="Submitted, poll externally"`

**用例 TC-GTS-004: Node.js 脚本执行失败 (非零返回码)**
-   **描述**: Node.js 脚本由于内部错误（例如语法错误）而执行失败，返回非零退出码。
-   **Mock `create_subprocess_exec`**:
    -   `stdout`: `b''` (或者部分非JSON输出)
    -   `stderr`: `b'SyntaxError: Unexpected token'`
    -   `returncode`: 1
-   **预期 `TradeResult`**: `status=FAILED, error_message="Node.js script execution failed with return code 1. Stderr: b'SyntaxError: Unexpected token'"`

**用例 TC-GTS-005: Node.js 脚本 `stdout` 不是有效JSON**
-   **描述**: Node.js 脚本执行成功 (返回码为0)，但其标准输出不是有效的JSON格式。
-   **Mock `create_subprocess_exec`**:
    -   `stdout`: `b'This is not JSON'`
    -   `stderr`: `b''`
    -   `returncode`: 0
-   **预期 `TradeResult`**: `status=FAILED, error_message="Failed to parse JSON output from Node.js script. Raw stdout: b'This is not JSON'"`

**用例 TC-GTS-006: Node.js 脚本 `stdout` 是JSON但缺少 `status` 字段**
-   **描述**: Node.js 脚本的标准输出是有效的JSON，但缺少必要的 `status` 字段。
-   **Mock `create_subprocess_exec`**:
    -   `stdout`: `{"txHash": "some_hash"}`
    -   `stderr`: `b''`
    -   `returncode`: 0
-   **预期 `TradeResult`**: `status=FAILED, error_message="Node.js script output missing 'status' field. Raw stdout: ..."` (具体错误信息可能根据实现而定)

**用例 TC-GTS-007: Node.js 脚本未找到 (模拟 `FileNotFoundError`)**
-   **描述**: 当指定的Node.js脚本路径无效时，`asyncio.create_subprocess_exec` 抛出 `FileNotFoundError`。
-   **Mock `create_subprocess_exec`**:
    -   (配置 `patch` 以在调用时 `raise FileNotFoundError("Script not found")`)
-   **预期 `TradeResult`**: `status=FAILED, error_message="Node.js script not found at path: <NODE_SCRIPT_PATH>"` (或包含实际的FileNotFoundError信息)

**用例 TC-GTS-008: `execute_trade` 调用时 `NODE_SCRIPT_PATH` 为 `None` 或无效**
-   **描述**: 在调用 `execute_trade` 之前，如果 `GmgnTradeService` 实例的 `node_script_path` 属性（或等效配置）未设置或无效。
-   **Mock `create_subprocess_exec`**:
    -   (可能不需要mock，而是测试服务内部对脚本路径的检查逻辑)
    -   或者，如果服务不直接检查，但脚本路径确实是 `None`，则 `asyncio.create_subprocess_exec` 可能会因第一个参数无效而失败。
-   **预期 `TradeResult`**: `status=FAILED, error_message="Node.js script path is not configured."` (或由 `create_subprocess_exec` 引发的具体错误)

**用例 TC-GTS-009: `quoteResponse` 缺失或 `inAmount`/`outAmount` 解析失败**
-   **描述**: Node.js 脚本成功返回，但其输出的 `quoteResponse` 中缺少 `inAmount` 或 `outAmount`，或者这些字段无法正确解析为 `Decimal`。
-   **Mock `create_subprocess_exec`**:
    -   `stdout`: `{"status": "success", "txHash": "mock_tx_no_quote_amounts", "quoteResponse": {"something": "else"}}` (缺少inAmount/outAmount)
    -   `stderr`: `b''`
    -   `returncode`: 0
-   **预期 `TradeResult`**: `status=SUCCESS, tx_hash="mock_tx_no_quote_amounts", actual_in=None, actual_out=None` (日志中应记录解析失败的警告)。

**用例 TC-GTS-010: `execute_trade` 因 `trade_record_id` 为空字符串而日志中不包含它**
-   **描述**: 验证当 `trade_record_id` 作为空字符串传递时，日志消息中不应出现 `[TradeRec:]` 格式的前缀。
-   **Mock `create_subprocess_exec`**:
    -   `stdout`: `{"status": "success", "txHash": "mock_tx_no_id"}`
    -   `stderr`: `b''`
    -   `returncode`: 0
-   **预期 `TradeResult`**: `status=SUCCESS`。此外，需要检查日志输出，确保不包含 `[TradeRec:]` （或类似针对空ID的特殊处理）。

**用例 TC-GTS-011: Node.js 脚本返回 `stdout` 和 `stderr` 都有内容 (stderr 应被记录)**
-   **描述**: Node.js 脚本成功执行，但同时在 `stderr` 中输出了一些警告或调试信息。这些 `stderr` 信息应该被Python服务记录。
-   **Mock `create_subprocess_exec`**:
    -   `stdout`: `{"status": "success", "txHash": "mock_tx_with_stderr"}`
    -   `stderr`: `b'Warning: Deprecated feature used.'`
    -   `returncode`: 0
-   **预期 `TradeResult`**: `status=SUCCESS`。此外，需要检查日志输出，确保 `stderr` 的内容 (`Warning: Deprecated feature used.`) 被记录（例如，通过 `logger.info` 或 `logger.warning`）。

**用例 TC-GTS-012: Node.js 脚本返回的 `txHash` 为 `None` 或空字符串**
-   **描述**: Node.js 脚本成功执行，但返回的 `txHash` 字段为 `null` 或空字符串。
-   **Mock `create_subprocess_exec`**:
    -   `stdout`: `{"status": "success", "txHash": null, "quoteResponse": {"inAmount": "10", "outAmount": "20"}}`
    -   `stderr`: `b''`
    -   `returncode`: 0
-   **预期 `TradeResult`**: `status=SUCCESS, tx_hash=None, actual_in=Decimal("10"), actual_out=Decimal("20")`

**用例 TC-GTS-013: `execute_trade` 传递给Node.js脚本的参数正确性检查**
-   **描述**: 验证 `GmgnTradeService` 在调用 `asyncio.create_subprocess_exec` 时，传递给 Node.js 脚本的命令行参数列表是否正确构建。
-   **Mock `create_subprocess_exec`**:
    -   (Mock `asyncio.create_subprocess_exec` 以捕获其 `call_args`)
    -   `stdout`: `{"status": "success", "txHash": "mock_tx_args_check"}` (或其他表示成功的最小输出)
    -   `stderr`: `b''`
    -   `returncode`: 0
-   **预期 `TradeResult`**: `status=SUCCESS`。核心验证在于检查传递给 mock `create_subprocess_exec` 的 `cmd` 参数列表，确保其中包含正确的私钥、金额（转换为原子单位的字符串）、代币地址、滑点、优先费用和 API Host 等，且顺序和格式符合预期。

### 3.4. 数据模型和DAO单元测试
(与之前版本类似，但重点是 `TradeRecord` 如何根据Node.js脚本的输出填充字段，如 `provider_response_raw` 存储脚本的stdout/stderr)

**用例 TC-DM-001: `TradeRecord` 创建和保存 (成功交易, Node.js脚本返回成功)**
-   **预期结果**: `TradeRecordDAO.insert_record` 被调用，`TradeRecord` 包含: `status=TradeStatus.SUCCESS`, `tx_hash` (来自脚本), `provider_response_raw` 包含脚本的 `stdout` (JSON)。

**用例 TC-DM-002: `TradeRecord` 创建和保存 (失败交易, Node.js脚本报告错误)**
-   **预期结果**: `TradeRecordDAO` 被调用，`TradeRecord` 包含: `status=TradeStatus.FAILED`, `error_message` (来自脚本的 `message`), `provider_response_raw` 包含脚本的 `stdout` (JSON) 和可能的 `stderr`。

**用例 TC-DM-003: `TradeRecord` 创建和保存 (失败交易, Node.js脚本执行失败)**
-   **预期结果**: `TradeRecordDAO` 被调用，`TradeRecord` 包含: `status=TradeStatus.FAILED`, `error_message` (包含 `returncode` 和 `stderr`), `provider_response_raw` 包含脚本的 `stderr`。

## 4. 注意事项 (严格为单元测试上下文)

-   所有测试都必须是确定性的。
-   **Mocking `asyncio.create_subprocess_exec` 是核心**: 这是验证 `GmgnTradeService` 行为的关键。
-   测试覆盖应包括成功路径、所有定义的错误处理路径 (脚本成功但逻辑失败、脚本执行失败、脚本输出解析失败)、以及关键的边界条件。
