import unittest
from unittest.mock import AsyncMock, MagicMock, patch, call
import asyncio # For subprocess
import json
from decimal import Decimal
from datetime import datetime, timezone
import os
import logging

from utils.trading.solana.gmgn_trade_service import GmgnTradeService, SOL_MINT_ADDRESS, _calculate_next_priority_fee # NODE_SCRIPT_PATH is in gmgn_trade_service
from utils.trading.solana.trade_interface import TradeType, TradeStatus, TradeResult
from beanie import PydanticObjectId
# from models.config import SingleKolStrategyConfig # Not strictly needed if we pass dict directly

# Define the base path for the service under test for @patch decorators
BASE_PATH_FOR_SERVICE_UNDER_TEST = 'utils.trading.solana.gmgn_trade_service'

# Mock for the Process object returned by asyncio.create_subprocess_exec
def create_mock_process(stdout: bytes, stderr: bytes, returncode: int) -> MagicMock:
    mock_proc = MagicMock(spec=asyncio.subprocess.Process)
    mock_proc.returncode = returncode
    mock_proc.communicate = AsyncMock(return_value=(stdout, stderr))
    return mock_proc

async def mock_sleep_impl(*args, **kwargs): # Helper async function for sleep side_effect
    return None

class TestGmgnTradeService(unittest.IsolatedAsyncioTestCase):

    def setUp(self):
        self.gmgn_api_host = "https://fake-gmgn-api.com"
        
        # Patch NODE_SCRIPT_PATH directly in the module where it's used
        # This patch will be temporarily stopped for test_node_script_path_is_absolute_and_correct_filename
        self.patcher_node_script_path = patch('utils.trading.solana.gmgn_trade_service.NODE_SCRIPT_PATH', "mock_node_script.mjs")
        self.mock_node_script_path = self.patcher_node_script_path.start()
        # self.addCleanup(self.patcher_node_script_path.stop) # Cleanup will be handled more granularly or remains for other tests

        self.service = GmgnTradeService(gmgn_api_host=self.gmgn_api_host)
        
        self.default_strategy_snapshot_dict: dict = {
            "strategy_name": "test_strat",
            "gmgn_buy_slippage_percentage": 0.5,
            "gmgn_sell_slippage_percentage": 0.6,
            "gmgn_buy_priority_fee": 0.0001,
            "gmgn_sell_priority_fee": 0.0002,
        }
        
        # 确保对以下模块级别函数的引用是正确的，以便patch可以正确工作
        self.asyncio_sleep_path = 'utils.trading.solana.gmgn_trade_service.asyncio.sleep'
        self.execute_single_attempt_path = f'{BASE_PATH_FOR_SERVICE_UNDER_TEST}.GmgnTradeService._execute_single_trade_attempt'
        
        self.default_wallet_private_key_b58 = "5 PRIVKEY5 PRIVKEY5 PRIVKEY5 PRIVKEY5 PRIVKEY5 PRIVKEY5 PRIVKEY5 PRIVKEY"
        self.default_wallet_address = "EKoC7n1is5hL2g3b2YJ4c2qj2f8e8w7F6k5D4C3b2a1"
        self.default_input_token_address_buy = SOL_MINT_ADDRESS
        self.default_output_token_address_buy = "TOKENMINTADDRESSNEWTOKENBUYXYZ123456"
        self.default_amount_input_token_buy = 0.01 
        self.default_signal_id = PydanticObjectId()
        self.default_trade_record_id = PydanticObjectId()

        # Main mock for subprocess execution
        self.patcher_create_subprocess_exec = patch('asyncio.create_subprocess_exec')
        self.mock_create_subprocess_exec = self.patcher_create_subprocess_exec.start()
        # Set a default mock process for communicate() to avoid unpack errors if a test doesn't set its own
        self.mock_create_subprocess_exec.return_value = create_mock_process(stdout=b'{}', stderr=b'', returncode=0) 
        self.addCleanup(self.patcher_create_subprocess_exec.stop)
        
        # Mock datetime.now for consistent executed_at timestamps
        self.patcher_datetime_now = patch('utils.trading.solana.gmgn_trade_service.datetime')
        self.mock_datetime = self.patcher_datetime_now.start()
        self.mock_datetime.now.return_value = datetime(2023, 1, 1, 12, 0, 0, tzinfo=timezone.utc)
        self.addCleanup(self.patcher_datetime_now.stop)

        # Ensure the logger is in a known state before tests that use assertLogs
        # This might help if other tests or reloads are affecting its state.
        logger_to_test = logging.getLogger('utils.gmgn_trade_service')
        logger_to_test.setLevel(logging.DEBUG) # Ensure it's not too high for assertLogs to capture
        logger_to_test.propagate = True
        logger_to_test.disabled = False

    # Clean up the global patcher if it was started in setUp for the class
    def tearDown(self):
        if hasattr(self, 'patcher_node_script_path'): # Check if patcher exists
            try:
                self.patcher_node_script_path.stop()
            except RuntimeError: # Raised if patcher was not started
                pass
        # Other patchers started with addCleanup will be stopped automatically

    async def test_execute_trade_buy_node_script_success(self):
        """Test successful BUY trade via Node.js script."""
        mock_stdout_data = {
            "status": "success",
            "txHash": "mock_tx_hash_buy_success",
            "message": "Trade completed successfully.",
            "quoteResponse": {"data": {"quote": {"inAmount": "10000000", "outAmount": "20000000000"}}},
            "submitResponse": {"txId": "mock_tx_hash_buy_success"},
            "statusResponse": {"status": "confirmed"}
        }
        self.mock_process = create_mock_process(stdout=json.dumps(mock_stdout_data).encode(), stderr=b'', returncode=0)
        self.mock_create_subprocess_exec.return_value = self.mock_process

        result = await self.service.execute_trade(
            trade_type=TradeType.BUY,
            input_token_address=self.default_input_token_address_buy,
            output_token_address=self.default_output_token_address_buy,
            amount_input_token=self.default_amount_input_token_buy,
            wallet_private_key_b58=self.default_wallet_private_key_b58,
            wallet_address=self.default_wallet_address,
            strategy_snapshot=self.default_strategy_snapshot_dict,
            signal_id=self.default_signal_id,
            trade_record_id=self.default_trade_record_id
        )

        self.assertEqual(result.status, TradeStatus.SUCCESS)
        self.assertEqual(result.tx_hash, "mock_tx_hash_buy_success")
        self.assertEqual(result.executed_at, datetime(2023, 1, 1, 12, 0, 0, tzinfo=timezone.utc))
        self.assertIsNone(result.error_message)
        self.assertEqual(result.actual_amount_in, Decimal("10000000")) 
        self.assertEqual(result.actual_amount_out, Decimal("20000000000"))
        self.assertIn("quoteResponse", result.provider_response_raw)
        self.assertEqual(result.provider_response_raw["quoteResponse"]["data"]["quote"]["inAmount"], "10000000")
        self.assertIn("submitResponse", result.provider_response_raw)
        self.assertIn("statusResponse", result.provider_response_raw)

        self.mock_create_subprocess_exec.assert_called_once()
        # Further assertions on the command arguments can be added in a dedicated test like test_correct_command_construction
        # For BUY, _execute_single_trade_attempt should be called once.
        # We can verify this by patching _execute_single_trade_attempt
        # For this specific test, let's assume direct call to Node.js as it was originally.
        # The retry logic tests will specifically test _execute_single_trade_attempt calls.

    async def test_execute_trade_sell_node_script_success(self):
        """Test successful SELL trade via Node.js script."""
        input_spl = "SPLSELLMINTADDRESS"
        output_sol = SOL_MINT_ADDRESS
        amount_spl = 10.0

        mock_stdout_data = {
            "status": "success",
            "txHash": "mock_tx_hash_sell_success",
            "message": "Sell trade completed successfully.",
            "quoteResponse": {"data": {"quote": {"inAmount": "500000000", "outAmount": "15000000"}}},
            "submitResponse": {"txId": "mock_tx_hash_sell_success"},
            "statusResponse": {"status": "confirmed"}
        }
        self.mock_process = create_mock_process(stdout=json.dumps(mock_stdout_data).encode(), stderr=b'', returncode=0)
        self.mock_create_subprocess_exec.return_value = self.mock_process

        strategy_snapshot_for_sell_test = {
            **self.default_strategy_snapshot_dict,
            'input_token_decimals': 8 # Assuming SPLSELLMINTADDRESS has 8 decimals for this test
        }

        result = await self.service.execute_trade(
            trade_type=TradeType.SELL,
            input_token_address=input_spl,
            output_token_address=output_sol,
            amount_input_token=amount_spl,
            wallet_private_key_b58=self.default_wallet_private_key_b58,
            wallet_address=self.default_wallet_address,
            strategy_snapshot=strategy_snapshot_for_sell_test, # Use updated snapshot
            signal_id=self.default_signal_id,
            trade_record_id=self.default_trade_record_id
        )

        self.assertEqual(result.status, TradeStatus.SUCCESS)
        self.assertEqual(result.tx_hash, "mock_tx_hash_sell_success")
        # Assuming input SPL has 8 decimals, output SOL has 9 decimals from quote
        self.assertEqual(result.actual_amount_in, Decimal("500000000"))
        self.assertEqual(result.actual_amount_out, Decimal("15000000"))
        self.assertIn("quoteResponse", result.provider_response_raw)
        self.assertEqual(result.provider_response_raw["quoteResponse"]["data"]["quote"]["inAmount"], "500000000")
        self.assertIn("submitResponse", result.provider_response_raw)
        self.assertIn("statusResponse", result.provider_response_raw)

    async def test_execute_trade_node_script_submit_no_poll(self):
        """Test Node.js script reporting 'submitted_no_poll'."""
        node_stdout_data = {
            "status": "submitted_no_poll", 
            "txHash": "mock_tx_hash_pending_no_poll",
            "message": "Transaction submitted, polling skipped by Node.js."
        }
        mock_process = create_mock_process(stdout=json.dumps(node_stdout_data).encode(), stderr=b'', returncode=0)
        self.mock_create_subprocess_exec.return_value = mock_process

        result = await self.service.execute_trade(
            trade_type=TradeType.BUY,
            input_token_address=self.default_input_token_address_buy,
            output_token_address=self.default_output_token_address_buy,
            amount_input_token=self.default_amount_input_token_buy,
            wallet_private_key_b58=self.default_wallet_private_key_b58,
            wallet_address=self.default_wallet_address,
            strategy_snapshot=self.default_strategy_snapshot_dict,
            signal_id=self.default_signal_id,
            trade_record_id=self.default_trade_record_id
        )

        self.assertEqual(result.status, TradeStatus.PENDING) # Or FAILED, per business logic. Current code does PENDING.
        self.assertEqual(result.tx_hash, "mock_tx_hash_pending_no_poll")
        self.assertEqual(result.error_message, "Transaction submitted, polling skipped by Node.js.")

    @patch('utils.trading.solana.gmgn_trade_service.logger.warning')
    async def test_execute_trade_fallback_if_onchain_data_failed(self, mock_logger_warning):
        """Test fallback to quote data if Node.js script reports onchain data fetch failure."""
        mock_stdout_data = {
            "status": "success", 
            "txHash": "mock_tx_hash_onchain_fail",
            "quoteResponse": {"data": {"quote": {"inAmount": "1000000", "outAmount": "200000000"}}},
            "onchain_data_fetch_status": "failed_to_fetch",
            "onchain_error_message": "RPC timeout contacting Solana node."
        }
        self.mock_process = create_mock_process(stdout=json.dumps(mock_stdout_data).encode(), stderr=b'', returncode=0)
        self.mock_create_subprocess_exec.return_value = self.mock_process

        current_strategy_snapshot = {
            **self.default_strategy_snapshot_dict,
            'input_token_decimals': 9 # Explicitly set for BUY SOL test
        }

        result = await self.service.execute_trade(
            trade_type=TradeType.BUY,
            input_token_address=self.default_input_token_address_buy, # SOL_MINT_ADDRESS
            output_token_address=self.default_output_token_address_buy,
            amount_input_token=self.default_amount_input_token_buy,
            wallet_private_key_b58=self.default_wallet_private_key_b58,
            wallet_address=self.default_wallet_address,
            strategy_snapshot=current_strategy_snapshot, 
            signal_id=self.default_signal_id,
            trade_record_id=self.default_trade_record_id
        )
        
        self.assertEqual(result.status, TradeStatus.SUCCESS) 
        self.assertEqual(result.actual_amount_in, Decimal("1000000")) 
        self.assertEqual(result.actual_amount_out, Decimal("200000000")) 
        mock_logger_warning.assert_called_once_with(
            f"[TradeRec:{self.default_trade_record_id}] [Attempt:1] Failed to get onchain trade details or data incomplete (status: failed_to_fetch), falling back to quote amounts."
        )
        self.assertIn("onchain_data_fetch_status", result.provider_response_raw)
        self.assertEqual(result.provider_response_raw["onchain_data_fetch_status"], "failed_to_fetch")

    @patch('utils.trading.solana.gmgn_trade_service.logger.warning')
    async def test_execute_trade_handles_missing_onchain_fields(self, mock_logger_warning):
        """Test fallback to quote data if Node.js script output (e.g. older version) misses onchain fields."""
        mock_stdout_data = {
            "status": "success", 
            "txHash": "mock_tx_hash_missing_fields",
            "quoteResponse": {"data": {"quote": {"inAmount": "1000001", "outAmount": "200000001"}}},
            "onchain_data_fetch_status": "success", 
            "onchain_actual_amount_in_lamports": None, 
            "onchain_actual_amount_out_lamports": "300000000" 
        }
        self.mock_process = create_mock_process(stdout=json.dumps(mock_stdout_data).encode(), stderr=b'', returncode=0)
        self.mock_create_subprocess_exec.return_value = self.mock_process

        current_strategy_snapshot_missing_fields = {
            **self.default_strategy_snapshot_dict,
            'input_token_decimals': 9 # Explicitly set for BUY SOL test
        }

        result = await self.service.execute_trade(
            trade_type=TradeType.BUY,
            input_token_address=self.default_input_token_address_buy, # SOL_MINT_ADDRESS
            output_token_address=self.default_output_token_address_buy,
            amount_input_token=self.default_amount_input_token_buy,
            wallet_private_key_b58=self.default_wallet_private_key_b58,
            wallet_address=self.default_wallet_address,
            strategy_snapshot=current_strategy_snapshot_missing_fields, 
            signal_id=self.default_signal_id,
            trade_record_id=self.default_trade_record_id
        )

        self.assertEqual(result.status, TradeStatus.SUCCESS)
        self.assertEqual(result.actual_amount_in, Decimal("1000001")) 
        self.assertEqual(result.actual_amount_out, Decimal("200000001")) 
        
        # Adjusted expected log calls to include [Attempt:1]
        expected_log_call_1 = call(f"[TradeRec:{self.default_trade_record_id}] [Attempt:1] Onchain data fetch reported success by script, but actual onchain amounts are missing/null. Will attempt fallback.")
        expected_log_call_2 = call(f"[TradeRec:{self.default_trade_record_id}] [Attempt:1] Failed to get onchain trade details or data incomplete (status: partial_data), falling back to quote amounts.")
        
        calls = mock_logger_warning.call_args_list
        self.assertIn(expected_log_call_1, calls, "Log for missing onchain amounts not found or mismatch")
        self.assertIn(expected_log_call_2, calls, "Log for fallback to quote not found or mismatch")
        self.assertEqual(mock_logger_warning.call_count, 2, f"Expected two warning calls, got {mock_logger_warning.call_count}. Calls: {calls}")

    @patch(f'{BASE_PATH_FOR_SERVICE_UNDER_TEST}.logger') 
    async def test_execute_trade_node_script_reports_error_with_message(self, mock_logger_actual_module): 
        """Test Node.js script reporting an error with a message and details."""
        mock_stdout_data = {
            "status": "error",
            "message": "GMGN API timed out during quote.",
            "txHash": "may_exist_or_not_on_error", 
            "details": {"api_endpoint": "/get_swap_route", "error_code": "TIMEOUT"} 
        }
        self.mock_process = create_mock_process(stdout=json.dumps(mock_stdout_data).encode(), stderr=b'stderr output from node', returncode=0)
        self.mock_create_subprocess_exec.return_value = self.mock_process

        result = await self.service.execute_trade(
            trade_type=TradeType.BUY,
            input_token_address=self.default_input_token_address_buy,
            output_token_address=self.default_output_token_address_buy,
            amount_input_token=self.default_amount_input_token_buy,
            wallet_private_key_b58=self.default_wallet_private_key_b58,
            wallet_address=self.default_wallet_address,
            strategy_snapshot=self.default_strategy_snapshot_dict,
            signal_id=self.default_signal_id,
            trade_record_id=self.default_trade_record_id
        )

        self.assertEqual(result.status, TradeStatus.FAILED)
        self.assertEqual(result.error_message, "GMGN API timed out during quote.")
        self.assertEqual(result.tx_hash, "may_exist_or_not_on_error")
        self.assertIn("details", result.provider_response_raw) 
        self.assertEqual(result.provider_response_raw["details"]["error_code"], "TIMEOUT")
        # Verify the specific info log call
        mock_logger_actual_module.info.assert_any_call(f"[TradeRec:{self.default_trade_record_id}] [Attempt:1] Node.js script stderr:\nstderr output from node")

    async def test_execute_trade_node_script_execution_fails_return_code(self):
        """Test Node.js script execution failing with a non-zero return code."""
        # stdout might be empty or contain partial data if script crashes
        mock_process = create_mock_process(stdout=b'Something went wrong before JSON output.', stderr=b'Node.js Critical Error: Unhandled Promise Rejection!', returncode=1)
        self.mock_create_subprocess_exec.return_value = mock_process

        result = await self.service.execute_trade(
            trade_type=TradeType.BUY,
            input_token_address=self.default_input_token_address_buy,
            output_token_address=self.default_output_token_address_buy,
            amount_input_token=self.default_amount_input_token_buy,
            wallet_private_key_b58=self.default_wallet_private_key_b58,
            wallet_address=self.default_wallet_address,
            strategy_snapshot=self.default_strategy_snapshot_dict,
            signal_id=self.default_signal_id,
            trade_record_id=self.default_trade_record_id
        )
        self.assertEqual(result.status, TradeStatus.FAILED)
        self.assertIn("Node.js script execution failed with return code 1", result.error_message)
        if result.provider_response_raw and "message" in result.provider_response_raw:
            self.assertIn("Stderr might contain more info", result.provider_response_raw.get("message", ""))
        elif result.provider_response_raw and "raw_stdout" in result.provider_response_raw: # Fallback check if stdout was captured instead
            self.assertIn("Something went wrong before JSON output.", result.provider_response_raw.get("raw_stdout", ""))

    async def test_execute_trade_node_script_invalid_json_output(self):
        """Test Node.js script producing non-JSON output but exiting cleanly."""
        mock_process = create_mock_process(stdout=b'This is definitely not JSON output.', stderr=b'', returncode=0)
        self.mock_create_subprocess_exec.return_value = mock_process

        result = await self.service.execute_trade(
            trade_type=TradeType.BUY,
            input_token_address=self.default_input_token_address_buy,
            output_token_address=self.default_output_token_address_buy,
            amount_input_token=self.default_amount_input_token_buy,
            wallet_private_key_b58=self.default_wallet_private_key_b58,
            wallet_address=self.default_wallet_address,
            strategy_snapshot=self.default_strategy_snapshot_dict,
            signal_id=self.default_signal_id,
            trade_record_id=self.default_trade_record_id
        )
        self.assertEqual(result.status, TradeStatus.FAILED)
        # Adjusted error message to match current implementation more closely
        self.assertIn("Failed to parse JSON output from Node.js script", result.error_message)
        self.assertIn("Expecting value: line 1 column 1 (char 0)", result.error_message)
        self.assertIn("This is definitely not JSON output.", result.provider_response_raw.get("raw_stdout", ""))

    async def test_execute_trade_node_script_file_not_found(self):
        """Test scenario where the Node.js script file is not found."""
        # The NODE_SCRIPT_PATH in the module is patched to "mock_node_script.mjs" by setUp
        mocked_path_value = "mock_node_script.mjs"
        self.mock_create_subprocess_exec.side_effect = FileNotFoundError(f"No such file or directory: '{mocked_path_value}'")

        result = await self.service.execute_trade(
            trade_type=TradeType.BUY,
            input_token_address=self.default_input_token_address_buy,
            output_token_address=self.default_output_token_address_buy,
            amount_input_token=self.default_amount_input_token_buy,
            wallet_private_key_b58=self.default_wallet_private_key_b58,
            wallet_address=self.default_wallet_address,
            strategy_snapshot=self.default_strategy_snapshot_dict,
            signal_id=self.default_signal_id,
            trade_record_id=self.default_trade_record_id
        )
        self.assertEqual(result.status, TradeStatus.FAILED)
        # Corrected error message to align with how it's constructed in the except block
        expected_msg = f"Node.js script not found at {mocked_path_value}."
        self.assertEqual(result.error_message, expected_msg)

    async def test_execute_trade_unexpected_exception_during_subprocess(self):
        """Test an unexpected error during subprocess execution itself."""
        self.mock_create_subprocess_exec.side_effect = RuntimeError("Unexpected asyncio error")

        result = await self.service.execute_trade(
            trade_type=TradeType.BUY,
            input_token_address=self.default_input_token_address_buy,
            output_token_address=self.default_output_token_address_buy,
            amount_input_token=self.default_amount_input_token_buy,
            wallet_private_key_b58=self.default_wallet_private_key_b58,
            wallet_address=self.default_wallet_address,
            strategy_snapshot=self.default_strategy_snapshot_dict,
            signal_id=self.default_signal_id,
            trade_record_id=self.default_trade_record_id
        )
        self.assertEqual(result.status, TradeStatus.FAILED)
        # Adjusted assertion to match the actual error message format from _execute_single_trade_attempt
        # The error_message does not include [Attempt:X] from this specific exception handling
        expected_msg = f"Unexpected error during Node.js script execution/processing: Unexpected asyncio error"
        self.assertEqual(result.error_message, expected_msg)

    def test_correct_command_construction(self):
        """Test that the command to execute Node.js script is constructed correctly."""
        patcher_was_active = False
        if hasattr(self, 'patcher_node_script_path'):
            try:
                self.patcher_node_script_path.stop()
                patcher_was_active = True
            except RuntimeError: 
                patcher_was_active = False
        
        current_mock_node_path_value = None
        if hasattr(self, 'patcher_node_script_path'):
           # Get the value it was patched to, if needed for re-patching later.
           # However, the goal here is to test the *original* unpatched value.
           pass # No need to get self.mock_node_script_path which is the mock object itself

        try:
            import importlib
            # Ensure utils.gmgn_trade_service is reloaded to get its original NODE_SCRIPT_PATH
            import utils.trading.solana.gmgn_trade_service
            importlib.reload(utils.trading.solana.gmgn_trade_service)
            
            NODE_SCRIPT_PATH_original = utils.trading.solana.gmgn_trade_service.NODE_SCRIPT_PATH
            
            self.assertTrue(
                os.path.isabs(NODE_SCRIPT_PATH_original),
                f"NODE_SCRIPT_PATH '{NODE_SCRIPT_PATH_original}' 不是一个绝对路径。"
            )
            
            expected_filename = "gmgn_test.mjs"
            actual_filename = os.path.basename(NODE_SCRIPT_PATH_original)
            self.assertEqual(
                actual_filename,
                expected_filename,
                f"NODE_SCRIPT_PATH '{NODE_SCRIPT_PATH_original}' 的文件名应为 '{expected_filename}'，但实际为 '{actual_filename}'。"
            )
        finally:
            # Restart the patch if it was active before this test and was stopped by this test
            if patcher_was_active and hasattr(self, 'patcher_node_script_path'):
                self.patcher_node_script_path.start() # Restart the original patcher from setUp
            # Ensure the module is re-patched to the mock value if other tests expect it.
            # This is tricky because reload clears module-level patches. 
            # The setUp patcher should re-apply when it was started there.
            # For safety, explicitly re-start the setUp patcher if it was not the one manipulated here,
            # or rely on tearDown to stop it if it was started in setUp.
            # The current logic seems to correctly restart the original patcher if it was stopped.

    async def test_execute_trade_handles_missing_actual_amount_data(self):
        """Test parsing of actual_amount_in/out when quoteResponse or its parts are missing."""
        node_stdout_data_no_quote = {"status": "success", "txHash": "mock_tx_hash_no_quote"}
        node_stdout_data_empty_quote = {"status": "success", "txHash": "mock_tx_hash_empty_quote", "quoteResponse": {}}
        node_stdout_data_quote_no_data = {"status": "success", "txHash": "mock_tx_hash_q_no_data", "quoteResponse": {"data": {}}}
        node_stdout_data_quote_data_no_quote_obj = {"status": "success", "txHash": "mock_tx_hash_qd_no_qobj", "quoteResponse": {"data": {"quote": {}}}}
        
        test_cases = [
            node_stdout_data_no_quote,
            node_stdout_data_empty_quote,
            node_stdout_data_quote_no_data,
            node_stdout_data_quote_data_no_quote_obj
        ]

        for i, stdout_data in enumerate(test_cases):
            with self.subTest(scenario=f"Missing quote data case {i}"):
                mock_process = create_mock_process(stdout=json.dumps(stdout_data).encode(), stderr=b'', returncode=0)
                self.mock_create_subprocess_exec.return_value = mock_process
                # Reset call count for each subtest if needed, or use a fresh mock.
                # Here, since we're re-assigning return_value, it's okay.

                result = await self.service.execute_trade(
                    trade_type=TradeType.BUY,
                    input_token_address=self.default_input_token_address_buy,
                    output_token_address=self.default_output_token_address_buy,
                    amount_input_token=self.default_amount_input_token_buy,
                    wallet_private_key_b58=self.default_wallet_private_key_b58,
                    wallet_address=self.default_wallet_address,
                    strategy_snapshot=self.default_strategy_snapshot_dict,
                    signal_id=self.default_signal_id,
                    trade_record_id=self.default_trade_record_id
                )
                self.assertEqual(result.status, TradeStatus.SUCCESS)
                self.assertIsNone(result.actual_amount_in)
                self.assertIsNone(result.actual_amount_out)

    @patch('utils.trading.solana.gmgn_trade_service.logger.warning')
    async def test_execute_trade_handles_malformed_actual_amount_strings(self, mock_logger_warning):
        """Test parsing of actual_amount_in/out when amount strings are malformed."""
        node_stdout_data = {
            "status": "success", 
            "txHash": "mock_tx_hash_malformed_quote",
            "quoteResponse": {"data": {"quote": {"inAmount": "not_a_number", "outAmount": "1.0.0"}}} # Malformed
        }
        mock_process = create_mock_process(stdout=json.dumps(node_stdout_data).encode(), stderr=b'', returncode=0)
        self.mock_create_subprocess_exec.return_value = mock_process
        
        # Explicitly add input_token_decimals for BUY SOL
        current_strategy_snapshot_malformed = {
            **self.default_strategy_snapshot_dict,
            'input_token_decimals': 9 # Explicitly set for BUY SOL test
        }

        result = await self.service.execute_trade(
            trade_type=TradeType.BUY,
            input_token_address=self.default_input_token_address_buy, # SOL_MINT_ADDRESS
            output_token_address=self.default_output_token_address_buy,
            amount_input_token=self.default_amount_input_token_buy,
            wallet_private_key_b58=self.default_wallet_private_key_b58,
            wallet_address=self.default_wallet_address,
            strategy_snapshot=current_strategy_snapshot_malformed, # Use modified
            signal_id=self.default_signal_id,
            trade_record_id=self.default_trade_record_id
        )
        
        self.assertEqual(result.status, TradeStatus.SUCCESS)
        self.assertIsNone(result.actual_amount_in) # Failed parsing "not_a_number"
        self.assertIsNone(result.actual_amount_out) # Failed parsing "1.0.0"
        
        calls = mock_logger_warning.call_args_list
        self.assertEqual(mock_logger_warning.call_count, 2, f"Expected two warning calls, got {mock_logger_warning.call_count}. Calls: {calls}")

        # Check for the first log (parsing error)
        first_call_args = calls[0][0]
        first_log_message = str(first_call_args[0])
        
        self.assertIn(f"[TradeRec:{self.default_trade_record_id}] [Attempt:1] Could not parse actual amounts from quote data", first_log_message, "Core message for parsing error not found")
        self.assertTrue("InvalidOperation" in first_log_message or "ConversionSyntax" in first_log_message, "Exception type string for parsing error not found in log")

        # Check for the second log (cannot determine amounts)
        expected_log_call_cannot_determine = call(f"[TradeRec:{self.default_trade_record_id}] [Attempt:1] Could not determine actual amounts from onchain (status: None) or quote. Amounts will be None.")
        self.assertEqual(calls[1], expected_log_call_cannot_determine, "Log for cannot determine amounts not found or not in correct order")

    @patch(f'{BASE_PATH_FOR_SERVICE_UNDER_TEST}.logger') 
    async def test_provider_response_raw_population(self, mock_logger_actual_module): 
        """Test that provider_response_raw is populated correctly."""
        mock_node_output = {
            "status": "success",
            "txHash": "some_hash",
            "quoteResponse": {"q_data": "q_val"},
            "submitResponse": {"s_data": "s_val"},
            "statusResponse": {"st_data": "st_val"},
            "onchain_data_fetch_status": "success",
            "onchain_actual_amount_in_lamports": "100",
            "onchain_actual_amount_out_lamports": "200",
            "misc_other_field": "misc_val"
        }
        self.mock_process = create_mock_process(stdout=json.dumps(mock_node_output).encode(), stderr=b'', returncode=0)
        self.mock_create_subprocess_exec.return_value = self.mock_process
        
        result = await self.service.execute_trade(
            trade_type=TradeType.BUY,
            input_token_address=self.default_input_token_address_buy,
            output_token_address=self.default_output_token_address_buy,
            amount_input_token=self.default_amount_input_token_buy,
            wallet_private_key_b58=self.default_wallet_private_key_b58,
            wallet_address=self.default_wallet_address,
            strategy_snapshot=self.default_strategy_snapshot_dict,
            signal_id=self.default_signal_id,
            trade_record_id=self.default_trade_record_id
        )
        
        self.assertEqual(result.status, TradeStatus.SUCCESS)
        self.assertIsNotNone(result.provider_response_raw)
        self.assertEqual(result.provider_response_raw.get("status"), "success")
        self.assertEqual(result.provider_response_raw.get("txHash"), "some_hash")
        self.assertEqual(result.provider_response_raw.get("quoteResponse"), {"q_data": "q_val"})
        self.assertEqual(result.provider_response_raw.get("submitResponse"), {"s_data": "s_val"})
        self.assertEqual(result.provider_response_raw.get("statusResponse"), {"st_data": "st_val"})
        self.assertEqual(result.provider_response_raw.get("misc_other_field"), "misc_val")
        self.assertEqual(result.actual_amount_in, Decimal("100"))
        self.assertEqual(result.actual_amount_out, Decimal("200"))

    async def test_close_method(self):
        """Test the close method (currently a no-op)."""
        await self.service.close() # Should not raise any error
        # No specific assertion other than successful execution if it's a no-op

    def test_node_script_path_is_absolute_and_correct_filename(self):
        """Test that NODE_SCRIPT_PATH is an absolute path and has the correct filename."""
        patcher_was_active = False
        if hasattr(self, 'patcher_node_script_path'):
            try:
                self.patcher_node_script_path.stop()
                patcher_was_active = True
            except RuntimeError:
                patcher_was_active = False
        
        try:
            import importlib
            import utils.trading.solana.gmgn_trade_service
            importlib.reload(utils.trading.solana.gmgn_trade_service)
            
            actual_path = utils.trading.solana.gmgn_trade_service.NODE_SCRIPT_PATH
            
            self.assertTrue(
                os.path.isabs(actual_path),
                f"NODE_SCRIPT_PATH '{actual_path}' in the module is not an absolute path."
            )
            expected_filename = "gmgn_test.mjs"
            actual_filename = os.path.basename(actual_path)
            self.assertEqual(
                actual_filename,
                expected_filename,
                f"NODE_SCRIPT_PATH '{actual_path}' in the module should have filename '{expected_filename}', but got '{actual_filename}'."
            )
        finally:
            if patcher_was_active and hasattr(self, 'patcher_node_script_path'):
                self.patcher_node_script_path.start() # Restart the original patcher from setUp

    # Test for _calculate_next_priority_fee
    def test_calculate_next_priority_fee(self):
        """Test the _calculate_next_priority_fee helper function."""
        test_cases = [
            # Input string, Expected output string (Decimal will compare values)
            ("0.00005", "0.00006"),
            ("0.00009", "0.00010"), # Expect re-quantization to keep precision
            ("0.00500", "0.00501"),
            ("0.005",   "0.006"),
            ("0.5",     "0.6"),
            ("0.9",     "1.0"),   # Expect re-quantization
            ("1.0",     "1.1"),
            ("1",       "2"),
            ("0.0",     "0.1"),   # Original "0.0" treated as 1 decimal place
            ("0.00000", "0.00001"),
            # Test with Decimal input direct (string conversion inside function will handle)
            (Decimal("0.000000005"), Decimal("0.000000006")), 
            (Decimal("0.000000009"), Decimal("0.000000010")), # Expect re-quantization
            (Decimal("1.9"), Decimal("2.0")), # Expect re-quantization
            (Decimal("9.99"), Decimal("10.00")) # Expect re-quantization
        ]
        for current_fee_input, expected_fee_str in test_cases:
            current_decimal = Decimal(current_fee_input) if isinstance(current_fee_input, str) else current_fee_input
            expected_decimal = Decimal(expected_fee_str)
            with self.subTest(current_fee=str(current_decimal), expected_fee=str(expected_decimal)):
                calculated_next_fee = _calculate_next_priority_fee(current_decimal)
                self.assertEqual(calculated_next_fee, expected_decimal)
                # Also check string representation if re-quantization was expected for trailing zeros
                if str(expected_decimal).endswith('0') and '.' in str(expected_decimal) and not str(calculated_next_fee).endswith('0') and calculated_next_fee == expected_decimal:
                    # This specific check is for cases like 0.00009 -> 0.00010 where Decimal value is same as 0.0001
                    # but the string representation from _calculate_next_priority_fee should match the quantize.
                    self.assertEqual(str(calculated_next_fee), str(expected_decimal), f"String representation mismatch for {current_decimal} -> {expected_decimal}")

    @patch('utils.trading.solana.gmgn_trade_service.asyncio.sleep', new_callable=AsyncMock)
    async def test_sell_trade_retries_and_succeeds_on_third_attempt(self, mock_asyncio_sleep: AsyncMock):
        """Test SELL trade fails twice, then succeeds on the third attempt (start of group 2)."""
        mock_asyncio_sleep.side_effect = mock_sleep_impl # Standardize to side_effect
        
        fail_result_1 = TradeResult(status=TradeStatus.FAILED, error_message="Fail 1")
        fail_result_2 = TradeResult(status=TradeStatus.FAILED, error_message="Fail 2")
        success_result_3 = TradeResult(status=TradeStatus.SUCCESS, tx_hash="success_tx_3")

        strategy_snapshot_with_decimals = {
            **self.default_strategy_snapshot_dict,
            'input_token_decimals': 6 
        }

        with patch.object(self.service, '_execute_single_trade_attempt', new_callable=AsyncMock) as mock_single_attempt:
            # 设置side_effect连续返回3个不同结果
            mock_single_attempt.side_effect = [fail_result_1, fail_result_2, success_result_3]

            result = await self.service.execute_trade(
                trade_type=TradeType.SELL,
                input_token_address="SPL1", 
                output_token_address=SOL_MINT_ADDRESS, 
                amount_input_token=10.0,
                wallet_private_key_b58=self.default_wallet_private_key_b58, 
                wallet_address=self.default_wallet_address,
                strategy_snapshot=strategy_snapshot_with_decimals, 
                signal_id=self.default_signal_id, 
                trade_record_id=self.default_trade_record_id
            )

            self.assertEqual(result.status, TradeStatus.SUCCESS)
            self.assertEqual(result.tx_hash, "success_tx_3")
            self.assertEqual(mock_single_attempt.call_count, 3)
            
            # 检查asyncio.sleep是否被正确调用了两次（失败后的两次重试前）
            self.assertEqual(mock_asyncio_sleep.call_count, 2)
            mock_asyncio_sleep.assert_has_calls([call(5), call(5)])

    @patch('utils.trading.solana.gmgn_trade_service.asyncio.sleep', new_callable=AsyncMock)
    async def test_sell_trade_all_10_retries_fail(self, mock_asyncio_sleep_decorator: AsyncMock):
        """Test SELL trade fails all 10 attempts."""
        mock_asyncio_sleep_decorator.side_effect = mock_sleep_impl # Configure the decorator's mock

        strategy_snapshot_with_decimals = {
            **self.default_strategy_snapshot_dict,
            'input_token_decimals': 6 
        }
        
        # Only patch _execute_single_trade_attempt here, use decorator's mock for sleep
        with patch.object(self.service, '_execute_single_trade_attempt', new_callable=AsyncMock) as mock_single_attempt:
            mock_single_attempt.return_value = TradeResult(status=TradeStatus.FAILED, error_message="Persistent Fail")

            result = await self.service.execute_trade(
                trade_type=TradeType.SELL,
                input_token_address="SPL1", 
                output_token_address=SOL_MINT_ADDRESS, 
                amount_input_token=10.0,
                wallet_private_key_b58=self.default_wallet_private_key_b58, 
                wallet_address=self.default_wallet_address,
                strategy_snapshot=strategy_snapshot_with_decimals, 
                signal_id=self.default_signal_id, 
                trade_record_id=self.default_trade_record_id
            )

            self.assertEqual(result.status, TradeStatus.FAILED)
            self.assertEqual(result.error_message, "Persistent Fail") 
            self.assertEqual(mock_single_attempt.call_count, 10)
            
            # Assert on the decorator's mock
            self.assertEqual(mock_asyncio_sleep_decorator.call_count, 9)
            mock_asyncio_sleep_decorator.assert_has_calls([call(5)] * 9)

    @patch('utils.trading.solana.gmgn_trade_service.asyncio.sleep', new_callable=AsyncMock)
    async def test_sell_trade_no_retry_on_success_first_attempt(self, mock_asyncio_sleep: AsyncMock): # Removed mock_single_attempt
        """Test SELL trade succeeds on the first attempt without retries."""

        strategy_snapshot_with_decimals = {
            **self.default_strategy_snapshot_dict,
            'input_token_decimals': 6 
        }
        with patch.object(self.service, '_execute_single_trade_attempt', new_callable=AsyncMock) as mock_single_attempt:
            mock_single_attempt.return_value = TradeResult(status=TradeStatus.SUCCESS, tx_hash="success_tx_1")

            result = await self.service.execute_trade(
                trade_type=TradeType.SELL,
                input_token_address="SPL1", 
                output_token_address=SOL_MINT_ADDRESS, 
                amount_input_token=10.0,
                wallet_private_key_b58=self.default_wallet_private_key_b58, 
                wallet_address=self.default_wallet_address,
                strategy_snapshot=strategy_snapshot_with_decimals, 
                signal_id=self.default_signal_id, 
                trade_record_id=self.default_trade_record_id
            )
            self.assertEqual(result.status, TradeStatus.SUCCESS)
            self.assertEqual(result.tx_hash, "success_tx_1")
            mock_single_attempt.assert_called_once()
            
            call_args = mock_single_attempt.call_args[1]
            self.assertEqual(call_args['current_slippage'], strategy_snapshot_with_decimals['gmgn_sell_slippage_percentage'])
            self.assertEqual(call_args['current_priority_fee'], Decimal(str(strategy_snapshot_with_decimals['gmgn_sell_priority_fee'])))
            self.assertEqual(call_args['attempt_number'], 1)

    @patch(f'{BASE_PATH_FOR_SERVICE_UNDER_TEST}.GmgnTradeService._execute_single_trade_attempt', new_callable=AsyncMock)
    async def test_buy_trade_does_not_retry_on_failure(self, mock_single_attempt: AsyncMock):
        """Test BUY trade fails and does NOT use the retry logic."""
        mock_single_attempt.return_value = TradeResult(status=TradeStatus.FAILED, error_message="Buy Fail")

        strategy_for_buy = {
            **self.default_strategy_snapshot_dict,
            'gmgn_buy_slippage_percentage': 0.8, # Specific buy slippage
            'gmgn_buy_priority_fee': 0.0003,    # Specific buy fee
        }

        result = await self.service.execute_trade(
            trade_type=TradeType.BUY,
            input_token_address=SOL_MINT_ADDRESS, output_token_address="SPL_OUT", amount_input_token=0.1,
            wallet_private_key_b58=self.default_wallet_private_key_b58, wallet_address=self.default_wallet_address,
            strategy_snapshot=strategy_for_buy,
            signal_id=self.default_signal_id, trade_record_id=self.default_trade_record_id
        )
        self.assertEqual(result.status, TradeStatus.FAILED)
        self.assertEqual(result.error_message, "Buy Fail")
        mock_single_attempt.assert_called_once()
        
        # Verify that the direct slippage and fee for BUY were used
        call_args = mock_single_attempt.call_args[1]
        self.assertEqual(call_args['current_slippage'], strategy_for_buy['gmgn_buy_slippage_percentage'])
        self.assertEqual(call_args['current_priority_fee'], Decimal(str(strategy_for_buy['gmgn_buy_priority_fee'])))
        self.assertEqual(call_args['attempt_number'], 1)

    async def test_execute_trade_parses_onchain_data_success(self):
        """Test Node.js script successfully returns onchain actual trade data."""
        node_stdout_data = {
            "status": "success", 
            "txHash": "mock_tx_hash_onchain_success",
            "onchain_data_fetch_status": "success",
            "onchain_actual_amount_in_lamports": "1050000", # e.g. 0.00105 SOL (9 decimals)
            "onchain_input_decimals": 9,
            "onchain_actual_amount_out_lamports": "**********", # e.g. Sketchify (6 decimals)
            "onchain_output_decimals": 6,
            "quoteResponse": {"data": {"quote": {"inAmount": "1000000", "outAmount": "2483677903"}}} # Quote for comparison
        }
        mock_process = create_mock_process(stdout=json.dumps(node_stdout_data).encode(), stderr=b'', returncode=0)
        self.mock_create_subprocess_exec.return_value = mock_process

        result = await self.service.execute_trade(
            trade_type=TradeType.BUY,
            input_token_address=self.default_input_token_address_buy,
            output_token_address=self.default_output_token_address_buy,
            amount_input_token=self.default_amount_input_token_buy,
            wallet_private_key_b58=self.default_wallet_private_key_b58,
            wallet_address=self.default_wallet_address,
            strategy_snapshot=self.default_strategy_snapshot_dict,
            signal_id=self.default_signal_id,
            trade_record_id=self.default_trade_record_id
        )

        self.assertEqual(result.status, TradeStatus.SUCCESS)
        self.assertEqual(result.tx_hash, "mock_tx_hash_onchain_success")
        self.assertEqual(result.actual_amount_in, Decimal("1050000")) # Prioritizes onchain data
        self.assertEqual(result.actual_amount_out, Decimal("**********")) # Prioritizes onchain data
        self.assertIn("onchain_data_fetch_status", result.provider_response_raw)
        self.assertEqual(result.provider_response_raw["onchain_data_fetch_status"], "success")

    async def test_execute_trade_node_script_fails_execution(self):
        # ... existing code ...
        pass 

    @patch(f'{BASE_PATH_FOR_SERVICE_UNDER_TEST}.logger')
    @patch('utils.trading.solana.gmgn_trade_service.asyncio.sleep', new_callable=AsyncMock) # Added sleep mock
    async def test_execute_trade_lamport_conversion_error(self, mock_sleep_for_lamport_test: AsyncMock, mock_logger: MagicMock):
        """Test trade failure if lamport conversion itself fails."""
        mock_sleep_for_lamport_test.side_effect = mock_sleep_impl # Configure the sleep mock
        
        amount_to_test_conversion_failure = 10.0 # This is amount_input_token for the sell trade
        # Store the original Decimal class before patching
        original_Decimal = Decimal

        def selective_decimal_side_effect(value_to_convert, *args, **kwargs):
            # We want to simulate error when Decimal(str(amount_input_token)) is called
            # inside _execute_single_trade_attempt for the lamport conversion.
            if str(value_to_convert) == str(amount_to_test_conversion_failure):
                raise Exception("Simulated Decimal Error during lamport conversion for amount_input_token")
            # For other calls (like priority fee conversion), use the original Decimal constructor
            return original_Decimal(value_to_convert, *args, **kwargs)

        with patch(f'{BASE_PATH_FOR_SERVICE_UNDER_TEST}.Decimal', side_effect=selective_decimal_side_effect) as mock_decimal_constructor:
            # Ensure subprocess is mocked as it should not be reached if lamport conversion fails early
            self.mock_create_subprocess_exec.return_value = create_mock_process(stdout=b'dummy_stdout', stderr=b'', returncode=0)

            # For SELL, decimals should come from snapshot
            strategy_snapshot_with_decimals = {
                **self.default_strategy_snapshot_dict, # This contains gmgn_sell_priority_fee like '0.0002'
                'input_token_decimals': 6 
            }

            result = await self.service.execute_trade(
                trade_type=TradeType.SELL,
                input_token_address="MOCK_SPL_ADDR", # SPL Token
                output_token_address=SOL_MINT_ADDRESS, # Added missing argument
                amount_input_token=amount_to_test_conversion_failure, # Use the specific float amount
                wallet_private_key_b58=self.default_wallet_private_key_b58,
                wallet_address=self.default_wallet_address,
                strategy_snapshot=strategy_snapshot_with_decimals,
                signal_id=self.default_signal_id,
                trade_record_id=self.default_trade_record_id
            )

            self.assertEqual(result.status, TradeStatus.FAILED)
            # The error message should now come from the except block in _execute_single_trade_attempt
            expected_err_msg_part = f"Lamport conversion error for MOCK_SPL_ADDR (amount: {amount_to_test_conversion_failure}, decimals: 6)"
            self.assertIn(expected_err_msg_part, result.error_message)
                
            # Check that the logger recorded the error from _execute_single_trade_attempt
            log_found = False
            # We expect 10 error logs from _execute_single_trade_attempt due to retries
            simulated_error_message_part = "Simulated Decimal Error during lamport conversion for amount_input_token"
            actual_conversion_error_message_part = f"Error converting amount '{amount_to_test_conversion_failure}' to lamports for MOCK_SPL_ADDR with 6 decimals"

            for i, call_item in enumerate(mock_logger.error.call_args_list):
                args, kwargs = call_item
                if args and isinstance(args[0], str):
                    logged_message = args[0]
                    # The log from _execute_single_trade_attempt should contain both the specific conversion error and the original simulated exception string
                    if actual_conversion_error_message_part in logged_message and simulated_error_message_part in logged_message:
                        log_found = True
                        break 
            self.assertTrue(log_found, f"Expected error log for lamport conversion (containing '{simulated_error_message_part}' and '{actual_conversion_error_message_part}') not found in {mock_logger.error.call_args_list}")
            
            # Assert that the subprocess was not called because failure happened before
            self.mock_create_subprocess_exec.assert_not_called()
            # Assert that sleep was called 9 times due to 10 retry attempts
            self.assertEqual(mock_sleep_for_lamport_test.call_count, 9)
            mock_sleep_for_lamport_test.assert_has_calls([call(5)] * 9)

    @patch('utils.trading.solana.gmgn_trade_service.asyncio.sleep', new_callable=AsyncMock)
    async def test_sell_spl_uses_correct_decimals_from_snapshot(self, mock_main_asyncio_sleep: AsyncMock):
        """
        Tests that for SELL trades of SPL tokens, the correct number of decimals 
        is taken from 'input_token_decimals' in the strategy_snapshot.
        Also tests failure modes if decimals are missing or invalid.
        """
        mock_main_asyncio_sleep.return_value = None # For the main test's scope if direct sleep occurs

        spl_token_addr = "SPLTOKENMINTFORDECIMALSTEST"
        ui_amount = Decimal("12.3456")

        # --- Scenario 1: input_token_decimals provided and valid (e.g., 6 decimals) ---
        strategy_snapshot_6_decimals = {
            **self.default_strategy_snapshot_dict,
            'gmgn_sell_slippage_percentage': 0.7, # ensure sell slippage is used
            'gmgn_sell_priority_fee': 0.00025,  # ensure sell priority fee is used
            'input_token_decimals': 6
        }
        # Mock subprocess to capture args
        mock_process_s1 = create_mock_process(stdout=b'{"status":"success", "txHash":"tx_s1_decimals_6"}', stderr=b'', returncode=0)
        self.mock_create_subprocess_exec.return_value = mock_process_s1

        await self.service.execute_trade(
            trade_type=TradeType.SELL,
            input_token_address=spl_token_addr,
            output_token_address=SOL_MINT_ADDRESS,
            amount_input_token=float(ui_amount), # Pass as float
            wallet_private_key_b58=self.default_wallet_private_key_b58,
            wallet_address=self.default_wallet_address,
            strategy_snapshot=strategy_snapshot_6_decimals,
            signal_id=PydanticObjectId(),
            trade_record_id=PydanticObjectId()
        )

        self.mock_create_subprocess_exec.assert_called_once()
        call_args_s1 = self.mock_create_subprocess_exec.call_args[0]
        expected_lamports_s1 = str(int(ui_amount * (Decimal('10') ** 6))) # 12.3456 * 10^6 = 12345600
        self.assertEqual(call_args_s1[5], expected_lamports_s1) # Check amount arg in cmd (index 5 for in_amount_lamports_str)
        self.assertEqual(call_args_s1[6], str(strategy_snapshot_6_decimals['gmgn_sell_slippage_percentage'])) # Check slippage (index 6)
        self.assertEqual(call_args_s1[7], str(strategy_snapshot_6_decimals['gmgn_sell_priority_fee'])) # Check priority fee (index 7)
        self.mock_create_subprocess_exec.reset_mock()

        # --- Scenario 1b: input_token_decimals provided and valid (e.g., 0 decimals) ---
        strategy_snapshot_0_decimals = {
            **self.default_strategy_snapshot_dict, 'input_token_decimals': 0
        }
        mock_process_s1b = create_mock_process(stdout=b'{"status":"success", "txHash":"tx_s1b_decimals_0"}', stderr=b'', returncode=0)
        self.mock_create_subprocess_exec.return_value = mock_process_s1b
        
        ui_amount_for_0_decimals = Decimal("123") # Whole number for 0 decimals
        await self.service.execute_trade(
            trade_type=TradeType.SELL,
            input_token_address=spl_token_addr,
            output_token_address=SOL_MINT_ADDRESS,
            amount_input_token=float(ui_amount_for_0_decimals), # Corrected: use ui_amount_for_0_decimals
            wallet_private_key_b58=self.default_wallet_private_key_b58,
            wallet_address=self.default_wallet_address,
            strategy_snapshot=strategy_snapshot_0_decimals,
            signal_id=PydanticObjectId(),
            trade_record_id=PydanticObjectId()
        )
        self.mock_create_subprocess_exec.assert_called_once()
        call_args_s1b = self.mock_create_subprocess_exec.call_args[0]
        expected_lamports_s1b = str(int(ui_amount_for_0_decimals * (Decimal('10') ** 0))) # 123
        self.assertEqual(call_args_s1b[5], expected_lamports_s1b) # Check amount arg in cmd (index 5)
        self.mock_create_subprocess_exec.reset_mock()

        # --- Scenario 2: input_token_decimals provided but invalid (e.g., "six", or too large) ---
        invalid_decimals_values = ["six", -1, 20, 6.5]
        for invalid_val in invalid_decimals_values:
            with self.subTest(invalid_decimal_value=invalid_val):
                strategy_snapshot_invalid_decimals = {
                    **self.default_strategy_snapshot_dict, 'input_token_decimals': invalid_val
                }
                self.mock_create_subprocess_exec.reset_mock()

                # Patch asyncio.sleep for this specific subtest's call to execute_trade
                # This test checks what happens if _execute_single_trade_attempt fails due to bad decimals.
                # The current execute_trade will still retry. So sleep WILL be called if it enters loop.
                # The goal is to verify the error message from the *first* failed attempt.
                with patch(f'{BASE_PATH_FOR_SERVICE_UNDER_TEST}.asyncio.sleep', new_callable=AsyncMock) as mock_sleep_s2:
                    mock_sleep_s2.return_value = None # Explicitly set return_value for this inner patch
                    # We need to mock _execute_single_trade_attempt to control its return for this sub-test
                    # if we want to isolate the behavior for the first attempt.
                    result_s2 = await self.service.execute_trade(
                        trade_type=TradeType.SELL,
                        input_token_address=spl_token_addr,
                        output_token_address=SOL_MINT_ADDRESS,
                        amount_input_token=float(ui_amount),
                        wallet_private_key_b58=self.default_wallet_private_key_b58,
                        wallet_address=self.default_wallet_address,
                        strategy_snapshot=strategy_snapshot_invalid_decimals,
                        signal_id=PydanticObjectId(),
                        trade_record_id=PydanticObjectId()
                    )
                    self.assertEqual(result_s2.status, TradeStatus.FAILED)
                    # The error message from the LAST attempt (after all retries) will be asserted here.
                    # The service logs the error for each attempt. The final TradeResult has the last error.
                    expected_error_fragment = f"Invalid input_token_decimals '{str(invalid_val)}'"
                    self.assertIn(expected_error_fragment, result_s2.error_message)
                    
                    # If the error is due to bad decimals, _execute_single_trade_attempt fails, 
                    # and execute_trade retries. So subprocess might not be called AT ALL for this specific error.
                    # Let's assume the decimal validation leads to _execute_single_trade_attempt returning FAILED
                    # without calling subprocess. Then execute_trade retries. 
                    # This test needs to be more precise about what it expects for subprocess calls and sleep calls
                    # if the failure is a config error that should not be retried.

                    # For now, focus on the error message. Sleep will be called due to retry logic.
                    # self.mock_create_subprocess_exec.assert_not_called() # This might fail if retries happen and decimal error is not caught early enough in _execute_single_trade_attempt
                    # mock_sleep_s2.assert_not_called() # This will fail because execute_trade retries on FAILED status
        
        self.mock_create_subprocess_exec.reset_mock()

        # --- Scenario 3: Selling SPL, but input_token_decimals MISSING from snapshot ---
        strategy_snapshot_missing_decimals = self.default_strategy_snapshot_dict.copy()
        if 'input_token_decimals' in strategy_snapshot_missing_decimals:
            del strategy_snapshot_missing_decimals['input_token_decimals']

        # Patch asyncio.sleep for this specific subtest's call. Retry will happen.
        with patch(f'{BASE_PATH_FOR_SERVICE_UNDER_TEST}.asyncio.sleep', new_callable=AsyncMock) as mock_sleep_s3:
            mock_sleep_s3.return_value = None # Explicitly set return_value for this inner patch
            result_s3 = await self.service.execute_trade(
                trade_type=TradeType.SELL,
                input_token_address=spl_token_addr,
                output_token_address=SOL_MINT_ADDRESS,
                amount_input_token=float(ui_amount),
                wallet_private_key_b58=self.default_wallet_private_key_b58,
                wallet_address=self.default_wallet_address,
                strategy_snapshot=strategy_snapshot_missing_decimals,
                signal_id=PydanticObjectId(),
                trade_record_id=PydanticObjectId()
            )
            self.assertEqual(result_s3.status, TradeStatus.FAILED)
            expected_error_s3 = f"input_token_decimals missing in strategy_snapshot for SPL token {spl_token_addr}"
            self.assertEqual(result_s3.error_message, expected_error_s3)
            # self.mock_create_subprocess_exec.assert_not_called() # Will be called if retried & not failing early
            # mock_sleep_s3.assert_not_called() # This will fail because of retry.
            # We expect sleep to be called (e.g., 9 times for 10 attempts if it always fails this way)
            self.assertTrue(mock_sleep_s3.called, "Expected sleep to be called due to retries")

    def test_analyze_error_message_characteristics(self):
        """测试错误消息特征分析功能"""
        from utils.trading.solana.gmgn_trade_service import _analyze_error_message_characteristics
        
        # 测试包含HTML特殊字符的错误消息（真实Bug场景）
        problematic_error = "SyntaxError: Unexpected token '<', \"<!DOCTYPE \"... is not valid JSON"
        analysis = _analyze_error_message_characteristics(problematic_error)
        
        self.assertTrue(analysis['has_html_chars'], "Should detect HTML characters")
        self.assertTrue(analysis['has_json_parse_error'], "Should detect JSON parse error")
        self.assertTrue(analysis['has_doctype'], "Should detect DOCTYPE")
        self.assertGreater(analysis['special_char_count'], 0, "Should count special characters")
        self.assertEqual(analysis['message_length'], len(problematic_error))
        
        # 测试正常错误消息
        normal_error = "Network timeout error"
        analysis_normal = _analyze_error_message_characteristics(normal_error)
        
        self.assertFalse(analysis_normal['has_html_chars'], "Normal error should not have HTML chars")
        self.assertFalse(analysis_normal['has_json_parse_error'], "Normal error should not be JSON parse error")
        self.assertFalse(analysis_normal['has_doctype'], "Normal error should not have DOCTYPE")
        self.assertEqual(analysis_normal['special_char_count'], 0)
        
        # 测试空消息
        empty_analysis = _analyze_error_message_characteristics("")
        self.assertEqual(empty_analysis['message_length'], 0)
        self.assertFalse(empty_analysis['has_html_chars'])

    def test_analyze_node_stderr_output(self):
        """测试Node.js stderr输出分析功能"""
        from utils.trading.solana.gmgn_trade_service import _analyze_node_stderr_output
        from beanie import PydanticObjectId
        
        # 模拟包含GMGN API错误的stderr输出
        stderr_with_gmgn_errors = """
Executing trade with parameters:
  GMGN API Host: https://gmgn.ai
Error during trade execution: {"stage": "submit", "message": "API returned HTML"}
=== Detailed Submit Response Analysis ===
URL: https://gmgn.ai/defi/router/v1/sol/tx/submit_signed_transaction
Status: 500 Internal Server Error
Response appears to be HTML instead of JSON!
Failed to parse route response as JSON: SyntaxError unexpected token
First 500 characters of HTML response:
<!DOCTYPE html><html><head><title>Error</title></head>
        """
        
        trade_record_id = PydanticObjectId()
        analysis = _analyze_node_stderr_output(stderr_with_gmgn_errors, trade_record_id, 1)
        
        self.assertEqual(analysis['analysis_status'], 'analyzed')
        self.assertTrue(analysis['contains_gmgn_api_errors'], "Should detect GMGN API errors")
        self.assertTrue(analysis['contains_response_analysis'], "Should detect response analysis")
        self.assertTrue(analysis['contains_html_response'], "Should detect HTML response")
        self.assertTrue(analysis['contains_json_parse_errors'], "Should detect JSON parse errors")
        self.assertIn('submit', analysis['error_stages'], "Should extract error stages")
        self.assertGreater(len(analysis['detailed_logs']), 0, "Should collect detailed logs")
        
        # 测试空stderr
        empty_analysis = _analyze_node_stderr_output("", trade_record_id, 1)
        self.assertEqual(empty_analysis['analysis_status'], 'empty_stderr')
        self.assertEqual(empty_analysis['content_length'], 0)
        
        # 测试正常stderr（无错误）
        normal_stderr = "Executing Node.js script\nTransaction submitted successfully"
        normal_analysis = _analyze_node_stderr_output(normal_stderr, trade_record_id, 1)
        self.assertEqual(normal_analysis['analysis_status'], 'analyzed')
        self.assertFalse(normal_analysis['contains_gmgn_api_errors'])
        self.assertFalse(normal_analysis['contains_html_response'])

    # ==================== R1.5 GMGN专业错误识别测试 ====================
    
    def test_gmgn_slippage_error_identification(self) -> None:
        """测试GMGN特定的滑点错误识别"""
        # 测试GMGN API特定的滑点错误关键词
        gmgn_slippage_errors = [
            "slippage tolerance exceeded",
            "price impact too high", 
            "insufficient output amount",
            "would result in less than minimum",
            "minimum received not met",
            "exceeds maximum slippage",
            "price moved too much",
            "gmgn slippage exceeded",
            "jupiter slippage tolerance",
            "solana dex slippage",
            "swap failed due to slippage",
            "price impact exceeds limit",
            "transaction simulation failed due to slippage"
        ]
        
        for error_msg in gmgn_slippage_errors:
            with self.subTest(error_message=error_msg):
                result = self.service.is_slippage_related_error(error_message=error_msg)
                self.assertTrue(result, f"应该识别为滑点错误: {error_msg}")
    
    def test_gmgn_slippage_error_identification_with_provider_response(self) -> None:
        """测试通过provider_response识别GMGN滑点错误"""
        # 测试GMGN API响应中的错误码
        test_cases = [
            {
                'provider_response': {
                    'error': 'slippage tolerance exceeded',
                    'message': 'Transaction failed'
                },
                'expected': True
            },
            {
                'provider_response': {
                    'status': 'failed',
                    'error_code': 'SLIPPAGE_EXCEEDED',
                    'details': {'impact': 5.2}
                },
                'expected': True
            },
            {
                'provider_response': {
                    'message': 'price impact too high',
                    'expected': 1000,
                    'actual': 950
                },
                'expected': True
            },
            {
                'provider_response': {
                    'code': 'PRICE_IMPACT_TOO_HIGH',
                    'slippage': 2.5
                },
                'expected': True
            },
            {
                'provider_response': {
                    'error': 'NETWORK_ERROR',
                    'message': 'Connection timeout'
                },
                'expected': False
            }
        ]
        
        for i, test_case in enumerate(test_cases):
            with self.subTest(case=i):
                result = self.service.is_slippage_related_error(
                    error_message=None,
                    provider_response=test_case['provider_response']
                )
                self.assertEqual(result, test_case['expected'])
    
    def test_gmgn_non_retryable_error_identification(self) -> None:
        """测试GMGN特定的不可重试错误识别"""
        # 测试GMGN特定的不可重试错误关键词（基于实际实现）
        gmgn_non_retryable_errors = [
            # 父类通用错误（会被识别）
            "insufficient funds",
            "insufficient balance",
            "token not found",
            "invalid token",
            "unauthorized",
            "forbidden",
            "account not found",
            "invalid private key",
            "signature verification failed",
            
            # GMGN特定错误关键词
            "invalid wallet",
            "wallet not found",
            "private key invalid",
            "signature failed",
            "token not supported",
            "invalid token address",
            "mint not found",
            "api key invalid",
            "unauthorized access",
            "invalid parameters",
            "malformed request",
            "invalid amount",
            "amount too small",
            "amount too large"
        ]
        
        for error_msg in gmgn_non_retryable_errors:
            with self.subTest(error_message=error_msg):
                result = self.service.is_non_retryable_error(error_message=error_msg)
                self.assertTrue(result, f"应该识别为不可重试错误: {error_msg}")
    
    def test_gmgn_non_retryable_error_identification_with_provider_response(self) -> None:
        """测试通过provider_response识别GMGN不可重试错误"""
        test_cases = [
            {
                'provider_response': {
                    'error': 'INSUFFICIENT_FUNDS',
                    'balance': 0.001,
                    'required': 0.01
                },
                'expected': True
            },
            {
                'provider_response': {
                    'status': 'failed',
                    'error_code': 'INVALID_TOKEN',
                    'token_address': 'invalid_address'
                },
                'expected': True
            },
            {
                'provider_response': {
                    'gmgn_response': {
                        'error': 'AUTHENTICATION_FAILED',
                        'message': 'Invalid API key'
                    }
                },
                'expected': True
            },
            {
                'provider_response': {
                    'jupiter_response': {
                        'error': 'UNAUTHORIZED',
                        'message': 'Access denied'
                    }
                },
                'expected': True
            },
            {
                'provider_response': {
                    'error': 'TEMPORARY_NETWORK_ERROR',
                    'message': 'Service temporarily unavailable'
                },
                'expected': False
            }
        ]
        
        for i, test_case in enumerate(test_cases):
            with self.subTest(case=i):
                result = self.service.is_non_retryable_error(
                    error_message=None,
                    provider_response=test_case['provider_response']
                )
                self.assertEqual(result, test_case['expected'])
    
    def test_gmgn_retryable_errors_not_flagged_as_non_retryable(self) -> None:
        """测试可重试错误不会被误识别为不可重试错误"""
        retryable_errors = [
            "network timeout",
            "connection refused",
            "server temporarily unavailable",
            "rate limit exceeded",
            "service unavailable",
            "internal server error",
            "gateway timeout",
            "slippage tolerance exceeded"  # 滑点错误是可重试的
        ]
        
        for error_msg in retryable_errors:
            with self.subTest(error_message=error_msg):
                result = self.service.is_non_retryable_error(error_message=error_msg)
                self.assertFalse(result, f"不应该识别为不可重试错误: {error_msg}")
    
    def test_gmgn_non_slippage_errors_not_flagged_as_slippage(self) -> None:
        """测试非滑点错误不会被误识别为滑点错误"""
        non_slippage_errors = [
            "network timeout",
            "connection refused", 
            "server error",
            "insufficient funds",
            "token not found",
            "unauthorized",
            "rate limit exceeded",
            "internal server error"
        ]
        
        for error_msg in non_slippage_errors:
            with self.subTest(error_message=error_msg):
                result = self.service.is_slippage_related_error(error_message=error_msg)
                self.assertFalse(result, f"不应该识别为滑点错误: {error_msg}")
    
    def test_gmgn_error_identification_case_insensitive(self) -> None:
        """测试GMGN错误识别的大小写不敏感性"""
        # 测试滑点错误的大小写变体
        slippage_variants = [
            "SLIPPAGE TOLERANCE EXCEEDED",
            "Price Impact Too High",
            "INSUFFICIENT OUTPUT AMOUNT",
            "Gmgn Slippage Exceeded"
        ]
        
        for error_msg in slippage_variants:
            with self.subTest(error_message=error_msg):
                result = self.service.is_slippage_related_error(error_message=error_msg)
                self.assertTrue(result, f"应该识别为滑点错误（大小写不敏感）: {error_msg}")
        
        # 测试不可重试错误的大小写变体
        non_retryable_variants = [
            "INSUFFICIENT FUNDS",
            "Token Not Found",
            "INVALID TOKEN ADDRESS",
            "Api Key Invalid"
        ]
        
        for error_msg in non_retryable_variants:
            with self.subTest(error_message=error_msg):
                result = self.service.is_non_retryable_error(error_message=error_msg)
                self.assertTrue(result, f"应该识别为不可重试错误（大小写不敏感）: {error_msg}")
    
    def test_gmgn_error_identification_with_empty_inputs(self) -> None:
        """测试GMGN错误识别对空输入的处理"""
        # 测试空字符串和None
        empty_inputs = [None, "", "   "]
        
        for empty_input in empty_inputs:
            with self.subTest(input=repr(empty_input)):
                # 滑点错误识别应该返回False
                result = self.service.is_slippage_related_error(error_message=empty_input)
                self.assertFalse(result)
                
                # 不可重试错误识别应该返回False
                result = self.service.is_non_retryable_error(error_message=empty_input)
                self.assertFalse(result)
        
        # 测试空的provider_response
        empty_responses = [None, {}, {'error': ''}, {'status': None}]
        
        for empty_response in empty_responses:
            with self.subTest(response=empty_response):
                result = self.service.is_slippage_related_error(
                    error_message=None,
                    provider_response=empty_response
                )
                self.assertFalse(result)
                
                result = self.service.is_non_retryable_error(
                    error_message=None,
                    provider_response=empty_response
                )
                self.assertFalse(result)

# This block must be at the top indentation level (module level)
if __name__ == '__main__':
    unittest.main() 