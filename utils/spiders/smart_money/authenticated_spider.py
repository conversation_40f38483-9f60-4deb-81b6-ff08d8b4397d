from abc import abstractmethod
from typing import Optional, Dict, Any, Tuple
import logging
import json
from curl_cffi.requests import Response
import time

from utils.spiders.smart_money import BasicSpider

logger = logging.getLogger(__name__)

class AuthenticatedSpider(BasicSpider):
    """需要认证的爬虫基类
    
    为需要使用认证令牌访问API的爬虫提供基础功能，包括：
    - 获取有效认证令牌
    - 检查令牌是否过期
    - 刷新令牌
    - 处理认证请求
    
    子类需要实现特定平台的令牌刷新逻辑。
    """
    
    def __init__(self, max_retries: int = 3, retry_interval: float = 1.0):
        """初始化认证爬虫
        
        Args:
            max_retries: 最大重试次数
            retry_interval: 重试间隔时间(秒)
        """
        super().__init__(max_retries=max_retries, retry_interval=retry_interval)
        self.access_token = None
        self.expire_at = 0
    
    @abstractmethod
    async def refresh_token(self) -> bool:
        """刷新访问令牌
        
        需要由子类实现特定平台的令牌刷新逻辑
        
        Returns:
            bool: 刷新成功返回True，否则返回False
        """
        pass
    
    @abstractmethod
    def is_token_valid(self) -> bool:
        """检查当前访问令牌是否有效
        
        需要由子类实现令牌有效性检查逻辑
        
        Returns:
            bool: 如果令牌存在且未过期返回True，否则返回False
        """
        pass
    
    async def get_valid_token(self) -> Optional[str]:
        """获取有效的访问令牌，如果当前令牌无效则尝试刷新
        
        Returns:
            Optional[str]: 有效的访问令牌，如果获取失败则返回None
        """
        if self.is_token_valid():
            logger.debug(f"当前token有效，直接返回。过期时间: {self.expire_at}")
            return self.access_token
        
        logger.info(f"当前token无效或过期，尝试刷新。当前时间: {int(time.time())}")
        success = await self.refresh_token()
        if not success:
            logger.error("刷新token失败")
            return None
        
        logger.info(f"成功刷新token，新token过期时间: {self.expire_at}")
        return self.access_token
    
    async def make_authenticated_request(self, method: str, url: str, **kwargs) -> Tuple[bool, Response, Optional[Dict]]:
        """发送带有认证的请求
        
        Args:
            method: 请求方法，如'get', 'post'等
            url: 请求URL
            **kwargs: 传递给请求方法的其他参数
            
        Returns:
            Tuple[bool, Response, Optional[Dict]]: 
                - 请求是否成功
                - 原始响应对象
                - 如果请求成功且能解析为JSON，则为解析后的数据，否则为None
        """
        # 获取有效Token
        if self.is_token_valid() and self.access_token:
            logger.debug(f"使用现有token，过期时间: {self.expire_at}")
            token = self.access_token
        else:
            logger.info("当前token无效或过期，尝试获取新token")
            token = await self.get_valid_token()
            
        if not token:
            logger.error("无法获取有效认证令牌，请求失败")
            return False, None, None
        
        # 设置或更新Authorization头
        headers = kwargs.get('headers', {}).copy()
        headers['authorization'] = f'Bearer {token}'
        kwargs['headers'] = headers
        
        # 发送请求
        try:
            # 根据method调用相应的请求方法
            if method.lower() == 'get':
                response = await self.get(url, **kwargs)
            elif method.lower() == 'post':
                response = await self.post(url, **kwargs)
            elif method.lower() == 'put':
                response = await self.put(url, **kwargs)
            elif method.lower() == 'delete':
                response = await self.delete(url, **kwargs)
            else:
                logger.error(f"不支持的请求方法: {method}")
                return False, None, None
            
            # 尝试解析JSON响应
            try:
                data = response.json()
                
                # 检查令牌是否过期（由子类实现处理）
                if not self.check_response_valid(data):
                    logger.warning("请求失败，尝试刷新令牌并重试请求")
                    # 强制刷新Token
                    self.access_token = None
                    success = await self.refresh_token()
                    if success:
                        # 更新Authorization头并重试请求
                        headers['authorization'] = f'Bearer {self.access_token}'
                        kwargs['headers'] = headers
                        
                        # 再次发送请求
                        if method.lower() == 'get':
                            response = await self.get(url, **kwargs)
                        elif method.lower() == 'post':
                            response = await self.post(url, **kwargs)
                        elif method.lower() == 'put':
                            response = await self.put(url, **kwargs)
                        elif method.lower() == 'delete':
                            response = await self.delete(url, **kwargs)
                        
                        data = response.json()
                    else:
                        logger.error("令牌刷新失败，无法完成请求")
                        return False, response, None
                
                # 检查请求是否成功
                if self.is_response_success(data):
                    return True, response, data
                else:
                    logger.error(f"API请求失败: {self.get_error_message(data)}")
                    return False, response, data
            
            except json.JSONDecodeError:
                logger.error(f"解析响应失败: {response.text[:200]}...")
                return False, response, None
                
        except Exception as e:
            logger.error(f"请求过程中发生错误: {e}")
            return False, None, None
    
    def check_response_valid(self, data: Dict[str, Any]) -> bool:
        """检查响应是否有效（令牌是否过期）
        
        子类可以重写此方法以实现特定的响应检查逻辑
        
        Args:
            data: 响应数据
            
        Returns:
            bool: 如果响应有效返回True，否则返回False
        """
        return True
    
    def is_response_success(self, data: Dict[str, Any]) -> bool:
        """检查API响应是否成功
        
        子类可以重写此方法以实现特定的成功判断逻辑
        
        Args:
            data: 响应数据
            
        Returns:
            bool: 如果响应表示成功返回True，否则返回False
        """
        return True
    
    def get_error_message(self, data: Dict[str, Any]) -> str:
        """从响应中获取错误消息
        
        子类可以重写此方法以实现特定的错误消息提取逻辑
        
        Args:
            data: 响应数据
            
        Returns:
            str: 错误消息
        """
        return "未知错误" 