import logging
from typing import Dict, List, Any, Optional
import random

from utils.backtest_event_driven.events import (
    Event, EventType, OrderEvent, FillEvent, SignalType, PriceUnavailableEvent
)
from utils.backtest_event_driven.event_queue import EventQueue
from utils.backtest_event_driven.data_handler import DataHandler

logger = logging.getLogger("ExecutionHandler")


class ExecutionHandler:
    """执行处理器，负责处理订单事件并生成成交事件"""
    
    def __init__(self, event_queue: EventQueue, data_handler: DataHandler = None, 
                config: Dict[str, Any] = None):
        """初始化执行处理器
        
        Args:
            event_queue: 事件队列
            data_handler: 数据处理器，用于获取代币价格数据
            config: 配置参数
        """
        self.event_queue = event_queue
        self.data_handler = data_handler
        self.config = config or {}
        self._price_cache = {}  # 初始化价格缓存
        
        # 交易成本参数
        self.slippage_pct = self.config.get('slippage_pct', 0.01)  # 滑点百分比
        self.commission_pct = self.config.get('commission_pct', 0.001)  # 手续费百分比
        
        # 价格不可用记录
        self.skipped_trades = []  # 记录因价格不可用而跳过的交易
        
        # 注册为订单事件的处理器
        event_queue.register_handler(EventType.ORDER, self.handle_order)
        
    async def handle_order(self, event: OrderEvent) -> None:
        """处理订单事件，生成成交事件
        
        Args:
            event: 订单事件
        """
        # 获取基本信息
        token_address = event.token_address
        token_info = event.token_info
        timestamp = event.timestamp
        
        # 获取或估算价格（现在调用异步方法）
        price = await self._get_price(token_address, token_info, timestamp)
        
        # 如果价格为None，表示无法获取价格数据，创建价格不可用事件并跳过此次交易
        if price is None:
            price_unavailable_event = PriceUnavailableEvent(
                event.signal_type,
                timestamp,
                token_address,
                event.amount,
                token_info,
                "无法获取价格数据"
            )
            # 将价格不可用事件推入队列
            self.event_queue.push(price_unavailable_event)
            # 记录跳过的交易
            self.skipped_trades.append({
                "timestamp": timestamp,
                "token_address": token_address,
                "signal_type": "BUY" if event.signal_type == SignalType.BUY else "SELL",
                "amount": event.amount,
                "token_symbol": token_info.get('symbol', 'Unknown') if token_info else 'Unknown'
            })
            logger.warning(f"跳过交易: 无法获取 {token_address} 的价格数据")
            return
        
        # 应用滑点
        if event.signal_type == SignalType.BUY:
            # 买入价格上浮 (不利于买家)
            slippage_adjusted_price = price * (1 + self._calculate_slippage())
            # 买入时，amount表示美元金额，需要转换为代币数量
            actual_amount = event.amount / slippage_adjusted_price
            logger.info(f"买入订单: 金额${event.amount:.2f}，价格${slippage_adjusted_price:.4f}，数量{actual_amount:.6f}")
        else:
            # 卖出价格下浮 (不利于卖家)
            slippage_adjusted_price = price * (1 - self._calculate_slippage())
            # 卖出时，amount表示代币数量
            actual_amount = event.amount
            
        # 计算手续费
        fill_cost = actual_amount * slippage_adjusted_price
        commission = fill_cost * self.commission_pct
        
        # 创建成交事件
        fill_event = FillEvent(
            event.signal_type,
            timestamp,
            token_address,
            actual_amount,  # 使用转换后的数量
            slippage_adjusted_price,
            commission,
            token_info
        )
        
        # 将成交事件推入队列
        self.event_queue.push(fill_event)
        logger.info(f"生成成交事件: {fill_event}")
        
    async def _get_price(self, token_address: str, token_info: Dict[str, Any],
                  timestamp: int) -> Optional[float]:
        """获取代币价格
        
        Args:
            token_address: 代币地址
            token_info: 代币信息
            timestamp: 时间戳
            
        Returns:
            Optional[float]: 代币价格，如果无法获取则返回None
        """
        # 如果token_info中包含价格，直接使用
        if token_info and 'price' in token_info:
            return token_info['price']
            
        # 尝试从缓存中获取价格
        cache_key = f"{token_address}_{timestamp}"
        if cache_key in self._price_cache:
            return self._price_cache[cache_key]
        
        # 尝试从API获取价格数据
        if self.data_handler:
            try:
                # 使用data_handler的异步方法获取价格数据
                if hasattr(self.data_handler, 'get_token_price_async'):
                    candle_data = await self.data_handler.get_token_price_async(token_address, timestamp)
                    if candle_data and 'close' in candle_data:
                        price = float(candle_data['close'])
                        logger.debug(f"从API获取到价格: {token_address} @ {timestamp} = {price}")
                        
                        # 如果价格为0或无效，视为无法获取价格
                        if price <= 0:
                            logger.warning(f"获取到无效价格: {token_address} @ {timestamp} = {price}")
                            return None
                        
                        # 缓存价格
                        self._price_cache[cache_key] = price
                        return price
            except Exception as e:
                logger.warning(f"获取价格失败: {e}")
        
        # 无法获取价格数据，返回None
        return None
        
    def _calculate_slippage(self) -> float:
        """计算滑点
        
        Returns:
            float: 滑点比例
        """
        # 暂时移除随机滑点，返回固定值0以保证确定性
        # 后续可以恢复或调整此处的滑点模拟逻辑
        # base_slippage = self.slippage_pct
        # random_factor = random.random() * base_slippage  # 0到基础滑点之间的随机值
        # return base_slippage + random_factor 
        return 0.0 