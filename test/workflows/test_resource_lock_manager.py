import unittest
import asyncio
from utils.workflows.parallel_control import ResourceLockManager

class TestResourceLockManager(unittest.TestCase):
    """ResourceLockManager类的单元测试"""

    @classmethod
    def setUpClass(cls):
        cls.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(cls.loop)

    @classmethod
    def tearDownClass(cls):
        tasks = asyncio.all_tasks(loop=cls.loop)
        for task in tasks:
            task.cancel()
        if tasks:
            cls.loop.run_until_complete(asyncio.gather(*tasks, return_exceptions=True))
        cls.loop.close()
        asyncio.set_event_loop(None)

    async def async_setup(self):
        self.lock_manager = ResourceLockManager() # 移除 loop

    async def async_teardown(self):
        # ResourceLockManager 可能没有显式的 stop/cleanup 方法
        # 但如果它内部创建了任务，需要在 tearDownClass 中处理
        pass 

    async def _run_test_async(self, test_coro_func, *args):
        await self.async_setup()
        try:
            await test_coro_func(*args)
        finally:
            await self.async_teardown()

    def test_acquire_and_release_lock(self):
        """测试获取和释放锁的基本功能"""
        self.loop.run_until_complete(self._run_test_async(self._test_acquire_and_release_lock_actual))
        
    async def _test_acquire_and_release_lock_actual(self):
        """测试锁的获取和释放"""
        # 获取锁
        result = await self.lock_manager.acquire_lock("resource1", "owner1")
        self.assertTrue(result, "应该成功获取锁")
        
        # 释放锁
        release_result = await self.lock_manager.release_lock("resource1", "owner1")
        self.assertTrue(release_result, "应该成功释放锁")
        
        # 验证释放后可以重新获取
        result2 = await self.lock_manager.acquire_lock("resource1", "owner2")
        self.assertTrue(result2, "锁释放后应该可以被其他所有者获取")
        
        # 释放锁
        await self.lock_manager.release_lock("resource1", "owner2") # Clean up
        
    def test_lock_ownership(self):
        """测试锁的所有权验证"""
        self.loop.run_until_complete(self._run_test_async(self._test_lock_ownership_actual))
        
    async def _test_lock_ownership_actual(self):
        """测试锁的所有权"""
        # 所有者1获取锁
        await self.lock_manager.acquire_lock("resource1", "owner1")
        
        # 所有者2不能释放所有者1的锁
        release_result = await self.lock_manager.release_lock("resource1", "owner2")
        self.assertFalse(release_result, "非锁持有者不应该能释放锁")
        
        # 所有者1可以释放自己的锁
        release_result = await self.lock_manager.release_lock("resource1", "owner1")
        self.assertTrue(release_result, "锁持有者应该能释放自己的锁")
        
    def test_concurrent_lock_access(self):
        """测试并发访问锁"""
        self.loop.run_until_complete(self._run_test_async(self._test_concurrent_lock_access_actual))
        
    async def _test_concurrent_lock_access_actual(self):
        """测试并发获取锁"""
        # 所有者1获取锁
        await self.lock_manager.acquire_lock("resource1", "owner1")
        
        # 创建所有者2获取同一资源锁的任务
        acquire_task = asyncio.create_task(
            self.lock_manager.acquire_lock("resource1", "owner2", timeout=0.5)
        )
        
        # 等待一小段时间
        await asyncio.sleep(0.1)
        
        # 所有者1释放锁
        await self.lock_manager.release_lock("resource1", "owner1")
        
        # 等待所有者2的获取结果
        result = await acquire_task
        self.assertTrue(result, "锁释放后，等待的获取操作应该成功")
        
        if result: # Clean up if owner2 acquired the lock
            await self.lock_manager.release_lock("resource1", "owner2")

    def test_lock_timeout(self):
        """测试锁获取超时"""
        self.loop.run_until_complete(self._run_test_async(self._test_lock_timeout_actual))
        
    async def _test_lock_timeout_actual(self):
        """测试锁获取超时处理"""
        # 所有者1获取锁
        await self.lock_manager.acquire_lock("resource1", "owner1")
        
        # 所有者2尝试获取同一资源锁，设置较短超时
        result = await self.lock_manager.acquire_lock("resource1", "owner2", timeout=0.1)
        self.assertFalse(result, "由于超时，应该获取锁失败")
        
        # 确保所有者1仍然持有锁
        release_result = await self.lock_manager.release_lock("resource1", "owner1")
        self.assertTrue(release_result, "所有者1应该仍然持有锁")
        
    def test_multiple_locks(self):
        """测试多个资源锁"""
        self.loop.run_until_complete(self._run_test_async(self._test_multiple_locks_actual))
        
    async def _test_multiple_locks_actual(self):
        """测试管理多个资源锁"""
        # 获取多个不同资源的锁
        results = await asyncio.gather(
            self.lock_manager.acquire_lock("resource1", "owner1"),
            self.lock_manager.acquire_lock("resource2", "owner2"),
            self.lock_manager.acquire_lock("resource3", "owner3")
        )
        
        # 验证所有锁获取成功
        self.assertTrue(all(results), "应该能够同时获取多个不同资源的锁")
        
        # 释放所有锁
        release_results = await asyncio.gather(
            self.lock_manager.release_lock("resource1", "owner1"),
            self.lock_manager.release_lock("resource2", "owner2"),
            self.lock_manager.release_lock("resource3", "owner3")
        )
        
        # 验证所有锁释放成功
        self.assertTrue(all(release_results), "应该能够释放所有锁")
        
    def test_reacquire_own_lock(self):
        """测试重复获取自己的锁"""
        self.loop.run_until_complete(self._run_test_async(self._test_reacquire_own_lock_actual))
        
    async def _test_reacquire_own_lock_actual(self):
        """测试所有者重复获取自己的锁"""
        # 所有者获取锁
        await self.lock_manager.acquire_lock("resource1", "owner1")
        
        # 尝试再次获取，会因为锁被自己持有而阻塞，设置短超时
        result = await self.lock_manager.acquire_lock("resource1", "owner1", timeout=0.1)
        self.assertFalse(result, "不应该能重复获取自己已持有的锁 (asyncio.Lock is not re-entrant)")
        
        # 释放锁
        await self.lock_manager.release_lock("resource1", "owner1")

if __name__ == "__main__":
    unittest.main() 