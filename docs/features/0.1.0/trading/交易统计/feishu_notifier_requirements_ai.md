# 交易统计飞书消息发送器需求规格

## 1. 功能概述

### 1.1 功能描述
为现有的交易统计分析功能增加飞书机器人消息发送器，支持定期发送交易统计摘要到飞书群组，包含关键统计指标和HTML报告链接。

### 1.2 业务背景
- 用户需要定期了解交易表现情况
- 需要通过飞书群组及时获取交易统计信息
- 希望能够快速访问详细的HTML报告
- 支持日报和周报两种频率

### 1.3 目标用户
- 交易团队成员
- 投资决策者
- 风控人员
- 数据分析师

## 2. 功能需求

### 2.1 核心功能需求

#### 2.1.1 飞书消息发送器
- **FR-001**: 系统应支持通过飞书Webhook发送消息
- **FR-002**: 系统应支持富文本消息格式（卡片消息）
- **FR-003**: 系统应支持消息发送失败重试机制
- **FR-004**: 系统应记录消息发送日志和状态

#### 2.1.2 统计摘要生成
- **FR-005**: 系统应生成日交易统计摘要（最近24小时）
- **FR-006**: 系统应生成周交易统计摘要（最近7天）
- **FR-007**: 摘要应包含总交易数、总胜率、总盈利率
- **FR-008**: 摘要应包含分策略的胜率和盈利率（前5名）
- **FR-009**: 摘要应包含最佳和最差交易记录

#### 2.1.3 报告链接集成
- **FR-010**: 系统应支持配置HTML报告的公网访问基础URL
- **FR-011**: 系统应自动生成HTML报告并上传到指定位置
- **FR-012**: 消息中应包含可点击的报告访问链接
- **FR-013**: 支持报告文件的自动命名（包含时间戳）

#### 2.1.4 CLI参数扩展
- **FR-014**: 增加`--send-feishu`参数启用飞书消息发送
- **FR-015**: 增加`--feishu-webhook`参数指定Webhook URL
- **FR-016**: 增加`--report-base-url`参数指定报告访问基础URL
- **FR-017**: 增加`--upload-path`参数指定报告上传路径
- **FR-018**: 增加`--period`参数指定统计周期（daily/weekly）

### 2.2 消息格式要求

#### 2.2.1 消息结构
- **FR-019**: 使用飞书卡片消息格式
- **FR-020**: 消息标题应包含统计周期和时间范围
- **FR-021**: 消息内容应结构化展示关键指标
- **FR-022**: 消息底部应包含报告链接按钮

#### 2.2.2 内容要求
- **FR-023**: 总体统计：总交易数、胜率、盈利率、盈利金额
- **FR-024**: 策略统计：前5个策略的名称、交易数、胜率、盈利率
- **FR-025**: 极值统计：最大盈利、最大亏损
- **FR-026**: 时间信息：统计时间范围、生成时间

### 2.3 配置管理

#### 2.3.1 环境变量支持
- **FR-027**: 支持通过环境变量配置飞书Webhook URL
- **FR-028**: 支持通过环境变量配置报告基础URL
- **FR-029**: 支持通过环境变量配置默认上传路径

#### 2.3.2 配置文件支持
- **FR-030**: 支持通过配置文件管理多个飞书群组
- **FR-031**: 支持为不同群组配置不同的消息模板
- **FR-032**: 支持配置消息发送的时间窗口

## 3. 技术需求

### 3.1 飞书API集成
- **TR-001**: 使用飞书Webhook API发送消息
- **TR-002**: 支持飞书卡片消息格式
- **TR-003**: 实现消息发送的异步处理
- **TR-004**: 实现发送失败的重试机制

### 3.2 文件上传管理
- **TR-005**: 支持HTML报告文件的自动上传
- **TR-006**: 支持多种上传方式（本地路径、云存储等）
- **TR-007**: 实现文件URL的自动生成
- **TR-008**: 支持文件的定期清理

### 3.3 错误处理
- **TR-009**: 完善的异常捕获和处理
- **TR-010**: 详细的错误日志记录
- **TR-011**: 发送失败时的降级处理
- **TR-012**: 网络超时和重试机制

## 4. 数据模型

### 4.1 飞书消息模型
```python
class FeishuMessage(BaseModel):
    """飞书消息模型"""
    msg_type: str = "interactive"  # 卡片消息
    card: Dict[str, Any]          # 卡片内容
    
class FeishuCard(BaseModel):
    """飞书卡片模型"""
    header: Dict[str, Any]        # 卡片头部
    elements: List[Dict[str, Any]] # 卡片元素
    
class TradingSummary(BaseModel):
    """交易摘要模型"""
    period: str                   # 统计周期
    start_date: datetime         # 开始时间
    end_date: datetime           # 结束时间
    total_trades: int            # 总交易数
    win_rate: float              # 胜率
    profit_rate: float           # 盈利率
    profit_amount: float         # 盈利金额
    top_strategies: List[Dict]   # 前5策略
    best_trade: Dict             # 最佳交易
    worst_trade: Dict            # 最差交易
    report_url: str              # 报告链接
```

### 4.2 配置模型
```python
class FeishuNotifierConfig(BaseModel):
    """飞书通知器配置"""
    webhook_url: str             # Webhook URL
    report_base_url: str         # 报告基础URL
    upload_path: str             # 上传路径
    period: str = "daily"        # 统计周期
    retry_times: int = 3         # 重试次数
    timeout: int = 30            # 超时时间
```

## 5. 接口设计

### 5.1 飞书通知器接口
```python
class FeishuNotifier:
    async def send_trading_summary(
        self, 
        summary: TradingSummary,
        webhook_url: str
    ) -> bool
    
    async def upload_report(
        self,
        report_path: str,
        upload_path: str
    ) -> str
    
    def generate_card_message(
        self,
        summary: TradingSummary
    ) -> FeishuMessage
```

### 5.2 CLI接口扩展
```bash
# 发送日报到飞书
python -m utils.trading.statistics.cli \
    --days 1 \
    --send-feishu \
    --feishu-webhook "https://open.feishu.cn/open-apis/bot/v2/hook/xxx" \
    --report-base-url "https://reports.example.com" \
    --upload-path "/var/www/reports"

# 发送周报到飞书
python -m utils.trading.statistics.cli \
    --days 7 \
    --period weekly \
    --send-feishu \
    --feishu-webhook "https://open.feishu.cn/open-apis/bot/v2/hook/xxx"
```

## 6. 消息模板设计

### 6.1 日报模板
```
📊 交易日报 - {date}

📈 总体表现
• 总交易数：{total_trades}
• 胜率：{win_rate}%
• 盈利率：{profit_rate}%
• 盈利金额：{profit_amount} SOL

🎯 策略表现 (Top 5)
{strategy_list}

💰 极值统计
• 最大盈利：{max_profit} SOL
• 最大亏损：{max_loss} SOL

📋 详细报告：[点击查看]({report_url})
```

### 6.2 周报模板
```
📊 交易周报 - {start_date} 至 {end_date}

📈 本周总体表现
• 总交易数：{total_trades}
• 胜率：{win_rate}%
• 盈利率：{profit_rate}%
• 盈利金额：{profit_amount} SOL

🎯 策略表现排行
{strategy_ranking}

📈 趋势分析
• 较上周交易数变化：{trade_change}
• 较上周胜率变化：{win_rate_change}

📋 详细报告：[点击查看]({report_url})
```

## 7. 实施优先级

### 7.1 高优先级
1. 飞书消息发送器基础实现
2. 交易摘要生成功能
3. CLI参数扩展
4. 基础消息模板

### 7.2 中优先级
1. 卡片消息格式优化
2. 文件上传功能
3. 错误处理和重试机制
4. 配置管理功能

### 7.3 低优先级
1. 多群组支持
2. 消息模板自定义
3. 定时发送功能
4. 高级统计分析

## 8. 验收标准

### 8.1 功能验收
- [ ] 能够成功发送飞书消息
- [ ] 消息格式美观且信息完整
- [ ] HTML报告链接可正常访问
- [ ] CLI参数功能正常
- [ ] 错误处理机制有效

### 8.2 性能验收
- [ ] 消息发送响应时间 < 5秒
- [ ] 文件上传成功率 > 95%
- [ ] 系统稳定性良好

### 8.3 用户体验验收
- [ ] 消息内容清晰易读
- [ ] 报告链接访问便捷
- [ ] 命令行操作简单

## 9. 风险评估

### 9.1 技术风险
- **风险**: 飞书API变更导致兼容性问题
- **缓解**: 使用稳定的Webhook API，增加版本检测

### 9.2 网络风险
- **风险**: 网络不稳定导致发送失败
- **缓解**: 实现重试机制和降级处理

### 9.3 安全风险
- **风险**: Webhook URL泄露
- **缓解**: 使用环境变量管理敏感信息 