name: 'GMGN代币价格同步工作流'
description: '定期将不同时间范围的代币价格数据同步到Token模型'

nodes:
  - name: "TokenSyncSchedulerNode"
    node_type: "input"
    interval: 10  # 每分钟运行一次
    generate_data: workflows.gmgn_token_sync.handler.generate_sync_task
    flow_control:
      max_pending_messages: 10
      check_interval: 1
      enable_flow_control: true

  - name: "TokenSyncProcessNode"
    node_type: "process"
    depend_ons: ["TokenSyncSchedulerNode"]
    concurrency: 1
    interval: 1
    process_item: workflows.gmgn_token_sync.handler.process_sync_task

  - name: "TokenSyncStorageNode"
    node_type: "storage"
    depend_ons: ["TokenSyncProcessNode"]
    batch_size: 1000  # 每批处理1000条数据
    store_data: workflows.gmgn_token_sync.handler.store_sync_result
    validate: workflows.gmgn_token_sync.handler.validate_sync_result 