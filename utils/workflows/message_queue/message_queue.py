"""
消息队列模块

提供消息队列的抽象接口和基本实现
"""

# 避免直接导入asyncio.queues，用asyncio模块而不是具体的子模块
import asyncio
import logging
import json
import uuid
import os
import time
import traceback
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Set
from datetime import datetime
import atexit
import signal
import sys
import concurrent.futures

# 移动import语句到函数内部，避免循环导入
# import aiokafka
# import redis.asyncio as redis

class Message:
    """消息类，表示队列中的一条消息"""
    
    def __init__(self, data: Any, metadata: Dict = None):
        """初始化消息
        
        Args:
            data: 消息数据
            metadata: 消息元数据
        """
        self.data = data
        self.metadata = metadata or {}
        self.id = str(uuid.uuid4())
        self.timestamp = datetime.now().timestamp()
    
    def to_dict(self) -> Dict:
        """转换为字典
        
        Returns:
            Dict: 消息的字典表示
        """
        return {
            "id": self.id,
            "data": self.data,
            "metadata": self.metadata,
            "timestamp": self.timestamp
        }
    
    @classmethod
    def from_dict(cls, data_dict: Dict) -> 'Message':
        """从字典创建消息
        
        Args:
            data_dict: 消息的字典表示
            
        Returns:
            Message: 创建的消息对象
        """
        obj = cls(
            data=data_dict.get("data"),
            metadata=data_dict.get("metadata", {})
        )
        obj.id = data_dict.get("id", str(uuid.uuid4()))
        obj.timestamp = data_dict.get("timestamp", datetime.now().timestamp())
        return obj

class MessageQueue(ABC):
    """消息队列抽象基类"""
    
    def __init__(self, name: str):
        """初始化消息队列
        
        Args:
            name: 队列名称
        """
        self.name = name
        self.logger = logging.getLogger(f"MessageQueue.{name}")
    
    @abstractmethod
    async def setup(self):
        """设置队列，初始化必要的资源"""
        pass
    
    @abstractmethod
    async def close(self):
        """关闭队列，释放资源"""
        pass
    
    @abstractmethod
    async def send(self, message: Message) -> bool:
        """发送消息到队列
        
        Args:
            message: 要发送的消息
            
        Returns:
            bool: 是否发送成功
        """
        pass
    
    @abstractmethod
    async def receive(self, count: int = 1, timeout: int = 0) -> List[Message]:
        """从队列接收消息
        
        Args:
            count: 要接收的消息数量
            timeout: 等待超时时间（毫秒），0表示不等待
            
        Returns:
            List[Message]: 接收到的消息列表
        """
        pass
    
    @abstractmethod
    async def ack(self, message_id: str) -> bool:
        """确认消息已处理
        
        Args:
            message_id: 消息ID
            
        Returns:
            bool: 是否确认成功
        """
        pass
    
    @abstractmethod
    async def get_pending_count(self) -> int:
        """获取队列中待处理消息数量
        
        Returns:
            int: 待处理消息数量
        """
        pass

class RedisStreamQueue(MessageQueue):
    """基于Redis Stream的消息队列实现"""
    
    def __init__(self, name: str, redis_url: str = None, consumer_group: str = None, 
                 consumer_name: str = None, consumer_strategy: str = "latest"):
        """初始化Redis Stream队列
        
        Args:
            name: 队列名称
            redis_url: Redis连接URL
            consumer_group: 消费组名称
            consumer_name: 消费者名称
            consumer_strategy: 消费策略，"latest"或"earliest"
        """
        super().__init__(name)
        self.redis_url = redis_url or os.getenv('REDIS_URL', 'redis://localhost:6379/0')
        self.redis = None
        
        # 消费组配置
        self.consumer_group = consumer_group or f"{name}_group"
        self.consumer_name = consumer_name or f"{name}_{uuid.uuid4()}"
        self.consumer_strategy = consumer_strategy
        
        self.logger.debug(f"创建Redis Stream队列: {name}, 消费组: {self.consumer_group}, 消费者: {self.consumer_name}")
    
    async def setup(self):
        """设置队列，初始化Redis连接和消费组"""
        try:
            # 将redis导入移到函数内部
            import redis.asyncio as redis
            
            # 创建Redis连接
            self.redis = redis.from_url(self.redis_url)
            self.logger.debug(f"Redis连接已创建: {self.redis_url}")
            
            # 创建消费组
            try:
                # 获取起始ID
                start_id = "$" if self.consumer_strategy == "latest" else "0"
                
                # 创建消费组
                await self.redis.xgroup_create(
                    name=self.name,
                    groupname=self.consumer_group,
                    mkstream=True,
                    id=start_id
                )
                self.logger.info(f"创建消费组: {self.name} -> {self.consumer_group}")
            except redis.ResponseError as e:
                if "BUSYGROUP" not in str(e):
                    raise
                self.logger.info(f"消费组已存在: {self.name} -> {self.consumer_group}")
        except Exception as e:
            self.logger.error(f"设置Redis Stream队列失败: {str(e)}\n{traceback.format_exc()}")
            raise
    
    async def close(self):
        """关闭队列，释放Redis连接"""
        if self.redis:
            await self.redis.close()
            self.logger.debug("Redis连接已关闭")
    
    async def send(self, message: Message) -> bool:
        """发送消息到Redis Stream
        
        Args:
            message: 要发送的消息
            
        Returns:
            bool: 是否发送成功
        """
        if not self.redis:
            await self.setup()
        
        try:
            # 导入redis以获取可能的异常类型
            import redis.asyncio as redis
            
            # 将消息转换为字典
            message_dict = message.to_dict()
            
            # 将字典转换为JSON字符串
            try:
                message_json = json.dumps(message_dict, default=self._json_serializer)
            except TypeError as e:
                self.logger.error(f"JSON序列化失败: {str(e)}")
                self.logger.error(f"尝试序列化的数据: {message_dict}")
                raise
            
            # 将字典转换为Redis可接受的格式
            redis_data = {
                "data": message_json
            }
            
            # 发送数据到Stream
            message_id = await self.redis.xadd(self.name, redis_data)
            self.logger.debug(f"消息已发送到流 {self.name}: {message_id}")
            
            return True
        except Exception as e:
            self.logger.error(f"发送消息到流 {self.name} 失败: {str(e)}")
            return False
        
    async def delete(self, message_id: str) -> bool:
        """删除消息
        
        Args:
            message_id: 要删除的消息ID
            
        Returns:
            bool: 是否删除成功
        """
        if not self.redis:
            await self.setup()
        
        try:
            self.logger.info(f"删除消息 {message_id} 开始")
            # 从Stream中删除消息
            result = await self.redis.xdel(self.name, message_id)
            if result > 0:
                self.logger.info(f"消息 {message_id} 已从流 {self.name} 中删除")
                return True
            else:
                self.logger.warning(f"消息 {message_id} 不存在或无法删除")
                return False
        except Exception as e:
            self.logger.error(f"删除消息 {message_id} 失败: {str(e)}")
            return False
    
    async def receive(self, count: int = 1, timeout: int = 0) -> List[Message]:
        """从Redis Stream接收消息
        
        Args:
            count: 要接收的消息数量
            timeout: 等待超时时间（毫秒），0表示不等待
            
        Returns:
            List[Message]: 接收到的消息列表
        """
        if not self.redis:
            await self.setup()
        
        try:
            # 导入redis以获取可能的异常类型
            import redis.asyncio as redis
            
            # 从Stream读取消息
            messages = await self.redis.xreadgroup(
                groupname=self.consumer_group,
                consumername=self.consumer_name,
                streams={self.name: ">"},
                count=count,
                block=timeout
            )
            
            if not messages:
                return []
            
            # 解析消息
            result = []
            for stream_name, stream_messages in messages:
                for message_id, message_data in stream_messages:
                    try:
                        # 解析数据
                        data_json = message_data.get(b"data", b"{}")
                        data_dict = json.loads(data_json)
                        
                        # 创建Message对象
                        message = Message.from_dict(data_dict)
                        message.metadata["message_id"] = message_id.decode()
                        message.metadata["stream_name"] = stream_name.decode()
                        
                        result.append(message)
                    except Exception as e:
                        self.logger.error(f"解析消息 {message_id} 失败: {str(e)}")
            
            return result
        except Exception as e:
            self.logger.error(f"从流 {self.name} 接收消息失败: {str(e)}")
            return []
    
    async def ack(self, message_id: str) -> bool:
        """确认消息已处理
        
        Args:
            message_id: 消息ID
            
        Returns:
            bool: 是否确认成功
        """
        if not self.redis:
            await self.setup()
        
        try:
            # 确认消息
            self.logger.info(f"确认消息 {message_id} 开始")
            result = await self.redis.xack(self.name, self.consumer_group, message_id)
            success = result > 0
            if success:
                self.logger.info(f"消息 {message_id} 已确认")
            else:
                self.logger.warning(f"消息 {message_id} 确认失败，可能已被确认或不存在")
            self.logger.info(f"准备删除消息: {message_id}")
            # 删除消息
            result = await self.delete(message_id)
            if result:
                self.logger.info(f"消息 {message_id} 已删除")
            else:
                self.logger.warning(f"消息 {message_id} 删除失败")
            return success
        except Exception as e:
            self.logger.error(f"确认消息 {message_id} 失败: {str(e)}")
            return False
    
    async def get_pending_count(self) -> int:
        """获取队列中待处理消息数量
        
        Returns:
            int: 待处理消息数量
        """
        if not self.redis:
            await self.setup()
        
        try:
            # 获取流长度
            stream_length = await self.redis.xlen(self.name)
            self.logger.debug(f"流 {self.name} 中总共有 {stream_length} 条消息")
            
            # 直接返回流长度作为待处理消息数量
            # 这是一个简化的实现，实际上应该减去已确认但未删除的消息数量
            # 但由于我们在确认后立即删除消息，所以这个简化是可接受的
            return stream_length
        except Exception as e:
            self.logger.error(f"获取流 {self.name} 长度失败: {str(e)}")
            return 0
    
    def _json_serializer(self, obj):
        """自定义JSON序列化器，处理特殊类型
        
        Args:
            obj: 要序列化的对象
            
        Returns:
            序列化后的对象
            
        Raises:
            TypeError: 无法序列化的类型
        """
        # 处理PydanticObjectId
        try:
            from beanie import PydanticObjectId
            if isinstance(obj, PydanticObjectId):
                return str(obj)
        except ImportError:
            pass
        
        if isinstance(obj, datetime):
            return obj.isoformat()
        if hasattr(obj, 'dict') and callable(getattr(obj, 'dict')):
            return obj.dict()
        if hasattr(obj, '__dict__'):
            return obj.__dict__
        raise TypeError(f"无法序列化类型 {type(obj)}")

class FlowController:
    """流量控制器，用于管理消息队列的流量控制"""
    
    def __init__(self, queue: MessageQueue, max_pending_messages: int = 100, 
                 check_interval: float = 60.0, enable_flow_control: bool = True):
        """初始化流量控制器
        
        Args:
            queue: 要控制的消息队列
            max_pending_messages: 队列中最大待处理消息数
            check_interval: 检查间隔（秒）
            enable_flow_control: 是否启用流量控制
        """
        self.queue = queue
        self.max_pending_messages = max_pending_messages
        self.check_interval = check_interval
        self.enable_flow_control = enable_flow_control
        self.last_check_time = 0
        self.logger = logging.getLogger(f"FlowController.{queue.name}")
    
    async def should_proceed(self) -> bool:
        """检查是否应该继续处理
        
        Returns:
            bool: 如果应该继续处理则返回True，否则返回False
        """
        if not self.enable_flow_control or not self.queue:
            return True
        
        now = time.time()
        if now - self.last_check_time >= self.check_interval:
            self.last_check_time = now
            
            # 检查队列大小
            pending_count = await self.queue.get_pending_count()
            # 如果队列中的消息数量超过阈值，则暂时不继续处理
            if pending_count >= self.max_pending_messages:
                self.logger.warning(f"流量控制: 队列 {self.queue.name} 中已有 {pending_count} 条待处理消息，超过阈值 {self.max_pending_messages}，暂停处理")
                return False
            
            self.logger.info(f"流量控制: 队列 {self.queue.name} 中有 {pending_count} 条待处理消息，低于阈值 {self.max_pending_messages}，继续处理")
        
        return True
    
    async def check_immediate(self) -> bool:
        """立即检查队列状态，不考虑时间间隔
        
        Returns:
            bool: 如果应该继续处理则返回True，否则返回False
        """
        if not self.enable_flow_control or not self.queue:
            return True
        
        self.last_check_time = time.time()
        
        # 检查队列大小
        pending_count = await self.queue.get_pending_count()
        
        # 如果队列中的消息数量超过阈值，则暂时不继续处理
        if pending_count >= self.max_pending_messages:
            self.logger.debug(f"流量控制(立即检查): 队列 {self.queue.name} 中已有 {pending_count} 条待处理消息，超过阈值 {self.max_pending_messages}，暂停处理")
            return False
        
        self.logger.debug(f"流量控制(立即检查): 队列 {self.queue.name} 中有 {pending_count} 条待处理消息，低于阈值 {self.max_pending_messages}，继续处理")
        return True
    
    async def wait_if_needed(self) -> bool:
        """如果需要，等待一段时间再继续处理
        
        Returns:
            bool: 如果应该继续处理则返回True，否则返回False
        """
        if not await self.should_proceed():
            await asyncio.sleep(self.check_interval)
            return False
        
        return True

class KafkaQueue(MessageQueue):
    """基于Kafka的消息队列实现，使用aiokafka提供异步支持"""
    
    # atexit处理器的注册状态，避免重复注册
    _atexit_registered = False
    
    def __init__(self, name: str, bootstrap_servers: str = None, 
                 consumer_group: str = None, auto_offset_reset: str = "earliest"):
        """初始化Kafka队列
        
        Args:
            name: 队列名称（即Kafka主题）
            bootstrap_servers: Kafka服务器地址，如"localhost:9092"
            consumer_group: 消费者组ID
            auto_offset_reset: 消费策略，"latest"或"earliest"
        """
        super().__init__(name)
        self.bootstrap_servers = bootstrap_servers or os.getenv('KAFKA_SERVERS', 'localhost:9092')
        self.consumer_group = consumer_group or f"{name}_group"
        self.auto_offset_reset = auto_offset_reset
        self.producer = None
        self.consumer = None
        self._closing = False  # 标记是否正在关闭
        
        self.logger.debug(f"创建Kafka队列: {name}, 服务器: {self.bootstrap_servers}, 消费组: {self.consumer_group}")
        
        # 仅在首次创建实例时注册atexit处理器
        if not KafkaQueue._atexit_registered:
            import atexit
            import signal
            import sys
            # os 已经在全局导入，这里不需要再导入
            
            # 为SIGTERM和SIGINT信号注册处理函数
            def signal_handler(sig, frame):
                self.logger.info(f"收到信号: {sig}，准备清理资源...")
                try:
                    # 不直接调用阻塞式清理，而是简单地设置标记让程序正常退出
                    # Python信号处理器中应当尽量少做事情
                    self._closing = True
                    # 尝试优雅地退出
                    # 不等待资源清理完成，让系统退出机制处理
                    self.logger.info(f"设置关闭标记，让程序正常退出流程...")
                except Exception as e:
                    self.logger.error(f"设置关闭标记时发生错误: {e}")
                finally:
                    # 使用原始的退出方式
                    # 调用内置的默认处理器，通常会导致程序异常终止
                    sys.exit(1)
            
            # 注册信号处理器
            try:
                signal.signal(signal.SIGTERM, signal_handler)  # 终止信号
                signal.signal(signal.SIGINT, signal_handler)   # 中断信号 (Ctrl+C)
                self.logger.info("已注册信号处理器")
            except (ValueError, OSError) as e:
                self.logger.warning(f"无法注册信号处理器: {e}")
            
            # 注册atexit处理器，这个会在正常退出时执行
            atexit.register(self._sync_cleanup_resources)
            KafkaQueue._atexit_registered = True
            self.logger.info("已注册atexit处理器")
    
    def _blocking_cleanup_resources(self):
        """阻塞式资源清理，确保在进程退出前彻底清理Kafka资源
        
        这个函数创建新的事件循环并使用同步方式调用异步清理函数
        """
        self.logger.info("阻塞式清理Kafka资源开始")
        
        try:
            # 尝试使用已存在的事件循环
            try:
                loop = asyncio.get_event_loop()
                loop_created = False
            except RuntimeError:
                # 如果没有当前事件循环，创建一个新的
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop_created = True
            
            # 检查事件循环是否正在运行
            if loop.is_running():
                # 事件循环正在运行，使用run_coroutine_threadsafe
                self.logger.info("使用run_coroutine_threadsafe清理资源")
                
                # 导入concurrent.futures来处理超时
                import concurrent.futures
                
                if self.producer or self.consumer:
                    # 创建producer和consumer停止的任务
                    if self.producer:
                        try:
                            producer_future = asyncio.run_coroutine_threadsafe(self.producer.stop(), loop)
                            producer_future.result(timeout=5.0)  # 最多等待5秒
                        except (asyncio.TimeoutError, concurrent.futures.TimeoutError, Exception) as e:
                            self.logger.error(f"关闭Kafka生产者时出错: {str(e)}")
                        finally:
                            self.producer = None
                            self.logger.info("Kafka生产者已强制关闭")
                    
                    if self.consumer:
                        try:
                            consumer_future = asyncio.run_coroutine_threadsafe(self.consumer.stop(), loop)
                            consumer_future.result(timeout=5.0)  # 最多等待5秒
                        except (asyncio.TimeoutError, concurrent.futures.TimeoutError, Exception) as e:
                            self.logger.error(f"关闭Kafka消费者时出错: {str(e)}")
                        finally:
                            self.consumer = None
                            self.logger.info("Kafka消费者已强制关闭")
                else:
                    self.logger.info("没有活动的Kafka连接需要关闭")
            else:
                # 事件循环没有运行，可以使用run_until_complete
                self.logger.info("使用run_until_complete清理资源")
                
                if self.producer or self.consumer:
                    self.logger.info("强制关闭Kafka连接...")
                    
                    # 执行同步关闭
                    if self.producer:
                        try:
                            # 使用同步方式调用异步方法
                            loop.run_until_complete(self.producer.stop())
                        except Exception as e:
                            self.logger.error(f"关闭Kafka生产者时出错: {str(e)}")
                        finally:
                            self.producer = None
                            self.logger.info("Kafka生产者已强制关闭")
                        
                    if self.consumer:
                        try:
                            # 使用同步方式调用异步方法
                            loop.run_until_complete(self.consumer.stop())
                        except Exception as e:
                            self.logger.error(f"关闭Kafka消费者时出错: {str(e)}")
                        finally:
                            self.consumer = None
                            self.logger.info("Kafka消费者已强制关闭")
                else:
                    self.logger.info("没有活动的Kafka连接需要关闭")
                
                # 只有当我们创建了事件循环时才关闭它
                if loop_created:
                    loop.close()
        except Exception as e:
            self.logger.error(f"阻塞式清理Kafka资源时出错: {str(e)}")
            # 确保清空引用
            self.producer = None
            self.consumer = None
        finally:
            self.logger.info("阻塞式清理Kafka资源完成")
    
    def _sync_cleanup_resources(self):
        """同步版本的资源清理函数，用于atexit和信号处理
        
        这个函数会在主线程中同步调用，确保在程序退出前清理资源
        """
        self.logger.info("同步清理Kafka资源开始")
        try:
            # 获取当前事件循环或创建新的事件循环
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                # 如果没有当前事件循环，创建一个新的
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            # 确保事件循环在运行
            if not loop.is_running():
                # 直接运行清理函数
                self.logger.info("在新事件循环中运行清理函数")
                loop.run_until_complete(self._cleanup_resources())
            else:
                # 如果事件循环已在运行，创建一个任务并等待一小段时间
                self.logger.info("在现有事件循环中添加清理任务")
                future = asyncio.run_coroutine_threadsafe(self._cleanup_resources(), loop)
                # 等待一小段时间以允许任务完成
                try:
                    future.result(timeout=3.0)  # 最多等待3秒
                except (asyncio.TimeoutError, concurrent.futures.TimeoutError):
                    self.logger.warning("清理任务超时，继续执行后续清理")
        except Exception as e:
            self.logger.error(f"同步清理Kafka资源时出错: {str(e)}")
        finally:
            # 确保引用被清除
            self.producer = None
            self.consumer = None
            self.logger.info("同步清理Kafka资源完成")
    
    async def setup(self):
        """设置队列，初始化Kafka生产者和消费者"""
        try:
            # 导入aiokafka，在使用时才导入，避免循环导入
            import aiokafka
            from aiokafka.admin import AIOKafkaAdminClient, NewTopic
            from aiokafka.errors import UnknownTopicOrPartitionError, TopicAlreadyExistsError
            
            # 在初始化消费者前，检查并创建主题
            await self._ensure_topic_exists()
            
            # 创建异步生产者
            if self.producer is None:  # 避免重复初始化
                self.producer = aiokafka.AIOKafkaProducer(
                    bootstrap_servers=self.bootstrap_servers,
                    value_serializer=lambda v: json.dumps(v, default=self._json_serializer).encode('utf-8')
                )
                await self.producer.start()
                self.logger.debug(f"Kafka生产者已创建: {self.bootstrap_servers}")
            
            # 创建异步消费者
            if self.consumer is None:  # 避免重复初始化
                self.consumer = aiokafka.AIOKafkaConsumer(
                    self.name,
                    bootstrap_servers=self.bootstrap_servers,
                    group_id=self.consumer_group,
                    auto_offset_reset=self.auto_offset_reset,
                    enable_auto_commit=False,
                    value_deserializer=lambda v: json.loads(v.decode('utf-8'))
                )
                await self.consumer.start()
                self.logger.debug(f"Kafka消费者已创建: {self.bootstrap_servers}, 主题: {self.name}")
            
        except Exception as e:
            self.logger.error(f"设置Kafka队列失败: {str(e)}\n{traceback.format_exc()}")
            # 如果部分初始化成功，确保清理已创建的资源
            await self._cleanup_resources()
            raise
    
    async def _ensure_topic_exists(self, num_partitions=1, replication_factor=1):
        """确保主题存在，如果不存在则创建
        
        Args:
            num_partitions: 分区数量
            replication_factor: 副本因子
        
        Returns:
            bool: 主题是否存在或创建成功
        """
        import aiokafka
        from aiokafka.admin import AIOKafkaAdminClient, NewTopic
        from aiokafka.errors import UnknownTopicOrPartitionError, TopicAlreadyExistsError
        
        self.logger.info(f"检查主题 {self.name} 是否存在")
        
        # 创建管理客户端
        admin_client = AIOKafkaAdminClient(
            bootstrap_servers=self.bootstrap_servers,
            client_id=f"admin-{self.name}"
        )
        
        try:
            await admin_client.start()
            
            # 检查主题是否存在
            topic_exists = False
            try:
                topics_metadata = await admin_client.describe_topics([self.name])
                if self.name in topics_metadata:
                    self.logger.info(f"主题 {self.name} 已存在")
                    topic_exists = True
            except UnknownTopicOrPartitionError:
                self.logger.warning(f"主题 {self.name} 不存在，将自动创建")
                topic_exists = False
            
            # 如果主题不存在，创建新主题
            if not topic_exists:
                self.logger.info(f"创建主题 {self.name}，分区数: {num_partitions}，副本因子: {replication_factor}")
                new_topic = NewTopic(
                    name=self.name,
                    num_partitions=num_partitions,
                    replication_factor=replication_factor
                )
                
                try:
                    await admin_client.create_topics([new_topic])
                    self.logger.info(f"主题 {self.name} 创建成功")
                    return True
                except TopicAlreadyExistsError:
                    # 如果主题已经存在（并发创建），忽略错误
                    self.logger.info(f"主题 {self.name} 已被其他进程创建")
                    return True
                except Exception as e:
                    self.logger.error(f"创建主题 {self.name} 失败: {str(e)}")
                    # 这里不抛出异常，让上层代码继续尝试使用已有主题
                    return False
            
            return True
        except Exception as e:
            self.logger.error(f"检查或创建主题时出错: {str(e)}")
            return False
        finally:
            await admin_client.close()
    
    async def _cleanup_resources(self):
        """清理资源，确保所有连接正确关闭"""
        if self._closing:
            self.logger.debug("资源清理已在进行中，跳过")
            return
        
        self._closing = True
        self.logger.info(f"开始清理 {self.name} 的Kafka资源")
        
        # 关闭生产者
        if self.producer:
            try:
                self.logger.info("正在停止Kafka生产者...")
                # 使用shield避免被取消
                await asyncio.shield(self.producer.stop())
                self.logger.info("Kafka生产者已关闭")
            except Exception as e:
                self.logger.error(f"关闭Kafka生产者时出错: {str(e)}")
            finally:
                # 无论如何都将producer设为None，防止再次使用已关闭的连接
                self.producer = None
        
        # 关闭消费者
        if self.consumer:
            try:
                self.logger.info("正在停止Kafka消费者...")
                # 使用shield避免被取消
                await asyncio.shield(self.consumer.stop())
                self.logger.info("Kafka消费者已关闭")
            except Exception as e:
                self.logger.error(f"关闭Kafka消费者时出错: {str(e)}")
            finally:
                # 无论如何都将consumer设为None，防止再次使用已关闭的连接
                self.consumer = None
        
        self._closing = False
        self.logger.info(f"{self.name} 的Kafka资源清理完成")
    
    async def close(self):
        """关闭队列，释放Kafka连接"""
        self.logger.info(f"关闭 {self.name} 队列")
        await self._cleanup_resources()
        self.logger.info(f"{self.name} 队列已关闭")
    
    async def send(self, message: Message) -> bool:
        """发送消息到Kafka主题
        
        Args:
            message: 要发送的消息
            
        Returns:
            bool: 是否发送成功
        """
        if not self.producer:
            await self.setup()
        
        try:
            # 将消息转换为字典
            message_dict = message.to_dict()
            
            # 异步发送消息
            await self.producer.send_and_wait(self.name, message_dict)
            self.logger.debug(f"消息已发送到主题 {self.name}: {message.id}")
            
            return True
        except Exception as e:
            self.logger.error(f"发送消息到主题 {self.name} 失败: {str(e)}")
            return False
    
    async def pull(self, max_count: int = 10, timeout: float = 1.0) -> List[Message]:
        """从队列中获取消息
        
        Args:
            max_count: 最大消息数量
            timeout: 等待超时时间（秒）
            
        Returns:
            List[Message]: 消息列表
        """
        try:
            # 确保消费者已初始化
            if not self.consumer:
                await self.setup()
                
            # 再次检查消费者是否初始化成功
            if not self.consumer:
                self.logger.error("无法初始化Kafka消费者")
                return []
            
            # 确保分区已分配
            await self._ensure_partition_assignment()
            
        except Exception as e:
            self.logger.error(f"设置Kafka消费者失败: {str(e)}")
            return []
        
        messages = []
        start_time = time.time()
        
        try:
            # 批量获取消息，直到达到最大数量或超时
            while len(messages) < max_count and time.time() - start_time < timeout:
                try:
                    # 确保消费者仍然有效
                    if not self.consumer:
                        self.logger.error("Kafka消费者已失效")
                        break
                        
                    # 检查消费者是否已关闭
                    if getattr(self.consumer, '_closed', False):
                        self.logger.error("Kafka消费者已关闭")
                        self.consumer = None
                        break
                        
                    # 检查是否有分区分配
                    if not self.consumer.assignment():
                        self.logger.warning("消费者没有分配的分区，无法获取消息")
                        await self._ensure_partition_assignment()
                        if not self.consumer.assignment():
                            self.logger.error("尝试分配分区后，仍无分区可用")
                            break
                        
                    # 设置较短的超时时间，以便能够及时响应max_count和timeout限制
                    # 使用一个较小的超时时间，避免长时间阻塞
                    poll_timeout = min(0.1, timeout - (time.time() - start_time))
                    if poll_timeout <= 0:
                        break
                    
                    # 获取消息
                    batch = await self.consumer.getmany(timeout_ms=int(poll_timeout * 1000), max_records=max_count - len(messages))
                    
                    # 记录获取到的消息数量
                    batch_count = sum(len(records) for records in batch.values())
                    self.logger.debug(f"从Kafka获取到 {batch_count} 条消息")
                    
                    # 没有获取到消息，等待一小段时间再重试
                    if batch_count == 0:
                        # 但不要等待太久，避免超过总的超时时间
                        short_wait = min(0.05, timeout - (time.time() - start_time))
                        if short_wait > 0:
                            await asyncio.sleep(short_wait)
                        continue
                    
                    # 处理获取到的消息
                    for tp, records in batch.items():
                        for record in records:
                            # 创建Message对象
                            message = Message(
                                data=record.value.get("data", {}),
                                metadata={
                                    "topic": record.topic,
                                    "partition": record.partition,
                                    "message_id": record.offset,
                                    "timestamp": record.timestamp if hasattr(record, "timestamp") else None
                                }
                            )
                            # 手动设置id属性
                            message.id = str(record.offset)
                            messages.append(message)
                            
                            # 如果已达到最大数量，提前结束
                            if len(messages) >= max_count:
                                break
                except asyncio.TimeoutError:
                    # 超时是正常的，继续尝试直到总超时
                    continue
                except Exception as e:
                    # 捕获其他异常并记录，但继续尝试
                    self.logger.error(f"从Kafka获取消息时发生错误: {str(e)}")
                    # 如果消费者出错，可能需要重新创建
                    if "check_errors" in str(e) or "NoneType" in str(e):
                        self.logger.warning("消费者可能已失效，尝试重新创建")
                        self.consumer = None
                        try:
                            await self.setup()
                            if not self.consumer:
                                break
                        except Exception as setup_err:
                            self.logger.error(f"重新创建消费者失败: {str(setup_err)}")
                            break
                    # 短暂等待后继续尝试
                    await asyncio.sleep(0.1)
                    continue
            
            self.logger.info(f"从Kafka获取到 {len(messages)} 条消息，主题: {self.name}")
            return messages
            
        except Exception as e:
            self.logger.error(f"从Kafka获取消息失败: {str(e)}")
            return []
    
    async def ack(self, message_id: str) -> bool:
        """确认消息已处理
        
        Args:
            message_id: 消息ID（在Kafka中，我们使用offset作为消息ID）
            
        Returns:
            bool: 是否确认成功
        """
        if not self.consumer:
            await self.setup()
        
        try:
            # 确保message_id是整数
            try:
                offset = int(message_id)
            except (TypeError, ValueError):
                self.logger.error(f"消息ID必须是整数偏移量，收到的是: {message_id} ({type(message_id)})")
                return False
            
            # 获取当前分配的分区
            partitions = self.consumer.assignment()
            if not partitions:
                self.logger.warning("消费者没有分配的分区，无法提交特定偏移量")
                # 提交所有已处理的偏移量
                await self.consumer.commit()
                return True
            
            # 为每个分区提交相同的偏移量
            # 注意：这是一个简化实现，假设所有分区的偏移量都是同步的
            # 在实际应用中，应该为每个分区单独跟踪偏移量
            offsets = {}
            for tp in partitions:
                # 提交的是下一条要消费的消息的偏移量（当前偏移量+1）
                offsets[tp] = offset + 1
            
            # 提交偏移量
            await self.consumer.commit(offsets)
            self.logger.info(f"已提交偏移量: {offset + 1}")
            
            return True
        except Exception as e:
            self.logger.error(f"确认消息 {message_id} 失败: {str(e)}")
            return False
    
    async def get_pending_count(self) -> int:
        """获取队列中待处理消息数量
        
        Returns:
            int: 待处理消息数量
        """
        if not self.consumer:
            await self.setup()
            
        try:
            # 获取当前分配的分区
            partitions = self.consumer.assignment()
            if not partitions:
                self.logger.warning("消费者没有分配的分区，无法获取待处理消息数量")
                return 0
            
            # 获取每个分区的最新偏移量（end offsets）
            end_offsets = await self.consumer.end_offsets(partitions)
            
            # 获取每个分区的已提交偏移量
            committed_offsets = {}
            for tp in partitions:
                committed = await self.consumer.committed(tp)
                committed_offsets[tp] = committed if committed is not None else 0
            
            # 计算待处理消息总数
            total_pending = 0
            for tp in partitions:
                latest_offset = end_offsets.get(tp, 0)
                committed_offset = committed_offsets.get(tp, 0)
                partition_pending = latest_offset - committed_offset
                
                self.logger.debug(f"分区 {tp.topic}-{tp.partition}: 最新偏移量={latest_offset}, 已提交偏移量={committed_offset}, 待处理消息数={partition_pending}")
                total_pending += partition_pending
            
            self.logger.info(f"主题 {self.name} 的总待处理消息数: {total_pending}")
            return total_pending
            
        except Exception as e:
            self.logger.error(f"获取待处理消息数量失败: {str(e)}\n{traceback.format_exc()}")
            return 0
    
    async def get_last_offset(self) -> int:
        """获取当前消费者组已提交的最新偏移量
        
        Returns:
            int: 最新提交的偏移量，如果无法获取则返回-1
        """
        if not self.consumer:
            await self.setup()
            
        try:
            # 等待分区分配
            await self._ensure_partition_assignment()
            
            # 获取当前分配的分区
            partitions = self.consumer.assignment()
            if not partitions:
                self.logger.warning("消费者没有分配的分区，尝试从管理客户端获取偏移量")
                # 使用AdminClient方式获取偏移量
                return await self._get_offset_from_admin()
            
            # 获取每个分区的已提交偏移量
            committed_offsets = {}
            for tp in partitions:
                committed = await self.consumer.committed(tp)
                committed_offsets[tp] = committed if committed is not None else 0
            
            # 返回最大的已提交偏移量
            if committed_offsets:
                max_offset = max(committed_offsets.values())
                self.logger.info(f"主题 {self.name} 的最新提交偏移量: {max_offset}")
                return max_offset
            else:
                return -1
            
        except Exception as e:
            self.logger.error(f"获取已提交偏移量失败: {str(e)}\n{traceback.format_exc()}")
            return -1
    
    async def _ensure_partition_assignment(self):
        """确保消费者已被分配分区
        
        尝试等待分区分配完成，如果超时则尝试手动分配分区
        """
        max_retries = 5
        retry_interval = 2.0
        
        # 检查是否已有分区分配
        if self.consumer and self.consumer.assignment():
            return True
        
        # 等待自动分区分配
        self.logger.info(f"等待Kafka为消费者分配分区...")
        for attempt in range(max_retries):
            if self.consumer and self.consumer.assignment():
                self.logger.info(f"消费者已被分配分区: {self.consumer.assignment()}")
                return True
            
            self.logger.warning(f"等待分区分配尝试 {attempt+1}/{max_retries}")
            await asyncio.sleep(retry_interval)
        
        # 如果自动分配失败，尝试手动分配
        self.logger.warning("自动分区分配超时，尝试手动获取分区信息")
        
        try:
            return await self._manually_assign_partitions()
        except Exception as e:
            self.logger.error(f"手动分配分区失败: {str(e)}")
            return False
    
    async def _manually_assign_partitions(self):
        """手动为消费者分配分区"""
        try:
            from aiokafka.admin import AIOKafkaAdminClient
            from aiokafka.structs import TopicPartition
            
            # 创建管理客户端
            admin = AIOKafkaAdminClient(bootstrap_servers=self.bootstrap_servers)
            await admin.start()
            
            try:
                # 获取主题的分区信息
                topics = await admin.describe_topics([self.name])
                if not topics or self.name not in topics:
                    self.logger.error(f"无法获取主题 {self.name} 的信息")
                    return False
                
                # 获取分区列表
                topic_info = topics[self.name]
                partitions = [TopicPartition(self.name, p.id) for p in topic_info.partitions]
                
                if not partitions:
                    self.logger.error(f"主题 {self.name} 没有分区")
                    return False
                
                # 手动分配分区
                self.logger.info(f"手动分配分区: {partitions}")
                await self.consumer.assign(partitions)
                
                # 确认分配成功
                if self.consumer.assignment():
                    self.logger.info(f"手动分区分配成功: {self.consumer.assignment()}")
                    return True
                else:
                    self.logger.error("手动分区分配后，仍无分配的分区")
                    return False
                
            finally:
                await admin.close()
            
        except Exception as e:
            self.logger.error(f"手动分配分区异常: {str(e)}")
            raise
    
    async def _get_offset_from_admin(self) -> int:
        """使用AdminClient获取偏移量信息"""
        try:
            from aiokafka.admin import AIOKafkaAdminClient
            from aiokafka.structs import TopicPartition
            
            # 创建管理客户端
            admin = AIOKafkaAdminClient(bootstrap_servers=self.bootstrap_servers)
            await admin.start()
            
            try:
                # 获取主题的分区信息
                topics = await admin.describe_topics([self.name])
                if not topics or self.name not in topics:
                    self.logger.error(f"无法获取主题 {self.name} 的信息")
                    return -1
                
                # 获取分区列表
                topic_info = topics[self.name]
                partitions = [TopicPartition(self.name, p.id) for p in topic_info.partitions]
                
                if not partitions:
                    self.logger.error(f"主题 {self.name} 没有分区")
                    return -1
                
                # 获取消费者组信息
                group_offsets = await admin.list_consumer_group_offsets(
                    self.consumer_group,
                    partitions=partitions
                )
                
                # 返回最大的已提交偏移量
                if group_offsets:
                    offsets = [offset_meta.offset for tp, offset_meta in group_offsets.items() if offset_meta]
                    if offsets:
                        max_offset = max(offsets)
                        self.logger.info(f"使用AdminClient获取的最大偏移量: {max_offset}")
                        return max_offset
                
                self.logger.warning(f"无法从AdminClient获取偏移量，使用默认值")
                return -1
                
            finally:
                await admin.close()
            
        except Exception as e:
            self.logger.error(f"使用AdminClient获取偏移量异常: {str(e)}")
            return -1
    
    def _json_serializer(self, obj):
        """自定义JSON序列化器，处理特殊类型
        
        Args:
            obj: 要序列化的对象
            
        Returns:
            序列化后的对象
            
        Raises:
            TypeError: 无法序列化的类型
        """
        # 处理PydanticObjectId
        try:
            from beanie import PydanticObjectId
            if isinstance(obj, PydanticObjectId):
                return str(obj)
        except ImportError:
            pass
        
        if isinstance(obj, datetime):
            return obj.isoformat()
        if hasattr(obj, 'dict') and callable(getattr(obj, 'dict')):
            return obj.dict()
        if hasattr(obj, '__dict__'):
            return obj.__dict__
        raise TypeError(f"无法序列化类型 {type(obj)}")

    def _find_message_by_id(self, message_id: str, messages: List[Message]) -> Optional[Message]:
        """根据消息ID查找对应的消息对象
        
        Args:
            message_id: 消息ID
            messages: 消息列表
            
        Returns:
            Optional[Message]: 找到的消息对象，如果未找到则返回None
        """
        for message in messages:
            if message.id == message_id:
                return message
        return None

    # 提供receive方法的别名，为向后兼容
    async def receive(self, count: int = 1, timeout: int = 0) -> List[Message]:
        """从Kafka主题接收消息（兼容方法，调用pull）
        
        Args:
            count: 要接收的消息数量
            timeout: 等待超时时间（毫秒），0表示不等待
            
        Returns:
            List[Message]: 接收到的消息列表
        """
        # 转换参数并调用新的pull方法
        timeout_sec = timeout / 1000 if timeout > 0 else 1.0
        return await self.pull(max_count=count, timeout=timeout_sec)

class KafkaOffsetFlowController(FlowController):
    """基于Kafka消费者偏移量的流量控制器
    
    通过监控消费者提交的偏移量与生产者最新偏移量之间的差距（消费滞后量）来控制生产速率
    """
    
    def __init__(self, queue: KafkaQueue, max_lag: int = 1000, 
                 check_interval: float = 30.0, enable_flow_control: bool = True,
                 topic_partitions: List = None, startup_grace_period: float = 120.0):
        """初始化基于Kafka偏移量的流量控制器
        
        Args:
            queue: 要控制的Kafka消息队列
            max_lag: 最大允许的消费滞后量（消息数）
            check_interval: 检查间隔（秒）
            enable_flow_control: 是否启用流量控制
            topic_partitions: 要监控的主题分区列表，如果为None则监控所有分区
            startup_grace_period: 系统启动后的宽限期(秒)，在此期间不进行流量控制
        """
        super().__init__(queue, max_lag, check_interval, enable_flow_control)
        
        # 确保队列是KafkaQueue类型
        if not isinstance(queue, KafkaQueue):
            raise TypeError("KafkaOffsetFlowController只能用于KafkaQueue")
        
        self.max_lag = max_lag
        self.topic_partitions = topic_partitions
        self.logger = logging.getLogger(f"KafkaOffsetFlowController.{queue.name}")
        
        # 添加启动时间和宽限期
        self.startup_time = time.time()
        self.startup_grace_period = startup_grace_period
    
    async def should_proceed(self) -> bool:
        """检查是否应该继续处理，基于消费者偏移量与生产者最新偏移量的差距
        
        Returns:
            bool: 如果应该继续处理则返回True，否则返回False
        """
        if not self.enable_flow_control or not self.queue:
            return True
            
        # 检查是否在启动宽限期内
        if time.time() - self.startup_time < self.startup_grace_period:
            self.logger.info(f"流量控制: 处于启动宽限期内(剩余{self.startup_grace_period - (time.time() - self.startup_time):.1f}秒)，允许处理")
            return True
        
        now = time.time()
        if now - self.last_check_time >= self.check_interval:
            self.last_check_time = now
            
            # 获取消费滞后量
            lag = await self._get_consumer_lag()
            
            # 如果滞后量超过阈值，则暂时不继续处理
            if lag >= self.max_lag:
                self.logger.warning(f"流量控制: 主题 {self.queue.name} 的消费滞后量为 {lag}，超过阈值 {self.max_lag}，暂停处理")
                return False
            
            self.logger.info(f"流量控制: 主题 {self.queue.name} 的消费滞后量为 {lag}，低于阈值 {self.max_lag}，继续处理")
        
        return True
    
    async def check_immediate(self) -> bool:
        """立即检查消费滞后量，不考虑时间间隔
        
        Returns:
            bool: 如果应该继续处理则返回True，否则返回False
        """
        if not self.enable_flow_control or not self.queue:
            return True
        
        self.last_check_time = time.time()
        
        # 获取消费滞后量
        lag = await self._get_consumer_lag()
        
        # 如果滞后量超过阈值，则暂时不继续处理
        if lag >= self.max_lag:
            self.logger.info(f"流量控制(立即检查): 主题 {self.queue.name} 的消费滞后量为 {lag}，超过阈值 {self.max_lag}，暂停处理")
            return False
        
        self.logger.info(f"流量控制(立即检查): 主题 {self.queue.name} 的消费滞后量为 {lag}，低于阈值 {self.max_lag}，继续处理")
        return True
    
    async def _get_consumer_lag(self) -> int:
        """获取消费者的滞后量（消费者提交的偏移量与生产者最新偏移量之间的差距）
        
        Returns:
            int: 消费滞后量（消息数）
        """
        try:
            # 确保消费者已初始化
            if not self.queue.consumer:
                await self.queue.setup()
            
            consumer = self.queue.consumer
            
            # 获取要监控的主题分区
            if self.topic_partitions is None:
                # 使用当前分配的分区
                partitions = consumer.assignment()
                
                # 如果没有分配分区，返回0
                if not partitions:
                    self.logger.warning(f"无法获取主题 {self.queue.name} 的分区信息")
                    return 0
            else:
                partitions = self.topic_partitions
            
            # 获取最新偏移量
            end_offsets = await consumer.end_offsets(partitions)
            
            # 获取已提交的偏移量
            committed_offsets = {}
            for tp in partitions:
                committed = await consumer.committed(tp)
                # 如果没有提交过偏移量(committed为None)且auto_offset_reset为earliest，
                # 则应该从最早的消息开始消费，此时滞后量应该是所有消息
                # 如果auto_offset_reset为latest，则应该从最新消息开始消费，此时滞后量为0
                if committed is None:
                    if self.queue.auto_offset_reset == "earliest":
                        # 获取最早的偏移量
                        begin_offsets = await consumer.beginning_offsets(partitions)
                        committed_offsets[tp] = begin_offsets.get(tp, 0)
                        self.logger.info(f"分区 {tp.partition} 未提交过偏移量，使用最早偏移量: {committed_offsets[tp]}")
                    else:  # latest
                        committed_offsets[tp] = end_offsets.get(tp, 0)
                        self.logger.info(f"分区 {tp.partition} 未提交过偏移量，使用最新偏移量: {committed_offsets[tp]}")
                else:
                    committed_offsets[tp] = committed
            
            # 计算总滞后量
            total_lag = 0
            for tp in partitions:
                latest_offset = end_offsets.get(tp, 0)
                committed_offset = committed_offsets.get(tp, 0)
                partition_lag = latest_offset - committed_offset
                
                self.logger.debug(f"分区 {tp.partition}: 最新偏移量={latest_offset}, 已提交偏移量={committed_offset}, 滞后量={partition_lag}")
                total_lag += partition_lag
            
            self.logger.info(f"主题 {self.queue.name} 的总消费滞后量: {total_lag}")
            return total_lag
            
        except Exception as e:
            self.logger.error(f"获取消费滞后量失败: {str(e)}\n{traceback.format_exc()}")
            # 出错时返回0，避免错误地阻止处理
            return 0
    
    async def get_partition_lags(self) -> Dict[str, int]:
        """获取每个分区的消费滞后量
        
        Returns:
            Dict[str, int]: 分区ID到滞后量的映射
        """
        try:
            # 确保消费者已初始化
            if not self.queue.consumer:
                await self.queue.setup()
            
            consumer = self.queue.consumer
            
            # 获取要监控的主题分区
            if self.topic_partitions is None:
                partitions = consumer.assignment()
                if not partitions:
                    return {}
            else:
                partitions = self.topic_partitions
            
            # 获取最新偏移量和已提交的偏移量
            end_offsets = await consumer.end_offsets(partitions)
            
            result = {}
            for tp in partitions:
                latest_offset = end_offsets.get(tp, 0)
                committed = await consumer.committed(tp)
                committed_offset = committed if committed is not None else 0
                partition_lag = latest_offset - committed_offset
                
                # 使用分区ID作为键
                result[f"{tp.topic}-{tp.partition}"] = partition_lag
            
            return result
            
        except Exception as e:
            self.logger.error(f"获取分区滞后量失败: {str(e)}")
            return {}

# 工厂函数，用于创建消息队列
def create_message_queue(queue_type: str, name: str, **kwargs) -> MessageQueue:
    """创建消息队列
    
    Args:
        queue_type: 队列类型，如"redis"或"kafka"
        name: 队列名称
        **kwargs: 其他参数
        
    Returns:
        MessageQueue: 创建的消息队列
        
    Raises:
        ValueError: 不支持的队列类型
    """
    if queue_type.lower() == "redis":
        return RedisStreamQueue(name, **kwargs)
    elif queue_type.lower() == "kafka":
        return KafkaQueue(name, **kwargs)
    else:
        raise ValueError(f"不支持的队列类型: {queue_type}")

# 工厂函数，用于创建流量控制器
def create_flow_controller(controller_type: str, queue: MessageQueue, **kwargs) -> FlowController:
    """创建流量控制器
    
    Args:
        controller_type: 控制器类型，如"basic"或"kafka_offset"
        queue: 要控制的消息队列
        **kwargs: 其他参数
        
    Returns:
        FlowController: 创建的流量控制器
        
    Raises:
        ValueError: 不支持的控制器类型或队列类型不匹配
    """
    if controller_type.lower() == "basic":
        return FlowController(queue, **kwargs)
    elif controller_type.lower() == "kafka_offset":
        if not isinstance(queue, KafkaQueue):
            raise ValueError(f"kafka_offset控制器只能用于KafkaQueue，而不是{type(queue).__name__}")
        return KafkaOffsetFlowController(queue, **kwargs)
    else:
        raise ValueError(f"不支持的控制器类型: {controller_type}") 