import logging
import asyncio
from typing import List, Dict, Any, Optional, AsyncGenerator
from datetime import datetime, timedelta
import time
from pymongo import UpdateOne

from dao.kol_wallet_dao import KOLWalletDAO
from dao.kol_wallet_activity_dao import KOLWalletActivityDAO
from dao.token_dao import TokenDAO
from models.kol_wallet import KOLWallet
from models.kol_wallet_activity import KOLWalletActivity
from models.token import Token
from models import init_db

logger = logging.getLogger("BacktestDataLoader")

class DataLoader:
    """数据加载器，负责加载历史数据"""
    
    def __init__(self):
        """初始化数据加载器"""
        logger.info("初始化数据加载器")
        self.initialized = False
        
    async def setup(self):
        """初始化数据库连接和DAO"""
        if not self.initialized:
            logger.info("初始化数据库连接和DAO...")
            self.kol_wallet_dao = KOLWalletDAO()
            self.kol_wallet_activity_dao = KOLWalletActivityDAO()
            self.token_dao = TokenDAO()
            self.initialized = True
            logger.info("数据库初始化完成")
        
    async def load_kol_wallets(self) -> List[KOLWallet]:
        """加载所有KOL钱包
        
        Returns:
            List[KOLWallet]: KOL钱包列表
        """
        await self.setup()
        logger.info("开始加载KOL钱包数据")
        try:
            # 查找所有具有'kol'标签的钱包
            wallets = await self.kol_wallet_dao.find_by_tag("kol")
            logger.info(f"加载了 {len(wallets)} 个KOL钱包信息")
            
            # 如果没有找到带有kol标签的钱包，尝试加载所有钱包
            if not wallets:
                logger.warning("未找到带有'kol'标签的钱包，尝试加载所有钱包")
                wallets = await self.kol_wallet_dao.find_all_wallets()
                logger.info(f"加载了 {len(wallets)} 个钱包信息（未过滤标签）")
                
                # 为所有钱包添加kol标签，确保策略可以正常工作
                for wallet in wallets:
                    if not wallet.tags:
                        wallet.tags = ["kol"]
                    elif "kol" not in wallet.tags:
                        wallet.tags.append("kol")
                
            return wallets
        except Exception as e:
            logger.error(f"加载KOL钱包信息时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return []
    
    async def load_kol_activities(self, 
                                  start_time: int, 
                                  end_time: int, 
                                  event_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """加载指定时间范围内的KOL活动
        
        Args:
            start_time: 开始时间戳
            end_time: 结束时间戳
            event_type: 事件类型，如 'buy' 或 'sell'，为None则加载所有类型
            
        Returns:
            List[Dict[str, Any]]: KOL活动列表
        """
        await self.setup()
        logger.info(f"开始从数据库加载KOL活动记录，时间范围: {datetime.fromtimestamp(start_time)} 到 {datetime.fromtimestamp(end_time)}")
        
        try:
            # 构建查询条件
            query = {
                "timestamp": {"$gte": start_time, "$lte": end_time}
            }
            
            # 如果需要过滤事件类型
            if event_type:
                query["event_type"] = event_type
            
            # 获取总记录数（使用聚合方式进行计数，效率更高）
            count_pipeline = [
                {"$match": query},
                {"$count": "total"}
            ]
            count_result = await self.kol_wallet_activity_dao.aggregate(count_pipeline)
            total_count = count_result[0]["total"] if count_result else 0
            logger.info(f"匹配查询条件的总记录数: {total_count}")
            
            if total_count == 0:
                logger.warning("没有找到匹配的记录")
                return []
            
            # 使用DAO直接查询活动数据
            activities = await self.kol_wallet_activity_dao.find_many(query, limit=total_count)
            
            # 将模型转换为字典
            activities_dict = [activity.dict() for activity in activities]
            
            logger.info(f"加载了 {len(activities_dict)} 条KOL活动记录")
            
            # 如果没有获取到记录，尝试简单查询检查数据库连接
            if len(activities_dict) == 0:
                try:
                    # 获取一条记录看看集合中是否真的有数据
                    sample = await self.kol_wallet_activity_dao.find_one({})
                    if sample:
                        logger.info(f"示例记录: {sample.dict()}")
                    else:
                        logger.warning("集合是空的")
                except Exception as e:
                    logger.error(f"检查数据库时出错: {str(e)}")
            
            # 处理cost_usd字段，确保是浮点数
            for activity in activities_dict:
                if 'cost_usd' in activity and not isinstance(activity['cost_usd'], float):
                    try:
                        activity['cost_usd'] = float(activity['cost_usd'])
                    except (ValueError, TypeError):
                        activity['cost_usd'] = 0.0
                        
                # 确保wallet字段存在
                if 'wallet' not in activity and 'wallet_address' in activity:
                    activity['wallet'] = activity['wallet_address']
            
            return activities_dict
        
        except Exception as e:
            logger.error(f"加载KOL活动记录时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return []
    
    async def load_tokens(self, addresses: List[str]) -> List[Dict[str, Any]]:
        """根据地址列表加载代币信息
        
        Args:
            addresses: 代币地址列表
            
        Returns:
            List[Dict[str, Any]]: 代币信息列表
        """
        await self.setup()
        logger.info(f"开始从数据库加载代币信息")
        
        try:
            if not addresses:
                logger.warning("代币地址列表为空")
                return []
                
            # 使用DAO加载代币信息
            tokens = await self.token_dao.find_by_addresses(addresses)
            
            logger.info(f"加载了 {len(tokens)} 个代币信息")
            
            # 如果没有获取到代币，尝试直接查询所有代币
            if len(tokens) == 0 and len(addresses) > 0:
                logger.warning(f"未找到指定地址的代币，尝试加载所有代币")
                
                # 创建一个聚合管道获取有限数量的代币
                tokens = await self.token_dao.find_many({}, limit=1000)
                logger.info(f"加载了 {len(tokens)} 个代币信息（未过滤地址）")
                
                if len(tokens) > 0:
                    logger.info(f"第一条代币记录示例: {tokens[0].dict()}")
            
            # 将模型对象转换为字典
            return [token.dict() for token in tokens]
        except Exception as e:
            logger.error(f"加载代币信息时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return []
    
    async def load_kol_activities_by_window(self,
                                           time_slice: Dict[str, int],
                                           lookback_hours: int = 48,
                                           event_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """按时间窗口加载KOL活动数据
        
        Args:
            time_slice: 包含当前时间的时间片，格式为 {'current_time': int}
            lookback_hours: 向前查找的小时数，基于配置的transaction_lookback_hours
            event_type: 事件类型过滤
            
        Returns:
            List[Dict[str, Any]]: 该时间窗口内的KOL活动
        """
        current_time = time_slice['current_time']
        lookback_start = current_time - (lookback_hours * 3600)
        
        logger.info(f"按时间窗口加载KOL活动: {datetime.fromtimestamp(lookback_start)} 到 {datetime.fromtimestamp(current_time)}")
        logger.info(f"窗口大小: {lookback_hours} 小时")
        
        return await self.load_kol_activities(lookback_start, current_time, event_type) 
        
    async def stream_time_slices(self, 
                                start_time: int, 
                                end_time: int, 
                                slice_hours: int = 24):
        """流式生成时间片
        
        Args:
            start_time: 开始时间戳
            end_time: 结束时间戳
            slice_hours: 每个时间片的小时数
            
        Yields:
            Dict[str, int]: 时间片，包含 current_time 和 lookback_start_time
        """
        slice_seconds = slice_hours * 3600
        current = start_time
        total_slices = (end_time - start_time) // slice_seconds + 1
        count = 0
        
        while current <= end_time:
            time_slice = {
                'current_time': current,
                'lookback_start_time': current - slice_seconds
            }
            count += 1
            logger.info(f"生成时间片 {count}/{total_slices}: {datetime.fromtimestamp(current)}")
            yield time_slice
            current += slice_seconds
            
    async def stream_kol_activities(self, 
                                  start_time: int, 
                                  end_time: int, 
                                  batch_size: int = 100, 
                                  event_type: Optional[str] = None):
        """流式加载KOL活动数据
        
        Args:
            start_time: 开始时间戳
            end_time: 结束时间戳
            batch_size: 每批次记录数
            event_type: 事件类型过滤
            
        Yields:
            List[Dict[str, Any]]: 一批KOL活动数据
        """
        await self.setup()
        
        # 构建查询条件
        query = {
            "timestamp": {"$gte": start_time, "$lte": end_time}
        }
        
        if event_type:
            query["event_type"] = event_type
            
        # 获取总记录数
        count_pipeline = [
            {"$match": query},
            {"$count": "total"}
        ]
        count_result = await self.kol_wallet_activity_dao.aggregate(count_pipeline)
        total_count = count_result[0]["total"] if count_result else 0
        logger.info(f"流式加载KOL活动数据，共 {total_count} 条记录")
        
        if total_count == 0:
            logger.warning("没有找到匹配的记录")
            return
            
        # 批量获取数据
        skip = 0
        while skip < total_count:
            activities = await self.kol_wallet_activity_dao.find_many(
                query, 
                skip=skip, 
                limit=batch_size,
                sort=[("timestamp", 1)]  # 按时间戳升序排序
            )
            
            if not activities:
                break
                
            # 转换为字典并处理字段
            activities_dict = []
            for activity in activities:
                activity_dict = activity.dict()
                
                # 处理cost_usd字段
                if 'cost_usd' in activity_dict and not isinstance(activity_dict['cost_usd'], float):
                    try:
                        activity_dict['cost_usd'] = float(activity_dict['cost_usd'])
                    except (ValueError, TypeError):
                        activity_dict['cost_usd'] = 0.0
                        
                # 确保wallet字段存在
                if 'wallet' not in activity_dict and 'wallet_address' in activity_dict:
                    activity_dict['wallet'] = activity_dict['wallet_address']
                    
                activities_dict.append(activity_dict)
            
            logger.info(f"流式处理批次数据: {len(activities_dict)} 条记录")
            yield activities_dict
            
            skip += batch_size
            
    async def stream_activities_by_window(self,
                                        time_slice: Dict[str, int],
                                        lookback_hours: int = 48,
                                        batch_size: int = 100,
                                        event_type: Optional[str] = None):
        """按时间窗口流式加载KOL活动数据
        
        Args:
            time_slice: 包含当前时间的时间片
            lookback_hours: 向前查找的小时数
            batch_size: 每批次记录数
            event_type: 事件类型过滤
            
        Yields:
            List[Dict[str, Any]]: 一批KOL活动数据
        """
        current_time = time_slice['current_time']
        lookback_start = current_time - (lookback_hours * 3600)
        
        logger.info(f"按时间窗口流式加载KOL活动: {datetime.fromtimestamp(lookback_start)} 到 {datetime.fromtimestamp(current_time)}")
        logger.info(f"窗口大小: {lookback_hours} 小时")
        
        async for batch in self.stream_kol_activities(lookback_start, current_time, batch_size, event_type):
            yield batch
            
    async def get_time_slices(self, 
                             start_time: int, 
                             end_time: int, 
                             slice_hours: int = 24) -> List[Dict[str, int]]:
        """将时间范围切分为多个时间片，用于模拟策略在不同时间点的运行
        
        Args:
            start_time: 开始时间戳
            end_time: 结束时间戳
            slice_hours: 每个时间片的小时数
            
        Returns:
            List[Dict[str, int]]: 时间片列表，每个时间片包含 current_time 和 lookback_start_time
        """
        slices = []
        slice_seconds = slice_hours * 3600
        
        current = start_time
        while current <= end_time:
            slices.append({
                'current_time': current,
                'lookback_start_time': current - slice_seconds
            })
            current += slice_seconds
        
        logger.info(f"生成了 {len(slices)} 个时间片")
        return slices 