"""重试决策引擎 - 分离重试决策和滑点调整决策"""

from typing import Optional, TYPE_CHECKING, Dict, Any
import logging
from models.slippage_retry import RetryDecision, SlippageRetryConfig

if TYPE_CHECKING:
    from utils.trading.solana.trade_interface import TradeInterface

logger = logging.getLogger(__name__)


class RetryDecisionEngine:
    """重试决策引擎 - 负责决定是否继续重试和是否调整滑点"""
    
    def __init__(self, trade_interface: Optional['TradeInterface'] = None):
        """
        初始化重试决策引擎
        
        Args:
            trade_interface: 交易接口实例，用于专业的错误识别
        """
        self.trade_interface = trade_interface
    
    def should_continue_retry(
        self,
        retry_count: int,
        max_retries: int,
        error_message: Optional[str] = None,
        provider_response: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        判断是否应该继续重试（独立于滑点调整）
        
        Args:
            retry_count: 当前重试次数
            max_retries: 最大重试次数
            error_message: 错误信息
            provider_response: 交易接口的原始响应（可选）
            
        Returns:
            bool: 是否应该继续重试
        """
        # 检查重试次数限制
        if retry_count >= max_retries:
            logger.debug(f"已达到最大重试次数 {max_retries}，停止重试")
            return False
        
        # 检查是否为不可重试的错误
        if self._is_non_retryable_error(error_message, provider_response):
            logger.info(f"检测到不可重试错误，停止重试: {error_message}")
            return False
        
        logger.debug(f"继续重试: 当前次数 {retry_count}/{max_retries}")
        return True
    
    def should_increase_slippage(
        self,
        error_message: Optional[str],
        config: SlippageRetryConfig,
        current_slippage: float,
        provider_response: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        判断是否应该增加滑点（独立于重试决策）
        
        Args:
            error_message: 错误信息
            config: 滑点重试配置
            current_slippage: 当前滑点百分比
            provider_response: 交易接口的原始响应（可选）
            
        Returns:
            bool: 是否应该增加滑点
        """
        # 检查滑点递增功能是否启用
        if not config.enabled:
            logger.debug("滑点递增功能未启用")
            return False
        
        # 检查是否为滑点相关错误
        if not self._is_slippage_related_error(error_message, provider_response):
            logger.debug(f"错误与滑点无关，不调整滑点: {error_message}")
            return False
        
        # 检查是否已达到滑点上限
        if current_slippage >= config.max_slippage_percentage:
            logger.warning(f"当前滑点 {current_slippage}% 已达到上限 {config.max_slippage_percentage}%，无法继续增加")
            return False
        
        logger.info(f"检测到滑点相关错误，将增加滑点: {error_message}")
        return True
    
    def make_retry_decision(
        self,
        retry_count: int,
        max_retries: int,
        current_slippage: float,
        config: SlippageRetryConfig,
        error_message: str,
        provider_response: dict = None
    ) -> RetryDecision:
        """
        根据当前重试次数、滑点和错误信息，决定是否继续重试以及是否调整滑点。
        
        Args:
            retry_count: 当前重试次数
            max_retries: 最大重试次数
            current_slippage: 当前滑点百分比
            config: 滑点重试配置
            error_message: 错误信息
            provider_response: 交易接口的原始响应（可选）
            
        Returns:
            RetryDecision: 重试决策结果
        """
        # 1. 判断是否继续重试
        should_retry = self.should_continue_retry(retry_count, max_retries, error_message, provider_response)
        
        # 2. 判断是否调整滑点（只有在继续重试时才有意义）
        should_adjust_slippage = False
        if should_retry:
            should_adjust_slippage = self.should_increase_slippage(
                error_message, config, current_slippage, provider_response
            )
        
        # 3. 生成决策原因
        decision_reason = self._generate_decision_reason(
            should_retry, should_adjust_slippage, retry_count, max_retries,
            current_slippage, config, error_message, provider_response
        )
        
        # 4. 创建决策结果
        return RetryDecision(
            should_retry=should_retry,
            should_adjust_slippage=should_adjust_slippage,
            retry_count=retry_count,
            max_retries=max_retries,
            current_slippage=current_slippage,
            max_slippage=config.max_slippage_percentage,
            is_slippage_related_error=self._is_slippage_related_error(error_message, provider_response),
            error_message=error_message,
            slippage_retry_enabled=config.enabled,
            decision_reason=decision_reason
        )
    
    def _is_slippage_related_error(
        self, 
        error_message: Optional[str], 
        provider_response: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        判断错误是否与滑点相关 - 优先使用交易接口的专业识别
        
        Args:
            error_message: 错误信息
            provider_response: 交易接口的原始响应（可选）
            
        Returns:
            bool: 是否为滑点相关错误
        """
        # 优先使用交易接口的专业识别
        if self.trade_interface:
            try:
                is_slippage_error = self.trade_interface.is_slippage_related_error(
                    error_message, provider_response
                )
                logger.debug(f"使用交易接口专业识别滑点错误: {is_slippage_error}")
                return is_slippage_error
            except Exception as e:
                logger.warning(f"交易接口滑点错误识别失败，回退到通用识别: {e}")
        
        # 回退到通用关键词匹配
        if not error_message:
            return False
        
        error_lower = error_message.lower()
        
        # 通用滑点相关关键词
        slippage_keywords = [
            "slippage",
            "price impact",
            "insufficient output amount",
            "would result in",
            "minimum received",
            "exceeds maximum slippage",
            "price moved too much"
        ]
        
        for keyword in slippage_keywords:
            if keyword in error_lower:
                logger.debug(f"通用识别检测到滑点相关错误关键词: '{keyword}' in '{error_message}'")
                return True
        
        logger.debug(f"未检测到滑点相关错误: {error_message}")
        return False
    
    def _is_non_retryable_error(
        self, 
        error_message: Optional[str], 
        provider_response: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        判断错误是否为不可重试错误 - 优先使用交易接口的专业识别
        
        Args:
            error_message: 错误信息
            provider_response: 交易接口的原始响应（可选）
            
        Returns:
            bool: 是否为不可重试错误
        """
        # 优先使用交易接口的专业识别
        if self.trade_interface:
            try:
                is_non_retryable = self.trade_interface.is_non_retryable_error(
                    error_message, provider_response
                )
                logger.debug(f"使用交易接口专业识别不可重试错误: {is_non_retryable}")
                return is_non_retryable
            except Exception as e:
                logger.warning(f"交易接口不可重试错误识别失败，回退到通用识别: {e}")
        
        # 回退到通用关键词匹配
        if not error_message:
            return False
        
        error_lower = error_message.lower()
        
        # 通用不可重试的错误关键词
        non_retryable_keywords = [
            "insufficient funds",
            "insufficient balance",
            "token not found",
            "invalid token",
            "unauthorized",
            "forbidden",
            "account not found",
            "invalid private key",
            "signature verification failed"
        ]
        
        for keyword in non_retryable_keywords:
            if keyword in error_lower:
                logger.debug(f"通用识别检测到不可重试错误关键词: '{keyword}' in '{error_message}'")
                return True
        
        logger.debug(f"未检测到不可重试错误: {error_message}")
        return False
    
    def _generate_decision_reason(
        self,
        should_retry: bool,
        should_adjust_slippage: bool,
        retry_count: int,
        max_retries: int,
        current_slippage: float,
        config: SlippageRetryConfig,
        error_message: Optional[str],
        provider_response: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        生成决策原因说明
        
        Args:
            should_retry: 是否继续重试
            should_adjust_slippage: 是否调整滑点
            retry_count: 当前重试次数
            max_retries: 最大重试次数
            current_slippage: 当前滑点
            config: 滑点重试配置
            error_message: 错误信息
            provider_response: 交易接口的原始响应（可选）
            
        Returns:
            str: 决策原因说明
        """
        if not should_retry:
            if retry_count >= max_retries:
                return f"已达到最大重试次数 ({max_retries})"
            elif self._is_non_retryable_error(error_message, provider_response):
                return "检测到不可重试错误类型"
            else:
                return "基于错误类型决定停止重试"
        
        # 继续重试的情况
        reason_parts = [f"继续重试 ({retry_count + 1}/{max_retries})"]
        
        if should_adjust_slippage:
            reason_parts.append(f"检测到滑点相关错误，将从 {current_slippage}% 增加滑点")
        elif not config.enabled:
            reason_parts.append("滑点递增功能未启用")
        elif current_slippage >= config.max_slippage_percentage:
            reason_parts.append(f"滑点已达上限 ({config.max_slippage_percentage}%)")
        elif not self._is_slippage_related_error(error_message, provider_response):
            reason_parts.append("错误与滑点无关")
        
        return "，".join(reason_parts) 