# Bug 修复方案：Jupiter 链上错误 0x1771 (6001) 导致交易失败及下游 "No Available Channels"

## 1. Bug 标识
- **主要 Bug 描述**: Jupiter 交易通道在尝试执行交易时，由于 Solana 链上 Jupiter V6 程序 (`JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4`) 返回自定义错误 `0x1771` (十进制 `6001`)，导致单次交易尝试失败。
- **用户指示**: 此错误应被视为一种可重试的滑点错误。
- **下游效应**: 如果此错误未被按滑点妥善处理，并且没有其他可用通道，系统最终可能报告 "No available channels"。
- **错误信息示例 (核心错误)**: `utils.trading.solana.jupiter_trade_service - ERROR - [TradeRec:...] [Attempt:1] Transaction sign/send failed: SendTransactionPreflightFailureMessage { message: "Transaction simulation failed: Error processing Instruction 6: custom program error: 0x1771", data: RpcSimulateTransactionResult(RpcSimulateTransactionResult { err: Some(InstructionError(6, Custom(6001))) ...`
- **错误信息示例 (下游效应)**: `🚨 Auto-Trade FAILED 🚨 ... Reason: No available channels`

## 2. 报告日期/发现日期
- 2025-05-27 (根据用户报告和日志分析时间)

## 3. 根源分析概要 (RCA)
1.  **直接原因**: 交易在 Solana 链上模拟或执行时，Jupiter V6 程序 (`JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4`) 的第6条指令返回了自定义错误码 `0x1771` (十进制 `6001`)。虽然这通常表示与特定 AMM 池交互时出现问题（例如流动性不足、价格影响过大），但根据用户指示，系统应将其视为一种滑点错误进行处理。
2.  **`JupiterTradeService` 错误处理**:
    *   当前的 `_execute_single_trade_attempt` 方法捕获了此 `RPCException`。
    *   `is_slippage_related_error` 方法当前可能未将 `0x1771` 错误识别为滑点。
3.  **`TradeOrchestrator` 行为**:
    *   如果 `JupiterTradeService` 未将 `0x1771` 报告为滑点错误，`TradeOrchestrator` 将不会应用其滑点重试逻辑（即增加滑点容限再次尝试）。
    *   如果连续的尝试都因此失败，并且没有其他通道可用，`ChannelSelector` 最终可能返回空列表，导致 "No available channels" 错误。
4.  **"No Available Channels"**: 这是最终的表现，当所有尝试过的通道都因各种原因（包括此 `0x1771` 错误未经按滑点逻辑妥善处理）而变得不可用时发生。

## 4. 详细的、已获批准的修复方案

**核心目标**: 将 Jupiter 程序错误 `0x1771` (6001) 明确识别为一种可重试的滑点错误，并确保 `TradeOrchestrator` 能够据此执行滑点重试逻辑。

**具体修改点**:

### 4.1. `utils/trading/solana/jupiter_trade_service.py`

1.  **增强 `JupiterErrorCode` (如果存在或创建它)** (此步骤保持不变):
    *   如果项目中有一个枚举或常量集合用于 Solana/Jupiter 错误码，添加 `JUPITER_CUSTOM_ERROR_6001 = 6001` (或 `0x1771`)。

2.  **修改 `is_slippage_related_error` 方法**:
    *   **关键修改**: 扩展此方法的逻辑，使其能够识别 `RPCException` 中包含的 `InstructionError(6, Custom(6001))` 为滑点相关错误。
    *   当检测到此特定错误时，方法应返回 `True`。

    ```python
    # 在 is_slippage_related_error 方法内
    # ... (现有检查逻辑) ...

    # 新增检查逻辑以识别 0x1771 (6001) 错误
    if isinstance(error, RPCException):
        parsed_error = getattr(error, 'parsed', None)
        if parsed_error and isinstance(parsed_error, dict):
            error_data = parsed_error.get('data', {}).get('err', {})
            if isinstance(error_data, dict) and 'InstructionError' in error_data:
                instruction_error = error_data['InstructionError']
                # instruction_error is typically a tuple like (instruction_index, error_type_dict)
                # For "custom program error: 0x1771", error_type_dict is {'Custom': 6001}
                if isinstance(instruction_error, (list, tuple)) and len(instruction_error) == 2:
                    error_details = instruction_error[1]
                    if isinstance(error_details, dict) and 'Custom' in error_details:
                        custom_error_code = error_details['Custom']
                        if custom_error_code == 6001: # 0x1771
                            logger.info(f"Identified Jupiter program error 0x1771 (6001) as a slippage-related error for retry.")
                            return True
    
    # ... (现有检查逻辑的其余部分) ...
    return False # 如果没有其他条件匹配
    ```

3.  **`_execute_single_trade_attempt` 方法中的日志** (此步骤保持不变，但其意义有所调整):
    *   当捕获到 `0x1771` 时，日志应仍然记录这是一个 Jupiter AMM 程序错误，但由于它现在被视为滑点，后续的流程将由 `TradeOrchestrator` 的滑点重试逻辑主导。

    ```python
    # 在 _execute_single_trade_attempt 的 except RPCException as e: 块内
    # ... (如之前方案中所示的日志记录代码，用于识别和记录 6001 错误) ...
    # parsed_error = getattr(e, 'parsed', None)
    # ... (代码片段以识别 custom_error_code == 6001)
    # if custom_error_code == 6001:
    #     logger.warning(f"[TradeRec:{trade_record_id}] [Attempt:{attempt_number}] Jupiter AMM program error 0x1771 (6001) detected. Will be treated as slippage for retry.")
    # ...
    ```

### 4.2. `utils/trading/trade_orchestrator.py`

1.  **无需显式修改**：只要 `JupiterTradeService.is_slippage_related_error` 正确地将 `0x1771` 报告为滑点错误，`TradeOrchestrator` 现有的 `_execute_on_channel_with_slippage_retry` 方法应该就能按预期工作，即：
    *   增加滑点容限（根据配置）。
    *   使用更新后的参数再次调用 `channel_instance.execute_trade`。

### 4.3. 配置文件 (`config/global_config.json` 或数据库中的配置)

1.  **检查滑点重试配置**: 审阅 `ChannelConfig` 中与 Jupiter 通道相关的滑点重试参数 (例如 `enable_slippage_retry`, `slippage_increment_percentage`, `max_slippage_percentage`, `retry_delay_seconds`)，确保它们设置合理，以应对这种新认定的"滑点"情况。可能需要调整这些值，如果 `0x1771` 错误比传统滑点更难通过简单的滑点增加来解决。

## 5. 测试用例设计

1.  **单元测试 (`test_jupiter_trade_service.py`)**:
    *   **模拟 `0x1771` 错误**: 使得 `solana_client.send_raw_transaction` 抛出包含 `InstructionError(6, Custom(6001))` 的 `RPCException`。
    *   **验证 `is_slippage_related_error`**: 确认该方法在遇到此模拟错误时返回 `True`。
    *   **验证日志**: 确认记录了相关的警告/信息日志。

2.  **集成测试 (`test_trade_orchestrator.py`)**:
    *   设置 `JupiterTradeService` 在首次交易尝试时模拟抛出 `0x1771` (6001) 错误，并确保 `is_slippage_related_error` 将其识别为滑点。
    *   **验证 `TradeOrchestrator` 的滑点重试行为**:
        *   确认 `TradeOrchestrator` 捕获到此"滑点"错误后，会尝试增加滑点并进行重试。
        *   可以通过 Mock 检查 `JupiterTradeService.execute_trade` 是否被以更高的滑点参数再次调用。
        *   如果重试后成功（例如，模拟第二次调用不抛异常），验证最终交易状态是成功。
        *   如果多次重试后仍然失败（例如，每次都模拟抛出 `0x1771`），验证最终状态是失败，并且记录了所有尝试。
        *   确认最终的通知（如果交易完全失败）能够反映出尝试过滑点重试。

3.  **手动/场景测试 (部署后)** (此步骤保持不变):
    *   监控日志中 `0x1771` 错误以及相关的滑点重试行为。

## 6. 方案提出者/执行者
- AI Assistant (Gemini 2.5 Pro based)，根据用户提供的日志和反馈进行调整。

## 7. 方案审阅者/批准者
- 用户 (待审阅)

## 8. 方案批准日期
- 2025-05-27 (存档日期，待用户批准后生效)

## 9. 预期的验证方法
- 部署代码修改。
- 观察生产日志，特别是 `utils.trading.solana.jupiter_trade_service` 和 `utils.trading.trade_orchestrator` 的日志。
- 当交易通过 Jupiter 执行时，如果发生 `0x1771` (6001) 错误：
    - 确认 `JupiterTradeService.is_slippage_related_error` 将其识别为滑点。
    - 确认 `TradeOrchestrator` 触发了滑点重试逻辑（例如，日志中应有增加滑点并重试的记录）。
    - 观察重试后的结果。如果最终成功，则验证通过。如果多次重试后依然失败，确认所有尝试都被记录，并且最终的失败通知是合理的。
    - 确认 "No available channels" 的错误不再因为此特定 Jupiter 错误未被按滑点逻辑处理而出现。 