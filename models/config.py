from typing import Optional, Dict, Any, Union, List
from datetime import datetime
from enum import Enum
from beanie import Indexed, Document
from pydantic import BaseModel, Field, validator
import logging # Added for config.py compatibility
# import os # No longer needed here if TelegramConfig is removed


class RetryDelayStrategy(str, Enum):
    """重试间隔策略枚举"""
    FIXED = "fixed"          # 固定间隔
    LINEAR = "linear"        # 线性递增
    EXPONENTIAL = "exponential"  # 指数退避


# === 新增：自动交易管理器配置类 ===

class WalletConfig(BaseModel):
    """钱包配置 - 全局默认钱包配置"""
    default_private_key_env_var: str = Field(..., description="默认钱包私钥环境变量名")
    default_wallet_address: str = Field(..., description="默认钱包地址")


class TradingParams(BaseModel):
    """交易参数配置 - 渠道级别的默认参数"""
    # 基础交易参数
    default_buy_amount_sol: float = Field(default=0.01, description="默认每笔买入交易花费的SOL数量")
    default_buy_slippage_percentage: float = Field(default=1.0, description="默认买入滑点百分比")
    default_buy_priority_fee_sol: float = Field(default=0.00005, description="默认买入优先费（SOL）")
    default_sell_slippage_percentage: float = Field(default=1.0, description="默认卖出滑点百分比")
    default_sell_priority_fee_sol: float = Field(default=0.00005, description="默认卖出优先费（SOL）")
    
    # 滑点递增重试配置
    enable_slippage_retry: bool = Field(default=False, description="是否启用滑点递增重试")
    slippage_increment_percentage: float = Field(default=0.5, description="滑点增加步长（百分比）")
    max_slippage_percentage: float = Field(default=10.0, description="最大滑点限制（百分比）")
    
    # 重试间隔配置
    retry_delay_seconds: float = Field(default=0.5, description="基础重试间隔（秒）")
    retry_delay_strategy: RetryDelayStrategy = Field(default=RetryDelayStrategy.FIXED, description="重试间隔策略")
    max_retry_delay_seconds: float = Field(default=5.0, description="最大重试间隔（秒）")
    slippage_error_delay_seconds: Optional[float] = Field(default=None, description="滑点错误专用间隔（秒）")
    
    # 买卖独立滑点重试配置
    enable_buy_slippage_retry: Optional[bool] = Field(default=None, description="买入滑点递增开关（None时使用通用开关）")
    buy_slippage_increment_percentage: Optional[float] = Field(default=None, description="买入滑点增加步长")
    max_buy_slippage_percentage: Optional[float] = Field(default=None, description="买入最大滑点")
    buy_retry_delay_seconds: Optional[float] = Field(default=None, description="买入重试间隔（秒）")
    
    enable_sell_slippage_retry: Optional[bool] = Field(default=None, description="卖出滑点递增开关")
    sell_slippage_increment_percentage: Optional[float] = Field(default=None, description="卖出滑点增加步长")
    max_sell_slippage_percentage: Optional[float] = Field(default=None, description="卖出最大滑点")
    sell_retry_delay_seconds: Optional[float] = Field(default=None, description="卖出重试间隔（秒）")
    
    @validator('slippage_increment_percentage', 'buy_slippage_increment_percentage', 'sell_slippage_increment_percentage')
    def validate_increment(cls, v):
        if v is not None and v <= 0:
            raise ValueError("滑点增加步长必须大于0")
        return v
    
    @validator('max_slippage_percentage', 'max_buy_slippage_percentage', 'max_sell_slippage_percentage')
    def validate_max_slippage(cls, v):
        if v is not None and (v <= 0 or v > 50):
            raise ValueError("最大滑点必须在0-50%之间")
        return v
    
    @validator('retry_delay_seconds', 'max_retry_delay_seconds', 'slippage_error_delay_seconds', 'buy_retry_delay_seconds', 'sell_retry_delay_seconds')
    def validate_delay_times(cls, v):
        if v is not None and v < 0:
            raise ValueError("重试间隔时间不能为负数")
        if v is not None and v > 60:
            raise ValueError("重试间隔时间不应超过60秒")
        return v
    
    def get_effective_slippage_config(self, trade_type: str) -> dict:
        """获取指定交易类型的有效滑点重试配置"""
        if trade_type.lower() == 'buy':
            return {
                'enabled': self.enable_buy_slippage_retry if self.enable_buy_slippage_retry is not None else self.enable_slippage_retry,
                'increment': self.buy_slippage_increment_percentage if self.buy_slippage_increment_percentage is not None else self.slippage_increment_percentage,
                'max_slippage': self.max_buy_slippage_percentage if self.max_buy_slippage_percentage is not None else self.max_slippage_percentage,
                'retry_delay': self.buy_retry_delay_seconds if self.buy_retry_delay_seconds is not None else self.retry_delay_seconds
            }
        else:  # sell
            return {
                'enabled': self.enable_sell_slippage_retry if self.enable_sell_slippage_retry is not None else self.enable_slippage_retry,
                'increment': self.sell_slippage_increment_percentage if self.sell_slippage_increment_percentage is not None else self.slippage_increment_percentage,
                'max_slippage': self.max_sell_slippage_percentage if self.max_sell_slippage_percentage is not None else self.max_slippage_percentage,
                'retry_delay': self.sell_retry_delay_seconds if self.sell_retry_delay_seconds is not None else self.retry_delay_seconds
            }
    
    def calculate_retry_delay(self, retry_count: int, is_slippage_error: bool = False, trade_type: str = "buy") -> float:
        """
        计算重试间隔时间
        
        Args:
            retry_count: 当前重试次数（从1开始）
            is_slippage_error: 是否为滑点相关错误
            trade_type: 交易类型 (buy/sell)
            
        Returns:
            float: 重试间隔时间（秒）
        """
        # 确定基础间隔
        if is_slippage_error and self.slippage_error_delay_seconds is not None:
            base_delay = self.slippage_error_delay_seconds
        else:
            effective_config = self.get_effective_slippage_config(trade_type)
            base_delay = effective_config['retry_delay']
        
        # 根据策略计算间隔
        if self.retry_delay_strategy == RetryDelayStrategy.FIXED:
            calculated_delay = base_delay
        elif self.retry_delay_strategy == RetryDelayStrategy.LINEAR:
            calculated_delay = base_delay * retry_count
        elif self.retry_delay_strategy == RetryDelayStrategy.EXPONENTIAL:
            calculated_delay = base_delay * (2 ** (retry_count - 1))
        else:
            calculated_delay = base_delay
        
        # 应用最大间隔限制
        return min(calculated_delay, self.max_retry_delay_seconds)


class TradeChannelConfig(BaseModel):
    """交易渠道配置 - 不包含钱包信息，钱包由策略级别提供"""
    channel_type: str = Field(..., description="渠道类型：gmgn, jupiter")
    priority: int = Field(..., description="优先级，数字越小越优先")
    enabled: bool = Field(default=True, description="是否启用")
    timeout_seconds: int = Field(default=30, description="超时时间")
    max_retries: int = Field(default=3, description="最大重试次数")
    
    # 渠道级别的默认交易参数（可被策略级别参数覆盖）
    trading_params: TradingParams = Field(default_factory=TradingParams, description="渠道默认交易参数")
    
    # 渠道特定参数
    channel_params: Dict[str, Any] = Field(default_factory=dict, description="渠道特定参数（如API Host等）")


class NotificationConfig(BaseModel):
    """通知配置"""
    notify_on_failure: bool = Field(default=True, description="交易失败时是否通知")
    notify_on_fallback: bool = Field(default=True, description="渠道故障转移时是否通知")
    admin_chat_ids: List[str] = Field(default_factory=list, description="管理员Telegram Chat ID列表")
    include_trade_details: bool = Field(default=True, description="是否在通知中包含交易详情")


class AutoTradeConfig(BaseModel):
    """自动交易全局配置"""
    enabled: bool = Field(default=True, description="是否启用自动交易")
    wallet_config: WalletConfig = Field(..., description="全局默认钱包配置")
    channels: List[TradeChannelConfig] = Field(..., description="交易渠道配置列表")
    default_timeout: int = Field(default=60, description="默认总超时时间")
    max_total_retries: int = Field(default=5, description="所有渠道最大重试次数")
    notification_config: NotificationConfig = Field(default_factory=NotificationConfig, description="通知配置")


class AutoTradeManagerConfig(BaseModel):
    """AutoTradeManager配置，存储在数据库config集合中"""
    auto_trade: AutoTradeConfig = Field(..., description="自动交易配置")

# === 结束：自动交易管理器配置类 ===


class SingleKolStrategyConfig(BaseModel):
    """Configuration for a single KOL monitoring and buy strategy."""
    strategy_name: str = Field(default="default_kol_buy", description="此策略实例的唯一名称。")
    transaction_lookback_hours: int = Field(default=24, description="获取过去几个小时之内的交易记录, 单位: 小时")
    transaction_min_amount: float = Field(default=1000.0, description="交易金额最小值, 单位: USD")
    kol_account_min_count: int = Field(default=3, description="购买该代币的最小KOL账号数量")
    token_mint_lookback_hours: int = Field(default=48, description="是否为新代币的判断时间,即n个小时内mint的代币, 单位: 小时")
    kol_account_min_txs: Optional[int] = Field(default=10, description="KOL账号最少交易次数")
    kol_account_max_txs: Optional[int] = Field(default=100, description="KOL账号最多交易次数")
    sell_strategy_hours: int = Field(default=24, description="持有代币的最长时间，单位：小时")
    sell_kol_ratio: float = Field(default=0.5, description="触发卖出信号的KOL卖出比例")
    same_token_notification_interval: Optional[int] = Field(default=10, description="相同代币通知间隔, 单位: 分钟 (per strategy)")
    is_active: bool = Field(default=True, description="此策略配置是否激活。")

    # === 钱包配置（可选覆盖，重命名后的字段）===
    wallet_private_key_env_var: Optional[str] = Field(default=None, description="可选：覆盖全局默认私钥环境变量名")
    wallet_address: Optional[str] = Field(default=None, description="可选：覆盖全局默认钱包地址")

    # === 可选的交易参数覆盖字段（如果策略需要不同于全局默认值）===
    buy_amount_sol: Optional[float] = Field(default=None, description="可选：覆盖全局默认买入金额")
    buy_slippage_percentage: Optional[float] = Field(default=None, description="可选：覆盖全局默认买入滑点百分比")
    buy_priority_fee_sol: Optional[float] = Field(default=None, description="可选：覆盖全局默认买入优先费（SOL）")
    sell_slippage_percentage: Optional[float] = Field(default=None, description="可选：覆盖全局默认卖出滑点百分比")
    sell_priority_fee_sol: Optional[float] = Field(default=None, description="可选：覆盖全局默认卖出优先费（SOL）")
    
    # === 滑点递增重试覆盖配置（可选，策略级别覆盖）===
    strategy_enable_slippage_retry: Optional[bool] = Field(default=None, description="策略级别滑点递增开关覆盖")
    strategy_slippage_increment_percentage: Optional[float] = Field(default=None, description="策略级别滑点增加步长覆盖")
    strategy_max_slippage_percentage: Optional[float] = Field(default=None, description="策略级别最大滑点覆盖")
    
    # === 买卖独立滑点重试覆盖配置 ===
    strategy_enable_buy_slippage_retry: Optional[bool] = Field(default=None, description="策略级别买入滑点递增开关覆盖")
    strategy_buy_slippage_increment_percentage: Optional[float] = Field(default=None, description="策略级别买入滑点增加步长覆盖")
    strategy_max_buy_slippage_percentage: Optional[float] = Field(default=None, description="策略级别买入最大滑点覆盖")
    
    strategy_enable_sell_slippage_retry: Optional[bool] = Field(default=None, description="策略级别卖出滑点递增开关覆盖")
    strategy_sell_slippage_increment_percentage: Optional[float] = Field(default=None, description="策略级别卖出滑点增加步长覆盖")
    strategy_max_sell_slippage_percentage: Optional[float] = Field(default=None, description="策略级别卖出最大滑点覆盖")
    
    # === 重试间隔覆盖配置 ===
    strategy_retry_delay_seconds: Optional[float] = Field(default=None, description="策略级别重试间隔覆盖")
    strategy_retry_delay_strategy: Optional[str] = Field(default=None, description="策略级别重试间隔策略覆盖")
    strategy_max_retry_delay_seconds: Optional[float] = Field(default=None, description="策略级别最大重试间隔覆盖")
    strategy_slippage_error_delay_seconds: Optional[float] = Field(default=None, description="策略级别滑点错误间隔覆盖")
    strategy_buy_retry_delay_seconds: Optional[float] = Field(default=None, description="策略级别买入重试间隔覆盖")
    strategy_sell_retry_delay_seconds: Optional[float] = Field(default=None, description="策略级别卖出重试间隔覆盖")


class KolActivityConfig(BaseModel):
    """Top-level configuration for KOL activity monitoring, possibly containing multiple buy strategies."""
    buy_strategies: List[SingleKolStrategyConfig] = Field(default_factory=list, description="KOL购买策略配置列表。")
    # global_same_token_notification_interval: Optional[int] = Field(default=10, description="Global fallback for notification interval if not set per strategy")


class IdataRiverSpiderConfig(BaseModel):
    """IdataRiver爬虫配置"""
    # 添加具体的配置项
    pass


class UtoolsConfig(BaseModel):
    """Utools配置"""
    # 添加具体的配置项
    pass


class GmgnAccountConfig(BaseModel):
    """GMGN账户配置"""
    refresh_token: str = Field(description="GMGN API的refresh_token")
    device_id: str = Field(description="与refresh_token对应的device_id")


class GmgnApiConfig(BaseModel):
    """GMGN API配置"""
    accounts: List[GmgnAccountConfig] = Field(default_factory=list, description="GMGN API 账户配置列表。")
    # 可以在这里添加其他GMGN相关的配置，例如API密钥、访问频率等


class KolActivityMonitorConfig(BaseModel):
    """KOL活动监控特定配置"""
    alert_threshold_seconds: int = Field(default=60, description="触发告警的时间差异阈值(秒)")
    consecutive_alerts_required: int = Field(default=3, description="触发告警所需的连续检测次数")
    alert_suppression_minutes: int = Field(default=30, description="告警抑制时间(分钟)")


class KOLStrategyScoringParams(BaseModel):
    """单个策略的特定打分参数"""
    positive_score: Optional[float] = Field(default=None, description="盈利时的固定加分值。如果为None，则使用全局默认值。")
    negative_score_multiplier: Optional[float] = Field(default=None, description="亏损时的扣分乘数 (乘以PnL绝对值)。如果为None，则使用全局默认值。PnL为负时，最终扣分 = abs(PnL) * multiplier * -1")


class KOLScoringConfig(BaseModel):
    """KOL打分功能的配置模型"""
    # 全局默认参数
    default_positive_score: float = Field(default=1.0, description="全局默认：盈利时的固定加分值")
    default_negative_score_multiplier: float = Field(default=1.0, description="全局默认：亏损时的扣分乘数。最终扣分 = abs(PnL) * multiplier * -1")
    
    # 工作流参数
    workflow_limit: int = Field(default=50, description="工作流每次处理的最大组合数")
    workflow_max_lookback_days: int = Field(default=90, description="工作流聚合查询时的最大回溯天数")
    
    # 策略特定参数
    # 键是 strategy_name
    strategy_specific_params: Dict[str, KOLStrategyScoringParams] = Field(default_factory=dict, description="策略特定的打分参数，用于覆盖全局默认值")

    def get_effective_params_for_strategy(self, strategy_name: str) -> Dict[str, float]:
        """
        获取指定策略的有效打分参数。
        如果策略特定参数存在，则使用；否则回退到全局默认参数。
        """
        strategy_params = self.strategy_specific_params.get(strategy_name)
        
        effective_positive_score = self.default_positive_score
        effective_negative_multiplier = self.default_negative_score_multiplier
        
        if strategy_params:
            if strategy_params.positive_score is not None:
                effective_positive_score = strategy_params.positive_score
            if strategy_params.negative_score_multiplier is not None:
                effective_negative_multiplier = strategy_params.negative_score_multiplier
        
        return {
            "positive_score": effective_positive_score,
            "negative_score_multiplier": effective_negative_multiplier
        }


class ApplicationConfig(BaseModel):
    """应用通用配置"""
    admin_telegram_chat_ids: Optional[List[str]] = Field(default_factory=list, description="接收管理和错误通知的Telegram用户Chat ID列表。")
    kol_activity_monitor: Optional[KolActivityMonitorConfig] = Field(default_factory=KolActivityMonitorConfig, description="KOL活动监控配置")
    kol_scoring_config: Optional[KOLScoringConfig] = Field(default_factory=KOLScoringConfig, description="KOL打分功能配置")
    # 可以在这里添加其他应用级别的配置


class Config(Document):
    """配置类"""
    type: Indexed(str) = Field(description="配置类型")
    version: int = Field(default=1, description="配置版本号")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")
    
    # 使用联合类型定义配置数据
    data: Union[
        KolActivityConfig, 
        SingleKolStrategyConfig, 
        AutoTradeManagerConfig,  # 新增
        KOLScoringConfig,        # 新增：KOL打分配置
        IdataRiverSpiderConfig, 
        UtoolsConfig, 
        GmgnApiConfig, 
        ApplicationConfig, 
        Dict[str, Any]
    ] = Field(description="配置数据，根据type字段确定具体类型")
    # Note: SingleKolStrategyConfig is temporarily kept in Union for smoother transition / testing individual old configs if needed,
    # but new "kol_activity" types should use KolActivityConfig.
    # Eventually, SingleKolStrategyConfig might be removed from this top-level Union if it's always nested.
    
    # 可选的描述字段
    description: Optional[str] = Field(default=None, description="配置描述")
    
    class Settings:
        name = "config"
        use_state_management = True
        indexes = [
            "type",
            "version",
            "updated_at"
        ]
    
    @classmethod
    async def from_api_data(cls, data: dict) -> "Config":
        """从API数据创建配置
        
        Args:
            data: API数据，包含type和data字段
            
        Returns:
            Config: 配置对象
        """
        config_type = data["type"]
        config_data_dict = data["data"]
        
        # 根据类型转换数据为对应的模型
        if config_type == "kol_activity":
            typed_config_data = KolActivityConfig(**config_data_dict)
        elif config_type == "kol_monitor":
            typed_config_data = SingleKolStrategyConfig(**config_data_dict)
        elif config_type == "auto_trade_manager":  # 新增
            typed_config_data = AutoTradeManagerConfig(**config_data_dict)
        elif config_type == "kol_scoring":  # 新增：KOL打分配置
            typed_config_data = KOLScoringConfig(**config_data_dict)
        elif config_type == "idata_river_spider":
            typed_config_data = IdataRiverSpiderConfig(**config_data_dict)
        elif config_type == "utools":
            typed_config_data = UtoolsConfig(**config_data_dict)
        elif config_type == "gmgn_api":
            typed_config_data = GmgnApiConfig(**config_data_dict)
        elif config_type == "application_config":
            typed_config_data = ApplicationConfig(**config_data_dict)
        else:
            typed_config_data = config_data_dict
            
        return cls(
            type=config_type,
            data=typed_config_data,
            description=data.get("description")
        )
    
    async def update_config(self, new_data: dict) -> None:
        """更新配置
        
        Args:
            new_data: 新的配置数据
        """
        # 增加版本号
        self.version += 1
        self.updated_at = datetime.utcnow()
        
        # 更新数据
        current_data_dict = self.data.model_dump() if isinstance(self.data, BaseModel) else self.data
        current_data_dict.update(new_data)
            
        # 根据类型重新创建模型
        if self.type == "kol_activity":
            self.data = KolActivityConfig(**current_data_dict)
        elif self.type == "kol_monitor":
            try:
                self.data = SingleKolStrategyConfig(**current_data_dict)
            except Exception:
                self.data = current_data_dict
        elif self.type == "auto_trade_manager":  # 新增
            self.data = AutoTradeManagerConfig(**current_data_dict)
        elif self.type == "kol_scoring":  # 新增：KOL打分配置
            self.data = KOLScoringConfig(**current_data_dict)
        elif self.type == "idata_river_spider":
            self.data = IdataRiverSpiderConfig(**current_data_dict)
        elif self.type == "utools":
            self.data = UtoolsConfig(**current_data_dict)
        elif self.type == "gmgn_api":
            self.data = GmgnApiConfig(**current_data_dict)
        elif self.type == "application_config":
            self.data = ApplicationConfig(**current_data_dict)
        else:
            self.data = current_data_dict
