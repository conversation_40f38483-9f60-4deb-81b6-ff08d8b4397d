# TradeRecord缺少token_out_decimals字段Bug修复方案

## Bug标识
**Bug ID**: TradeRecord_MissingTokenDecimals  
**报告日期**: 2025-05-27T10:56:18+08:00  
**发现者**: 用户报告  

## 根源分析概要
在 `workflows/monitor_kol_activity/sell_signal_handler.py` 第374行，代码尝试访问 `original_buy_trade_record.token_out_decimals` 字段来获取代币的小数位数信息，但是 `models/trade_record.py` 中的 `TradeRecord` 类没有定义这个字段，导致 `AttributeError: 'TradeRecord' object has no attribute 'token_out_decimals'` 错误。

## 详细的修复方案
### 问题定位
- **文件**: `models/trade_record.py` 和 `workflows/monitor_kol_activity/sell_signal_handler.py`
- **位置**: TradeRecord模型缺少token_out_decimals字段，但sell_signal_handler.py第374行尝试访问
- **根本原因**: 模型定义与代码使用不一致

### 修复措施
1. **添加缺失字段**: 在 `TradeRecord` 模型中添加 `token_out_decimals` 和 `token_in_decimals` 字段
2. **保持向后兼容**: 字段设为可选，默认值为None
3. **更新交易服务**: 在交易成功时保存decimals信息到TradeRecord
4. **数据库索引**: 不需要为decimals字段添加索引

### 具体代码修改
**文件**: `models/trade_record.py`  
**修改位置**: TradeRecord类定义中  
**修改前**:
```python
class TradeRecord(Document):
    # ... 现有字段 ...
    token_out_address: str
    token_out_amount_expected: Optional[float] = Field(default=None, description="Amount expected to receive")
    token_out_actual_amount: Optional[float] = Field(default=None, description="Amount actually received")
    # ... 其他字段 ...
```

**修改后**:
```python
class TradeRecord(Document):
    # ... 现有字段 ...
    token_out_address: str
    token_out_amount_expected: Optional[float] = Field(default=None, description="Amount expected to receive")
    token_out_actual_amount: Optional[float] = Field(default=None, description="Amount actually received")
    token_out_decimals: Optional[int] = Field(default=None, description="Decimals of the output token")
    token_in_decimals: Optional[int] = Field(default=None, description="Decimals of the input token")
    # ... 其他字段 ...
```

### 测试用例设计
1. **模型测试**: 验证TradeRecord可以正确保存和读取新字段
2. **功能测试**: 验证sell_signal_handler可以正确访问token_out_decimals字段
3. **向后兼容测试**: 确保现有数据（decimals为None）不受影响
4. **集成测试**: 验证交易服务正确保存decimals信息

### 影响评估
- **数据库**: 新字段为可选，不影响现有数据
- **API**: 不影响现有API接口
- **性能**: 无性能影响
- **向后兼容**: 完全向后兼容

## 方案提出者/执行者
AI Assistant

## 方案审阅者/批准者
用户

## 方案批准日期
待用户确认

## 预期的验证方法
1. 运行相关测试用例确保修复有效
2. 验证sell_signal_handler不再抛出AttributeError
3. 确认新的交易记录正确保存decimals信息 