"""
AutoTradeManager单元测试

测试AutoTradeManager的核心功能：
1. 初始化和单例模式
2. 交易执行流程
3. 配置管理
4. 错误处理
5. 渠道管理
"""

import unittest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import Dict, Any, List, Optional
from datetime import datetime

# 测试相关导入
from models.config import Config, AutoTradeManagerConfig, AutoTradeConfig, WalletConfig, TradingParams, TradeChannelConfig, NotificationConfig, RetryDelayStrategy
from models.trade_execution import TradeExecutionResult, TradeStatus, ChannelAttemptResult
from beanie import PydanticObjectId
from models.trade_record import TradeRecord, TradeStatus as ModelTradeStatus, TradeType as ModelTradeType
from utils.trading.auto_trade_manager import AutoTradeManager, get_auto_trade_manager
from utils.trading.config_manager import ConfigManager
from utils.trading.channel_registry import ChannelRegistry
from utils.trading.channel_selector import ChannelSelector
from utils.trading.trade_orchestrator import TradeOrchestrator, TradeRequest
from utils.trading.trade_record_manager import TradeRecordManager
from utils.message_sender.message_sender import MessageSender, TelegramMessageSender
from models.slippage_retry import SlippageAdjustmentRecord, SlippageAdjustmentReason
from utils.trading.slippage_retry import ParameterMerger


class TestAutoTradeManager(unittest.TestCase):
    """AutoTradeManager单元测试类"""
    
    def setUp(self) -> None:
        """测试前置设置"""
        # 重置单例实例
        AutoTradeManager._instance = None
        
        # 创建mock对象
        self.mock_config_manager = Mock(spec=ConfigManager)
        self.mock_channel_registry = Mock(spec=ChannelRegistry)
        self.mock_channel_selector = Mock(spec=ChannelSelector)
        self.mock_trade_orchestrator = Mock(spec=TradeOrchestrator)
        self.mock_trade_record_manager = Mock(spec=TradeRecordManager)
        
        # 创建测试配置
        self.sample_config = self._create_test_config()
        
        # 设置mock返回值（异步方法使用AsyncMock）
        self.mock_config_manager.get_config = AsyncMock(return_value=self.sample_config.auto_trade)
        self.mock_config_manager.is_enabled = AsyncMock(return_value=True)
        self.mock_config_manager.get_enabled_channels = AsyncMock(return_value=self.sample_config.auto_trade.channels)
        self.mock_config_manager.get_wallet_config = AsyncMock(return_value=self.sample_config.auto_trade.wallet_config)
        self.mock_config_manager.get_notification_config = AsyncMock(return_value=self.sample_config.auto_trade.notification_config)
        self.mock_config_manager.get_cache_info = AsyncMock(return_value={"has_cache": True, "last_reload": "2025-01-25T21:00:00"})
        
        # 设置其他mock方法
        self.mock_channel_registry.close_all_channels = AsyncMock()
        self.mock_channel_registry.list_channels.return_value = ["gmgn", "jupiter"]
        self.mock_channel_registry.get_registry_stats.return_value = {
            "total_channels": 2,
            "healthy_channels": 2
        }
        self.mock_channel_selector.clear_health_cache = Mock()
        self.mock_channel_selector.get_selector_stats.return_value = {
            "selection_count": 5,
            "health_check_count": 10
        }
        self.mock_config_manager.force_reload = AsyncMock()
        self.mock_trade_orchestrator.get_execution_stats.return_value = {
            "total_trades": 10,
            "success_rate": 0.8,
            "avg_execution_time": 15.5
        }
        self.mock_trade_record_manager.create_trade_record = AsyncMock()
        self.mock_trade_record_manager.update_trade_record_from_execution_result = AsyncMock()
        self.mock_trade_record_manager.save_channel_attempt_records = AsyncMock()
        self.mock_trade_record_manager.get_trade_stats = AsyncMock(return_value={"total_trades": 0})
        
        # 设置测试环境变量
        import os
        os.environ["TEST_PRIVATE_KEY"] = "test_private_key_value"
        os.environ["CUSTOM_PRIVATE_KEY"] = "custom_private_key_value"
    
    def tearDown(self) -> None:
        """测试后置清理"""
        # 重置单例实例
        AutoTradeManager._instance = None
        
        # 清理测试环境变量
        import os
        for key in ["TEST_PRIVATE_KEY", "CUSTOM_PRIVATE_KEY"]:
            if key in os.environ:
                del os.environ[key]
    
    def _create_test_config(self) -> AutoTradeManagerConfig:
        """创建测试用配置"""
        wallet_config = WalletConfig(
            default_private_key_env_var="TEST_PRIVATE_KEY",
            default_wallet_address="test_wallet_address"
        )
        
        trading_params = TradingParams(
            default_buy_amount_sol=0.01,
            default_buy_slippage_percentage=10.0,
            default_buy_priority_fee_sol=0.0005,
            default_sell_slippage_percentage=10.0,
            default_sell_priority_fee_sol=0.0005,
            # 滑点重试配置
            enable_slippage_retry=True,
            slippage_increment_percentage=0.5,
            max_slippage_percentage=15.0,
            retry_delay_seconds=0.1,
            retry_delay_strategy=RetryDelayStrategy.FIXED,
            max_retry_delay_seconds=2.0
        )
        
        channel_config = TradeChannelConfig(
            channel_type="gmgn",
            enabled=True,
            priority=1,
            timeout_seconds=60,
            max_retries=3,
            trading_params=trading_params,
            channel_params={"gmgn_api_host": "https://gmgn.ai"}
        )
        
        notification_config = NotificationConfig(
            notify_on_failure=True,
            notify_on_fallback=True,
            admin_chat_ids=["123456789", "987654321"],
            include_trade_details=True
        )
        
        auto_trade_config = AutoTradeConfig(
            enabled=True,
            wallet_config=wallet_config,
            channels=[channel_config],
            default_timeout=120,
            max_total_retries=5,
            notification_config=notification_config
        )
        
        return AutoTradeManagerConfig(
            auto_trade=auto_trade_config
        )
    
    @patch('utils.trading.auto_trade_manager.ConfigManager')
    @patch('utils.trading.auto_trade_manager.ChannelRegistry')
    @patch('utils.trading.auto_trade_manager.ChannelSelector')
    @patch('utils.trading.auto_trade_manager.TradeOrchestrator')
    @patch('utils.trading.auto_trade_manager.TradeRecordManager')
    async def test_initialization(
        self, 
        mock_record_manager_class: Mock,
        mock_orchestrator_class: Mock,
        mock_selector_class: Mock,
        mock_registry_class: Mock,
        mock_config_manager_class: Mock
    ) -> None:
        """测试AutoTradeManager初始化"""
        # 设置mock类的返回值
        mock_config_manager_class.return_value = self.mock_config_manager
        mock_registry_class.return_value = self.mock_channel_registry
        mock_selector_class.return_value = self.mock_channel_selector
        mock_orchestrator_class.return_value = self.mock_trade_orchestrator
        mock_record_manager_class.return_value = self.mock_trade_record_manager
        
        # 创建AutoTradeManager实例
        manager = AutoTradeManager()
        manager._initialized = True  # 跳过initialize以避免调用实际配置
        
        # 验证初始化
        self.assertIsNotNone(manager.config_manager)
        self.assertIsNotNone(manager.channel_registry)
        self.assertIsNotNone(manager.channel_selector)
        self.assertIsNotNone(manager.trade_orchestrator)
        self.assertIsNotNone(manager.trade_record_manager)
        self.assertTrue(manager.is_initialized)
        
        # 验证配置加载
        self.mock_config_manager.get_config.assert_called()
    
    @patch('utils.trading.auto_trade_manager.ConfigManager')
    @patch('utils.trading.auto_trade_manager.ChannelRegistry')
    @patch('utils.trading.auto_trade_manager.ChannelSelector')
    @patch('utils.trading.auto_trade_manager.TradeOrchestrator')
    @patch('utils.trading.auto_trade_manager.TradeRecordManager')
    async def test_singleton_pattern(
        self,
        mock_record_manager_class: Mock,
        mock_orchestrator_class: Mock,
        mock_selector_class: Mock,
        mock_registry_class: Mock,
        mock_config_manager_class: Mock
    ) -> None:
        """测试单例模式"""
        # 设置mock类的返回值
        mock_config_manager_class.return_value = self.mock_config_manager
        mock_registry_class.return_value = self.mock_channel_registry
        mock_selector_class.return_value = self.mock_channel_selector
        mock_orchestrator_class.return_value = self.mock_trade_orchestrator
        mock_record_manager_class.return_value = self.mock_trade_record_manager
        
        # 获取两个实例
        manager1 = await get_auto_trade_manager()
        manager2 = await get_auto_trade_manager()
        
        # 验证是同一个实例
        self.assertIs(manager1, manager2)
        self.assertTrue(manager1.is_initialized)
    
    @patch('utils.trading.auto_trade_manager.ConfigManager')
    @patch('utils.trading.auto_trade_manager.ChannelRegistry')
    @patch('utils.trading.auto_trade_manager.ChannelSelector')
    @patch('utils.trading.auto_trade_manager.TradeOrchestrator')
    @patch('utils.trading.auto_trade_manager.TradeRecordManager')
    async def test_execute_trade_success(
        self,
        mock_record_manager_class: Mock,
        mock_orchestrator_class: Mock,
        mock_selector_class: Mock,
        mock_registry_class: Mock,
        mock_config_manager_class: Mock
    ) -> None:
        """测试成功的交易执行"""
        # 设置mock类的返回值
        mock_config_manager_class.return_value = self.mock_config_manager
        mock_registry_class.return_value = self.mock_channel_registry
        mock_selector_class.return_value = self.mock_channel_selector
        mock_orchestrator_class.return_value = self.mock_trade_orchestrator
        mock_record_manager_class.return_value = self.mock_trade_record_manager
        
        # 创建成功的交易结果
        successful_attempt = ChannelAttemptResult(
            channel_type="gmgn",
            attempt_number=1,
            status=TradeStatus.SUCCESS,
            tx_hash="test_tx_hash",
            execution_time=10.5,
            started_at=datetime.now(),
            completed_at=datetime.now()
        )
        
        execution_result = TradeExecutionResult(
            final_status=TradeStatus.SUCCESS,
            successful_channel="gmgn",
            final_trade_record_id=PydanticObjectId(),
            channel_attempts=[successful_attempt],
            total_execution_time=10.5,
            started_at=datetime.now(),
            completed_at=datetime.now()
        )
        
        # 设置mock返回值
        self.mock_trade_orchestrator.execute_trade = AsyncMock(return_value=execution_result)
        
        # 确保自动交易启用
        self.mock_config_manager.is_enabled = AsyncMock(return_value=True)
        
        # 创建Mock交易记录
        mock_trade_record = Mock()
        mock_trade_record.id = PydanticObjectId()
        self.mock_trade_record_manager.create_trade_record = AsyncMock(return_value=mock_trade_record)
        
        # 创建AutoTradeManager并执行交易
        manager = AutoTradeManager()
        # 手动设置Mock对象以覆盖__init__中创建的实例
        manager.config_manager = self.mock_config_manager
        manager.channel_registry = self.mock_channel_registry
        manager.channel_selector = self.mock_channel_selector
        manager.trade_orchestrator = self.mock_trade_orchestrator
        manager.trade_record_manager = self.mock_trade_record_manager
        manager._initialized = True  # 跳过initialize以避免调用实际配置
        
        # 执行交易
        result = await manager.execute_trade(
            trade_type="buy",
            token_in_address="So11111111111111111111111111111111111111112",
            token_out_address="target_token",
            amount=0.01,
            signal_id=None,
            strategy_name=None
        )
        
        # 验证结果
        self.assertEqual(result.final_status, TradeStatus.SUCCESS)
        self.assertEqual(result.successful_channel, "gmgn")
        self.assertEqual(len(result.channel_attempts), 1)
        self.assertIsNotNone(result.final_trade_record_id)
        
        # 测试创建交易记录的调用
        self.mock_trade_record_manager.create_trade_record.assert_called_once()
        call_args = self.mock_trade_record_manager.create_trade_record.call_args
        
        # 验证调用参数包含signal_id和strategy_name
        assert call_args[1]['signal_id'] is not None  # 应该有signal_id（生成的临时ID）
        assert call_args[1]['strategy_name'] == "auto_trade"  # 默认策略名称（因为测试中strategy_name=None）
        assert call_args[1]['trade_type'] == "buy"
        assert call_args[1]['token_in_address'] == "So11111111111111111111111111111111111111112"
        assert call_args[1]['token_out_address'] == "target_token"
        assert call_args[1]['amount'] == 0.01
        assert call_args[1]['wallet_address'] == "test_wallet_address"
    
    @patch('utils.trading.auto_trade_manager.ConfigManager')
    @patch('utils.trading.auto_trade_manager.ChannelRegistry')
    @patch('utils.trading.auto_trade_manager.ChannelSelector')
    @patch('utils.trading.auto_trade_manager.TradeOrchestrator')
    @patch('utils.trading.auto_trade_manager.TradeRecordManager')
    async def test_execute_trade_disabled(
        self,
        mock_record_manager_class: Mock,
        mock_orchestrator_class: Mock,
        mock_selector_class: Mock,
        mock_registry_class: Mock,
        mock_config_manager_class: Mock
    ) -> None:
        """测试禁用状态下的交易执行"""
        # 设置mock类的返回值
        mock_config_manager_class.return_value = self.mock_config_manager
        mock_registry_class.return_value = self.mock_channel_registry
        mock_selector_class.return_value = self.mock_channel_selector
        mock_orchestrator_class.return_value = self.mock_trade_orchestrator
        mock_record_manager_class.return_value = self.mock_trade_record_manager
        
        # 设置自动交易为禁用状态
        self.mock_config_manager.is_enabled = AsyncMock(return_value=False)
        
        # 创建AutoTradeManager并执行交易
        manager = AutoTradeManager()
        # 手动设置Mock对象以覆盖__init__中创建的实例
        manager.config_manager = self.mock_config_manager
        manager.channel_registry = self.mock_channel_registry
        manager.channel_selector = self.mock_channel_selector
        manager.trade_orchestrator = self.mock_trade_orchestrator
        manager.trade_record_manager = self.mock_trade_record_manager
        manager._initialized = True  # 跳过initialize以避免调用实际配置
        
        result = await manager.execute_trade(
            trade_type="buy",
            token_in_address="So11111111111111111111111111111111111111112",
            token_out_address="test_token_address",
            amount=0.01,
            signal_id="test_signal_id",
            strategy_name="test_strategy"
        )
        
        # 验证结果
        self.assertEqual(result.final_status, TradeStatus.SKIPPED)
        self.assertIn("disabled", result.error_summary.lower())
    
    @patch('utils.trading.auto_trade_manager.ConfigManager')
    @patch('utils.trading.auto_trade_manager.ChannelRegistry')
    @patch('utils.trading.auto_trade_manager.ChannelSelector')
    @patch('utils.trading.auto_trade_manager.TradeOrchestrator')
    @patch('utils.trading.auto_trade_manager.TradeRecordManager')
    async def test_execute_trade_with_overrides(
        self,
        mock_record_manager_class: Mock,
        mock_orchestrator_class: Mock,
        mock_selector_class: Mock,
        mock_registry_class: Mock,
        mock_config_manager_class: Mock
    ) -> None:
        """测试带参数覆盖的交易执行"""
        # 设置mock类的返回值
        mock_config_manager_class.return_value = self.mock_config_manager
        mock_registry_class.return_value = self.mock_channel_registry
        mock_selector_class.return_value = self.mock_channel_selector
        mock_orchestrator_class.return_value = self.mock_trade_orchestrator
        mock_record_manager_class.return_value = self.mock_trade_record_manager
        
        # 创建成功的交易结果
        execution_result = TradeExecutionResult(
            final_status=TradeStatus.SUCCESS,
            successful_channel="gmgn",
            final_trade_record_id=PydanticObjectId(),
            channel_attempts=[],
            total_execution_time=10.5,
            started_at=datetime.now(),
            completed_at=datetime.now()
        )
        
        self.mock_trade_orchestrator.execute_trade = AsyncMock(return_value=execution_result)
        
        # 确保自动交易启用
        self.mock_config_manager.is_enabled = AsyncMock(return_value=True)
        
        # 创建Mock交易记录
        mock_trade_record = Mock()
        mock_trade_record.id = PydanticObjectId()
        self.mock_trade_record_manager.create_trade_record = AsyncMock(return_value=mock_trade_record)
        
        # 创建AutoTradeManager并执行交易
        manager = AutoTradeManager()
        # 手动设置Mock对象以覆盖__init__中创建的实例
        manager.config_manager = self.mock_config_manager
        manager.channel_registry = self.mock_channel_registry
        manager.channel_selector = self.mock_channel_selector
        manager.trade_orchestrator = self.mock_trade_orchestrator
        manager.trade_record_manager = self.mock_trade_record_manager
        manager._initialized = True  # 跳过initialize以避免调用实际配置
        
        strategy_overrides = {
            "buy_amount_sol": 0.05,
            "buy_slippage_percentage": 15.0
        }
        
        result = await manager.execute_trade(
            trade_type="buy",
            token_in_address="So11111111111111111111111111111111111111112",
            token_out_address="test_token_address",
            wallet_private_key_env_var="CUSTOM_PRIVATE_KEY",
            wallet_address="custom_wallet_address",
            strategy_trading_overrides=strategy_overrides,
            signal_id="test_signal_id",
            strategy_name="test_strategy"
        )
        
        # 验证结果
        self.assertEqual(result.final_status, TradeStatus.SUCCESS)
        
        # 验证TradeOrchestrator被正确调用
        self.mock_trade_orchestrator.execute_trade.assert_called_once()
        call_args = self.mock_trade_orchestrator.execute_trade.call_args
        trade_request: TradeRequest = call_args[0][0]
        
        # 验证参数覆盖生效
        self.assertEqual(trade_request.amount, 0.05)
        self.assertEqual(trade_request.wallet_address, "custom_wallet_address")
        # 注意：私钥字段是wallet_private_key_b58而不是wallet_private_key_env_var
    
    @patch('utils.trading.auto_trade_manager.ConfigManager')
    @patch('utils.trading.auto_trade_manager.ChannelRegistry')
    @patch('utils.trading.auto_trade_manager.ChannelSelector')
    @patch('utils.trading.auto_trade_manager.TradeOrchestrator')
    @patch('utils.trading.auto_trade_manager.TradeRecordManager')
    async def test_error_handling(
        self,
        mock_record_manager_class: Mock,
        mock_orchestrator_class: Mock,
        mock_selector_class: Mock,
        mock_registry_class: Mock,
        mock_config_manager_class: Mock
    ) -> None:
        """测试错误处理"""
        # 设置mock类的返回值
        mock_config_manager_class.return_value = self.mock_config_manager
        mock_registry_class.return_value = self.mock_channel_registry
        mock_selector_class.return_value = self.mock_channel_selector
        mock_orchestrator_class.return_value = self.mock_trade_orchestrator
        mock_record_manager_class.return_value = self.mock_trade_record_manager
        
        # 设置TradeOrchestrator抛出异常
        self.mock_trade_orchestrator.execute_trade = AsyncMock(
            side_effect=Exception("Mock error")
        )
        
        # 创建Mock交易记录
        mock_trade_record = Mock()
        mock_trade_record.id = PydanticObjectId()
        self.mock_trade_record_manager.create_trade_record = AsyncMock(return_value=mock_trade_record)
        
        # 创建AutoTradeManager并执行交易
        manager = AutoTradeManager()
        # 手动设置Mock对象以覆盖__init__中创建的实例
        manager.config_manager = self.mock_config_manager
        manager.channel_registry = self.mock_channel_registry
        manager.channel_selector = self.mock_channel_selector
        manager.trade_orchestrator = self.mock_trade_orchestrator
        manager.trade_record_manager = self.mock_trade_record_manager
        manager._initialized = True  # 跳过initialize以避免调用实际配置
        
        result = await manager.execute_trade(
            trade_type="buy",
            token_in_address="So11111111111111111111111111111111111111112",
            token_out_address="test_token_address",
            amount=0.01,
            signal_id="test_signal_id",
            strategy_name="test_strategy"
        )
        
        # 验证错误处理
        self.assertEqual(result.final_status, TradeStatus.FAILED)
        self.assertIn("Mock error", result.error_summary)
    
    @patch('utils.trading.auto_trade_manager.ConfigManager')
    @patch('utils.trading.auto_trade_manager.ChannelRegistry')
    @patch('utils.trading.auto_trade_manager.ChannelSelector')
    @patch('utils.trading.auto_trade_manager.TradeOrchestrator')
    @patch('utils.trading.auto_trade_manager.TradeRecordManager')
    async def test_config_reload(
        self,
        mock_record_manager_class: Mock,
        mock_orchestrator_class: Mock,
        mock_selector_class: Mock,
        mock_registry_class: Mock,
        mock_config_manager_class: Mock
    ) -> None:
        """测试配置重新加载"""
        # 设置mock类的返回值
        mock_config_manager_class.return_value = self.mock_config_manager
        mock_registry_class.return_value = self.mock_channel_registry
        mock_selector_class.return_value = self.mock_channel_selector
        mock_orchestrator_class.return_value = self.mock_trade_orchestrator
        mock_record_manager_class.return_value = self.mock_trade_record_manager
        
        # 创建AutoTradeManager
        manager = AutoTradeManager()
        # 手动设置Mock对象以覆盖__init__中创建的实例
        manager.config_manager = self.mock_config_manager
        manager.channel_registry = self.mock_channel_registry
        manager.channel_selector = self.mock_channel_selector
        manager.trade_orchestrator = self.mock_trade_orchestrator
        manager.trade_record_manager = self.mock_trade_record_manager
        manager._initialized = True  # 跳过initialize以避免调用实际配置
        
        # 重新加载配置
        await manager.reload_config()
        
        # 验证配置重新加载
        self.mock_config_manager.force_reload.assert_called_once()
    
    @patch('utils.trading.auto_trade_manager.ConfigManager')
    @patch('utils.trading.auto_trade_manager.ChannelRegistry')
    @patch('utils.trading.auto_trade_manager.ChannelSelector')
    @patch('utils.trading.auto_trade_manager.TradeOrchestrator')
    @patch('utils.trading.auto_trade_manager.TradeRecordManager')
    async def test_get_status(
        self,
        mock_record_manager_class: Mock,
        mock_orchestrator_class: Mock,
        mock_selector_class: Mock,
        mock_registry_class: Mock,
        mock_config_manager_class: Mock
    ) -> None:
        """测试状态获取"""
        # 设置mock类的返回值
        mock_config_manager_class.return_value = self.mock_config_manager
        mock_registry_class.return_value = self.mock_channel_registry
        mock_selector_class.return_value = self.mock_channel_selector
        mock_orchestrator_class.return_value = self.mock_trade_orchestrator
        mock_record_manager_class.return_value = self.mock_trade_record_manager
        
        # 设置mock返回值
        self.mock_channel_registry.list_channels.return_value = ["gmgn", "jupiter"]
        self.mock_channel_registry.get_registry_stats.return_value = {
            "total_channels": 2,
            "healthy_channels": 2
        }
        self.mock_channel_selector.get_selector_stats.return_value = {
            "selection_count": 5,
            "health_check_count": 10
        }
        self.mock_trade_orchestrator.get_execution_stats.return_value = {
            "total_trades": 10,
            "success_rate": 0.8,
            "avg_execution_time": 15.5
        }
        
        # 创建AutoTradeManager
        manager = AutoTradeManager()
        # 手动设置Mock对象以覆盖__init__中创建的实例
        manager.config_manager = self.mock_config_manager
        manager.channel_registry = self.mock_channel_registry
        manager.channel_selector = self.mock_channel_selector
        manager.trade_orchestrator = self.mock_trade_orchestrator
        manager.trade_record_manager = self.mock_trade_record_manager
        manager._initialized = True  # 跳过initialize以避免调用实际配置
        
        # 获取状态
        status = await manager.get_status()
        
        # 验证状态信息
        self.assertIn("initialized", status)
        self.assertIn("auto_trade_enabled", status)
        self.assertIn("channel_registry", status)
        self.assertIn("trade_execution", status)
        self.assertEqual(status["channel_registry"]["total_channels"], 2)
    
    @patch('utils.trading.auto_trade_manager.ConfigManager')
    @patch('utils.trading.auto_trade_manager.ChannelRegistry')
    @patch('utils.trading.auto_trade_manager.ChannelSelector')
    @patch('utils.trading.auto_trade_manager.TradeOrchestrator')
    @patch('utils.trading.auto_trade_manager.TradeRecordManager')
    async def test_parameter_validation(
        self,
        mock_record_manager_class: Mock,
        mock_orchestrator_class: Mock,
        mock_selector_class: Mock,
        mock_registry_class: Mock,
        mock_config_manager_class: Mock
    ) -> None:
        """测试参数验证"""
        # 设置mock类的返回值
        mock_config_manager_class.return_value = self.mock_config_manager
        mock_registry_class.return_value = self.mock_channel_registry
        mock_selector_class.return_value = self.mock_channel_selector
        mock_orchestrator_class.return_value = self.mock_trade_orchestrator
        mock_record_manager_class.return_value = self.mock_trade_record_manager
        
        # 确保自动交易启用
        self.mock_config_manager.is_enabled = AsyncMock(return_value=True)
        
        # 测试无效的环境变量（私钥不存在）
        manager = AutoTradeManager()
        # 手动设置Mock对象以覆盖__init__中创建的实例
        manager.config_manager = self.mock_config_manager
        manager.channel_registry = self.mock_channel_registry
        manager.channel_selector = self.mock_channel_selector
        manager.trade_orchestrator = self.mock_trade_orchestrator
        manager.trade_record_manager = self.mock_trade_record_manager
        manager._initialized = True  # 跳过initialize以避免调用实际配置
        
        result = await manager.execute_trade(
            trade_type="buy",
            token_in_address="test",
            token_out_address="test",
            wallet_private_key_env_var="NON_EXISTENT_KEY"
        )
        
        # 应该返回失败状态而不是抛出异常
        self.assertEqual(result.final_status, TradeStatus.FAILED)
        self.assertIn("无法从环境变量", result.error_summary)

    # ========== 通知功能测试用例 ==========
    
    @patch('utils.trading.auto_trade_manager.TelegramMessageSender')
    @patch('utils.trading.auto_trade_manager.ConfigManager')
    @patch('utils.trading.auto_trade_manager.ChannelRegistry')
    @patch('utils.trading.auto_trade_manager.ChannelSelector')
    @patch('utils.trading.auto_trade_manager.TradeOrchestrator')
    @patch('utils.trading.auto_trade_manager.TradeRecordManager')
    async def test_notification_on_trade_failure(
        self,
        mock_record_manager_class: Mock,
        mock_orchestrator_class: Mock,
        mock_selector_class: Mock,
        mock_registry_class: Mock,
        mock_config_manager_class: Mock,
        mock_telegram_sender_class: Mock
    ) -> None:
        """测试交易失败时的通知功能"""
        # 设置mock类的返回值
        mock_config_manager_class.return_value = self.mock_config_manager
        mock_registry_class.return_value = self.mock_channel_registry
        mock_selector_class.return_value = self.mock_channel_selector
        mock_orchestrator_class.return_value = self.mock_trade_orchestrator
        mock_record_manager_class.return_value = self.mock_trade_record_manager
        
        # 创建mock message sender
        mock_message_sender = AsyncMock(spec=TelegramMessageSender)
        mock_telegram_sender_class.return_value = mock_message_sender
        
        # 配置通知设置
        notification_config = NotificationConfig(
            notify_on_failure=True,
            notify_on_fallback=False,
            admin_chat_ids=["123456789", "987654321"],
            include_trade_details=True
        )
        self.mock_config_manager.get_notification_config = AsyncMock(return_value=notification_config)
        
        # 设置交易失败
        failed_execution_result = TradeExecutionResult(
            final_status=TradeStatus.FAILED,
            successful_channel=None,
            final_trade_record_id=PydanticObjectId(),
            channel_attempts=[
                ChannelAttemptResult(
                    channel_type="gmgn",
                    attempt_number=1,
                    status=TradeStatus.FAILED,
                    error_message="Insufficient funds",
                    execution_time=5.0,
                    started_at=datetime.now()
                )
            ],
            total_execution_time=5.0,
            error_summary="All channels failed",
            started_at=datetime.now(),
            completed_at=datetime.now()
        )
        
        self.mock_trade_orchestrator.execute_trade = AsyncMock(return_value=failed_execution_result)
        
        # 创建Mock交易记录
        mock_trade_record = Mock()
        mock_trade_record.id = PydanticObjectId()
        self.mock_trade_record_manager.create_trade_record = AsyncMock(return_value=mock_trade_record)
        
        # 创建AutoTradeManager
        manager = AutoTradeManager()
        manager.config_manager = self.mock_config_manager
        manager.channel_registry = self.mock_channel_registry
        manager.channel_selector = self.mock_channel_selector
        manager.trade_orchestrator = self.mock_trade_orchestrator
        manager.trade_record_manager = self.mock_trade_record_manager
        manager.message_sender = mock_message_sender
        manager._initialized = True
        
        # 执行交易
        result = await manager.execute_trade(
            trade_type="buy",
            token_in_address="So11111111111111111111111111111111111111112",
            token_out_address="test_token_address",
            amount=0.01,
            signal_id=PydanticObjectId(),
            strategy_name="test_strategy"
        )
        
        # 验证交易失败
        self.assertEqual(result.final_status, TradeStatus.FAILED)
        
        # 验证发送了通知
        self.assertEqual(mock_message_sender.send_message_to_user.call_count, 2)  # 两个管理员
        
        # 验证通知内容
        call_args_list = mock_message_sender.send_message_to_user.call_args_list
        message_content = call_args_list[0][0][0]  # 第一个调用的第一个参数
        
        self.assertIn("🚨 Auto-Trade FAILED 🚨", message_content)
        self.assertIn("test_strategy", message_content)
        self.assertIn("test_token_address", message_content)
        self.assertIn("All channels failed", message_content)
        self.assertIn("Channels attempted: 1", message_content)
        
        # 验证发送给正确的管理员
        chat_ids = [call[0][1] for call in call_args_list]  # 所有调用的第二个参数
        self.assertIn("123456789", chat_ids)
        self.assertIn("987654321", chat_ids)

    @patch('utils.trading.auto_trade_manager.TelegramMessageSender')
    @patch('utils.trading.auto_trade_manager.ConfigManager')
    @patch('utils.trading.auto_trade_manager.ChannelRegistry')
    @patch('utils.trading.auto_trade_manager.ChannelSelector')
    @patch('utils.trading.auto_trade_manager.TradeOrchestrator')
    @patch('utils.trading.auto_trade_manager.TradeRecordManager')
    async def test_notification_on_fallback_success(
        self,
        mock_record_manager_class: Mock,
        mock_orchestrator_class: Mock,
        mock_selector_class: Mock,
        mock_registry_class: Mock,
        mock_config_manager_class: Mock,
        mock_telegram_sender_class: Mock
    ) -> None:
        """测试故障转移成功时的通知功能"""
        # 设置mock类的返回值
        mock_config_manager_class.return_value = self.mock_config_manager
        mock_registry_class.return_value = self.mock_channel_registry
        mock_selector_class.return_value = self.mock_channel_selector
        mock_orchestrator_class.return_value = self.mock_trade_orchestrator
        mock_record_manager_class.return_value = self.mock_trade_record_manager
        
        # 创建mock message sender
        mock_message_sender = AsyncMock(spec=TelegramMessageSender)
        mock_telegram_sender_class.return_value = mock_message_sender
        
        # 配置通知设置
        notification_config = NotificationConfig(
            notify_on_failure=True,
            notify_on_fallback=True,
            admin_chat_ids=["123456789"],
            include_trade_details=True
        )
        self.mock_config_manager.get_notification_config = AsyncMock(return_value=notification_config)
        
        # 设置故障转移成功（第一个渠道失败，第二个成功）
        fallback_execution_result = TradeExecutionResult(
            final_status=TradeStatus.SUCCESS,
            successful_channel="jupiter",
            final_trade_record_id=PydanticObjectId(),
            channel_attempts=[
                ChannelAttemptResult(
                    channel_type="gmgn",
                    attempt_number=1,
                    status=TradeStatus.FAILED,
                    error_message="Network timeout",
                    execution_time=30.0,
                    started_at=datetime.now()
                ),
                ChannelAttemptResult(
                    channel_type="jupiter",
                    attempt_number=2,
                    status=TradeStatus.SUCCESS,
                    tx_hash="5f7d8b9c123",
                    execution_time=15.0,
                    started_at=datetime.now()
                )
            ],
            total_execution_time=45.0,
            error_summary=None,
            started_at=datetime.now(),
            completed_at=datetime.now()
        )
        
        self.mock_trade_orchestrator.execute_trade = AsyncMock(return_value=fallback_execution_result)
        
        # 创建Mock交易记录
        mock_trade_record = Mock()
        mock_trade_record.id = PydanticObjectId()
        self.mock_trade_record_manager.create_trade_record = AsyncMock(return_value=mock_trade_record)
        
        # 创建AutoTradeManager
        manager = AutoTradeManager()
        manager.config_manager = self.mock_config_manager
        manager.channel_registry = self.mock_channel_registry
        manager.channel_selector = self.mock_channel_selector
        manager.trade_orchestrator = self.mock_trade_orchestrator
        manager.trade_record_manager = self.mock_trade_record_manager
        manager.message_sender = mock_message_sender
        manager._initialized = True
        
        # 执行交易
        result = await manager.execute_trade(
            trade_type="buy",
            token_in_address="So11111111111111111111111111111111111111112",
            token_out_address="test_token_address",
            amount=0.01,
            signal_id=PydanticObjectId(),
            strategy_name="test_strategy"
        )
        
        # 验证交易成功
        self.assertEqual(result.final_status, TradeStatus.SUCCESS)
        self.assertEqual(result.successful_channel, "jupiter")
        
        # 验证发送了故障转移通知（故障转移成功）
        self.assertEqual(mock_message_sender.send_message_to_user.call_count, 1)
        
        # 验证通知内容
        call_args = mock_message_sender.send_message_to_user.call_args
        message_content = call_args[0][0]
        
        self.assertIn("⚠️ Auto-Trade: Fallback Success ⚠️", message_content)
        self.assertIn("Failed channels: gmgn", message_content)
        self.assertIn("Successful channel: jupiter", message_content)
        self.assertIn("test_strategy", message_content)

    @patch('utils.trading.auto_trade_manager.TelegramMessageSender')
    @patch('utils.trading.auto_trade_manager.ConfigManager')
    @patch('utils.trading.auto_trade_manager.ChannelRegistry')
    @patch('utils.trading.auto_trade_manager.ChannelSelector')
    @patch('utils.trading.auto_trade_manager.TradeOrchestrator')
    @patch('utils.trading.auto_trade_manager.TradeRecordManager')
    async def test_notification_disabled(
        self,
        mock_record_manager_class: Mock,
        mock_orchestrator_class: Mock,
        mock_selector_class: Mock,
        mock_registry_class: Mock,
        mock_config_manager_class: Mock,
        mock_telegram_sender_class: Mock
    ) -> None:
        """测试通知功能禁用时不发送通知"""
        # 设置mock类的返回值
        mock_config_manager_class.return_value = self.mock_config_manager
        mock_registry_class.return_value = self.mock_channel_registry
        mock_selector_class.return_value = self.mock_channel_selector
        mock_orchestrator_class.return_value = self.mock_trade_orchestrator
        mock_record_manager_class.return_value = self.mock_trade_record_manager
        
        # 创建mock message sender
        mock_message_sender = AsyncMock(spec=TelegramMessageSender)
        mock_telegram_sender_class.return_value = mock_message_sender
        
        # 配置通知设置（禁用失败通知）
        notification_config = NotificationConfig(
            notify_on_failure=False,
            notify_on_fallback=False,
            admin_chat_ids=["123456789"],
            include_trade_details=True
        )
        self.mock_config_manager.get_notification_config = AsyncMock(return_value=notification_config)
        
        # 设置交易失败
        failed_execution_result = TradeExecutionResult(
            final_status=TradeStatus.FAILED,
            successful_channel=None,
            final_trade_record_id=PydanticObjectId(),
            channel_attempts=[],
            total_execution_time=5.0,
            error_summary="All channels failed",
            started_at=datetime.now(),
            completed_at=datetime.now()
        )
        
        self.mock_trade_orchestrator.execute_trade = AsyncMock(return_value=failed_execution_result)
        
        # 创建Mock交易记录
        mock_trade_record = Mock()
        mock_trade_record.id = PydanticObjectId()
        self.mock_trade_record_manager.create_trade_record = AsyncMock(return_value=mock_trade_record)
        
        # 创建AutoTradeManager
        manager = AutoTradeManager()
        manager.config_manager = self.mock_config_manager
        manager.channel_registry = self.mock_channel_registry
        manager.channel_selector = self.mock_channel_selector
        manager.trade_orchestrator = self.mock_trade_orchestrator
        manager.trade_record_manager = self.mock_trade_record_manager
        manager.message_sender = mock_message_sender
        manager._initialized = True
        
        # 执行交易
        result = await manager.execute_trade(
            trade_type="buy",
            token_in_address="So11111111111111111111111111111111111111112",
            token_out_address="test_token_address",
            amount=0.01,
            signal_id=PydanticObjectId(),
            strategy_name="test_strategy"
        )
        
        # 验证交易失败
        self.assertEqual(result.final_status, TradeStatus.FAILED)
        
        # 验证没有发送通知
        mock_message_sender.send_message_to_user.assert_not_called()

    @patch('utils.trading.auto_trade_manager.TelegramMessageSender')
    @patch('utils.trading.auto_trade_manager.ConfigManager')
    @patch('utils.trading.auto_trade_manager.ChannelRegistry')
    @patch('utils.trading.auto_trade_manager.ChannelSelector')
    @patch('utils.trading.auto_trade_manager.TradeOrchestrator')
    @patch('utils.trading.auto_trade_manager.TradeRecordManager')
    async def test_notification_no_admin_chat_ids(
        self,
        mock_record_manager_class: Mock,
        mock_orchestrator_class: Mock,
        mock_selector_class: Mock,
        mock_registry_class: Mock,
        mock_config_manager_class: Mock,
        mock_telegram_sender_class: Mock
    ) -> None:
        """测试没有配置管理员Chat ID时不发送通知"""
        # 设置mock类的返回值
        mock_config_manager_class.return_value = self.mock_config_manager
        mock_registry_class.return_value = self.mock_channel_registry
        mock_selector_class.return_value = self.mock_channel_selector
        mock_orchestrator_class.return_value = self.mock_trade_orchestrator
        mock_record_manager_class.return_value = self.mock_trade_record_manager
        
        # 创建mock message sender
        mock_message_sender = AsyncMock(spec=TelegramMessageSender)
        mock_telegram_sender_class.return_value = mock_message_sender
        
        # 配置通知设置（没有管理员Chat ID）
        notification_config = NotificationConfig(
            notify_on_failure=True,
            notify_on_fallback=True,
            admin_chat_ids=[],  # 空列表
            include_trade_details=True
        )
        self.mock_config_manager.get_notification_config = AsyncMock(return_value=notification_config)
        
        # 设置交易失败
        failed_execution_result = TradeExecutionResult(
            final_status=TradeStatus.FAILED,
            successful_channel=None,
            final_trade_record_id=PydanticObjectId(),
            channel_attempts=[],
            total_execution_time=5.0,
            error_summary="All channels failed",
            started_at=datetime.now(),
            completed_at=datetime.now()
        )
        
        self.mock_trade_orchestrator.execute_trade = AsyncMock(return_value=failed_execution_result)
        
        # 创建Mock交易记录
        mock_trade_record = Mock()
        mock_trade_record.id = PydanticObjectId()
        self.mock_trade_record_manager.create_trade_record = AsyncMock(return_value=mock_trade_record)
        
        # 创建AutoTradeManager
        manager = AutoTradeManager()
        manager.config_manager = self.mock_config_manager
        manager.channel_registry = self.mock_channel_registry
        manager.channel_selector = self.mock_channel_selector
        manager.trade_orchestrator = self.mock_trade_orchestrator
        manager.trade_record_manager = self.mock_trade_record_manager
        manager.message_sender = mock_message_sender
        manager._initialized = True
        
        # 执行交易
        result = await manager.execute_trade(
            trade_type="buy",
            token_in_address="So11111111111111111111111111111111111111112",
            token_out_address="test_token_address",
            amount=0.01,
            signal_id=PydanticObjectId(),
            strategy_name="test_strategy"
        )
        
        # 验证交易失败
        self.assertEqual(result.final_status, TradeStatus.FAILED)
        
        # 验证没有发送通知
        mock_message_sender.send_message_to_user.assert_not_called()

    @patch('utils.trading.auto_trade_manager.TelegramMessageSender')
    @patch('utils.trading.auto_trade_manager.ConfigManager')
    @patch('utils.trading.auto_trade_manager.ChannelRegistry')
    @patch('utils.trading.auto_trade_manager.ChannelSelector')
    @patch('utils.trading.auto_trade_manager.TradeOrchestrator')
    @patch('utils.trading.auto_trade_manager.TradeRecordManager')
    async def test_notification_message_sender_failure(
        self,
        mock_record_manager_class: Mock,
        mock_orchestrator_class: Mock,
        mock_selector_class: Mock,
        mock_registry_class: Mock,
        mock_config_manager_class: Mock,
        mock_telegram_sender_class: Mock
    ) -> None:
        """测试消息发送失败时的处理"""
        # 设置mock类的返回值
        mock_config_manager_class.return_value = self.mock_config_manager
        mock_registry_class.return_value = self.mock_channel_registry
        mock_selector_class.return_value = self.mock_channel_selector
        mock_orchestrator_class.return_value = self.mock_trade_orchestrator
        mock_record_manager_class.return_value = self.mock_trade_record_manager
        
        # 创建mock message sender
        mock_message_sender = AsyncMock(spec=TelegramMessageSender)
        mock_telegram_sender_class.return_value = mock_message_sender
        
        # 设置消息发送失败
        mock_message_sender.send_message_to_user = AsyncMock(side_effect=Exception("Network error"))
        
        # 配置通知设置
        notification_config = NotificationConfig(
            notify_on_failure=True,
            notify_on_fallback=False,
            admin_chat_ids=["123456789"],
            include_trade_details=True
        )
        self.mock_config_manager.get_notification_config = AsyncMock(return_value=notification_config)
        
        # 设置交易失败
        failed_execution_result = TradeExecutionResult(
            final_status=TradeStatus.FAILED,
            successful_channel=None,
            final_trade_record_id=PydanticObjectId(),
            channel_attempts=[],
            total_execution_time=5.0,
            error_summary="All channels failed",
            started_at=datetime.now(),
            completed_at=datetime.now()
        )
        
        self.mock_trade_orchestrator.execute_trade = AsyncMock(return_value=failed_execution_result)
        
        # 创建Mock交易记录
        mock_trade_record = Mock()
        mock_trade_record.id = PydanticObjectId()
        self.mock_trade_record_manager.create_trade_record = AsyncMock(return_value=mock_trade_record)
        
        # 创建AutoTradeManager
        manager = AutoTradeManager()
        manager.config_manager = self.mock_config_manager
        manager.channel_registry = self.mock_channel_registry
        manager.channel_selector = self.mock_channel_selector
        manager.trade_orchestrator = self.mock_trade_orchestrator
        manager.trade_record_manager = self.mock_trade_record_manager
        manager.message_sender = mock_message_sender
        manager._initialized = True
        
        # 执行交易
        # 应该不抛出异常，即使消息发送失败
        result = await manager.execute_trade(
            trade_type="buy",
            token_in_address="So11111111111111111111111111111111111111112",
            token_out_address="test_token_address",
            amount=0.01,
            signal_id=PydanticObjectId(),
            strategy_name="test_strategy"
        )
        
        # 验证交易失败
        self.assertEqual(result.final_status, TradeStatus.FAILED)
        
        # 验证尝试发送了通知
        mock_message_sender.send_message_to_user.assert_called_once()

    async def test_build_notification_message_content(self) -> None:
        """测试通知消息内容构建"""
        manager = AutoTradeManager()
        manager._initialized = True
        
        # 测试失败通知消息
        notification_config = NotificationConfig(
            notify_on_failure=True,
            admin_chat_ids=["123456789"],
            include_trade_details=True
        )
        
        failed_execution_result = TradeExecutionResult(
            final_status=TradeStatus.FAILED,
            successful_channel=None,
            final_trade_record_id=PydanticObjectId(),
            channel_attempts=[
                ChannelAttemptResult(
                    channel_type="gmgn",
                    attempt_number=1,
                    status=TradeStatus.FAILED,
                    error_message="Insufficient funds",
                    execution_time=5.0,
                    started_at=datetime.now()
                )
            ],
            total_execution_time=5.0,
            error_summary="All channels failed",
            started_at=datetime.now(),
            completed_at=datetime.now()
        )
        
        message = await manager._build_notification_message(
            failed_execution_result,
            notification_config,
            signal_id=PydanticObjectId(),
            strategy_name="test_strategy",
            token_address="test_token_address"
        )
        
        # 验证消息内容
        self.assertIn("🚨 Auto-Trade FAILED 🚨", message)
        self.assertIn("test_strategy", message)
        self.assertIn("test_token_address", message)
        self.assertIn("All channels failed", message)
        self.assertIn("Channels attempted: 1", message)
        self.assertIn("Total time: 5.00s", message)
        self.assertIn("gmgn: failed (Insufficient funds)", message)


    @patch('utils.trading.auto_trade_manager.ConfigManager')
    @patch('utils.trading.auto_trade_manager.ChannelRegistry')
    @patch('utils.trading.auto_trade_manager.ChannelSelector')
    @patch('utils.trading.auto_trade_manager.TradeOrchestrator')
    @patch('utils.trading.auto_trade_manager.TradeRecordManager')
    async def test_execute_trade_with_slippage_retry_success(
        self,
        mock_record_manager_class: Mock,
        mock_orchestrator_class: Mock,
        mock_selector_class: Mock,
        mock_registry_class: Mock,
        mock_config_manager_class: Mock
    ) -> None:
        """测试滑点重试成功的交易执行"""
        # 设置mock类的返回值
        mock_config_manager_class.return_value = self.mock_config_manager
        mock_registry_class.return_value = self.mock_channel_registry
        mock_selector_class.return_value = self.mock_channel_selector
        mock_orchestrator_class.return_value = self.mock_trade_orchestrator
        mock_record_manager_class.return_value = self.mock_trade_record_manager
        
        # 创建带滑点调整的成功执行结果
        slippage_adjustments = [
            SlippageAdjustmentRecord(
                old_slippage=1.0,
                new_slippage=1.5,
                reason=SlippageAdjustmentReason.SLIPPAGE_ERROR,
                error_message="slippage tolerance exceeded",
                adjusted_at=datetime.now()
            )
        ]
        
        channel_attempt = ChannelAttemptResult(
            channel_type="gmgn",
            attempt_number=2,
            status=TradeStatus.SUCCESS,
            tx_hash="test_tx_hash_456",
            execution_time=5.2,
            started_at=datetime.now(),
            completed_at=datetime.now(),
            initial_slippage=1.0,
            final_slippage=1.5,
            slippage_adjustments=slippage_adjustments,
            slippage_retry_enabled=True
        )
        
        mock_execution_result = TradeExecutionResult(
            final_status=TradeStatus.SUCCESS,
            successful_channel="gmgn",
            final_trade_record_id=PydanticObjectId(),
            channel_attempts=[channel_attempt],
            total_execution_time=8.5,
            started_at=datetime.now(),
            completed_at=datetime.now()
        )
        
        # 配置mock
        self.mock_trade_orchestrator.execute_with_fallback = AsyncMock(return_value=mock_execution_result)
        
        # 创建AutoTradeManager并执行交易
        manager = AutoTradeManager()
        manager._initialized = True
        
        result = await manager.execute_trade(
            trade_type=ModelTradeType.BUY,
            token_in_address="So11111111111111111111111111111111111111112",
            token_out_address="TokenOut123",
            amount=0.05,
            signal_id=PydanticObjectId(),
            strategy_name="test_strategy"
        )
        
        # 验证结果
        self.assertEqual(result.final_status, TradeStatus.SUCCESS)
        self.assertEqual(result.successful_channel, "gmgn")
        self.assertEqual(len(result.channel_attempts), 1)
        
        # 验证滑点重试信息
        attempt = result.channel_attempts[0]
        self.assertTrue(attempt.slippage_retry_enabled)
        self.assertEqual(attempt.initial_slippage, 1.0)
        self.assertEqual(attempt.final_slippage, 1.5)
        self.assertEqual(len(attempt.slippage_adjustments), 1)
        self.assertEqual(attempt.slippage_adjustments[0].reason, SlippageAdjustmentReason.SLIPPAGE_ERROR)
        
        # 验证trade_orchestrator被正确调用
        self.mock_trade_orchestrator.execute_with_fallback.assert_called_once()
        call_args = self.mock_trade_orchestrator.execute_with_fallback.call_args[1]
        self.assertIn('merged_trading_params', call_args['trade_request'].__dict__)
    
    @patch('utils.trading.auto_trade_manager.ConfigManager')
    @patch('utils.trading.auto_trade_manager.ChannelRegistry')
    @patch('utils.trading.auto_trade_manager.ChannelSelector')
    @patch('utils.trading.auto_trade_manager.TradeOrchestrator')
    @patch('utils.trading.auto_trade_manager.TradeRecordManager')
    async def test_slippage_retry_config_merging(
        self,
        mock_record_manager_class: Mock,
        mock_orchestrator_class: Mock,
        mock_selector_class: Mock,
        mock_registry_class: Mock,
        mock_config_manager_class: Mock
    ) -> None:
        """测试滑点重试配置合并逻辑"""
        # 设置mock类的返回值
        mock_config_manager_class.return_value = self.mock_config_manager
        mock_registry_class.return_value = self.mock_channel_registry
        mock_selector_class.return_value = self.mock_channel_selector
        mock_orchestrator_class.return_value = self.mock_trade_orchestrator
        mock_record_manager_class.return_value = self.mock_trade_record_manager
        
        # 创建简单的成功结果
        mock_execution_result = TradeExecutionResult(
            final_status=TradeStatus.SUCCESS,
            successful_channel="gmgn",
            final_trade_record_id=PydanticObjectId(),
            channel_attempts=[],
            total_execution_time=2.0,
            started_at=datetime.now(),
            completed_at=datetime.now()
        )
        
        self.mock_trade_orchestrator.execute_with_fallback = AsyncMock(return_value=mock_execution_result)
        
        # 创建AutoTradeManager
        manager = AutoTradeManager()
        manager._initialized = True
        
        # 使用运行时滑点重试覆盖
        strategy_trading_overrides = {
            'enable_slippage_retry': True,
            'slippage_increment_percentage': 1.0,
            'max_slippage_percentage': 20.0,
            'retry_delay_seconds': 0.2
        }
        
        await manager.execute_trade(
            trade_type=ModelTradeType.BUY,
            token_in_address="So11111111111111111111111111111111111111112",
            token_out_address="TokenOut123",
            strategy_trading_overrides=strategy_trading_overrides
        )
        
        # 验证trade_orchestrator收到了正确的合并配置
        self.mock_trade_orchestrator.execute_with_fallback.assert_called_once()
        call_args = self.mock_trade_orchestrator.execute_with_fallback.call_args[1]
        trade_request = call_args['trade_request']
        
        # 验证合并后的配置包含运行时覆盖
        merged_params = trade_request.merged_trading_params
        self.assertTrue(merged_params.enable_slippage_retry)
        self.assertEqual(merged_params.slippage_increment_percentage, 1.0)
        self.assertEqual(merged_params.max_slippage_percentage, 20.0)
        self.assertEqual(merged_params.retry_delay_seconds, 0.2)
    
    @patch('utils.trading.auto_trade_manager.ConfigManager')
    @patch('utils.trading.auto_trade_manager.ChannelRegistry')
    @patch('utils.trading.auto_trade_manager.ChannelSelector')
    @patch('utils.trading.auto_trade_manager.TradeOrchestrator')
    @patch('utils.trading.auto_trade_manager.TradeRecordManager')
    async def test_slippage_retry_disabled_execution(
        self,
        mock_record_manager_class: Mock,
        mock_orchestrator_class: Mock,
        mock_selector_class: Mock,
        mock_registry_class: Mock,
        mock_config_manager_class: Mock
    ) -> None:
        """测试禁用滑点重试的交易执行"""
        # 创建禁用滑点重试的配置
        disabled_trading_params = TradingParams(
            default_buy_amount_sol=0.01,
            default_buy_slippage_percentage=1.0,
            enable_slippage_retry=False  # 禁用滑点重试
        )
        
        disabled_channel_config = TradeChannelConfig(
            channel_type="gmgn",
            enabled=True,
            priority=1,
            trading_params=disabled_trading_params
        )
        
        disabled_auto_trade_config = AutoTradeConfig(
            enabled=True,
            wallet_config=self.sample_config.auto_trade.wallet_config,
            channels=[disabled_channel_config],
            default_timeout=120,
            max_total_retries=3,
            notification_config=self.sample_config.auto_trade.notification_config
        )
        
        disabled_config = AutoTradeManagerConfig(auto_trade=disabled_auto_trade_config)
        
        # 设置mock返回禁用滑点重试的配置
        mock_config_manager = Mock(spec=ConfigManager)
        mock_config_manager.get_config = AsyncMock(return_value=disabled_config.auto_trade)
        mock_config_manager.is_enabled = AsyncMock(return_value=True)
        mock_config_manager.get_enabled_channels = AsyncMock(return_value=[disabled_channel_config])
        mock_config_manager.get_wallet_config = AsyncMock(return_value=disabled_config.auto_trade.wallet_config)
        mock_config_manager.get_notification_config = AsyncMock(return_value=disabled_config.auto_trade.notification_config)
        
        # 设置mock类的返回值
        mock_config_manager_class.return_value = mock_config_manager
        mock_registry_class.return_value = self.mock_channel_registry
        mock_selector_class.return_value = self.mock_channel_selector
        mock_orchestrator_class.return_value = self.mock_trade_orchestrator
        mock_record_manager_class.return_value = self.mock_trade_record_manager
        
        # 创建不带滑点调整的成功结果
        channel_attempt = ChannelAttemptResult(
            channel_type="gmgn",
            attempt_number=1,
            status=TradeStatus.SUCCESS,
            tx_hash="test_tx_hash_789",
            execution_time=2.1,
            started_at=datetime.now(),
            completed_at=datetime.now(),
            initial_slippage=1.0,
            final_slippage=1.0,
            slippage_adjustments=[],
            slippage_retry_enabled=False
        )
        
        mock_execution_result = TradeExecutionResult(
            final_status=TradeStatus.SUCCESS,
            successful_channel="gmgn",
            final_trade_record_id=PydanticObjectId(),
            channel_attempts=[channel_attempt],
            total_execution_time=2.5,
            started_at=datetime.now(),
            completed_at=datetime.now()
        )
        
        self.mock_trade_orchestrator.execute_with_fallback = AsyncMock(return_value=mock_execution_result)
        
        # 创建AutoTradeManager并执行交易
        manager = AutoTradeManager()
        manager._initialized = True
        
        result = await manager.execute_trade(
            trade_type=ModelTradeType.BUY,
            token_in_address="So11111111111111111111111111111111111111112",
            token_out_address="TokenOut123"
        )
        
        # 验证滑点重试被禁用
        attempt = result.channel_attempts[0]
        self.assertFalse(attempt.slippage_retry_enabled)
        self.assertEqual(attempt.initial_slippage, attempt.final_slippage)
        self.assertEqual(len(attempt.slippage_adjustments), 0)
        
        # 验证合并后的配置禁用了滑点重试
        call_args = self.mock_trade_orchestrator.execute_with_fallback.call_args[1]
        trade_request = call_args['trade_request']
        merged_params = trade_request.merged_trading_params
        self.assertFalse(merged_params.enable_slippage_retry)

    @patch('utils.trading.auto_trade_manager.ConfigManager')
    @patch('utils.trading.auto_trade_manager.ChannelRegistry')
    @patch('utils.trading.auto_trade_manager.ChannelSelector')
    @patch('utils.trading.auto_trade_manager.TradeOrchestrator')
    @patch('utils.trading.auto_trade_manager.TradeRecordManager')
    async def test_create_gmgn_v2_channel_instance(
        self,
        mock_record_manager_class: Mock,
        mock_orchestrator_class: Mock,
        mock_selector_class: Mock,
        mock_registry_class: Mock,
        mock_config_manager_class: Mock
    ):
        """测试创建GMGN V2渠道实例"""
        # 设置mock类的返回值
        mock_config_manager_class.return_value = self.mock_config_manager
        mock_registry_class.return_value = self.mock_channel_registry
        mock_selector_class.return_value = self.mock_channel_selector
        mock_orchestrator_class.return_value = self.mock_trade_orchestrator
        mock_record_manager_class.return_value = self.mock_trade_record_manager
        
        # 创建AutoTradeManager实例
        manager = AutoTradeManager()
        
        # 准备测试数据
        channel_config = TradeChannelConfig(
            channel_type="gmgn_v2",
            priority=1,
            enabled=True,
            timeout_seconds=60,
            max_retries=3,
            trading_params=TradingParams(),
            channel_params={
                "api_host": "https://gmgn.ai",
                "anti_mev": False,
                "partner": "",
                "max_poll_attempts": 3,
                "poll_interval": 1
            }
        )
        
        # Mock GmgnTradeServiceV2 - 需要mock在实际导入位置
        with patch('utils.trading.solana.gmgn_trade_service_v2.GmgnTradeServiceV2') as mock_gmgn_v2:
            mock_instance = Mock()
            mock_gmgn_v2.return_value = mock_instance
            
            # 调用测试方法
            result = await manager._create_channel_instance(channel_config)
            
            # 验证结果
            self.assertIsNotNone(result)
            self.assertEqual(result, mock_instance)
            mock_gmgn_v2.assert_called_once_with(gmgn_api_host="https://gmgn.ai")
    
    @patch('utils.trading.auto_trade_manager.ConfigManager')
    @patch('utils.trading.auto_trade_manager.ChannelRegistry')
    @patch('utils.trading.auto_trade_manager.ChannelSelector')
    @patch('utils.trading.auto_trade_manager.TradeOrchestrator')
    @patch('utils.trading.auto_trade_manager.TradeRecordManager')
    async def test_create_gmgn_v2_channel_instance_missing_api_host(
        self,
        mock_record_manager_class: Mock,
        mock_orchestrator_class: Mock,
        mock_selector_class: Mock,
        mock_registry_class: Mock,
        mock_config_manager_class: Mock
    ):
        """测试创建GMGN V2渠道实例缺少api_host参数的情况"""
        # 设置mock类的返回值
        mock_config_manager_class.return_value = self.mock_config_manager
        mock_registry_class.return_value = self.mock_channel_registry
        mock_selector_class.return_value = self.mock_channel_selector
        mock_orchestrator_class.return_value = self.mock_trade_orchestrator
        mock_record_manager_class.return_value = self.mock_trade_record_manager
        
        # 创建AutoTradeManager实例
        manager = AutoTradeManager()
        
        # 准备测试数据 - 缺少api_host
        channel_config = TradeChannelConfig(
            channel_type="gmgn_v2",
            priority=1,
            enabled=True,
            timeout_seconds=60,
            max_retries=3,
            trading_params=TradingParams(),
            channel_params={}  # 缺少api_host
        )
        
        # 调用测试方法
        result = await manager._create_channel_instance(channel_config)
        
        # 验证结果 - 应该返回None
        self.assertIsNone(result)

    @patch('utils.trading.auto_trade_manager.ParameterMerger')
    @patch('utils.trading.auto_trade_manager.ConfigManager')
    @patch('utils.trading.auto_trade_manager.ChannelRegistry')
    @patch('utils.trading.auto_trade_manager.ChannelSelector')
    @patch('utils.trading.auto_trade_manager.TradeOrchestrator')
    @patch('utils.trading.auto_trade_manager.TradeRecordManager')
    async def test_parameter_merger_method_called_correctly(
        self,
        mock_record_manager_class: Mock,
        mock_orchestrator_class: Mock,
        mock_selector_class: Mock,
        mock_registry_class: Mock,
        mock_config_manager_class: Mock,
        mock_parameter_merger_class: Mock
    ):
        """测试ParameterMerger的方法调用正确"""
        # 设置mock类的返回值
        mock_config_manager_class.return_value = self.mock_config_manager
        mock_registry_class.return_value = self.mock_channel_registry
        mock_selector_class.return_value = self.mock_channel_selector
        mock_orchestrator_class.return_value = self.mock_trade_orchestrator
        mock_record_manager_class.return_value = self.mock_trade_record_manager
        
        # 创建mock parameter merger实例
        mock_parameter_merger = Mock()
        mock_parameter_merger.merge_trading_params_with_slippage_retry = Mock(return_value=TradingParams())
        mock_parameter_merger_class.return_value = mock_parameter_merger
        
        # 创建成功的交易结果
        execution_result = TradeExecutionResult(
            final_status=TradeStatus.SUCCESS,
            successful_channel="gmgn",
            final_trade_record_id=PydanticObjectId(),
            channel_attempts=[],
            total_execution_time=10.5,
            started_at=datetime.now(),
            completed_at=datetime.now()
        )
        
        self.mock_trade_orchestrator.execute_trade = AsyncMock(return_value=execution_result)
        
        # 创建Mock交易记录
        mock_trade_record = Mock()
        mock_trade_record.id = PydanticObjectId()
        self.mock_trade_record_manager.create_trade_record = AsyncMock(return_value=mock_trade_record)
        
        # 创建AutoTradeManager
        manager = AutoTradeManager()
        manager._initialized = True
        
        # 执行交易
        await manager.execute_trade(
            trade_type="buy",
            token_in_address="So11111111111111111111111111111111111111112",
            token_out_address="test_token_address",
            amount=0.01,
            strategy_trading_overrides={"buy_slippage_percentage": 15.0}
        )
        
        # 验证正确的方法被调用
        mock_parameter_merger.merge_trading_params_with_slippage_retry.assert_called_once()
        call_args = mock_parameter_merger.merge_trading_params_with_slippage_retry.call_args
        
        # 验证参数
        self.assertIsNotNone(call_args[1]['global_params'])  # global_params不为None
        self.assertIsNone(call_args[1]['channel_params'])    # channel_params为None
        self.assertIsNone(call_args[1]['strategy_config'])   # strategy_config为None
        self.assertIsNotNone(call_args[1]['runtime_overrides'])  # runtime_overrides包含策略覆盖
        self.assertIn("buy_slippage_percentage", call_args[1]['runtime_overrides'])


if __name__ == '__main__':
    unittest.main() 