---
description: 项目API的编写规范，在编写或者修改API时需要注意。
globs: 
alwaysApply: false
---
# FastAPI API 开发规范

本文档规定了使用 FastAPI 框架开发 API 时应遵循的规范。

## 核心技术栈

- **框架**: **必须**使用 **FastAPI** 构建 API。
- **数据模型**: **必须**使用 **Pydantic** 定义请求体、查询参数和响应模型。
- **编程模型**: **必须**优先使用 `async/await` 异步编程模型。

## 文件结构

- API 路由实现应放置在 `api/v{版本号}/` 目录下，例如 `api/v1/`。
- 每个主要资源或功能模块对应一个独立的 Python 文件，例如 `api/v1/users_api.py`, `api/v1/token_message_send_history_api.py`。
- 通用的 Pydantic schema（例如标准响应模型、分页参数等）应放置在 `api/v{版本号}/schemas.py` 或类似的共享文件中。

## 路由与命名

- **必须**使用 `APIRouter` 组织相关资源的路由。
- 路由路径应遵循 RESTful 设计原则：
    - 使用名词复数表示资源集合 (e.g., `/users`, `/items`)。
    - 路径参数使用花括号 `{}` (e.g., `/users/{user_id}`)。
- **必须**使用标准的 HTTP 方法表达操作意图：
    - `GET`: 获取资源（列表或单个）。
    - `POST`: 创建新资源。
    - `PUT`: 完整替换（更新）现有资源。
    - `PATCH`: 部分更新现有资源。
    - `DELETE`: 删除资源。
- **必须**为每个 API 端点添加简洁明了的 `summary` 和必要的 `description`，以便在 OpenAPI (Swagger) 文档中清晰展示。

## 请求与响应模型

- **必须**使用 Pydantic 模型严格定义所有请求体和响应体的数据结构。
- **必须**为 Pydantic 模型及其字段添加清晰的 `description`。
- **必须**使用项目定义的标准响应结构 `StandardResponse` (通常位于 `api/v{版本号}/schemas.py`) 作为所有 API 端点的最终响应模型。其结构如下：
    ```python
    class StandardResponse(Generic[T], BaseModel):
        code: int = Field(0, description="状态码，0 表示成功，非 0 表示失败")
        msg: str = Field("success", description="响应消息")
        data: Optional[T] = Field(None, description="响应数据体")
    ```
- 具体的业务数据模型（用于填充 `StandardResponse` 的 `data` 字段）应根据业务需求单独定义。

## 错误处理

- **必须**通过 `StandardResponse` 的 `code` 和 `msg` 字段返回明确的错误信息。
- 应定义一套统一的业务错误码（`code` 非 0 值），并在文档中说明含义。
- **推荐**使用 FastAPI 的异常处理器 (`@app.exception_handler`) 来捕获特定业务异常或通用异常，并统一格式化为 `StandardResponse` 返回。
- 客户端请求验证错误（如路径/查询参数、请求体格式错误），FastAPI 会自动返回 HTTP 422 错误，其响应结构可能与 `StandardResponse` 不同，文档中应予以说明。

## 数据验证

- **必须**充分利用 Pydantic 模型进行请求数据的自动验证（类型、必需性、格式等）。
- **必须**使用 FastAPI 提供的 `Query`, `Path`, `Body` 等工具函数明确声明参数来源，并可添加额外的验证规则 (e.g., `ge`, `le`, `max_length`)。

## API 文档

- **代码内文档**:
    - **必须**为每个 API 端点函数编写清晰的 Python 文档字符串 (docstring)，说明其功能、参数和可能的异常。FastAPI 会使用它生成 OpenAPI 文档。
- **自动生成文档**:
    - FastAPI 会自动生成 OpenAPI schema (`/openapi.json`) 以及交互式文档界面 (Swagger UI at `/docs`, ReDoc at `/redoc`)。
    - **必须**确保路由、模型、参数和文档字符串定义清晰、准确，以生成高质量的自动文档。
- **手动文档 (`docs/apis/`)**:
    - **必须**在 `docs/apis/` 目录下为每个主要 API 端点或资源创建独立的 Markdown 文件 (e.g., `docs/apis/获取信号记录列表（分页）.md`)。
    - 每个文件应提供详细的端点说明、请求/响应示例（包括 `curl` 命令）、错误码列表等。
    - **必须**确保 `docs/apis/` 目录下各文件中的信息与代码实现和自动生成的文档保持严格同步。
    - **必须**在添加新的 API 文档文件后，同步更新 `docs/apis/README.md` 文件中的映射列表。

## 代码风格与依赖

- **必须**遵循 PEP 8 Python 代码风格指南。
- **必须**遵循项目定义的通用规范 (`general.mdc`) 和 Python 特定规范 (`python.mdc`)。
- **推荐**使用 FastAPI 的依赖注入系统 (`Depends`) 来管理数据库连接/会话、DAO 实例或其他共享服务。 