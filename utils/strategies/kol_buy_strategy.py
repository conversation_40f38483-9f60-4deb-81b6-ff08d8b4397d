import asyncio
import time
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from models.token import Token
from utils.common import convert_list_to_dict
from utils.spiders.solana.token_info import TokenInfo
from .base_strategy import BaseStrategy
from dao.kol_wallet_activity_dao import KOLWalletActivityDAO
from dao.token_dao import TokenDAO
from dao.kol_wallet_dao import KOLWalletDAO

logger = logging.getLogger("KOLBuyStrategy")

class KOLBuyStrategy(BaseStrategy):
    """基于KOL买入行为的买入策略
    
    该策略模拟workflows/monitor_kol_activity/handler.py中的filter_target_tokens函数
    用于查找KOL关注的新代币，作为买入信号
    """
    
    def __init__(self, config=None):
        """初始化买入策略
        
        Args:
            config: 策略配置
        """
        config = config or {}
        super().__init__(config)
        self.config = config
    
    async def generate_signals(self, 
                               start_time: int,
                               end_time: int,
                               min_amount: float,
                               kol_account_min_txs: int,
                               kol_account_max_txs: int,
                               min_kol_count: int,
                               token_mint_lookback_hours: int
                               ) -> Dict[str, Any]:
        """生成买入信号 
        回溯时间窗口 = end_time - start_time, 所以买入信号的时间应该是
        
        Args:
            start_time: 时间窗口的开始时间点
            end_time: 时间窗口的结束时间点
            min_amount: 交易的最小金额
            kol_account_min_txs: kol账号的最小交易个数
            kol_account_max_txs: kol账号的最大交易个数
            min_kol_count: 同一个token买入的最小kol账号个数
            token_mint_lookback_hours: 代币铸造时间距今多长时间, 小时
            
        
        Returns:
            List[Dict[str, Any]]: 返回token对应信息和kol列表
        """
        logger.info(f"开始生成买入信号，时间窗口开始时间点: {datetime.fromtimestamp(start_time)}，结束时间点: {datetime.fromtimestamp(end_time)}")
        
        # 使用KOLWalletActivityDAO直接执行aggregate pipeline
        kol_activity_dao = KOLWalletActivityDAO()
        
        # 构建聚合管道 - 使用带 Pipeline 的 $lookup 优化
        pipeline = [
            {
                '$match': {
                    'timestamp': {
                        '$lt': end_time,
                        '$gt': start_time
                    },
                    'event_type': 'buy'
                }
            },
            {
                '$project': {
                    'cost_usd': { '$toDouble': '$cost_usd' },
                    'price_usd': { '$toDouble': '$price_usd' },
                    'token_amount': { '$toDouble': '$token_amount' },
                    'quote_amount': { '$toDouble': '$quote_amount' },
                    'allFields': '$$ROOT'
                }
            }, {
                '$replaceRoot': {
                    'newRoot': {
                        '$mergeObjects': [
                            '$allFields', {
                                'cost_usd': '$cost_usd',
                                'price_usd': '$price_usd',
                                'token_amount': '$token_amount',
                                'quote_amount': '$quote_amount'
                            }
                        ]
                    }
                }
            },
            {
                '$match': {
                    'cost_usd': { '$gt': min_amount }
                }
            },
            {
                '$sort': { 'timestamp': 1 }
            },
            {
                '$group': {
                    '_id': '$token.address',
                    'records': { '$push': '$$ROOT' },
                    'unique_wallets': { '$addToSet': '$wallet' } # 获取参与购买的钱包地址列表
                }
            },
            # --- 使用带 Pipeline 的 $lookup 替代旧的 $lookup + $filter ---
            {
                '$lookup': {
                    'from': 'kol_wallets',
                    'let': { 'wallet_list': '$unique_wallets' }, # 将 unique_wallets 传递给 pipeline
                    'pipeline': [
                        {
                            '$match': {
                                '$expr': { '$in': ['$wallet_address', '$$wallet_list'] } # 匹配 wallet_address
                            }
                        },
                        {
                            '$match': { # 直接在 kol_wallets 集合上进行过滤
                                'tags': { '$in': ['kol'] }, # 检查 'kol' 是否在 tags 数组中
                                'txs': {
                                    '$gte': kol_account_min_txs,
                                    '$lte': kol_account_max_txs
                                }
                            }
                        }
                        # 可以选择性地添加 $project 来只选择必要字段，以减少数据传输
                        # {'$project': {'_id': 0, 'wallet_address': 1}}
                    ],
                    'as': 'kol_wallets' # 直接将过滤后的结果命名为 kol_wallets
                }
            },
            # --- 旧的 $lookup 和 $addFields (含 $filter) 被移除 ---
            {
                '$addFields': {
                    'kol_wallets_count': { '$size': '$kol_wallets' } # 计算过滤后的 kol_wallets 数量
                }
            },
            {
                '$match': {
                    'kol_wallets_count': { '$gte': min_kol_count } # 基于过滤后的数量进行最终匹配
                }
            }
        ]
        
        # 执行聚合查询
        aggregation_results = await kol_activity_dao.aggregate(pipeline)
        logger.info(f"聚合查询完成，找到 {len(aggregation_results)} 个符合条件的代币")
        
        # 对每个代币记录查找达到阈值时的时间点
        for doc in aggregation_results:
            token_address = doc['_id']
            kol_wallets = [wallet.get('wallet_address') for wallet in doc.get('kol_wallets', [])]
            
            # 对每个代币的交易记录按时间排序
            records = doc.get('records', [])
            records.sort(key=lambda x: x.get('timestamp', 0))
            
            # 记录每个KOL钱包的最早交易时间，并跟踪对应的交易记录
            kol_tx_times = {}
            kol_count = 0
            threshold_timestamp = end_time  # 默认使用结束时间
            
            # 按时间顺序遍历所有交易记录
            for record in records:
                wallet = record.get('wallet')
                timestamp = record.get('timestamp')
                
                # 如果是KOL钱包且还未记录过
                if wallet in kol_wallets and wallet not in kol_tx_times:
                    kol_tx_times[wallet] = timestamp
                    kol_count += 1
                    
                    # 如果刚好达到了阈值，记录该时间点
                    if kol_count == min_kol_count:
                        threshold_timestamp = timestamp
                        # 不立即退出循环，而是记录此时的时间
                        logger.info(f"代币 {token_address} 第{min_kol_count}个KOL {wallet} 在时间点 {datetime.fromtimestamp(timestamp)} 买入")
                        break  # 达到了所需数量的KOL，可以退出循环
            
            # 将阈值时间点保存到结果中
            doc['threshold_timestamp'] = threshold_timestamp
            logger.info(f"代币 {token_address} 达到阈值时间点: {datetime.fromtimestamp(threshold_timestamp)}")
        
        # 提取符合条件的代币地址
        kol_bought_tokens = [doc['_id'] for doc in aggregation_results]
        
        # 如果没有找到符合条件的代币，返回空列表
        if len(kol_bought_tokens) == 0:
            return []
        
        # 获取Token信息
        token_dao = TokenDAO()
        
        await self.check_token_saved(kol_bought_tokens, token_dao)
        await self.check_field_exists(kol_bought_tokens, token_dao)
        
        target_tokens = await self.filter_new_tokens(end_time, kol_bought_tokens, token_mint_lookback_hours, token_dao)
        
        # 数据处理，处理成 [{token_address: [kol_wallets: [wallet_address1, wallet_address2], token_info: token}]
        result = {}
        aggregation_results_dict = convert_list_to_dict(aggregation_results, "_id")
        for i in target_tokens:
            token_address = i["address"]
            if token_address in aggregation_results_dict:
                result[token_address] = {
                    "kol_wallets": [kol_wallet["wallet_address"] for kol_wallet in aggregation_results_dict[token_address]["kol_wallets"]],
                    "token_info": i,
                    "start_time": start_time,
                    "end_time": end_time,
                    "threshold_timestamp": aggregation_results_dict[token_address].get("threshold_timestamp", end_time)
                }
        
        # ===== 新增：即时卖出检查，防止买入时间晚于卖出时间 =====
        filtered_result = await self._filter_immediate_sell_conflicts(result, kol_activity_dao)
        
        return filtered_result

    async def _filter_immediate_sell_conflicts(self, buy_signals: Dict[str, Any], kol_activity_dao: KOLWalletActivityDAO) -> Dict[str, Any]:
        """过滤会立即触发卖出信号的买入信号，防止卖出时间早于买入时间
        
        Args:
            buy_signals: 原始买入信号字典
            kol_activity_dao: KOL活动数据访问对象
            
        Returns:
            Dict[str, Any]: 过滤后的买入信号字典
        """
        if not buy_signals:
            return buy_signals
        
        logger.info(f"开始检查 {len(buy_signals)} 个买入信号是否存在即时卖出冲突")
        
        filtered_signals = {}
        
        # 获取卖出策略配置参数
        sell_ratio_threshold = self.config.get("sell_kol_ratio", 0.5)
        transaction_lookback_hours = self.config.get("transaction_lookback_hours", 12)
        sell_strategy_hours = self.config.get("sell_strategy_hours", 24)
        
        for token_address, signal_info in buy_signals.items():
            # 注意：这里的kol_wallets是基于买入信号时间窗口确定的固定KOL列表
            kol_wallets = signal_info.get("kol_wallets", [])
            buy_signal_timestamp = signal_info.get("threshold_timestamp", 0)
            
            if not kol_wallets or buy_signal_timestamp == 0:
                logger.warning(f"代币 {token_address} 缺少必要信息，跳过即时卖出检查")
                filtered_signals[token_address] = signal_info
                continue
            
            # 重要：冲突检测必须基于买入信号时间点的固定窗口
            # 使用与生成买入信号时相同的KOL列表，确保检测结果一致
            sell_check_start_time = buy_signal_timestamp - (transaction_lookback_hours * 3600)
            sell_check_end_time = buy_signal_timestamp  # 只检查到买入信号时间点
            
            # 检查在整个可能的时间窗口内是否会触发卖出信号
            sell_pipeline = [
                {
                    "$match": {
                        "token.address": token_address,
                        "wallet": {"$in": kol_wallets},
                        "event_type": "sell",
                        "timestamp": {"$gte": sell_check_start_time, "$lte": sell_check_end_time}
                    }
                },
                {
                    "$sort": {"timestamp": 1}
                },
                {
                    "$group": {
                        "_id": "$wallet",
                        'first_sell_timestamp': {'$min': '$timestamp'}
                    }
                },
                {
                    "$sort": {
                        "first_sell_timestamp": 1
                    }
                }
            ]
            
            try:
                selling_kols_activity = await kol_activity_dao.aggregate(sell_pipeline)
                
                # 检查是否存在在买入信号时间点之前就已经触发卖出阈值的情况
                has_conflict = False
                conflict_timestamp = None
                
                if len(selling_kols_activity) > 0:
                    # 计算需要多少个KOL卖出才能达到阈值
                    required_sell_count = max(1, int(len(kol_wallets) * sell_ratio_threshold))
                    
                    # 检查前required_sell_count个KOL的卖出情况
                    if len(selling_kols_activity) >= required_sell_count:
                        # 获取达到阈值时的时间戳
                        threshold_kol = selling_kols_activity[required_sell_count - 1]
                        threshold_timestamp = threshold_kol['first_sell_timestamp']
                        
                        # 如果阈值达到时间早于或等于买入信号时间，则存在冲突
                        if threshold_timestamp <= buy_signal_timestamp:
                            has_conflict = True
                            conflict_timestamp = threshold_timestamp
                
                if has_conflict:
                    logger.warning(f"代币 {token_address} 存在即时卖出冲突：买入信号时间 {datetime.fromtimestamp(buy_signal_timestamp)}，"
                                  f"但卖出阈值已在 {datetime.fromtimestamp(conflict_timestamp)} 达到，跳过此买入信号")
                    continue  # 跳过这个买入信号
                else:
                    logger.debug(f"代币 {token_address} 无即时卖出冲突：卖出阈值未在买入信号时间或之前达到")
                    
            except Exception as e:
                logger.error(f"检查代币 {token_address} 即时卖出冲突时出错: {e}，保留此买入信号", exc_info=True)
            
            # 如果没有即时卖出冲突，保留这个买入信号
            filtered_signals[token_address] = signal_info
        
        skipped_count = len(buy_signals) - len(filtered_signals)
        if skipped_count > 0:
            logger.info(f"即时卖出冲突检查完成：跳过了 {skipped_count} 个买入信号，保留了 {len(filtered_signals)} 个")
        else:
            logger.info(f"即时卖出冲突检查完成：所有 {len(filtered_signals)} 个买入信号均通过检查")
        
        return filtered_signals

    async def check_field_exists(self, kol_bought_tokens: list[str], token_dao: TokenDAO):
        """检查kol_bought_tokens是否存在first_mint_time字段,如果不存在则重新获取

        Args:
            kol_bought_tokens (list[str]): kol已经购买的token address列表
            token_dao: TokenDAO
        """
        # 重新获取Token信息
        tokens = await token_dao.find_by_addresses(kol_bought_tokens)
        
        # 如果不存在first_mint_time, 则重新获取
        unfound_first_mint_time_tokens = []
        for token in tokens:
            if not token.get("first_mint_time"):
                unfound_first_mint_time_tokens.append(token["address"])
        if len(unfound_first_mint_time_tokens) > 0:
            tokens = await self.find_tokens_by_address(unfound_first_mint_time_tokens)
            logger.info(f"通过API查找Token信息完成, 找到{len(tokens)}个Token信息")
            # 保存到Tokens表内
            await token_dao.save_tokens(tokens)
    
    async def check_token_saved(self, 
                                kol_bought_tokens: list[str],
                                token_dao: TokenDAO
                                ):
        """检查kol_bought_tokens是否已经存储, 如果在数据库中不存在, 则调用api获取信息并存储

        Args:
            kol_bought_tokens (list[str]): kol已经购买的token address列表
            token_dao: TokenDAO
        """
        # 如果Token Address不再Tokens表内, 则通过API查找Token信息, 并保存到Tokens表内
        
        tokens = await token_dao.find_by_addresses(kol_bought_tokens)
        
        if not tokens:
            # 如果完全没有找到任何Token，直接查找所有Token
            logger.info(f"数据库中未找到任何Token信息，直接查询所有{len(kol_bought_tokens)}个Token")
            ts = await self.find_tokens_by_address(kol_bought_tokens)
            logger.info(f"通过API查找Token信息完成, 找到{len(ts)}个Token信息")
            # 保存到Tokens表内
            await token_dao.save_tokens(ts)
            return
            
        token_addresses = [token["address"] for token in tokens]
        
        if len(token_addresses) != len(kol_bought_tokens):
            # 查找未在Tokens表内的Token Address
            unfound_tokens = list(set(kol_bought_tokens) - set(token_addresses))
            logger.info(f"找到{len(unfound_tokens)}个未在Tokens表内的Token Address, 分别是: {unfound_tokens}")
            # 通过API查找Token信息
            ts = await self.find_tokens_by_address(unfound_tokens)
            logger.info(f"通过API查找Token信息完成, 找到{len(ts)}个Token信息")
            # 保存到Tokens表内
            await token_dao.save_tokens(ts)
    
    @staticmethod
    async def find_tokens_by_address(addresses: list[str]) -> list[Dict]:
        """
        通过API并发查找Token信息
        """
        async def fetch_token(address):
            logger.info(f"开始使用 TokenInfo 查找Token信息: {address}")
            token_info_getter = TokenInfo(address=address) # chain defaults to 'sol'
            token_data = await token_info_getter.get_token_info()
            logger.info(f"TokenInfo 查找Token信息完成 for: {address}, Data: {'Found' if token_data else 'Not Found'}")
            
            final_token_dict = {}
            if token_data:
                final_token_dict = token_data
            else:
                # Fallback logic from the original function
                logger.warning(f"TokenInfo could not find {address}. Using fallback data.")
                final_token_dict = {
                    'symbol': 'UNKNOWN',
                    'name': 'Unknown Token',
                    'decimals': 9,  # 默认精度
                    'created_at': int(time.time())  # 使用当前时间作为创建时间
                }
                
            final_token_dict['address'] = address # Ensure address is always in the returned dict
            return final_token_dict
        
        # 使用asyncio.gather并发执行所有请求
        tokens_results = await asyncio.gather(*[fetch_token(address) for address in addresses])
        
        # tokens_results will be a list of dictionaries, as fetch_token always returns a dict due to fallback.
        # The original filter `[token for token in tokens_results if token]` is redundant here.
        
        # 打印日志以供调试
        logger.debug(f"找到以下Token信息: {tokens_results}")
        
        return tokens_results
    
    async def filter_new_tokens(self, end_time:int, kol_bought_tokens: list[str], token_mint_lookback_hours: int, token_dao: TokenDAO):
        tokens = await token_dao.find_by_addresses(kol_bought_tokens)
        
        # 筛选符合条件的新代币
        target_tokens = []
        for t in tokens:
            # 如果first_mint_time是字符串格式，需要转换为时间戳
            if t["first_mint_time"] and isinstance(t["first_mint_time"], str):
                # 处理ISO格式的日期时间字符串
                # 如果字符串中没有时区信息，添加北京时区+08:00
                if 'Z' in t["first_mint_time"]:
                    # 将Z（UTC时间）替换为北京时区+08:00
                    iso_time = t["first_mint_time"].replace('Z', '+08:00')
                elif '+' not in t["first_mint_time"] and '-' not in t["first_mint_time"][-6:]:
                    # 如果没有时区信息，添加北京时区
                    iso_time = t["first_mint_time"] + '+08:00'
                else:
                    # 已经有时区信息
                    iso_time = t["first_mint_time"]
                    
                # 将ISO格式的日期时间字符串转换为datetime对象
                mint_datetime = datetime.fromisoformat(iso_time)
                # 转换为时间戳
                mint_timestamp = mint_datetime.timestamp()
            elif t["first_mint_time"] and isinstance(t["first_mint_time"], datetime):
                # 如果已经是datetime对象，直接转换为时间戳
                # 注意：这里假设datetime对象已经包含了正确的时区信息
                mint_timestamp = t["first_mint_time"].timestamp()
            else:
                # 如果没有铸造时间信息，跳过该代币
                continue
            
            # 判断是否在指定时间范围内
            if mint_timestamp >= end_time - token_mint_lookback_hours * 3600:
                del t["_id"]
                target_tokens.append(t)
                
        logger.info(f"找到{len(target_tokens)}个符合条件的Token信息, 分别是: {[t['address'] for t in target_tokens]}")
        
        return target_tokens
    