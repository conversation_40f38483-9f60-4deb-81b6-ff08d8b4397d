# Bug修复方案：卖出通知中"Amount Sold"显示不正确

**Bug 标识**: KOL活动监控在处理卖出信号并发送Telegram通知时，当交易成功，消息中的 `Amount Sold` 字段显示的数值未经过所售Token的 `decimals` 正确转换，导致数量显示错误（通常是过大的整数）。

**报告日期/发现日期**: 2025-05-19

**根源分析概要**:
1.  在 `workflows/monitor_kol_activity/sell_signal_handler.py` 的 `process_sell_signal` 函数中，当构建Telegram通知消息时，`template_render_data['trade_result']['actual_amount_in_ui']` (对应 "Amount Sold") 直接使用了 `trade_executed_result.actual_amount_in`。
2.  `trade_executed_result.actual_amount_in` 存储的是实际卖出代币的最小单位数量（例如 lamports）。
3.  代码将其直接格式化为 `.4f`，而没有先除以 `10 ** decimals` (其中 `decimals` 是被卖出代币的小数位数)。
4.  在调用交易执行前，变量 `input_token_decimals_for_sell` 已经正确获取了待售代币的 `decimals`，并且这个值（或者从 `buy_signal.trigger_conditions.get('input_token_decimals')` 获取的等效值）在格式化通知时是可用的。

**详细的、已获批准的修复方案**:

在 `workflows/monitor_kol_activity/sell_signal_handler.py` 文件的 `process_sell_signal` 函数内部，当构造 `template_render_data['trade_result']` 字典时，针对 `actual_amount_in_ui` (Amount Sold) 和 `actual_amount_out_ui` (Amount Received) 进行以下修改：

1.  **获取已售Token的decimals (`decimals_for_sold_token`)**:
    *   从 `buy_signal.trigger_conditions.get('input_token_decimals')` 获取。这个值是在执行卖出交易前，为SPL代币获取的 `decimals` 或为SOL代币固定的 `decimals` (9)，并已注入到买入信号的快照中。

2.  **处理已售Token的数量 (`actual_amount_in_ui`)**:
    *   从 `trade_executed_result.actual_amount_in` 获取原始的最小单位数量。
    *   如果 `trade_executed_result.actual_amount_in` 和 `decimals_for_sold_token` 均有效：
        *   使用 `Decimal` 进行精确计算：`raw_amount_in = Decimal(str(trade_executed_result.actual_amount_in))`
        *   `actual_decimals = int(decimals_for_sold_token)` (确保为整数，并处理可能的负值)。
        *   `divisor_in = Decimal('10') ** actual_decimals`
        *   `adjusted_amount_in = raw_amount_in / divisor_in`
        *   格式化显示：根据 `actual_decimals` 的值格式化字符串，例如 `f"{adjusted_amount_in:.{actual_decimals}f}"`。如果 `actual_decimals` 为0，则格式化为整数。
    *   增加错误处理和日志：如果 `decimals_for_sold_token` 缺失或数值转换失败，记录警告/错误，并回退显示原始值加 `(raw)` 标识或 "N/A"。

3.  **处理收到的SOL数量 (`actual_amount_out_ui`)**:
    *   从 `trade_executed_result.actual_amount_out` 获取原始的最小单位数量。
    *   假定卖出总是得到SOL，SOL固定有9位小数。
    *   使用 `Decimal` 进行精确计算和格式化，显示为带9位小数的SOL数量。
    *   增加错误处理和日志，并在转换失败时回退到简单格式化。

**示例代码片段修改 (已在实际代码中应用)**:
```python
# ... (在 process_sell_signal 函数内, template_render_data['trade_result'] 构造部分) ...
                # --- 处理 Amount Sold (卖出的Token数量) ---
                amount_in_ui = "N/A"
                decimals_for_sold_token = buy_signal.trigger_conditions.get('input_token_decimals')

                if trade_executed_result.actual_amount_in is not None:
                    if decimals_for_sold_token is not None:
                        try:
                            raw_amount_in = Decimal(str(trade_executed_result.actual_amount_in))
                            actual_decimals = int(decimals_for_sold_token)
                            if actual_decimals < 0: # Handle potential invalid negative decimals
                                logger.warning(f"[SellSignal:{new_sell_signal_id}] Invalid negative decimals ({actual_decimals}) for sold token {token_address}. Using 0.")
                                actual_decimals = 0
                            divisor_in = Decimal('10') ** actual_decimals
                            adjusted_amount_in = raw_amount_in / divisor_in
                            if actual_decimals == 0:
                                amount_in_ui = f"{adjusted_amount_in:.0f}"
                            else:
                                amount_in_ui = f"{adjusted_amount_in:.{actual_decimals}f}"
                        except (ValueError, TypeError, ArithmeticError) as e: # Broader exception catch for Decimal
                            logger.error(f"[SellSignal:{new_sell_signal_id}] 无法转换 sold token amount ({trade_executed_result.actual_amount_in}) 或 decimals ({decimals_for_sold_token}) for token {token_address}. Error: {e}", exc_info=True)
                            amount_in_ui = f"{trade_executed_result.actual_amount_in} (raw)"
                    else:
                        logger.warning(f"[SellSignal:{new_sell_signal_id}] Token {token_address} (sold) 缺少 decimals 信息 (from buy_signal.trigger_conditions.input_token_decimals)，无法调整数量显示。")
                        amount_in_ui = f"{trade_executed_result.actual_amount_in} (raw)"
                
                # --- 处理 Amount Received (收到的SOL数量) ---
                amount_out_ui = "N/A" 
                if trade_executed_result.actual_amount_out is not None:
                    sol_decimals = 9 
                    try:
                        raw_amount_out_sol = Decimal(str(trade_executed_result.actual_amount_out))
                        divisor_out_sol = Decimal('10') ** sol_decimals
                        adjusted_amount_out_sol = raw_amount_out_sol / divisor_out_sol
                        amount_out_ui = f"{adjusted_amount_out_sol:.{sol_decimals}f}"
                    except (ValueError, TypeError, ArithmeticError) as e_sol:
                        logger.error(f"[SellSignal:{new_sell_signal_id}] 无法转换 received SOL amount ({trade_executed_result.actual_amount_out}). Error: {e_sol}", exc_info=True)
                        amount_out_ui = f"{trade_executed_result.actual_amount_out:.4f}" # Fallback

                template_render_data['trade_result'] = {
                    # ... other keys
                    'actual_amount_in_ui': amount_in_ui,      # 更新后的 "Amount Sold"
                    'actual_amount_out_ui': amount_out_ui, # 更新后的 "Amount Received"
                }
```

**方案提出者/执行者**: AI Assistant (Gemini 2.5 Pro)
**方案审阅者/批准者**: User (gaojerry)
**方案批准日期**: 2025-05-19 (内容批准), 2025-05-19 (存档执行)

**预期的验证方法**:
1.  代码修改后，模拟或触发一次SPL代币的成功卖出交易。
2.  检查发送到Telegram的卖出成功通知消息。
3.  确认 "Amount Sold" 字段显示的是经过对应SPL代币 `decimals` 调整后的正确数量。
4.  确认 "Amount Received" 字段显示的是经过SOL的9位 `decimals` 调整后的正确数量。 

**单元测试验证**:
为确保此修复的覆盖和未来回归的防止，已在 `test/workflows/monitor_kol_activity/test_sell_signal_handler.py` 中添加了新的单元测试方法 `test_process_sell_signal_formats_notification_amounts_correctly`。该测试专门验证以下场景：
- 模拟一个成功的SPL代币卖出（换取SOL）的完整流程。
- 检查 `TokenInfo` 服务是否被调用以获取SPL代币的 `decimals`。
- 验证买入信号的快照 (`buy_signal.trigger_conditions`) 是否被正确注入了 `input_token_decimals`。
- 验证传递给 `GmgnTradeService.execute_trade` 的卖出数量（UI amount）是基于正确的 `decimals` 计算得出的。
- **核心验证点**: 捕获发送给模拟用户的Telegram通知消息内容，并断言：
    - "Amount Sold" 字段中的SPL代币数量是根据其 `decimals` 正确格式化的 (例如，`123.456789 MYSPL`，若MYSPL有6位小数)。
    - "Amount Received" 字段中的SOL数量是根据SOL固定的9位 `decimals` 正确格式化的 (例如，`0.987654321 SOL`)。
- 同时，该测试也覆盖了相关的数据库操作（如Signal和TradeRecord的创建/更新）以及消息发送历史的记录。 