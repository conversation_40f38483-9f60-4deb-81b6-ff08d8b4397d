from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field
from beanie import PydanticObjectId
from enum import Enum


class TradeStatus(str, Enum):
    """交易状态枚举"""
    PENDING = "pending"
    SUCCESS = "success"
    FAILED = "failed"
    SKIPPED = "skipped"


class ChannelAttemptResult(BaseModel):
    """单个渠道的尝试结果"""
    channel_type: str = Field(..., description="渠道类型")
    attempt_number: int = Field(..., description="尝试次数")
    status: TradeStatus = Field(..., description="尝试状态")
    tx_hash: Optional[str] = Field(None, description="交易哈希")
    error_message: Optional[str] = Field(None, description="错误信息")
    execution_time: float = Field(..., description="执行时间（秒）")
    started_at: datetime = Field(..., description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    
    # 新增实际交易数量字段
    actual_amount_in: Optional[float] = Field(None, description="实际输入数量")
    actual_amount_out: Optional[float] = Field(None, description="实际输出数量")
    
    # 新增：滑点重试相关字段
    initial_buy_slippage: Optional[float] = Field(None, description="初始买入滑点(%)")
    final_buy_slippage: Optional[float] = Field(None, description="最终使用的买入滑点(%)")
    initial_sell_slippage: Optional[float] = Field(None, description="初始卖出滑点(%)")
    final_sell_slippage: Optional[float] = Field(None, description="最终使用的卖出滑点(%)")
    
    slippage_retry_enabled: bool = Field(default=False, description="是否启用了滑点递增重试")
    is_slippage_retry_attempt: bool = Field(default=False, description="是否为滑点递增重试尝试")
    slippage_adjustment_reason: Optional[str] = Field(None, description="滑点调整原因")
    
    # 滑点调整历史（简化版，用于快速查看）
    slippage_adjustments_count: int = Field(default=0, description="滑点调整次数")
    total_slippage_increase: Optional[float] = Field(None, description="累计滑点增加(%)")
    total_slippage_retries: int = Field(default=0, description="总滑点重试次数")
    
    # 兼容性字段 - 用于测试和向后兼容
    slippage_adjustments: List = Field(default_factory=list, description="滑点调整历史记录列表")
    
    @property
    def final_slippage(self) -> Optional[float]:
        """获取最终滑点值 - 兼容性属性"""
        if self.final_buy_slippage is not None:
            return self.final_buy_slippage
        elif self.final_sell_slippage is not None:
            return self.final_sell_slippage
        return None
    
    class Config:
        """Pydantic配置"""
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class TradeExecutionResult(BaseModel):
    """最终交易执行结果"""
    final_status: TradeStatus = Field(..., description="最终交易状态")
    successful_channel: Optional[str] = Field(None, description="成功的渠道类型")
    final_trade_record_id: PydanticObjectId = Field(..., description="最终交易记录ID")
    channel_attempts: List[ChannelAttemptResult] = Field(default_factory=list, description="所有渠道尝试记录")
    total_execution_time: float = Field(..., description="总执行时间（秒）")
    error_summary: Optional[str] = Field(None, description="错误总结")
    started_at: datetime = Field(..., description="开始时间")
    completed_at: datetime = Field(..., description="完成时间")
    
    class Config:
        """Pydantic配置"""
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            PydanticObjectId: str
        } 