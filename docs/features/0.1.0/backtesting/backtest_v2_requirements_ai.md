# 回测模块V2详细需求规格

**生成时间**: 2025-06-05T10:26:12+08:00  
**版本**: 0.1.0  
**模块**: backtesting  
**文档类型**: 需求规格  
**生成方式**: AI生成  

## 项目背景

当前memeMonitor项目已有两个回测模块：
1. **固定步长回测** (`utils/backtest/`) - 基于时间窗口的固定步长回测
2. **事件驱动回测** (`utils/backtest_event_driven/`) - 基于事件流的回测架构

现需要开发**回测模块V2**，采用全新的数据获取和分析方法，完全颠覆现有回测逻辑。

## 核心需求概述

### 1. 数据获取方式革新

**需求描述**: 使用单一MongoDB聚合查询获取完整的回测数据集，替代现有的多步骤数据加载方式。

**具体要求**:
- 使用用户提供的MongoDB聚合管道查询交易数据
- 支持可配置的时间范围参数（如：2025年5月整月）
- 按token地址分组，每组包含符合条件的KOL交易记录
- 内置KOL钱包筛选逻辑（交易数量、标签过滤）
- 内置交易金额筛选逻辑（cost_usd > 指定阈值）

### 2. 信号检测逻辑

**需求描述**: 基于获取的grouped数据，分析每个token的时间序列交易记录，检测买入信号。

**核心算法**:
- 在每个token的records中，寻找连续的`transaction_lookback_hours`时间段
- 该时间段内必须包含至少`kol_account_min_count`个不同的KOL账号进行买入交易
- 当满足条件时，将**最后一个触发账号的交易时间**作为买入信号时间戳

**买入信号额外限制**:
1. **新代币买入记录过滤**: 对每条买入记录，只保留 `买入时间戳 - mint时间戳 <= token_mint_lookback_hours * 3600` 的记录
2. **KOL数量预筛选**: 过滤后立即统计剩余的唯一KOL账号数量，如果 < `kol_account_min_count` 则直接移除该token
3. **买入即卖出过滤**: 在生成买入信号时，检查从`买入时间 - transaction_lookback_hours`到`买入时间`窗口内，买入信号KOL列表中卖出的KOL比例是否达到`sell_kol_ratio`，如果是则跳过该买入信号
4. **重复信号抑制**: 对同一代币，两次买入信号之间必须间隔至少`same_token_notification_interval_minutes`分钟
   - 注意：如果滑动窗口再次满足KOL数量条件，且距离上次信号时间超过间隔限制，应允许产生新的买入信号
   - 无论间隔期间是否有新KOL加入，只要满足时间间隔和KOL数量条件即可触发新信号

**信号时间精度要求**:
- 必须使用实际的KOL交易时间戳，而非模拟时间
- 解决现有事件驱动回测中信号时间戳不准确的问题

### 3. 回测架构简化

**需求描述**: 采用直接分析方法，简化回测流程和组件依赖。

**架构原则**:
- 单一数据查询 + 批量信号分析
- 移除复杂的事件队列和适配器架构
- 直接基于时间序列数据进行向量化计算
- 保持结果分析和可视化能力

## 详细功能需求

### 3.1 数据查询组件 (DataQuery)

**功能概述**: 封装MongoDB聚合查询，提供配置化的数据获取接口。

**输入参数**:
- `backtest_start_time`: 回测开始时间戳
- `backtest_end_time`: 回测结束时间戳  
- `transaction_min_amount`: 最小交易金额阈值（默认500）
- `kol_account_min_txs`: KOL最小交易数量（默认10）
- `kol_account_max_txs`: KOL最大交易数量（默认500）
- `kol_account_min_count`: token最小KOL数量阈值（默认6）
- `token_mint_lookback_hours`: 代币创建时间回溯小时数（默认48）
- `same_token_notification_interval_minutes`: 相同代币通知最小间隔分钟数（默认60）

**输出结果**:
```python
{
    "token_address_1": {
        "records": [  # 按时间排序的交易记录
            {
                "timestamp": int,
                "wallet": str,
                "cost_usd": float,
                "price_usd": float,
                "token_amount": float,
                "event_type": "buy",
                # ... 其他字段
            }
        ],
        "kol_wallets": [  # 符合条件的KOL钱包信息
            {
                "wallet_address": str,
                "txs": int,
                "tags": ["kol"]
            }
        ],
        "kol_wallets_count": int
    }
}
```

### 3.2 信号分析组件 (SignalAnalyzer)

**功能概述**: 分析每个token的交易记录，检测买入信号，并过滤低质量信号。

**核心算法实现**:
1. **滑动窗口分析**: 
   - 对每个token的records按时间戳排序
   - 使用滑动窗口方式检查连续的`transaction_lookback_hours`时间段

2. **KOL计数逻辑**:
   - 在每个时间窗口内，统计唯一KOL账号数量
   - 当唯一账号数 >= `kol_account_min_count`时，触发候选信号

3. **买入即卖出过滤**:
   - 对候选信号，检查同一时间窗口内的KOL卖出情况
   - 计算买入信号KOL列表中卖出的KOL比例
   - 如果卖出比例 >= `sell_kol_ratio`，则跳过该信号

4. **信号时间戳确定**:
   - 记录达到最小KOL数量时的最后一个交易时间戳
   - 作为买入信号的精确触发时间

**输出格式**:
```python
{
    "token_address": str,
    "signal_timestamp": int,  # 买入信号时间戳
    "trigger_kol_count": int,  # 触发时的KOL数量
    "window_start": int,      # 时间窗口开始时间
    "window_end": int,        # 时间窗口结束时间
    "kol_wallets": List[str], # 参与的KOL钱包地址
    "total_volume_usd": float # 窗口内总交易金额
}
```

### 3.3 卖出策略组件 (SellStrategy)

**功能概述**: 基于KOL卖出行为和时间限制，确定最优卖出时机。

**数据获取需求**:
- **预查询策略**: 预先查询整个回测时间段+`sell_strategy_hours`的KOL卖出记录
- **查询时间范围**: `[回测开始时间, 回测结束时间 + sell_strategy_hours * 3600]`
- **数据筛选**: 使用与买入数据相同的KOL条件筛选（交易数量、标签等）

**策略类型**:
1. **KOL比例卖出**: 当买入信号KOL列表中达到`sell_kol_ratio`比例的KOL卖出时触发
2. **超时卖出**: 持仓时间超过`sell_strategy_hours`时强制卖出

**卖出时机判断逻辑**:
- **时间窗口**: 从`买入时间 - transaction_lookback_hours`到数据结束时间
- **KOL卖出统计**: 在买入信号触发的KOL账号列表中查找卖出行为
- **比例计算**: 按时间顺序累积卖出KOL数量，计算占总KOL数量的比例
- **触发条件**: 比例达到`sell_kol_ratio`阈值时返回该时间戳
- **超时保护**: 持仓时间超过`sell_strategy_hours`则使用超时卖出

**时间戳精度要求**:
- KOL比例卖出: 使用达到比例阈值时的实际KOL卖出时间戳
- 超时卖出: 使用买入时间 + `sell_strategy_hours * 3600`

### 3.4 回测执行引擎 (BacktestEngine)

**功能概述**: 统筹整个回测流程，集成各个组件。

**执行流程**:
1. 配置加载和参数验证
2. 调用DataQuery获取数据集
3. 调用SignalAnalyzer检测买入信号
4. 对每个买入信号应用SellStrategy确定卖出点
5. 计算交易收益和回测指标
6. 生成结果报告

### 3.5 结果分析组件 (ResultAnalyzer)

**功能概述**: 保持与现有回测模块兼容的结果分析能力。

**功能要求**:
- 交易统计（胜率、总收益、最大回撤等）
- 收益曲线生成
- 交易记录导出（CSV格式）
- 可视化图表生成

## 配置参数

### 回测时间参数
- `backtest_start_time`: 回测开始时间戳
- `backtest_end_time`: 回测结束时间戳

### 数据筛选参数
- `transaction_min_amount`: 最小交易金额（USD）
- `kol_account_min_txs`: KOL账号最小交易数
- `kol_account_max_txs`: KOL账号最大交易数
- `kol_account_min_count`: 最小KOL账号数量
- `token_mint_lookback_hours`: 代币创建时间回溯小时数

### 策略参数
- `transaction_lookback_hours`: 交易回溯时间窗口（小时）
- `sell_strategy_hours`: 卖出策略时间窗口（小时）
- `sell_kol_ratio`: 卖出KOL比例阈值

### 回测控制参数
- `initial_capital`: 初始资金
- `commission_pct`: 手续费百分比
- `slippage_pct`: 滑点百分比
- `same_token_notification_interval_minutes`: 相同代币通知最小间隔（分钟）

## 性能要求

### 数据处理性能
- 支持月级别时间范围的数据处理（如2025年5月整月）
- 聚合查询执行时间 < 30秒
- 新代币记录过滤 + KOL数量预筛选时间 < 10秒
- 信号分析处理时间 < 60秒（仅针对通过预筛选的token）

### 内存使用
- 合理控制内存使用，避免一次性加载过大数据集
- 支持数据流式处理（如果需要）

### 可扩展性
- 支持多参数网格搜索
- 支持并发回测执行

## 兼容性要求

### 配置兼容性
- 支持现有回测配置格式（YAML/JSON）
- 参数名称与现有回测模块保持一致

### 输出兼容性
- 结果文件格式与现有模块一致
- 支持相同的可视化图表类型

### API兼容性
- 命令行接口与现有回测脚本类似
- 支持单次回测和参数网格搜索模式

## 质量要求

### 数据准确性
- 信号时间戳必须精确到秒级
- 交易计算必须准确无误
- 结果数据可重现

### 代码质量
- 遵循项目代码规范
- 充分的错误处理和日志记录
- 完整的单元测试覆盖

### 用户体验
- 提供清晰的执行进度指示
- 详细的日志输出便于调试
- 友好的错误提示信息

## 验收标准

### 功能验收
1. 能够使用提供的MongoDB聚合查询成功获取数据
2. 买入信号检测算法正确实现
3. 信号时间戳使用实际KOL交易时间
4. 卖出策略正确执行
5. 回测结果计算准确

### 性能验收
1. 月级别数据处理时间在可接受范围内
2. 内存使用合理，无内存泄漏
3. 支持参数网格搜索

### 兼容性验收
1. 与现有配置格式兼容
2. 输出结果格式与现有模块一致
3. 命令行接口友好易用

---

**需求澄清点**:
1. 是否需要支持实时价格数据获取？
2. 是否需要与现有事件驱动回测结果进行对比验证？
3. 是否有特定的性能基准要求？
4. 卖出策略是否需要扩展新的策略类型？ 