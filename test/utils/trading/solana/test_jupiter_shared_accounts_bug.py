import unittest
from unittest.mock import Mock, AsyncMock, patch
import httpx
import json
from utils.trading.solana.jupiter_trade_service import JupiterTradeService


class TestJupiterSharedAccountsBug(unittest.TestCase):
    """测试Jupiter共享账户与简单AMM兼容性Bug"""

    def setUp(self):
        """测试前置设置"""
        self.service = JupiterTradeService(
            jupiter_api_host="https://quote-api.jup.ag",
            solana_rpc_url="https://api.mainnet-beta.solana.com"
        )
        
        # 模拟quote数据
        self.mock_quote_data = {
            "inputMint": "So11111111111111111111111111111111111111112",
            "inAmount": "1000000",
            "outputMint": "3Sv7iA27rRwqkDbffDQpRATv7UsehHY3nWfbf5ENpump",
            "outAmount": "**********",
            "otherAmountThreshold": "**********",
            "swapMode": "ExactIn",
            "slippageBps": 100,
            "platformFee": None,
            "priceImpactPct": "0.01",
            "routePlan": []
        }
        
        self.test_user_public_key = "11111111111111111111111111111111"

    async def test_jupiter_shared_accounts_simple_amm_error_reproduction(self):
        """测试修复前：复现共享账户与简单AMM兼容性错误"""
        # 模拟400错误响应 - 共享账户与简单AMM不兼容
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.text = '{"error": "Simple AMMs are not supported with shared accounts"}'
        mock_response.json.return_value = {"error": "Simple AMMs are not supported with shared accounts"}
        
        # 创建HTTPStatusError
        http_error = httpx.HTTPStatusError(
            message="400 Bad Request",
            request=Mock(),
            response=mock_response
        )
        
        # 模拟http_client.post抛出HTTPStatusError
        with patch.object(self.service, 'http_client') as mock_client:
            mock_client.post.side_effect = http_error
            
            # 应该抛出包含详细错误信息的ValueError
            with self.assertRaises(ValueError) as context:
                await self.service._get_jupiter_swap_transaction(
                    self.mock_quote_data,
                    self.test_user_public_key
                )
            
            # 验证错误信息包含具体的兼容性错误
            error_message = str(context.exception)
            self.assertIn("Jupiter swap API error (400)", error_message)
            self.assertIn("Simple AMMs are not supported with shared accounts", error_message)
            print(f"Bug复现成功，错误信息: {error_message}")

    async def test_jupiter_swap_request_uses_shared_accounts_before_fix(self):
        """测试修复前：验证当前代码确实使用了共享账户"""
        # 模拟成功的响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "swapTransaction": "base64encodedtransaction"
        }
        
        # 捕获实际的请求数据
        captured_request_data = None
        
        async def capture_request(*args, **kwargs):
            nonlocal captured_request_data
            captured_request_data = kwargs.get('json')
            return mock_response
        
        # 模拟http_client.post
        with patch.object(self.service, 'http_client') as mock_client:
            mock_client.post = AsyncMock(side_effect=capture_request)
            
            # 执行请求
            result = await self.service._get_jupiter_swap_transaction(
                self.mock_quote_data,
                self.test_user_public_key
            )
            
            # 验证结果
            self.assertEqual(result, "base64encodedtransaction")
            
            # 验证请求数据中确实使用了共享账户
            self.assertIsNotNone(captured_request_data)
            self.assertTrue(captured_request_data.get('useSharedAccounts'))
            print(f"修复前确实使用共享账户: useSharedAccounts = {captured_request_data.get('useSharedAccounts')}")

    async def test_jupiter_swap_request_disables_shared_accounts_after_fix(self):
        """测试修复后：验证修复后禁用了共享账户"""
        # 这个测试在修复后应该通过
        
        # 模拟成功的响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "swapTransaction": "base64encodedtransaction"
        }
        
        # 捕获实际的请求数据
        captured_request_data = None
        
        async def capture_request(*args, **kwargs):
            nonlocal captured_request_data
            captured_request_data = kwargs.get('json')
            return mock_response
        
        # 模拟http_client.post
        with patch.object(self.service, 'http_client') as mock_client:
            mock_client.post = AsyncMock(side_effect=capture_request)
            
            # 执行请求
            result = await self.service._get_jupiter_swap_transaction(
                self.mock_quote_data,
                self.test_user_public_key
            )
            
            # 验证结果
            self.assertEqual(result, "base64encodedtransaction")
            
            # 验证请求数据中禁用了共享账户
            self.assertIsNotNone(captured_request_data)
            self.assertFalse(captured_request_data.get('useSharedAccounts'))
            print(f"修复后禁用共享账户: useSharedAccounts = {captured_request_data.get('useSharedAccounts')}")

    async def test_jupiter_swap_other_errors_still_work(self):
        """测试修复后：其他类型的错误仍然正常处理"""
        # 模拟其他类型的400错误
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.text = '{"error": "Invalid slippage parameter"}'
        mock_response.json.return_value = {"error": "Invalid slippage parameter"}
        
        # 创建HTTPStatusError
        http_error = httpx.HTTPStatusError(
            message="400 Bad Request",
            request=Mock(),
            response=mock_response
        )
        
        # 模拟http_client.post抛出HTTPStatusError
        with patch.object(self.service, 'http_client') as mock_client:
            mock_client.post.side_effect = http_error
            
            # 应该抛出包含详细错误信息的ValueError
            with self.assertRaises(ValueError) as context:
                await self.service._get_jupiter_swap_transaction(
                    self.mock_quote_data,
                    self.test_user_public_key
                )
            
            # 验证错误信息包含其他错误
            error_message = str(context.exception)
            self.assertIn("Jupiter swap API error (400)", error_message)
            self.assertIn("Invalid slippage parameter", error_message)
            print(f"其他错误正常处理: {error_message}")


if __name__ == '__main__':
    import asyncio
    
    async def run_async_tests():
        """运行异步测试"""
        test_instance = TestJupiterSharedAccountsBug()
        test_instance.setUp()
        
        print("=== 测试Jupiter共享账户与简单AMM兼容性Bug ===")
        
        # 测试Bug复现
        print("\n1. 测试Bug复现...")
        try:
            await test_instance.test_jupiter_shared_accounts_simple_amm_error_reproduction()
            print("✅ Bug复现测试完成")
        except Exception as e:
            print(f"❌ Bug复现测试失败: {e}")
        
        # 测试修复前的行为
        print("\n2. 测试修复前使用共享账户...")
        try:
            await test_instance.test_jupiter_swap_request_uses_shared_accounts_before_fix()
            print("✅ 修复前行为验证完成")
        except Exception as e:
            print(f"❌ 修复前行为验证失败: {e}")
        
        # 测试修复后的行为（这个测试在修复前会失败）
        print("\n3. 测试修复后禁用共享账户...")
        try:
            await test_instance.test_jupiter_swap_request_disables_shared_accounts_after_fix()
            print("✅ 修复后行为验证完成")
        except Exception as e:
            print(f"❌ 修复后行为验证失败: {e}")
        
        # 测试其他错误处理
        print("\n4. 测试其他错误处理...")
        try:
            await test_instance.test_jupiter_swap_other_errors_still_work()
            print("✅ 其他错误处理测试完成")
        except Exception as e:
            print(f"❌ 其他错误处理测试失败: {e}")
    
    # 运行异步测试
    asyncio.run(run_async_tests()) 