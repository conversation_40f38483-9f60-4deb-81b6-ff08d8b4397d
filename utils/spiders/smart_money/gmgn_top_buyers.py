import logging
from typing import Dict, Any
from datetime import datetime
import json
import asyncio
from utils.spiders.smart_money import BasicSpider

logger = logging.getLogger(__name__)

class GmgnTopBuyersSpider(BasicSpider):
    """GMGN代币买家信息爬虫
    
    用于获取代币的买家信息，包括：
    - 持有者数量
    - 持有者状态统计
    - 持有者详细信息
    - 智能钱包统计
    """
    
    BASE_URL = "https://gmgn.ai/defi/quotation/v1/tokens/top_buyers"
    
    def __init__(self, max_retries: int = 3, retry_interval: float = 1.0):
        super().__init__(max_retries=max_retries, retry_interval=retry_interval)
        self.headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-TW;q=0.6',
            'cache-control': 'no-cache',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://gmgn.ai/trade/dDO6ZdYE?chain=sol&tab=pump_smart',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
    
    async def setup(self):
        """设置爬虫，初始化会话并更新头信息"""
        await self.init_sessions()
        # 更新会话头信息
        self.session.headers.update(self.headers)
        # 设置浏览器模拟
        self.session.impersonate = "chrome110"  # 模拟Chrome浏览器
    
    async def get_top_buyers(self, chain: str, address: str) -> Dict[str, Any]:
        """
        获取代币买家信息
        
        Args:
            chain: 链名称
            address: 代币地址
            
        Returns:
            Dict[str, Any]: 代币买家信息
        """
        try:
                
            url = f"{self.BASE_URL}/{chain}/{address}"
            logger.info(f"正在获取代币买家信息: {url}")
            
            # 使用带有重试机制的get方法
            response = await self.get(url, params={
                'device_id': '65990bc3-0a13-4d99-8c66-eeea94916b0f',
                'client_id': 'gmgn_web_2025.0227.180046',
                'from_app': 'gmgn',
                'app_ver': '2025.0227.180046',
                'tz_name': 'Asia/Shanghai',
                'tz_offset': '28800',
                'app_lang': 'zh-CN'
            })
            
            # 记录响应状态和内容
            logger.debug(f"API响应状态码: {response.status_code}")
            logger.debug(f"API响应内容: {response.text[:500]}...")  # 只记录前500个字符
            
            # 检查响应状态
            if response.status_code != 200:
                logger.error(f"获取代币买家信息失败: HTTP {response.status_code}")
                logger.error(f"错误响应: {response.text}")
                return {}
                
            # 解析响应数据
            try:
                data = response.json()
                logger.debug(f"解析后的API响应: {data}")
                
                # 检查API响应格式
                if not isinstance(data, dict):
                    logger.error(f"API响应格式错误，期望dict类型，实际为: {type(data)}")
                    return {}
                    
                # 检查API响应状态
                if data.get('code') != 0:  # 0 表示成功
                    logger.error(f"API返回错误: {data.get('message', '未知错误')}")
                    return {}
                    
                # 提取实际数据
                result = data.get('data', {}).get('holders', {})
                if not result:
                    logger.warning("API响应中没有data.holders字段")
                    return {}
                    
                return result
                
            except json.JSONDecodeError as e:
                logger.error(f"解析API响应JSON失败: {str(e)}")
                logger.error(f"原始响应: {response.text}")
                return {}
                
        except Exception as e:
            logger.error(f"获取代币买家信息时发生错误: {str(e)}")
            return {}
            
    def format_top_buyers(self, buyers_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        格式化代币买家数据
        
        Args:
            buyers_data: 原始代币买家数据
            
        Returns:
            Dict[str, Any]: 格式化后的代币买家数据
        """
        if not buyers_data:
            return {}
            
        try:
            # 记录原始数据用于调试
            logger.debug(f"原始API数据: {buyers_data}")
            
            # API响应数据直接在根级别
            status_now = buyers_data.get('statusNow', {})
            
            formatted_data = {
                # 基本信息
                'chain': buyers_data.get('chain', 'sol'),
                'holder_count': buyers_data.get('holder_count', 0),
                
                # 状态统计
                'status_stats': {
                    'hold': status_now.get('hold', 0),
                    'bought_more': status_now.get('bought_more', 0),
                    'sold_part': status_now.get('sold_part', 0),
                    'sold': status_now.get('sold', 0),
                    'transfered': status_now.get('transfered', 0),
                    'bought_rate': status_now.get('bought_rate', '0'),
                    'holding_rate': status_now.get('holding_rate', '0'),
                    'top_10_holder_rate': status_now.get('top_10_holder_rate', 0)
                },
                
                # 智能钱包统计
                'smart_stats': {
                    'smart_pos': status_now.get('smart_pos', []),
                    'smart_count_hold': status_now.get('smart_count_hold'),
                    'smart_count_bought_more': status_now.get('smart_count_bought_more'),
                    'smart_count_sold_part': status_now.get('smart_count_sold_part'),
                    'smart_count_sold': status_now.get('smart_count_sold'),
                    'smart_count_transfered': status_now.get('smart_count_transfered')
                },
                
                # 变化统计
                'changes': {
                    'sold_diff': buyers_data.get('sold_diff', 0),
                    'sold_part_diff': buyers_data.get('sold_part_diff', 0),
                    'hold_diff': buyers_data.get('hold_diff', 0),
                    'bought_more': buyers_data.get('bought_more', 0)
                },
                
                # 持有者详细信息
                'holders': []
            }
            
            # 处理持有者信息
            holder_info = buyers_data.get('holderInfo', [])
            if isinstance(holder_info, list):
                formatted_data['holders'] = [
                    {
                        'status': holder.get('status', ''),
                        'wallet_address': holder.get('wallet_address', ''),
                        'tags': holder.get('tags', []),
                        'maker_token_tags': holder.get('maker_token_tags', [])
                    }
                    for holder in holder_info
                    if holder.get('wallet_address')  # 只包含有钱包地址的记录
                ]
            
            # 记录格式化后的数据用于调试
            logger.debug(f"格式化后的数据: {formatted_data}")
            
            # 检查holders数据
            if not formatted_data['holders']:
                logger.warning(f"格式化后的holders为空，原始holderInfo数据: {holder_info}")
            else:
                logger.info(f"成功格式化 {len(formatted_data['holders'])} 条holders数据")
            
            return formatted_data
            
        except Exception as e:
            logger.error(f"格式化代币买家数据时发生错误: {str(e)}")
            logger.error(f"原始数据: {buyers_data}")
            return {}

async def main():
    spider = GmgnTopBuyersSpider()
    data = await spider.get_top_buyers("sol", "6p6xgHyF7AeE6TZkSmFsko444wqoP15icUSqi2jfGiPN")
    if data:
        formatted_data = spider.format_top_buyers(data)
        print(json.dumps(formatted_data, indent=2))
    else:
        print("获取代币买家信息失败")
    # 关闭爬虫会话连接
    await spider.close()

if __name__ == "__main__":
    # 使用asyncio运行异步主函数
    asyncio.run(main())
