import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
import json
import random
import asyncio
from utils.spiders.smart_money import BasicSpider

logger = logging.getLogger(__name__)

class GmgnTokenLinksSpider(BasicSpider):
    """GMGN代币相关链接爬虫
    
    用于获取代币的相关链接信息，包括：
    - 社交媒体链接（Twitter、Telegram、Discord等）
    - 官方网站
    - 交易平台链接
    - 验证状态
    - Rug信息
    - 社区投票数据
    """
    
    BASE_URL = "https://gmgn.ai/api/v1/mutil_window_token_link_rug_vote"
    
    def __init__(self, max_retries: int = 3, retry_interval: float = 1.0):
        """初始化爬虫"""
        super().__init__(max_retries=max_retries, retry_interval=retry_interval)
        self.headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-TW;q=0.6',
            'cache-control': 'no-cache',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://gmgn.ai/trade/dDO6ZdYE?chain=sol&tab=pump_smart',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
    
    async def setup(self):
        """设置爬虫，初始化会话并更新头信息"""
        await self.init_sessions()
        # 更新会话头信息
        self.session.headers.update(self.headers)
        # 设置浏览器模拟
        self.session.impersonate = "chrome110"  # 模拟Chrome浏览器
    
    async def get_token_links(self, chain: str, address: str) -> Optional[Dict[str, Any]]:
        """
        获取代币链接信息
        
        Args:
            chain: 链名称
            address: 代币地址
            
        Returns:
            Optional[Dict[str, Any]]: 代币链接信息
        """
        try:
            url = f"{self.BASE_URL}/{chain}/{address}"
            logger.info(f"正在获取代币 {address} 的链接信息: {url}")
            
            # 使用带有重试机制的get方法
            response = await self.get(url, params={
                'device_id': '65990bc3-0a13-4d99-8c66-eeea94916b0f',
                'client_id': 'gmgn_web_2025.0314.194246',
                'from_app': 'gmgn',
                'app_ver': '2025.0227.180046',
                'tz_name': 'Asia/Shanghai',
                'tz_offset': '28800',
                'app_lang': 'zh-CN',
            })
            
            # 记录响应状态和内容
            logger.debug(f"API响应状态码: {response.status_code}")
            logger.debug(f"API响应内容: {response.text[:500]}...")  # 只记录前500个字符
            
            # 检查响应状态
            if response.status_code != 200:
                logger.error(f"获取代币链接信息失败: HTTP {response.status_code}")
                logger.error(f"错误响应: {response.text}")
                return None
                
            # 解析响应数据
            try:
                data = response.json()
                logger.debug(f"解析后的API响应: {data}")
                
                # 检查API响应格式
                if not isinstance(data, dict):
                    logger.error(f"API响应格式错误，期望dict类型，实际为: {type(data)}")
                    return None
                    
                # 检查API响应状态
                if data.get('code') != 0:  # 0 表示成功
                    logger.error(f"API返回错误: {data.get('message', '未知错误')}")
                    return None
                    
                # 提取实际数据
                result = data.get('data', {})
                if not result:
                    logger.warning("API响应中没有data字段")
                    return None
                
                # 构建返回数据结构
                token_links = {
                    "address": address,
                    "chain": chain,
                    "link": result.get('link', {}),
                    "rug": result.get('rug', {}),
                    "vote": result.get('vote', {})
                }
                
                logger.info(f"成功获取代币 {address} 的链接信息")
                return token_links
                
            except json.JSONDecodeError as e:
                logger.error(f"解析API响应JSON失败: {str(e)}")
                logger.error(f"原始响应: {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"获取代币 {address} 的链接信息失败: {str(e)}")
            return None
            
    def format_token_links(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        格式化代币链接数据
        
        Args:
            data: 原始代币链接数据
            
        Returns:
            Dict[str, Any]: 格式化后的代币链接数据
        """
        try:
            if not data:
                return {}
                
            # 获取各部分数据，确保不为None
            link_info = data.get('link', {}) or {}
            rug_info = data.get('rug', {}) or {}
            vote_info = data.get('vote', {}) or {}
            
            # 构建格式化后的数据
            formatted_data = {
                "address": data.get('address', ''),
                "chain": data.get('chain', 'sol'),
                "website": link_info.get('website'),
                "twitter": link_info.get('twitter_username'),
                "telegram": link_info.get('telegram'),
                "discord": link_info.get('discord'),
                "medium": link_info.get('medium'),
                "github": link_info.get('github'),
                "description": link_info.get('description'),
                "gmgn": link_info.get('gmgn'),
                "geckoterminal": link_info.get('geckoterminal'),
                "facebook": link_info.get('facebook'),
                "instagram": link_info.get('instagram'),
                "linkedin": link_info.get('linkedin'),
                "reddit": link_info.get('reddit'),
                "tiktok": link_info.get('tiktok'),
                "youtube": link_info.get('youtube'),
                "verify_status": link_info.get('verify_status', 0),
                "rug_ratio": rug_info.get('rug_ratio', '0'),
                "holder_rugged_num": rug_info.get('holder_rugged_num', 0),
                "holder_token_num": rug_info.get('holder_token_num', 0),
                "like": vote_info.get('like', 0),
                "unlike": vote_info.get('unlike', 0)
            }
            
            return formatted_data
        except Exception as e:
            logger.error(f"格式化代币链接数据时发生错误: {str(e)}")
            logger.error(f"原始数据: {data}")
            return {}
        

async def main():
    spider = GmgnTokenLinksSpider()
    data = await spider.get_token_links("sol", "3BqvN7A3ZofDnhA8eMJkMfHbqVx6Nq8RoMK4WMx5nGtb")
    if data:
        formatted_data = spider.format_token_links(data)
        print(json.dumps(formatted_data, indent=2))
    else:
        print("获取代币链接信息失败")
    # 关闭爬虫会话连接
    await spider.close()

if __name__ == "__main__":
    # 使用asyncio运行异步主函数
    asyncio.run(main())