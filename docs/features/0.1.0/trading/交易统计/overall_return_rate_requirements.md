# 交易统计整体收益率指标增强功能需求规格

## 1. 功能概述

### 1.1 功能描述
为交易统计分析系统增加"整体收益率"指标，用于计算总盈利金额与总投入资金的比值，反映实际的资金利用效率。

### 1.2 业务背景
- 当前的`total_profit_rate`是所有交易盈利率的简单累加，不能反映实际的资金使用效率
- 用户需要了解投入的资金获得了多少实际收益
- 整体收益率 = 总盈利金额 / 总投入金额，这是更直观的投资回报指标

### 1.3 计算公式
```
整体收益率(%) = (总盈利金额 / 总买入金额) × 100
                = (total_profit_amount / total_buy_amount) × 100
```

### 1.4 目标用户
- 需要评估资金使用效率的交易员
- 需要进行投资回报分析的用户
- 进行策略效果评估的管理者

## 2. 功能需求

### FR-001: OverallStats模型增强
- **需求**: 在OverallStats模型中新增`overall_return_rate`字段
- **类型**: `float`，单位为百分比
- **描述**: "整体收益率(%)"
- **默认值**: 0.0

### FR-002: 计算逻辑实现
- **需求**: 在StatisticsCalculator中实现整体收益率计算
- **公式**: `(total_profit_amount / total_buy_amount) × 100`
- **边界处理**: 当`total_buy_amount`为0时，返回0.0

### FR-003: HTML报告显示
- **需求**: 在HTML报告的总体统计部分显示整体收益率
- **位置**: 总体统计卡片区域
- **格式**: "XX.XX%"，保留2位小数

### FR-004: JSON输出支持
- **需求**: 在JSON格式输出中包含整体收益率
- **字段名**: `overall_return_rate`
- **格式**: 数值类型，保留4位小数

### FR-005: 飞书消息支持
- **需求**: 在飞书消息摘要中显示整体收益率
- **显示名**: "整体收益率"
- **格式**: "XX.XX%"

### FR-006: API接口支持
- **需求**: 在统计API的所有相关接口中返回整体收益率
- **包含接口**:
  - `get_detailed_statistics`
  - `get_statistics_summary`
  - Trade Statistics Analyzer的相关方法

### FR-007: 图表支持
- **需求**: 在相关图表中包含整体收益率指标
- **目标图表**: 总体统计柱状图
- **显示方式**: 与其他指标一起显示

## 3. 技术规范

### 3.1 数据模型变更
```python
class OverallStats(BaseModel):
    # ... 现有字段 ...
    overall_return_rate: float = Field(default=0.0, description="整体收益率(%)")
```

### 3.2 计算逻辑
```python
# 在_calculate_overall_stats方法中添加
overall_return_rate = (total_profit_amount / total_buy_amount * 100.0) if total_buy_amount > 0 else 0.0
```

### 3.3 显示格式
- HTML: `{{ "%.2f"|format(stats.overall_stats.overall_return_rate) }}%`
- JSON: 保留4位小数
- 飞书: 保留2位小数

## 4. 验收标准

### 4.1 基本功能
- [ ] OverallStats模型包含`overall_return_rate`字段
- [ ] 计算逻辑正确实现整体收益率公式
- [ ] 边界情况处理正确（零投入金额）

### 4.2 显示功能
- [ ] HTML报告正确显示整体收益率
- [ ] JSON输出包含整体收益率字段
- [ ] 飞书消息显示整体收益率

### 4.3 集成功能
- [ ] 所有相关API接口返回整体收益率
- [ ] 图表功能正常包含新指标
- [ ] 与现有功能兼容，无破坏性变更

### 4.4 数据准确性
- [ ] 计算结果与手工验算一致
- [ ] 不同数据集下计算准确
- [ ] 边界情况处理正确

## 5. 测试用例设计

### 5.1 正常情况测试
```
场景: 正常交易数据
输入: 
  - 交易1: 买入10 SOL, 卖出12 SOL, 盈利2 SOL
  - 交易2: 买入5 SOL, 卖出4 SOL, 亏损1 SOL
预期:
  - total_buy_amount = 15 SOL
  - total_profit_amount = 1 SOL
  - overall_return_rate = (1/15) × 100 = 6.67%
```

### 5.2 边界情况测试
```
场景: 零投入金额
输入: total_buy_amount = 0, total_profit_amount = 0
预期: overall_return_rate = 0.0%

场景: 全部亏损
输入: total_buy_amount = 10, total_profit_amount = -2
预期: overall_return_rate = -20.0%
```

### 5.3 集成测试
- 验证HTML报告显示
- 验证JSON输出格式
- 验证飞书消息内容
- 验证API接口返回

## 6. 实施注意事项

### 6.1 向后兼容性
- 新增字段使用默认值，不影响现有数据
- 现有API保持兼容，仅新增字段

### 6.2 性能考虑
- 计算逻辑简单，无性能影响
- 使用现有的聚合数据，无额外查询

### 6.3 数据精度
- 使用float类型，精度足够
- 显示时适当四舍五入

## 7. 优先级
- **高优先级**: 模型定义、计算逻辑、基本显示
- **中优先级**: HTML报告、API接口
- **低优先级**: 图表功能、飞书消息增强 