"""数据查询组件 - 回测模块V2

封装MongoDB聚合查询，提供配置化的数据获取接口。
使用单一聚合管道查询获取完整的回测数据集。
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

from dao.kol_wallet_activity_dao import KOLWalletActivityDAO
from dao.token_dao import TokenDAO
from utils.backtest_v2.config_manager import BacktestConfigV2

logger = logging.getLogger("DataQueryV2")


class DataQuery:
    """数据查询组件"""
    
    def __init__(self, config: BacktestConfigV2):
        """初始化数据查询组件
        
        Args:
            config: 回测配置对象
        """
        self.config = config
        self.activity_dao = KOLWalletActivityDAO()
        self.token_dao = TokenDAO()
        
        # 从配置中提取参数
        self.transaction_min_amount = config.transaction_min_amount
        self.kol_account_min_txs = config.kol_account_min_txs
        self.kol_account_max_txs = config.kol_account_max_txs
        self.kol_account_min_count = config.kol_account_min_count
        self.token_mint_lookback_hours = config.token_mint_lookback_hours
    
    async def build_buy_data_aggregation_pipeline(self, start_time: int, end_time: int) -> List[Dict[str, Any]]:
        """构建买入数据聚合管道
        
        Args:
            start_time: 开始时间戳
            end_time: 结束时间戳
            
        Returns:
            List[Dict[str, Any]]: MongoDB聚合管道
        """
        pipeline = [
            # 1. 时间和事件类型过滤
            {
                '$match': {
                    'timestamp': {
                        '$gte': start_time,
                        '$lte': end_time
                    },
                    'event_type': "buy"
                }
            },
            # 2. 数据类型转换
            {
                '$project': {
                    'cost_usd': {
                        '$toDouble': "$cost_usd"
                    },
                    'price_usd': {
                        '$toDouble': "$price_usd"
                    },
                    'token_amount': {
                        '$toDouble': "$token_amount"
                    },
                    'quote_amount': {
                        '$toDouble': "$quote_amount"
                    },
                    'allFields': "$$ROOT"
                }
            },
            # 3. 字段合并
            {
                '$replaceRoot': {
                    'newRoot': {
                        '$mergeObjects': [
                            "$allFields",
                            {
                                'cost_usd': "$cost_usd",
                                'price_usd': "$price_usd",
                                'token_amount': "$token_amount",
                                'quote_amount': "$quote_amount"
                            }
                        ]
                    }
                }
            },
            # 4. 交易金额过滤
            {
                '$match': {
                    'cost_usd': {
                        '$gt': self.transaction_min_amount
                    }
                }
            },
            # 5. 时间排序
            {
                '$sort': {
                    'timestamp': 1
                }
            },
            # 6. Token分组
            {
                '$group': {
                    '_id': "$token.address",
                    'records': {
                        '$push': "$$ROOT"
                    },
                    'unique_wallets': {
                        '$addToSet': "$wallet"
                    }
                }
            },
            # 7. KOL钱包关联
            {
                '$lookup': {
                    'from': "kol_wallets",
                    'localField': "unique_wallets",
                    'foreignField': "wallet_address",
                    'as': "matched_kols"
                }
            },
            # 8. KOL条件筛选
            {
                '$addFields': {
                    'kol_wallets': {
                        '$filter': {
                            'input': "$matched_kols",
                            'as': "wallet",
                            'cond': {
                                '$and': [
                                    {
                                        '$in': ["kol", "$$wallet.tags"]
                                    },
                                    {
                                        '$gte': ["$$wallet.txs", self.kol_account_min_txs]
                                    },
                                    {
                                        '$lte': ["$$wallet.txs", self.kol_account_max_txs]
                                    }
                                ]
                            }
                        }
                    }
                }
            },
            # 9. KOL数量统计
            {
                '$addFields': {
                    'kol_wallets_count': {
                        '$size': "$kol_wallets"
                    }
                }
            },
            # 10. Token预筛选
            {
                '$match': {
                    'kol_wallets_count': {
                        '$gte': self.kol_account_min_count
                    }
                }
            },
            # 11. 提取合格KOL钱包地址
            {
                '$addFields': {
                    "qualified_kol_wallet_addresses": {
                        '$map': {
                            'input': "$kol_wallets",
                            'as': "kol",
                            'in': "$$kol.wallet_address"
                        }
                    }
                }
            },
            # 12. 记录过滤
            {
                '$addFields': {
                    "records": {
                        '$filter': {
                            'input': "$records",
                            'as': "record",
                            'cond': {
                                '$in': ["$$record.wallet", "$qualified_kol_wallet_addresses"]
                            }
                        }
                    }
                }
            },
            # 13. 字段清理
            {
                '$project': {
                    "matched_kols": 0,
                    "unique_wallets": 0,
                    "qualified_kol_wallet_addresses": 0
                }
            }
        ]
        
        logger.debug(f"构建买入数据聚合管道，时间范围: {start_time} - {end_time}")
        return pipeline
    
    async def execute_aggregation_query(self, pipeline: List[Dict[str, Any]]) -> Dict[str, Any]:
        """执行聚合查询并返回结果
        
        Args:
            pipeline: MongoDB聚合管道
            
        Returns:
            Dict[str, Any]: 查询结果，按token地址分组
        """
        try:
            logger.info("开始执行MongoDB聚合查询")
            start_time = datetime.now()
            
            # 执行聚合查询
            cursor = self.activity_dao.collection.aggregate(pipeline)
            raw_results = await cursor.to_list(length=None)
            
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            logger.info(f"聚合查询完成，用时: {execution_time:.2f}秒，获得 {len(raw_results)} 个token")
            
            # 转换结果格式
            token_data_map = {}
            for result in raw_results:
                token_address = result['_id']
                token_data_map[token_address] = {
                    'records': result['records'],
                    'kol_wallets': result['kol_wallets'],
                    'kol_wallets_count': result['kol_wallets_count']
                }
            
            # 验证查询结果
            self.validate_query_result(token_data_map)
            
            return token_data_map
            
        except Exception as e:
            logger.error(f"执行聚合查询失败: {e}")
            raise
    
    def validate_query_result(self, result: Dict[str, Any]) -> bool:
        """验证查询结果的数据完整性
        
        Args:
            result: 查询结果
            
        Returns:
            bool: 验证是否通过
        """
        if not isinstance(result, dict):
            raise ValueError("查询结果必须是字典类型")
        
        for token_address, token_data in result.items():
            if not isinstance(token_data, dict):
                raise ValueError(f"Token {token_address} 的数据必须是字典类型")
            
            required_fields = ['records', 'kol_wallets', 'kol_wallets_count']
            for field in required_fields:
                if field not in token_data:
                    raise ValueError(f"Token {token_address} 缺少必需字段: {field}")
            
            # 验证记录格式
            records = token_data['records']
            if not isinstance(records, list):
                raise ValueError(f"Token {token_address} 的records必须是列表类型")
            
            for record in records:
                if not isinstance(record, dict):
                    raise ValueError(f"Token {token_address} 的记录必须是字典类型")
                
                required_record_fields = ['timestamp', 'wallet', 'cost_usd', 'event_type']
                for field in required_record_fields:
                    if field not in record:
                        raise ValueError(f"Token {token_address} 的记录缺少必需字段: {field}")
        
        logger.debug(f"查询结果验证通过，包含 {len(result)} 个token")
        return True

    async def get_token_info(self, token_addresses: List[str]) -> Dict[str, Any]:
        """获取token基础信息

        Args:
            token_addresses: token地址列表

        Returns:
            Dict[str, Any]: token信息映射
        """
        try:
            logger.info(f"获取 {len(token_addresses)} 个token的基础信息")

            # 批量查询token信息
            tokens = await self.token_dao.find_by_addresses(token_addresses)

            # 转换为映射格式
            token_info_map = {}
            for token in tokens:
                token_info_map[token.address] = {
                    'address': token.address,
                    'first_mint_time': token.first_mint_time,
                    'name': getattr(token, 'name', ''),
                    'symbol': getattr(token, 'symbol', '')
                }

            # 检查缺失的token
            missing_tokens = set(token_addresses) - set(token_info_map.keys())
            if missing_tokens:
                logger.warning(f"未找到 {len(missing_tokens)} 个token的信息: {list(missing_tokens)[:5]}...")

            logger.info(f"成功获取 {len(token_info_map)} 个token的基础信息")
            return token_info_map

        except Exception as e:
            logger.error(f"获取token信息失败: {e}")
            raise

    async def filter_new_token_records(self, token_data_map: Dict[str, Any],
                                     token_info_map: Dict[str, Any]) -> Dict[str, Any]:
        """对每个token的交易记录进行新代币时间过滤

        Args:
            token_data_map: token数据映射
            token_info_map: token信息映射

        Returns:
            Dict[str, Any]: 过滤后的token数据
        """
        try:
            logger.info("开始新代币记录过滤和KOL数量预筛选")

            filtered_data = {}
            lookback_seconds = self.token_mint_lookback_hours * 3600

            for token_address, token_data in token_data_map.items():
                # 获取token的mint时间
                token_info = token_info_map.get(token_address)
                if not token_info or not token_info.get('first_mint_time'):
                    logger.warning(f"Token {token_address} 缺少mint时间信息，跳过")
                    continue

                mint_timestamp = token_info['first_mint_time']
                records = token_data['records']

                # 过滤买入记录：只保留在新代币时期内的记录
                filtered_records = []
                for record in records:
                    record_timestamp = record['timestamp']
                    if record_timestamp - mint_timestamp <= lookback_seconds:
                        filtered_records.append(record)

                # 统计过滤后的唯一KOL数量
                unique_kol_wallets = set()
                for record in filtered_records:
                    unique_kol_wallets.add(record['wallet'])

                # KOL数量预筛选：如果唯一KOL数量不足，直接移除该token
                if len(unique_kol_wallets) < self.kol_account_min_count:
                    logger.debug(f"Token {token_address} 过滤后只有 {len(unique_kol_wallets)} 个唯一KOL，少于阈值 {self.kol_account_min_count}，移除")
                    continue

                # 保留该token的过滤后数据
                filtered_data[token_address] = {
                    'records': filtered_records,
                    'kol_wallets': token_data['kol_wallets'],
                    'kol_wallets_count': len(unique_kol_wallets)  # 更新为实际的唯一KOL数量
                }

                logger.debug(f"Token {token_address}: 原始记录 {len(records)} 条，过滤后 {len(filtered_records)} 条，唯一KOL {len(unique_kol_wallets)} 个")

            logger.info(f"新代币记录过滤完成，从 {len(token_data_map)} 个token过滤到 {len(filtered_data)} 个token")
            return filtered_data

        except Exception as e:
            logger.error(f"新代币记录过滤失败: {e}")
            raise
