from datetime import datetime
from beanie import Document, Indexed
from typing import Optional
from pydantic import Field

class SolanaTransaction(Document):
    """Solana 交易记录模型"""
    
    # 基本交易信息
    address: Indexed(str) = Field(description="监控的钱包地址")
    signature: Indexed(str) = Field(description="交易签名（Solana 特有）", unique=True)
    timestamp: datetime = Field(description="交易时间")
    transaction_type: str = Field(description="交易类型（token_transfer/sol_transfer）")
    
    # 代币转账信息
    token_name: Optional[str] = Field(default=None, description="代币名称")
    token_symbol: Optional[str] = Field(default=None, description="代币符号")
    token_mint: Optional[str] = Field(default=None, description="代币的 mint 地址")
    amount_change: float = Field(default=0.0, description="交易数量变化（正数表示买入，负数表示卖出）")
    pre_balance: Optional[float] = Field(default=None, description="交易前余额")
    post_balance: Optional[float] = Field(default=None, description="交易后余额")
    position_impact: Optional[float] = Field(default=None, description="持仓影响（百分比）")
    trade_type: Optional[str] = Field(default=None, description="具体交易类型（首次建仓/大幅加仓等）")
    
    # SOL 特定信息
    is_sol_transfer: bool = Field(default=False, description="是否是 SOL 转账")
    sol_amount: Optional[float] = Field(default=None, description="SOL 转账数量")
    
    # 区块链特定信息
    block_time: int = Field(description="区块时间戳")
    block_slot: Optional[int] = Field(default=None, description="区块槽位")
    
    # 记录元数据
    created_at: datetime = Field(default_factory=datetime.utcnow, description="记录创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="记录更新时间")
    
    class Settings:
        name = "solana_transactions"
        
    class Config:
        json_schema_extra = {
            "example": {
                "address": "2j3MGgjTZnf5woD1dV9XScaSy5SxPeKh5eTTzcpZ142z",
                "signature": "5UK1P8PGxb6EXpBwGPsXQfJ5s7ZFgtP5HxWsXN3VrxKFGYqF1cC4ZsLxh2x6zW7HuRqvzJHEuJhcs5nXGKJbKqYH",
                "timestamp": "2024-02-08T12:00:00",
                "transaction_type": "token_transfer",
                "token_name": "Wrapped SOL",
                "token_symbol": "SOL",
                "token_mint": "So11111111111111111111111111111111111111112",
                "amount_change": -1.5,
                "pre_balance": 10.0,
                "post_balance": 8.5,
                "position_impact": 15.0,
                "trade_type": "中幅减仓",
                "is_sol_transfer": False,
                "block_time": 1707393600,
                "block_slot": 123456789
            }
        }