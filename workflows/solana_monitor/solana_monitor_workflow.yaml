name: 'Solana监控工作流'
description: '定期获取监控地址列表，处理地址的最新交易，并将交易数据存储到数据库'

nodes:
  - name: "SolanaAddressSchedulerNode"
    node_type: "input"
    interval: 10  # 每30秒运行一次
    generate_data: workflows.solana_monitor.handler.generate_monitor_addresses
    flow_control:
      max_pending_messages: 10
      check_interval: 1
      enable_flow_control: true

  - name: "SolanaMonitorNode"
    node_type: "process"
    depend_ons: ["SolanaAddressSchedulerNode"]
    min_request_interval: 1.0  # 最小请求间隔（秒）
    max_request_interval: 5.0  # 最大请求间隔（秒）
    batch_size: 1  # 每批处理的地址数量
    batch_interval: 10.0  # 批次间隔（秒）
    item_interval: 5.0  # 地址处理间隔（秒）
    enable_streaming: true  # 启用流式处理
    process_item: workflows.solana_monitor.handler.process_address_transactions
    flow_control:
      max_pending_messages: 20
      check_interval: 10
      enable_flow_control: true

  - name: "SolanaTransactionStorageNode"
    node_type: "storage"
    depend_ons: ["SolanaMonitorNode"]
    batch_size: 10  # 每批处理10条数据
    store_data: workflows.solana_monitor.handler.store_transaction_data
    validate: workflows.solana_monitor.handler.validate_transaction_data 