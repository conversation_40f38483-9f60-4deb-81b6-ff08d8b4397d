# GMGN v2 API 交易接口测试用例设计

## 1. 测试概述

### 1.1 测试目标
- 验证GMGN v2 API交易接口的功能正确性
- 确保与现有系统的兼容性
- 验证错误处理和边界条件
- 测试性能和并发能力

### 1.2 测试范围
- 单元测试：各个组件的独立功能
- 集成测试：完整的交易流程
- 性能测试：与Node.js版本的对比
- 兼容性测试：与现有系统的集成

### 1.3 测试环境
- 测试网络：Solana Devnet
- Mock服务：模拟GMGN API响应
- 测试钱包：专用测试私钥
- 测试代币：Devnet上的测试代币

## 2. 单元测试用例

### 2.1 路由查询测试

#### 2.1.1 成功获取路由
```python
async def test_get_swap_route_success(self):
    """测试成功获取交易路由"""
    # 输入参数
    input_params = {
        "token_in_address": "So11111111111111111111111111111111111111112",  # SOL
        "token_out_address": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",  # USDC
        "in_amount": "100000000",  # 0.1 SOL
        "from_address": "test_wallet_address",
        "slippage": 0.5,
        "fee": 0.00005
    }
    
    # 预期输出
    expected_response = {
        "code": 0,
        "msg": "success",
        "data": {
            "quote": {
                "inputMint": "So11111111111111111111111111111111111111112",
                "inAmount": "100000000",
                "outputMint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
                "outAmount": "15000000",  # 预期输出数量
                "slippageBps": 50
            },
            "raw_tx": {
                "swapTransaction": "base64_encoded_transaction",
                "lastValidBlockHeight": 123456789
            }
        }
    }
    
    # 验证点
    assert response["code"] == 0
    assert "quote" in response["data"]
    assert "raw_tx" in response["data"]
    assert response["data"]["quote"]["inAmount"] == "100000000"
```

#### 2.1.2 无效代币地址
```python
async def test_get_swap_route_invalid_token(self):
    """测试无效代币地址的处理"""
    # 输入参数（无效的代币地址）
    input_params = {
        "token_in_address": "invalid_token_address",
        "token_out_address": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
        "in_amount": "100000000",
        "from_address": "test_wallet_address",
        "slippage": 0.5,
        "fee": 0.00005
    }
    
    # 预期异常或错误响应
    with pytest.raises(ValueError) or assert response["code"] != 0
```

#### 2.1.3 滑点过高
```python
async def test_get_swap_route_high_slippage(self):
    """测试滑点过高的情况"""
    # 输入参数（极低滑点）
    input_params = {
        "token_in_address": "So11111111111111111111111111111111111111112",
        "token_out_address": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
        "in_amount": "100000000",
        "from_address": "test_wallet_address",
        "slippage": 0.01,  # 极低滑点
        "fee": 0.00005
    }
    
    # 验证滑点相关错误的识别
    assert service.is_slippage_related_error(error_message, response)
```

### 2.2 交易签名测试

#### 2.2.1 成功签名交易
```python
def test_sign_transaction_success(self):
    """测试成功签名交易"""
    # 输入参数
    swap_transaction_b64 = "valid_base64_transaction"
    private_key_b58 = "test_private_key_base58"
    
    # 执行签名
    signed_tx = service._sign_transaction(swap_transaction_b64, private_key_b58)
    
    # 验证点
    assert isinstance(signed_tx, str)
    assert len(signed_tx) > 0
    # 验证base64格式
    import base64
    try:
        base64.b64decode(signed_tx)
        assert True
    except Exception:
        assert False, "Signed transaction is not valid base64"
```

#### 2.2.2 无效私钥
```python
def test_sign_transaction_invalid_private_key(self):
    """测试无效私钥的处理"""
    # 输入参数（无效私钥）
    swap_transaction_b64 = "valid_base64_transaction"
    private_key_b58 = "invalid_private_key"
    
    # 预期异常
    with pytest.raises(Exception):
        service._sign_transaction(swap_transaction_b64, private_key_b58)
```

#### 2.2.3 无效交易数据
```python
def test_sign_transaction_invalid_transaction(self):
    """测试无效交易数据的处理"""
    # 输入参数（无效交易数据）
    swap_transaction_b64 = "invalid_base64_data"
    private_key_b58 = "valid_private_key_base58"
    
    # 预期异常
    with pytest.raises(Exception):
        service._sign_transaction(swap_transaction_b64, private_key_b58)
```

### 2.3 交易提交测试

#### 2.3.1 成功提交交易
```python
async def test_submit_transaction_success(self):
    """测试成功提交交易"""
    # 输入参数
    signed_tx = "valid_signed_transaction_base64"
    is_anti_mev = False
    
    # 预期响应
    expected_response = {
        "code": 0,
        "msg": "success",
        "data": {
            "hash": "transaction_hash_string",
            "resArr": [
                {
                    "hash": "transaction_hash_string",
                    "err": None
                }
            ]
        }
    }
    
    # 验证点
    assert response["code"] == 0
    assert "hash" in response["data"]
    assert response["data"]["hash"] is not None
```

#### 2.3.2 交易提交失败
```python
async def test_submit_transaction_failure(self):
    """测试交易提交失败的处理"""
    # 输入参数（无效的签名交易）
    signed_tx = "invalid_signed_transaction"
    
    # 预期错误响应
    assert response["code"] != 0 or "error" in response
```

### 2.4 状态监控测试

#### 2.4.1 交易成功确认
```python
async def test_monitor_transaction_success(self):
    """测试交易成功确认"""
    # 输入参数
    tx_hash = "valid_transaction_hash"
    last_valid_height = 123456789
    
    # Mock成功响应
    mock_response = {
        "code": 0,
        "msg": "success",
        "data": {
            "success": True,
            "expired": False,
            "failed": False
        }
    }
    
    # 验证点
    result = await service._monitor_transaction_status(tx_hash, last_valid_height)
    assert result["data"]["success"] is True
    assert result["data"]["expired"] is False
```

#### 2.4.2 交易过期
```python
async def test_monitor_transaction_expired(self):
    """测试交易过期的处理"""
    # 输入参数
    tx_hash = "expired_transaction_hash"
    last_valid_height = 123456789
    
    # Mock过期响应
    mock_response = {
        "code": 0,
        "msg": "success",
        "data": {
            "success": False,
            "expired": True,
            "failed": False
        }
    }
    
    # 验证点
    result = await service._monitor_transaction_status(tx_hash, last_valid_height)
    assert result["data"]["expired"] is True
    assert result["data"]["success"] is False
```

#### 2.4.3 监控超时
```python
async def test_monitor_transaction_timeout(self):
    """测试监控超时的处理"""
    # 输入参数
    tx_hash = "pending_transaction_hash"
    last_valid_height = 123456789
    max_attempts = 2  # 减少等待时间
    poll_interval = 1
    
    # Mock一直pending的响应
    mock_response = {
        "code": 0,
        "msg": "success", 
        "data": {
            "success": False,
            "expired": False,
            "failed": False
        }
    }
    
    # 验证超时处理
    result = await service._monitor_transaction_status(
        tx_hash, last_valid_height, max_attempts, poll_interval
    )
    assert result["data"]["expired"] is True
```

### 2.5 错误处理测试

#### 2.5.1 滑点错误识别
```python
def test_is_slippage_related_error(self):
    """测试滑点相关错误的识别"""
    # 测试用例
    test_cases = [
        ("route failed", True),
        ("swap failed", True),
        ("price change", True),
        ("market impact", True),
        ("liquidity insufficient", True),
        ("output amount too low", True),
        ("insufficient output amount", True),
        ("price moved too much", True),
        ("slippage exceeded", True),
        ("price impact too high", True),
        ("insufficient funds", False),  # 非滑点错误
        ("invalid token", False),  # 非滑点错误
        ("", False),  # 空错误消息
        (None, False)  # None错误消息
    ]
    
    for error_message, expected in test_cases:
        result = service.is_slippage_related_error(error_message)
        assert result == expected, f"Failed for: {error_message}"
```

#### 2.5.2 不可重试错误识别
```python
def test_is_non_retryable_error(self):
    """测试不可重试错误的识别"""
    # 测试用例
    test_cases = [
        ("invalid wallet", True),
        ("private key invalid", True),
        ("token not supported", True),
        ("invalid token address", True),
        ("api key invalid", True),
        ("unauthorized access", True),
        ("invalid parameters", True),
        ("amount too small", True),
        ("amount too large", True),
        ("insufficient funds", True),  # 通用不可重试错误
        ("network error", False),  # 可重试错误
        ("timeout", False),  # 可重试错误
        ("", False),  # 空错误消息
        (None, False)  # None错误消息
    ]
    
    for error_message, expected in test_cases:
        result = service.is_non_retryable_error(error_message)
        assert result == expected, f"Failed for: {error_message}"
```

## 3. 集成测试用例

### 3.1 完整交易流程测试

#### 3.1.1 成功的买入交易
```python
async def test_complete_buy_trade_success(self):
    """测试完整的买入交易流程"""
    # 输入参数
    trade_params = {
        "trade_type": TradeType.BUY,
        "input_token_address": "So11111111111111111111111111111111111111112",  # SOL
        "output_token_address": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",  # USDC
        "amount_input_token": 0.1,  # 0.1 SOL
        "wallet_private_key_b58": "test_private_key",
        "wallet_address": "test_wallet_address",
        "strategy_snapshot": {
            "gmgn_buy_slippage_percentage": 0.5,
            "gmgn_buy_priority_fee": 0.00005,
            "gmgn_anti_mev": False
        },
        "signal_id": ObjectId(),
        "trade_record_id": ObjectId()
    }
    
    # 执行交易
    result = await service.execute_trade(**trade_params)
    
    # 验证点
    assert result.status == TradeStatus.SUCCESS
    assert result.tx_hash is not None
    assert result.actual_amount_in is not None
    assert result.actual_amount_out is not None
    assert result.error_message is None
    assert result.executed_at is not None
```

#### 3.1.2 成功的卖出交易
```python
async def test_complete_sell_trade_success(self):
    """测试完整的卖出交易流程"""
    # 输入参数
    trade_params = {
        "trade_type": TradeType.SELL,
        "input_token_address": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",  # USDC
        "output_token_address": "So11111111111111111111111111111111111111112",  # SOL
        "amount_input_token": 15.0,  # 15 USDC
        "wallet_private_key_b58": "test_private_key",
        "wallet_address": "test_wallet_address",
        "strategy_snapshot": {
            "gmgn_sell_slippage_percentage": 1.0,
            "gmgn_sell_priority_fee": 0.0001,
            "input_token_decimals": 6,  # USDC decimals
            "gmgn_anti_mev": True
        },
        "signal_id": ObjectId(),
        "trade_record_id": ObjectId()
    }
    
    # 执行交易
    result = await service.execute_trade(**trade_params)
    
    # 验证点
    assert result.status == TradeStatus.SUCCESS
    assert result.tx_hash is not None
    assert result.actual_amount_in is not None
    assert result.actual_amount_out is not None
```

#### 3.1.3 滑点错误处理
```python
async def test_trade_slippage_error_handling(self):
    """测试滑点错误的处理"""
    # 输入参数（极低滑点，容易失败）
    trade_params = {
        "trade_type": TradeType.BUY,
        "input_token_address": "So11111111111111111111111111111111111111112",
        "output_token_address": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
        "amount_input_token": 1.0,  # 较大金额
        "wallet_private_key_b58": "test_private_key",
        "wallet_address": "test_wallet_address",
        "strategy_snapshot": {
            "gmgn_buy_slippage_percentage": 0.01,  # 极低滑点
            "gmgn_buy_priority_fee": 0.00005
        },
        "signal_id": ObjectId(),
        "trade_record_id": ObjectId()
    }
    
    # Mock滑点错误响应
    mock_error_response = {
        "code": 1,
        "msg": "route failed due to insufficient output amount",
        "data": None
    }
    
    # 执行交易
    result = await service.execute_trade(**trade_params)
    
    # 验证滑点错误识别
    assert result.status == TradeStatus.FAILED
    assert service.is_slippage_related_error(result.error_message, result.provider_response_raw)
    assert not service.is_non_retryable_error(result.error_message, result.provider_response_raw)
```

### 3.2 边界条件测试

#### 3.2.1 最小交易金额
```python
async def test_minimum_trade_amount(self):
    """测试最小交易金额"""
    # 输入参数（最小金额）
    trade_params = {
        "trade_type": TradeType.BUY,
        "input_token_address": "So11111111111111111111111111111111111111112",
        "output_token_address": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
        "amount_input_token": 0.000001,  # 极小金额
        "wallet_private_key_b58": "test_private_key",
        "wallet_address": "test_wallet_address",
        "strategy_snapshot": {
            "gmgn_buy_slippage_percentage": 1.0,
            "gmgn_buy_priority_fee": 0.00005
        },
        "signal_id": ObjectId(),
        "trade_record_id": ObjectId()
    }
    
    # 执行交易
    result = await service.execute_trade(**trade_params)
    
    # 验证处理结果（可能成功或失败，但不应崩溃）
    assert result.status in [TradeStatus.SUCCESS, TradeStatus.FAILED]
    if result.status == TradeStatus.FAILED:
        assert result.error_message is not None
```

#### 3.2.2 网络错误重试
```python
async def test_network_error_retry(self):
    """测试网络错误的重试机制"""
    # Mock网络错误
    with patch('httpx.AsyncClient.get') as mock_get:
        mock_get.side_effect = [
            httpx.ConnectError("Connection failed"),  # 第一次失败
            httpx.ConnectError("Connection failed"),  # 第二次失败
            Mock(status_code=200, json=lambda: {"code": 0, "data": {}})  # 第三次成功
        ]
        
        # 执行交易
        result = await service.execute_trade(**standard_trade_params)
        
        # 验证重试机制
        assert mock_get.call_count == 3
        assert result.status == TradeStatus.SUCCESS
```

## 4. 性能测试用例

### 4.1 响应时间测试
```python
async def test_trade_response_time(self):
    """测试交易响应时间"""
    import time
    
    start_time = time.time()
    result = await service.execute_trade(**standard_trade_params)
    end_time = time.time()
    
    execution_time = end_time - start_time
    
    # 验证响应时间在30秒内
    assert execution_time < 30.0, f"Trade took {execution_time:.2f} seconds"
    assert result.status in [TradeStatus.SUCCESS, TradeStatus.FAILED]
```

### 4.2 并发交易测试
```python
async def test_concurrent_trades(self):
    """测试并发交易处理"""
    import asyncio
    
    # 创建多个并发交易
    trade_tasks = []
    for i in range(5):
        trade_params = {
            **standard_trade_params,
            "trade_record_id": ObjectId(),
            "amount_input_token": 0.01 * (i + 1)  # 不同金额
        }
        task = service.execute_trade(**trade_params)
        trade_tasks.append(task)
    
    # 并发执行
    results = await asyncio.gather(*trade_tasks, return_exceptions=True)
    
    # 验证所有交易都有结果
    assert len(results) == 5
    for result in results:
        assert not isinstance(result, Exception)
        assert result.status in [TradeStatus.SUCCESS, TradeStatus.FAILED]
```

### 4.3 内存使用测试
```python
def test_memory_usage(self):
    """测试内存使用情况"""
    import psutil
    import os
    
    process = psutil.Process(os.getpid())
    initial_memory = process.memory_info().rss
    
    # 执行多次交易
    for i in range(10):
        result = await service.execute_trade(**standard_trade_params)
    
    final_memory = process.memory_info().rss
    memory_increase = final_memory - initial_memory
    
    # 验证内存增长在合理范围内（例如小于100MB）
    assert memory_increase < 100 * 1024 * 1024, f"Memory increased by {memory_increase / 1024 / 1024:.2f} MB"
```

## 5. 兼容性测试用例

### 5.1 与现有系统集成测试
```python
async def test_integration_with_existing_system(self):
    """测试与现有交易系统的集成"""
    # 使用现有的配置格式
    existing_strategy_snapshot = {
        "gmgn_buy_slippage_percentage": 0.5,
        "gmgn_buy_priority_fee": 0.00005,
        "gmgn_sell_slippage_percentage": 1.0,
        "gmgn_sell_priority_fee": 0.0001,
        "input_token_decimals": 6
    }
    
    # 执行交易
    result = await service.execute_trade(
        trade_type=TradeType.BUY,
        input_token_address="So11111111111111111111111111111111111111112",
        output_token_address="EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
        amount_input_token=0.1,
        wallet_private_key_b58="test_private_key",
        wallet_address="test_wallet_address",
        strategy_snapshot=existing_strategy_snapshot,
        signal_id=ObjectId(),
        trade_record_id=ObjectId()
    )
    
    # 验证返回格式与现有系统兼容
    assert isinstance(result, TradeResult)
    assert hasattr(result, 'status')
    assert hasattr(result, 'tx_hash')
    assert hasattr(result, 'error_message')
    assert hasattr(result, 'provider_response_raw')
    assert hasattr(result, 'actual_amount_in')
    assert hasattr(result, 'actual_amount_out')
    assert hasattr(result, 'executed_at')
```

### 5.2 配置向后兼容测试
```python
def test_config_backward_compatibility(self):
    """测试配置的向后兼容性"""
    # 旧版本配置格式
    old_config = {
        "gmgn_api_host": "https://gmgn.ai",
        "gmgn_buy_slippage_percentage": 0.5,
        "gmgn_buy_priority_fee": 0.00005
        # 缺少新的v2配置项
    }
    
    # 创建服务实例
    service = GmgnTradeServiceV2(gmgn_api_host=old_config["gmgn_api_host"])
    
    # 验证服务可以正常初始化
    assert service.gmgn_api_host == "https://gmgn.ai"
    assert service.http_client is not None
```

## 6. 测试数据和Mock

### 6.1 测试数据定义
```python
# 标准测试参数
STANDARD_TRADE_PARAMS = {
    "trade_type": TradeType.BUY,
    "input_token_address": "So11111111111111111111111111111111111111112",
    "output_token_address": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
    "amount_input_token": 0.1,
    "wallet_private_key_b58": "test_private_key_base58",
    "wallet_address": "test_wallet_address",
    "strategy_snapshot": {
        "gmgn_buy_slippage_percentage": 0.5,
        "gmgn_buy_priority_fee": 0.00005,
        "gmgn_anti_mev": False
    },
    "signal_id": ObjectId(),
    "trade_record_id": ObjectId()
}

# Mock API响应
MOCK_ROUTE_RESPONSE = {
    "code": 0,
    "msg": "success",
    "data": {
        "quote": {
            "inputMint": "So11111111111111111111111111111111111111112",
            "inAmount": "100000000",
            "outputMint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
            "outAmount": "15000000",
            "slippageBps": 50
        },
        "raw_tx": {
            "swapTransaction": "mock_base64_transaction",
            "lastValidBlockHeight": 123456789
        }
    }
}

MOCK_SUBMIT_RESPONSE = {
    "code": 0,
    "msg": "success",
    "data": {
        "hash": "mock_transaction_hash",
        "resArr": [{"hash": "mock_transaction_hash", "err": None}]
    }
}

MOCK_STATUS_SUCCESS_RESPONSE = {
    "code": 0,
    "msg": "success",
    "data": {
        "success": True,
        "expired": False,
        "failed": False
    }
}
```

### 6.2 Mock设置
```python
@pytest.fixture
def mock_gmgn_api():
    """Mock GMGN API响应"""
    with patch('httpx.AsyncClient.get') as mock_get, \
         patch('httpx.AsyncClient.post') as mock_post:
        
        # 设置默认响应
        mock_get.return_value = Mock(
            status_code=200,
            json=lambda: MOCK_ROUTE_RESPONSE
        )
        mock_post.return_value = Mock(
            status_code=200,
            json=lambda: MOCK_SUBMIT_RESPONSE
        )
        
        yield mock_get, mock_post
```

## 7. 测试执行计划

### 7.1 测试阶段
1. **单元测试阶段**：独立测试各个组件
2. **集成测试阶段**：测试完整流程
3. **性能测试阶段**：验证性能指标
4. **兼容性测试阶段**：确保向后兼容

### 7.2 测试覆盖率目标
- 代码覆盖率：≥90%
- 分支覆盖率：≥85%
- 功能覆盖率：100%

### 7.3 测试通过标准
- 所有单元测试通过
- 集成测试成功率≥95%
- 性能测试满足要求
- 兼容性测试无回归

## 8. 测试环境配置

### 8.1 依赖安装
```bash
poetry install --with test
```

### 8.2 环境变量
```bash
export GMGN_API_HOST="https://gmgn.ai"
export TEST_WALLET_PRIVATE_KEY="test_private_key"
export TEST_WALLET_ADDRESS="test_wallet_address"
export SOLANA_RPC_URL="https://api.devnet.solana.com"
```

### 8.3 测试执行命令
```bash
# 运行所有测试
pytest test/utils/trading/solana/test_gmgn_trade_service_v2.py -v

# 运行特定测试
pytest test/utils/trading/solana/test_gmgn_trade_service_v2.py::TestGmgnTradeServiceV2::test_complete_buy_trade_success -v

# 生成覆盖率报告
pytest --cov=utils.trading.solana.gmgn_trade_service_v2 --cov-report=html
``` 