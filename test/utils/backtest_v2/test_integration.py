"""
回测模块V2集成测试

测试整个V2回测流程的端到端功能
"""

import pytest
import asyncio
import tempfile
import json
import os
from datetime import datetime, timedelta

from utils.backtest_v2.config_manager import ConfigManagerV2, BacktestConfigV2
from utils.backtest_v2.backtest_engine import BacktestEngineV2


class TestBacktestV2Integration:
    """回测模块V2集成测试"""
    
    @pytest.fixture
    def sample_config(self):
        """创建测试配置"""
        # 使用较短的时间范围进行测试
        end_time = int(datetime.now().timestamp())
        start_time = end_time - 7 * 24 * 3600  # 7天前
        
        return {
            "backtest_start_time": start_time,
            "backtest_end_time": end_time,
            "transaction_min_amount": 100,
            "kol_account_min_txs": 5,
            "kol_account_max_txs": 1000,
            "kol_account_min_count": 3,
            "token_mint_lookback_hours": 24,
            "transaction_lookback_hours": 1,
            "sell_strategy_hours": 12,
            "sell_kol_ratio": 0.3,
            "initial_capital": 1000.0,
            "commission_pct": 0.003,
            "slippage_pct": 0.002,
            "same_token_notification_interval_minutes": 30
        }
    
    @pytest.fixture
    def config_file(self, sample_config):
        """创建临时配置文件"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(sample_config, f, indent=2)
            temp_file = f.name
        yield temp_file
        try:
            os.unlink(temp_file)
        except FileNotFoundError:
            pass
    
    def test_config_manager_integration(self, config_file):
        """测试配置管理器集成"""
        # 测试从文件加载配置
        config = ConfigManagerV2.load_from_file(config_file)
        assert isinstance(config, BacktestConfigV2)
        assert config.initial_capital == 1000.0
        assert config.kol_account_min_count == 3
    
    @pytest.mark.asyncio
    async def test_backtest_engine_initialization(self, sample_config):
        """测试回测引擎初始化"""
        config = BacktestConfigV2(**sample_config)
        engine = BacktestEngineV2(config)

        # 验证引擎初始化
        assert engine.config == config
        assert engine.data_query is not None
        assert engine.sell_strategy is not None
        # result_analyzer在run_backtest时才初始化
        assert engine.result_analyzer is None
    
    @pytest.mark.asyncio
    async def test_backtest_engine_dry_run(self, sample_config):
        """测试回测引擎干运行（不执行实际查询）"""
        config = BacktestConfigV2(**sample_config)
        engine = BacktestEngineV2(config)
        
        # 测试各组件是否正确初始化
        assert hasattr(engine, 'data_query')
        assert hasattr(engine, 'sell_strategy')
        assert hasattr(engine, 'result_analyzer')  # 属性存在但初始值为None
        
        # 测试配置传递
        assert engine.config.initial_capital == 1000.0
        assert engine.config.kol_account_min_count == 3
    
    def test_config_validation(self):
        """测试配置验证"""
        # 测试有效配置
        valid_config = {
            "backtest_start_time": **********,  # 2022-01-01
            "backtest_end_time": **********,    # 2022-01-02
            "transaction_min_amount": 100,
            "kol_account_min_txs": 5,
            "kol_account_max_txs": 1000,
            "kol_account_min_count": 3,
            "token_mint_lookback_hours": 24,
            "transaction_lookback_hours": 1,
            "sell_strategy_hours": 12,
            "sell_kol_ratio": 0.3,
            "initial_capital": 1000.0,
            "commission_pct": 0.003,
            "slippage_pct": 0.002,
            "same_token_notification_interval_minutes": 30
        }
        
        config = BacktestConfigV2(**valid_config)
        assert config.initial_capital == 1000.0
        
        # 测试无效配置
        invalid_config = valid_config.copy()
        invalid_config["sell_kol_ratio"] = 1.5  # 超出范围
        
        with pytest.raises(ValueError):
            BacktestConfigV2(**invalid_config)
    
    def test_parameter_grid_generation(self):
        """测试参数网格生成"""
        param_grid = {
            "kol_account_min_count": [3, 5],
            "sell_kol_ratio": [0.3, 0.5]
        }

        combinations = ConfigManagerV2.generate_parameter_combinations(param_grid)
        
        # 应该生成 2 x 2 = 4 个组合
        assert len(combinations) == 4

        # 验证组合内容
        for combo in combinations:
            assert isinstance(combo, dict)
            assert combo["kol_account_min_count"] in [3, 5]
            assert combo["sell_kol_ratio"] in [0.3, 0.5]
    
    def test_config_compatibility(self):
        """测试V1配置兼容性"""
        # 模拟V1配置（包含V2不支持的参数）
        v1_config = {
            "backtest_start_time": **********,
            "backtest_end_time": **********,
            "transaction_min_amount": 100,
            "kol_account_min_txs": 5,
            "kol_account_max_txs": 1000,
            "kol_account_min_count": 3,
            "token_mint_lookback_hours": 24,
            "transaction_lookback_hours": 1,
            "sell_strategy_hours": 12,
            "sell_kol_ratio": 0.3,
            "initial_capital": 1000.0,
            "commission_pct": 0.003,
            "slippage_pct": 0.002,
            "same_token_notification_interval_minutes": 30,
            # V1特有参数（V2应该忽略）
            "kol_min_winrate_7d": 0.5,
            "processing_interval": 1,
            "use_real_price": True,
            "skip_price_api_query": False
        }
        
        # V2应该能够处理V1配置，忽略不支持的参数
        # 过滤掉V2不支持的参数
        v2_supported_params = {
            k: v for k, v in v1_config.items()
            if k in BacktestConfigV2.__dataclass_fields__
        }

        config = BacktestConfigV2(**v2_supported_params)
        assert config.initial_capital == 1000.0
        assert config.kol_account_min_count == 3
        # V1特有参数应该被忽略，不会出现在V2配置中
        assert not hasattr(config, 'kol_min_winrate_7d')
        assert not hasattr(config, 'processing_interval')


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
