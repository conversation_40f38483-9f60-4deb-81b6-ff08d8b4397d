"""
交易统计分析器

主分析器，集成所有组件，实现完整的统计分析流程。
支持多种输出格式，提供灵活的配置选项。
"""

import logging
from typing import Optional, List, Dict, Any
from datetime import datetime
import asyncio

from dao.trade_record_dao import TradeRecordDAO
from models.trade_record import TradeRecord
from utils.trading.trade_record_verification_updater import TradeRecordVerificationUpdater
from .models import StatisticsResult, StatisticsConfig, ReportFormat
from .trade_pair_matcher import TradePairMatcher
from .statistics_calculator import StatisticsCalculator
from .chart_generator import ChartGenerator
from .html_report_generator import HTMLReportGenerator
from .json_report_generator import JSONReportGenerator


class TradeStatisticsAnalyzer:
    """
    交易统计分析器
    
    统计分析的主入口，协调各个组件完成统计任务。
    支持HTML和JSON格式输出，提供灵活的配置选项。
    """
    
    def __init__(self, 
                 trade_record_dao: TradeRecordDAO,
                 verification_updater: TradeRecordVerificationUpdater):
        """
        初始化分析器
        
        Args:
            trade_record_dao: 交易记录数据访问对象
            verification_updater: 验证更新器
        """
        self.trade_record_dao = trade_record_dao
        self.verification_updater = verification_updater
        
        # 初始化组件
        self.pair_matcher = TradePairMatcher(verification_updater)
        self.calculator = StatisticsCalculator()
        self.chart_generator = ChartGenerator()
        self.html_generator = HTMLReportGenerator()
        self.json_generator = JSONReportGenerator()
        
        self.logger = logging.getLogger(__name__)
    
    async def analyze(self, config: Optional[StatisticsConfig] = None) -> StatisticsResult:
        """
        执行统计分析
        
        Args:
            config: 统计配置
            
        Returns:
            统计结果
        """
        try:
            self.logger.info("开始执行交易统计分析")
            
            # 使用默认配置
            if config is None:
                config = StatisticsConfig()
            
            # 1. 查询交易记录
            self.logger.info("正在查询交易记录...")
            trade_records = await self._query_trade_records(config)
            self.logger.info(f"查询到 {len(trade_records)} 条交易记录")
            
            if not trade_records:
                self.logger.warning("未找到符合条件的交易记录")
                return self._create_empty_result()
            
            # 2. 匹配交易对
            self.logger.info("正在匹配交易对...")
            trade_pairs = await self.pair_matcher.match_trade_pairs(trade_records)
            self.logger.info(f"匹配到 {len(trade_pairs)} 个交易对")
            
            if not trade_pairs:
                self.logger.warning("未找到有效的交易对")
                return self._create_empty_result()
            
            # 3. 计算统计数据
            self.logger.info("正在计算统计数据...")
            statistics = self.calculator.calculate_statistics(trade_pairs)
            
            self.logger.info("交易统计分析完成")
            return statistics
            
        except Exception as e:
            self.logger.error(f"统计分析失败: {str(e)}")
            raise
    
    async def generate_report(self, 
                            format: ReportFormat = ReportFormat.HTML,
                            config: Optional[StatisticsConfig] = None) -> str:
        """
        生成统计报告
        
        Args:
            format: 报告格式
            config: 统计配置
            
        Returns:
            报告内容字符串
        """
        try:
            self.logger.info(f"开始生成 {format.value} 格式报告")
            
            # 执行统计分析
            statistics = await self.analyze(config)
            
            if format == ReportFormat.HTML:
                return await self._generate_html_report(statistics, config)
            elif format == ReportFormat.JSON:
                return await self._generate_json_report(statistics, config)
            else:
                raise ValueError(f"不支持的报告格式: {format}")
                
        except Exception as e:
            self.logger.error(f"生成报告失败: {str(e)}")
            raise
    
    async def save_report(self, 
                         file_path: str,
                         format: ReportFormat = ReportFormat.HTML,
                         config: Optional[StatisticsConfig] = None) -> None:
        """
        生成并保存统计报告到文件
        
        Args:
            file_path: 保存路径
            format: 报告格式
            config: 统计配置
        """
        try:
            self.logger.info(f"开始生成并保存报告到: {file_path}")
            
            # 生成报告内容
            report_content = await self.generate_report(format, config)
            
            # 保存到文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            self.logger.info(f"报告已成功保存到: {file_path}")
            
        except Exception as e:
            self.logger.error(f"保存报告失败: {str(e)}")
            raise
    
    async def _query_trade_records(self, config: StatisticsConfig) -> List[TradeRecord]:
        """
        查询交易记录
        
        Args:
            config: 统计配置
            
        Returns:
            交易记录列表
        """
        try:
            self.logger.info(f"开始查询交易记录，配置: {config}")
            
            # 查询已验证的交易记录
            trade_records = await self.trade_record_dao.find_verified_trade_records(
                start_date=config.start_date,
                end_date=config.end_date,
                strategies=config.strategy_filter,
                tokens=config.token_filter
            )
            
            self.logger.info(f"查询到 {len(trade_records)} 条交易记录")
            return trade_records
            
        except Exception as e:
            self.logger.error(f"查询交易记录失败: {e}")
            raise
    
    async def _generate_html_report(self, 
                                  statistics: StatisticsResult, 
                                  config: Optional[StatisticsConfig]) -> str:
        """
        生成HTML报告
        
        Args:
            statistics: 统计结果
            config: 统计配置
            
        Returns:
            HTML报告字符串
        """
        try:
            self.logger.info("正在生成图表...")
            
            # 生成图表
            charts = self.chart_generator.generate_all_charts(statistics)
            
            self.logger.info("正在生成HTML报告...")
            
            # 生成HTML报告
            html_content = self.html_generator.generate_report(
                statistics, charts, config
            )
            
            return html_content
            
        except Exception as e:
            self.logger.error(f"生成HTML报告失败: {str(e)}")
            raise
    
    async def _generate_json_report(self, 
                                  statistics: StatisticsResult, 
                                  config: Optional[StatisticsConfig]) -> str:
        """
        生成JSON报告
        
        Args:
            statistics: 统计结果
            config: 统计配置
            
        Returns:
            JSON报告字符串
        """
        try:
            self.logger.info("正在生成JSON报告...")
            
            # 生成JSON报告
            json_content = self.json_generator.generate_report(statistics, config)
            
            return json_content
            
        except Exception as e:
            self.logger.error(f"生成JSON报告失败: {str(e)}")
            raise
    
    def _create_empty_result(self) -> StatisticsResult:
        """
        创建空的统计结果
        
        Returns:
            空的统计结果
        """
        from .models import OverallStats
        
        return StatisticsResult(
            overall_stats=OverallStats(),
            token_stats=[],
            strategy_stats=[],
            trade_pairs=[],
            profit_rankings=[],
            loss_rankings=[],
            generation_time=datetime.now(),
            data_range={}
        )
    
    async def get_statistics_summary(self, config: Optional[StatisticsConfig] = None) -> Dict[str, Any]:
        """
        获取统计摘要
        
        Args:
            config: 统计配置
            
        Returns:
            统计摘要字典
        """
        try:
            self.logger.info("正在生成统计摘要...")
            
            # 执行统计分析
            statistics = await self.analyze(config)
            
            # 生成摘要
            summary = {
                "total_trades": statistics.overall_stats.total_trades if statistics.overall_stats else 0,
                "total_win_rate": round(statistics.overall_stats.total_win_rate, 2) if statistics.overall_stats else 0,
                "total_profit_rate": round(statistics.overall_stats.total_profit_rate, 2) if statistics.overall_stats else 0,
                "avg_profit_rate": round(statistics.overall_stats.avg_profit_rate, 2) if statistics.overall_stats else 0,
                "total_profit_amount": round(statistics.overall_stats.total_profit_amount, 4) if statistics.overall_stats else 0,
                "profitable_trades": statistics.overall_stats.profitable_trades if statistics.overall_stats else 0,
                "loss_trades": statistics.overall_stats.loss_trades if statistics.overall_stats else 0,
                "strategy_count": len(statistics.strategy_stats),
                "token_count": len(statistics.token_stats),
                "generation_time": statistics.generation_time.isoformat() if statistics.generation_time else datetime.now().isoformat(),
                "data_range": statistics.data_range
            }
            
            # 添加最佳表现信息
            if statistics.profit_rankings:
                summary["best_profit"] = {
                    "amount": round(statistics.profit_rankings[0].profit_amount, 4),
                    "rate": round(statistics.profit_rankings[0].profit_rate, 2),
                    "signal_id": statistics.profit_rankings[0].signal_id
                }
            
            if statistics.loss_rankings:
                summary["worst_loss"] = {
                    "amount": round(statistics.loss_rankings[0].loss_amount, 4),
                    "rate": round(statistics.loss_rankings[0].loss_rate, 2),
                    "signal_id": statistics.loss_rankings[0].signal_id
                }
            
            return summary
            
        except Exception as e:
            self.logger.error(f"获取统计摘要失败: {str(e)}")
            raise
    
    async def validate_data_integrity(self, config: Optional[StatisticsConfig] = None) -> Dict[str, Any]:
        """
        验证数据完整性
        
        Args:
            config: 统计配置
            
        Returns:
            验证结果字典
        """
        try:
            self.logger.info("正在验证数据完整性...")
            
            # 查询交易记录
            trade_records = await self._query_trade_records(config or StatisticsConfig())
            
            validation_result = {
                "total_records": len(trade_records),
                "verified_records": 0,
                "missing_verification": 0,
                "invalid_amounts": 0,
                "orphaned_trades": 0,
                "validation_time": datetime.now().isoformat()
            }
            
            # 统计验证状态
            for record in trade_records:
                if record.verification_status == "verified":
                    validation_result["verified_records"] += 1
                    
                    # 检查验证金额
                    if (record.trade_type == "SELL" and 
                        record.token_out_verified_amount is None):
                        validation_result["missing_verification"] += 1
                    
                    # 检查金额有效性
                    if (record.token_in_amount is None or record.token_in_amount <= 0):
                        validation_result["invalid_amounts"] += 1
            
            # 检查孤立交易
            signal_counts = {}
            for record in trade_records:
                signal_id = record.signal_id
                if signal_id not in signal_counts:
                    signal_counts[signal_id] = {"BUY": 0, "SELL": 0}
                # 直接使用字符串值，因为TradeType枚举的值就是字符串
                trade_type_str = record.trade_type.value.upper() if hasattr(record.trade_type, 'value') else str(record.trade_type).upper()
                if trade_type_str in signal_counts[signal_id]:
                    signal_counts[signal_id][trade_type_str] += 1
            
            # 统计孤立交易
            for signal_id, counts in signal_counts.items():
                if counts["BUY"] != counts["SELL"]:
                    validation_result["orphaned_trades"] += abs(counts["BUY"] - counts["SELL"])
            
            # 计算完整性百分比
            if validation_result["total_records"] > 0:
                validation_result["integrity_percentage"] = round(
                    (validation_result["verified_records"] - 
                     validation_result["missing_verification"] - 
                     validation_result["invalid_amounts"]) / 
                    validation_result["total_records"] * 100, 2
                )
            else:
                validation_result["integrity_percentage"] = 100.0
            
            self.logger.info(f"数据完整性验证完成，完整性: {validation_result['integrity_percentage']}%")
            
            return validation_result
            
        except Exception as e:
            self.logger.error(f"数据完整性验证失败: {str(e)}")
            raise 