#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易记录验证工作流Handler测试用例

测试覆盖：
1. 函数签名正确性（单个记录vs记录列表）
2. 字段名正确性（token_in_actual_amount, token_out_actual_amount）
3. 类型转换正确性（Decimal到float）
4. 工作流数据流完整性
5. GmgN API集成测试
"""

import unittest
import asyncio
from datetime import datetime
from decimal import Decimal
from unittest.mock import AsyncMock, patch, MagicMock
from beanie import PydanticObjectId

from workflows.trade_record_verification_updater.handler import (
    scan_pending_trade_records,
    verify_trade_amounts,
    update_verification_results
)
from models.trade_record import TradeType


class TestTradeRecordVerificationHandler(unittest.TestCase):
    """交易记录验证工作流Handler测试类"""
    
    def test_scan_pending_trade_records_return_type(self):
        """测试扫描函数返回类型（应该返回列表）"""
        async def run_test():
            with patch('workflows.trade_record_verification_updater.handler.TradeRecordDAO') as mock_dao_class:
                mock_dao = AsyncMock()
                mock_dao_class.return_value = mock_dao
                
                # 模拟买入记录
                mock_buy_record = MagicMock()
                mock_buy_record.id = PydanticObjectId()
                mock_buy_record.trade_type = TradeType.BUY
                mock_buy_record.tx_hash = "test_buy_hash"
                mock_buy_record.token_out_address = "test_token_address"
                mock_buy_record.token_in_address = "sol_address"
                mock_buy_record.wallet_address = "test_wallet"
                mock_buy_record.created_at = datetime.now()
                mock_buy_record.strategy_name = "test_strategy"
                
                # 模拟卖出记录
                mock_sell_record = MagicMock()
                mock_sell_record.id = PydanticObjectId()
                mock_sell_record.trade_type = TradeType.SELL
                mock_sell_record.tx_hash = "test_sell_hash"
                mock_sell_record.token_in_address = "test_token_address"
                mock_sell_record.token_out_address = "sol_address"
                mock_sell_record.wallet_address = "test_wallet"
                mock_sell_record.created_at = datetime.now()
                mock_sell_record.strategy_name = "test_strategy"
                
                # 设置DAO方法返回值
                mock_dao.find_trades_missing_actual_amounts.side_effect = [
                    [mock_buy_record],  # 第一次调用返回买入记录
                    [mock_sell_record]  # 第二次调用返回卖出记录
                ]
                
                # 执行测试
                result = await scan_pending_trade_records()
                
                # 验证返回类型
                self.assertIsInstance(result, list, "应该返回列表")
                self.assertEqual(len(result), 2, "应该返回2条记录")
                
                # 验证买入记录格式
                buy_record = result[0]
                self.assertIn('id', buy_record)
                self.assertIn('trade_type', buy_record)
                self.assertIn('tx_hash', buy_record)
                self.assertIn('target_token', buy_record)
                self.assertIn('wallet_address', buy_record)
                
                # 验证卖出记录格式
                sell_record = result[1]
                self.assertIn('id', sell_record)
                self.assertIn('trade_type', sell_record)
                self.assertIn('tx_hash', sell_record)
                self.assertIn('target_token', sell_record)
                self.assertIn('wallet_address', sell_record)
                
                # 验证DAO方法调用
                self.assertEqual(mock_dao.find_trades_missing_actual_amounts.call_count, 2)
        
        asyncio.run(run_test())
    
    def test_verify_trade_amounts_single_record_input(self):
        """测试验证函数接收单个记录（不是列表）"""
        async def run_test():
            # 单个记录输入
            single_record = {
                'id': '507f1f77bcf86cd799439011',
                'trade_type': 'BUY',
                'tx_hash': 'test_hash',
                'wallet_address': 'test_wallet',
                'target_token': 'test_token_address'
            }
            
            with patch('workflows.trade_record_verification_updater.handler.GmgnWalletTokenActivitySpider') as mock_spider_class:
                mock_spider = AsyncMock()
                mock_spider_class.return_value = mock_spider
                
                # 模拟GmgN API返回
                mock_spider.find_activity_by_signature.return_value = {
                    'amount': -0.001,  # 买入时为负数
                    'type': 'buy',
                    'timestamp': 1640995200
                }
                
                # 执行测试
                result = await verify_trade_amounts(single_record)
                
                # 验证结果
                self.assertIsInstance(result, dict, "应该返回字典")
                self.assertEqual(result['id'], '507f1f77bcf86cd799439011')
                self.assertEqual(result['verification_status'], 'verified')
                self.assertEqual(result['verified_amount'], 0.001)  # 取绝对值
                self.assertEqual(result['trade_type'], 'BUY')
                
                # 验证GmgN API调用
                mock_spider.find_activity_by_signature.assert_called_once()
        
        asyncio.run(run_test())
    
    def test_verify_trade_amounts_empty_record_handling(self):
        """测试验证函数处理空记录"""
        async def run_test():
            # 测试空记录
            result = await verify_trade_amounts({})
            
            self.assertEqual(result['verification_status'], 'failed')
            self.assertIsNone(result['verified_amount'])
            self.assertIn('error_message', result)
            self.assertEqual(result['error_message'], 'Empty record')
        
        asyncio.run(run_test())
    
    def test_update_verification_results_list_input(self):
        """测试更新函数接收结果列表"""
        async def run_test():
            # 结果列表输入
            results_list = [
                {
                    'id': '507f1f77bcf86cd799439011',
                    'trade_type': 'BUY',
                    'verification_status': 'verified',
                    'verified_amount': 0.001,
                    'verification_timestamp': datetime.utcnow().isoformat(),
                    'data_source': 'gmgn_api'
                },
                {
                    'id': '507f1f77bcf86cd799439012',
                    'trade_type': 'SELL',
                    'verification_status': 'verified',
                    'verified_amount': 0.0015,
                    'verification_timestamp': datetime.utcnow().isoformat(),
                    'data_source': 'gmgn_api'
                }
            ]
            
            with patch('workflows.trade_record_verification_updater.handler.TradeRecordDAO') as mock_dao_class:
                mock_dao = AsyncMock()
                mock_dao_class.return_value = mock_dao
                mock_dao.update_trade_record.return_value = True
                
                # 执行测试
                result = await update_verification_results(results_list)
                
                # 验证结果
                self.assertEqual(result['updated_count'], 2)
                self.assertEqual(result['failed_count'], 0)
                
                # 验证DAO调用次数
                self.assertEqual(mock_dao.update_trade_record.call_count, 2)
        
        asyncio.run(run_test())
    
    def test_field_name_consistency(self):
        """测试字段名一致性（token_in_actual_amount, token_out_actual_amount）"""
        async def run_test():
            with patch('workflows.trade_record_verification_updater.handler.TradeRecordDAO') as mock_dao_class:
                mock_dao = AsyncMock()
                mock_dao_class.return_value = mock_dao
                
                # 模拟买入记录
                mock_record = MagicMock()
                mock_record.id = PydanticObjectId()
                mock_record.trade_type = TradeType.BUY
                mock_record.tx_hash = "test_hash"
                mock_record.token_out_address = "test_address"
                mock_record.token_in_address = "sol_address"
                mock_record.wallet_address = "test_wallet"
                mock_record.created_at = datetime.now()
                mock_record.strategy_name = "test_strategy"
                
                mock_dao.find_trades_missing_actual_amounts.side_effect = [
                    [mock_record],  # 买入记录
                    []  # 卖出记录为空
                ]
                
                # 执行测试
                result = await scan_pending_trade_records()
                
                # 验证字段存在
                if result:
                    record = result[0]
                    self.assertEqual(record['trade_type'], 'BUY')
                    self.assertEqual(record['target_token'], 'test_address')
        
        asyncio.run(run_test())
    
    def test_decimal_to_float_conversion(self):
        """测试Decimal到float的转换"""
        async def run_test():
            with patch('workflows.trade_record_verification_updater.handler.TradeRecordDAO') as mock_dao_class:
                mock_dao = AsyncMock()
                mock_dao_class.return_value = mock_dao
                
                # 模拟记录
                mock_record = MagicMock()
                mock_record.id = PydanticObjectId()
                mock_record.trade_type = TradeType.SELL
                mock_record.tx_hash = "test_hash"
                mock_record.token_in_address = "test_address"
                mock_record.token_out_address = "sol_address"
                mock_record.wallet_address = "test_wallet"
                mock_record.created_at = datetime.now()
                mock_record.strategy_name = "test_strategy"
                
                mock_dao.find_trades_missing_actual_amounts.side_effect = [
                    [],  # 买入记录为空
                    [mock_record]  # 卖出记录
                ]
                
                # 执行测试
                result = await scan_pending_trade_records()
                
                # 验证结果
                if result:
                    record = result[0]
                    self.assertEqual(record['trade_type'], 'SELL')
                    self.assertEqual(record['target_token'], 'test_address')
        
        asyncio.run(run_test())
    
    def test_timeout_handling(self):
        """测试超时处理"""
        async def run_test():
            # 单个记录输入
            single_record = {
                'id': '507f1f77bcf86cd799439011',
                'trade_type': 'BUY',
                'tx_hash': 'test_hash',
                'wallet_address': 'test_wallet',
                'target_token': 'test_token_address'
            }
            
            with patch('workflows.trade_record_verification_updater.handler.GmgnWalletTokenActivitySpider') as mock_spider_class:
                mock_spider = AsyncMock()
                mock_spider_class.return_value = mock_spider
                
                # 模拟超时
                mock_spider.find_activity_by_signature.side_effect = asyncio.TimeoutError()
                
                # 执行测试
                result = await verify_trade_amounts(single_record)
                
                # 验证结果
                self.assertEqual(result['verification_status'], 'failed')
                self.assertIsNone(result['verified_amount'])
                self.assertIn('error_message', result)
                self.assertEqual(result['error_message'], 'GmgN API timeout')
        
        asyncio.run(run_test())
    
    def test_update_verification_results_empty_list(self):
        """测试更新函数处理空列表"""
        async def run_test():
            # 空列表输入
            result = await update_verification_results([])
            
            self.assertEqual(result['updated_count'], 0)
            self.assertEqual(result['failed_count'], 0)
        
        asyncio.run(run_test())
    
    def test_update_verification_results_validation_error(self):
        """测试更新函数处理验证错误"""
        async def run_test():
            # 包含无效数据的结果列表
            results_list = [
                {
                    'id': None,  # 无效ID
                    'verification_status': 'invalid_status',  # 无效状态
                    'verified_amount': 100.50,
                    'verification_timestamp': datetime.utcnow().isoformat()
                }
            ]
            
            with patch('workflows.trade_record_verification_updater.handler.TradeRecordDAO') as mock_dao_class:
                mock_dao = AsyncMock()
                mock_dao_class.return_value = mock_dao
                
                # 执行测试
                result = await update_verification_results(results_list)
                
                # 验证结果
                self.assertEqual(result['updated_count'], 0)
                self.assertEqual(result['failed_count'], 1)
        
        asyncio.run(run_test())


if __name__ == '__main__':
    unittest.main() 