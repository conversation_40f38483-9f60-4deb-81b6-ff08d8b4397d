from datetime import datetime
from typing import Optional
from beanie import Document, Indexed

class KolActivityFilter(Document):
    """KOL活动过滤器模型。"""
    
    wallet_address: Indexed(str)  # 钱包地址
    tx_hash: Indexed(str)  # 交易哈希
    created_at: datetime  # 创建时间
    updated_at: datetime  # 更新时间
    
    class Settings:
        name = "kol_activity_filter"
        indexes = [
            [("wallet_address", 1), ("tx_hash", 1)],  # 复合索引
        ] 