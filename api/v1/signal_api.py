from fastapi import APIRouter, Query, HTTPException
from typing import List, Optional, Dict, Any
from beanie import PydanticObjectId
from datetime import datetime, timedelta
from zoneinfo import ZoneInfo, ZoneInfoNotFoundError # Use zoneinfo (Python 3.9+)
from enum import Enum
from pydantic import BaseModel, Field
import pymongo # For sorting

# Import DAO and Model for Signals
from models.signal import Signal
from dao.signal_dao import SignalDAO
# Import standard response model
from .schemas import StandardResponse

router = APIRouter()

# --- Enums ---
class SignalPeriod(str, Enum):
    """信号时间范围枚举"""
    TODAY = "today"
    PAST = "past"

# --- Pydantic Models ---
class SignalResponse(BaseModel):
    """API 响应中单个信号的数据结构 (已移除 status, buy_signal_ref_id, trigger_conditions)"""
    id: PydanticObjectId = Field(..., alias="_id", description="信号ID")
    token_address: str = Field(description="代币地址")
    token_name: Optional[str] = Field(default=None, description="代币名称")
    token_symbol: Optional[str] = Field(default=None, description="代币符号")
    signal_type: str = Field(description="信号类型 (例如 'kol_buy', 'kol_sell')")
    trigger_timestamp: datetime = Field(description="信号触发时间")
    hit_kol_wallets: Optional[List[str]] = Field(default=None, description="命中该信号的KOL钱包地址列表")
    created_at: datetime = Field(description="记录创建时间")
    updated_at: Optional[datetime] = Field(default=None, description="记录更新时间") # Added updated_at

    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True
        json_encoders = {
            PydanticObjectId: str,
            datetime: lambda dt: dt.isoformat() if dt else None # Ensure datetime is ISO string, handle None
        }

# Concrete response model for the /signals endpoint to help OpenAPI schema generation
class SignalListApiResponse(BaseModel):
    """获取信号列表API的具体响应模型"""
    code: int = Field(0, description="状态码，0 表示成功，非 0 表示失败")
    msg: str = Field("success", description="响应消息")
    data: Optional[List[SignalResponse]] = Field(None, description="信号记录列表")

# Define error code (example)
ERROR_CODE_DB_ERROR = 5001
ERROR_CODE_INVALID_TZ = 4001 # Example error code for bad request (invalid timezone)

# --- API Endpoint ---
@router.get(
    "/signals",
    response_model=SignalListApiResponse,
    summary="获取信号记录列表 (分页)",
    description="根据分页和过滤参数获取 Signal 记录，按创建时间降序排列。"
)
async def list_signals(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(10, ge=1, le=100, description="每页返回的最大记录数"),
    signal_type: Optional[str] = Query(None, description="按信号类型过滤 (例如 'kol_buy', 'kol_sell')"), # Added signal_type filter
    period: Optional[SignalPeriod] = Query(None, description="时间范围 ('today' 获取今天, 'past' 获取今天之前)"),
    tz: str = Query("Asia/Shanghai", description="用于区分 '今天' 和 '往期' 的时区 (IANA 时区名称, 例如 'Asia/Shanghai', 'UTC')")
) -> SignalListApiResponse:
    """
    获取信号 (Signal) 记录的分页列表，可按信号类型和时间范围筛选。

    - **skip**: 跳过指定数量的记录。
    - **limit**: 限制返回的记录数量 (最大 100)。
    - **signal_type**: 可选，按信号类型精确匹配过滤。
    - **period**: 可选，筛选时间范围: 'today' (今天), 'past' (今天之前)。
    - **tz**: 指定用于计算 '今天' 的时区 (默认为 'Asia/Shanghai')。
    """
    try:
        # Validate timezone
        try:
            timezone = ZoneInfo(tz)
        except ZoneInfoNotFoundError:
            return SignalListApiResponse(
                code=ERROR_CODE_INVALID_TZ,
                msg=f"无效的时区名称: '{tz}'",
                data=None
            )

        # Build base query filter
        query_filter: Dict[str, Any] = {}

        # Add signal_type filter if provided
        if signal_type:
            query_filter['signal_type'] = signal_type

        # Apply time period filtering if specified
        if period:
            now = datetime.now(timezone)
            start_of_today_local = now.replace(hour=0, minute=0, second=0, microsecond=0)
            # Workaround: Use naive datetime for query matching potential storage issue
            start_dt_naive = start_of_today_local.replace(tzinfo=None)

            if period == SignalPeriod.TODAY:
                start_of_tomorrow_local = start_of_today_local + timedelta(days=1)
                end_dt_naive = start_of_tomorrow_local.replace(tzinfo=None)
                # Filter for today (using created_at)
                query_filter['created_at'] = {'$gte': start_dt_naive, '$lt': end_dt_naive}
            elif period == SignalPeriod.PAST:
                 # Filter for before today (using created_at)
                query_filter['created_at'] = {'$lt': start_dt_naive}
            # print(f"query_filter with time: {query_filter}") # Debugging

        # 过滤掉测试策略信号
        # 同时排除 trigger_conditions.strategy_name 和 trigger_conditions.original_buy_strategy_name 为 "测试" 的记录
        # 保留 trigger_conditions 或相关字段不存在的记录
        query_filter['$and'] = [
            {
                '$or': [
                    {'trigger_conditions.strategy_name': {'$ne': '测试'}},
                    {'trigger_conditions.strategy_name': {'$exists': False}},
                    {'trigger_conditions': {'$exists': False}}
                ]
            },
            {
                '$or': [
                    {'trigger_conditions.original_buy_strategy_name': {'$ne': '测试'}},
                    {'trigger_conditions.original_buy_strategy_name': {'$exists': False}},
                    {'trigger_conditions': {'$exists': False}}
                ]
            }
        ]

        signal_dao = SignalDAO()
        # Query Signal collection, sort by creation time descending
        signal_docs: List[Dict] = await signal_dao.collection.find(query_filter).sort('created_at', pymongo.DESCENDING).skip(skip).limit(limit).to_list()

        # Convert raw dicts from DB to response model list
        response_data = [SignalResponse.model_validate(doc) for doc in signal_docs]

        # Return the concrete response model instance
        return SignalListApiResponse(data=response_data)

    except Exception as e:
        # Simple error handling
        print(f"Error fetching signals: {e}")
        return SignalListApiResponse(
            code=ERROR_CODE_DB_ERROR,
            msg=f"获取信号数据时出错: {e}",
            data=None
        ) 