"""动态重试配置接口模型 - 预留扩展接口，供后续模块实现"""

from typing import Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum


class DynamicConfigSource(str, Enum):
    """动态配置来源枚举"""
    MARKET_ANALYSIS = "market_analysis"         # 市场分析模块
    NETWORK_MONITOR = "network_monitor"         # 网络监控模块
    RISK_CONTROL = "risk_control"               # 风控模块
    LIQUIDITY_MONITOR = "liquidity_monitor"     # 流动性监控模块
    PRICE_PREDICTION = "price_prediction"       # 价格预测模块
    COST_OPTIMIZATION = "cost_optimization"     # 成本优化模块
    MANUAL_OVERRIDE = "manual_override"         # 手动覆盖
    EMERGENCY_RESPONSE = "emergency_response"   # 紧急响应


class DynamicRetryConfig(BaseModel):
    """
    动态重试配置接口模型
    
    此模型作为运行时交易配置覆盖的接口标准，供后续的市场分析、
    网络监控等模块实现具体的动态配置逻辑。
    
    设计原则：
    1. 只定义接口标准，不实现具体业务逻辑
    2. 支持所有交易参数的动态覆盖
    3. 预留充分的扩展空间
    """
    
    # 配置元信息
    config_id: str = Field(..., description="配置唯一标识")
    source: DynamicConfigSource = Field(..., description="配置来源")
    reason: str = Field(..., description="应用此配置的原因")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="配置创建时间")
    
    # 生效时间和有效期
    effective_at: datetime = Field(default_factory=datetime.utcnow, description="配置生效时间")
    expires_at: Optional[datetime] = Field(default=None, description="配置过期时间")
    
    # 重试间隔覆盖配置
    retry_delay_seconds: Optional[float] = Field(default=None, description="覆盖基础重试间隔")
    max_retry_delay_seconds: Optional[float] = Field(default=None, description="覆盖最大重试间隔")
    retry_delay_strategy: Optional[str] = Field(default=None, description="覆盖重试间隔策略")
    slippage_error_delay_seconds: Optional[float] = Field(default=None, description="覆盖滑点错误专用间隔")
    
    # 滑点重试覆盖配置
    enable_slippage_retry: Optional[bool] = Field(default=None, description="覆盖滑点递增开关")
    slippage_increment_percentage: Optional[float] = Field(default=None, description="覆盖滑点增加步长")
    max_slippage_percentage: Optional[float] = Field(default=None, description="覆盖最大滑点限制")
    
    # 买卖差异化配置
    buy_retry_delay_seconds: Optional[float] = Field(default=None, description="覆盖买入重试间隔")
    sell_retry_delay_seconds: Optional[float] = Field(default=None, description="覆盖卖出重试间隔")
    enable_buy_slippage_retry: Optional[bool] = Field(default=None, description="覆盖买入滑点递增开关")
    enable_sell_slippage_retry: Optional[bool] = Field(default=None, description="覆盖卖出滑点递增开关")
    buy_slippage_increment_percentage: Optional[float] = Field(default=None, description="覆盖买入滑点步长")
    sell_slippage_increment_percentage: Optional[float] = Field(default=None, description="覆盖卖出滑点步长")
    max_buy_slippage_percentage: Optional[float] = Field(default=None, description="覆盖买入最大滑点")
    max_sell_slippage_percentage: Optional[float] = Field(default=None, description="覆盖卖出最大滑点")
    
    # 其他交易参数覆盖（预留扩展）
    buy_slippage_percentage: Optional[float] = Field(default=None, description="覆盖买入初始滑点")
    sell_slippage_percentage: Optional[float] = Field(default=None, description="覆盖卖出初始滑点")
    buy_priority_fee_sol: Optional[float] = Field(default=None, description="覆盖买入优先费")
    sell_priority_fee_sol: Optional[float] = Field(default=None, description="覆盖卖出优先费")
    
    # 额外元数据
    metadata: Dict[str, Any] = Field(default_factory=dict, description="额外的配置元数据")
    
    def is_expired(self, check_time: Optional[datetime] = None) -> bool:
        """检查配置是否已过期"""
        if self.expires_at is None:
            return False
        
        current_time = check_time or datetime.utcnow()
        return current_time > self.expires_at
    
    def is_effective(self, check_time: Optional[datetime] = None) -> bool:
        """检查配置是否有效（已生效且未过期）"""
        current_time = check_time or datetime.utcnow()
        
        # 检查是否已生效
        if current_time < self.effective_at:
            return False
        
        # 检查是否已过期
        if self.is_expired(current_time):
            return False
        
        return True
    
    def to_override_dict(self) -> Dict[str, Any]:
        """
        转换为运行时覆盖字典
        
        提取所有非None的交易参数覆盖字段，用于参数合并逻辑
        """
        override_dict = {}
        
        # 提取所有非None的覆盖字段
        for field_name, field_value in self.model_dump().items():
            if field_value is not None and field_name not in [
                'config_id', 'source', 'reason', 'created_at', 
                'effective_at', 'expires_at', 'metadata'
            ]:
                override_dict[field_name] = field_value
        
        return override_dict


class MarketConditionRetryConfig(DynamicRetryConfig):
    """
    市场条件特化的动态重试配置接口
    
    预留给市场分析模块使用，提供基于市场条件的配置接口标准。
    具体的市场条件识别和配置生成逻辑由相应模块实现。
    """
    
    # 市场条件识别信息（可选扩展字段）
    market_condition: Optional[str] = Field(default=None, description="触发的市场条件标识")
    confidence_score: Optional[float] = Field(default=None, description="市场条件识别置信度")
    
    # 触发阈值和参数（预留扩展）
    trigger_params: Dict[str, Any] = Field(default_factory=dict, description="触发条件参数")
    
    class Config:
        """配置类 - 预留后续扩展"""
        extra = "allow"  # 允许额外字段，便于后续模块扩展 