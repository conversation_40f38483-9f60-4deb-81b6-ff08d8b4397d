from datetime import datetime
from beanie import Document, Indexed
from typing import Optional, Any
from pydantic import Field


class TaskStatus(Document):
    """任务状态记录模型"""
    
    # 任务基本信息
    task_id: Indexed(str) = Field(description="Celery任务ID", unique=True)
    task_name: str = Field(description="任务名称")
    status: str = Field(description="任务状态", default="PENDING")  # PENDING, SUCCESS, FAILURE
    
    # 任务执行信息
    result: Optional[Any] = Field(default=None, description="任务结果")
    error: Optional[str] = Field(default=None, description="错误信息")
    traceback: Optional[str] = Field(default=None, description="错误堆栈")
    
    # 时间信息
    created_at: datetime = Field(default_factory=datetime.utcnow, description="任务创建时间")
    started_at: Optional[datetime] = Field(default=None, description="任务开始时间")
    stopped_at: Optional[datetime] = Field(default=None, description="任务完成时间")
    
    # 重试信息
    retries: int = Field(default=0, description="重试次数")
    max_retries: Optional[int] = Field(default=None, description="最大重试次数")
    
    class Settings:
        name = "task_status"
        
    class Config:
        json_schema_extra = {
            "example": {
                "task_id": "123e4567-e89b-12d3-a456-426614174000",
                "task_name": "spider_x_user_info",
                "status": "SUCCESS",
                "args": ["example_user"],
                "kwargs": {},
                "result": {"status": "success", "data": {"user_info": {}}},
                "created_at": "2024-02-08T12:00:00",
                "started_at": "2024-02-08T12:00:01",
                "completed_at": "2024-02-08T12:00:02",
                "retries": 0,
                "max_retries": 3
            }
        } 