# SolanaMonitor 测试用例文档

创建日期：2025-05-26
更新日期：2025-05-26
测试方法：自动化测试
测试级别：单元测试

## 概述

本文档描述了 `SolanaMonitor` 类的单元测试用例，特别关注Bug修复中依赖的关键方法。这些测试确保了自动交易系统中卖出功能的可靠性。

## 测试文件位置

- **测试文件**: `test/utils/spiders/solana/test_solana_monitor.py`
- **被测试模块**: `utils/spiders/solana/solana_monitor.py`

## 测试覆盖的核心方法

### 1. `get_token_balance` 方法测试

该方法是Bug修复的核心，用于获取钱包中特定代币的实际余额。

#### 测试用例：

- **`test_get_token_balance_success_with_balance`**: 测试成功获取有余额的代币
  - 模拟RPC返回有效的代币账户数据
  - 验证正确解析 `uiAmountString` 字段
  - 确认返回正确的余额数值

- **`test_get_token_balance_success_zero_balance`**: 测试余额为0的情况
  - 模拟RPC返回空的代币账户列表
  - 验证返回0.0余额

- **`test_get_token_balance_rpc_exception`**: 测试RPC异常处理
  - 模拟RPC连接失败
  - 验证异常被正确捕获并返回0.0
  - 确认错误被记录到日志

- **`test_get_token_balance_invalid_account_data`**: 测试无效数据格式处理
  - 模拟返回格式错误的账户数据
  - 验证解析失败时返回0.0

### 2. `get_transaction_details` 方法测试

该方法用于获取交易的详细信息，支持 `get_confirmed_token_output_from_tx` 方法。

#### 测试用例：

- **`test_get_transaction_details_success`**: 测试成功获取交易详情
  - 模拟完整的交易数据响应
  - 验证正确解析交易JSON数据

- **`test_get_transaction_details_not_found`**: 测试交易不存在
  - 模拟RPC返回空值
  - 验证返回None

- **`test_get_transaction_details_rpc_exception`**: 测试RPC异常
  - 模拟网络错误
  - 验证异常处理和日志记录

### 3. `get_confirmed_token_output_from_tx` 方法测试

该方法从交易哈希中获取确认的代币输出数量，用于验证买入交易的实际结果。

#### 测试用例：

- **`test_get_confirmed_token_output_from_tx_spl_token_success`**: 测试SPL代币成功场景
  - 模拟代币余额变化的交易数据
  - 验证正确计算代币输出数量（post - pre）

- **`test_get_confirmed_token_output_from_tx_sol_success`**: 测试SOL代币成功场景
  - 模拟SOL余额变化
  - 验证SOL数量计算（lamports转换为SOL）

- **`test_get_confirmed_token_output_from_tx_failed_transaction`**: 测试失败交易
  - 模拟包含错误的交易
  - 验证返回None

- **`test_get_confirmed_token_output_from_tx_no_token_change`**: 测试无变化交易
  - 模拟余额无变化的交易
  - 验证返回None

- **`test_get_confirmed_token_output_from_tx_transaction_not_found`**: 测试交易不存在
  - 模拟交易查询失败
  - 验证返回None

- **`test_get_confirmed_token_output_from_tx_exception`**: 测试异常处理
  - 模拟处理过程中的异常
  - 验证错误处理和日志记录

### 4. `analyze_transaction` 方法测试

该方法用于分析交易数据，识别代币转账和SOL转账。

#### 测试用例：

- **`test_analyze_transaction_token_transfer`**: 测试代币转账分析
  - 模拟包含代币转账的交易
  - 验证正确识别转账类型和数量

- **`test_analyze_transaction_sol_transfer`**: 测试SOL转账分析
  - 模拟SOL转账交易
  - 验证余额变化计算

- **`test_analyze_transaction_no_changes`**: 测试无变化交易
  - 模拟没有余额变化的交易
  - 验证返回"unknown"类型

## Bug修复集成测试

### 5. Bug修复场景测试

这些测试专门验证Bug修复逻辑的正确性：

- **`test_bug_fix_scenario_balance_check_integration`**: 集成测试
  - 模拟完整的余额查询和交易确认流程
  - 验证Bug修复逻辑：使用当前余额而非历史记录
  - 确认保守策略：使用较小的数值作为卖出数量

- **`test_bug_fix_scenario_insufficient_balance`**: 余额不足场景
  - 模拟零余额情况
  - 验证应触发跳过交易的逻辑

- **`test_bug_fix_scenario_rpc_failure_handling`**: RPC失败处理
  - 模拟RPC节点不可用
  - 验证错误处理和通知机制

## 测试数据和Mock策略

### Mock对象配置

- **Solana RPC Client**: 使用 `MagicMock` 模拟所有RPC调用
- **Logger**: 模拟日志记录，避免测试输出干扰
- **测试数据**: 使用真实格式的Solana地址和交易哈希

### 测试常量

```python
test_wallet_address = "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM"
test_token_mint = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"  # USDC
test_sol_mint = "So11111111111111111111111111111111111111112"
test_tx_hash = "5VfYmGC52L1BS4oCNy8VtTGGiGD8KQz9Gm1uGmWkKkqGKqGm1uGmWkKkqGKqGm1uGmWkKkqG"
```

## 运行测试

### 单独运行SolanaMonitor测试

```bash
python -m pytest test/utils/spiders/solana/test_solana_monitor.py -v
```

### 运行所有相关测试

```bash
python -m pytest test/utils/spiders/ -v
```

## 测试重要性

这些测试对于确保Bug修复的有效性至关重要：

1. **防止回归**: 确保修复不会在未来的代码变更中被破坏
2. **验证边界条件**: 测试各种异常和边界情况
3. **文档化行为**: 测试用例作为方法行为的文档
4. **集成验证**: 确保各个方法协同工作正确

## 与Bug修复的关系

这些测试直接支持了以下Bug修复场景：

- **原问题**: 卖出交易因"insufficient funds"失败
- **根本原因**: 依赖不准确的历史买入记录
- **修复方案**: 使用实际钱包余额查询
- **测试验证**: 确保新逻辑在各种情况下都能正确工作

## 测试用例

| 用例方法 | 用例概述 | 前置条件 | 输入 | 预期输出 | 实际输出 | 状态 |
| --- | --- | --- | --- | --- | --- | --- |
| test_get_token_balance_success_with_balance | 测试成功获取有余额的代币 | Mock RPC返回有效代币账户 | 钱包地址、代币mint | 123.456789 | 123.456789 | ✅ |
| test_get_token_balance_success_zero_balance | 测试余额为0的情况 | Mock RPC返回空账户列表 | 钱包地址、代币mint | 0.0 | 0.0 | ✅ |
| test_get_token_balance_rpc_exception | 测试RPC异常处理 | Mock RPC抛出异常 | 钱包地址、代币mint | 0.0 | 0.0 | ✅ |
| test_get_token_balance_invalid_account_data | 测试无效数据格式处理 | Mock返回格式错误数据 | 钱包地址、代币mint | 0.0 | 0.0 | ✅ |
| test_get_transaction_details_success | 测试成功获取交易详情 | Mock RPC返回完整交易数据 | 交易哈希 | 交易详情对象 | 交易详情对象 | ✅ |
| test_get_transaction_details_not_found | 测试交易不存在 | Mock RPC返回空值 | 交易哈希 | None | None | ✅ |
| test_get_transaction_details_rpc_exception | 测试RPC异常 | Mock RPC抛出异常 | 交易哈希 | None | None | ✅ |
| test_get_confirmed_token_output_from_tx_spl_token_success | 测试SPL代币成功场景 | Mock交易包含代币余额变化 | 交易哈希、代币mint、钱包地址 | 50.0 | 50.0 | ✅ |
| test_get_confirmed_token_output_from_tx_sol_success | 测试SOL代币成功场景 | Mock交易包含SOL余额变化 | 交易哈希、SOL mint、钱包地址 | 0.5 | 0.5 | ✅ |
| test_get_confirmed_token_output_from_tx_failed_transaction | 测试失败交易 | Mock交易包含错误 | 交易哈希、代币mint、钱包地址 | None | None | ✅ |
| test_get_confirmed_token_output_from_tx_no_token_change | 测试无变化交易 | Mock交易余额无变化 | 交易哈希、代币mint、钱包地址 | None | None | ✅ |
| test_get_confirmed_token_output_from_tx_transaction_not_found | 测试交易不存在 | Mock交易查询失败 | 交易哈希、代币mint、钱包地址 | None | None | ✅ |
| test_get_confirmed_token_output_from_tx_exception | 测试异常处理 | Mock处理过程异常 | 交易哈希、代币mint、钱包地址 | None | None | ✅ |
| test_analyze_transaction_token_transfer | 测试代币转账分析 | Mock包含代币转账的交易 | 交易数据 | 代币转账分析结果 | 代币转账分析结果 | ✅ |
| test_analyze_transaction_sol_transfer | 测试SOL转账分析 | Mock SOL转账交易 | 交易数据 | SOL转账分析结果 | SOL转账分析结果 | ✅ |
| test_analyze_transaction_no_changes | 测试无变化交易 | Mock无余额变化交易 | 交易数据 | unknown类型 | unknown类型 | ✅ |
| test_bug_fix_scenario_balance_check_integration | Bug修复集成测试 | Mock完整余额查询流程 | 钱包地址、代币mint、交易哈希 | 使用当前余额75.5 | 使用当前余额75.5 | ✅ |
| test_bug_fix_scenario_insufficient_balance | 余额不足场景 | Mock零余额情况 | 钱包地址、代币mint | 0.0 | 0.0 | ✅ |
| test_bug_fix_scenario_rpc_failure_handling | RPC失败处理 | Mock RPC节点不可用 | 钱包地址、代币mint | 0.0并记录错误 | 0.0并记录错误 | ✅ |

## 测试统计

- **总测试用例数**: 19个
- **通过率**: 100% (19/19)
- **核心方法覆盖**: 4个主要方法
- **Bug修复场景**: 3个集成测试
- **异常处理测试**: 7个
- **成功路径测试**: 9个
- **运行时间**: 约0.70秒

## 测试验证结果

✅ **所有测试通过** - SolanaMonitor模块功能正常，Bug修复依赖的关键方法都能正确工作。

这些测试确保了 `SolanaMonitor` 模块的可靠性，特别是在自动交易系统的卖出功能中的关键作用。 