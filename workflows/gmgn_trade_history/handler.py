import logging
from dao.token_dao import TokenDAO
from utils.spiders.smart_money.gmgn_token_trades_spider import GmgnTokenTradesSpider
from typing import Any, Dict, AsyncGenerator, Optional
from dao.gmgn_token_trades_dao import GmgnTokenTradesDAO
from dao.token_trade_fetch_status_dao import TokenTradeFetchStatusDAO
import traceback

logger = logging.getLogger("GmgnTradeHistoryHandler")
spider = GmgnTokenTradesSpider()
gmgn_token_trades_dao = GmgnTokenTradesDAO()
tokens_dao = TokenDAO()
fetch_status_dao = TokenTradeFetchStatusDAO()


async def generate_data():
    async for address in tokens_dao.get_tokens_address():
        yield {"address": address, "chain": "sol"}

async def process_trade_history(token_dict: Dict) -> AsyncGenerator[Dict, None]:
    """根据代币抓取状态，获取并处理交易记录。

    - 检查代币的首次抓取状态。
    - 如果未完成或失败，则尝试从头或上次中断的游标开始抓取所有历史记录。
    - 如果已完成，则抓取增量更新。
    - 处理过程中更新抓取状态和游标。

    Args:
        token_dict (Dict): 包含 'address' 和 'chain' 的代币字典。

    Yields:
        Dict: 单条交易记录。
    """
    token_address = token_dict.get("address")
    chain = token_dict.get("chain")

    if not token_address or not chain:
        logger.error(f"输入数据缺少 address 或 chain: {token_dict}")
        return

    logger.info(f"开始处理: {chain}:{token_address}")

    status_doc = await fetch_status_dao.get_or_create_status(token_address, chain)
    current_status = status_doc.initial_fetch_status
    start_cursor = status_doc.next_cursor
    latest_trade_timestamp = None
    revert_fetch = True
    is_initial_fetch = False

    logger.info(f"当前抓取状态: {current_status}, 下一个游标: {start_cursor} for {chain}:{token_address}")

    if current_status == "completed":
        logger.info(f"初始抓取已完成，执行增量更新 for {chain}:{token_address}")
        latest_trade_timestamp = await gmgn_token_trades_dao.get_latest_trade_timestamp(token_address, chain)
        start_cursor = None
        revert_fetch = False
        is_initial_fetch = False
        logger.info(f"最新交易时间戳: {latest_trade_timestamp} for {chain}:{token_address}")
    else:
        logger.info(f"初始抓取状态为 {current_status}，执行初始/恢复抓取 for {chain}:{token_address}")
        latest_trade_timestamp = None
        revert_fetch = True
        is_initial_fetch = True
        if current_status != "failed":
            await fetch_status_dao.update_status(token_address, chain, status="in_progress")

    current_page_cursor = start_cursor
    error_occurred = False
    error_message = None
    
    try:
        trade_count = 0
        async for trade_history in spider.get_token_trades_yield(
            chain=chain,
            address=token_address,
            tag="",
            limit=100,
            interval=0.1,
            until_trade_timestamp=latest_trade_timestamp,
            start_cursor=start_cursor,
            revert=revert_fetch
        ):
            yield trade_history
            trade_count += 1

        logger.info(f"抓取循环完成 for {chain}:{token_address}, 共 yield {trade_count} 条记录.")
        if is_initial_fetch:
             await fetch_status_dao.update_status(
                 token_address, chain, 
                 status="completed", 
                 set_next_cursor_none=True,
                 error_message=None
             )
             logger.info(f"成功完成初始抓取 for {chain}:{token_address}, 状态更新为 completed.")

    except Exception as e:
        error_occurred = True
        error_message = f"{type(e).__name__}: {str(e)}"
        logger.error(f"处理 {chain}:{token_address} 时发生错误 (cursor: {start_cursor}): {error_message}", exc_info=True)
        
        if is_initial_fetch:
            await fetch_status_dao.update_status(
                token_address, chain, 
                status="failed", 
                next_cursor=start_cursor,
                error_message=error_message[:500]
            )
            logger.warning(f"初始抓取失败 for {chain}:{token_address}, 状态更新为 failed, 下次将从 cursor {start_cursor} 尝试恢复.")
        else:
            await fetch_status_dao.update_status(
                token_address, chain, 
                status="failed", 
                set_next_cursor_none=True,
                error_message=error_message[:500]
            )
            logger.warning(f"增量更新失败 for {chain}:{token_address}, 状态更新为 failed.")

    finally:
        # 可以在这里添加最终确认逻辑，但当前逻辑主要依赖 try/except 块
        pass 


async def store_data(trade_history: Any):
    if not trade_history:
        return 0
    
    trade_history_list = trade_history if isinstance(trade_history, list) else [trade_history]
    
    try:
        await gmgn_token_trades_dao.insert_trades(trade_history_list)
        logger.info(f"存储代币交易记录成功: {len(trade_history_list)}条")
        return len(trade_history_list)
    except Exception as e:
        logger.error(f"存储代币交易记录时发生错误: {e}")
        return 0

async def validate(trade_history: Any):
    return True