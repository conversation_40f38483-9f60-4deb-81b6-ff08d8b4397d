# KOL打分功能 - 技术实现方案 (AI生成)

## 1. 概述

本方案旨在实现KOL打分功能。核心思路是**创建一个全新的、独立的KOL打分工作流。此工作流将采用定时轮询机制，负责处理已完成的、且其真实盈亏已被 `trade_record_verification_updater` 工作流验证过的交易对**。

具体流程如下:
1.  新的KOL打分工作流定时启动。
2.  工作流执行一个优化的数据获取和过滤过程，目标是识别出**"尚未在 `TradeScoreLog` 中针对特定KOL和策略记录过的、已验证的、已成功匹配的交易对"**。此过程将主要通过MongoDB聚合查询实现，大致步骤如下：
    a.  **定位已验证的成功卖出交易 (`TradeRecord`)**: 筛选 `trade_records` 集合中 `trade_type == SELL`, `status == SUCCESS`, 且 `verification_status == 'verified'` (或项目中实际表示真实金额已计算的状态) 的记录。
    b.  **关联卖出信号 (`Signal`)**: 对每个符合条件的卖出交易记录，通过其 `signal_id` 查找对应的卖出 `Signal` 文档。
    c.  **获取买入信号ID**: 从卖出 `Signal` 文档中提取 `buy_signal_ref_id`。如果此ID不存在，则该卖出交易无法配对，跳过。
    d.  **定位对应的已验证买入交易 (`TradeRecord`)**: 使用上一步的 `buy_signal_ref_id` (作为买入交易的 `signal_id`) 和原始卖出交易的 `wallet_address`，在 `trade_records` 集合中查找匹配的买入交易记录 (`trade_type == BUY`, `status == SUCCESS`, `verification_status == 'verified'`, 且 `wallet_address` 相同)。如果找不到匹配的买入交易，则跳过。
    e.  此时，我们有了一个已成功匹配且双方均已验证的（买入交易记录, 卖出交易记录）交易对。
    f.  **确定关联的KOL和策略**: 
        i.  `strategy_name`: 从买入交易记录的 `strategy_name` 字段获取。
        ii. `associated_kol_wallet_addresses`: 通过买入交易记录的 `signal_id` (即买入信号的ID) 查找对应的买入 `Signal` 文档，并从中提取 `hit_kol_wallets` (KOL钱包地址列表)。
    g.  **转换为KOL Wallet对象/ID**: (如果 `TradeScoreLog` 中存储的是 `Link[KOLWallet]`) 将获取到的KOL钱包地址列表转换为 `KOLWallet` 的 `_id` 或Beanie Link对象。
    h.  **过滤已打分组合**: 对于形成的每一个 (交易对, 具体KOL的Wallet对象/ID, 策略名称) 组合，通过聚合中的 `$lookup` (或后续批量查询) 检查 `TradeScoreLog` 是否已存在相应的打分记录。只保留那些尚未被记录的组合。
3.  对于每一个从上一步筛选出的"未打分的 (交易对, KOL, 策略)"组合，调用 `KOLScoringService` 进行打分处理。
4.  `KOLScoringService` 内部 (此时已确认该 (交易对, KOL, 策略) 组合未在 `TradeScoreLog` 中记录过)：
    a.  如果 `strategy_name` 为空或无法确定 (理论上在步骤2c中已处理，但可作为防御性检查)，服务将记录一条警告日志并中止。
    b.  根据交易对的盈亏情况 (PnL) 和从 `KOLScoringConfig` 中获取的、针对该策略的有效打分规则，计算得分。
    c.  原子化地更新KOL在该特定策略下的总评分 (`KOLStrategyScore` 表)。
    d.  在 `TradeScoreLog` 表中记录详细的打分日志。

此方案通过在工作流早期阶段整合"基于`Signal.buy_signal_ref_id`的精确交易对匹配"与"基于`TradeScoreLog`的打分历史检查"，优化了处理流程，减少了服务层的冗余判断。查询大量数据时，仍应采用数据库游标/分页机制，并结合Python生成器（`yield`）逐条处理，避免内存压力。

## 2. 详细设计

### 2.1. 数据模型设计: `KOLWallet` (`models/kol_wallet.py`)
-   保持不变。不存储任何打分信息。
    ```python
    from beanie import Document
    from pydantic import Field
    from typing import Optional, List
    from datetime import datetime

    class KOLWallet(Document):
        kol_id: str = Field(..., description="KOL的唯一标识符，例如Twitter用户ID")
        wallet_address: str = Field(..., description="KOL的钱包地址", unique=True)
        source: str = Field(..., description="KOL信息来源，例如 'twitter', 'gmgn'")
        name: Optional[str] = Field(default=None, description="KOL的名称或昵称")
        avatar_url: Optional[str] = Field(default=None, description="KOL头像URL")
        follower_count: Optional[int] = Field(default=None, description="KOL粉丝数量 (例如Twitter粉丝数)")
        
        created_at: datetime = Field(default_factory=datetime.utcnow)
        updated_at: datetime = Field(default_factory=datetime.utcnow)

        class Settings:
            name = "kol_wallets"
            indexes = [
                "kol_id",
                "wallet_address",
                "source",
            ]
    ```

### 2.2. 数据模型设计: `KOLStrategyScore` (`models/kol_strategy_score.py`)
-   存储KOL在特定策略下的总加分和总扣分。
    ```python
    from beanie import Document, Link
    from pydantic import Field
    from typing import Optional
    from datetime import datetime
    from models.kol_wallet import KOLWallet # 假设KOLWallet定义在同级或可导入

    class KOLStrategyScore(Document):
        kol_wallet: Link[KOLWallet]
        strategy_name: str = Field(..., description="策略名称")
        
        total_positive_score: float = Field(default=0.0, description="该策略下的总加分")
        total_negative_score: float = Field(default=0.0, description="该策略下的总扣分 (通常为负数或0)")
        
        last_scored_at: Optional[datetime] = Field(default=None, description="最后计分时间")
        
        created_at: datetime = Field(default_factory=datetime.utcnow)
        updated_at: datetime = Field(default_factory=datetime.utcnow)

        class Settings:
            name = "kol_strategy_scores"
            indexes = [
                [("kol_wallet._id", 1), ("strategy_name", 1), {"unique": True}], # 确保KOL和策略组合的唯一性
                "strategy_name",
                "last_scored_at",
            ]
    ```

### 2.3. 数据模型设计: `TradeScoreLog` (`models/trade_score_log.py`)
-   记录每一笔交易对KOL在该策略下的具体打分详情。
    ```python
    from beanie import Document, Link
    from pydantic import Field
    from typing import Optional, Any, Dict
    from datetime import datetime
    from models.kol_wallet import KOLWallet # 假设KOLWallet定义在同级或可导入
    # 假设TradeRecord定义在 models.trade_record
    # from models.trade_record import TradeRecord 

    class TradeScoreLog(Document):
        # 使用 str 类型存储 TradeRecord 的 ID，避免直接 Link 导致 TradeRecord 被修改时产生问题
        # 或者，如果 TradeRecord 是稳定的，可以考虑使用 Link[TradeRecord]
        buy_trade_record_id: str = Field(..., description="关联的买入TradeRecord的ID")
        sell_trade_record_id: str = Field(..., description="关联的卖出TradeRecord的ID")
        
        kol_wallet: Link[KOLWallet]
        strategy_name: str = Field(..., description="打分时应用的策略名称")
        
        pnl_at_scoring: float = Field(..., description="打分时计算的该笔交易对的盈亏 (PnL)")
        
        positive_score_applied: float = Field(default=0.0, description="本次应用的加分值")
        negative_score_applied: float = Field(default=0.0, description="本次应用的扣分值 (通常为负数或0)")
        
        # 快照当时的打分参数，用于追溯和调试
        scoring_params_snapshot: Dict[str, Any] = Field(..., description="打分时使用的参数快照")
        
        scored_at: datetime = Field(default_factory=datetime.utcnow, description="打分发生时间")

        class Settings:
            name = "trade_score_logs"
            indexes = [
                # 核心索引，用于快速检查某交易对、某KOL、某策略是否已打分
                [
                    ("buy_trade_record_id", 1),
                    ("sell_trade_record_id", 1),
                    ("kol_wallet._id", 1),
                    ("strategy_name", 1),
                    {"unique": True} # 确保唯一性，防止重复打分日志
                ],
                "kol_wallet._id",
                "strategy_name",
                "scored_at",
            ]
    ```

### 2.4. 数据模型设计: `TradeRecord` (`models/trade_record.py`)
-   **不进行任何修改**。所有打分相关的状态和日志通过 `TradeScoreLog` 和 `KOLStrategyScore` 管理。

### 2.5. 配置模型设计: `KOLScoringConfig` (`models/config.py`)
-   在 `models/config.py` (或其他集中的配置模型文件) 中定义KOL打分相关的配置。
    ```python
    from pydantic import BaseModel, Field
    from typing import Optional, Dict, Any

    class KOLStrategyScoringParams(BaseModel):
        """单个策略的特定打分参数"""
        positive_score: Optional[float] = Field(default=None, description="盈利时的固定加分值。如果为None，则使用全局默认值。")
        negative_score_multiplier: Optional[float] = Field(default=None, description="亏损时的扣分乘数 (乘以PnL绝对值)。如果为None，则使用全局默认值。PnL为负时，最终扣分 = abs(PnL) * multiplier * -1")

    class KOLScoringConfig(BaseModel):
        """KOL打分功能的配置模型"""
        # 全局默认参数
        default_positive_score: float = Field(default=1.0, description="全局默认：盈利时的固定加分值")
        default_negative_score_multiplier: float = Field(default=1.0, description="全局默认：亏损时的扣分乘数。最终扣分 = abs(PnL) * multiplier * -1")
        
        # 策略特定参数
        # 键是 strategy_name
        strategy_specific_params: Dict[str, KOLStrategyScoringParams] = Field(default_factory=dict, description="策略特定的打分参数，用于覆盖全局默认值")

        def get_effective_params_for_strategy(self, strategy_name: str) -> Dict[str, float]:
            """
            获取指定策略的有效打分参数。
            如果策略特定参数存在，则使用；否则回退到全局默认参数。
            """
            strategy_params = self.strategy_specific_params.get(strategy_name)
            
            effective_positive_score = self.default_positive_score
            effective_negative_multiplier = self.default_negative_score_multiplier
            
            if strategy_params:
                if strategy_params.positive_score is not None:
                    effective_positive_score = strategy_params.positive_score
                if strategy_params.negative_score_multiplier is not None:
                    effective_negative_multiplier = strategy_params.negative_score_multiplier
            
            return {
                "positive_score": effective_positive_score,
                "negative_score_multiplier": effective_negative_multiplier
            }

    # 示例：在总配置模型中集成
    # class GlobalAppConfig(BaseModel):
    #     # ... other configs ...
    #     kol_scoring_config: KOLScoringConfig = Field(default_factory=KOLScoringConfig)

    # class Config(Document): # If using Beanie for config storage
    #     # ... other configs ...
    #     kol_scoring_config: KOLScoringConfig = Field(default_factory=KOLScoringConfig)
    #     class Settings:
    #         name = "global_app_configs" # example collection name
    ```

### 2.6. DAO层设计: `KOLWalletDAO` (`dao/kol_wallet_dao.py`)
-   保持现有功能，无需为打分功能直接修改。主要用于获取KOL信息。

### 2.7. DAO层设计: `KOLStrategyScoreDAO` (`dao/kol_strategy_score_dao.py`)
-   **文字伪代码描述**:
    -   `async def get_or_create_score(kol_wallet_address: str, strategy_name: str) -> KOLStrategyScore`:
        -   根据 `kol_wallet_address` (KOL的钱包地址) 调用 `KOLWalletDAO` (或直接查询 `KOLWallet` 集合) 获取对应的 `KOLWallet` 文档实例。
        -   如果 `KOLWallet` 文档不存在，则记录错误或抛出异常，因为无法关联打分主体。
        -   使用获取到的 `KOLWallet` 实例的 `Link` (Beanie的 `Link[KOLWallet]`) 和 `strategy_name` 查找 `KOLStrategyScore` 记录。
        -   如果找到，返回该记录。
        -   如果未找到，创建一个新的 `KOLStrategyScore` 记录 (使用 `KOLWallet` Link, `strategy_name`，初始分数为0) 并返回。
    -   `async def update_score(kol_wallet_address: str, strategy_name: str, positive_score_change: float = 0, negative_score_change: float = 0) -> bool`:
        -   首先，调用 `get_or_create_score(kol_wallet_address, strategy_name)` 来确保 `KOLStrategyScore` 记录存在，并获取到该记录的实例。如果此步骤失败（例如KOL钱包不存在），则应中断并返回失败。
        -   使用 `find_one_and_update` (或Beanie提供的等效原子操作方法，如对已获取实例的 `save()` 前进行修改) 在获取到的 `KOLStrategyScore` 记录上。
        -   使用 `$inc` 操作符原子性的增加 `total_positive_score` 和 `total_negative_score`。
        -   同时更新 `updated_at` 和 `last_scored_at` 字段。
        -   返回操作是否成功。

### 2.8. DAO层设计: `TradeScoreLogDAO` (`dao/trade_score_log_dao.py`)
-   **文字伪代码描述**:
    -   `async def create_log_entry(log_data: TradeScoreLog) -> TradeScoreLog`:
        -   简单插入一条新的 `TradeScoreLog` 记录。
        -   `log_data` 应包含所有必要字段，如 `buy_trade_record_id`, `sell_trade_record_id`, `kol_wallet` (Link), `strategy_name`, `pnl_at_scoring`, `positive_score_applied`, `negative_score_applied`, `scoring_params_snapshot`。
    -   `async def has_log_entry_existed(buy_trade_record_id: str, sell_trade_record_id: str, kol_wallet_address: str, strategy_name: str) -> bool`:
        -   根据 `kol_wallet_address` 获取 `KOLWallet` 实例的 `Link`。如果获取失败，应认为日志不存在或处理错误。
        -   使用 `buy_trade_record_id`, `sell_trade_record_id`, 获取到的 `KOLWallet` Link, 和 `strategy_name` 查询 `TradeScoreLog` 集合。
        -   如果查询到任何匹配记录，返回 `True`。

### 2.9. 服务层设计: `KOLScoringService` (`utils/kol_scoring_service.py`)
-   **文字伪代码描述**:
    -   `class KOLScoringService:`
        -   `__init__(self, kol_strategy_score_dao: KOLStrategyScoreDAO, trade_score_log_dao: TradeScoreLogDAO, config_dao: ConfigDAO)`:
            -   初始化DAO实例。`ConfigDAO` 用于获取 `KOLScoringConfig`。
        -   `async def process_trade_for_kol_scoring(self, buy_trade_record: TradeRecord, sell_trade_record: TradeRecord, kol_wallet: KOLWallet, strategy_name: Optional[str])`:
            -   **1. 检查策略名称**:
                -   如果 `strategy_name` 为 `None` 或空字符串：
                    -   记录警告日志 ("KOLScoringService: 接收到无效的strategy_name。KOL: {kol_wallet.id}, 交易对: ({buy_trade_record.id}, {sell_trade_record.id})，跳过打分。")
                    -   直接返回，不进行后续处理。
            -   **2. 防重复检查 (重要说明)**:
                -   **此步骤的显式 `has_log_entry_existed` 调用已由上游工作流在数据获取与过滤阶段完成。**
                -   `KOLScoringService` 假定传递给它的 (交易对, KOL, 策略) 组合是经过筛选的，即尚未在 `TradeScoreLog` 中存在记录。
            -   **3. 获取打分配置**:
                -   从 `self.config_dao` 获取当前的 `KOLScoringConfig`。
                -   调用 `config.kol_scoring_config.get_effective_params_for_strategy(strategy_name)` 获取该策略的有效打分参数 (`effective_positive_score`, `effective_negative_multiplier`)。
            -   **4. 计算 PnL**:
                -   `pnl = sell_trade_record.token_out_verified_amount - buy_trade_record.token_in_amount` (确保这两个字段是已验证的真实金额，如 `buy_trade_record.token_in_actual_amount` 和 `sell_trade_record.token_out_verified_amount`)。
            -   **5. 计算得分变更**:
                -   `positive_score_change = 0.0`
                -   `negative_score_change = 0.0`
                -   如果 `pnl > 0`:
                    -   `positive_score_change = effective_positive_score`
                -   elif `pnl < 0`:
                    -   `negative_score_change = effective_negative_multiplier * -1` (固定分数)
                -   (如果 pnl == 0，则分数不变)
            -   **6. 更新KOL策略总分**:
                -   调用 `self.kol_strategy_score_dao.update_score(kol_wallet.id, strategy_name, positive_score_change, negative_score_change)`。
                -   确保此操作是原子的。如果 `update_score` 之前没有记录，它应该能正确创建。或者，先调用 `get_or_create_score`。
            -   **7. 创建打分日志**:
                -   准备 `TradeScoreLog` 数据，包括 `buy_trade_record_id`, `sell_trade_record_id`, `kol_wallet` 的Link, `strategy_name`, `pnl_at_scoring=pnl`, `positive_score_applied=positive_score_change`, `negative_score_applied=negative_score_change`, 以及 `scoring_params_snapshot` (包含 `effective_positive_score`, `effective_negative_multiplier`)。
                -   调用 `self.trade_score_log_dao.create_log_entry(log_data)`。
            -   记录成功处理的日志。

### 2.10. 新KOL打分工作流 (`workflows/kol_scoring_workflow/`)

-   **创建新目录**: `workflows/kol_scoring_workflow/`
-   **配置文件**: `workflows/kol_scoring_workflow/kol_scoring_workflow.yaml`
-   **处理器**: `workflows/kol_scoring_workflow/handler.py` (或者直接在 `utils/workflows/nodes/` 中创建新的节点类型，然后在yaml中引用)

-   **文字伪代码描述 (`handler.py` 和 `kol_scoring_workflow.yaml`)**:
    -   **触发机制**:
        -   工作流配置为定时任务 (例如，通过 `run_jobs.py` 每隔N分钟/小时执行一次)。
    -   **核心处理逻辑 (单个工作流节点或多个串联节点)**:
        -   **节点1: 获取并过滤待打分交易组合 (通过聚合查询或Python与查询结合)**
            -   **a. 查询所有已验证的成功卖出交易 (`TradeRecord`)**: 
                -   筛选条件：`trade_type == TradeType.SELL`, `status == TradeStatus.SUCCESS`, `verification_status == 'verified'`。
                -   使用数据库游标/分页及Python生成器处理。
            -   **b. 对于每个卖出交易，尝试匹配对应的已验证买入交易，形成交易对**: 
                -   `$lookup` 卖出交易关联的卖出 `Signal` (via `sell_trade_record.signal_id`)。
                -   从卖出 `Signal` 获取 `buy_signal_ref_id`。
                -   如果 `buy_signal_ref_id` 存在，则 `$lookup` 对应的买入 `TradeRecord` (条件：`signal_id == buy_signal_ref_id`, `wallet_address == sell_trade_record.wallet_address`, `trade_type == BUY`, `status == SUCCESS`, `verification_status == 'verified'`)。
            -   **c. 确定KOL和策略，并过滤已打分项**:
                -   初始化一个列表 `unscored_trade_kol_strategy_combinations = []`。
                -   对于每个成功匹配的交易对 (`buy_record`, `sell_record`):
                    -   `strategy_name = buy_record.strategy_name`。
                    -   `$lookup` 买入交易关联的买入 `Signal` (via `buy_record.signal_id`)，获取其 `hit_kol_wallets` (钱包地址列表)。
                    -   如果无法找到KOL钱包地址或策略名称，记录日志并跳过此交易对。
                    -   对于 `hit_kol_wallets` 中的每个 `kol_wallet_address`:
                        -   将 `kol_wallet_address` 转换为 `KOLWallet` 对象或其在数据库中的 `_id` (可能需要查询 `kol_wallets` 表)。
                        -   **检查 `TradeScoreLog`**: 调用 `trade_score_log_dao.has_log_entry_existed(buy_record.id, sell_record.id, kol_wallet_object_or_id, strategy_name)`。
                        -   如果返回 `False` (表示未打分)：
                            -   将 `(buy_record, sell_record, kol_wallet_object_or_id, strategy_name)` 这个组合添加到 `unscored_trade_kol_strategy_combinations`。
            -   **d. 传递结果**: 将 `unscored_trade_kol_strategy_combinations` 列表中的每一项逐个传递给下一个节点或步骤。
                -   *优化说明*: 上述步骤 b 和 c 中的 `$lookup` 和 `has_log_entry_existed` 检查可以尝试整合到一个更复杂的MongoDB聚合管道中，以减少应用服务器和数据库之间的往返次数。目标是让聚合查询直接输出需要打分的 (交易对, KOL, 策略) 组合列表。如果聚合过于复杂难以维护，则当前伪代码描述的Python层面循环配合DAO调用也是一种清晰的实现方式，但需注意批量操作以优化性能（例如，收集一批keys再一次性查询`TradeScoreLog`）。

        -   **节点2: 执行KOL打分 (针对每个未打分的组合)**
            -   输入：一个组合 `(buy_trade_record, sell_trade_record, kol_wallet_object_or_id, strategy_name)`。
            -   实例化 `KOLScoringService` (依赖注入DAO实例)。
            -   调用 `kol_scoring_service.process_trade_for_kol_scoring(buy_trade_record, sell_trade_record, kol_wallet_object_or_id, strategy_name)`。
            -   捕获 `process_trade_for_kol_scoring` 可能抛出的异常，并记录详细错误日志，确保单个组合打分失败不影响其他组合或整个工作流的后续处理。
        -   **工作流级别的错误处理和日志**:
            -   记录工作流的开始、结束时间，处理的组合数量，成功打分的数量等。
            -   如果整个工作流执行失败（例如数据库连接问题），应有重试机制或告警。

## 3. 潜在风险与考虑

-   **数据量**: 如果 `TradeRecord` 非常多，全量查询和匹配交易对可能较慢。**已通过移除时间窗口并强调游标/分页机制来缓解。**
-   **KOL与交易的关联**: 已明确。通过 `TradeRecord` -> `Signal` -> `Signal.hit_kol_wallets` (KOL钱包地址列表) 进行关联。
-   **策略的确定**: 已明确。通过 `TradeRecord.strategy_name` 获取。
-   **原子性**: `KOLStrategyScoreDAO.update_score` 必须保证原子性。
-   **配置管理**: `KOLScoringConfig` 如何加载和更新。通常通过DAO从数据库读取或从配置文件加载。
-   **历史数据回溯**: 如果需要对历史数据进行打分，此工作流可以手动触发执行一次，处理所有符合条件的旧交易。

## 4. 未来扩展
-   支持更复杂的打分规则（例如，考虑持仓时间、交易频率等）。
-   引入KOL的风险评分或黑名单机制。
-   基于KOL分数进行排行和展示。

## 5. 总结
此方案设计了一个独立的、基于定时轮询的KOL打分工作流，通过解耦打分逻辑和交易验证逻辑，实现了对KOL表现的量化评估。核心依赖 `TradeScoreLog` 来确保打分的幂等性和可追溯性，并通过 `KOLScoringConfig` 实现打分规则的灵活性。 

## 修改记录

### 2025年1月19日 - 评分算法优化

#### 📋 修改背景
在生产环境验证过程中，发现原有的比例化负向评分设计存在实际应用问题：
- **正向评分**：固定分数模式工作良好（10分/笔盈利交易）
- **负向评分**：基于PnL比例计算产生微小扣分值（-0.0011分），与正向评分不平衡

#### 🔄 算法调整
**原设计**（2.9节 `KOLScoringService`）:
```
如果 pnl < 0:
    negative_score_change = abs(pnl) * effective_negative_multiplier * -1
```

**优化后设计**:
```
如果 pnl < 0:
    negative_score_change = effective_negative_multiplier * -1  # 固定分数
```

#### 📊 参数含义调整
**配置模型更新**（2.5节 `KOLScoringConfig`）:
- `negative_score_multiplier`: 
  - **原含义**: 亏损时的扣分乘数（乘以PnL绝对值）
  - **新含义**: 亏损时的固定扣分值（直接作为负向分数）

#### 🔧 实现变更
**核心服务逻辑**（2.9节）:
- **计算 PnL**: 保持不变，继续基于 `token_in_actual_amount` 和 `token_out_actual_amount`
- **PnL记录**: 继续保存到 `TradeScoreLog.pnl_at_scoring` 用于分析
- **分数计算**: 统一使用固定分数模式，不再基于PnL比例

#### 💡 设计优势
1. **评分平衡**: 正负向评分采用相同的固定分数机制
2. **配置简化**: 负向分数参数直接表示扣分值，更直观
3. **系统稳定**: 避免因交易金额差异导致的评分波动
4. **分析保留**: PnL信息仍完整保存，支持后续数据分析

#### 📈 兼容性说明
- **配置兼容**: 现有配置结构保持不变，仅参数语义调整
- **数据兼容**: 历史评分数据不受影响，新评分采用新算法
- **接口兼容**: 服务接口和数据模型无变更

#### 🎯 后续扩展
此优化为未来更复杂的评分模式奠定基础：
- 支持基于交易频率的动态权重
- 支持基于时间衰减的历史评分调整
- 支持多维度综合评分机制 