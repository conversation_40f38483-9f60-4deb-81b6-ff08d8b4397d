from typing import List, Dict, Any, Optional
import datetime
from beanie.exceptions import RevisionIdWasChanged
# from beanie.odm.operators.find import In # 移除 In 的导入
from models.gmgn_token_trades import GmgnTokenTrade
import logging

logger = logging.getLogger("GmgnTokenTradesDAO")

class GmgnTokenTradesDAO:
    """
    用于 GmgnTokenTrade 模型的数据库访问对象
    """
    
    @staticmethod
    async def insert_trades(trades_data: List[Dict[str, Any]]) -> int:
        """
        批量插入交易数据，使用 Pydantic V2 的 model_validate 进行验证和转换。

        Args:
            trades_data: 从 API 获取的交易数据列表。

        Returns:
            成功插入的记录数量。
        """
        if not trades_data:
            logger.info("没有提供交易数据用于插入。")
            return 0
        
        inserted_count = 0
        skipped_count = 0
        trades_to_insert = []
        
        # 优化：一次性获取所有传入数据的 trade_id
        trade_ids_in_batch = {trade_data.get("id") for trade_data in trades_data if trade_data.get("id")}
        if not trade_ids_in_batch:
            logger.warning("提供的交易数据中没有有效的 'id' 字段。")
            return 0
            
        # 优化：一次性查询数据库中已存在的 trade_id
        try:
            # 使用 MongoDB 原生的 $in 操作符进行查询
            query_doc = {"trade_id": {"$in": list(trade_ids_in_batch)}}
            existing_trades_cursor = GmgnTokenTrade.find(query_doc)
            existing_trades = await existing_trades_cursor.to_list()
            existing_trade_ids = {trade.trade_id for trade in existing_trades}
            logger.info(f"在批次 {len(trade_ids_in_batch)} 个ID中，数据库已存在 {len(existing_trade_ids)} 个。")
        except Exception as e:
            logger.error(f"查询已存在 trade_id 时出错: {e}", exc_info=True)
            return 0 # 查询失败则不继续插入

        for trade_data in trades_data:
            api_trade_id = trade_data.get("id")
            if not api_trade_id:
                skipped_count += 1
                continue

            if api_trade_id in existing_trade_ids:
                skipped_count += 1
                continue

            validation_data = trade_data.copy()
            if "id" in validation_data:
                validation_data["trade_id"] = validation_data.pop("id")
            else:
                logger.warning(f"API 数据中缺少 'id' 键: {trade_data}")
                skipped_count += 1
                continue

            try:
                trade_obj = GmgnTokenTrade.model_validate(validation_data)
                trades_to_insert.append(trade_obj)
            except Exception as e:
                logger.error(f"验证/转换交易数据时出错 (API ID: {api_trade_id}): {validation_data}, 错误: {e}", exc_info=True)
                skipped_count += 1

        if trades_to_insert:
            logger.info(f"准备插入 {len(trades_to_insert)} 条新的 GmgnTokenTrade 记录。")
            try:
                # 使用 insert_many 进行批量插入
                result = await GmgnTokenTrade.insert_many(trades_to_insert)
                inserted_count = len(result.inserted_ids)
                logger.info(f"成功插入 {inserted_count} 条 GmgnTokenTrade 记录.")
            except RevisionIdWasChanged as e:
                # Beanie 特定的并发控制异常，可能在某些场景下需要处理
                logger.warning(f"插入 GmgnTokenTrade 时发生 RevisionIdWasChanged 错误 (可能由并发更新引起): {e}")
                # 可以选择重试或记录为部分失败
                inserted_count = 0 # 或者尝试获取部分成功的数量，但这比较复杂
            except Exception as e:
                logger.error(f"批量插入 GmgnTokenTrade 时发生未知错误: {e}", exc_info=True)
                inserted_count = 0 # 插入失败
        
        if skipped_count > 0:
            logger.info(f"跳过 {skipped_count} 条重复或无效的 GmgnTokenTrade 记录.")

        return inserted_count

    @staticmethod
    async def find_trades_by_token(token_address: str, chain: str, limit: int = 100, sort_desc: bool = True) -> List[GmgnTokenTrade]:
        """
        根据代币地址和链查找交易记录。

        Args:
            token_address: 代币地址。
            chain: 链名称。
            limit: 返回的最大记录数。
            sort_desc: 是否按时间戳降序排序。

        Returns:
            交易记录列表。
        """
        sort_order = -1 if sort_desc else 1
        return await GmgnTokenTrade.find(
            GmgnTokenTrade.token_address == token_address,
            GmgnTokenTrade.chain == chain
        ).sort(("timestamp", sort_order)).limit(limit).to_list()

    @staticmethod
    async def find_trade_by_tx_hash(tx_hash: str) -> Optional[GmgnTokenTrade]:
        """
        根据交易哈希查找交易记录。

        Args:
            tx_hash: 交易哈希。

        Returns:
            找到的交易记录或 None。
        """
        return await GmgnTokenTrade.find_one(GmgnTokenTrade.tx_hash == tx_hash)

    @staticmethod
    async def get_latest_trade_timestamp(token_address: str, chain: str) -> Optional[datetime.datetime]:
        """
        获取指定代币的最新交易时间戳。

        Args:
            token_address: 代币地址。
            chain: 链名称。

        Returns:
            最新的交易时间戳或 None。
        """
        latest_trade = await GmgnTokenTrade.find(
            GmgnTokenTrade.token_address == token_address,
            GmgnTokenTrade.chain == chain
        ).sort(("timestamp", -1)).limit(1).first_or_none()
        
        return latest_trade.timestamp if latest_trade else None

    @staticmethod
    async def get_earliest_trade_timestamp(token_address: str, chain: str) -> Optional[datetime.datetime]:
        """
        获取指定代币的最早交易时间戳。

        Args:
            token_address: 代币地址。
            chain: 链名称。

        Returns:
            最早的交易时间戳或 None。
        """
        earliest_trade = await GmgnTokenTrade.find(
            GmgnTokenTrade.token_address == token_address,
            GmgnTokenTrade.chain == chain
        ).sort(("timestamp", 1)).limit(1).first_or_none()

        return earliest_trade.timestamp if earliest_trade else None 