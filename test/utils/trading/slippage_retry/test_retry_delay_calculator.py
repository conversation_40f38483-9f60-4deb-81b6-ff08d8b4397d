"""重试间隔计算器单元测试"""

import unittest
from models.config import RetryDelayStrategy, TradingParams
from utils.trading.slippage_retry.retry_delay_calculator import RetryDelayCalculator


class TestRetryDelayCalculator(unittest.TestCase):
    """重试间隔计算器测试类"""
    
    def setUp(self) -> None:
        """测试前准备"""
        self.calculator = RetryDelayCalculator()
        self.trading_params = TradingParams(
            retry_delay_seconds=1.0,
            max_retry_delay_seconds=10.0,
            retry_delay_strategy=RetryDelayStrategy.FIXED,
            slippage_error_delay_seconds=0.5
        )
    
    def test_calculate_delay_fixed_strategy(self) -> None:
        """测试固定间隔策略"""
        self.trading_params.retry_delay_strategy = RetryDelayStrategy.FIXED
        
        # 多次重试应该使用相同间隔
        for retry_count in [1, 2, 3, 5]:
            delay = self.calculator.calculate_delay(
                retry_count=retry_count,
                trading_params=self.trading_params,
                trade_type="buy"
            )
            self.assertEqual(delay, 1.0)
    
    def test_calculate_delay_linear_strategy(self) -> None:
        """测试线性递增策略"""
        self.trading_params.retry_delay_strategy = RetryDelayStrategy.LINEAR
        
        # 线性递增测试
        test_cases = [
            (1, 1.0),  # 1 * 1.0
            (2, 2.0),  # 2 * 1.0
            (3, 3.0),  # 3 * 1.0
            (5, 5.0),  # 5 * 1.0
        ]
        
        for retry_count, expected_delay in test_cases:
            delay = self.calculator.calculate_delay(
                retry_count=retry_count,
                trading_params=self.trading_params,
                trade_type="buy"
            )
            self.assertEqual(delay, expected_delay)
    
    def test_calculate_delay_exponential_strategy(self) -> None:
        """测试指数退避策略"""
        self.trading_params.retry_delay_strategy = RetryDelayStrategy.EXPONENTIAL
        
        # 指数退避测试: base * (2^(retry_count-1))
        test_cases = [
            (1, 1.0),  # 1.0 * 2^0 = 1.0
            (2, 2.0),  # 1.0 * 2^1 = 2.0
            (3, 4.0),  # 1.0 * 2^2 = 4.0
            (4, 8.0),  # 1.0 * 2^3 = 8.0
        ]
        
        for retry_count, expected_delay in test_cases:
            delay = self.calculator.calculate_delay(
                retry_count=retry_count,
                trading_params=self.trading_params,
                trade_type="buy"
            )
            self.assertEqual(delay, expected_delay)
    
    def test_calculate_delay_max_limit(self) -> None:
        """测试最大延迟限制"""
        self.trading_params.retry_delay_strategy = RetryDelayStrategy.EXPONENTIAL
        self.trading_params.max_retry_delay_seconds = 5.0
        
        # 指数增长超过限制时应该被限制
        delay = self.calculator.calculate_delay(
            retry_count=4,  # 1.0 * 2^3 = 8.0，但应该被限制为5.0
            trading_params=self.trading_params,
            trade_type="buy"
        )
        self.assertEqual(delay, 5.0)
    
    def test_calculate_delay_slippage_error_priority(self) -> None:
        """测试滑点错误专用延迟的优先级"""
        # 滑点错误应该使用专用延迟而不是基础延迟
        delay = self.calculator.calculate_delay(
            retry_count=1,
            trading_params=self.trading_params,
            is_slippage_error=True,
            trade_type="buy"
        )
        # 应该使用slippage_error_delay_seconds=0.5而不是retry_delay_seconds=1.0
        self.assertEqual(delay, 0.5)
    
    def test_calculate_delay_non_slippage_error(self) -> None:
        """测试非滑点错误使用基础延迟"""
        delay = self.calculator.calculate_delay(
            retry_count=1,
            trading_params=self.trading_params,
            is_slippage_error=False,
            trade_type="buy"
        )
        # 应该使用基础延迟
        self.assertEqual(delay, 1.0)
    
    def test_get_effective_delay_config_buy_specific(self) -> None:
        """测试买入特定延迟配置"""
        self.trading_params.buy_retry_delay_seconds = 0.3
        
        config = self.calculator.get_effective_delay_config(
            trading_params=self.trading_params,
            trade_type="buy"
        )
        
        self.assertEqual(config['specific_delay'], 0.3)
        self.assertEqual(config['default_delay'], 1.0)
        self.assertEqual(config['effective_delay'], 0.3)
    
    def test_get_effective_delay_config_sell_specific(self) -> None:
        """测试卖出特定延迟配置"""
        self.trading_params.sell_retry_delay_seconds = 0.8
        
        config = self.calculator.get_effective_delay_config(
            trading_params=self.trading_params,
            trade_type="sell"
        )
        
        self.assertEqual(config['specific_delay'], 0.8)
        self.assertEqual(config['default_delay'], 1.0)
        self.assertEqual(config['effective_delay'], 0.8)
    
    def test_get_effective_delay_config_no_specific(self) -> None:
        """测试没有特定延迟时使用默认配置"""
        config = self.calculator.get_effective_delay_config(
            trading_params=self.trading_params,
            trade_type="buy"
        )
        
        self.assertIsNone(config['specific_delay'])
        self.assertEqual(config['default_delay'], 1.0)
        self.assertEqual(config['effective_delay'], 1.0)
    
    def test_validate_delay_config_valid(self) -> None:
        """测试有效配置验证"""
        result = self.calculator.validate_delay_config(
            trading_params=self.trading_params,
            meme_coin_market=True
        )
        self.assertTrue(result)
    
    def test_validate_delay_config_negative_delay(self) -> None:
        """测试负延迟配置验证"""
        self.trading_params.retry_delay_seconds = -1.0
        
        result = self.calculator.validate_delay_config(
            trading_params=self.trading_params,
            meme_coin_market=True
        )
        self.assertFalse(result)
    
    def test_validate_delay_config_max_less_than_base(self) -> None:
        """测试最大延迟小于基础延迟的配置验证"""
        self.trading_params.retry_delay_seconds = 5.0
        self.trading_params.max_retry_delay_seconds = 2.0
        
        result = self.calculator.validate_delay_config(
            trading_params=self.trading_params,
            meme_coin_market=True
        )
        self.assertFalse(result)
    
    def test_validate_delay_config_negative_slippage_delay(self) -> None:
        """测试负滑点延迟配置验证"""
        self.trading_params.slippage_error_delay_seconds = -0.5
        
        result = self.calculator.validate_delay_config(
            trading_params=self.trading_params,
            meme_coin_market=True
        )
        self.assertFalse(result)
    
    def test_validate_delay_config_meme_coin_warnings(self) -> None:
        """测试meme币市场配置警告"""
        # 配置过大的延迟
        self.trading_params.retry_delay_seconds = 3.0  # 超过2秒建议
        self.trading_params.max_retry_delay_seconds = 15.0  # 超过10秒建议
        
        # 应该通过验证但有警告
        result = self.calculator.validate_delay_config(
            trading_params=self.trading_params,
            meme_coin_market=True
        )
        self.assertTrue(result)  # 仍然有效，只是有警告
    
    def test_validate_delay_config_exponential_warning(self) -> None:
        """测试指数退避在meme币市场的警告"""
        self.trading_params.retry_delay_strategy = RetryDelayStrategy.EXPONENTIAL
        self.trading_params.retry_delay_seconds = 1.0  # 超过0.5秒建议
        
        result = self.calculator.validate_delay_config(
            trading_params=self.trading_params,
            meme_coin_market=True
        )
        self.assertTrue(result)  # 有效但有警告
    
    def test_get_strategy_info_fixed(self) -> None:
        """测试固定策略信息"""
        info = self.calculator.get_strategy_info(RetryDelayStrategy.FIXED)
        
        self.assertEqual(info['name'], "固定间隔")
        self.assertIn("delay = base_delay", info['formula'])
        self.assertIn("高", info['meme币适应性'])
    
    def test_get_strategy_info_linear(self) -> None:
        """测试线性策略信息"""
        info = self.calculator.get_strategy_info(RetryDelayStrategy.LINEAR)
        
        self.assertEqual(info['name'], "线性递增")
        self.assertIn("delay = base_delay * retry_count", info['formula'])
        self.assertIn("中", info['meme币适应性'])
    
    def test_get_strategy_info_exponential(self) -> None:
        """测试指数策略信息"""
        info = self.calculator.get_strategy_info(RetryDelayStrategy.EXPONENTIAL)
        
        self.assertEqual(info['name'], "指数退避")
        self.assertIn("delay = base_delay * (2^(retry_count-1))", info['formula'])
        self.assertIn("低", info['meme币适应性'])
    
    def test_get_strategy_info_unknown(self) -> None:
        """测试未知策略信息"""
        # 创建一个不存在的策略（通过字符串）
        class UnknownStrategy:
            pass
        
        unknown_strategy = UnknownStrategy()
        info = self.calculator.get_strategy_info(unknown_strategy)
        
        self.assertEqual(info['name'], "未知策略")
    
    def test_estimate_total_retry_time_fixed(self) -> None:
        """测试固定策略的总重试时间预估"""
        self.trading_params.retry_delay_strategy = RetryDelayStrategy.FIXED
        
        total_time = self.calculator.estimate_total_retry_time(
            max_retries=3,
            trading_params=self.trading_params,
            trade_type="buy"
        )
        
        # 固定间隔：1.0 + 1.0 + 1.0 = 3.0
        self.assertEqual(total_time, 3.0)
    
    def test_estimate_total_retry_time_linear(self) -> None:
        """测试线性策略的总重试时间预估"""
        self.trading_params.retry_delay_strategy = RetryDelayStrategy.LINEAR
        
        total_time = self.calculator.estimate_total_retry_time(
            max_retries=3,
            trading_params=self.trading_params,
            trade_type="buy"
        )
        
        # 线性递增：1.0 + 2.0 + 3.0 = 6.0
        self.assertEqual(total_time, 6.0)
    
    def test_estimate_total_retry_time_exponential(self) -> None:
        """测试指数策略的总重试时间预估"""
        self.trading_params.retry_delay_strategy = RetryDelayStrategy.EXPONENTIAL
        
        total_time = self.calculator.estimate_total_retry_time(
            max_retries=3,
            trading_params=self.trading_params,
            trade_type="buy"
        )
        
        # 指数退避：1.0 + 2.0 + 4.0 = 7.0
        self.assertEqual(total_time, 7.0)
    
    def test_estimate_total_retry_time_with_max_limit(self) -> None:
        """测试带最大限制的总重试时间预估"""
        self.trading_params.retry_delay_strategy = RetryDelayStrategy.EXPONENTIAL
        self.trading_params.max_retry_delay_seconds = 3.0
        
        total_time = self.calculator.estimate_total_retry_time(
            max_retries=4,
            trading_params=self.trading_params,
            trade_type="buy"
        )
        
        # 指数退避但受限制：1.0 + 2.0 + 3.0(限制) + 3.0(限制) = 9.0
        self.assertEqual(total_time, 9.0)
    
    def test_trade_type_specific_delays(self) -> None:
        """测试买卖特定延迟"""
        self.trading_params.buy_retry_delay_seconds = 0.3
        self.trading_params.sell_retry_delay_seconds = 0.8
        
        # 测试买入延迟
        buy_delay = self.calculator.calculate_delay(
            retry_count=1,
            trading_params=self.trading_params,
            trade_type="buy"
        )
        self.assertEqual(buy_delay, 0.3)
        
        # 测试卖出延迟
        sell_delay = self.calculator.calculate_delay(
            retry_count=1,
            trading_params=self.trading_params,
            trade_type="sell"
        )
        self.assertEqual(sell_delay, 0.8)
    
    def test_edge_cases(self) -> None:
        """测试边界情况"""
        # 重试次数为0（虽然实际不会发生）
        delay = self.calculator.calculate_delay(
            retry_count=0,
            trading_params=self.trading_params,
            trade_type="buy"
        )
        # 对于指数退避，retry_count=0时实际应该返回基础延迟
        self.assertEqual(delay, 1.0)
        
        # 非常大的重试次数
        self.trading_params.retry_delay_strategy = RetryDelayStrategy.EXPONENTIAL
        delay = self.calculator.calculate_delay(
            retry_count=100,
            trading_params=self.trading_params,
            trade_type="buy"
        )
        # 应该被最大限制约束
        self.assertEqual(delay, self.trading_params.max_retry_delay_seconds)
        
        # 零延迟配置
        self.trading_params.retry_delay_seconds = 0.0
        delay = self.calculator.calculate_delay(
            retry_count=1,
            trading_params=self.trading_params,
            trade_type="buy"
        )
        self.assertEqual(delay, 0.0)
    
    def test_slippage_error_delay_none(self) -> None:
        """测试滑点错误延迟为None时的行为"""
        self.trading_params.slippage_error_delay_seconds = None
        
        delay = self.calculator.calculate_delay(
            retry_count=1,
            trading_params=self.trading_params,
            is_slippage_error=True,
            trade_type="buy"
        )
        # 应该回退到基础延迟
        self.assertEqual(delay, self.trading_params.retry_delay_seconds)
    
    def test_case_insensitive_trade_type(self) -> None:
        """测试交易类型大小写不敏感"""
        self.trading_params.buy_retry_delay_seconds = 0.3
        self.trading_params.sell_retry_delay_seconds = 0.8
        
        # 测试大写
        buy_delay = self.calculator.calculate_delay(
            retry_count=1,
            trading_params=self.trading_params,
            trade_type="BUY"
        )
        self.assertEqual(buy_delay, 0.3)
        
        sell_delay = self.calculator.calculate_delay(
            retry_count=1,
            trading_params=self.trading_params,
            trade_type="SELL"
        )
        self.assertEqual(sell_delay, 0.8)
        
        # 测试混合大小写
        buy_delay = self.calculator.calculate_delay(
            retry_count=1,
            trading_params=self.trading_params,
            trade_type="Buy"
        )
        self.assertEqual(buy_delay, 0.3)
    
    def test_complex_scenarios(self) -> None:
        """测试复杂场景"""
        # 场景1：meme币高频交易配置
        meme_params = TradingParams(
            retry_delay_seconds=0.2,
            max_retry_delay_seconds=2.0,
            retry_delay_strategy=RetryDelayStrategy.FIXED,
            buy_retry_delay_seconds=0.1,
            sell_retry_delay_seconds=0.3,
            slippage_error_delay_seconds=0.05
        )
        
        # 买入滑点错误应该使用最快的延迟
        delay = self.calculator.calculate_delay(
            retry_count=1,
            trading_params=meme_params,
            is_slippage_error=True,
            trade_type="buy"
        )
        self.assertEqual(delay, 0.05)
        
        # 场景2：保守交易配置
        conservative_params = TradingParams(
            retry_delay_seconds=2.0,
            max_retry_delay_seconds=30.0,
            retry_delay_strategy=RetryDelayStrategy.EXPONENTIAL,
            slippage_error_delay_seconds=1.0
        )
        
        # 多次重试测试
        delays = []
        for retry_count in range(1, 6):
            delay = self.calculator.calculate_delay(
                retry_count=retry_count,
                trading_params=conservative_params,
                trade_type="buy"
            )
            delays.append(delay)
        
        # 验证指数增长
        self.assertEqual(delays[0], 2.0)   # 2.0 * 2^0
        self.assertEqual(delays[1], 4.0)   # 2.0 * 2^1
        self.assertEqual(delays[2], 8.0)   # 2.0 * 2^2
        self.assertEqual(delays[3], 16.0)  # 2.0 * 2^3
        self.assertEqual(delays[4], 30.0)  # 2.0 * 2^4 = 32.0，但被限制为30.0
    
    def test_validation_comprehensive(self) -> None:
        """测试全面的配置验证"""
        # 正常配置
        valid_params = TradingParams(
            retry_delay_seconds=0.5,
            max_retry_delay_seconds=5.0,
            retry_delay_strategy=RetryDelayStrategy.LINEAR,
            slippage_error_delay_seconds=0.2
        )
        
        self.assertTrue(self.calculator.validate_delay_config(
            valid_params, meme_coin_market=True
        ))
        
        # 测试负数验证在模型层面会被拦截
        with self.assertRaises(ValueError):
            TradingParams(
                retry_delay_seconds=-1.0,  # 负值，应该被模型验证拦截
                max_retry_delay_seconds=5.0,
                retry_delay_strategy=RetryDelayStrategy.FIXED
            )
        
        # 测试最大延迟小于基础延迟的配置（这个可以通过模型验证）
        invalid_params = TradingParams(
            retry_delay_seconds=5.0,
            max_retry_delay_seconds=2.0,  # 小于基础延迟
            retry_delay_strategy=RetryDelayStrategy.FIXED
        )
        
        self.assertFalse(self.calculator.validate_delay_config(
            invalid_params, meme_coin_market=True
        ))


if __name__ == '__main__':
    unittest.main() 