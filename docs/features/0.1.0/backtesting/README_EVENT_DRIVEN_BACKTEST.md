# 事件驱动回测框架

## 简介

这是一个基于事件驱动架构的回测框架，用于回测加密货币交易策略。与传统的固定步长回测方法相比，事件驱动回测能够更真实地模拟市场行为和策略反应，特别适合于需要精确时间点的策略回测。

## 核心组件

事件驱动回测框架由以下核心组件组成：

1. **事件系统**
   - `Event`: 事件基类，所有事件类型的基础
   - `TransactionEvent`: KOL交易事件，代表KOL账户的交易活动
   - `SignalEvent`: 信号事件，由策略生成的买入或卖出信号
   - `OrderEvent`: 订单事件，由投资组合管理器根据信号生成的订单
   - `FillEvent`: 成交事件，由执行处理器生成的订单执行结果

2. **事件队列**
   - `EventQueue`: 管理和分发事件的队列，支持事件处理和监听

3. **数据处理**
   - `DataHandler`: 负责从数据库加载历史数据并生成事件

4. **策略适配器**
   - `BuyStrategyAdapter`: 买入策略适配器，将KOLBuyStrategy适配到事件驱动框架
   - `SellStrategyAdapter`: 卖出策略适配器，将KOLSellStrategy适配到事件驱动框架

5. **投资组合管理**
   - `Portfolio`: 投资组合类，管理资金和持仓
   - `PortfolioManager`: 投资组合管理器，处理信号事件并生成订单事件

6. **执行处理**
   - `ExecutionHandler`: 执行处理器，处理订单事件并生成成交事件

7. **结果分析**
   - `ResultAnalyzer`: 结果分析器，分析回测结果并生成报告

## 工作流程

1. **初始化阶段**
   - 加载配置和参数
   - 初始化各个组件
   - 与数据库建立连接

2. **数据加载阶段**
   - 加载KOL钱包数据
   - 筛选符合条件的KOL账户（基于交易数量范围）
   - 从数据库流式加载符合条件的KOL账户的历史交易数据
   - 应用交易金额筛选，确保只处理有意义的交易

3. **事件处理阶段**
   - 将加载的历史数据转换为事件并推入事件队列
   - 策略适配器处理交易事件，生成信号事件
   - 投资组合管理器处理信号事件，生成订单事件
   - 执行处理器处理订单事件，生成成交事件
   - 投资组合跟踪持仓和资金变化

4. **结果分析阶段**
   - 计算交易统计数据（胜率、收益率等）
   - 导出交易记录和结果数据
   - 生成可视化图表

## 配置项

事件驱动回测框架支持以下配置参数：

- **时间范围参数**
  - `backtest_start_time`: 回测开始时间（Unix时间戳）
  - `backtest_end_time`: 回测结束时间（Unix时间戳）

- **KOL交易策略参数**
  - `transaction_lookback_hours`: 交易回溯小时数
  - `transaction_min_amount`: 最低交易金额
  - `kol_account_min_count`: 最低KOL账号数
  - `kol_account_min_txs`: 每个KOL账号最低交易数
  - `kol_account_max_txs`: 每个KOL账号最高交易数
  - `token_mint_lookback_hours`: 代币创建回溯小时数
  - `kol_min_winrate_7d`: (可选) KOL最低7日胜率阈值 (0到1之间)，用于二次筛选KOL

- **卖出策略参数**
  - `sell_strategy_hours`: 卖出策略回溯小时数
  - `sell_kol_ratio`: 卖出KOL比例阈值

- **回测控制参数**
  - `processing_interval`: 信号处理间隔（秒），默认为60秒，控制策略检查信号的频率。较小的值能提高模拟精度，但会增加回测时间
  - `initial_capital`: 初始资金
  - `same_token_notification_interval_minutes`: 相同代币通知的最小间隔（分钟），默认为60分钟，模拟真实环境中的通知抑制逻辑

- **交易成本参数**
  - `slippage_pct`: 滑点百分比配置项，默认为 0.01 (1%)。**注意：当前代码实现中，实际应用的滑点固定为0，此配置暂不生效。**
  - `commission_pct`: 手续费百分比，默认为 0.001 (0.1%)，双向收取

## 使用方法

### 单次回测

使用配置文件：

```bash
python run_backtest_ed.py --mode single --config config/backtest_config.yaml
```

使用命令行参数：

```bash
python run_backtest_ed.py --mode single --start_time ********** --end_time ********** --transaction_lookback_hours 24 --transaction_min_amount 1000 --kol_account_min_count 3 --initial_capital 10000
```

### 参数网格搜索

使用参数网格文件：

```bash
python run_backtest_ed.py --mode grid --param_grid config/param_grid.json
```

使用默认参数网格：

```bash
python run_backtest_ed.py --mode grid
```

## 与固定步长回测的对比

| 特性 | 事件驱动回测 | 固定步长回测 |
|------|--------------|--------------|
| 时间精度 | 基于实际事件发生时间 | 基于固定步长的时间点 |
| 内存使用 | 流式处理，内存友好 | 可能一次性加载大量数据 |
| 真实性 | 更接近真实交易流程 | 简化的时间切片模拟 |
| 复杂度 | 较高，需要管理事件流 | 较低，逻辑直观 |
| 扩展性 | 易于添加新事件类型和处理器 | 需要修改核心逻辑 |

## 目录结构

```
utils/backtest_event_driven/
├── __init__.py           # 模块初始化和导出
├── backtest.py           # 事件驱动回测主类
├── data_handler.py       # 数据处理器
├── event_queue.py        # 事件队列管理器
├── events.py             # 事件类定义
├── execution_handler.py  # 执行处理器
├── portfolio.py          # 投资组合管理器
├── result_analyzer.py    # 结果分析器
└── strategy_adapter.py   # 策略适配器

run_backtest_ed.py        # 命令行入口脚本
```

## 结果输出

回测结果将保存在以时间戳命名的目录中，包含以下文件：

- `results.json`: 回测结果和统计数据
- `trades.csv`: 交易历史记录
- `equity_curve.png`: 资金曲线图
- `returns_distribution.png`: 收益分布图

参数网格搜索结果将保存在以时间戳命名的目录中，包含：

- `param_search_results.json`: 所有参数组合的回测结果

## 性能优化

为提高回测效率，框架实现了以下优化：

1. **选择性数据加载**：先筛选符合条件的KOL账户，再查询这些账户的交易记录，避免加载无关数据
2. **数据格式处理**：自动处理不同格式的数据字段（如转换字符串为数字），提高兼容性
3. **并发处理**：策略适配器中使用并发处理多个信号和检查
4. **内存管理**：定期执行垃圾回收，保持内存使用在合理范围内

这些优化措施大幅提高了回测速度，特别是在处理大量历史数据时效果显著。 