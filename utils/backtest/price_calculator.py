import asyncio
import logging
from typing import Dict, Any, Optional, List
from utils.spiders.smart_money.gmgn_token_candles import GmgnTokenCandlesSpider

logger = logging.getLogger("PriceCalculator")

class PriceCalculator:
    """价格计算模块，负责获取代币在特定时间点的价格"""
    
    def __init__(self):
        """初始化价格计算器"""
        self.spider = GmgnTokenCandlesSpider()
        self.price_cache = {}  # 缓存已查询过的价格
        
    async def setup(self):
        """设置爬虫"""
        await self.spider.setup()
        
    async def get_price(self, token_address: str, timestamp: int) -> Optional[float]:
        """获取代币在特定时间点的价格
        
        Args:
            token_address: 代币地址
            timestamp: 时间戳
            
        Returns:
            Optional[float]: 价格，若获取失败则返回None
        """
        # 检查缓存
        cache_key = f"{token_address}_{timestamp}"
        if cache_key in self.price_cache:
            return self.price_cache[cache_key]
        
        # 查询价格
        try:
            price_data = await self.spider.get_price_point_in_time(
                address=token_address,
                timestamp=timestamp,
                resolution="1m"
            )
            
            # 获取价格
            if price_data and 'close' in price_data:
                price = float(price_data['close'])
                # 更新缓存
                self.price_cache[cache_key] = price
                return price
            else:
                logger.warning(f"无法获取价格数据: {token_address} at {timestamp}")
                return None
                
        except Exception as e:
            logger.error(f"获取价格时出错: {e}")
            return None
            
    async def get_prices_batch(self, price_requests: List[Dict[str, Any]]) -> Dict[str, float]:
        """批量获取价格数据
        
        Args:
            price_requests: 价格请求列表，每个请求包含 token_address 和 timestamp
            
        Returns:
            Dict[str, float]: 价格映射，键为 token_address_timestamp，值为价格
        """
        results = {}
        # 先检查缓存，找出未缓存的请求
        uncached_requests = []
        
        for req in price_requests:
            token_address = req['token_address']
            timestamp = req['timestamp']
            cache_key = f"{token_address}_{timestamp}"
            
            if cache_key in self.price_cache:
                results[cache_key] = self.price_cache[cache_key]
            else:
                uncached_requests.append(req)
        
        # 限制并发数量，每批次最多10个并发请求
        batch_size = 10
        for i in range(0, len(uncached_requests), batch_size):
            batch = uncached_requests[i:i+batch_size]
            tasks = [
                self.get_price(req['token_address'], req['timestamp'])
                for req in batch
            ]
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for j, result in enumerate(batch_results):
                if not isinstance(result, Exception) and result is not None:
                    token_address = batch[j]['token_address']
                    timestamp = batch[j]['timestamp']
                    cache_key = f"{token_address}_{timestamp}"
                    results[cache_key] = result
                    self.price_cache[cache_key] = result
        
        return results
    
    async def calculate_returns(self, buy_signals: List[Dict[str, Any]], 
                              sell_signals: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """计算每个交易信号的回报率
        
        Args:
            buy_signals: 买入信号列表
            sell_signals: 卖出信号列表
            
        Returns:
            List[Dict[str, Any]]: 交易结果列表，包含回报率
        """
        if not buy_signals or not sell_signals:
            logger.warning("没有足够的买入或卖出信号进行回报率计算")
            return []
            
        # 将买入信号组织为字典，方便查找
        buy_dict = {}
        for signal in buy_signals:
            token_address = signal['token_address']
            buy_time = signal['time']
            key = f"{token_address}_{buy_time}"
            buy_dict[key] = signal
        
        # 准备批量价格请求
        price_requests = []
        
        # 收集所有需要查询价格的请求
        for sell_signal in sell_signals:
            token_address = sell_signal['token_address']
            buy_time = sell_signal['buy_signal_time']
            sell_time = sell_signal['time']
            
            key = f"{token_address}_{buy_time}"
            if key in buy_dict:
                # 添加买入价格请求
                price_requests.append({
                    'token_address': token_address,
                    'timestamp': buy_time
                })
                
                # 添加卖出价格请求
                price_requests.append({
                    'token_address': token_address,
                    'timestamp': sell_time
                })
        
        # 批量获取价格
        logger.info(f"批量获取 {len(price_requests)} 个价格数据点")
        prices = await self.get_prices_batch(price_requests)
        
        # 计算每个卖出信号对应的回报率
        results = []
        for sell_signal in sell_signals:
            token_address = sell_signal['token_address']
            buy_time = sell_signal['buy_signal_time']
            sell_time = sell_signal['time']
            
            key = f"{token_address}_{buy_time}"
            if key in buy_dict:
                buy_signal = buy_dict[key]
                
                # 获取买入和卖出价格
                buy_price_key = f"{token_address}_{buy_time}"
                sell_price_key = f"{token_address}_{sell_time}"
                
                buy_price = prices.get(buy_price_key)
                sell_price = prices.get(sell_price_key)
                
                if buy_price and sell_price:
                    # 计算回报率
                    return_rate = (sell_price - buy_price) / buy_price
                    
                    # 创建交易结果
                    trade_result = {
                        'token_address': token_address,
                        'token_name': sell_signal['token_name'],
                        'token_symbol': sell_signal['token_symbol'],
                        'buy_time': buy_time,
                        'sell_time': sell_time,
                        'buy_price': buy_price,
                        'sell_price': sell_price,
                        'return_rate': return_rate,
                        'profit': return_rate > 0,
                        'buy_signal': buy_signal,
                        'sell_signal': sell_signal
                    }
                    results.append(trade_result)
                else:
                    logger.warning(f"无法计算回报率，因为买入价格或卖出价格缺失: {token_address}")
        
        logger.info(f"计算了 {len(results)} 个交易结果")
        return results
        
    async def stream_calculate_returns(self, buy_signals: List[Dict[str, Any]], 
                                    sell_signals: List[Dict[str, Any]],
                                    batch_size: int = 100):
        """流式计算交易信号的回报率
        
        Args:
            buy_signals: 买入信号列表
            sell_signals: 卖出信号列表
            batch_size: 每批计算的信号数量
            
        Yields:
            List[Dict[str, Any]]: 一批交易结果
        """
        if not buy_signals or not sell_signals:
            logger.warning("没有足够的买入或卖出信号进行回报率计算")
            return
            
        # 将买入信号组织为字典，方便查找
        buy_dict = {}
        for signal in buy_signals:
            token_address = signal['token_address']
            buy_time = signal['time']
            key = f"{token_address}_{buy_time}"
            buy_dict[key] = signal
            
        logger.info(f"开始流式计算 {len(sell_signals)} 个卖出信号的回报率")
        
        # 按批次处理卖出信号
        for i in range(0, len(sell_signals), batch_size):
            current_batch = sell_signals[i:i+batch_size]
            logger.info(f"处理第 {i//batch_size + 1} 批卖出信号，共 {len(current_batch)} 个")
            
            # 当前批次需要的价格请求
            price_requests = []
            
            # 收集当前批次需要的价格请求
            for sell_signal in current_batch:
                token_address = sell_signal['token_address']
                buy_time = sell_signal['buy_signal_time']
                sell_time = sell_signal['time']
                
                key = f"{token_address}_{buy_time}"
                if key in buy_dict:
                    # 添加买入价格请求
                    price_requests.append({
                        'token_address': token_address,
                        'timestamp': buy_time
                    })
                    
                    # 添加卖出价格请求
                    price_requests.append({
                        'token_address': token_address,
                        'timestamp': sell_time
                    })
            
            # 批量获取价格
            if price_requests:
                logger.info(f"批量获取 {len(price_requests)} 个价格数据点")
                prices = await self.get_prices_batch(price_requests)
                
                # 计算当前批次的回报率
                batch_results = []
                for sell_signal in current_batch:
                    token_address = sell_signal['token_address']
                    buy_time = sell_signal['buy_signal_time']
                    sell_time = sell_signal['time']
                    
                    key = f"{token_address}_{buy_time}"
                    if key in buy_dict:
                        buy_signal = buy_dict[key]
                        
                        # 获取买入和卖出价格
                        buy_price_key = f"{token_address}_{buy_time}"
                        sell_price_key = f"{token_address}_{sell_time}"
                        
                        buy_price = prices.get(buy_price_key)
                        sell_price = prices.get(sell_price_key)
                        
                        if buy_price and sell_price:
                            # 计算回报率
                            return_rate = (sell_price - buy_price) / buy_price
                            
                            # 创建交易结果
                            trade_result = {
                                'token_address': token_address,
                                'token_name': sell_signal['token_name'],
                                'token_symbol': sell_signal['token_symbol'],
                                'buy_time': buy_time,
                                'sell_time': sell_time,
                                'buy_price': buy_price,
                                'sell_price': sell_price,
                                'return_rate': return_rate,
                                'profit': return_rate > 0,
                                'buy_signal': buy_signal,
                                'sell_signal': sell_signal
                            }
                            batch_results.append(trade_result)
                
                if batch_results:
                    logger.info(f"计算得到 {len(batch_results)} 个交易结果")
                    yield batch_results 