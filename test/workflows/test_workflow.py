import unittest
import asyncio
import logging
import time
from unittest import mock
from typing import Any, Dict, List, Optional, Tuple, AsyncGenerator

from utils.workflows.workflow import Workflow
from utils.workflows.nodes import Node, NodeStatus, InputNode, ProcessNode, OutputNode, StorageNode

# 模拟消息队列，提供简单API
class MockQueue:
    def __init__(self, name):
        self.name = name
        self.messages = []
        self.closed = False
    
    async def send(self, message):
        """发送消息"""
        if not self.closed:
            self.messages.append(message)
            return True
        return False
    
    async def receive(self):
        """接收消息"""
        if not self.messages or self.closed:
            return None
        return self.messages.pop(0)
    
    async def close(self):
        """关闭队列"""
        self.closed = True
    
    @property
    def queue_size(self):
        return len(self.messages)
    
    async def get_last_offset(self) -> int:
        """模拟 Kafka 队列的 get_last_offset。返回消息数减一，如果没有消息则为-1。"""
        if not self.messages:
            return -1 # 或者根据 Kafka 实际行为返回 None 或 0
        return len(self.messages) - 1

    async def get_pending_count(self) -> int:
        """模拟流控制器期望的 get_pending_count。返回当前队列中的消息数。"""
        return len(self.messages)

    async def pull(self, max_count: int, timeout: Optional[float] = None) -> List[Any]:
        """模拟 Kafka 队列的 pull 方法。

        Args:
            max_count: 要拉取的最大消息数。
            timeout: 超时时间 (在Mock中可能未使用)。

        Returns:
            消息列表。
        """
        pulled_messages = []
        if self.closed:
            return pulled_messages
        
        for _ in range(max_count):
            if not self.messages:
                break
            pulled_messages.append(self.messages.pop(0))
        return pulled_messages

# 简单消息类
class SimpleMessage:
    def __init__(self, data):
        self.data = data
        self.id = str(time.time())

# 模拟节点类
class MockInputNode(InputNode):
    def __init__(self, name, **kwargs):
        super().__init__(name, **kwargs)
        self.data_to_send = []
        self.run_count = 0
        self.custom_stopped = False
        self.task_check_interval = 0.01 # 设置较小的任务检查间隔

    def add_data(self, data):
        print(f"[MockInputNode {self.name}] add_data: {data}")
        self.data_to_send.append(data)
    
    async def process_message(self, message: Any = None) -> Optional[List[Tuple[Any, str]]]:
        """处理数据"""
        print(f"[MockInputNode {self.name}] process_message called. data_to_send: {self.data_to_send}")
        self.run_count += 1
        if self.data_to_send:
            # 在实际的InputNode中，数据会通过WorkflowData发送到输出队列
            # 这里我们只是标记它为已处理，并假设工作流处理排队
            # 确保 self.output_queues 被正确设置
            target_queue_name = self.output_queues[0].name if self.output_queues else "NO_OUTPUT_QUEUE"
            processed_data = [(SimpleMessage(data), target_queue_name) for data in self.data_to_send]
            print(f"[MockInputNode {self.name}] processing and returning: {processed_data} to queue hint: {target_queue_name}")
            self.data_to_send = [] # 发送后清除
            return processed_data
        return None
    
    async def generate_data(self) -> AsyncGenerator[Any, None]:
        """使其成为一个异步生成器，yield 来自 process_message 的原始数据部分。"""
        print(f"[MockInputNode {self.name}] generate_data called")
        processed_data_list = await self.process_message() # process_message 返回 List[Tuple[SimpleMessage, str]]
        if processed_data_list:
            for item_tuple in processed_data_list: # item_tuple is (SimpleMessage(payload), queue_name_hint)
                print(f"[MockInputNode {self.name}] generate_data yielding: {item_tuple[0].data}")
                yield item_tuple[0].data # Yield the raw data part (e.g. dict from SimpleMessage.data)
        else:
            print(f"[MockInputNode {self.name}] generate_data: no data from process_message")

    def stop(self):
        """停止节点"""
        self.custom_stopped = True
        super().stop()

class MockProcessNode(ProcessNode):
    """简单处理节点"""
    
    def __init__(self, name, transform_func=None, **kwargs):
        super().__init__(name, **kwargs)
        self.transform_func = transform_func or (lambda x: x)
        self.processed_data_log = []
        self.run_count = 0
        self.custom_stopped = False
        self.task_check_interval = 0.01 # 设置较小的任务检查间隔
    
    async def process_item(self, item: Any) -> AsyncGenerator[Any, None]:
        """使其成为一个异步生成器，yield 转换后的数据。"""
        print(f"[MockProcessNode {self.name}] process_item called with: {item}")
        # self.run_count += 1 # run_count 在 process_message 中，或应在实际调用处处理
        if item: # item 应该是原始数据 (e.g. dict)
            transformed_data = self.transform_func(item)
            print(f"[MockProcessNode {self.name}] process_item yielding: {transformed_data}")
            # self.processed_data_log.append(transformed_data) # 日志记录也可以在 process_message 中
            yield transformed_data
        else:
            print(f"[MockProcessNode {self.name}] process_item received None item")

    async def process_message(self, message: Any) -> Optional[List[Tuple[Any, str]]]:
        """处理数据"""
        self.run_count += 1
        if message:
            data = message.data # Assuming SimpleMessage structure
            transformed_data = self.transform_func(data)
            self.processed_data_log.append(transformed_data)
            if self.output_queues:
                 return [(SimpleMessage(transformed_data), self.output_queues[0].name)]
        return None
    
    def stop(self):
        """停止节点"""
        self.custom_stopped = True
        super().stop()

class MockOutputNode(OutputNode):
    """简单输出节点"""
    
    def __init__(self, name, **kwargs):
        super().__init__(name, **kwargs)
        self.received_data_log = []
        self.run_count = 0
        self.custom_stopped = False
        self.task_check_interval = 0.01 # 设置较小的任务检查间隔

    async def store_data(self, data: Any):
        print(f"[MockOutputNode {self.name}] store_data (deprecated by handle_data) called with: {data}")
        self.run_count +=1
        self.received_data_log.append(data)
        return True

    async def handle_data(self, data: Any) -> bool:
        """处理 MockOutputNode 接收到的数据。"""
        print(f"[MockOutputNode {self.name}] handle_data called with: {data}")
        self.run_count += 1
        # MockOutputNode 的主要逻辑是在 process_message 中记录数据
        # handle_data 被 OutputNode 的 process 方法调用
        # 这里我们简单地将数据记录到 received_data_log
        if data:
            if isinstance(data, list):
                print(f"[MockOutputNode {self.name}] received list, extending log: {data}")
                self.received_data_log.extend(data)
            else:
                print(f"[MockOutputNode {self.name}] received single item, appending to log: {data}")
                self.received_data_log.append(data)
        else:
            print(f"[MockOutputNode {self.name}] handle_data received None data")
        return True # 表示处理成功

    async def process_message(self, message: Any) -> None:
        """处理数据"""
        self.run_count += 1
        if message:
            self.received_data_log.append(message.data) # Assuming SimpleMessage
        return None # Output nodes don't return data for further queueing
    
    def stop(self):
        """停止节点"""
        self.custom_stopped = True
        super().stop()

# 简单工作流类
class SimpleWorkflow:
    """简单工作流实现，用于测试"""
    
    def __init__(self, name):
        self.name = name
        self.nodes = {}
        self.connections = []
        self.tasks = {}
        self._stop = False
        self.logger = logging.getLogger(name)
    
    def add_node(self, node, connect_to=None, queue_name=None):
        """添加节点"""
        self.nodes[node.name] = node
        
        if connect_to:
            self.connect_nodes(connect_to, node, queue_name)
        
        return node
    
    def connect_nodes(self, source_node, target_node, queue_name=None):
        """连接节点"""
        source_name = source_node.name if isinstance(source_node, Node) else source_node
        target_name = target_node.name if isinstance(target_node, Node) else target_node
        
        if isinstance(source_node, str):
            source_node = self.nodes.get(source_node)
        
        if isinstance(target_node, str):
            target_node = self.nodes.get(target_node)
        
        if queue_name is None:
            queue_name = f"{source_name}_to_{target_name}"
        
        # 创建队列
        queue = MockQueue(queue_name)
        
        # 设置连接
        if hasattr(source_node, 'output_queue'):
            source_node.output_queue = queue
        
        if hasattr(target_node, 'input_queue'):
            target_node.input_queue = queue
        
        # 记录连接
        self.connections.append((source_name, target_name, queue_name))
        
        return queue
    
    def get_node_connections(self):
        """获取节点连接关系"""
        return self.connections
    
    def stop(self):
        """停止工作流"""
        self._stop = True
        
        # 停止所有节点
        for node in self.nodes.values():
            node.stop()
        
        # 取消所有任务
        for task_name, task in self.tasks.items():
            if not task.done():
                task.cancel()
    
    async def run(self):
        """运行工作流"""
        # 创建并启动节点任务
        for node_name, node in self.nodes.items():
            task = asyncio.create_task(self._run_node(node), name=node_name)
            self.tasks[node_name] = task
        
        # 等待所有任务完成或工作流停止
        pending = list(self.tasks.values())
        while pending and not self._stop:
            done, pending = await asyncio.wait(
                pending,
                timeout=0.1,
                return_when=asyncio.FIRST_COMPLETED
            )
            
            # 处理已完成的任务
            for task in done:
                node_name = task.get_name()
                try:
                    result = task.result()
                except asyncio.CancelledError:
                    pass
                except Exception as e:
                    self.logger.error(f"节点 {node_name} 任务出错: {str(e)}")
                    # 如果有节点出错，停止工作流
                    self.stop()
    
    async def _run_node(self, node):
        """运行节点"""
        # 重复执行节点的处理方法，直到工作流停止
        while not self._stop and not getattr(node, 'stopped', False):
            try:
                await node.process()
                # 短暂等待，避免CPU占用过高
                await asyncio.sleep(0.01)
            except Exception as e:
                self.logger.error(f"节点 {node.name} 处理出错: {str(e)}")
                # 如果节点处理出错，停止工作流
                self.stop()
                raise

class TestWorkflow(unittest.TestCase):
    """Workflow类的单元测试"""
    
    @classmethod
    def setUpClass(cls):
        cls.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(cls.loop)

    @classmethod
    def tearDownClass(cls):
        tasks = asyncio.all_tasks(loop=cls.loop)
        for task in tasks:
            task.cancel()
        if tasks:
            cls.loop.run_until_complete(asyncio.gather(*tasks, return_exceptions=True))
        cls.loop.close()
        asyncio.set_event_loop(None)
    
    async def async_setup(self):
        """异步设置，在每个测试方法中调用"""
        self.workflow = Workflow("test_workflow")
        self.logger = logging.getLogger("TestWorkflow")

    async def async_teardown(self):
        """异步清理，在每个测试方法中调用"""
        if hasattr(self, 'workflow') and self.workflow:
            # Workflow.stop() 是同步方法
            self.workflow.stop()
            # 等待一小段时间，确保由 stop() 触发的异步任务（如任务取消）有机会执行
            await asyncio.sleep(0.1)

    async def _run_test_async(self, test_coro_func, *args):
        await self.async_setup()
        try:
            await test_coro_func(*args)
        finally:
            await self.async_teardown()

    def test_add_node(self):
        """测试添加节点功能"""
        self.loop.run_until_complete(self._run_test_async(self._test_add_node_actual))
    
    async def _test_add_node_actual(self):
        """测试添加节点的异步实现"""
        node1 = MockInputNode("input1")
        self.workflow.add_node(node1)
        self.assertIn("input1", self.workflow.nodes)
        self.assertEqual(self.workflow.nodes["input1"], node1)

    @mock.patch('utils.workflows.workflow.create_message_queue')
    async def _test_connect_nodes_actual(self, mock_create_queue):
        mock_create_queue.side_effect = lambda type, name: MockQueue(name)

        node1 = MockInputNode("input1")
        node2 = MockProcessNode("process1")
        self.workflow.add_node(node1)
        self.workflow.add_node(node2)
        
        queue = self.workflow.connect_nodes(node1, node2, "q1")
        self.assertIsInstance(queue, MockQueue)
        self.assertIn(queue, node1.output_queues)
        self.assertEqual(node2.input_queue, queue)
        self.assertIn(("input1", "process1", "q1"), self.workflow.get_node_connections())

    @mock.patch('utils.workflows.workflow.create_message_queue')
    async def _test_add_node_with_connect_actual(self, mock_create_queue):
        mock_create_queue.side_effect = lambda type, name: MockQueue(name)

        node1 = MockInputNode("input_connect")
        self.workflow.add_node(node1)
        node2 = MockProcessNode("process_connect")
        # Add node2 and connect it to node1
        self.workflow.add_node(node2, connect_to=node1, queue_name="q_auto")
        
        self.assertIn("process_connect", self.workflow.nodes)
        self.assertTrue(any(conn[0] == "input_connect" and conn[1] == "process_connect" and conn[2] == "q_auto" 
                          for conn in self.workflow.get_node_connections()))
        # Check that node1 has an output queue and node2 has an input queue assigned by connect_nodes
        self.assertTrue(len(node1.output_queues) > 0)
        self.assertIsNotNone(node2.input_queue)
        self.assertEqual(node1.output_queues[0], node2.input_queue)

    @mock.patch('utils.workflows.workflow.create_message_queue')
    async def _test_get_node_connections_actual(self, mock_create_queue):
        mock_create_queue.side_effect = lambda type, name: MockQueue(name)

        node1 = MockInputNode("i1")
        node2 = MockProcessNode("p1")
        node3 = MockOutputNode("o1")
        self.workflow.add_node(node1)
        self.workflow.add_node(node2)
        self.workflow.add_node(node3)
        self.workflow.connect_nodes(node1, node2, "q_i_p")
        self.workflow.connect_nodes(node2, node3, "q_p_o")
        
        connections = self.workflow.get_node_connections()
        self.assertEqual(len(connections), 2)
        self.assertIn(("i1", "p1", "q_i_p"), connections)
        self.assertIn(("p1", "o1", "q_p_o"), connections)

    @mock.patch('utils.workflows.workflow.create_message_queue')
    async def _test_run_workflow_actual(self, mock_create_queue):
        mock_create_queue.side_effect = lambda type, name: MockQueue(name)

        input_node = MockInputNode("input")
        process_node = MockProcessNode("process", transform_func=lambda x: x * 2)
        output_node = MockOutputNode("output")

        input_node.add_data(10)
        input_node.add_data(20)

        self.workflow.add_node(input_node)
        self.workflow.add_node(process_node)
        self.workflow.add_node(output_node)
        self.workflow.connect_nodes(input_node, process_node, "q12")
        self.workflow.connect_nodes(process_node, output_node, "q23")

        # Start workflow and let it run for a bit
        workflow_task = self.loop.create_task(self.workflow.run())
        await asyncio.sleep(0.5) # Allow time for processing
        # Workflow.stop() 是同步方法
        self.workflow.stop()
        await workflow_task # Wait for workflow to finish stopping

        self.assertEqual(input_node.run_count, 1, f"Input node run count: {input_node.run_count}") # Input node might run multiple times
        self.assertGreaterEqual(process_node.run_count, 2, f"Process node run count: {process_node.run_count}")
        self.assertGreaterEqual(output_node.run_count, 2, f"Output node run count: {output_node.run_count}")
        self.assertListEqual(sorted(output_node.received_data_log), [20, 40])

    @mock.patch('utils.workflows.workflow.create_message_queue')
    async def _test_stop_workflow_actual(self, mock_create_queue):
        mock_create_queue.side_effect = lambda type, name: MockQueue(name)

        input_node = MockInputNode("input_stop")
        process_node = MockProcessNode("process_stop")
        self.workflow.add_node(input_node)
        self.workflow.add_node(process_node)
        self.workflow.connect_nodes(input_node, process_node, "q_stop")

        # Start workflow and immediately stop it
        workflow_task = self.loop.create_task(self.workflow.run())
        await asyncio.sleep(0.01) # Let it start
        # Workflow.stop() 是同步方法
        self.workflow.stop()
        await workflow_task

        self.assertTrue(self.workflow._stopped, "Workflow should be marked as stopped")
        # Nodes might have run once before stop signal is processed
        self.assertTrue(input_node.custom_stopped or input_node.status == NodeStatus.STOPPED)
        self.assertTrue(process_node.custom_stopped or process_node.status == NodeStatus.STOPPED)
        # Verify tasks are cancelled or done
        for task in self.workflow.tasks.values():
            self.assertTrue(task.done() or task.cancelled())

    @mock.patch('utils.workflows.workflow.create_message_queue')
    async def _test_node_exception_actual(self, mock_create_queue):
        mock_create_queue.side_effect = lambda type, name: MockQueue(name)

        class ExceptionNode(MockProcessNode):
            async def process_message(self, message: Any) -> Optional[List[Tuple[Any, str]]]:
                self.run_count +=1
                raise RuntimeError("Node processing error")

        input_node = MockInputNode("input_exc")
        exception_node = ExceptionNode("exception_node")
        output_node = MockOutputNode("output_exc")

        input_node.add_data("test_data_for_exception")

        self.workflow.add_node(input_node)
        self.workflow.add_node(exception_node)
        self.workflow.add_node(output_node)
        self.workflow.connect_nodes(input_node, exception_node, "q_in_exc")
        self.workflow.connect_nodes(exception_node, output_node, "q_exc_out")
        
        with mock.patch.object(self.workflow.logger, 'error') as mock_log_error:
            workflow_task = self.loop.create_task(self.workflow.run())
            await asyncio.sleep(0.2) # Allow time for the exception to occur and be handled
            
            # Workflow should stop itself upon node error
            # Workflow.stop() is called internally and is synchronous
            self.assertTrue(self.workflow._stopped, "Workflow should stop on node error")
            await workflow_task # Ensure workflow task completes

            # Check if the error was logged
            mock_log_error.assert_called()
            args, _ = mock_log_error.call_args
            self.assertIn(f"Node task exception_node raised an exception: Node processing error", args[0])
            self.assertGreaterEqual(exception_node.run_count, 1) # It should have at least tried to run
            self.assertEqual(len(output_node.received_data_log), 0) # Output node should not receive data

if __name__ == "__main__":
    unittest.main() 