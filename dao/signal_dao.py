from typing import List, Optional
from beanie import PydanticObjectId
from beanie.odm.operators.update.general import Set
from datetime import datetime
from dao.base_dao import BaseDAO
from models.signal import Signal

class SignalDAO(BaseDAO):
    def __init__(self):
        super().__init__(Signal)

    async def insert_signals(self, signals: List[Signal]) -> Optional[PydanticObjectId]:
        """
        批量插入信号记录
        """
        return await Signal.insert_many(signals)

    async def find_signals(self, query: dict) -> List[Signal]:
        """
        根据查询条件查找信号
        """
        return await Signal.find(query).to_list()
    
    async def get_signal(self, signal_id: PydanticObjectId) -> Optional[Signal]:
        """
        根据ID获取单个信号
        """
        return await Signal.get(signal_id)

    async def find_one(self, query: dict) -> Optional[Signal]:
        """
        根据查询条件查找单个信号
        """
        return await Signal.find_one(query)

    async def update_signal_status(self, signal_id: PydanticObjectId, status: str) -> int:
        """
        更新指定信号的状态

        Args:
            signal_id: 要更新的信号 ID
            status: 新的状态值 (e.g., 'sold')

        Returns:
            更新的文档数量 (0 or 1)
        """
        update_result = await Signal.find_one({"_id": signal_id}).update(
            Set({"status": status, "updated_at": datetime.utcnow()})
        )
        return update_result.modified_count 