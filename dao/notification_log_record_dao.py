from typing import Optional
from datetime import datetime
from models.notification_log_record import Notification<PERSON>ogR<PERSON>ord
from .base_dao import BaseDAO

class NotificationLogRecordDAO(BaseDAO[NotificationLogRecord]):
    """DAO for NotificationLogRecord model."""

    def __init__(self):
        super().__init__(NotificationLogRecord)

    async def create_notification_log(
        self,
        recipient_chat_id: str,
        status: str,
        alert_event_raw_id: Optional[str] = None,
        message_content_preview: Optional[str] = None,
        error_message: Optional[str] = None,
        send_time: Optional[datetime] = None
    ) -> NotificationLogRecord:
        """Create and save a new notification log record."""
        if send_time is None:
            send_time = datetime.utcnow()

        notification_log = NotificationLogRecord(
            alert_event_raw_id=alert_event_raw_id,
            recipient_chat_id=recipient_chat_id,
            status=status,
            message_content_preview=message_content_preview,
            error_message=error_message,
            send_time=send_time
        )
        await self.insert_one(notification_log)
        return notification_log 