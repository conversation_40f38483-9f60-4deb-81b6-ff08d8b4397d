# Bug修复计划：GMGN卖出交易因报价过旧失败的重试机制

## 1. Bug 标识
- **ID/描述**: GMGN 自动卖出交易在链上失败，返回错误 `{"InstructionError":[3,{"Custom":6004}]}` (十六进制 `0x1774`)。
- **影响**: 导致自动卖出策略无法按预期执行，代币可能未被及时卖出。

## 2. 报告与发现信息
- **报告日期**: 2025-05-20 (根据用户提供的日志推断)
- **发现途径**: 用户观察到生产环境/测试环境中的卖出交易失败日志。

## 3. 根源分析 (RCA)
错误码 `Custom:6004` (或 `0x1774`) 通常表示在与 Solana 上的 DEX 聚合器（如 Jupiter，GMGN 可能通过其进行交易）交互时，交易使用的报价（swap route）或指令在其尝试执行时已经过期。
根本原因是在 Node.js 脚本 (`gmgn_test.mjs`) 从 GMGN API 获取到交易报价后，到该交易在 Solana 链上实际被矿工打包处理之间存在一定的延迟。如果这段时间内市场价格发生显著变化，或者报价本身有一个较短的有效期（这在 DEX 聚合器中很常见，以防止过时的价格执行），那么当交易指令最终被链上程序处理时，它引用的报价可能已经被认为是"太旧"或"无效"的，从而导致此特定错误。

## 4. 已批准的修复方案

### 4.1. 核心思路
在 Python 服务端 (`utils/gmgn_trade_service.py`) 的卖出交易逻辑中引入一个带有多级参数调整的重试机制。如果单次卖出尝试失败（特别是由于报价过时等暂时性问题），系统将自动使用调整后的滑点和优先费用进行重试，以提高交易成功率。买入交易逻辑保持不变，失败时直接报错。

### 4.2. 详细实施方案
1.  **修改位置**: `utils/gmgn_trade_service.py` 内的 `execute_trade` 方法。
2.  **触发条件**: 此重试逻辑仅当 `trade_type` 参数为 `TradeType.SELL` 时激活。
3.  **重试结构**:
    *   总共最多进行 **10次** 交易尝试。
    *   这10次尝试分为 **5组**，每组进行连续 **2次** 尝试。
    *   每次尝试（无论是组内还是开始新的一组）之间，固定等待 **5秒** (`retry_delay_seconds = 5`)。
4.  **参数初始化 (源自 `strategy_snapshot`)**:
    *   **初始卖出滑点 (`initial_sell_slippage_percentage`)**: 从 `strategy_snapshot['gmgn_sell_slippage_percentage']` 获取。如果未配置，则使用默认值 (例如, 1.0%)。
    *   **初始卖出优先费用 (`initial_sell_priority_fee`)**: 从 `strategy_snapshot['gmgn_sell_priority_fee']` 获取。如果未配置，则使用默认值 (例如, `Decimal("0.00005")`)。
5.  **参数动态调整 (每组开始时调整，组内两次尝试使用该组的相同参数)**:
    *   令 `group_index` 为当前重试组的索引 (从1到5)。
    *   **滑点 (`current_slippage`)**:
        *   对于第 `group_index` 组: `current_slippage = initial_sell_slippage_percentage + (group_index - 1) * 1.0`
        *   示例:
            *   组1: `initial_sell_slippage_percentage`
            *   组2: `initial_sell_slippage_percentage + 1.0`
            *   ...
            *   组5: `initial_sell_slippage_percentage + 4.0`
    *   **优先费用 (`current_priority_fee`)**:
        *   对于第1组: `current_priority_fee = initial_sell_priority_fee`
        *   对于后续第 `group_index` ( > 1) 组: `current_priority_fee` 将通过以下方式从第 `group_index - 1` 组的优先费用计算：
            1.  将上一组的优先费用转换为 `Decimal` 对象。
            2.  确定其小数位数 `dp` (例如，`0.00005` 有 `dp=5`；`0.005` 有 `dp=3`；`0.5` 有 `dp=1`)。
            3.  计算增量：`increment = Decimal('1') / (Decimal('10') ** dp)`。
            4.  新的优先费用 = 上一组的优先费用 (`Decimal`) + `increment` (`Decimal`)。
            *   示例:
                *   若上组为 `0.00005`, 新费用为 `0.00006`。
                *   若上组为 `0.00009`, 新费用为 `0.00010`。
                *   若上组为 `0.005`, 新费用为 `0.006`。
                *   若上组为 `0.5`, 新费用为 `0.6`。
6.  **执行调用**: 每次重试尝试都会调用内部的 `_execute_single_trade_attempt` 方法，该方法负责调用 Node.js 脚本 (`gmgn_test.mjs`)，并将当前计算出的滑点和优先费用作为参数传递。Node.js 脚本本身不包含重试逻辑。
7.  **终止条件**:
    *   **成功**: 任何一次交易尝试返回成功状态 (Node.js 脚本输出 `status: "success"`)，则立即停止重试循环，并返回该成功的 `TradeResult`。
    *   **失败**: 如果所有10次尝试均告失败，则返回最后一次尝试得到的 `TradeResult`（包含错误信息）。
8.  **日志记录**: 在每次重试尝试前后、参数调整时以及最终成功/失败时，都应有清晰的日志记录，包括尝试次数、当前使用的滑点和优先费用、以及来自 Node.js 脚本的响应。

### 4.3. Node.js 脚本 (`gmgn_test.mjs`)
该脚本本身**不需要修改**以实现此重试逻辑。它将继续接收滑点和优先费用作为命令行参数，并执行单次交易尝试。

## 5. 方案相关人员
- **方案提出与实现**: AI (Gemini 2.5 Pro)
- **方案审阅与批准**: 用户 (gaojerry)
- **方案批准日期**: 2025-05-20T09:45:58+08:00

## 6. 预期验证方法
1.  **单元测试**: 鉴于修复的核心逻辑在Python中，可以针对 `_calculate_next_priority_fee` 辅助函数编写单元测试，确保费用递增逻辑的正确性。可以模拟 `_execute_single_trade_attempt` 的不同返回结果（成功/失败）来测试整体重试循环是否按预期执行（例如，是否在成功时停止，是否在达到最大次数后停止，参数是否正确调整）。
2.  **集成测试/准生产验证**:
    *   在测试环境中，模拟可能导致报价过时错误的条件（例如，通过工具临时增加RPC响应延迟或在目标DEX流动性较低时进行交易）。
    *   观察在启用此重试机制后，卖出交易的成功率是否有显著提高，特别是在之前容易失败的场景。
    *   检查 `TradeRecord` 中记录的最终状态、错误信息以及 `provider_response_raw`，确认重试是否按预期发生。
3.  **生产监控**:
    *   部署后，密切监控与卖出交易相关的日志，特别是 `GmgnTradeService` 的日志输出，以确认重试逻辑的实际运行情况和效果。
    *   关注卖出交易失败率的变化。

## 7. 辅助函数 (`_calculate_next_priority_fee`)
```python
from decimal import Decimal, Context, getcontext

def _calculate_next_priority_fee(current_fee_decimal: Decimal) -> Decimal:
    """
    Calculates the next priority fee by incrementing the smallest effective digit.
    e.g., 0.00005 -> 0.00006; 0.00009 -> 0.00010; 0.5 -> 0.6
    """
    prec = getcontext().prec
    ctx = Context(prec=max(prec, 20)) # Ensure enough precision

    s = ctx.to_eng_string(current_fee_decimal)
    if '.' in s:
        _integer_part, decimal_part = s.split('.')
        # Handle cases like "0.0000" where decimal_part might be all zeros but we want to treat its length
        # For "0.5", len is 1. For "0.00005", len is 5.
        increment = Decimal('1') / (Decimal('10') ** len(decimal_part))
    else: # Whole number, unlikely for typical SOL fees but handle
        increment = Decimal('1')
    
    next_fee = current_fee_decimal + increment
    return next_fee
``` 