name: 'GMGN关注KOL监控工作流'
description: '定期从config获取refresh_tokens, 使用token爬取GMGN关注的KOL列表, 更新到数据库, 并返回更新的数据进行后续处理'

# 工作流默认并发数，节点可以覆盖此设置
concurrency: 1

nodes:
  - name: "GmgnTokenProviderNode2"
    node_type: "input"         # 节点类型：输入
    interval: 1             # 每60秒运行一次
    concurrency: 1           # 此输入节点使用1个协程
    generate_data: workflows.gmgn_kol_monitor.handler.generate_refresh_tokens
    flow_control:
      max_pending_messages: 5  # 允许下游最多积压5个token批次（如果一次生成多个token）
      check_interval: 5
      enable_flow_control: true

  - name: "GmgnKolFetchNode2"
    node_type: "process"       # 节点类型：处理
    depend_ons: ["GmgnTokenProviderNode2"] # 依赖于TokenProvider节点
    concurrency: 2           # 此处理节点使用2个协程并发处理token
    process_item: workflows.gmgn_kol_monitor.handler.fetch_kols
    interval: 1

  - name: "GmgnKolFilterNode2"
    node_type: "process"
    depend_ons: ["GmgnKolFetchNode2"]
    concurrency: 2
    process_item: workflows.gmgn_kol_monitor.handler.filter_new_active_kols
    interval: 1

  # 新增的KOL Wallet转换节点，直接从GmgnKolFetchNode获取数据
  - name: "GmgnKolWalletConverterNode2"
    node_type: "process"
    depend_ons: ["GmgnKolFetchNode2"]
    concurrency: 2
    process_item: workflows.gmgn_kol_monitor.handler.convert_to_kol_wallet
    interval: 1

  # 新增的KOL Wallet存储节点
  - name: "GmgnKolWalletStorageNode2"
    node_type: "storage"
    depend_ons: ["GmgnKolWalletConverterNode2"]
    concurrency: 1
    batch_size: 1000
    store_data: workflows.gmgn_kol_monitor.handler.store_kol_wallet_data
    validate: workflows.gmgn_kol_monitor.handler.validate_kol_wallet_data
    flow_control:
      max_pending_messages: 5
      check_interval: 1
      enable_flow_control: false

  - name: "GmgnKolTradeNode2"
    node_type: "process"
    depend_ons: ["GmgnKolFilterNode2"]
    concurrency: 7
    process_item: workflows.gmgn_kol_wallet_activity.handler.process_kol_wallet_activity
    interval: 1
  
  - name: "GmgnKolTradeStoreNode2"
    node_type: "storage"
    depend_ons: ["GmgnKolTradeNode2"]
    batch_size: 1000
    store_data: workflows.gmgn_kol_wallet_activity.handler.store_data
    validate: workflows.gmgn_kol_wallet_activity.handler.validate 

  - name: "GmgnKolStorageNode2"
    node_type: "storage"       # 节点类型：存储
    depend_ons: ["GmgnKolFilterNode2"]   # 依赖于KolFilterNode节点
    concurrency: 1           # 存储节点通常使用1个协程以保证数据一致性或顺序
    batch_size: 1000
    store_data: workflows.gmgn_kol_monitor.handler.store_kol_data
    validate: workflows.gmgn_kol_monitor.handler.validate_kol_data
    flow_control:
      max_pending_messages: 5 # 存储节点通常不应该有太多积压，表明存储能力不足
      check_interval: 1
      enable_flow_control: false # 通常存储节点是最终环节，不控制上游 
  