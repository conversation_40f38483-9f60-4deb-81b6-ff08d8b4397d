# Bug修复方案：KOL监控通知中交易金额显示N/A问题

**Bug 标识**: KOL监控模块在发送Telegram购买通知时，`Amount Bought` 和 `Amount Spent` 字段显示为"N/A"，无法显示实际的交易数据。

**报告日期/发现日期**: 2025-05-26

**根源分析概要**:
1. **Amount Bought显示N/A**: `workflows/monitor_kol_activity/handler.py`第544行硬编码了`"N/A"`值，没有使用`successful_attempt.actual_amount_out`的实际交易数据。
2. **Amount Spent显示N/A**: 代码只使用了策略配置中的计划金额，没有使用`successful_attempt.actual_amount_in`的实际交易数据。
3. **缺少Token decimals处理**: 即使有实际数据，也没有正确处理Token的decimals，导致显示的是最小单位数量而不是用户友好的格式。

**详细的、已获批准的修复方案**:

在 `workflows/monitor_kol_activity/handler.py` 文件的 `send_message_to_channel` 函数内部，替换原有的硬编码逻辑，实现以下修改：

1. **使用实际交易数据**: 优先使用`successful_attempt.actual_amount_in`和`successful_attempt.actual_amount_out`的实际值
2. **智能格式检测**: 通过启发式方法判断数值是否已经是UI格式还是最小单位格式
3. **正确的decimals处理**: 
   - SOL使用9位decimals (lamports转换)
   - Token使用从`token_info.decimals`获取的decimals信息
4. **错误处理和降级**: 当数据缺失时提供合理的回退机制

**实际修复代码**:
```python
# 处理交易金额显示 - 使用实际交易数据
amount_in_ui = "N/A"
amount_out_ui = "N/A"

if successful_attempt:
    # --- 处理 Amount Spent (花费的SOL数量) ---
    if successful_attempt.actual_amount_in is not None:
        try:
            # SOL的decimals为9
            sol_decimals = 9
            raw_amount_in = float(successful_attempt.actual_amount_in)
            # 如果actual_amount_in已经是UI格式（小数），直接使用
            # 如果是lamports格式（大整数），需要转换
            if raw_amount_in > 1000:  # 假设超过1000的是lamports格式
                adjusted_amount_in = raw_amount_in / (10 ** sol_decimals)
            else:
                adjusted_amount_in = raw_amount_in
            amount_in_ui = f"{adjusted_amount_in:.{sol_decimals}f}"
            logger.info(f"[Signal:{str_signal_id}] 使用实际SOL花费金额: {amount_in_ui}")
        except (ValueError, TypeError) as e:
            logger.error(f"[Signal:{str_signal_id}] 无法转换实际SOL花费金额: {successful_attempt.actual_amount_in}, Error: {e}")
            amount_in_ui = f"{successful_attempt.actual_amount_in} (raw)"
    else:
        # 回退到计划金额
        # ... 回退逻辑 ...

    # --- 处理 Amount Bought (购买到的Token数量) ---
    if successful_attempt.actual_amount_out is not None:
        try:
            # 获取输出token的decimals
            output_token_decimals = token_info.get('decimals')
            raw_amount_out = float(successful_attempt.actual_amount_out)
            
            if output_token_decimals is not None:
                # 智能判断是否需要转换
                decimals_int = int(output_token_decimals)
                if decimals_int > 0 and raw_amount_out > (10 ** (decimals_int - 2)):
                    adjusted_amount_out = raw_amount_out / (10 ** decimals_int)
                else:
                    adjusted_amount_out = raw_amount_out
                
                # 动态确定显示的小数位数，最多显示6位小数
                display_decimals = min(decimals_int, 6)
                amount_out_ui = f"{adjusted_amount_out:.{display_decimals}f}"
                logger.info(f"[Signal:{str_signal_id}] 使用实际Token购买数量: {amount_out_ui} (decimals: {decimals_int})")
            else:
                # 没有decimals信息，直接显示原始值
                amount_out_ui = f"{raw_amount_out:.6f}"
                logger.warning(f"[Signal:{str_signal_id}] Token {token_info.get('address')} 缺少decimals信息，显示原始值: {amount_out_ui}")
        except (ValueError, TypeError) as e:
            logger.error(f"[Signal:{str_signal_id}] 无法转换实际Token购买数量: {successful_attempt.actual_amount_out}, decimals: {token_info.get('decimals')}, Error: {e}")
            amount_out_ui = f"{successful_attempt.actual_amount_out} (raw)"
    else:
        logger.warning(f"[Signal:{str_signal_id}] 实际Token购买数量为空")
        amount_out_ui = f"N/A"

template_render_data['trade_result']['actual_amount_in_ui'] = f"{amount_in_ui}"
template_render_data['trade_result']['actual_amount_out_ui'] = f"{amount_out_ui}"
```

**方案提出者/执行者**: AI Assistant (Claude Sonnet 4)
**方案审阅者/批准者**: User (gaojerry)
**方案批准日期**: 2025-05-26
**方案实施日期**: 2025-05-26

**验证方法和结果**:

1. **单元测试验证**: 创建了`test/workflows/monitor_kol_activity/test_handler_amount_display.py`测试文件
   - 测试成功交易时的金额显示
   - 测试UI格式金额的处理
   - 测试缺少decimals信息时的降级处理
   - **所有测试通过** ✅

2. **测试用例覆盖**:
   - Lamports格式转换: 1000000 lamports → 0.001000000 SOL
   - Token最小单位转换: 1500000 (6 decimals) → 1.500000 tokens
   - UI格式直接使用: 0.001 SOL → 0.001000000 SOL
   - 缺少decimals降级: 显示原始值

3. **预期修复效果**:
   
   **修复前**:
   ```
   Amount Bought: N/A grow grow
   Amount Spent: N/A SOL SOL
   ```
   
   **修复后**:
   ```
   Amount Bought: 1.500000 grow
   Amount Spent: 0.001000000 SOL
   ```

**修复状态**: ✅ **已完成并验证**

**相关文件**:
- 修复文件: `workflows/monitor_kol_activity/handler.py`
- 测试文件: `test/workflows/monitor_kol_activity/test_handler_amount_display.py`
- 测试文档: `test/workflows/monitor_kol_activity/test_handler_amount_display.md`

**注意事项**:
1. 修复使用了启发式方法判断数值格式，在极端情况下可能需要调整阈值
2. 当Token缺少decimals信息时，会显示原始值并记录警告日志
3. 所有错误情况都有适当的日志记录和降级处理
4. 修复保持了向后兼容性，不会影响现有功能 