from typing import List, Optional, Dict, Any
from datetime import datetime
from models.gmgn_token_stats import GmgnTokenStats
from .base_dao import BaseDAO
from pymongo import UpdateOne

class GmgnTokenStatsDAO(BaseDAO[GmgnTokenStats]):
    """GMGN代币统计信息数据访问对象"""
    
    def __init__(self):
        super().__init__(GmgnTokenStats)
    
    async def find_by_address(self, chain: str, address: str) -> Optional[GmgnTokenStats]:
        """根据链和地址获取代币统计信息
        
        Args:
            chain: 链名称
            address: 代币地址
            
        Returns:
            Optional[GmgnTokenStats]: 代币统计信息
        """
        return await self.find_one({
            "chain": chain,
            "address": address
        })
    
    async def find_by_holder_count_range(
        self,
        min_count: int = 0,
        max_count: Optional[int] = None
    ) -> List[GmgnTokenStats]:
        """获取指定持有者数量范围内的代币统计信息
        
        Args:
            min_count: 最小持有者数量
            max_count: 最大持有者数量
            
        Returns:
            List[GmgnTokenStats]: 代币统计信息列表
        """
        query = {"holder_count": {"$gte": min_count}}
        if max_count is not None:
            query["holder_count"]["$lte"] = max_count
            
        return await self.find_many(query)
    
    async def find_by_bluechip_owner_percentage_range(
        self,
        min_percentage: float = 0,
        max_percentage: Optional[float] = None
    ) -> List[GmgnTokenStats]:
        """获取指定蓝筹持有者比例范围内的代币统计信息
        
        Args:
            min_percentage: 最小比例
            max_percentage: 最大比例
            
        Returns:
            List[GmgnTokenStats]: 代币统计信息列表
        """
        query = {"bluechip_owner_percentage": {"$gte": min_percentage}}
        if max_percentage is not None:
            query["bluechip_owner_percentage"]["$lte"] = max_percentage
            
        return await self.find_many(query)
    
    async def find_by_signal_count_range(
        self,
        min_count: int = 0,
        max_count: Optional[int] = None
    ) -> List[GmgnTokenStats]:
        """获取指定信号数量范围内的代币统计信息
        
        Args:
            min_count: 最小数量
            max_count: 最大数量
            
        Returns:
            List[GmgnTokenStats]: 代币统计信息列表
        """
        query = {"signal_count": {"$gte": min_count}}
        if max_count is not None:
            query["signal_count"]["$lte"] = max_count
            
        return await self.find_many(query)
    
    async def upsert_token_stats(self, chain: str, address: str, data: Dict[str, Any]) -> bool:
        """更新或插入代币统计信息
        
        Args:
            chain: 链名称
            address: 代币地址
            data: API返回的数据
            
        Returns:
            bool: 是否成功
        """
        # 创建新的代币统计实例
        token_stats = await GmgnTokenStats.from_api_data(chain, address, data)
        
        # 更新时间
        token_stats.updated_at = datetime.utcnow()
        
        # 使用原子更新操作
        return await self.update_one(
            {
                "chain": chain,
                "address": address
            },
            token_stats.dict(),
            upsert=True
        )
    
    async def upsert_token_stats_many(
        self,
        chain: str,
        data_list: List[Dict[str, Any]]
    ) -> int:
        """批量更新或插入代币统计信息
        
        Args:
            chain: 链名称
            data_list: API返回的数据列表，每个元素必须包含address字段
            
        Returns:
            int: 更新或插入的记录数
        """
        if not data_list:
            return 0
            
        # 准备批量更新操作
        bulk_operations = []
        current_time = datetime.utcnow()
        
        for data in data_list:
            address = data.get('address')
            if not address:
                continue
                
            # 创建新的代币统计实例
            token_stats = await GmgnTokenStats.from_api_data(chain, address, data)
            token_stats.updated_at = current_time
            
            # 创建更新操作
            bulk_operations.append(
                UpdateOne(
                    {
                        "chain": chain,
                        "address": address
                    },
                    {"$set": token_stats.dict()},
                    upsert=True
                )
            )
        
        # 执行批量更新
        try:
            result = await self.collection.bulk_write(bulk_operations)
            self.logger.info(f"批量更新完成: 修改 {result.modified_count} 条, 插入 {result.upserted_count} 条")
            return result.modified_count + result.upserted_count
        except Exception as e:
            self.logger.error(f"批量更新代币统计数据失败: {str(e)}")
            return 0
    
    async def delete_by_address(self, chain: str, address: str) -> bool:
        """根据链和地址删除代币统计信息
        
        Args:
            chain: 链名称
            address: 代币地址
            
        Returns:
            bool: 是否删除成功
        """
        return await self.delete_one({
            "chain": chain,
            "address": address
        }) 